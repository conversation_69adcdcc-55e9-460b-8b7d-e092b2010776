import os
import logging
import json
import time
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import sqlite3
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MetricsService:
    """指标收集与分析服务"""
    
    def __init__(self, db_path: str = "metrics.db"):
        """初始化指标服务"""
        self.db_path = db_path
        self._initialize_db()
        
    def _initialize_db(self):
        """初始化指标数据库"""
        try:
            # 创建数据库目录
            db_dir = os.path.dirname(self.db_path)
            if db_dir and not os.path.exists(db_dir):
                os.makedirs(db_dir)
                
            # 连接数据库
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 创建交互指标表
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS interaction_metrics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                digital_human_id TEXT NOT NULL,
                user_id INTEGER,
                session_id TEXT NOT NULL,
                interaction_type TEXT NOT NULL,
                content TEXT,
                emotion TEXT,
                response_time REAL,
                timestamp TEXT NOT NULL
            )
            ''')
            
            # 创建性能指标表
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS performance_metrics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                metric_type TEXT NOT NULL,
                value REAL NOT NULL,
                component TEXT NOT NULL,
                context TEXT,
                timestamp TEXT NOT NULL
            )
            ''')
            
            # 创建情感分析表
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS emotion_analytics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                digital_human_id TEXT NOT NULL,
                user_id INTEGER,
                session_id TEXT NOT NULL,
                detected_emotion TEXT NOT NULL,
                confidence REAL,
                source TEXT NOT NULL,
                timestamp TEXT NOT NULL
            )
            ''')
            
            # 创建使用统计表
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS usage_stats (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                feature TEXT NOT NULL,
                count INTEGER NOT NULL,
                user_id INTEGER,
                date TEXT NOT NULL
            )
            ''')
            
            conn.commit()
            conn.close()
            logger.info(f"指标数据库初始化成功: {self.db_path}")
            
        except Exception as e:
            logger.error(f"初始化指标数据库失败: {str(e)}")
            
    def log_interaction(self, 
                      digital_human_id: str, 
                      user_id: Optional[int], 
                      session_id: str,
                      interaction_type: str,
                      content: Optional[str] = None,
                      emotion: Optional[str] = None,
                      response_time: Optional[float] = None):
        """记录交互数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            timestamp = datetime.now().isoformat()
            
            cursor.execute('''
            INSERT INTO interaction_metrics 
            (digital_human_id, user_id, session_id, interaction_type, content, emotion, response_time, timestamp)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (digital_human_id, user_id, session_id, interaction_type, content, emotion, response_time, timestamp))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"记录交互指标失败: {str(e)}")
            
    def log_performance(self, 
                       metric_type: str, 
                       value: float, 
                       component: str,
                       context: Optional[str] = None):
        """记录性能指标"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            timestamp = datetime.now().isoformat()
            
            cursor.execute('''
            INSERT INTO performance_metrics 
            (metric_type, value, component, context, timestamp)
            VALUES (?, ?, ?, ?, ?)
            ''', (metric_type, value, component, context, timestamp))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"记录性能指标失败: {str(e)}")
            
    def log_emotion(self, 
                  digital_human_id: str, 
                  user_id: Optional[int], 
                  session_id: str,
                  emotion: str,
                  confidence: float,
                  source: str):
        """记录情感分析数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            timestamp = datetime.now().isoformat()
            
            cursor.execute('''
            INSERT INTO emotion_analytics 
            (digital_human_id, user_id, session_id, detected_emotion, confidence, source, timestamp)
            VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (digital_human_id, user_id, session_id, emotion, confidence, source, timestamp))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"记录情感分析数据失败: {str(e)}")
            
    def increment_usage(self, feature: str, user_id: Optional[int] = None):
        """增加功能使用计数"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            date = datetime.now().strftime("%Y-%m-%d")
            
            # 检查是否已存在记录
            cursor.execute('''
            SELECT id, count FROM usage_stats 
            WHERE feature = ? AND user_id = ? AND date = ?
            ''', (feature, user_id, date))
            
            result = cursor.fetchone()
            
            if result:
                # 更新现有记录
                cursor.execute('''
                UPDATE usage_stats SET count = count + 1
                WHERE id = ?
                ''', (result[0],))
            else:
                # 创建新记录
                cursor.execute('''
                INSERT INTO usage_stats (feature, count, user_id, date)
                VALUES (?, ?, ?, ?)
                ''', (feature, 1, user_id, date))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"更新使用统计失败: {str(e)}")
            
    def get_interaction_stats(self, digital_human_id: Optional[str] = None, 
                           user_id: Optional[int] = None,
                           days: int = 7) -> Dict[str, Any]:
        """获取交互统计数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            query_parts = ["SELECT interaction_type, COUNT(*) as count FROM interaction_metrics WHERE"]
            params = []
            
            query_conditions = []
            if digital_human_id:
                query_conditions.append("digital_human_id = ?")
                params.append(digital_human_id)
                
            if user_id:
                query_conditions.append("user_id = ?")
                params.append(user_id)
                
            if days > 0:
                date_limit = (datetime.now() - timedelta(days=days)).isoformat()
                query_conditions.append("timestamp >= ?")
                params.append(date_limit)
                
            if query_conditions:
                query_parts.append(" AND ".join(query_conditions))
            else:
                # 移除WHERE子句
                query_parts = [query_parts[0].replace(" WHERE", "")]
                
            query_parts.append("GROUP BY interaction_type")
            
            cursor.execute(" ".join(query_parts), params)
            results = cursor.fetchall()
            
            conn.close()
            
            stats = {}
            for interaction_type, count in results:
                stats[interaction_type] = count
                
            return stats
            
        except Exception as e:
            logger.error(f"获取交互统计失败: {str(e)}")
            return {}
            
    def get_emotion_distribution(self, digital_human_id: Optional[str] = None,
                              user_id: Optional[int] = None,
                              days: int = 7) -> Dict[str, float]:
        """获取情感分布"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            query_parts = ["SELECT detected_emotion, COUNT(*) as count FROM emotion_analytics WHERE"]
            params = []
            
            query_conditions = []
            if digital_human_id:
                query_conditions.append("digital_human_id = ?")
                params.append(digital_human_id)
                
            if user_id:
                query_conditions.append("user_id = ?")
                params.append(user_id)
                
            if days > 0:
                date_limit = (datetime.now() - timedelta(days=days)).isoformat()
                query_conditions.append("timestamp >= ?")
                params.append(date_limit)
                
            if query_conditions:
                query_parts.append(" AND ".join(query_conditions))
            else:
                # 移除WHERE子句
                query_parts = [query_parts[0].replace(" WHERE", "")]
                
            query_parts.append("GROUP BY detected_emotion")
            
            cursor.execute(" ".join(query_parts), params)
            results = cursor.fetchall()
            
            # 计算总数
            total = sum(count for _, count in results)
            
            conn.close()
            
            distribution = {}
            if total > 0:
                for emotion, count in results:
                    distribution[emotion] = count / total
                    
            return distribution
            
        except Exception as e:
            logger.error(f"获取情感分布失败: {str(e)}")
            return {}
            
    def get_response_time_stats(self, component: Optional[str] = None,
                             days: int = 7) -> Dict[str, float]:
        """获取响应时间统计"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            query_parts = ["SELECT AVG(value) as avg_time, MIN(value) as min_time, MAX(value) as max_time FROM performance_metrics WHERE metric_type = 'response_time'"]
            params = []
            
            if component:
                query_parts.append("AND component = ?")
                params.append(component)
                
            if days > 0:
                date_limit = (datetime.now() - timedelta(days=days)).isoformat()
                query_parts.append("AND timestamp >= ?")
                params.append(date_limit)
                
            cursor.execute(" ".join(query_parts), params)
            result = cursor.fetchone()
            
            conn.close()
            
            if result and result[0] is not None:
                return {
                    "avg_time": result[0],
                    "min_time": result[1],
                    "max_time": result[2]
                }
            else:
                return {
                    "avg_time": 0,
                    "min_time": 0,
                    "max_time": 0
                }
                
        except Exception as e:
            logger.error(f"获取响应时间统计失败: {str(e)}")
            return {"avg_time": 0, "min_time": 0, "max_time": 0}
            
    def export_metrics(self, output_path: str):
        """导出指标数据到JSON文件"""
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            metrics = {}
            
            # 导出交互指标
            cursor.execute("SELECT * FROM interaction_metrics")
            metrics["interactions"] = [dict(row) for row in cursor.fetchall()]
            
            # 导出性能指标
            cursor.execute("SELECT * FROM performance_metrics")
            metrics["performance"] = [dict(row) for row in cursor.fetchall()]
            
            # 导出情感分析
            cursor.execute("SELECT * FROM emotion_analytics")
            metrics["emotions"] = [dict(row) for row in cursor.fetchall()]
            
            # 导出使用统计
            cursor.execute("SELECT * FROM usage_stats")
            metrics["usage"] = [dict(row) for row in cursor.fetchall()]
            
            conn.close()
            
            # 写入JSON文件
            with open(output_path, "w") as f:
                json.dump(metrics, f, indent=2)
                
            logger.info(f"指标数据已导出到: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"导出指标数据失败: {str(e)}")
            return False

def get_metrics_service(db_path: str = "data/metrics.db"):
    """获取指标服务实例"""
    # 确保数据目录存在
    os.makedirs(os.path.dirname(db_path), exist_ok=True)
    return MetricsService(db_path) 