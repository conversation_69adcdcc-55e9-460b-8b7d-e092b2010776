#!/usr/bin/env python3
"""
情感表达系统
根据对话内容调整数字人的表情和动作
"""

import logging
from typing import Dict, Any, Optional, Tuple, List
from pathlib import Path
import json
import numpy as np

logger = logging.getLogger(__name__)

class EmotionExpression:
    """情感表达类"""
    
    def __init__(self):
        # 情感到表情的映射
        self.emotion_to_expression = {
            "happy": {
                "facial_expression": "smile",
                "eye_expression": "bright",
                "mouth_movement": "wide",
                "head_movement": "slight_nod",
                "animation_speed": 1.2,
                "brightness_adjustment": 1.1,
                "color_temperature": "warm"
            },
            "sad": {
                "facial_expression": "frown",
                "eye_expression": "droopy",
                "mouth_movement": "narrow",
                "head_movement": "slight_down",
                "animation_speed": 0.8,
                "brightness_adjustment": 0.9,
                "color_temperature": "cool"
            },
            "angry": {
                "facial_expression": "stern",
                "eye_expression": "intense",
                "mouth_movement": "tight",
                "head_movement": "firm",
                "animation_speed": 1.3,
                "brightness_adjustment": 1.0,
                "color_temperature": "neutral"
            },
            "surprised": {
                "facial_expression": "wide_eyes",
                "eye_expression": "wide",
                "mouth_movement": "open",
                "head_movement": "slight_back",
                "animation_speed": 1.4,
                "brightness_adjustment": 1.05,
                "color_temperature": "bright"
            },
            "fear": {
                "facial_expression": "worried",
                "eye_expression": "wide",
                "mouth_movement": "small",
                "head_movement": "cautious",
                "animation_speed": 0.9,
                "brightness_adjustment": 0.95,
                "color_temperature": "cool"
            },
            "neutral": {
                "facial_expression": "calm",
                "eye_expression": "normal",
                "mouth_movement": "natural",
                "head_movement": "gentle",
                "animation_speed": 1.0,
                "brightness_adjustment": 1.0,
                "color_temperature": "neutral"
            }
        }
        
        # 内容类型到情感的映射
        self.content_emotion_mapping = {
            "greeting": "happy",
            "farewell": "neutral",
            "question": "curious",
            "explanation": "confident",
            "apology": "sad",
            "encouragement": "happy",
            "warning": "serious",
            "joke": "playful"
        }
    
    def analyze_content_emotion(self, text: str, detected_emotion: str = "neutral") -> str:
        """分析内容情感"""
        text_lower = text.lower()
        
        # 检查特定内容类型
        if any(word in text_lower for word in ["你好", "hello", "hi", "早上好", "下午好"]):
            return "happy"
        elif any(word in text_lower for word in ["再见", "拜拜", "goodbye", "bye"]):
            return "neutral"
        elif any(word in text_lower for word in ["对不起", "抱歉", "sorry"]):
            return "sad"
        elif any(word in text_lower for word in ["恭喜", "太棒了", "很好", "excellent", "great"]):
            return "happy"
        elif any(word in text_lower for word in ["小心", "注意", "警告", "warning"]):
            return "serious"
        elif any(word in text_lower for word in ["哈哈", "呵呵", "有趣", "funny"]):
            return "playful"
        else:
            return detected_emotion
    
    def get_expression_config(self, emotion: str, intensity: float = 1.0) -> Dict[str, Any]:
        """获取表情配置"""
        base_config = self.emotion_to_expression.get(emotion, self.emotion_to_expression["neutral"])
        
        # 根据强度调整配置
        adjusted_config = base_config.copy()
        adjusted_config["animation_speed"] *= intensity
        adjusted_config["brightness_adjustment"] = 1.0 + (adjusted_config["brightness_adjustment"] - 1.0) * intensity
        
        return adjusted_config
    
    def generate_emotion_enhanced_video_params(self, 
                                             emotion: str, 
                                             text: str,
                                             base_params: Dict[str, Any] = None) -> Dict[str, Any]:
        """生成情感增强的视频参数"""
        # 分析内容情感
        content_emotion = self.analyze_content_emotion(text, emotion)
        
        # 获取表情配置
        expression_config = self.get_expression_config(content_emotion)
        
        # 基础参数
        if base_params is None:
            base_params = {
                "fps": 25,
                "duration": 10,
                "quality": "high"
            }
        
        # 情感增强参数
        enhanced_params = base_params.copy()
        enhanced_params.update({
            "emotion": content_emotion,
            "facial_expression": expression_config["facial_expression"],
            "animation_speed": expression_config["animation_speed"],
            "brightness_adjustment": expression_config["brightness_adjustment"],
            "color_temperature": expression_config["color_temperature"],
            "motion_intensity": self._calculate_motion_intensity(content_emotion),
            "expression_keyframes": self._generate_expression_keyframes(content_emotion, text)
        })
        
        return enhanced_params
    
    def _calculate_motion_intensity(self, emotion: str) -> float:
        """计算动作强度"""
        intensity_map = {
            "happy": 0.08,
            "sad": 0.03,
            "angry": 0.06,
            "surprised": 0.10,
            "fear": 0.04,
            "neutral": 0.05,
            "playful": 0.09,
            "serious": 0.04,
            "confident": 0.06
        }
        return intensity_map.get(emotion, 0.05)
    
    def _generate_expression_keyframes(self, emotion: str, text: str) -> List[Dict[str, Any]]:
        """生成表情关键帧"""
        text_length = len(text)
        duration = max(5, min(15, text_length // 10))  # 根据文本长度调整时长
        
        keyframes = []
        
        if emotion == "happy":
            keyframes = [
                {"time": 0.0, "expression": "neutral", "intensity": 0.5},
                {"time": 0.2, "expression": "slight_smile", "intensity": 0.7},
                {"time": 0.5, "expression": "smile", "intensity": 1.0},
                {"time": 0.8, "expression": "smile", "intensity": 0.9},
                {"time": 1.0, "expression": "slight_smile", "intensity": 0.6}
            ]
        elif emotion == "sad":
            keyframes = [
                {"time": 0.0, "expression": "neutral", "intensity": 0.5},
                {"time": 0.3, "expression": "slight_frown", "intensity": 0.6},
                {"time": 0.6, "expression": "frown", "intensity": 0.8},
                {"time": 1.0, "expression": "slight_frown", "intensity": 0.5}
            ]
        elif emotion == "surprised":
            keyframes = [
                {"time": 0.0, "expression": "neutral", "intensity": 0.5},
                {"time": 0.1, "expression": "wide_eyes", "intensity": 1.2},
                {"time": 0.3, "expression": "surprised", "intensity": 1.0},
                {"time": 0.7, "expression": "interested", "intensity": 0.8},
                {"time": 1.0, "expression": "neutral", "intensity": 0.6}
            ]
        else:
            # 默认中性表情
            keyframes = [
                {"time": 0.0, "expression": "neutral", "intensity": 0.5},
                {"time": 0.5, "expression": "gentle", "intensity": 0.7},
                {"time": 1.0, "expression": "neutral", "intensity": 0.5}
            ]
        
        return keyframes

class AdvancedVideoGenerator:
    """高级视频生成器"""
    
    def __init__(self):
        self.emotion_expression = EmotionExpression()
    
    def generate_emotion_aware_video(self, 
                                   avatar_path: Path,
                                   text: str,
                                   output_path: Path,
                                   emotion: str = "neutral",
                                   **kwargs) -> bool:
        """生成情感感知的视频"""
        try:
            import cv2
            import numpy as np
            
            # 获取情感增强参数
            enhanced_params = self.emotion_expression.generate_emotion_enhanced_video_params(
                emotion, text, kwargs
            )
            
            logger.info(f"🎭 生成情感视频: {emotion} -> {enhanced_params['emotion']}")
            
            # 读取头像
            img = cv2.imread(str(avatar_path))
            if img is None:
                logger.error(f"无法读取头像: {avatar_path}")
                return False
            
            # 视频参数
            height, width = img.shape[:2]
            fps = enhanced_params.get("fps", 25)
            duration = enhanced_params.get("duration", 10)
            total_frames = fps * duration
            
            # 情感参数
            motion_intensity = enhanced_params.get("motion_intensity", 0.05)
            animation_speed = enhanced_params.get("animation_speed", 1.0)
            brightness_adj = enhanced_params.get("brightness_adjustment", 1.0)
            expression_keyframes = enhanced_params.get("expression_keyframes", [])
            
            # 创建视频写入器
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            video_writer = cv2.VideoWriter(str(output_path), fourcc, fps, (width, height))
            
            if not video_writer.isOpened():
                logger.error("无法创建视频写入器")
                return False
            
            logger.info(f"🎬 生成 {total_frames} 帧情感视频...")
            
            for i in range(total_frames):
                progress = i / total_frames
                
                # 计算当前表情强度
                expression_intensity = self._interpolate_expression_intensity(
                    progress, expression_keyframes
                )
                
                # 高级说话效果算法（情感增强）
                primary_cycle = np.sin(i * 0.4 * animation_speed) * motion_intensity
                secondary_cycle = np.sin(i * 0.8 * animation_speed) * motion_intensity * 0.3
                micro_cycle = np.sin(i * 1.2 * animation_speed) * motion_intensity * 0.1
                
                # 情感调制
                emotion_modulation = expression_intensity * 0.5
                combined_cycle = (primary_cycle + secondary_cycle + micro_cycle) * (1.0 + emotion_modulation)
                
                # 应用变换
                zoom_factor = 1.0 + combined_cycle * 0.08
                brightness = brightness_adj + combined_cycle * 0.12
                
                # 位移计算（情感影响）
                y_offset = int(combined_cycle * 6 * expression_intensity)
                x_offset = int(np.sin(i * 0.6 * animation_speed) * motion_intensity * 2)
                
                # 应用复合变换
                center = (width // 2, height // 2)
                M = cv2.getRotationMatrix2D(center, 0, zoom_factor)
                M[0, 2] += x_offset
                M[1, 2] += y_offset
                
                frame = cv2.warpAffine(img, M, (width, height))
                frame = cv2.convertScaleAbs(frame, alpha=brightness, beta=0)
                
                # 情感色彩调整
                frame = self._apply_emotion_color_filter(frame, enhanced_params.get("color_temperature", "neutral"))
                
                # 添加动态模糊效果
                if motion_intensity > 0.06:
                    kernel_size = max(1, int(abs(combined_cycle) * 3))
                    if kernel_size > 1:
                        frame = cv2.GaussianBlur(frame, (kernel_size, kernel_size), 0)
                
                video_writer.write(frame)
                
                # 显示进度
                if i % max(1, total_frames // 20) == 0:
                    progress_percent = int((i / total_frames) * 100)
                    logger.info(f"🎭 情感视频进度: {progress_percent}% (帧 {i}/{total_frames})")
            
            video_writer.release()
            
            if output_path.exists():
                file_size = output_path.stat().st_size
                logger.info(f"✅ 情感视频生成成功: {output_path} ({file_size} bytes)")
                return True
            else:
                logger.error("情感视频生成失败")
                return False
                
        except Exception as e:
            logger.error(f"❌ 情感视频生成异常: {e}")
            return False
    
    def _interpolate_expression_intensity(self, progress: float, keyframes: List[Dict[str, Any]]) -> float:
        """插值表情强度"""
        if not keyframes:
            return 1.0
        
        # 找到当前时间点的关键帧
        for i, keyframe in enumerate(keyframes):
            if progress <= keyframe["time"]:
                if i == 0:
                    return keyframe["intensity"]
                
                # 线性插值
                prev_keyframe = keyframes[i-1]
                t = (progress - prev_keyframe["time"]) / (keyframe["time"] - prev_keyframe["time"])
                return prev_keyframe["intensity"] + t * (keyframe["intensity"] - prev_keyframe["intensity"])
        
        # 超出范围，返回最后一个关键帧的强度
        return keyframes[-1]["intensity"]
    
    def _apply_emotion_color_filter(self, frame: np.ndarray, color_temperature: str) -> np.ndarray:
        """应用情感色彩滤镜"""
        if color_temperature == "warm":
            # 暖色调（快乐）
            frame[:, :, 0] = cv2.multiply(frame[:, :, 0], 0.9)  # 减少蓝色
            frame[:, :, 2] = cv2.multiply(frame[:, :, 2], 1.1)  # 增加红色
        elif color_temperature == "cool":
            # 冷色调（悲伤）
            frame[:, :, 0] = cv2.multiply(frame[:, :, 0], 1.1)  # 增加蓝色
            frame[:, :, 2] = cv2.multiply(frame[:, :, 2], 0.9)  # 减少红色
        elif color_temperature == "bright":
            # 明亮（惊讶）
            frame = cv2.convertScaleAbs(frame, alpha=1.1, beta=10)
        
        return frame

# 全局实例
advanced_video_generator = AdvancedVideoGenerator()
