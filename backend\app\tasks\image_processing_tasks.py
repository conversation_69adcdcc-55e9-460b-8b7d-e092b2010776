"""
图像处理任务
"""

from app.core.celery_app import celery_app, DatabaseTask
import logging
import os
import numpy as np
from PIL import Image
from datetime import datetime
import uuid
import requests

# 简化版本，不依赖复杂的AI库
try:
    import cv2
    CV2_AVAILABLE = True
except ImportError:
    CV2_AVAILABLE = False

try:
    import face_recognition
    FACE_RECOGNITION_AVAILABLE = True
except ImportError:
    FACE_RECOGNITION_AVAILABLE = False

try:
    import mediapipe as mp
    MEDIAPIPE_AVAILABLE = True
except ImportError:
    MEDIAPIPE_AVAILABLE = False

logger = logging.getLogger(__name__)

@celery_app.task(bind=True, base=DatabaseTask, name="process_avatar_image")
def process_avatar_image(self, digital_human_id: int, task_id: str, input_data: dict):
    """
    处理头像图像
    包括人脸检测、特征提取、图像增强等
    """
    try:
        logger.info(f"开始处理头像图像: ID={digital_human_id}")
        
        # 更新进度
        self.update_task_progress(task_id, 10, "正在处理头像图像...")
        
        # 获取输入图像
        image_url = get_input_image_url(input_data)
        if not image_url:
            raise ValueError("未找到输入图像")
        
        # 下载或加载图像
        image_path = download_or_load_image(image_url)
        
        # 人脸检测和验证
        self.update_task_progress(task_id, 15, "正在检测人脸...")
        face_info = detect_and_validate_face(image_path)
        
        if not face_info["has_face"]:
            raise ValueError("图像中未检测到人脸")
        
        # 图像预处理
        self.update_task_progress(task_id, 20, "正在预处理图像...")
        processed_image = preprocess_image(image_path, face_info)
        
        # 应用外观特征调整
        self.update_task_progress(task_id, 25, "正在应用外观特征...")
        enhanced_image = apply_appearance_features(processed_image, input_data)
        
        # 保存处理后的图像
        self.update_task_progress(task_id, 30, "正在保存处理结果...")
        output_path = save_processed_image(enhanced_image, digital_human_id)
        
        result = {
            "success": True,
            "digital_human_id": digital_human_id,
            "task_id": task_id,
            "original_image_url": image_url,
            "processed_image_url": f"/static/generated/{os.path.basename(output_path)}",
            "processed_image_path": output_path,
            "face_info": face_info,
            "processing_steps": [
                "人脸检测完成",
                "图像预处理完成", 
                "外观特征应用完成",
                "图像保存完成"
            ]
        }
        
        logger.info(f"头像图像处理完成: {result}")
        return result
        
    except Exception as e:
        logger.error(f"头像图像处理失败: {str(e)}")
        raise

def get_input_image_url(input_data: dict) -> str:
    """获取输入图像URL"""
    if input_data.get("uploaded_image_url"):
        return input_data["uploaded_image_url"]
    elif input_data.get("generated_image_url"):
        return input_data["generated_image_url"]
    elif input_data.get("selected_template"):
        # 从模板获取图像
        return get_template_image_url(input_data["selected_template"])
    else:
        # 使用默认图像
        return get_default_image_url(input_data.get("gender", "female"))

def get_template_image_url(template_id: str) -> str:
    """从模板ID获取图像URL"""
    # TODO: 从数据库查询模板图像URL
    return f"/static/templates/{template_id}.jpg"

def get_default_image_url(gender: str) -> str:
    """获取默认图像URL"""
    return f"/static/defaults/{gender}_default.jpg"

def download_or_load_image(image_url: str) -> str:
    """下载或加载图像"""
    import requests
    from urllib.parse import urlparse
    
    if image_url.startswith("http"):
        # 下载网络图像
        response = requests.get(image_url)
        response.raise_for_status()
        
        # 保存到临时文件
        temp_path = f"/tmp/input_{uuid.uuid4().hex}.jpg"
        with open(temp_path, "wb") as f:
            f.write(response.content)
        return temp_path
    else:
        # 本地文件路径
        return image_url.replace("/static/", "storage/")

def detect_and_validate_face(image_path: str) -> dict:
    """检测和验证人脸"""
    try:
        if FACE_RECOGNITION_AVAILABLE:
            # 使用face_recognition库检测人脸
            image = face_recognition.load_image_file(image_path)
            face_locations = face_recognition.face_locations(image)
            face_encodings = face_recognition.face_encodings(image, face_locations)

            if not face_locations:
                return {
                    "has_face": False,
                    "face_count": 0,
                    "error": "未检测到人脸"
                }

            if len(face_locations) > 1:
                logger.warning(f"检测到多个人脸: {len(face_locations)}")

            # 使用第一个检测到的人脸
            face_location = face_locations[0]
            face_encoding = face_encodings[0] if face_encodings else None

            # 使用MediaPipe进行更详细的人脸分析
            landmarks = None
            if MEDIAPIPE_AVAILABLE:
                mp_face_mesh = mp.solutions.face_mesh
                face_mesh = mp_face_mesh.FaceMesh(
                    static_image_mode=True,
                    max_num_faces=1,
                    refine_landmarks=True,
                    min_detection_confidence=0.5
                )

                # 转换为RGB
                if CV2_AVAILABLE:
                    rgb_image = cv2.cvtColor(cv2.imread(image_path), cv2.COLOR_BGR2RGB)
                    results = face_mesh.process(rgb_image)

                    if results.multi_face_landmarks:
                        landmarks = results.multi_face_landmarks[0]

            return {
                "has_face": True,
                "face_count": len(face_locations),
                "face_location": face_location,  # (top, right, bottom, left)
                "face_encoding": face_encoding.tolist() if face_encoding is not None else None,
                "landmarks": landmarks,
                "image_shape": image.shape
            }
        else:
            # 简化版本：假设图像中有人脸
            logger.warning("face_recognition库不可用，使用简化检测")

            # 使用PIL获取图像信息
            with Image.open(image_path) as img:
                width, height = img.size

                # 假设人脸在图像中央区域
                face_location = (
                    int(height * 0.2),  # top
                    int(width * 0.8),   # right
                    int(height * 0.8),  # bottom
                    int(width * 0.2)    # left
                )

                return {
                    "has_face": True,
                    "face_count": 1,
                    "face_location": face_location,
                    "face_encoding": None,
                    "landmarks": None,
                    "image_shape": (height, width, 3),
                    "method": "simplified"
                }

    except Exception as e:
        logger.error(f"人脸检测失败: {str(e)}")
        return {
            "has_face": False,
            "face_count": 0,
            "error": str(e)
        }

def preprocess_image(image_path: str, face_info: dict) -> np.ndarray:
    """图像预处理"""
    if CV2_AVAILABLE:
        # 使用OpenCV加载图像
        image = cv2.imread(image_path)
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

        # 人脸对齐
        if face_info.get("face_location"):
            image = align_face(image, face_info)

        # 图像增强
        image = enhance_image(image)

        # 调整尺寸
        image = resize_image(image, target_size=(512, 512))

        return image
    else:
        # 使用PIL处理图像
        with Image.open(image_path) as img:
            # 转换为RGB
            if img.mode != 'RGB':
                img = img.convert('RGB')

            # 调整尺寸
            img = img.resize((512, 512), Image.Resampling.LANCZOS)

            # 转换为numpy数组
            image = np.array(img)

            return image

def align_face(image: np.ndarray, face_info: dict) -> np.ndarray:
    """人脸对齐"""
    if not CV2_AVAILABLE:
        return image

    # 简单的人脸居中对齐
    top, right, bottom, left = face_info["face_location"]

    # 计算人脸中心
    face_center_x = (left + right) // 2
    face_center_y = (top + bottom) // 2

    # 计算图像中心
    img_center_x = image.shape[1] // 2
    img_center_y = image.shape[0] // 2

    # 计算偏移
    offset_x = img_center_x - face_center_x
    offset_y = img_center_y - face_center_y

    # 应用平移变换
    M = np.float32([[1, 0, offset_x], [0, 1, offset_y]])
    aligned_image = cv2.warpAffine(image, M, (image.shape[1], image.shape[0]))

    return aligned_image

def enhance_image(image: np.ndarray) -> np.ndarray:
    """图像增强"""
    # 亮度和对比度调整
    alpha = 1.1  # 对比度
    beta = 10    # 亮度
    enhanced = cv2.convertScaleAbs(image, alpha=alpha, beta=beta)
    
    # 去噪
    denoised = cv2.bilateralFilter(enhanced, 9, 75, 75)
    
    # 锐化
    kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
    sharpened = cv2.filter2D(denoised, -1, kernel)
    
    return sharpened

def resize_image(image: np.ndarray, target_size: tuple) -> np.ndarray:
    """调整图像尺寸"""
    return cv2.resize(image, target_size, interpolation=cv2.INTER_LANCZOS4)

def apply_appearance_features(image: np.ndarray, input_data: dict) -> np.ndarray:
    """应用外观特征调整"""
    enhanced_image = image.copy()
    
    # 获取特征参数
    facial_expression = input_data.get("facial_expression", "friendly")
    clothing_style = input_data.get("clothing_style", "business")
    background = input_data.get("background", "office")
    
    # 应用表情调整
    enhanced_image = apply_facial_expression(enhanced_image, facial_expression)
    
    # 应用服装风格调整
    enhanced_image = apply_clothing_style(enhanced_image, clothing_style)
    
    # 应用背景调整
    enhanced_image = apply_background_style(enhanced_image, background)
    
    return enhanced_image

def apply_facial_expression(image: np.ndarray, expression: str) -> np.ndarray:
    """应用面部表情调整"""
    if expression == "friendly":
        # 增加亮度和饱和度
        hsv = cv2.cvtColor(image, cv2.COLOR_RGB2HSV)
        hsv[:,:,1] = cv2.multiply(hsv[:,:,1], 1.2)  # 饱和度
        hsv[:,:,2] = cv2.add(hsv[:,:,2], 20)        # 亮度
        return cv2.cvtColor(hsv, cv2.COLOR_HSV2RGB)
    elif expression == "professional":
        # 降低饱和度，增加对比度
        hsv = cv2.cvtColor(image, cv2.COLOR_RGB2HSV)
        hsv[:,:,1] = cv2.multiply(hsv[:,:,1], 0.8)  # 降低饱和度
        return cv2.convertScaleAbs(cv2.cvtColor(hsv, cv2.COLOR_HSV2RGB), alpha=1.2, beta=0)
    elif expression == "gentle":
        # 柔化处理
        return cv2.GaussianBlur(image, (3, 3), 0.5)
    elif expression == "energetic":
        # 增强对比度和饱和度
        hsv = cv2.cvtColor(image, cv2.COLOR_RGB2HSV)
        hsv[:,:,1] = cv2.multiply(hsv[:,:,1], 1.4)  # 高饱和度
        enhanced = cv2.cvtColor(hsv, cv2.COLOR_HSV2RGB)
        return cv2.convertScaleAbs(enhanced, alpha=1.3, beta=10)
    
    return image

def apply_clothing_style(image: np.ndarray, style: str) -> np.ndarray:
    """应用服装风格调整"""
    if style == "business":
        # 添加轻微的冷色调
        image[:,:,0] = cv2.multiply(image[:,:,0], 0.95)  # 减少红色
        image[:,:,2] = cv2.multiply(image[:,:,2], 1.05)  # 增加蓝色
    elif style == "casual":
        # 添加暖色调
        image[:,:,0] = cv2.multiply(image[:,:,0], 1.1)   # 增加红色
        image[:,:,1] = cv2.multiply(image[:,:,1], 1.05)  # 增加绿色
    elif style == "fashion":
        # 增强色彩对比
        hsv = cv2.cvtColor(image, cv2.COLOR_RGB2HSV)
        hsv[:,:,1] = cv2.multiply(hsv[:,:,1], 1.3)  # 高饱和度
        image = cv2.cvtColor(hsv, cv2.COLOR_HSV2RGB)
    
    return image

def apply_background_style(image: np.ndarray, background: str) -> np.ndarray:
    """应用背景风格调整"""
    # 这里主要调整整体色调
    if background == "office":
        # 中性色调
        pass
    elif background == "white":
        # 增加亮度
        image = cv2.add(image, np.ones(image.shape, dtype=np.uint8) * 15)
    elif background == "home":
        # 温暖色调
        image[:,:,0] = cv2.multiply(image[:,:,0], 1.1)  # 增加红色
        image[:,:,1] = cv2.multiply(image[:,:,1], 1.05) # 增加绿色
    elif background == "tech":
        # 冷色调
        image[:,:,2] = cv2.multiply(image[:,:,2], 1.1)  # 增加蓝色
    
    return image

def save_processed_image(image: np.ndarray, digital_human_id: int) -> str:
    """保存处理后的图像"""
    # 确保输出目录存在
    output_dir = "storage/generated"
    os.makedirs(output_dir, exist_ok=True)
    
    # 生成文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"avatar_{digital_human_id}_{timestamp}.jpg"
    output_path = os.path.join(output_dir, filename)
    
    # 转换为PIL图像并保存
    pil_image = Image.fromarray(image)
    pil_image.save(output_path, "JPEG", quality=95)
    
    return output_path
