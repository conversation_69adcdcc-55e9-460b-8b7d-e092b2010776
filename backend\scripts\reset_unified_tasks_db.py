#!/usr/bin/env python3
"""
重置统一任务数据库表
"""
import os
import sys
import psycopg2
from pathlib import Path
from dotenv import load_dotenv

# 添加后端路径
backend_dir = Path(__file__).parent.parent
sys.path.append(str(backend_dir))

# 加载环境变量
env_path = backend_dir / ".env"
load_dotenv(env_path)

def reset_database():
    """重置数据库表"""
    print("🔄 重置统一任务数据库表")
    print("=" * 50)
    
    try:
        # 连接数据库
        conn = psycopg2.connect(os.getenv("DATABASE_URL"))
        cursor = conn.cursor()
        
        print("✅ 数据库连接成功")
        
        # 1. 删除旧的任务表
        old_tables = [
            'wanx_video_tasks',
            'unified_tasks'  # 也删除可能存在的旧版本
        ]
        
        for table in old_tables:
            try:
                cursor.execute(f"DROP TABLE IF EXISTS {table} CASCADE")
                print(f"🗑️ 删除旧表: {table}")
            except Exception as e:
                print(f"⚠️ 删除表 {table} 失败: {e}")
        
        # 2. 创建新的统一任务表
        print("\n📋 创建统一任务表...")
        
        cursor.execute("""
            CREATE TABLE unified_tasks (
                id SERIAL PRIMARY KEY,
                task_id VARCHAR(255) UNIQUE NOT NULL,
                celery_task_id VARCHAR(255),
                task_type VARCHAR(50) NOT NULL,
                task_subtype VARCHAR(50),
                user_id VARCHAR(255) NOT NULL,
                status VARCHAR(20) DEFAULT 'pending',
                progress INTEGER DEFAULT 0,
                title VARCHAR(500),
                description TEXT,
                message TEXT,
                error_message TEXT,
                input_params JSONB,
                output_data JSONB,
                input_files JSONB,
                output_files JSONB,
                estimated_duration INTEGER,
                actual_duration INTEGER,
                priority INTEGER DEFAULT 5,
                queue_name VARCHAR(50),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                started_at TIMESTAMP,
                completed_at TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                retry_count INTEGER DEFAULT 0,
                max_retries INTEGER DEFAULT 3,
                tags JSONB,
                extra_data JSONB
            )
        """)
        
        print("✅ 统一任务表创建成功")
        
        # 3. 创建索引
        print("\n📊 创建索引...")
        
        indexes = [
            "CREATE INDEX idx_unified_tasks_task_id ON unified_tasks(task_id)",
            "CREATE INDEX idx_unified_tasks_celery_task_id ON unified_tasks(celery_task_id)",
            "CREATE INDEX idx_unified_tasks_type ON unified_tasks(task_type)",
            "CREATE INDEX idx_unified_tasks_subtype ON unified_tasks(task_subtype)",
            "CREATE INDEX idx_unified_tasks_status ON unified_tasks(status)",
            "CREATE INDEX idx_unified_tasks_user_id ON unified_tasks(user_id)",
            "CREATE INDEX idx_unified_tasks_created_at ON unified_tasks(created_at)",
            "CREATE INDEX idx_unified_tasks_priority ON unified_tasks(priority)"
        ]
        
        for index_sql in indexes:
            try:
                cursor.execute(index_sql)
                print(f"✅ 索引创建成功")
            except Exception as e:
                print(f"⚠️ 索引创建失败: {e}")
        
        # 4. 创建触发器自动更新 updated_at
        print("\n⚡ 创建触发器...")
        
        cursor.execute("""
            CREATE OR REPLACE FUNCTION update_updated_at_column()
            RETURNS TRIGGER AS $$
            BEGIN
                NEW.updated_at = CURRENT_TIMESTAMP;
                RETURN NEW;
            END;
            $$ language 'plpgsql'
        """)
        
        cursor.execute("""
            CREATE TRIGGER update_unified_tasks_updated_at 
            BEFORE UPDATE ON unified_tasks 
            FOR EACH ROW EXECUTE FUNCTION update_updated_at_column()
        """)
        
        print("✅ 触发器创建成功")
        
        # 5. 插入测试数据
        print("\n🧪 插入测试数据...")
        
        test_tasks = [
            {
                'task_id': 'test-task-001',
                'task_type': 'video_generation',
                'task_subtype': 'text_to_video',
                'user_id': 'demo-user',
                'title': '测试视频生成任务',
                'description': '这是一个测试任务',
                'status': 'completed',
                'progress': 100,
                'message': '任务完成',
                'input_params': '{"prompt": "测试提示词", "model": "t2v-1.3B"}',
                'output_data': '{"video_url": "/api/v1/video/download/test.mp4"}',
                'priority': 5
            },
            {
                'task_id': 'test-task-002',
                'task_type': 'digital_human',
                'task_subtype': 'avatar_generation',
                'user_id': 'demo-user',
                'title': '测试数字人生成',
                'description': '数字人头像生成测试',
                'status': 'processing',
                'progress': 60,
                'message': '正在生成中...',
                'priority': 7
            }
        ]
        
        for task in test_tasks:
            cursor.execute("""
                INSERT INTO unified_tasks
                (task_id, task_type, task_subtype, user_id, title, description,
                 status, progress, message, input_params, output_data, priority)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, [
                task['task_id'], task['task_type'], task['task_subtype'],
                task['user_id'], task['title'], task['description'],
                task['status'], task['progress'], task['message'],
                task.get('input_params'), task.get('output_data'), task['priority']
            ])
        
        print(f"✅ 插入 {len(test_tasks)} 条测试数据")
        
        # 6. 验证表结构
        print("\n🔍 验证表结构...")
        
        cursor.execute("""
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'unified_tasks'
            ORDER BY ordinal_position
        """)
        
        columns = cursor.fetchall()
        print(f"📋 表字段 ({len(columns)} 个):")
        for col in columns:
            nullable = "NULL" if col[2] == "YES" else "NOT NULL"
            default = f" DEFAULT {col[3]}" if col[3] else ""
            print(f"   {col[0]}: {col[1]} {nullable}{default}")
        
        # 7. 验证数据
        cursor.execute("SELECT COUNT(*) FROM unified_tasks")
        count = cursor.fetchone()[0]
        print(f"\n📊 表中记录数: {count}")
        
        # 提交所有更改
        conn.commit()
        cursor.close()
        conn.close()
        
        print(f"\n🎉 数据库重置完成！")
        print(f"✅ 统一任务表已创建并初始化")
        print(f"✅ 索引和触发器已设置")
        print(f"✅ 测试数据已插入")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库重置失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def verify_task_manager():
    """验证任务管理器"""
    print(f"\n🧪 验证任务管理器...")
    
    try:
        from app.core.task_manager import task_manager
        
        # 测试获取任务
        task = task_manager.get_task('test-task-001')
        if task:
            print(f"✅ 任务查询成功: {task['title']}")
        else:
            print(f"❌ 任务查询失败")
            return False
        
        # 测试获取用户任务
        user_tasks = task_manager.get_user_tasks('demo-user')
        print(f"✅ 用户任务查询成功: {len(user_tasks)} 个任务")
        
        # 测试统计
        stats = task_manager.get_task_stats('demo-user')
        print(f"✅ 任务统计查询成功: {stats}")
        
        return True
        
    except Exception as e:
        print(f"❌ 任务管理器验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔄 统一任务数据库重置工具")
    print("=" * 60)
    
    # 重置数据库
    db_ok = reset_database()
    
    if db_ok:
        # 验证任务管理器
        manager_ok = verify_task_manager()
        
        if manager_ok:
            print(f"\n🎯 重置完成！现在可以运行统一任务测试:")
            print(f"   python backend/scripts/test_unified_tasks.py")
        else:
            print(f"\n⚠️ 数据库重置成功，但任务管理器验证失败")
    else:
        print(f"\n❌ 数据库重置失败")

if __name__ == "__main__":
    main()
