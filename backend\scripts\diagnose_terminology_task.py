#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
术语采集任务诊断脚本
用于诊断特定术语采集任务的详细信息和结果
"""

import os
import sys
import logging
import argparse
from datetime import datetime
from sqlalchemy import desc

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

from models.terminology_collection import TerminologyCollectionTask, TerminologyCollectionResult
from utils.db import SessionLocal
from services.celery_tasks.terminology_tasks import determine_domain

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("terminology_diagnosis")

# 添加控制台处理器
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.INFO)
console_formatter = logging.Formatter('%(message)s')  # 简化控制台输出格式
console_handler.setFormatter(console_formatter)
logger.addHandler(console_handler)

def diagnose_terminology_task(task_id):
    """
    诊断术语采集任务
    """
    print(f"开始诊断术语采集任务 - 任务ID: {task_id}")
    
    # 创建数据库会话
    db = SessionLocal()
    
    try:
        # 查询任务
        task = db.query(TerminologyCollectionTask).filter(TerminologyCollectionTask.task_id == task_id).first()
        
        if not task:
            print(f"❌ 任务不存在 - 任务ID: {task_id}")
            return
        
        print(f"✅ 找到任务")
        print(f"任务信息:")
        print(f"- ID: {task.task_id}")
        print(f"- 名称: {task.name}")
        print(f"- 状态: {task.status}")
        print(f"- 进度: {task.progress}%")
        print(f"- 创建时间: {task.created_at}")
        print(f"- 完成时间: {task.completed_at}")
        print(f"- 错误信息: {task.error_message}")
        print(f"- 关键词: {task.keywords}")
        print(f"- 模式: {task.mode}")
        print(f"- 语言: {task.language}")
        
        # 查询结果
        results = db.query(TerminologyCollectionResult).filter(
            TerminologyCollectionResult.task_id == task_id
        ).order_by(desc(TerminologyCollectionResult.confidence)).all()
        
        print(f"\n结果数量: {len(results)}")
        
        if results:
            print("\n✅ 找到结果")
            print("\n示例结果:")
            for i, result in enumerate(results[:5]):  # 只显示前5个结果
                print(f"结果 {i+1}:")
                print(f"- 术语: {result.term}")
                print(f"- 定义: {result.definition}")
                print(f"- 词性: {result.pos}")
                print(f"- 置信度: {result.confidence}")
                print(f"- 来源URL: {result.source_url}")
                print("---")
            
            if len(results) > 5:
                print(f"... 还有 {len(results) - 5} 个结果未显示")
        else:
            print("❌ 没有找到任何结果")
            print("可能的原因:")
            print("1. 任务执行失败")
            print("2. 任务执行成功但没有生成结果")
            print("3. 结果被删除")
            
            # 检查任务名称和关键词，判断应该生成什么领域的术语
            domain = determine_domain(task.name, task.keywords)
            print(f"\n根据任务名称和关键词，应该生成 {domain} 领域的术语")
    
    except Exception as e:
        print(f"❌ 诊断出错: {str(e)}")
        import traceback
        print(traceback.format_exc())
    
    finally:
        db.close()

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="诊断术语采集任务")
    parser.add_argument("task_id", help="任务ID")
    
    args = parser.parse_args()
    
    diagnose_terminology_task(args.task_id)

if __name__ == "__main__":
    main()