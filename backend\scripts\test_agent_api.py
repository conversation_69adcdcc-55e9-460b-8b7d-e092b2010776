#!/usr/bin/env python3
"""
测试智能体 API
"""
import requests
import json

def test_agent_api():
    """测试智能体 API"""
    print("🤖 测试智能体 API")
    print("=" * 50)
    
    agent_id = "e55f5e84-6d8b-4265-8e55-728bdb0d2455"
    base_url = "http://localhost:8000"
    
    try:
        # 测试获取智能体详情
        url = f"{base_url}/api/v1/agents/{agent_id}"
        print(f"📊 请求 URL: {url}")
        
        response = requests.get(url, timeout=10)
        print(f"📊 响应状态码: {response.status_code}")
        print(f"📊 响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"✅ API 调用成功:")
                print(f"   成功: {data.get('success')}")
                if data.get('success'):
                    agent = data.get('agent', {})
                    print(f"   智能体ID: {agent.get('id')}")
                    print(f"   智能体名称: {agent.get('name')}")
                    print(f"   智能体类型: {agent.get('agent_type')}")
                    print(f"   智能体状态: {agent.get('status')}")
                    print(f"   智能体描述: {agent.get('description', '')[:50]}...")
                    return True
                else:
                    print(f"   错误: {data.get('error')}")
                    return False
            except json.JSONDecodeError as e:
                print(f"❌ JSON 解析失败: {e}")
                print(f"   响应内容: {response.text[:200]}...")
                return False
        else:
            print(f"❌ API 调用失败: {response.status_code}")
            print(f"   响应内容: {response.text[:200]}...")
            return False
        
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他异常: {e}")
        return False

def test_agent_chat():
    """测试智能体聊天"""
    print(f"\n💬 测试智能体聊天")
    print("=" * 50)
    
    agent_id = "e55f5e84-6d8b-4265-8e55-728bdb0d2455"
    base_url = "http://localhost:8000"
    
    try:
        # 测试聊天接口
        url = f"{base_url}/api/v1/agents/{agent_id}/chat"
        print(f"📊 请求 URL: {url}")
        
        chat_data = {
            "message": "Hello! Can you help me learn English?",
            "user_id": "test-user"
        }
        
        response = requests.post(
            url, 
            json=chat_data,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"✅ 聊天 API 调用成功:")
                print(f"   成功: {data.get('success')}")
                if data.get('success'):
                    response_text = data.get('response', '')
                    print(f"   智能体回复: {response_text}")
                    return True
                else:
                    print(f"   错误: {data.get('error')}")
                    return False
            except json.JSONDecodeError as e:
                print(f"❌ JSON 解析失败: {e}")
                print(f"   响应内容: {response.text[:200]}...")
                return False
        else:
            print(f"❌ 聊天 API 调用失败: {response.status_code}")
            print(f"   响应内容: {response.text[:200]}...")
            return False
        
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他异常: {e}")
        return False

def test_agent_list():
    """测试智能体列表"""
    print(f"\n📋 测试智能体列表")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    try:
        # 测试列表接口
        url = f"{base_url}/api/v1/agents/list"
        print(f"📊 请求 URL: {url}")
        
        response = requests.get(url, timeout=10)
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"✅ 列表 API 调用成功:")
                print(f"   成功: {data.get('success')}")
                if data.get('success'):
                    agents = data.get('agents', [])
                    total = data.get('total', 0)
                    print(f"   智能体总数: {total}")
                    print(f"   当前页智能体数: {len(agents)}")
                    for i, agent in enumerate(agents[:3]):
                        print(f"   {i+1}. {agent.get('name')} ({agent.get('agent_type')})")
                    return True
                else:
                    print(f"   错误: {data.get('error')}")
                    return False
            except json.JSONDecodeError as e:
                print(f"❌ JSON 解析失败: {e}")
                print(f"   响应内容: {response.text[:200]}...")
                return False
        else:
            print(f"❌ 列表 API 调用失败: {response.status_code}")
            print(f"   响应内容: {response.text[:200]}...")
            return False
        
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他异常: {e}")
        return False

def main():
    """主函数"""
    print("🧪 智能体 API 测试")
    print("=" * 60)
    
    # 1. 测试智能体详情获取
    agent_ok = test_agent_api()
    
    # 2. 测试智能体列表
    list_ok = test_agent_list()
    
    # 3. 如果智能体详情正常，测试聊天
    chat_ok = False
    if agent_ok:
        chat_ok = test_agent_chat()
    
    # 总结
    print(f"\n📊 测试总结:")
    print(f"{'✅' if agent_ok else '❌'} 智能体详情: {'成功' if agent_ok else '失败'}")
    print(f"{'✅' if list_ok else '❌'} 智能体列表: {'成功' if list_ok else '失败'}")
    print(f"{'✅' if chat_ok else '❌'} 智能体聊天: {'成功' if chat_ok else '失败/跳过'}")
    
    if agent_ok:
        print(f"\n🎉 智能体 API 基本功能正常！")
        if chat_ok:
            print(f"💬 聊天功能也正常工作")
            print(f"💡 前端智能体聊天系统应该能正常使用")
        else:
            print(f"⚠️ 聊天功能可能需要检查")
    else:
        print(f"\n❌ 智能体 API 有问题")
        print(f"💡 需要检查后端服务和配置")

if __name__ == "__main__":
    main()
