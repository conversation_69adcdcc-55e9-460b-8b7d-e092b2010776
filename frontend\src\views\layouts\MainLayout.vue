<template>
    <div class="app-container">
      <!-- 顶部导航栏 -->
      <header class="main-header">
        <div class="header-container">
          <div class="logo-section">
            <router-link to="/">
              <div class="logo">
                <span class="logo-icon">
                  <img src="/assets/logo.png" alt="多语数据" width="32" height="32" />
                </span>
                <span class="logo-text">多语数据</span>
              </div>
            </router-link>
          </div>
  
          <div class="menu-section">
            <!-- 自定义MegaMenu -->
            <div class="mega-menu-container">
              <ul class="mega-menu">
                <li class="mega-menu-item">
                  <router-link to="/" class="mega-menu-link">首页</router-link>
                </li>
                <li class="mega-menu-item">
                  <router-link to="/digital-human/app" class="mega-menu-link" @click="handleDigitalHumanClick">数字人</router-link>
                </li>
                <li class="mega-menu-item has-dropdown utilities-menu">
                  <a class="mega-menu-link">翻译服务</a>
                  <div class="mega-dropdown-container">
                    <div class="mega-dropdown-content">
                      <div class="mega-dropdown-section level2-section">
                        <div class="dropdown-links">
                          <div class="level2-item">
                            <h4 class="section-title">在线翻译</h4>
                            <div class="dropdown-links horizontal level3-links">
                              <router-link to="/translation" class="dropdown-link">文本翻译</router-link>
                              <router-link to="/translation/document" class="dropdown-link">文档翻译</router-link>
                              <router-link to="/translation/audio" class="dropdown-link">音频翻译</router-link>
                              <router-link to="/translation/video" class="dropdown-link">视频翻译</router-link>
                            </div>
                          </div>
                          
                          <div class="level2-item">
                            <h4 class="section-title">翻译技术</h4>
                            <div class="dropdown-links horizontal level3-links">
                              <router-link to="/translation/alignment" class="dropdown-link">在线对齐</router-link>
                              <router-link to="/terminology" class="dropdown-link">术语工具</router-link>
                              <!-- 
                                术语工具重构版本可通过以下方式访问：
                                1. 直接访问URL: /terminology-refactored
                                2. 或在router/index.js中更新路由配置
                              -->
                            </div>
                          </div>
                          
                          <div class="level2-item">
                            <h4 class="section-title">AI同传</h4>
                            <div class="dropdown-links horizontal level3-links">
                              <router-link to="/translation/simultaneous" class="dropdown-link">同声传译</router-link>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </li>
                <li class="mega-menu-item has-dropdown">
                  <a class="mega-menu-link">视频生成</a>
                  <div class="mega-dropdown-container">
                    <div class="mega-dropdown-content">
                      <div class="mega-dropdown-section">
                        <div class="dropdown-links">
                          <router-link to="/video-generation" class="dropdown-link">文生视频</router-link>
                          <router-link to="/video-generation/image" class="dropdown-link">图生视频</router-link>
                          <router-link to="/video-generation/history" class="dropdown-link">视频历史</router-link>
                        </div>
                      </div>
                    </div>
                  </div>
                </li>
                <li class="mega-menu-item">
                  <router-link to="/deep-exploration" class="mega-menu-link">深度探索</router-link>
                </li>
                <li class="mega-menu-item has-dropdown utilities-menu">
                  <a class="mega-menu-link">实用工具</a>
                  <div class="mega-dropdown-container">
                    <div class="mega-dropdown-content">
                      <div class="mega-dropdown-section level2-section">
                        <div class="dropdown-links">
                          <div class="level2-item">
                            <h4 class="section-title">转换工具</h4>
                            <div class="dropdown-links horizontal level3-links">
                              <router-link to="/utilities/conversion/document" class="dropdown-link">文档转换</router-link>
                              <router-link to="/utilities/conversion/video" class="dropdown-link">视频转换</router-link>
                              <router-link to="/utilities/conversion/audio" class="dropdown-link">音频转换</router-link>
                              <router-link to="/utilities/conversion/image" class="dropdown-link">图片转换</router-link>
                            </div>
                          </div>
                          
                          <div class="level2-item">
                            <h4 class="section-title">办公工具</h4>
                            <div class="dropdown-links horizontal level3-links">
                              <router-link to="/utilities/office/transcription" class="dropdown-link">录音文件转写</router-link>
                              <router-link to="/utilities/office/text-to-audio" class="dropdown-link">文本转录音</router-link>
                              <router-link to="/utilities/office/reports" class="dropdown-link">日报周报</router-link>
                              <router-link to="/utilities/office/ppt" class="dropdown-link">智能PPT</router-link>
                            </div>
                          </div>
                          
                          <div class="level2-item">
                            <h4 class="section-title">便民工具</h4>
                            <div class="dropdown-links horizontal level3-links">
                              <router-link to="/utilities/daily/agent-marketplace" class="dropdown-link">AI智能体</router-link>
                              <router-link to="/utilities/daily/weather" class="dropdown-link">气象预警</router-link>
                              <router-link to="/utilities/daily/resume" class="dropdown-link">简历制作</router-link>
                              <a href="https://mp.weixin.qq.com/s/xbk40G7nFsYpS7URpdoh7w" target="_blank" class="dropdown-link">链接介绍</a>
                            </div>
                          </div>
                          
                        </div>
                      </div>
                    </div>
                  </div>
                </li>
                
                <!-- 高级应用(注释) -->
                <!-- <li class="mega-menu-item has-dropdown">
                  <a class="mega-menu-link">高级应用 <span class="arrow">▾</span></a>
                  <div class="mega-dropdown-container">
                    <div class="mega-dropdown-content">
                      <div class="mega-dropdown-section">
                        <div class="dropdown-links">
                          <router-link to="/deep-exploration/knowledge-mining" class="dropdown-link">知识挖掘</router-link>
                          <router-link to="/deep-exploration/custom-training" class="dropdown-link">定制训练</router-link>
                          <router-link to="/deep-exploration/advanced-analytics" class="dropdown-link">高级分析</router-link>
                        </div>
                      </div>
                    </div>
                  </div>
                </li> -->
              </ul>
            </div>
          </div>
  
          <div class="action-section">

              <theme-toggle />

              <el-dropdown @command="handleLanguageChange">
                <el-button link>
                  🌐 {{ currentLanguage }}
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="zh-CN">中文</el-dropdown-item>
                    <el-dropdown-item command="en-US">English</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
              
              <!-- 未登录状态 -->
              <template v-if="!isLoggedIn">
                <el-button type="primary" @click="handleLogin">
                  登录
                </el-button>
              </template>
              
              <!-- 已登录状态 -->
              <template v-else>
                <el-dropdown @command="handleMenuClick">
                  <div class="user-info-display">
                    <el-avatar :size="32" style="background-color: #2563EB;">
                      {{ currentUser?.username?.charAt(0)?.toUpperCase() || '👤' }}
                    </el-avatar>
                    <span class="username-text">{{ currentUser?.username || '用户' }}</span>
                  </div>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="profile">
                        👤 个人资料
                      </el-dropdown-item>
                      <el-dropdown-item command="tasks">
                        📋 任务管理
                      </el-dropdown-item>
                      <el-dropdown-item command="settings">
                        ⚙️ 账户设置
                      </el-dropdown-item>
                      <el-dropdown-item divided command="logout">
                        🚪 退出登录
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </template>
          </div>
        </div>
      </header>
  
      <!-- 主要内容区域 -->
      <main class="main-content" :class="{ 'with-utilities-sidebar': isUtilitiesPage }">
        <!-- 实用工具侧边栏 -->
        <div class="utilities-sidebar" v-if="isUtilitiesPage">
          <!-- 顶部分类导航 -->
          <div class="sidebar-header">
            <h3 class="sidebar-title">实用工具分类</h3>
            <div class="search-box">
              <input 
                type="text" 
                v-model="searchQuery" 
                placeholder="搜索工具..." 
                class="search-input"
                @input="handleSearch"
              />
              <search-outlined class="search-icon" />
              <close-circle-outlined 
                v-if="searchQuery" 
                class="clear-icon" 
                @click="clearSearch" 
              />
            </div>
          </div>
          
          <div class="sidebar-category-nav">
            <a @click.prevent="filterTools('all')" class="category-nav-item" :class="{ active: currentCategory === 'all' }">全部</a>
            <a @click.prevent="filterTools('conversion')" class="category-nav-item" :class="{ active: currentCategory === 'conversion' }">转换工具</a>
            <a @click.prevent="filterTools('office')" class="category-nav-item" :class="{ active: currentCategory === 'office' }">办公工具</a>
            <a @click.prevent="filterTools('daily')" class="category-nav-item" :class="{ active: currentCategory === 'daily' }">便民工具</a>
          </div>
          
          <div class="sidebar-content">
            <transition-group name="tool-fade" tag="div" class="card-grid">
              <!-- 转换工具 -->
              <div v-if="shouldShowTool('document')" class="tool-card" :class="{ active: currentUtilityPath === '/utilities/conversion/document' }" key="document">
                <router-link to="/utilities/conversion/document">
                  <div class="tool-card-content">
                    <div class="tool-icon"><file-outlined /></div>
                    <div class="tool-info">
                      <div class="tool-name">文档转换</div>
                      <div class="tool-desc">快速转换各类文档格式</div>
                    </div>
                  </div>
                </router-link>
              </div>
              
              <div v-if="shouldShowTool('video')" class="tool-card" :class="{ active: currentUtilityPath === '/utilities/conversion/video' }" key="video">
                <router-link to="/utilities/conversion/video">
                  <div class="tool-card-content">
                    <div class="tool-icon"><video-camera-outlined /></div>
                    <div class="tool-info">
                      <div class="tool-name">视频转换</div>
                      <div class="tool-desc">各种视频格式的转换工具</div>
                    </div>
                  </div>
                </router-link>
              </div>
              
              <div v-if="shouldShowTool('audio')" class="tool-card" :class="{ active: currentUtilityPath === '/utilities/conversion/audio' }" key="audio">
                <router-link to="/utilities/conversion/audio">
                  <div class="tool-card-content">
                    <div class="tool-icon"><sound-outlined /></div>
                    <div class="tool-info">
                      <div class="tool-name">音频转换</div>
                      <div class="tool-desc">多种音频格式转换</div>
                    </div>
                  </div>
                </router-link>
              </div>
              
              <div v-if="shouldShowTool('image')" class="tool-card" :class="{ active: currentUtilityPath === '/utilities/conversion/image' }" key="image">
                <router-link to="/utilities/conversion/image">
                  <div class="tool-card-content">
                    <div class="tool-icon"><picture-outlined /></div>
                    <div class="tool-info">
                      <div class="tool-name">图片转换</div>
                      <div class="tool-desc">图片格式转换工具</div>
                    </div>
                  </div>
                </router-link>
              </div>
              
              <!-- 办公工具 -->
              <div v-if="shouldShowTool('transcription')" class="tool-card" :class="{ active: currentUtilityPath === '/utilities/office/transcription' }" key="transcription">
                <router-link to="/utilities/office/transcription">
                  <div class="tool-card-content">
                    <div class="tool-icon"><file-text-outlined /></div>
                    <div class="tool-info">
                      <div class="tool-name">录音文件转写</div>
                      <div class="tool-desc">快速将录音转为文字</div>
                    </div>
                  </div>
                </router-link>
              </div>
              
              <div v-if="shouldShowTool('text-to-audio')" class="tool-card" :class="{ active: currentUtilityPath === '/utilities/office/text-to-audio' }" key="text-to-audio">
                <router-link to="/utilities/office/text-to-audio">
                  <div class="tool-card-content">
                    <div class="tool-icon"><audio-outlined /></div>
                    <div class="tool-info">
                      <div class="tool-name">文本转录音</div>
                      <div class="tool-desc">智能文字转语音工具</div>
                    </div>
                  </div>
                </router-link>
              </div>
              
              <div v-if="shouldShowTool('reports')" class="tool-card" :class="{ active: currentUtilityPath === '/utilities/office/reports' }" key="reports">
                <router-link to="/utilities/office/reports">
                  <div class="tool-card-content">
                    <div class="tool-icon"><file-sync-outlined /></div>
                    <div class="tool-info">
                      <div class="tool-name">日报周报</div>
                      <div class="tool-desc">快速生成工作报告</div>
                    </div>
                  </div>
                </router-link>
              </div>
              
              <div v-if="shouldShowTool('ppt')" class="tool-card" :class="{ active: currentUtilityPath === '/utilities/office/ppt' }" key="ppt">
                <router-link to="/utilities/office/ppt">
                  <div class="tool-card-content">
                    <div class="tool-icon"><fund-projection-screen-outlined /></div>
                    <div class="tool-info">
                      <div class="tool-name">智能PPT</div>
                      <div class="tool-desc">一键生成专业演示文稿</div>
                    </div>
                  </div>
                </router-link>
              </div>
              
              <!-- 便民工具 -->
              <div v-if="shouldShowTool('agent-marketplace')" class="tool-card" :class="{ active: currentUtilityPath === '/utilities/daily/agent-marketplace' }" key="agent-marketplace">
                <router-link to="/utilities/daily/agent-marketplace">
                  <div class="tool-card-content">
                    <div class="tool-icon"><robot-outlined /></div>
                    <div class="tool-info">
                      <div class="tool-name">AI智能体</div>
                      <div class="tool-desc">强大的AI智能助手</div>
                    </div>
                  </div>
                </router-link>
              </div>
              
              <div v-if="shouldShowTool('weather')" class="tool-card" :class="{ active: currentUtilityPath === '/utilities/daily/weather' }" key="weather">
                <router-link to="/utilities/daily/weather">
                  <div class="tool-card-content">
                    <div class="tool-icon"><cloud-outlined /></div>
                    <div class="tool-info">
                      <div class="tool-name">气象预警</div>
                      <div class="tool-desc">实时气象信息和预警</div>
                    </div>
                  </div>
                </router-link>
              </div>
              
              <div v-if="shouldShowTool('resume')" class="tool-card" :class="{ active: currentUtilityPath === '/utilities/daily/resume' }" key="resume">
                <router-link to="/utilities/daily/resume">
                  <div class="tool-card-content">
                    <div class="tool-icon"><solution-outlined /></div>
                    <div class="tool-info">
                      <div class="tool-name">简历制作</div>
                      <div class="tool-desc">专业简历一键生成</div>
                    </div>
                  </div>
                </router-link>
              </div>
              
              <!-- 空状态提示 -->
              <div v-if="filteredToolsCount === 0" class="empty-state" key="empty">
                <inbox-outlined class="empty-icon" />
                <p>没有找到符合条件的工具</p>
              </div>
            </transition-group>
          </div>
        </div>
        
        <!-- 页面主内容 -->
        <div class="page-content" :class="{ 'with-utilities-sidebar': isUtilitiesPage }">
          <router-view v-slot="{ Component }">
            <transition name="fade" mode="out-in">
              <component :is="Component" />
            </transition>
          </router-view>
        </div>
      </main>
  
      <!-- 页脚 -->
      <footer class="main-footer">
        <div class="footer-container">
          <div class="footer-content">
            <div class="footer-section">
              <h3 class="footer-title">多语数据</h3>
              <p class="footer-description">专业的人工智能解决方案平台，为您提供高质量的AI服务与工具</p>
            </div>
            
            <div class="footer-section">
              <h3 class="footer-title">联系我们</h3>
              <p><mail-outlined /> <EMAIL></p>
              <p><phone-outlined /> ************</p>
            </div>
            
            <div class="footer-section">
              <h3 class="footer-title">快速链接</h3>
              <ul class="footer-links">
                <li><a href="/about">关于我们</a></li>
                <li><a href="/privacy">隐私政策</a></li>
                <li><a href="/terms">服务条款</a></li>
                <li><a href="/help">帮助中心</a></li>
              </ul>
            </div>
          </div>
          
          <div class="footer-bottom">
            <p>&copy; {{ new Date().getFullYear() }} 多语数据 - 专业人工智能解决方案平台</p>
          </div>
        </div>
      </footer>
    </div>
  </template>
  
  <script>
  import { defineComponent, ref, computed, onMounted, watch } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import {
    UserOutlined,
    SettingOutlined,
    LogoutOutlined,
    GlobalOutlined,
    MailOutlined,
    PhoneOutlined,
    FileOutlined,
    FileTextOutlined,
    FileSyncOutlined,
    VideoCameraOutlined,
    AudioOutlined,
    SoundOutlined,
    PictureOutlined,
    LaptopOutlined,
    ScheduleOutlined,
    MessageOutlined,
    RobotOutlined,
    CloudOutlined,
    SolutionOutlined,
    FundProjectionScreenOutlined,
    InboxOutlined,
    SearchOutlined,
    CloseCircleOutlined
  } from '@ant-design/icons-vue';
  import { ElMessage } from 'element-plus';
  import ThemeToggle from '@/components/common/ThemeToggle.vue';
  import { useAuthStore } from '@/auth';

  export default defineComponent({
    name: 'MainLayout',
    components: {
      UserOutlined,
      SettingOutlined,
      LogoutOutlined,
      GlobalOutlined,
      MailOutlined,
      PhoneOutlined,
      FileOutlined,
      FileTextOutlined,
      FileSyncOutlined,
      VideoCameraOutlined,
      AudioOutlined,
      SoundOutlined,
      PictureOutlined,
      LaptopOutlined,
      ScheduleOutlined,
      MessageOutlined,
      RobotOutlined,
      CloudOutlined,
      SolutionOutlined,
      FundProjectionScreenOutlined,
      InboxOutlined,
      SearchOutlined,
      CloseCircleOutlined,
      ThemeToggle
    },
    setup() {
      const route = useRoute();
      const router = useRouter();
      const authStore = useAuthStore();

      // 简单的语言管理
      const currentLang = ref('zh-CN');

      // 当前选中的分类
      const currentCategory = ref('all');
      
      // 搜索关键词
      const searchQuery = ref('');
      
      // 工具数据
      const toolsData = [
        { id: 'document', name: '文档转换', desc: '快速转换各类文档格式', category: 'conversion', path: '/utilities/conversion/document' },
        { id: 'video', name: '视频转换', desc: '各种视频格式的转换工具', category: 'conversion', path: '/utilities/conversion/video' },
        { id: 'audio', name: '音频转换', desc: '多种音频格式转换', category: 'conversion', path: '/utilities/conversion/audio' },
        { id: 'image', name: '图片转换', desc: '图片格式转换工具', category: 'conversion', path: '/utilities/conversion/image' },
        { id: 'transcription', name: '录音文件转写', desc: '快速将录音转为文字', category: 'office', path: '/utilities/office/transcription' },
        { id: 'text-to-audio', name: '文本转录音', desc: '智能文字转语音工具', category: 'office', path: '/utilities/office/text-to-audio' },
        { id: 'reports', name: '日报周报', desc: '快速生成工作报告', category: 'office', path: '/utilities/office/reports' },
        { id: 'ppt', name: '智能PPT', desc: '一键生成专业演示文稿', category: 'office', path: '/utilities/office/ppt' },
        { id: 'agent-marketplace', name: 'AI智能体', desc: '强大的AI智能助手', category: 'daily', path: '/utilities/daily/agent-marketplace' },
        { id: 'weather', name: '气象预警', desc: '实时气象信息和预警', category: 'daily', path: '/utilities/daily/weather' },
        { id: 'resume', name: '简历制作', desc: '专业简历一键生成', category: 'daily', path: '/utilities/daily/resume' },
        { id: 'link-intro', name: '链接介绍', desc: '查看功能介绍和使用指南', category: 'daily', path: 'https://mp.weixin.qq.com/s/xbk40G7nFsYpS7URpdoh7w', external: true },
      ];

      // 判断是否为首页
      const isHomePage = computed(() => {
        return route.path === '/' || route.path === '/home';
      });
      
      // 判断当前是否为实用工具页面
      const isUtilitiesPage = computed(() => {
        return route.path.includes('/utilities/');
      });
      
      // 获取当前实用工具路径，用于侧边栏菜单高亮
      const currentUtilityPath = computed(() => {
        return route.path;
      });
      
      // 筛选工具
      const filterTools = (category) => {
        console.log('筛选工具:', category);
        currentCategory.value = category;
        // 添加弹性视觉反馈
        const navItems = document.querySelectorAll('.category-nav-item');
        navItems.forEach(item => {
          if (item.textContent.includes(getCategoryText(category))) {
            item.classList.add('category-active-animation');
            setTimeout(() => {
              item.classList.remove('category-active-animation');
            }, 300);
          }
        });
      };
      
      // 获取分类文本
      const getCategoryText = (category) => {
        switch(category) {
          case 'all': return '全部';
          case 'conversion': return '转换工具';
          case 'office': return '办公工具';
          case 'daily': return '便民工具';
          default: return '';
        }
      };
      
      // 判断是否显示工具
      const shouldShowTool = (toolId) => {
        const tool = toolsData.find(t => t.id === toolId);
        if (!tool) return false;
        
        // 如果有搜索关键词，检查工具名称和描述是否包含关键词
        if (searchQuery.value) {
          return tool.name.toLowerCase().includes(searchQuery.value.toLowerCase()) || 
                 tool.desc.toLowerCase().includes(searchQuery.value.toLowerCase());
        }
        
        // 否则按类别过滤
        return currentCategory.value === 'all' || tool.category === currentCategory.value;
      };
      
      // 处理搜索
      const handleSearch = () => {
        // 如果搜索框有内容，自动将分类设为全部
        if (searchQuery.value) {
          currentCategory.value = 'all';
        }
      };
      
      // 清除搜索
      const clearSearch = () => {
        searchQuery.value = '';
      };
      
      // 计算过滤后的工具数量
      const filteredToolsCount = computed(() => {
        if (searchQuery.value) {
          return toolsData.filter(tool => 
            tool.name.includes(searchQuery.value) || 
            tool.desc.includes(searchQuery.value)
          ).length;
        }
        
        if (currentCategory.value === 'all') return toolsData.length;
        return toolsData.filter(tool => tool.category === currentCategory.value).length;
      });
      
      // 语言相关
      const currentLanguage = computed(() => {
        return currentLang.value === 'zh-CN' ? '中文' : 'English';
      });

      // 使用auth系统的登录状态
      const isLoggedIn = computed(() => authStore.isAuthenticated);
      const currentUser = computed(() => authStore.user);

      // 处理登录
      const handleLogin = () => {
        router.push('/user/login');
      };

      // 处理退出登录
      const handleLogout = () => {
        authStore.logout();
        ElMessage.success('已退出登录');
        router.push('/');
      };

      // 处理菜单点击
      const handleMenuClick = (command) => {
        console.log('[MainLayout] 菜单点击:', command);
        if (command === 'logout') {
          handleLogout();
        } else if (command === 'profile') {
          router.push('/user/profile');
        } else if (command === 'settings') {
          router.push('/user/settings');
        } else if (command === 'tasks') {
          router.push('/user/tasks');
        }
      };

      // 语言切换
      const handleLanguageChange = (lang) => {
        currentLang.value = lang;
        console.log(`语言切换到: ${lang}`);
      };

      // 处理数字人点击
      const handleDigitalHumanClick = (event) => {
        event.preventDefault();
        console.log('[MainLayout] 数字人链接点击，强制导航到 /digital-human/app');
        router.push('/digital-human/app');
      };

      // 组件挂载时初始化auth状态
      onMounted(() => {
        // auth系统会自动从localStorage恢复状态
      });

      return {
        currentLanguage,
        isHomePage,
        isLoggedIn,
        currentUser,
        isUtilitiesPage,
        currentUtilityPath,
        currentCategory,
        filterTools,
        shouldShowTool,
        filteredToolsCount,
        searchQuery,
        handleLanguageChange,
        handleLogin,
        handleLogout,
        handleMenuClick,
        handleDigitalHumanClick
      };
    }
  });
  </script>
  
  <style scoped>
  .app-container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
  }
  
  /* 顶部导航栏样式 */
  .main-header {
    position: sticky;
    top: 0;
    z-index: 100;
    background-color: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.04);
    border-bottom: 1px solid rgba(0, 0, 0, 0.03);
  }
  
  .header-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1440px;
    margin: 0 auto;
    padding: 0 48px;
    height: 64px;
    width: 100%;
    min-width: 0;
    overflow: visible; /* 允许下拉菜单显示 */
    box-sizing: border-box;
  }
  
  .logo-section {
    flex-shrink: 0;
    min-width: 0;
    overflow: hidden;
  }

  .logo-section a {
    text-decoration: none;
  }

  .logo {
    display: flex;
    align-items: center;
    gap: 8px;
  }
  
  .logo-text {
    font-size: 22px;
    font-weight: 700;
    color: #1A1A2E;
    background: linear-gradient(135deg, #2563EB, #4338CA);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    letter-spacing: -0.5px;
  }
  
  .menu-section {
    flex: 1;
    display: flex;
    justify-content: center;
  }
  
  .action-section {
    flex-shrink: 1;
    min-width: 0;
    display: flex;
    align-items: center;
    gap: 6px;
    overflow: visible; /* 允许下拉菜单显示 */
    max-width: 300px; /* 限制最大宽度 */
  }

  .user-info-display {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 6px;
    transition: background-color 0.2s;
    min-width: 0; /* 允许收缩 */
    max-width: 120px; /* 限制最大宽度 */
    overflow: hidden; /* 防止超出 */
    flex-shrink: 1; /* 允许收缩 */
    box-sizing: border-box; /* 包含padding在宽度内 */
  }

  .user-info-display:hover {
    background-color: #f5f5f5;
  }

  .username-text {
    color: #333;
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 80px; /* 更严格的宽度限制 */
    flex-shrink: 1; /* 允许收缩 */
    min-width: 0; /* 允许收缩到0 */
  }
  
  /* 主要内容区域样式 */
  .main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-image: 
      radial-gradient(circle at 100% 0%, rgba(37, 99, 235, 0.02) 0%, transparent 25%),
      radial-gradient(circle at 0% 80%, rgba(79, 70, 229, 0.02) 0%, transparent 30%);
    margin-bottom: auto;
  }
  
  /* 有实用工具侧边栏时的主内容区域布局 */
  .main-content.with-utilities-sidebar {
    flex-direction: row;
    align-items: flex-start;
  }
  
  /* 页面内容区域样式 */
  .page-content {
    flex: 1;
    padding: 0 20px 20px;
    min-height: calc(100vh - 64px - 200px); /* 减去头部和底部的高度 */
  }
  
  .page-content.with-utilities-sidebar {
    padding: 20px 24px;
    background-color: #f9fafc;
  }
  
  /* 页脚样式 */
  .main-footer {
    background-color: #f8fafc;
    background-image: linear-gradient(to bottom, transparent, rgba(37, 99, 235, 0.02));
    border-top: 1px solid rgba(0, 0, 0, 0.03);
    padding: 48px 0 24px;
    width: 100%;
    margin-top: auto; /* 自动推到底部 */
  }
  
  .footer-container {
    max-width: 1440px;
    margin: 0 auto;
    padding: 0 48px;
  }
  
  .footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    gap: 40px;
    margin-bottom: 32px;
  }
  
  .footer-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 16px;
    color: #1A1A2E;
    position: relative;
    display: inline-block;
  }
  
  .footer-title:after {
    content: '';
    position: absolute;
    bottom: -6px;
    left: 0;
    width: 32px;
    height: 2px;
    background: linear-gradient(to right, #2563EB, transparent);
  }
  
  .footer-description {
    color: #4F4F6F;
    max-width: 300px;
  }
  
  .footer-links {
    list-style: none;
    padding: 0;
  }
  
  .footer-links li {
    margin-bottom: 8px;
  }
  
  .footer-links a {
    color: #4F4F6F;
    transition: color 0.2s ease;
  }
  
  .footer-links a:hover {
    color: #2563EB;
  }
  
  .footer-bottom {
    padding-top: 24px;
    border-top: 1px solid rgba(0, 0, 0, 0.06);
    text-align: center;
    color: #4F4F6F;
    font-size: 14px;
  }
  
  /* 响应式调整 */
  @media (max-width: 992px) {
    .header-container,
    .footer-container {
      padding: 0 24px;
    }
    
    .footer-content {
      grid-template-columns: 1fr;
      gap: 24px;
    }
    
    .footer-description {
      max-width: none;
    }
    
    /* 平板响应式 */
    .utilities-sidebar {
      width: 240px;
      padding: 20px 12px;
    }
  }
  
  @media (max-width: 768px) {
    .header-container {
      padding: 0 12px;
      height: 56px;
      max-width: 100vw;
      box-sizing: border-box;
    }

    .logo-text {
      font-size: 16px;
    }

    /* 隐藏复杂的菜单，只保留基本功能 */
    .menu-section {
      display: none;
    }

    .action-section {
      gap: 4px;
      max-width: 50vw;
    }

    /* 在移动端隐藏主题切换，但保留所有下拉菜单 */
    .action-section > theme-toggle {
      display: none;
    }

    .user-info-display {
      max-width: 100px;
      gap: 4px;
      padding: 2px 6px;
    }

    .username-text {
      max-width: 60px;
      font-size: 13px;
    }

    /* 手机响应式 */
    .main-content.with-utilities-sidebar {
      flex-direction: column;
    }

    .utilities-sidebar {
      width: 100%;
      height: auto;
      position: relative;
      top: 0;
      padding: 16px;
      border-bottom: 1px solid rgba(0, 0, 0, 0.06);
    }

    .utilities-sidebar :deep(.ant-menu-item) {
      margin: 6px 0;
      height: 44px;
      line-height: 44px;
    }

    .page-content.with-utilities-sidebar {
      padding-left: 20px;
    }

    .page-content {
      padding: 0 16px 20px;
    }
  }

  @media (max-width: 480px) {
    .header-container {
      padding: 0 8px;
      height: 52px;
      max-width: 100vw;
      box-sizing: border-box;
    }

    .logo-text {
      font-size: 16px;
    }

    .page-content {
      padding: 0 12px 20px;
    }

    .action-section {
      gap: 4px;
      max-width: 50vw;
    }

    .user-info-display {
      gap: 4px !important;
      padding: 2px 4px;
      max-width: 80px;
    }

    .username-text {
      font-size: 12px;
      max-width: 40px;
    }
  }

  /* 自定义MegaMenu样式 */
  .mega-menu-container {
    display: flex;
    justify-content: center;
    width: 100%;
  }

  .mega-menu {
    display: flex;
    align-items: center;
    list-style: none;
    margin: 0;
    padding: 0;
    height: 64px;
  }

  .mega-menu-item {
    position: relative;
    height: 100%;
    display: flex;
    align-items: center;
  }

  .mega-menu-link {
    padding: 0 16px;
    height: 100%;
    display: flex;
    align-items: center;
    font-size: 15px;
    color: #333;
    font-weight: 500;
    cursor: pointer;
    transition: color 0.2s ease;
    text-decoration: none;
  }

  .mega-menu-link:hover {
    color: #2563EB;
  }

  .mega-menu-item.has-dropdown {
    position: relative;
  }

  .mega-menu-item.has-dropdown:hover .mega-dropdown-container {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }

  /* 下拉菜单容器 - 基本样式 */
  .mega-dropdown-container {
    position: absolute;
    top: 100%;
    left: 0;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.25s ease;
  }

  /* 二级菜单专用样式（翻译服务和视频生成） */
  .mega-menu-item:nth-child(3) .mega-dropdown-container,
  .mega-menu-item:nth-child(4) .mega-dropdown-container {
    min-width: 140px;
    padding: 8px 12px;
  }

  /* 实用工具的特殊样式（三级菜单） */
  .utilities-menu .mega-dropdown-container {
    width: 400px !important;
    min-width: 300px !important;
    padding: 20px !important;
    left: auto !important;
    right: 0 !important;
  }

  /* 基本内容布局 */
  .mega-dropdown-content {
    display: flex;
    flex-wrap: wrap;
  }

  /* 实用工具二级菜单整体布局 */
  .level2-section {
    width: 100%;
  }

  /* 二级菜单项样式 */
  .level2-item {
    margin-bottom: 24px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding-bottom: 16px;
  }

  .level2-item:last-child {
    margin-bottom: 0;
    border-bottom: none;
  }
  
  /* 二级菜单标题样式 */
  .level2-item .section-title {
    font-size: 15px;
    font-weight: 600;
    color: #333;
    margin: 0 0 12px 0;
    padding-bottom: 8px;
    border-bottom: none;
  }

  /* 基本链接容器样式 */
  .dropdown-links {
    display: flex;
    flex-direction: column;
    gap: 0;
  }

  /* 二级菜单链接样式（翻译服务和视频生成） */
  .mega-menu-item:nth-child(3) .dropdown-link,
  .mega-menu-item:nth-child(4) .dropdown-link {
    padding: 7px 0;
    font-size: 13px;
  }

  /* 水平链接容器样式 */
  .dropdown-links.horizontal {
    flex-direction: row;
    flex-wrap: wrap;
    gap: 10px;
    display: inline-flex;
    width: 100%;
  }

  /* 基本链接样式 */
  .dropdown-link {
    font-size: 14px;
    color: #555;
    padding: 6px 0;
    text-decoration: none;
    transition: color 0.2s ease;
    white-space: nowrap;
    display: block;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  }

  /* 最后一个链接没有边框 */
  .dropdown-link:last-child {
    border-bottom: none;
  }

  /* 链接悬停效果 */
  .dropdown-link:hover {
    color: #2563EB;
  }

  /* 三级菜单链接容器样式 */
  .level3-links {
    padding-left: 0;
  }

  /* 三级菜单水平链接样式 */
  .level3-links .dropdown-link {
    margin-right: 24px;
    padding: 6px 0;
    position: relative;
    border-bottom: none;
    font-size: 13px;
    color: #666;
  }

  /* 三级菜单水平链接分隔线 */
  .level3-links .dropdown-link:not(:last-child)::after {
    content: "";
    width: 1px;
    height: 12px;
    background-color: #e0e0e0;
    position: absolute;
    right: -12px;
    top: 50%;
    transform: translateY(-50%);
  }

  /* 保持右侧对齐 */
  .mega-menu-item:nth-child(n+4):not(:last-child) .mega-dropdown-container {
    left: auto;
    right: 0;
  }

  /* 菜单增强样式 */
  :deep(.ant-menu) {
    border-bottom: none;
  }

  :deep(.ant-menu-horizontal) {
    line-height: 40px;
  }

  :deep(.ant-menu-submenu-title) {
    font-weight: 500;
    transition: all 0.3s ease;
  }

  :deep(.ant-menu-submenu) {
    padding: 0 2px;
  }

  :deep(.ant-menu-submenu:hover > .ant-menu-submenu-title) {
    color: #2563EB;
  }

  /* 子菜单一览式展示效果 */
  :deep(.ant-menu-submenu-popup) {
    animation: fadeIn 0.2s ease;
  }

  :deep(.ant-menu-submenu-popup .ant-menu) {
    border-radius: 8px;
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12);
    padding: 6px;
    min-width: 200px;
  }

  /* 一级子菜单项样式 */
  :deep(.ant-menu-submenu-popup .ant-menu-item) {
    border-radius: 4px;
    margin: 2px 0;
    padding: 0 16px;
    line-height: 36px;
    height: 36px;
  }

  /* 二级子菜单容器样式 - 关键部分 */
  :deep(.ant-menu-submenu-popup .ant-menu-submenu) {
    position: static !important;
    float: none;
  }

  /* 二级子菜单标题 */
  :deep(.ant-menu-submenu-popup .ant-menu-submenu-title) {
    border-radius: 4px;
    margin: 2px 0;
    padding: 0 16px;
    line-height: 36px;
    height: 36px;
    color: #333;
    font-weight: 600;
  }

  /* 二级子菜单内容区域 - 关键部分 */
  :deep(.ant-menu-submenu-popup .ant-menu-sub) {
    position: static !important;
    box-shadow: none !important;
    background: rgba(0, 0, 0, 0.02);
    border-radius: 4px;
    margin: 4px 0 8px 16px;
    padding: 4px;
    min-height: auto !important;
  }

  /* 二级子菜单项 */
  :deep(.ant-menu-submenu-popup .ant-menu-sub .ant-menu-item) {
    line-height: 32px;
    height: 32px;
    padding-left: 16px;
  }

  /* 菜单悬停效果 */
  :deep(.ant-menu-submenu-popup .ant-menu-item:hover),
  :deep(.ant-menu-submenu-popup .ant-menu-submenu-title:hover) {
    background-color: rgba(37, 99, 235, 0.06);
    color: #2563EB;
  }

  /* 去除默认的子菜单箭头 */
  :deep(.ant-menu-submenu-popup .ant-menu-submenu-arrow) {
    display: none;
  }

  /* 子标题分组样式 */
  :deep(.ant-menu-item-group-title) {
    padding: 6px 8px;
    color: #999;
    font-size: 12px;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  /* 实用工具侧边栏样式 */
  .utilities-sidebar {
    width: 240px;
    background-color: #f8f9fa;
    border-right: 1px solid #eee;
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
  }
  
  /* 顶部分类导航样式 */
  .sidebar-header {
    padding: 16px 16px 12px;
    background-color: #fff;
    border-bottom: 1px solid #eee;
  }
  
  .sidebar-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0;
  }
  
  .sidebar-category-nav {
    display: flex;
    flex-wrap: wrap;
    padding: 12px 12px 4px;
    background-color: #fff;
    gap: 8px;
  }
  
  .category-nav-item {
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 14px;
    color: #555;
    text-decoration: none;
    background-color: #f0f2f5;
    transition: all 0.2s;
    border: 2px solid transparent;
  }
  
  .category-nav-item:hover {
    background-color: #e6f7ff;
    color: #1890ff;
  }
  
  .category-nav-item.active {
    background-color: #e6f7ff;
    color: #1890ff;
    border: 2px solid #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }
  
  .sidebar-content {
    padding: 16px;
    flex-grow: 1;
    overflow-y: auto;
  }
  
  /* 侧边栏菜单列表 */
  .card-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .tool-card {
    border-radius: 8px;
    background-color: #fff;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    transition: all 0.2s;
    overflow: hidden;
    border: 2px solid transparent;
  }
  
  .tool-card:hover {
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }
  
  .tool-card.active {
    transform: translateY(-2px);
    border: 2px solid #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2), 0 3px 6px rgba(0, 0, 0, 0.1);
  }
  
  .tool-card a {
    display: block;
    text-decoration: none;
    color: inherit;
  }
  
  .tool-card-content {
    padding: 12px;
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 12px;
  }
  
  .tool-icon {
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f0f2f5;
    border-radius: 8px;
    color: #1890ff;
    font-size: 18px;
    flex-shrink: 0;
  }
  
  .tool-info {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
  }
  
  .tool-name {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    margin-bottom: 4px;
  }
  
  .tool-desc {
    font-size: 12px;
    color: #888;
    line-height: 1.3;
  }
  
  /* 自定义滚动条 */
  .utilities-sidebar::-webkit-scrollbar {
    width: 6px;
  }
  
  .utilities-sidebar::-webkit-scrollbar-track {
    background: #f8f9fa;
  }
  
  .utilities-sidebar::-webkit-scrollbar-thumb {
    background-color: #ddd;
    border-radius: 10px;
  }
  
  /* 响应式调整 */
  @media (max-width: 768px) {
    .utilities-sidebar {
      width: 100%;
      max-height: 300px;
    }
    
    .card-grid {
      grid-template-columns: repeat(2, 1fr);
    }
  }
  
  /* 主内容区域和侧边栏布局 */
  .main-content.with-utilities-sidebar {
    flex-direction: row;
    align-items: flex-start;
    background: #f8f9fa;
  }
  
  /* 页面内容区域样式 */
  .page-content {
    flex: 1;
    padding: 0 20px 20px;
    min-height: calc(100vh - 64px - 200px); /* 减去头部和底部的高度 */
  }
  
  .page-content.with-utilities-sidebar {
    padding: 20px 24px;
    background-color: #f8f9fa;
  }

  /* Resume Builder Enhancement Styles */
  :deep(.resume-builder-container) {
    background-color: #f7f9fc !important;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
  }

  :deep(.resume-chat) {
    background-color: white !important;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
    transition: all 0.3s ease;
  }

  :deep(.resume-chat .chat-container) {
    padding: 24px;
  }

  :deep(.resume-chat .message-content) {
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
  }

  :deep(.resume-chat .bot-message .message-content) {
    background-color: white;
    border: 1px solid rgba(0, 0, 0, 0.03);
  }

  :deep(.resume-chat .user-message .message-content) {
    background-color: #2563EB;
  }

  :deep(.resume-chat .input-container) {
    padding: 16px 24px;
    background-color: white;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
  }

  :deep(.resume-preview-container) {
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
    padding: 24px;
  }

  :deep(.resume-paper) {
    border: 1px solid rgba(0, 0, 0, 0.05);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
    border-radius: 8px;
    overflow: hidden;
  }

  :deep(.template-card) {
    transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }

  :deep(.template-card:hover) {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(37, 99, 235, 0.1);
  }

  :deep(.template-card.selected) {
    border-color: #2563EB;
    box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.2), 0 8px 20px rgba(37, 99, 235, 0.1);
  }

  /* Resume Generator Page Enhancements */
  :deep(.resume-generator-container) {
    background-color: transparent;
  }

  :deep(.resume-builder-card),
  :deep(.user-resumes-area .ant-card) {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
    border: none;
  }

  :deep(.resume-actions .ant-btn) {
    border-radius: 8px;
  }

  :deep(.resume-thumbnail-container) {
    border-radius: 8px 8px 0 0;
    overflow: hidden;
  }

  /* 工具卡片过渡动画 */
  .tool-fade-enter-active, 
  .tool-fade-leave-active {
    transition: all 0.3s ease;
  }

  .tool-fade-enter-from, 
  .tool-fade-leave-to {
    opacity: 0;
    transform: translateY(20px);
  }

  .tool-fade-move {
    transition: transform 0.3s ease;
  }

  /* 分类导航项动画 */
  .category-active-animation {
    animation: pulse 0.3s ease;
  }

  @keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
  }

  /* 空状态样式 */
  .empty-state {
    text-align: center;
    padding: 30px 20px;
    color: #888;
    width: 100%;
    background-color: #f9f9f9;
    border-radius: 8px;
    box-shadow: inset 0 0 6px rgba(0,0,0,0.03);
  }

  .empty-icon {
    font-size: 32px;
    margin-bottom: 8px;
    color: #ccc;
  }

  /* 搜索框样式 */
  .search-box {
    position: relative;
    margin-top: 10px;
  }

  .search-input {
    width: 100%;
    padding: 8px 12px;
    padding-right: 30px;
    border: 1px solid #e0e0e0;
    border-radius: 16px;
    font-size: 13px;
    transition: all 0.3s;
    background-color: #f5f5f5;
  }

  .search-input:focus {
    outline: none;
    border-color: #40a9ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    background-color: #fff;
  }

  .search-icon {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
    pointer-events: none;
  }

  .clear-icon {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
    cursor: pointer;
    transition: color 0.2s;
  }

  .clear-icon:hover {
    color: #666;
  }
  </style>