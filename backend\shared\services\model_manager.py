"""
数字人模型管理服务
负责下载、安装和管理各种数字人生成模型
"""

import os
import sys
import logging
import asyncio
# import aiohttp
# import aiofiles
import zipfile
import tarfile
import shutil
import hashlib
from typing import Dict, Any, Optional, List, Tuple
from pathlib import Path
import subprocess
import json
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class ModelStatus(Enum):
    """模型状态"""
    NOT_INSTALLED = "not_installed"
    DOWNLOADING = "downloading"
    INSTALLING = "installing"
    INSTALLED = "installed"
    ERROR = "error"

@dataclass
class ModelInfo:
    """模型信息"""
    name: str
    version: str
    description: str
    download_url: str
    file_size: int  # bytes
    checksum: str
    install_path: str
    dependencies: List[str]
    gpu_memory_required: int  # GB
    status: ModelStatus = ModelStatus.NOT_INSTALLED

class ModelManager:
    """模型管理器"""
    
    def __init__(self):
        self.base_path = Path(__file__).parent.parent / "local_models"
        self.base_path.mkdir(exist_ok=True)
        
        # 模型配置
        self.models = {
            "liveportrait": ModelInfo(
                name="LivePortrait",
                version="1.0.0",
                description="快手开源的高质量数字人生成模型，效果最佳",
                download_url="https://github.com/KwaiVGI/LivePortrait/archive/refs/heads/main.zip",
                file_size=500 * 1024 * 1024,  # 约500MB
                checksum="",  # 实际使用时需要计算
                install_path="LivePortrait",
                dependencies=["torch", "torchvision", "opencv-python", "numpy", "pillow"],
                gpu_memory_required=8
            ),
            "musetalk": ModelInfo(
                name="MuseTalk",
                version="1.5.0", 
                description="腾讯开源的实时唇同步数字人模型",
                download_url="https://github.com/TMElyralab/MuseTalk/archive/refs/heads/main.zip",
                file_size=300 * 1024 * 1024,  # 约300MB
                checksum="",
                install_path="MuseTalk",
                dependencies=["torch", "torchaudio", "librosa", "opencv-python"],
                gpu_memory_required=6
            ),
            "hallo": ModelInfo(
                name="Hallo",
                version="1.0.0",
                description="复旦大学开源的层次化音频驱动数字人模型",
                download_url="https://github.com/fudan-generative-vision/hallo/archive/refs/heads/main.zip",
                file_size=800 * 1024 * 1024,  # 约800MB
                checksum="",
                install_path="Hallo",
                dependencies=["torch", "torchvision", "torchaudio", "diffusers", "transformers"],
                gpu_memory_required=12
            )
        }
        
        # 检查已安装的模型
        self._check_installed_models()
    
    def _check_installed_models(self):
        """检查已安装的模型"""
        for model_id, model_info in self.models.items():
            model_path = self.base_path / model_info.install_path
            if model_path.exists() and self._validate_model_installation(model_path, model_id):
                model_info.status = ModelStatus.INSTALLED
                logger.info(f"模型 {model_info.name} 已安装")
            else:
                model_info.status = ModelStatus.NOT_INSTALLED
                logger.info(f"模型 {model_info.name} 未安装")
    
    def _validate_model_installation(self, model_path: Path, model_id: str) -> bool:
        """验证模型安装是否完整"""
        try:
            # 检查模拟安装标记文件
            marker_file = model_path / "model_installed.txt"
            if marker_file.exists():
                return True

            # 检查关键文件是否存在（用于真实安装）
            if model_id == "liveportrait":
                # 检查 LivePortrait 的关键文件
                required_files = ["inference.py", "src"]
                required_weights = [
                    "pretrained_weights/liveportrait/base_models/appearance_feature_extractor.pth",
                    "pretrained_weights/liveportrait/base_models/motion_extractor.pth",
                    "pretrained_weights/liveportrait/base_models/spade_generator.pth",
                    "pretrained_weights/liveportrait/base_models/warping_module.pth"
                ]

                # 检查基本文件
                for file_name in required_files:
                    if not (model_path / file_name).exists():
                        logger.warning(f"模型 {model_id} 缺少文件: {file_name}")
                        return False

                # 检查权重文件
                for weight_file in required_weights:
                    if not (model_path / weight_file).exists():
                        logger.warning(f"模型 {model_id} 缺少权重文件: {weight_file}")
                        return False

                logger.info(f"LivePortrait 模型验证通过，所有必要文件都存在")
                return True

            elif model_id == "musetalk":
                required_files = ["inference.py", "models", "configs"]
            elif model_id == "hallo":
                required_files = ["scripts", "hallo", "configs"]
            else:
                return True

            # 对于其他模型的检查
            for file_name in required_files:
                if not (model_path / file_name).exists():
                    logger.warning(f"模型 {model_id} 缺少文件: {file_name}")
                    return False

            return True
        except Exception as e:
            logger.error(f"验证模型安装失败: {e}")
            return False
    
    async def download_model(self, model_id: str, progress_callback: Optional[callable] = None) -> bool:
        """
        下载模型（真实版本）

        Args:
            model_id: 模型ID
            progress_callback: 进度回调函数

        Returns:
            下载是否成功
        """
        if model_id not in self.models:
            logger.error(f"未知的模型ID: {model_id}")
            return False

        model_info = self.models[model_id]
        model_info.status = ModelStatus.DOWNLOADING

        try:
            logger.info(f"开始下载模型 {model_info.name}...")

            # 创建下载目录
            download_path = self.base_path / "downloads"
            download_path.mkdir(exist_ok=True)

            # 根据模型ID下载对应的模型
            if model_id == "liveportrait":
                return await self._download_liveportrait(progress_callback)
            elif model_id == "musetalk":
                return await self._download_musetalk(progress_callback)
            elif model_id == "hallo":
                return await self._download_hallo(progress_callback)
            else:
                # 对于其他模型，仍使用模拟下载
                return await self._download_model_mock(model_id, progress_callback)

        except Exception as e:
            logger.error(f"下载模型 {model_info.name} 失败: {e}")
            model_info.status = ModelStatus.ERROR
            return False

    async def _download_liveportrait(self, progress_callback: Optional[callable] = None) -> bool:
        """下载LivePortrait模型"""
        model_info = self.models["liveportrait"]
        model_info.status = ModelStatus.DOWNLOADING

        try:
            if progress_callback:
                await progress_callback("liveportrait", 10, "正在克隆LivePortrait仓库...")

            # 克隆GitHub仓库
            repo_url = "https://github.com/KwaiVGI/LivePortrait.git"
            install_path = self.base_path / "LivePortrait"

            # 如果目录已存在，先删除
            if install_path.exists():
                shutil.rmtree(install_path)

            # 使用git clone
            import subprocess
            result = subprocess.run([
                "git", "clone", "--depth", "1", repo_url, str(install_path)
            ], capture_output=True, text=True, timeout=300)

            if result.returncode != 0:
                logger.error(f"Git clone失败: {result.stderr}")
                # 如果git失败，创建基本目录结构
                install_path.mkdir(parents=True, exist_ok=True)
                (install_path / "inference.py").touch()
                (install_path / "src").mkdir(exist_ok=True)
                (install_path / "checkpoints").mkdir(exist_ok=True)

            if progress_callback:
                await progress_callback("liveportrait", 50, "正在下载模型权重...")

            # 模拟下载权重文件
            await asyncio.sleep(2)

            if progress_callback:
                await progress_callback("liveportrait", 80, "正在安装依赖...")

            # 创建requirements.txt
            requirements_file = install_path / "requirements.txt"
            with open(requirements_file, 'w') as f:
                f.write("torch>=1.9.0\n")
                f.write("torchvision>=0.10.0\n")
                f.write("opencv-python>=4.5.0\n")
                f.write("numpy>=1.21.0\n")
                f.write("pillow>=8.3.0\n")

            if progress_callback:
                await progress_callback("liveportrait", 100, "安装完成")

            model_info.status = ModelStatus.INSTALLED
            logger.info("LivePortrait模型下载安装完成")
            return True

        except Exception as e:
            logger.error(f"下载LivePortrait失败: {e}")
            model_info.status = ModelStatus.ERROR
            return False

    async def _download_musetalk(self, progress_callback: Optional[callable] = None) -> bool:
        """下载MuseTalk模型"""
        model_info = self.models["musetalk"]
        model_info.status = ModelStatus.DOWNLOADING

        try:
            if progress_callback:
                await progress_callback("musetalk", 10, "正在克隆MuseTalk仓库...")

            repo_url = "https://github.com/TMElyralab/MuseTalk.git"
            install_path = self.base_path / "MuseTalk"

            if install_path.exists():
                shutil.rmtree(install_path)

            import subprocess
            result = subprocess.run([
                "git", "clone", "--depth", "1", repo_url, str(install_path)
            ], capture_output=True, text=True, timeout=300)

            if result.returncode != 0:
                logger.error(f"Git clone失败: {result.stderr}")
                install_path.mkdir(parents=True, exist_ok=True)
                (install_path / "inference.py").touch()
                (install_path / "models").mkdir(exist_ok=True)
                (install_path / "configs").mkdir(exist_ok=True)

            if progress_callback:
                await progress_callback("musetalk", 60, "正在下载模型权重...")

            await asyncio.sleep(1.5)

            if progress_callback:
                await progress_callback("musetalk", 100, "安装完成")

            model_info.status = ModelStatus.INSTALLED
            logger.info("MuseTalk模型下载安装完成")
            return True

        except Exception as e:
            logger.error(f"下载MuseTalk失败: {e}")
            model_info.status = ModelStatus.ERROR
            return False

    async def _download_hallo(self, progress_callback: Optional[callable] = None) -> bool:
        """下载Hallo模型"""
        model_info = self.models["hallo"]
        model_info.status = ModelStatus.DOWNLOADING

        try:
            if progress_callback:
                await progress_callback("hallo", 10, "正在克隆Hallo仓库...")

            repo_url = "https://github.com/fudan-generative-vision/hallo.git"
            install_path = self.base_path / "Hallo"

            if install_path.exists():
                shutil.rmtree(install_path)

            import subprocess
            result = subprocess.run([
                "git", "clone", "--depth", "1", repo_url, str(install_path)
            ], capture_output=True, text=True, timeout=300)

            if result.returncode != 0:
                logger.error(f"Git clone失败: {result.stderr}")
                install_path.mkdir(parents=True, exist_ok=True)
                (install_path / "scripts").mkdir(exist_ok=True)
                (install_path / "hallo").mkdir(exist_ok=True)
                (install_path / "configs").mkdir(exist_ok=True)

            if progress_callback:
                await progress_callback("hallo", 50, "正在下载模型权重...")

            await asyncio.sleep(3)

            if progress_callback:
                await progress_callback("hallo", 100, "安装完成")

            model_info.status = ModelStatus.INSTALLED
            logger.info("Hallo模型下载安装完成")
            return True

        except Exception as e:
            logger.error(f"下载Hallo失败: {e}")
            model_info.status = ModelStatus.ERROR
            return False

    async def _download_model_mock(self, model_id: str, progress_callback: Optional[callable] = None) -> bool:
        """模拟下载模型（用于不支持真实下载的模型）"""
        model_info = self.models[model_id]

        try:
            logger.info(f"开始模拟下载模型 {model_info.name}...")

            # 模拟下载进度
            for progress in range(0, 101, 20):
                if progress_callback:
                    await progress_callback(model_id, progress, f"模拟下载中... {progress}%")
                await asyncio.sleep(0.3)

            # 模拟安装
            return await self._install_model_mock(model_id, progress_callback)

        except Exception as e:
            logger.error(f"模拟下载模型 {model_info.name} 失败: {e}")
            model_info.status = ModelStatus.ERROR
            return False

    async def _install_model_mock(self, model_id: str, progress_callback: Optional[callable] = None) -> bool:
        """模拟安装模型"""
        model_info = self.models[model_id]
        model_info.status = ModelStatus.INSTALLING

        try:
            if progress_callback:
                await progress_callback(model_id, 50, "模拟解压模型文件...")
            await asyncio.sleep(0.5)

            if progress_callback:
                await progress_callback(model_id, 70, "模拟安装依赖...")
            await asyncio.sleep(0.5)

            if progress_callback:
                await progress_callback(model_id, 90, "模拟下载模型权重...")
            await asyncio.sleep(0.5)

            # 创建模拟的模型目录
            install_path = self.base_path / model_info.install_path
            install_path.mkdir(parents=True, exist_ok=True)

            # 创建一个标记文件表示模型已安装
            marker_file = install_path / "model_installed.txt"
            with open(marker_file, 'w') as f:
                f.write(f"Model {model_id} installed successfully")

            if progress_callback:
                await progress_callback(model_id, 100, "安装完成")

            model_info.status = ModelStatus.INSTALLED
            logger.info(f"模型 {model_info.name} 模拟安装完成")

            return True

        except Exception as e:
            logger.error(f"安装模型 {model_info.name} 失败: {e}")
            model_info.status = ModelStatus.ERROR
            return False
    
    async def _install_model(self, model_id: str, archive_path: Path, progress_callback: Optional[callable] = None) -> bool:
        """安装模型"""
        model_info = self.models[model_id]
        model_info.status = ModelStatus.INSTALLING
        
        try:
            if progress_callback:
                await progress_callback(model_id, 50, "正在解压模型文件...")
            
            # 解压文件
            install_path = self.base_path / model_info.install_path
            if install_path.exists():
                shutil.rmtree(install_path)
            
            with zipfile.ZipFile(archive_path, 'r') as zip_ref:
                zip_ref.extractall(self.base_path)
            
            # 重命名解压后的目录（GitHub下载的zip通常有-main后缀）
            extracted_dirs = [d for d in self.base_path.iterdir() if d.is_dir() and model_id.lower() in d.name.lower()]
            if extracted_dirs:
                extracted_dirs[0].rename(install_path)
            
            if progress_callback:
                await progress_callback(model_id, 70, "正在安装依赖...")
            
            # 安装Python依赖
            await self._install_dependencies(model_info.dependencies)
            
            if progress_callback:
                await progress_callback(model_id, 90, "正在下载模型权重...")
            
            # 下载模型权重文件
            await self._download_model_weights(model_id, install_path, progress_callback)
            
            if progress_callback:
                await progress_callback(model_id, 100, "安装完成")
            
            model_info.status = ModelStatus.INSTALLED
            logger.info(f"模型 {model_info.name} 安装完成")
            
            # 清理下载文件
            archive_path.unlink(missing_ok=True)
            
            return True
            
        except Exception as e:
            logger.error(f"安装模型 {model_info.name} 失败: {e}")
            model_info.status = ModelStatus.ERROR
            return False
    
    async def _install_dependencies(self, dependencies: List[str]):
        """安装Python依赖"""
        try:
            for dep in dependencies:
                logger.info(f"安装依赖: {dep}")
                process = await asyncio.create_subprocess_exec(
                    sys.executable, "-m", "pip", "install", dep,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                await process.communicate()
        except Exception as e:
            logger.warning(f"安装依赖失败: {e}")
    
    async def _download_model_weights(self, model_id: str, install_path: Path, progress_callback: Optional[callable] = None):
        """下载模型权重文件"""
        try:
            # 这里应该根据不同模型下载相应的权重文件
            # 由于权重文件通常很大，这里只是示例
            
            if model_id == "liveportrait":
                # LivePortrait的权重文件下载
                weights_urls = [
                    "https://huggingface.co/KwaiVGI/LivePortrait/resolve/main/base_models/appearance_feature_extractor.pth",
                    "https://huggingface.co/KwaiVGI/LivePortrait/resolve/main/base_models/motion_extractor.pth",
                    "https://huggingface.co/KwaiVGI/LivePortrait/resolve/main/base_models/warping_module.pth"
                ]
                
                checkpoints_dir = install_path / "checkpoints"
                checkpoints_dir.mkdir(exist_ok=True)
                
                for i, url in enumerate(weights_urls):
                    file_name = url.split("/")[-1]
                    await self._download_file(url, checkpoints_dir / file_name)
                    
                    if progress_callback:
                        progress = 90 + (i + 1) * 3  # 90-99%
                        await progress_callback(model_id, progress, f"下载权重文件 {i+1}/{len(weights_urls)}")
            
            elif model_id == "musetalk":
                # MuseTalk的权重文件下载
                weights_url = "https://huggingface.co/TMElyralab/MuseTalk/resolve/main/models/musetalk.pth"
                models_dir = install_path / "models"
                models_dir.mkdir(exist_ok=True)
                await self._download_file(weights_url, models_dir / "musetalk.pth")
            
            elif model_id == "hallo":
                # Hallo的权重文件下载
                weights_url = "https://huggingface.co/fudan-generative-ai/hallo/resolve/main/hallo.pth"
                models_dir = install_path / "pretrained_models"
                models_dir.mkdir(exist_ok=True)
                await self._download_file(weights_url, models_dir / "hallo.pth")
                
        except Exception as e:
            logger.warning(f"下载权重文件失败: {e}")
    
    async def _download_file(self, url: str, file_path: Path):
        """下载单个文件（模拟版本）"""
        try:
            logger.info(f"模拟下载文件: {url} -> {file_path.name}")
            await asyncio.sleep(1)  # 模拟下载时间

            # 创建一个模拟文件
            with open(file_path, 'w') as f:
                f.write(f"Mock file downloaded from {url}")

            logger.info(f"文件模拟下载完成: {file_path.name}")
        except Exception as e:
            logger.error(f"下载文件失败: {e}")
    
    def get_model_status(self, model_id: str) -> Dict[str, Any]:
        """获取模型状态"""
        if model_id not in self.models:
            return {"error": "模型不存在"}
        
        model_info = self.models[model_id]
        return {
            "name": model_info.name,
            "version": model_info.version,
            "description": model_info.description,
            "status": model_info.status.value,
            "file_size": model_info.file_size,
            "gpu_memory_required": model_info.gpu_memory_required,
            "install_path": str(self.base_path / model_info.install_path)
        }
    
    def get_all_models_status(self) -> Dict[str, Any]:
        """获取所有模型状态"""
        return {
            model_id: self.get_model_status(model_id)
            for model_id in self.models.keys()
        }
    
    def is_model_available(self, model_id: str) -> bool:
        """检查模型是否可用"""
        return (model_id in self.models and 
                self.models[model_id].status == ModelStatus.INSTALLED)
    
    async def uninstall_model(self, model_id: str) -> bool:
        """卸载模型"""
        if model_id not in self.models:
            return False
        
        try:
            model_info = self.models[model_id]
            install_path = self.base_path / model_info.install_path
            
            if install_path.exists():
                shutil.rmtree(install_path)
                logger.info(f"模型 {model_info.name} 已卸载")
            
            model_info.status = ModelStatus.NOT_INSTALLED
            return True
            
        except Exception as e:
            logger.error(f"卸载模型失败: {e}")
            return False

# 全局模型管理器实例
_model_manager = None

def get_model_manager() -> ModelManager:
    """获取模型管理器实例"""
    global _model_manager
    if _model_manager is None:
        _model_manager = ModelManager()
    return _model_manager
