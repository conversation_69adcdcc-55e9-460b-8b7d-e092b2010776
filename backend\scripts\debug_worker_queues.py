#!/usr/bin/env python3
"""
调试 Worker 队列问题
"""
import os
import sys
import time
import json
from pathlib import Path
from dotenv import load_dotenv

# 添加后端路径
backend_dir = Path(__file__).parent.parent
sys.path.append(str(backend_dir))

# 加载环境变量
env_path = backend_dir / ".env"
load_dotenv(env_path)

def check_redis_queues_detailed():
    """详细检查 Redis 队列"""
    print("🔍 详细检查 Redis 队列")
    print("=" * 50)
    
    try:
        import redis
        
        r = redis.Redis(host='localhost', port=6379, db=0)
        r.ping()
        print("✅ Redis 连接正常")
        
        # 检查所有队列
        all_queues = ['test', 'wanx_generation', 'celery', 'digital_human', 
                     'image_processing', 'voice_processing', 'video_generation', 'translation']
        
        print(f"\n📊 所有队列状态:")
        for queue in all_queues:
            length = r.llen(queue)
            print(f"   {queue}: {length} 个任务")
            
            if length > 0:
                # 查看队列中的任务
                tasks = r.lrange(queue, 0, 2)
                for i, task in enumerate(tasks):
                    try:
                        task_data = json.loads(task.decode())
                        task_name = task_data.get('task', 'N/A')
                        print(f"     任务 {i+1}: {task_name}")
                    except:
                        task_str = task.decode()[:100]
                        print(f"     任务 {i+1}: {task_str}...")
        
        # 检查 Celery 相关的键
        celery_keys = [k.decode() for k in r.keys('*') if b'celery' in k]
        print(f"\n📊 Celery 相关键数量: {len(celery_keys)}")
        
        # 检查最近的任务元数据
        task_meta_keys = [k for k in celery_keys if 'task-meta' in k]
        print(f"📊 任务元数据键数量: {len(task_meta_keys)}")
        
        if task_meta_keys:
            # 查看最近的几个任务元数据
            recent_keys = task_meta_keys[-3:]
            print(f"📊 最近的任务元数据:")
            for key in recent_keys:
                try:
                    meta = r.get(key)
                    if meta:
                        meta_data = json.loads(meta.decode())
                        status = meta_data.get('status', 'N/A')
                        task_name = meta_data.get('task', 'N/A')
                        result = meta_data.get('result', 'N/A')
                        print(f"   {key}: {task_name} - {status}")
                        if status == 'FAILURE':
                            print(f"     错误: {result}")
                except Exception as e:
                    print(f"   {key}: 无法解析 - {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Redis 详细检查失败: {e}")
        return False

def manually_push_task():
    """手动推送任务到队列"""
    print(f"\n🔧 手动推送任务到队列")
    print("=" * 50)
    
    try:
        import redis
        import uuid
        
        r = redis.Redis(host='localhost', port=6379, db=0)
        
        # 创建一个简单的测试任务
        task_id = str(uuid.uuid4())
        task_data = {
            "id": task_id,
            "task": "test_unified_task",
            "args": [],
            "kwargs": {
                "task_id": "manual-test",
                "test_message": "手动推送测试",
                "duration": 2
            },
            "retries": 0,
            "eta": None,
            "expires": None,
            "utc": True,
            "callbacks": None,
            "errbacks": None,
            "timelimit": [None, None],
            "taskset": None,
            "chord": None,
            "properties": {},
            "headers": {}
        }
        
        # 推送到 test 队列
        task_json = json.dumps(task_data)
        r.lpush("test", task_json)
        
        print(f"✅ 手动推送任务到 test 队列")
        print(f"📊 任务ID: {task_id}")
        print(f"📊 队列长度: {r.llen('test')}")
        
        # 等待一下看看 Worker 是否处理
        print(f"⏳ 等待 10 秒看 Worker 是否处理...")
        for i in range(10):
            time.sleep(1)
            length = r.llen('test')
            print(f"   [{i+1:2d}s] test 队列长度: {length}")
            
            if length == 0:
                print(f"✅ 任务被 Worker 处理了！")
                return True
        
        print(f"❌ 任务没有被处理")
        return False
        
    except Exception as e:
        print(f"❌ 手动推送任务失败: {e}")
        return False

def check_celery_worker_config():
    """检查 Celery Worker 配置"""
    print(f"\n🔍 检查 Celery Worker 配置")
    print("=" * 50)
    
    try:
        from app.core.celery_app import celery_app
        
        print(f"📊 Celery 应用配置:")
        print(f"   应用名: {celery_app.main}")
        print(f"   Broker: {celery_app.conf.broker_url}")
        print(f"   Backend: {celery_app.conf.result_backend}")
        print(f"   序列化: {celery_app.conf.task_serializer}")
        print(f"   接受内容: {celery_app.conf.accept_content}")
        print(f"   协议版本: {celery_app.conf.task_protocol}")
        
        print(f"\n📊 任务路由:")
        if hasattr(celery_app.conf, 'task_routes'):
            for pattern, config in celery_app.conf.task_routes.items():
                print(f"   {pattern} -> {config}")
        
        print(f"\n📊 已注册的任务:")
        test_tasks = []
        wanx_tasks = []
        for task_name in sorted(celery_app.tasks.keys()):
            if not task_name.startswith('celery.'):
                if 'test' in task_name:
                    test_tasks.append(task_name)
                elif 'wanx' in task_name:
                    wanx_tasks.append(task_name)
                else:
                    print(f"   📝 {task_name}")
        
        print(f"\n📊 测试任务:")
        for task in test_tasks:
            print(f"   🧪 {task}")
        
        print(f"\n📊 Wanx 任务:")
        for task in wanx_tasks:
            print(f"   🎬 {task}")
        
        # 检查 Worker 状态
        inspect = celery_app.control.inspect()
        
        print(f"\n📊 Worker 状态:")
        active = inspect.active()
        if active:
            for worker, tasks in active.items():
                print(f"   🔧 {worker}: {len(tasks)} 个活跃任务")
        
        registered = inspect.registered()
        if registered:
            for worker, tasks in registered.items():
                print(f"   🔧 {worker}: {len(tasks)} 个注册任务")
                test_registered = [t for t in tasks if 'test' in t]
                if test_registered:
                    print(f"     测试任务: {test_registered}")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查 Celery Worker 配置失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_direct_task_import():
    """测试直接导入和调用任务"""
    print(f"\n🧪 测试直接导入和调用任务")
    print("=" * 50)
    
    try:
        # 测试导入
        from app.tasks.test_unified_task import test_unified_task
        print(f"✅ 成功导入 test_unified_task")
        
        # 检查任务属性
        print(f"📊 任务名称: {test_unified_task.name}")
        print(f"📊 任务队列: {getattr(test_unified_task, 'queue', 'default')}")
        
        # 尝试直接调用（不通过 Celery）
        print(f"🔧 尝试直接调用任务...")
        result = test_unified_task(
            task_id="direct-test",
            test_message="直接调用测试",
            duration=1
        )
        print(f"✅ 直接调用成功: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ 直接任务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 Worker 队列调试")
    print("=" * 60)
    
    # 1. 检查 Celery Worker 配置
    config_ok = check_celery_worker_config()
    
    # 2. 详细检查 Redis 队列
    redis_ok = check_redis_queues_detailed()
    
    # 3. 测试直接任务导入和调用
    direct_ok = test_direct_task_import()
    
    # 4. 手动推送任务测试
    manual_ok = manually_push_task()
    
    # 总结
    print(f"\n📊 调试总结:")
    print(f"{'✅' if config_ok else '❌'} Celery 配置: {'正常' if config_ok else '异常'}")
    print(f"{'✅' if redis_ok else '❌'} Redis 队列: {'正常' if redis_ok else '异常'}")
    print(f"{'✅' if direct_ok else '❌'} 直接调用: {'成功' if direct_ok else '失败'}")
    print(f"{'✅' if manual_ok else '❌'} 手动推送: {'成功' if manual_ok else '失败'}")
    
    if manual_ok:
        print(f"\n🎉 Worker 能够处理手动推送的任务！")
        print(f"💡 问题可能在于任务提交方式或消息格式")
    elif direct_ok:
        print(f"\n✅ 任务代码本身正常")
        print(f"❌ 但 Worker 没有处理队列中的任务")
        print(f"💡 可能的问题:")
        print(f"   1. Worker 没有正确监听队列")
        print(f"   2. 消息格式仍有问题")
        print(f"   3. Worker 进程有问题")
    else:
        print(f"\n❌ 任务代码或配置有问题")

if __name__ == "__main__":
    main()
