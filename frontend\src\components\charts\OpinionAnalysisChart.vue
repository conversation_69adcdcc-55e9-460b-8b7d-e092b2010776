<template>
  <div class="opinion-analysis-chart">
    <div 
      ref="chartContainer" 
      class="chart-container"
      :style="{ height: height + 'px' }"
    />
  </div>
</template>

<script>
import { defineComponent, ref, onMounted, onUnmounted, watch, nextTick } from 'vue';
import * as echarts from 'echarts';

export default defineComponent({
  name: 'OpinionAnalysisChart',
  props: {
    type: {
      type: String,
      required: true,
      validator: (value) => ['trend', 'pie', 'bar', 'map', 'sentiment', 'wordcloud'].includes(value)
    },
    data: {
      type: [Object, Array],
      default: () => ({})
    },
    height: {
      type: Number,
      default: 400
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  emits: ['chart-click'],
  setup(props, { emit }) {
    const chartContainer = ref();
    const chart = ref(null);

    // 监听数据变化
    watch(() => props.data, (newData) => {
      if (chart.value && newData) {
        updateChart(newData);
      }
    }, { deep: true });

    // 监听加载状态
    watch(() => props.loading, (loading) => {
      if (chart.value) {
        if (loading) {
          chart.value.showLoading('default', {
            text: '加载中...',
            color: '#409eff',
            textColor: '#000',
            maskColor: 'rgba(255, 255, 255, 0.8)',
            zlevel: 0
          });
        } else {
          chart.value.hideLoading();
        }
      }
    });

    const initChart = () => {
      if (!chartContainer.value) return;

      chart.value = echarts.init(chartContainer.value);
      
      // 绑定点击事件
      chart.value.on('click', (params) => {
        emit('chart-click', params);
      });

      updateChart(props.data);
    };

    const updateChart = (data) => {
      if (!chart.value || !data) return;

      let option = {};

      switch (props.type) {
        case 'trend':
          option = getTrendOption(data);
          break;
        case 'pie':
          option = getPieOption(data);
          break;
        case 'bar':
          option = getBarOption(data);
          break;
        case 'map':
          option = getMapOption(data);
          break;
        case 'sentiment':
          option = getSentimentOption(data);
          break;
        case 'wordcloud':
          option = getWordCloudOption(data);
          break;
        default:
          option = getDefaultOption();
      }

      chart.value.setOption(option, true);
    };

    const getTrendOption = (data) => {
      return {
        title: {
          text: '舆情趋势分析',
          left: 'center',
          textStyle: {
            fontSize: 16,
            fontWeight: 'bold'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          }
        },
        legend: {
          data: ['舆情数量', '正面情感', '负面情感'],
          top: 30
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          top: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: data.dates || []
        },
        yAxis: [
          {
            type: 'value',
            name: '数量',
            position: 'left'
          },
          {
            type: 'value',
            name: '情感分数',
            position: 'right',
            min: -1,
            max: 1
          }
        ],
        series: [
          {
            name: '舆情数量',
            type: 'line',
            data: data.counts || [],
            smooth: true,
            itemStyle: {
              color: '#409eff'
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: 'rgba(64, 158, 255, 0.3)' },
                { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
              ])
            }
          },
          {
            name: '正面情感',
            type: 'line',
            yAxisIndex: 1,
            data: data.positive || [],
            smooth: true,
            itemStyle: {
              color: '#67c23a'
            }
          },
          {
            name: '负面情感',
            type: 'line',
            yAxisIndex: 1,
            data: data.negative || [],
            smooth: true,
            itemStyle: {
              color: '#f56c6c'
            }
          }
        ]
      };
    };

    const getPieOption = (data) => {
      return {
        title: {
          text: data.title || '分布统计',
          left: 'center',
          textStyle: {
            fontSize: 16,
            fontWeight: 'bold'
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          top: 'middle'
        },
        series: [
          {
            name: data.name || '统计',
            type: 'pie',
            radius: ['40%', '70%'],
            center: ['60%', '50%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '18',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: data.data || []
          }
        ]
      };
    };

    const getBarOption = (data) => {
      return {
        title: {
          text: data.title || '柱状图分析',
          left: 'center',
          textStyle: {
            fontSize: 16,
            fontWeight: 'bold'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          top: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: data.categories || [],
          axisTick: {
            alignWithLabel: true
          }
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: data.name || '数量',
            type: 'bar',
            barWidth: '60%',
            data: data.values || [],
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#409eff' },
                { offset: 1, color: '#67c23a' }
              ])
            }
          }
        ]
      };
    };

    const getMapOption = (data) => {
      return {
        title: {
          text: '地域分布',
          left: 'center',
          textStyle: {
            fontSize: 16,
            fontWeight: 'bold'
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: '{b}: {c}'
        },
        visualMap: {
          min: 0,
          max: data.max || 100,
          left: 'left',
          top: 'bottom',
          text: ['高', '低'],
          calculable: true,
          inRange: {
            color: ['#e0f3ff', '#409eff']
          }
        },
        series: [
          {
            name: '舆情数量',
            type: 'map',
            map: 'china',
            roam: false,
            label: {
              show: true
            },
            data: data.data || []
          }
        ]
      };
    };

    const getSentimentOption = (data) => {
      return {
        title: {
          text: '情感分析',
          left: 'center',
          textStyle: {
            fontSize: 16,
            fontWeight: 'bold'
          }
        },
        tooltip: {
          trigger: 'item'
        },
        series: [
          {
            name: '情感分布',
            type: 'pie',
            radius: '70%',
            center: ['50%', '60%'],
            data: [
              { value: data.positive || 0, name: '正面', itemStyle: { color: '#67c23a' } },
              { value: data.neutral || 0, name: '中性', itemStyle: { color: '#909399' } },
              { value: data.negative || 0, name: '负面', itemStyle: { color: '#f56c6c' } }
            ],
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      };
    };

    const getWordCloudOption = (data) => {
      return {
        title: {
          text: '热词云图',
          left: 'center',
          textStyle: {
            fontSize: 16,
            fontWeight: 'bold'
          }
        },
        series: [
          {
            type: 'wordCloud',
            gridSize: 2,
            sizeRange: [12, 50],
            rotationRange: [-90, 90],
            shape: 'pentagon',
            width: '100%',
            height: '80%',
            drawOutOfBound: true,
            textStyle: {
              fontFamily: 'sans-serif',
              fontWeight: 'bold',
              color: function () {
                return 'rgb(' + [
                  Math.round(Math.random() * 160),
                  Math.round(Math.random() * 160),
                  Math.round(Math.random() * 160)
                ].join(',') + ')';
              }
            },
            emphasis: {
              textStyle: {
                shadowBlur: 10,
                shadowColor: '#333'
              }
            },
            data: data.words || []
          }
        ]
      };
    };

    const getDefaultOption = () => {
      return {
        title: {
          text: '暂无数据',
          left: 'center',
          top: 'middle',
          textStyle: {
            fontSize: 18,
            color: '#999'
          }
        }
      };
    };

    const handleResize = () => {
      if (chart.value) {
        chart.value.resize();
      }
    };

    onMounted(() => {
      nextTick(() => {
        initChart();
      });
      
      window.addEventListener('resize', handleResize);
    });

    onUnmounted(() => {
      if (chart.value) {
        chart.value.dispose();
      }
      window.removeEventListener('resize', handleResize);
    });

    return {
      chartContainer
    };
  }
});
</script>

<style scoped>
.opinion-analysis-chart {
  width: 100%;
  height: 100%;
}

.chart-container {
  width: 100%;
}
</style>
