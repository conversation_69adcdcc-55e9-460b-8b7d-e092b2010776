#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试avatar_generation.py服务
"""

import os
import sys
import argparse
import logging
import dotenv
import time
import tempfile

# 加载环境变量
try:
    # 查找并加载.env文件
    script_dir = os.path.dirname(os.path.abspath(__file__))
    backend_dir = os.path.dirname(script_dir)
    repo_root = os.path.dirname(backend_dir)
    
    env_files = [
        os.path.join(repo_root, '.env'),  # 项目根目录
        os.path.join(backend_dir, '.env'),  # 后端目录
        '.env'  # 当前目录
    ]
    
    for env_file in env_files:
        if os.path.exists(env_file):
            print(f"加载环境变量文件: {env_file}")
            dotenv.load_dotenv(env_file)
            break
except Exception as e:
    print(f"加载环境变量失败: {e}")

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

# 添加backend目录到系统路径
script_dir = os.path.dirname(os.path.abspath(__file__))
backend_dir = os.path.dirname(script_dir)
if backend_dir not in sys.path:
    sys.path.append(backend_dir)

# 导入avatar_generation服务
try:
    from services.avatar_generation import get_avatar_generator
    logger.info("成功导入Avatar生成服务")
except Exception as e:
    logger.error(f"导入Avatar生成服务失败: {e}")
    sys.exit(1)

def test_init():
    """测试初始化Avatar生成器"""
    avatar_generator = get_avatar_generator()
    
    # 尝试初始化
    logger.info("尝试初始化Avatar生成器...")
    result = avatar_generator.initialize_avatar_model()
    logger.info(f"初始化结果: {result}")
    
    # 获取可用模型
    models = avatar_generator.get_available_models()
    logger.info(f"可用模型: {len(models)}个")
    for model in models:
        logger.info(f"  - {model['name']} ({model['id']}): {model['description']}")
    
    return result

def test_generate_preview():
    """测试生成预览图像"""
    avatar_generator = get_avatar_generator()
    
    # 生成预览图像
    logger.info("生成预览图像...")
    output_path = os.path.join(tempfile.gettempdir(), f"preview_{time.time()}.jpg")
    preview_path = avatar_generator.generate_preview_image(output_path)
    
    if preview_path and os.path.exists(preview_path):
        logger.info(f"预览图像生成成功: {preview_path}")
        file_size = os.path.getsize(preview_path) / 1024  # KB
        logger.info(f"文件大小: {file_size:.2f} KB")
    else:
        logger.error("预览图像生成失败")
        return False
    
    return True

def test_render_avatar_video(args):
    """测试渲染数字人视频"""
    avatar_generator = get_avatar_generator()
    
    # 设置输出路径
    if not args.output:
        args.output = os.path.join(tempfile.gettempdir(), f"avatar_{time.time()}.mp4")
    
    # 准备表情数据（简单示例）
    expressions = []
    if not args.no_expressions:
        logger.info("创建测试表情序列...")
        frame_count = int(args.duration * 30)  # 假设30fps
        
        import math
        import random
        
        for i in range(frame_count):
            # 生成随机表情
            t = i / frame_count
            expression = {
                "mouth_open": 0.3 + 0.2 * math.sin(t * 10),  # 变化的嘴部开合
                "smile": 0.2 + 0.1 * math.cos(t * 5),  # 变化的微笑程度
                "eyebrow_raise": 0.1 + 0.1 * math.sin(t * 3),  # 变化的眉毛抬起
                "eye_open_left": 1.0 if (i % 50) > 3 else 0.1,  # 眨眼
                "eye_open_right": 1.0 if (i % 50) > 3 else 0.1,  # 眨眼
                "head_rotation_x": 0.05 * math.sin(t * 2),  # 头部x轴旋转
                "head_rotation_y": 0.1 * math.sin(t * 1.5),  # 头部y轴旋转
                "head_rotation_z": 0.02 * math.cos(t * 1),  # 头部z轴旋转
            }
            expressions.append(expression)
        
        logger.info(f"创建了{len(expressions)}帧表情数据")
    
    # 查找音频文件
    if not args.audio:
        # 尝试查找示例音频
        sadtalker_path = os.getenv("SADTALKER_PATH", os.path.join(script_dir, "third_party", "SadTalker"))
        example_audio_dir = os.path.join(sadtalker_path, "examples", "driven_audio")
        
        if os.path.exists(example_audio_dir):
            audio_files = [
                os.path.join(example_audio_dir, "bus_chinese.wav"),
                os.path.join(example_audio_dir, "chinese.wav"),
                os.path.join(example_audio_dir, "RD_Radio31_000.wav"),
                os.path.join(example_audio_dir, "aintgonna.wav")
            ]
            
            for audio_file in audio_files:
                if os.path.exists(audio_file):
                    args.audio = audio_file
                    logger.info(f"使用SadTalker示例音频: {audio_file}")
                    break
        
        if not args.audio:
            # 找不到SadTalker示例音频，创建一个简单的音频
            args.audio = os.path.join(tempfile.gettempdir(), f"test_audio_{time.time()}.mp3")
            logger.info(f"创建测试音频: {args.audio}")
            
            try:
                from gtts import gTTS
                
                tts = gTTS("这是一个测试音频文件，用于验证数字人视频生成功能。", lang='zh-cn')
                tts.save(args.audio)
                logger.info(f"成功创建测试音频: {args.audio}")
            except Exception as e:
                logger.error(f"创建测试音频失败: {e}")
                try:
                    import pyttsx3
                    engine = pyttsx3.init()
                    engine.save_to_file("This is a test audio for avatar video generation.", args.audio)
                    engine.runAndWait()
                    logger.info(f"使用pyttsx3创建测试音频: {args.audio}")
                except Exception as e2:
                    logger.error(f"使用pyttsx3创建测试音频也失败: {e2}")
                    return False
    
    # 查找图像文件
    if not args.image:
        # 尝试查找示例图像
        sadtalker_path = os.getenv("SADTALKER_PATH", os.path.join(script_dir, "third_party", "SadTalker"))
        example_image_dir = os.path.join(sadtalker_path, "examples", "source_image")
        
        if os.path.exists(example_image_dir):
            image_files = [
                os.path.join(example_image_dir, "full3.png"),
                os.path.join(example_image_dir, "full2.png"),
                os.path.join(example_image_dir, "full1.png"),
                os.path.join(example_image_dir, "crop1.png")
            ]
            
            for image_file in image_files:
                if os.path.exists(image_file):
                    args.image = image_file
                    logger.info(f"使用SadTalker示例图像: {image_file}")
                    break
    
    # 确保音频文件存在
    if not args.audio or not os.path.exists(args.audio):
        logger.error(f"音频文件不存在: {args.audio}")
        return False
    
    # 渲染视频
    logger.info(f"渲染数字人视频: 音频={args.audio}, 图像={args.image if args.image else '默认'}, 风格={args.style}")
    start_time = time.time()
    
    if args.use_ai:
        # 使用AI方法(SadTalker)渲染
        logger.info("使用AI方法渲染视频...")
        video_path = avatar_generator.render_avatar_video_with_ai(
            audio_path=args.audio,
            image_path=args.image,
            expressions=expressions if not args.no_expressions else None,
            avatar_style=args.style,
            output_path=args.output
        )
    else:
        # 使用传统方法渲染
        logger.info("使用传统方法渲染视频...")
        video_path = avatar_generator.render_avatar_video(
            expressions=expressions if not args.no_expressions else None,
            duration=args.duration,
            output_path=args.output
        )
    
    end_time = time.time()
    
    if video_path and os.path.exists(video_path):
        logger.info(f"视频渲染成功: {video_path}")
        file_size = os.path.getsize(video_path) / (1024 * 1024)  # MB
        logger.info(f"文件大小: {file_size:.2f} MB")
        logger.info(f"渲染时间: {end_time - start_time:.2f} 秒")
    else:
        logger.error("视频渲染失败")
        return False
    
    return True

def main():
    parser = argparse.ArgumentParser(description="测试头像生成服务")
    parser.add_argument("--audio", help="音频文件路径")
    parser.add_argument("--image", help="图像文件路径")
    parser.add_argument("--output", help="输出视频路径")
    parser.add_argument("--style", default="default", choices=["default", "realistic", "cartoon", "stylized"], 
                      help="头像风格")
    parser.add_argument("--duration", type=float, default=5.0, help="视频时长(秒)")
    parser.add_argument("--init-only", action="store_true", help="仅测试初始化")
    parser.add_argument("--preview-only", action="store_true", help="仅生成预览图像")
    parser.add_argument("--no-expressions", action="store_true", help="不使用表情数据")
    parser.add_argument("--use-ai", action="store_true", help="使用AI(SadTalker)方法生成", default=True)
    parser.add_argument("--no-ai", dest="use_ai", action="store_false", help="不使用AI方法生成")
    args = parser.parse_args()
    
    # 测试初始化
    init_test = test_init()
    if not init_test:
        logger.error("头像生成器初始化失败")
        sys.exit(1)
    
    if args.init_only:
        logger.info("初始化测试完成")
        sys.exit(0)
    
    # 测试预览图像生成
    preview_test = test_generate_preview()
    if not preview_test:
        logger.error("预览图像生成测试失败")
        sys.exit(1)
    
    if args.preview_only:
        logger.info("预览图像测试完成")
        sys.exit(0)
    
    # 测试渲染视频
    video_test = test_render_avatar_video(args)
    if not video_test:
        logger.error("视频渲染测试失败")
        sys.exit(1)
    
    logger.info("所有测试通过！")

if __name__ == "__main__":
    main() 