import logging
import random
import uuid
import json
import time
import sys
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session
from datetime import datetime, timedelta

from models.user import User
from models.terminology import Term, Terminology, TermStatus
# 安全导入，防止NLTK错误
try:
    from utils.terminology_nlp_utils import (
    extract_terms, 
    translate_term, 
    clean_text, 
    detect_language
)
except ImportError as e:
    print(f"导入术语NLP工具失败: {e}")
    # 定义备用函数
    def extract_terms(text, language=None, domain=None):
        return []
    
    def translate_term(term, source_lang, target_lang):
        return {"translated_term": term, "confidence": 0.1}
    
    def clean_text(text):
        return text.strip() if text else ""
    
    def detect_language(text):
        return "en"

# 设置日志
logger = logging.getLogger("terminology_smart_service")

# 简单的内存存储，用于保存任务数据（在实际生产环境中应使用数据库）
_collection_tasks = {}  # 格式: {task_id: task_data}

# 添加一个示例任务，方便测试
demo_task_id = str(uuid.uuid4())
_collection_tasks[demo_task_id] = {
    "id": demo_task_id,
    "name": "示例术语采集任务",
    "language": "zh-cn",
    "collection_mode": "smart",
    "keywords": "人工智能,机器学习,深度学习",
    "target_website": "example.com",
    "url_list": ["https://example.com/ai", "https://example.com/ml"],
    "status": "completed",
    "progress": 100,
    "created_at": (datetime.now() - timedelta(days=1)).isoformat(),
    "user_id": 1,  # 默认用户ID，实际使用中需要根据系统用户调整
    "results": [
        {
            "term": "人工智能",
            "definition": "人工智能是计算机科学的一个分支，它企图了解智能的实质，并生产出一种新的能以人类智能相似的方式做出反应的智能机器。",
            "confidence": 0.95
        },
        {
            "term": "机器学习",
            "definition": "机器学习是人工智能的一个分支，它使用各种算法来解析数据，从中学习，然后对新数据进行决策和预测。",
            "confidence": 0.92
        },
        {
            "term": "深度学习",
            "definition": "深度学习是机器学习的一个子领域，它使用多层神经网络模拟人脑进行学习，以处理复杂的模式识别任务。",
            "confidence": 0.88
        }
    ]
}

class TerminologySmartService:
    """术语智能处理服务类"""
    
    @staticmethod
    async def extract_terms_from_text(
        text: str, 
        source_language: Optional[str] = None, 
        domain: Optional[str] = None, 
        user: Optional[User] = None, 
        db: Optional[Session] = None
    ) -> Dict[str, Any]:
        """
        从文本中提取术语
        
        Args:
            text: 源文本
            source_language: 源语言代码，如果为None则自动检测
            domain: 领域/域名
            user: 当前用户对象（可选）
            db: 数据库会话（可选，用于查询已有术语）
        
        Returns:
            包含提取结果的字典
        """
        try:
            # 清理文本
            text = clean_text(text)
            
            if not text:
                return {
                    "status": "error",
                    "message": "输入文本为空",
                    "data": []
                }
            
            # 检测语言（如果未指定）
            if not source_language:
                source_language = detect_language(text)
            
            # 提取术语
            extracted_terms = extract_terms(text, source_language, domain)
            
            # 如果提供了数据库会话，检查术语是否已存在
            if db and user:
                # 获取提取出的术语文本列表
                term_texts = [item["term"].lower() for item in extracted_terms]
                
                # 查询数据库中已存在的术语
                existing_terms = db.query(Term).filter(
                    Term.term_text.in_(term_texts),
                    Term.language == source_language
                ).all()
                
                # 创建已存在术语的映射
                existing_term_map = {term.term_text.lower(): term for term in existing_terms}
                
                # 更新提取结果，标记已存在的术语
                for item in extracted_terms:
                    term_text = item["term"].lower()
                    if term_text in existing_term_map:
                        item["exists"] = True
                        item["term_id"] = existing_term_map[term_text].id
                    else:
                        item["exists"] = False
                        item["term_id"] = None
            
            # 处理结果
            result = {
                "status": "success",
                "language": source_language,
                "domain": domain,
                "total": len(extracted_terms),
                "data": extracted_terms
            }
            
            return result
        
        except Exception as e:
            logger.error(f"术语提取失败: {str(e)}")
            return {
                "status": "error",
                "message": f"术语提取过程中出错: {str(e)}",
                "data": []
            }
    
    @staticmethod
    async def translate_terms(
        terms: List[str], 
        source_language: str, 
        target_language: str,
        domain: Optional[str] = None, 
        user: Optional[User] = None, 
        db: Optional[Session] = None
    ) -> Dict[str, Any]:
        """
        翻译术语
        
        Args:
            terms: 术语列表
            source_language: 源语言代码
            target_language: 目标语言代码
            domain: 领域/域名（可选）
            user: 当前用户对象（可选）
            db: 数据库会话（可选，用于查询/保存术语翻译）
            
        Returns:
            包含翻译结果的字典
        """
        try:
            if not terms:
                return {
                    "status": "error",
                    "message": "未提供术语",
                    "data": []
                }
            
            # 翻译结果
            translation_results = []
            
            # 遍历术语进行翻译
            for term in terms:
                # 清理术语
                term = term.strip()
                if not term:
                    continue
                
                # 如果提供了数据库会话，先查询数据库中是否已有翻译
                existing_translation = None
                if db:
                    # 查询术语库是否有现成的翻译
                    try:
                        terminology = db.query(Terminology).filter(
                            Terminology.term == term,
                            Terminology.language == source_language
                        ).first()
                        
                        if terminology and terminology.translations:
                            # 解析翻译
                            translations = json.loads(terminology.translations)
                            if target_language in translations:
                                existing_translation = {
                                    "translated_term": translations[target_language],
                                    "confidence": 0.99,  # 数据库中的翻译可信度很高
                                    "source": "database"
                                }
                    except Exception as db_error:
                        logger.warning(f"查询术语翻译失败: {str(db_error)}")
                
                if existing_translation:
                    # 使用数据库中已有的翻译
                    translation_result = {
                        "source": term,
                        "target": existing_translation["translated_term"],
                        "confidence": existing_translation["confidence"],
                        "from_database": True
                    }
                else:
                    # 使用翻译服务翻译
                    translation = translate_term(term, source_language, target_language)
                    
                    translation_result = {
                        "source": term,
                        "target": translation["translated_term"],
                        "confidence": translation["confidence"],
                        "from_database": False
                    }
                
                translation_results.append(translation_result)
            
            # 处理结果
            result = {
                "status": "success",
                "source_language": source_language,
                "target_language": target_language,
                "domain": domain,
                "total": len(translation_results),
                "data": translation_results
            }
            
            return result
        
        except Exception as e:
            logger.error(f"术语翻译失败: {str(e)}")
            return {
                "status": "error",
                "message": f"术语翻译过程中出错: {str(e)}",
                "data": []
            }
    
    @staticmethod
    async def save_term(
        term_data: Dict[str, Any],
        user: User,
        db: Session
    ) -> Dict[str, Any]:
        """
        保存术语到数据库
        
        Args:
            term_data: 术语数据
            user: 当前用户
            db: 数据库会话
            
        Returns:
            包含保存结果的字典
        """
        try:
            # 解析术语数据
            term_text = term_data.get("term")
            language = term_data.get("language")
            domain = term_data.get("domain")
            definition = term_data.get("definition", "")
            translations = term_data.get("translations", {})
            
            if not term_text or not language:
                return {
                    "status": "error",
                    "message": "术语和语言是必填项",
                }
            
            # 检查术语是否已存在
            existing_term = db.query(Term).filter(
                Term.term_text == term_text,
                Term.language == language
            ).first()
            
            if existing_term:
                # 更新已有术语
                existing_term.domain = domain or existing_term.domain
                existing_term.definition = definition or existing_term.definition
                
                # 更新时间戳
                existing_term.updated_at = datetime.now()
                
                db.commit()
                
                return {
                    "status": "success",
                    "message": "术语更新成功",
                    "data": {
                        "id": existing_term.id,
                        "term": existing_term.term_text,
                        "updated": True
                    }
                }
            else:
                # 创建新术语
                new_term = Term(
                    term_text=term_text,
                    language=language,
                    domain=domain,
                    definition=definition,
                    status=TermStatus.DRAFT,
                    user_id=user.id
                )
                
                db.add(new_term)
                db.commit()
                db.refresh(new_term)
                
                # 如果提供了翻译，添加到术语库
                if translations:
                    new_terminology = Terminology(
                        term_id=str(uuid.uuid4()),
                        user_id=user.id,
                        term=term_text,
                        definition=definition,
                        language=language,
                        domain=domain,
                        translations=json.dumps(translations)
                    )
                    
                    db.add(new_terminology)
                    db.commit()
                
                return {
                    "status": "success",
                    "message": "术语添加成功",
                    "data": {
                        "id": new_term.id,
                        "term": new_term.term_text,
                        "updated": False
                    }
                }
        
        except Exception as e:
            db.rollback()
            logger.error(f"保存术语失败: {str(e)}")
            return {
                "status": "error",
                "message": f"保存术语过程中出错: {str(e)}"
            }
    
    @staticmethod
    async def collect_terms(
        collection_params: Dict[str, Any],
        user: User,
        db: Session
    ) -> Dict[str, Any]:
        """
        智能采集术语
        
        Args:
            collection_params: 采集参数
            user: 当前用户
            db: 数据库会话
            
        Returns:
            包含任务ID和状态的字典
        """
        try:
            # 提取参数
            name = collection_params.get('name', '未命名采集')
            language = collection_params.get('language', 'zh')
            collection_mode = collection_params.get('collectionMode', 'smart')
            keywords = collection_params.get('keywords', '')
            target_website = collection_params.get('targetWebsite', '')
            url_list = collection_params.get('urlList', '')
            
            # 记录日志
            logger.info(f"用户 {user.username} 启动术语采集任务: {name}, 模式: {collection_mode}")
            
            # 创建任务ID
            task_id = str(uuid.uuid4())
            logger.info(f"创建任务ID: {task_id}")
            
            # 创建任务记录
            task_record = {
                "id": task_id,
                "name": name,
                "language": language,
                "collection_mode": collection_mode,  # 后端内部使用collection_mode
                "mode": collection_mode,             # 为前端兼容性添加mode字段
                "keywords": keywords,
                "target_website": target_website,
                "url_list": url_list.split("\n") if url_list else [],
                "status": "pending",
                "progress": 0,
                "created_at": datetime.now().isoformat(),
                "user_id": user.id,
                "results": []
            }

            # 保存到内存存储
            _collection_tasks[task_id] = task_record
            logger.info(f"已创建采集任务记录，保存到内存字典，当前任务总数: {len(_collection_tasks)}")

            # 模拟异步任务处理（在实际项目中应使用Celery等任务队列）
            # 启动一个后台线程来更新任务状态
            def update_task_status():
                """模拟任务进度更新"""
                logger.info(f"开始更新任务 {task_id} 状态")
                
                time.sleep(5)  # 等待5秒
                if task_id in _collection_tasks:
                    _collection_tasks[task_id]["status"] = "processing"
                    _collection_tasks[task_id]["progress"] = 25
                    logger.info(f"任务 {task_id} 状态更新: processing, 进度: 25%")
                
                time.sleep(5)  # 再等待5秒
                if task_id in _collection_tasks:
                    _collection_tasks[task_id]["progress"] = 50
                    logger.info(f"任务 {task_id} 进度更新: 50%")
                
                time.sleep(5)  # 再等待5秒
                if task_id in _collection_tasks:
                    _collection_tasks[task_id]["progress"] = 75
                    logger.info(f"任务 {task_id} 进度更新: 75%")
                
                time.sleep(5)
                if task_id in _collection_tasks:
                    _collection_tasks[task_id]["status"] = "completed"
                    _collection_tasks[task_id]["progress"] = 100
                    _collection_tasks[task_id]["completed_at"] = datetime.now().isoformat()
                    logger.info(f"任务 {task_id} 状态更新: completed, 进度: 100%")
                    
                    # 添加一些模拟结果
                    _collection_tasks[task_id]["results"] = [
                        {
                            "term": f"术语{i+1}",
                            "definition": f"术语{i+1}的定义",
                            "confidence": round(random.uniform(0.7, 0.95), 2)
                        }
                        for i in range(random.randint(5, 15))
                    ]
                    logger.info(f"任务 {task_id} 生成了 {len(_collection_tasks[task_id]['results'])} 个结果")

            # 在实际项目中，应该使用异步任务队列
            # 这里为了简单演示，使用线程
            import threading
            threading.Thread(target=update_task_status, daemon=True).start()
            logger.info(f"已启动后台线程处理任务 {task_id}")
            
            # 返回任务信息
            return {
                "status": "success",
                "message": "术语采集任务已启动",
                "data": {
                    "task_id": task_id,
                    "name": name,
                    "language": language,
                    "mode": collection_mode,
                    "target_website": target_website,
                    "created_at": datetime.now().isoformat(),
                    "status": "pending",
                    "progress": 0
                }
            }
        
        except Exception as e:
            logger.error(f"启动术语采集任务失败: {str(e)}")
            return {
                "status": "error",
                "message": f"启动术语采集任务失败: {str(e)}"
            }
    
    @staticmethod
    async def get_collection_status(
        task_id: str,
        user: User
    ) -> Dict[str, Any]:
        """
        获取术语采集任务状态
        
        Args:
            task_id: 任务ID
            user: 当前用户
            
        Returns:
            包含任务状态的字典
        """
        try:
            # 从内存存储中获取任务
            task = _collection_tasks.get(task_id)
            
            if not task:
                return {
                    "status": "error",
                    "message": f"未找到任务ID: {task_id}"
                }
            
            # 检查任务所有权
            if task.get("user_id") != user.id:
                return {
                    "status": "error",
                    "message": "您无权访问此任务"
                }
            
            # 返回任务状态
            return {
                "status": "success",
                "data": {
                    "task_id": task_id,
                    "status": task.get("status", "pending"),
                    "progress": task.get("progress", 0),
                    "results": task.get("results", []),
                    "total": len(task.get("results", []))
                }
            }
        
        except Exception as e:
            logger.error(f"获取术语采集任务状态失败: {str(e)}")
            return {
                "status": "error",
                "message": f"获取术语采集任务状态失败: {str(e)}"
            }
    
    @staticmethod
    async def get_collection_tasks(
        user: User,
        search: Optional[str] = None,
        status: Optional[str] = None,
        page: int = 1,
        page_size: int = 20
    ) -> Dict[str, Any]:
        """
        获取术语采集任务列表
        
        Args:
            user: 当前用户
            search: 搜索关键词（可选）
            status: 任务状态过滤（可选）
            page: 页码
            page_size: 每页数量
            
        Returns:
            包含任务列表的字典
        """
        try:
            # 确保_collection_tasks已初始化
            if not hasattr(sys.modules[__name__], '_collection_tasks'):
                global _collection_tasks
                _collection_tasks = {}
                logger.info("初始化_collection_tasks字典")

            # 过滤出当前用户的任务
            user_tasks = [
                task for task_id, task in _collection_tasks.items()
                if task.get("user_id") == user.id
            ]
            
            # 应用搜索过滤
            if search:
                search = search.lower()
                user_tasks = [
                    task for task in user_tasks
                    if search in task.get("name", "").lower() or 
                       search in task.get("keywords", "").lower() or
                       search in task.get("target_website", "").lower()
                ]
            
            # 应用状态过滤
            if status:
                user_tasks = [
                    task for task in user_tasks
                    if task.get("status") == status
                ]
            
            # 按创建时间倒序排序
            if user_tasks:
                user_tasks.sort(key=lambda x: x.get("created_at", ""), reverse=True)
            
            # 分页
            total = len(user_tasks)
            start_idx = (page - 1) * page_size
            end_idx = start_idx + page_size
            paged_tasks = user_tasks[start_idx:end_idx] if user_tasks else []
            
            # 检查是否为空列表，如果是，则添加演示任务
            if not paged_tasks:
                # 添加一个演示任务
                demo_task = {
                    "id": "demo-task-001",
                    "name": "示例术语采集任务",
                    "language": "zh-cn",
                    "collection_mode": "smart", 
                    "keywords": "人工智能,机器学习,深度学习",
                    "status": "completed",
                    "progress": 100,
                    "created_at": "2025-07-01T10:00:00Z",
                    "results_count": 15,
                    "is_demo": True  # 标记为演示任务
                }
                paged_tasks = [demo_task]
                total = 1
                
                logger.info(f"任务列表为空，添加演示任务: {demo_task['id']}")
            
            # 返回结果
            return {
                "status": "success",
                "data": {
                    "tasks": paged_tasks,
                    "total": total,
                    "page": page,
                    "page_size": page_size
                }
            }
        
        except Exception as e:
            logger.error(f"获取术语采集任务列表失败: {str(e)}")
            # 即使出错也返回空列表而不是错误信息
            return {
                "status": "success",
                "message": f"获取任务列表时遇到问题，返回空列表: {str(e)}",
                "data": {
                    "tasks": [],
                    "total": 0,
                    "page": page,
                    "page_size": page_size
                }
            }
    
    @staticmethod
    async def get_collection_task_detail(
        task_id: str,
        user: User
    ) -> Dict[str, Any]:
        """
        获取术语采集任务详情
        
        Args:
            task_id: 任务ID
            user: 当前用户
            
        Returns:
            包含任务详情的字典
        """
        try:
            # 从内存存储中获取任务
            task = _collection_tasks.get(task_id)
            
            if not task:
                logger.warning(f"尝试获取不存在的任务详情: {task_id}")
                return {
                    "status": "error",
                    "message": f"未找到任务ID: {task_id}",
                    "data": None
                }
            
            # 检查任务所有权
            if task.get("user_id") != user.id:
                logger.warning(f"用户 {user.id} 尝试获取其他用户的任务详情: {task_id}")
                return {
                    "status": "error",
                    "message": "您无权访问此任务",
                    "data": None
                }
            
            # 返回任务详情
            return {
                "status": "success",
                "data": task
            }
        
        except Exception as e:
            logger.error(f"获取术语采集任务详情失败: {str(e)}")
            return {
                "status": "error",
                "message": f"获取术语采集任务详情失败: {str(e)}",
                "data": None
            } 