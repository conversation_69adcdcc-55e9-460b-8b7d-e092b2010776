"""
测试统一任务管理的简化任务
"""
import time
from app.core.celery_app import celery_app, UnifiedTask
from app.core.task_manager import task_manager
from app.models.unified_tasks import TaskTypes, TaskStatus

@celery_app.task(bind=True, base=UnifiedTask, name="test_unified_task")
def test_unified_task(self, task_id: str, test_message: str = "测试任务", duration: int = 10):
    """测试统一任务管理的简单任务"""
    
    try:
        # 标记任务开始
        self.start_task(task_id, self.request.id)
        
        # 模拟任务执行过程
        for i in range(duration):
            progress = int((i + 1) / duration * 100)
            message = f"正在处理步骤 {i + 1}/{duration}: {test_message}"
            
            self.update_task_progress(task_id, progress, message)
            
            # 模拟工作
            time.sleep(1)
        
        # 标记任务完成
        self.complete_task(
            task_id,
            output_data={
                "result": f"任务完成: {test_message}",
                "steps_completed": duration,
                "message": "测试任务成功完成"
            }
        )
        
        return {
            "status": "completed",
            "message": f"测试任务完成: {test_message}",
            "steps": duration
        }
        
    except Exception as e:
        error_msg = str(e)
        print(f"测试任务失败: {error_msg}")
        
        # 标记任务失败
        self.fail_task(task_id, error_msg)
        
        return {
            "status": "failed",
            "error": error_msg
        }

@celery_app.task(bind=True, base=UnifiedTask, name="test_wanx_simple")
def test_wanx_simple(self, task_id: str, prompt: str = "测试视频生成"):
    """简化的 Wanx 测试任务"""
    
    try:
        # 标记任务开始
        self.start_task(task_id, self.request.id)
        
        # 模拟 Wanx 视频生成过程
        steps = [
            (10, "正在初始化 Wanx 2.1 模型..."),
            (30, "正在加载模型权重..."),
            (50, "正在生成视频帧..."),
            (70, "正在合成视频..."),
            (90, "正在后处理..."),
            (100, "视频生成完成")
        ]
        
        for progress, message in steps:
            self.update_task_progress(task_id, progress, message)
            time.sleep(2)  # 模拟处理时间
        
        # 模拟生成的视频文件
        fake_video_url = f"/api/v1/video/download/test_{task_id[:8]}.mp4"
        
        # 标记任务完成
        self.complete_task(
            task_id,
            output_data={
                "video_url": fake_video_url,
                "prompt": prompt,
                "message": "视频生成完成"
            },
            output_files=[f"test_{task_id[:8]}.mp4"]
        )
        
        return {
            "status": "completed",
            "video_url": fake_video_url,
            "message": "视频生成完成"
        }
        
    except Exception as e:
        error_msg = str(e)
        print(f"Wanx 测试任务失败: {error_msg}")
        
        # 标记任务失败
        self.fail_task(task_id, error_msg)
        
        return {
            "status": "failed",
            "error": error_msg
        }
