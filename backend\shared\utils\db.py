from sqlalchemy import create_engine, event, MetaData, inspect
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
import os
from contextlib import contextmanager, asynccontextmanager
import logging
import re
from typing import Dict, Any, Optional, List, Callable, Generator
import uuid
from urllib.parse import quote_plus
from dotenv import load_dotenv
from sqlalchemy import text
import time
import threading
import asyncio
from datetime import datetime
import sys
import traceback

# 为异步支持导入必要的库
try:
    from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
    from sqlalchemy.ext.asyncio import async_sessionmaker
    HAS_ASYNC_SUPPORT = True
except ImportError:
    HAS_ASYNC_SUPPORT = False
    logging.warning("未安装SQLAlchemy异步支持，将使用同步方式")

# 加载环境变量
try:
    # 查找并加载.env文件
    current_dir = os.path.dirname(os.path.abspath(__file__))
    backend_dir = os.path.dirname(current_dir)
    repo_root = os.path.dirname(backend_dir)
    
    env_files = [
        os.path.join(repo_root, '.env'),  # 项目根目录
        os.path.join(backend_dir, '.env'),  # 后端目录
        '.env'  # 当前目录
    ]
    
    env_loaded = False
    for env_file in env_files:
        if os.path.exists(env_file):
            logging.info(f"加载环境变量文件: {env_file}")
            load_dotenv(env_file)
            env_loaded = True
            break
            
    if not env_loaded:
        logging.warning("未找到.env文件，将使用系统环境变量")
except ValueError as e:
    if "embedded null character" in str(e):
        logging.warning("检测到.env文件中存在嵌入空字符，将使用系统环境变量")
    else:
        logging.error(f"加载环境变量时出错: {e}")

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 数据库初始化状态
DB_INITIALIZED = False
db_error = None

# 全局变量定义
engine = None

# 数据库连接池监控
db_pool_stats = {
    "created_connections": 0,
    "checked_out_connections": 0,
    "checked_in_connections": 0,
    "connection_errors": 0,
    "last_error": None,
    "last_error_time": None,
    "checkout_count": 0,
    "checkin_count": 0,
    "overflow_count": 0,
    "successful_checkouts": 0,
    "failed_checkouts": 0,
    "connection_resets": 0,
    "active_connections": 0,
    "idle_connections": 0,
    "connection_timeouts": 0
}

# 连接使用计数器
connection_usage_counter = {}

# 连接使用锁
connection_lock = threading.RLock()

# 数据库连接池配置 - 从环境变量读取或使用默认值
DB_POOL_SIZE = int(os.getenv("DB_POOL_SIZE", "10"))  # 增加默认连接池大小
DB_MAX_OVERFLOW = int(os.getenv("DB_MAX_OVERFLOW", "20"))  # 增加最大溢出连接数
DB_POOL_TIMEOUT = int(os.getenv("DB_POOL_TIMEOUT", "30"))
DB_POOL_RECYCLE = int(os.getenv("DB_POOL_RECYCLE", "1800"))  # 30分钟回收连接
DB_ECHO_SQL = os.getenv("DB_ECHO_SQL", "False").lower() in ["true", "1", "yes"]
DB_CONTINUE_ON_ERROR = os.getenv("DB_CONTINUE_ON_ERROR", "True").lower() in ["true", "1", "yes"]

# 获取数据库URL并处理编码
DATABASE_URL = os.getenv("DATABASE_URL")
if not DATABASE_URL:
    logger.error("未找到DATABASE_URL环境变量")
    raise ValueError("DATABASE_URL环境变量未设置")

# 安全记录数据库URL（隐藏密码）
def get_safe_db_url(url: str) -> str:
    """返回安全的数据库URL（隐藏密码）"""
    if not url or '://' not in url:
        return url

    # 解析URL并隐藏密码
    try:
        protocol, rest = url.split('://', 1)
        if '@' in rest:
            auth, location = rest.split('@', 1)
            if ':' in auth:
                username, _ = auth.split(':', 1)
                return f"{protocol}://{username}:****@{location}"
            else:
                return f"{protocol}://****@{location}"
        else:
            return url
    except Exception:
        return "****"

logger.info(f"原始DATABASE_URL: {get_safe_db_url(DATABASE_URL)}")

# 检查URL中是否包含非ASCII字符
if any(ord(c) > 127 for c in DATABASE_URL):
    logger.warning("检测到数据库URL中包含非ASCII字符，尝试修复...")
    # 解析URL
    from urllib.parse import urlparse, urlunparse
    parsed = urlparse(DATABASE_URL)
    # 对用户名和密码进行URL编码
    username = quote_plus(parsed.username)
    password = quote_plus(parsed.password)
    # 重建URL
    DATABASE_URL = urlunparse((
        parsed.scheme,
        f"{username}:{password}@{parsed.hostname}:{parsed.port}",
        parsed.path,
        parsed.params,
        parsed.query,
        parsed.fragment
    ))
    logger.info(f"修复后的数据库URL: {DATABASE_URL}")

# 不再自动添加 client_encoding 参数，因为 asyncpg 不支持
# 编码将通过 connect_args 设置
logger.info(f"使用数据库URL: {get_safe_db_url(DATABASE_URL)}")

# 安全记录数据库URL（隐藏密码）
def get_safe_db_url(url: str) -> str:
    """返回安全的数据库URL（隐藏密码）"""
    if not url or '://' not in url:
        return url
    
    # 解析URL并隐藏密码
    try:
        protocol, rest = url.split('://', 1)
        if '@' in rest:
            auth, location = rest.split('@', 1)
            if ':' in auth:
                username, _ = auth.split(':', 1)
                return f"{protocol}://{username}:****@{location}"
            return f"{protocol}://{auth}@{location}"
        return url
    except Exception as e:
        logger.error(f"解析数据库URL时出错: {e}")
        return url

# 记录安全的数据库URL
safe_db_url = get_safe_db_url(DATABASE_URL)
logger.info(f"使用数据库URL: {safe_db_url}")

# 检查连接字符串中是否有非ASCII字符
def check_non_ascii(s: str) -> bool:
    """检查字符串中是否包含非ASCII字符"""
    return any(ord(c) > 127 for c in s)

if check_non_ascii(DATABASE_URL):
    logger.warning("警告: 数据库URL包含非ASCII字符，这可能导致编码问题")
    try:
        # 尝试修复URL
        fixed_url = DATABASE_URL.encode('ascii', 'replace').decode('ascii')
        if fixed_url != DATABASE_URL:
            logger.info("尝试修复数据库URL中的非ASCII字符")
            DATABASE_URL = fixed_url
    except Exception as e:
        logger.error(f"修复数据库URL时出错: {e}")

# 创建数据库引擎
try:
    engine = create_engine(
        DATABASE_URL,
        pool_pre_ping=True,
        pool_size=DB_POOL_SIZE,
        max_overflow=DB_MAX_OVERFLOW,
        pool_timeout=DB_POOL_TIMEOUT,
        pool_recycle=DB_POOL_RECYCLE,
        echo=DB_ECHO_SQL,
        connect_args={
            "client_encoding": "utf8",  # 对psycopg2驱动，这是安全的
            "options": "-c timezone=utc -c client_encoding=utf8",
            "connect_timeout": 10  # 连接超时设置为10秒
        }
    )
    logger.info(f"数据库引擎创建成功 (pool_size={DB_POOL_SIZE}, max_overflow={DB_MAX_OVERFLOW}, pool_recycle={DB_POOL_RECYCLE}秒)")
    
    # 添加字段类型转换插件
    try:
        from utils.taskid_type_coerce import TaskIDTypeCoercePlugin
        TaskIDTypeCoercePlugin(engine)
        logger.info("已加载TaskIDTypeCoercePlugin插件，自动转换generation_task_id字段为字符串类型")
    except ImportError:
        logger.warning("TaskIDTypeCoercePlugin不可用，generation_task_id字段可能需要显式转换为字符串类型")
        
        # 添加默认的字段类型转换器
        @event.listens_for(engine, "before_execute", retval=True)
        def ensure_string_generation_task_id(conn, clauseelement, multiparams, params):
            # 在执行SQL前处理参数
            if multiparams:
                new_multiparams = []
                for mp in multiparams:
                    if isinstance(mp, dict) and 'generation_task_id' in mp and mp['generation_task_id'] is not None:
                        mp = mp.copy()
                        mp['generation_task_id'] = str(mp['generation_task_id'])
                    new_multiparams.append(mp)
                if new_multiparams != multiparams:
                    multiparams = tuple(new_multiparams)
            
            return clauseelement, multiparams, params
        
        logger.info("已添加内置的generation_task_id字段类型转换器")
        
except Exception as e:
    logger.error(f"创建数据库引擎失败: {e}")
    if DB_CONTINUE_ON_ERROR:
        logger.warning("继续运行，但数据库功能可能不可用")
    else:
        logger.critical("程序将退出")
        sys.exit(1)

# 创建会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 创建异步引擎和会话工厂（全局作用域）
if HAS_ASYNC_SUPPORT:
    ASYNC_DATABASE_URL = DATABASE_URL.replace('postgresql://', 'postgresql+asyncpg://')
    # 确保URL中不包含client_encoding参数 - asyncpg不支持此参数
    if "client_encoding" in ASYNC_DATABASE_URL:
        # 如果URL中包含client_encoding参数，去除它
        logger.warning("从asyncpg URL中移除client_encoding参数")
        # 移除client_encoding参数及其值
        ASYNC_DATABASE_URL = re.sub(r'[?&]client_encoding=[^&]*', '', ASYNC_DATABASE_URL)
        # 如果去除后URL以?或&结尾，则去除这些字符
        ASYNC_DATABASE_URL = re.sub(r'[?&]$', '', ASYNC_DATABASE_URL)
    
    logger.info(f"异步数据库URL (安全版): {get_safe_db_url(ASYNC_DATABASE_URL)}")
    
    async_engine = create_async_engine(
        ASYNC_DATABASE_URL,
        echo=False,
        pool_pre_ping=True,
        # asyncpg不接受options参数，使用其原生支持的参数
        connect_args={
            "server_settings": {"timezone": "UTC"}
        }
    )
    async_session = async_sessionmaker(
        async_engine, expire_on_commit=False, class_=AsyncSession
    )
    logger.info("异步数据库引擎创建成功")
else:
    async_session = None

# 创建基类
Base = declarative_base()

# 导入所有模型以确保它们被注册
try:
    from models.agent import Agent
    from models.true_agent import (
        TrueAgent, AgentTool, AgentKnowledgeBase, KnowledgeDocument,
        AgentMemory, AgentConversation, ConversationMessage, ToolExecution
    )
    logger.info("所有模型导入成功")
except ImportError as e:
    logger.warning(f"导入模型时出现警告: {e}")
except Exception as e:
    logger.error(f"导入模型失败: {e}")

# 添加连接监听器
@event.listens_for(engine, "checkout")
def receive_checkout(dbapi_connection, connection_record, connection_proxy):
    """监听数据库连接创建事件"""
    # 增加连接使用计数
    conn_id = id(dbapi_connection)
    with connection_lock:
        # 记录连接使用情况 - 用于监控可能的连接泄漏
        connection_usage_counter[conn_id] = connection_usage_counter.get(conn_id, 0) + 1
        db_pool_stats["checkout_count"] += 1
        
        # 检查连接是否被过度使用 - 可能表示连接泄漏
        usage_count = connection_usage_counter.get(conn_id, 0)
        # 降低警告阈值，及早发现问题
        if usage_count > 15:  # 原来是 30
            # 获取堆栈跟踪，帮助诊断泄漏源
            stack = traceback.format_stack()
            logger.warning(f"连接 {conn_id} 已被使用 {usage_count} 次，可能存在连接泄漏")
            logger.warning(f"可能的连接泄漏位置:\n" + ''.join(stack[-15:]))
            
            # 当连接使用次数过多时，尝试强制刷新连接
            if usage_count > 50:  # 原来是 100
                logger.error(f"连接 {conn_id} 使用次数过多 ({usage_count})，尝试强制刷新")
                try:
                    # 标记连接为无效，让连接池重新创建
                    connection_record.invalidate()
                    logger.info(f"已将连接 {conn_id} 标记为无效")
                    # 清除计数
                    connection_usage_counter.pop(conn_id, None)
                except Exception as e:
                    logger.error(f"刷新连接 {conn_id} 失败: {e}")
    
    return dbapi_connection

@event.listens_for(engine, "checkin")
def receive_checkin(dbapi_connection, connection_record):
    """监听连接归还事件"""
    conn_id = id(dbapi_connection)
    with connection_lock:
        db_pool_stats["checkin_count"] += 1
        db_pool_stats["checked_in_connections"] += 1
        db_pool_stats["active_connections"] = max(0, db_pool_stats["active_connections"] - 1)
        db_pool_stats["idle_connections"] = min(DB_POOL_SIZE, DB_POOL_SIZE - db_pool_stats["active_connections"])
        
        # 检查连接持有时间
        checkout_time = connection_record.info.get("checkout_time")
        if checkout_time:
            hold_time = time.time() - checkout_time
            if hold_time > 10:  # 如果连接持有时间超过10秒
                logger.warning(f"连接被持有时间过长: {hold_time:.2f}秒")
        
        # 重置当前连接的使用计数
        if conn_id in connection_usage_counter:
            # 不要完全清除计数，而是减少计数值，防止过度重置
            current_count = connection_usage_counter[conn_id]
            if current_count > 2:  # 保留小计数
                connection_usage_counter[conn_id] = max(1, current_count // 2)  # 减半而不是完全清零
                logger.debug(f"重置连接 {conn_id} 使用计数从 {current_count} 到 {connection_usage_counter[conn_id]}")
                
        # 定期检查并清理连接计数器
        if db_pool_stats["checkin_count"] % 50 == 0:  # 原来是100
            # 清理不再存在的连接ID
            to_remove = []
            for conn_id in connection_usage_counter:
                if conn_id != id(dbapi_connection) and connection_usage_counter[conn_id] > 100:  # 原来是500
                    logger.warning(f"清理过度使用的连接ID: {conn_id}，使用次数: {connection_usage_counter[conn_id]}")
                    to_remove.append(conn_id)
            
            # 从计数器中删除
            for conn_id in to_remove:
                del connection_usage_counter[conn_id]

@event.listens_for(engine, "connect")
def receive_connect(dbapi_connection, connection_record):
    """监听连接创建事件"""
    with connection_lock:
        db_pool_stats["created_connections"] += 1
        
    # 配置新连接
    cursor = dbapi_connection.cursor()
    try:
        # 确保使用正确的编码方式
        encoding = 'utf-8'  # 默认更改为utf-8
        if 'client_encoding=' in DATABASE_URL:
            start = DATABASE_URL.index('client_encoding=') + len('client_encoding=')
            end = DATABASE_URL.index('&', start) if '&' in DATABASE_URL[start:] else len(DATABASE_URL)
            encoding = DATABASE_URL[start:end]
        
        # 显式设置客户端编码
        cursor.execute(f"SET client_encoding='{encoding}'")
        cursor.execute("SET timezone='UTC'")
        cursor.execute("SET datestyle='ISO, YMD'")
        
        # 为每个连接创建特殊函数，可以用于诊断编码问题
        cursor.execute("""
        CREATE OR REPLACE FUNCTION check_encoding(text) RETURNS bytea AS $$
        BEGIN
            RETURN convert_to($1, 'UTF8');
        EXCEPTION WHEN OTHERS THEN
            RETURN convert_to('ENCODING_ERROR', 'UTF8');
        END;
        $$ LANGUAGE plpgsql;
        """)
        logger.info(f"数据库连接已设置: client_encoding={encoding}, timezone=UTC, datestyle=ISO")
    except Exception as e:
        logger.warning(f"设置数据库连接属性时出错: {e}")
        with connection_lock:
            db_pool_stats["connection_errors"] += 1
            db_pool_stats["last_error"] = str(e)
            db_pool_stats["last_error_time"] = datetime.now().isoformat()
    finally:
        cursor.close()

@event.listens_for(engine, "reset")
def receive_reset(dbapi_connection, connection_record):
    """监听连接重置事件"""
    with connection_lock:
        db_pool_stats["connection_resets"] += 1

# 智能会话管理器
@contextmanager
def smart_db_session():
    """
    智能数据库会话上下文管理器，简化会话管理
    
    使用方式:
    with smart_db_session() as session:
        # 使用session进行数据库操作
    """
    db_gen = get_db()
    session = None
    
    try:
        session = next(db_gen)
        yield session
        session.commit()
    except Exception as e:
        if session:
            session.rollback()
        raise
    finally:
        if session:
            session.close()

def get_db() -> Generator[Session, None, None]:
    """获取数据库会话"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# 后台连接池监控任务
def monitor_db_connections():
    """后台监控数据库连接池状态"""
    while True:
        try:
            # 检查连接池状态
            pool = engine.pool
            with connection_lock:
                # 更新连接池状态
                db_pool_stats["active_connections"] = pool.checkedout()
                db_pool_stats["idle_connections"] = pool.size() - pool.checkedout()
                
                # 记录当前连接池状态
                if db_pool_stats["active_connections"] >= DB_POOL_SIZE:
                    logger.warning(f"连接池使用率较高: {db_pool_stats['active_connections']}/{DB_POOL_SIZE} (活跃/总数)")
                
                # 检查是否需要回收空闲连接
                if time.time() % 60 < 1:  # 每1分钟执行一次(原来是5分钟)
                    logger.info(f"连接池状态: 活跃={db_pool_stats['active_connections']}, "
                               f"空闲={db_pool_stats['idle_connections']}, "
                               f"检出总数={db_pool_stats['checkout_count']}, "
                               f"归还总数={db_pool_stats['checkin_count']}")
                    
                    # 主动清理过度使用的连接
                    high_usage_connections = {
                        conn_id: usage for conn_id, usage in connection_usage_counter.items() 
                        if usage > 20  # 降低清理阈值，使之更主动清理（原来是50）
                    }
                    
                    if high_usage_connections:
                        logger.warning(f"检测到 {len(high_usage_connections)} 个高使用量连接，尝试清理")
                        for conn_id, usage in high_usage_connections.items():
                            logger.warning(f"尝试清理连接 {conn_id}，使用次数: {usage}")
                            # 重置计数而不是完全移除，避免数据丢失
                            connection_usage_counter[conn_id] = 1  # 重置为1
                            
                        # 尝试调用回收功能
                        try:
                            pool.dispose()
                            logger.info("已清理连接池")
                            
                            # 再次更新连接池状态
                            db_pool_stats["active_connections"] = pool.checkedout()
                            db_pool_stats["idle_connections"] = pool.size() - pool.checkedout()
                            logger.info(f"清理后连接池状态: 活跃={db_pool_stats['active_connections']}, 空闲={db_pool_stats['idle_connections']}")
                        except Exception as e:
                            logger.error(f"清理连接池时出错: {e}")
                
                # 更加主动地检测泄漏 - 每30秒检查一次
                if time.time() % 30 < 1:  # 每30秒执行一次（原来是1分钟）
                    # 检查是否有长时间未归还的连接
                    for conn_id, usage in list(connection_usage_counter.items()):
                        if usage > 20:  # 降低检测阈值（原来是50）
                            logger.warning(f"连接 {conn_id} 使用次数过高: {usage}，可能存在泄漏")
                            # 如果计数器超过30次，直接重置
                            if usage > 30:  # 新增条件
                                logger.error(f"连接 {conn_id} 使用次数过高 ({usage})，主动重置计数")
                                connection_usage_counter[conn_id] = 1  # 重置为1
                                
                                # 检查是否需要调用dispose
                                if time.time() % 120 < 1:  # 每2分钟才执行一次dispose，避免频繁执行
                                    try:
                                        pool.dispose()
                                        logger.info("主动清理连接池完成")
                                    except Exception as e:
                                        logger.error(f"主动清理连接池时出错: {e}")
            
            # 休眠
            time.sleep(10)
        except Exception as e:
            logger.error(f"监控数据库连接池时出错: {e}")
            time.sleep(30)

# 启动监控线程
def start_connection_monitor():
    """启动数据库连接监控线程"""
    monitor_thread = threading.Thread(
        target=monitor_db_connections,
        daemon=True,
        name="DBConnectionMonitor"
    )
    monitor_thread.start()
    logger.info("数据库连接监控线程已启动")

# 获取连接池状态
def get_db_pool_stats():
    """获取数据库连接池统计信息"""
    try:
        stats = {
            "pool_size": DB_POOL_SIZE,
            "max_overflow": DB_MAX_OVERFLOW,
            "pool_timeout": DB_POOL_TIMEOUT,
            "pool_recycle": DB_POOL_RECYCLE
        }
        
        # 如果可以获取更详细的池统计信息
        if hasattr(engine, "pool"):
            pool = engine.pool
            if hasattr(pool, "size") and callable(getattr(pool, "size", None)):
                stats["current_size"] = pool.size()
            if hasattr(pool, "checkedin") and callable(getattr(pool, "checkedin", None)):
                stats["checked_in"] = pool.checkedin()
            if hasattr(pool, "checkedout") and callable(getattr(pool, "checkedout", None)):
                stats["checked_out"] = pool.checkedout()
            if hasattr(pool, "overflow") and callable(getattr(pool, "overflow", None)):
                stats["overflow"] = pool.overflow()
                
        return stats
    except Exception as e:
        logger.exception(f"获取连接池统计信息失败: {e}")
        return {"error": str(e)}

def init_db(create_tables=True, force_recreate=False, check_tables=True):
    """初始化数据库"""
    global DB_INITIALIZED
    
    # 如果已初始化且不强制重新创建，则直接返回
    if DB_INITIALIZED and not force_recreate:
        logger.debug("数据库已初始化，跳过...")
        return True
    
    try:
        # 确保导入所有模型
        logger.info("加载所有数据库模型...")
        # 导入模型 - 这确保所有模型都被加载并注册到Base.metadata
        try:
            # 导入用户模型
            from models.user import User
            logger.debug("已加载用户模型")
            
            # 导入任务模型
            from models.task import Task
            logger.debug("已加载任务模型")
            
            # 导入Agent相关模型
            from models.agent import (
                Agent, AgentTag, AgentCapability, AgentExample, 
                AgentReview, UserFavorite, AgentSession, AgentMessage
            )
            logger.debug("已加载Agent相关模型")
            
            # 导入聊天相关模型
            from models.chat_session import ChatSession
            from models.chat_message import ChatMessage
            logger.debug("已加载聊天相关模型")
            
            # 这里可以添加其他模型导入
            
        except ImportError as e:
            logger.error(f"导入模型时出错: {e}")
            if not DB_CONTINUE_ON_ERROR:
                raise
        
        # 检查关键表是否存在
        if check_tables:
            try:
                from sqlalchemy import inspect
                inspector = inspect(engine)
                existing_tables = inspector.get_table_names()
                expected_tables = ["users", "tasks", "agents"]
                
                missing_tables = [table for table in expected_tables if table not in existing_tables]
                if missing_tables:
                    logger.warning(f"检测到缺失的关键表: {', '.join(missing_tables)}")
                    logger.warning("您可以运行 python backend/scripts/fix_missing_tables.py 来修复表结构")
                    
                    if not create_tables:
                        logger.error("由于create_tables=False，不会自动创建缺失的表")
                        if not DB_CONTINUE_ON_ERROR:
                            raise ValueError(f"缺失关键表: {', '.join(missing_tables)}")
            except Exception as e:
                logger.error(f"检查表结构时出错: {e}")
                if not DB_CONTINUE_ON_ERROR:
                    raise
        
        # 如果指定创建表
        if create_tables:
            logger.info("创建数据库表...")
            # 创建所有表
            Base.metadata.create_all(engine)
            logger.info("数据库表创建完成")
        
        # 设置初始化标志
        DB_INITIALIZED = True
        return True
    except Exception as e:
        global db_error
        db_error = str(e)
        logger.error(f"初始化数据库时出错: {e}")
        logger.debug(f"错误详情:", exc_info=True)
        
        if DB_CONTINUE_ON_ERROR:
            logger.warning("由于设置了DB_CONTINUE_ON_ERROR=True，应用将继续运行，但数据库功能可能不可用")
            return False
        else:
            raise

def create_test_data():
    """创建测试数据（开发环境）"""
    from models.user import User
    from models.translation import Translation
    from sqlalchemy.orm import Session
    
    # 使用上下文管理器创建会话
    with Session(engine) as session:
        try:
            # 检查是否已存在测试用户
            test_user_exists = False
            try:
                test_user_exists = session.query(User).filter(User.username == "testuser").first() is not None
                logger.info(f"检查测试用户: {'存在' if test_user_exists else '不存在'}")
            except Exception as e:
                logger.warning(f"检查测试用户时出错: {e}")
            
            if not test_user_exists:
                logger.info("创建测试用户...")
                # 创建一个测试用户
                test_user = User(
                    username="testuser",
                    email="<EMAIL>",
                    full_name="Test User",
                    hashed_password="$2b$12$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW",  # 密码: password
                    is_active=True
                )
                session.add(test_user)
                # 先提交用户以获取ID
                session.commit()
                logger.info("测试用户创建成功")
                
                # 创建一个测试翻译记录
                logger.info("创建测试翻译记录...")
                # 获取用户ID
                user_id = test_user.id
                
                test_translation = Translation(
                    user_id=user_id,
                    translation_id=str(uuid.uuid4()),
                    source_language="English",
                    target_language="Chinese",
                    source_text="Hello, world!",
                    translated_text="你好，世界！",
                    status="completed",
                    detected_language="English",
                    meta_info={"confidence": 0.95}
                )
                session.add(test_translation)
                
                # 提交事务
                session.commit()
                logger.info("测试翻译记录创建成功")
            return True
        except Exception as e:
            logger.error(f"创建测试数据失败: {e}")
            logger.debug("详细错误信息", exc_info=True)
            session.rollback()
            if os.environ.get("DB_CONTINUE_ON_ERROR", "False").lower() == "true":
                logger.error("由于 DB_CONTINUE_ON_ERROR=True，应用将继续运行")
                return False
            else:
                raise

async def init_db_async():
    """异步初始化数据库"""
    global DB_INITIALIZED
    try:
        if not HAS_ASYNC_SUPPORT:
            logger.error("未安装SQLAlchemy异步支持，无法进行异步数据库初始化")
            return False
        logger.info("开始异步数据库初始化...")
        # 测试连接
        async with async_session() as session:
            result = await session.execute(text("SELECT 1"))
            logger.info(f"异步数据库连接测试结果: {result.scalar()}")
            
            # 设置会话级别的时区
            await session.execute(text("SET timezone='UTC'"))
            logger.info("异步数据库会话时区已设置为UTC")
            
        logger.info("异步数据库初始化成功")
        DB_INITIALIZED = True
        return True
    except Exception as e:
        logger.error(f"异步数据库初始化失败: {e}")
        return False

def initialize():
    """初始化数据库系统，包括连接池和表结构"""
    try:
        # 初始化数据库系统
        init_success = init_db()
        
        # 如果初始化失败但允许继续，输出警告
        if not init_success and DB_CONTINUE_ON_ERROR:
            logger.warning("数据库初始化失败，但由于DB_CONTINUE_ON_ERROR=True，应用将继续运行")
            logger.warning("您可以运行 python backend/scripts/fix_missing_tables.py 来修复表结构")
        
        # 启动连接监控器
        start_connection_monitor()
        
        return init_success
    except Exception as e:
        logger.error(f"初始化数据库系统时出错: {e}")
        
        if DB_CONTINUE_ON_ERROR:
            logger.warning("由于设置了DB_CONTINUE_ON_ERROR=True，应用将继续运行，但数据库功能可能不可用")
            return False
        else:
            raise

# 添加获取引擎的函数
def get_engine():
    """获取数据库引擎实例"""
    global engine
    if engine is None:
        try:
            # 创建数据库引擎
            engine = create_engine(
                DATABASE_URL,
                pool_pre_ping=True,
                pool_size=DB_POOL_SIZE,
                max_overflow=DB_MAX_OVERFLOW,
                pool_timeout=DB_POOL_TIMEOUT,
                pool_recycle=DB_POOL_RECYCLE,
                echo=DB_ECHO_SQL,
                connect_args={
                    "client_encoding": "utf8",  # 对psycopg2驱动，这是安全的
                    "options": "-c timezone=utc -c client_encoding=utf8",
                    "connect_timeout": 10  # 连接超时设置为10秒
                }
            )
            logger.info(f"数据库引擎创建成功 (get_engine, pool_size={DB_POOL_SIZE})")
        except Exception as e:
            logger.error(f"创建数据库引擎时出错: {e}")
            if not DB_CONTINUE_ON_ERROR:
                raise
    return engine

@asynccontextmanager
async def get_async_session():
    """获取异步数据库会话 (async context manager)"""
    if not HAS_ASYNC_SUPPORT:
        raise RuntimeError("异步数据库支持未启用")
    async_db = None
    session_id = str(uuid.uuid4())[:8]
    logger.debug(f"创建异步数据库会话 ({session_id})...")
    
    try:
        async_db = async_session()
        logger.debug(f"异步数据库会话 ({session_id}) 创建成功")
        
        # 设置会话级别的时区 - asyncpg需要通过显式SQL设置
        try:
            await async_db.execute(text("SET timezone='UTC'"))
            await async_db.execute(text("SET datestyle='ISO, YMD'"))
            logger.debug(f"异步数据库会话 ({session_id}) 设置完成")
        except Exception as e:
            logger.warning(f"设置异步会话属性时出错 ({session_id}): {e}")
        
        yield async_db
    except Exception as e:
        logger.error(f"创建或使用异步数据库会话时出错 ({session_id}): {e}")
        raise
    finally:
        if async_db:
            try:
                await async_db.close()
                logger.debug(f"异步数据库会话 ({session_id}) 已关闭")
            except Exception as e:
                logger.error(f"关闭异步数据库会话时出错 ({session_id}): {e}")

# 异步数据库上下文管理器 - 用于适配同步get_db给异步上下文使用
class AsyncDBContextManager:
    """同步数据库会话的异步上下文管理器适配器"""
    
    def __init__(self):
        """初始化适配器"""
        self.db = None
        self.db_gen = None
    
    async def __aenter__(self):
        """异步进入上下文"""
        try:
            self.db_gen = get_db()
            self.db = next(self.db_gen)
            return self.db
        except Exception as e:
            logger.error(f"获取数据库会话失败: {str(e)}")
            if self.db:
                self.db.close()
            if self.db_gen:
                try:
                    next(self.db_gen, None)
                except StopIteration:
                    pass
            raise
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步退出上下文"""
        if self.db:
            try:
                if exc_type is not None:
                    # 如果有异常，回滚事务
                    self.db.rollback()
                else:
                    # 否则提交事务
                    self.db.commit()
            except Exception as e:
                logger.error(f"提交/回滚事务失败: {str(e)}")
                if exc_type is None:
                    raise
            finally:
                # 关闭会话
                self.db.close()
                self.db = None
        
        if self.db_gen:
            try:
                next(self.db_gen, None)  # 消耗生成器的剩余部分
            except StopIteration:
                pass
            self.db_gen = None

# 供异步上下文管理器使用的函数
def async_db_context():
    """
    获取异步兼容的数据库会话
    
    用法:
    async with async_db_context() as db:
        # 使用db进行数据库操作
    """
    return AsyncDBContextManager()

def refresh_pool_settings():
    """动态更新数据库连接池设置
    
    此函数允许在运行时调整数据库连接池参数，
    通过从环境变量重新读取配置并应用到现有引擎
    """
    global engine, SessionLocal, DB_POOL_SIZE, DB_MAX_OVERFLOW, DB_POOL_TIMEOUT, DB_POOL_RECYCLE
    global async_engine, async_session
    
    # 从环境变量读取新设置
    DB_POOL_SIZE = int(os.getenv("DB_POOL_SIZE", "10"))
    DB_MAX_OVERFLOW = int(os.getenv("DB_MAX_OVERFLOW", "20"))
    DB_POOL_TIMEOUT = int(os.getenv("DB_POOL_TIMEOUT", "30"))
    DB_POOL_RECYCLE = int(os.getenv("DB_POOL_RECYCLE", "1800"))
    
    logger.info(f"更新连接池设置: pool_size={DB_POOL_SIZE}, max_overflow={DB_MAX_OVERFLOW}, "
               f"pool_timeout={DB_POOL_TIMEOUT}, pool_recycle={DB_POOL_RECYCLE}")
    
    try:
        # 首先处理现有连接
        if engine and hasattr(engine, "pool"):
            # 记录当前状态
            active_before = engine.pool.checkedout()
            total_before = engine.pool.size()
            logger.info(f"重置前连接状态: 活跃={active_before}/{total_before}")
            
            # 清空连接池
            engine.pool.dispose()
            logger.info("已清空现有连接池")
        
        # 创建新引擎
        engine = create_engine(
            DATABASE_URL,
            pool_pre_ping=True,
            pool_size=DB_POOL_SIZE,
            max_overflow=DB_MAX_OVERFLOW,
            pool_timeout=DB_POOL_TIMEOUT,
            pool_recycle=DB_POOL_RECYCLE,
            echo=DB_ECHO_SQL,
            connect_args={
                "client_encoding": "utf8",  # 保留同步引擎的client_encoding设置
                "options": "-c timezone=utc -c client_encoding=utf8",
                "connect_timeout": 10  # 连接超时设置为10秒
            }
        )
        
        # 更新会话工厂
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        
        # 更新异步引擎（如果已启用）
        if HAS_ASYNC_SUPPORT:
            # 如果异步引擎已存在，先清理连接池
            if async_engine and hasattr(async_engine, "pool"):
                try:
                    await_func = getattr(async_engine.pool, "dispose", None)
                    if await_func and callable(await_func):
                        import asyncio
                        asyncio.create_task(await_func())
                        logger.info("已安排清理异步连接池")
                except Exception as e:
                    logger.warning(f"清理异步连接池时出错: {e}")
            
            # 重新创建异步引擎
            ASYNC_DATABASE_URL = DATABASE_URL.replace('postgresql://', 'postgresql+asyncpg://')
            # 确保URL中不包含client_encoding参数
            if "client_encoding" in ASYNC_DATABASE_URL:
                ASYNC_DATABASE_URL = re.sub(r'[?&]client_encoding=[^&]*', '', ASYNC_DATABASE_URL)
                ASYNC_DATABASE_URL = re.sub(r'[?&]$', '', ASYNC_DATABASE_URL)
            
            async_engine = create_async_engine(
                ASYNC_DATABASE_URL,
                echo=False,
                pool_pre_ping=True,
                connect_args={
                    "server_settings": {"timezone": "UTC"}
                }
            )
            async_session = async_sessionmaker(
                async_engine, expire_on_commit=False, class_=AsyncSession
            )
            logger.info("异步数据库引擎已更新")
        
        # 清空连接使用计数器
        connection_usage_counter.clear()
        
        # 重置连接池统计信息
        for key in db_pool_stats.keys():
            if key not in ["last_error", "last_error_time"]:
                db_pool_stats[key] = 0
        
        # 测试连接
        with SessionLocal() as session:
            result = session.execute(text("SELECT 1")).scalar()
            logger.info(f"新连接池测试: {result}")
        
        logger.info("连接池设置已成功更新")
        return True
    except Exception as e:
        logger.error(f"更新连接池设置失败: {e}")
        return False 

@asynccontextmanager
async def get_db_connection():
    """
    获取异步数据库连接的上下文管理器
    用于API路由中执行异步数据库操作
    
    用法:
    async with get_db_connection() as conn:
        result = await conn.fetch("SELECT * FROM users")
    """
    if not HAS_ASYNC_SUPPORT:
        raise RuntimeError("异步数据库支持未启用")
    
    session_id = str(uuid.uuid4())[:8]
    logger.debug(f"创建异步数据库连接 ({session_id})...")
    
    try:
        async with async_session() as session:
            # 获取底层连接
            conn = await session.connection()
            logger.debug(f"异步数据库连接 ({session_id}) 创建成功")
            
            # 设置连接级别的时区
            try:
                await conn.execute(text("SET timezone='UTC'"))
                await conn.execute(text("SET datestyle='ISO, YMD'"))
                logger.debug(f"异步数据库连接 ({session_id}) 设置完成")
            except Exception as e:
                logger.warning(f"设置异步连接属性时出错 ({session_id}): {e}")
            
            yield conn
    except Exception as e:
        logger.error(f"创建或使用异步数据库连接时出错 ({session_id}): {e}")
        raise 