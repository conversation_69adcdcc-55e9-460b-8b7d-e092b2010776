"""
TTS API端点
"""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import Dict, Any, List, Optional
import logging

from app.services.tts_model_manager import tts_model_manager

logger = logging.getLogger(__name__)

router = APIRouter()

class TTSRequest(BaseModel):
    """TTS请求模型"""
    text: str
    model_id: str
    voice_config: Dict[str, Any]

class TTSResponse(BaseModel):
    """TTS响应模型"""
    success: bool
    audio_url: Optional[str] = None
    duration: Optional[float] = None
    model: Optional[str] = None
    error: Optional[str] = None

@router.get("/models")
async def get_available_models():
    """获取可用的TTS模型"""
    try:
        models = tts_model_manager.get_available_models()
        return {
            "success": True,
            "models": models
        }
    except Exception as e:
        logger.error(f"获取TTS模型失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/models/{model_id}/voices")
async def get_model_voices(model_id: str):
    """获取指定模型的可用语音"""
    try:
        voices = tts_model_manager.get_model_voices(model_id)
        return {
            "success": True,
            "voices": voices
        }
    except Exception as e:
        logger.error(f"获取模型语音失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/install/{model_id}")
async def install_model(model_id: str):
    """安装TTS模型"""
    try:
        result = await tts_model_manager.install_model(model_id)
        if result["success"]:
            return result
        else:
            raise HTTPException(status_code=400, detail=result["error"])
    except Exception as e:
        logger.error(f"安装TTS模型失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/synthesize", response_model=TTSResponse)
async def synthesize_speech(request: TTSRequest):
    """合成语音"""
    try:
        result = await tts_model_manager.synthesize_speech(
            request.text,
            request.model_id,
            request.voice_config
        )
        
        if result["success"]:
            # 转换文件路径为URL
            audio_url = result["audio_path"].replace("\\", "/")
            if not audio_url.startswith("/"):
                audio_url = "/" + audio_url
            
            return TTSResponse(
                success=True,
                audio_url=audio_url,
                duration=result.get("duration"),
                model=result.get("model")
            )
        else:
            return TTSResponse(
                success=False,
                error=result["error"]
            )
            
    except Exception as e:
        logger.error(f"语音合成失败: {str(e)}")
        return TTSResponse(
            success=False,
            error=str(e)
        )

@router.post("/preview")
async def preview_voice():
    """语音预览"""
    try:
        # 简化的预览实现
        return {
            "success": True,
            "audio_url": "/static/preview/sample.wav",
            "message": "预览功能开发中"
        }
    except Exception as e:
        logger.error(f"语音预览失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
