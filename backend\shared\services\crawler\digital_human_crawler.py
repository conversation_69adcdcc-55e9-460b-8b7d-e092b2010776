from typing import List, Dict, Any, Optional
import aiohttp
import asyncio
import logging
import json
import os
import time
from datetime import datetime
from bs4 import BeautifulSoup
from pathlib import Path

# 配置日志
logger = logging.getLogger(__name__)

# 爬取目标
CRAWL_TARGETS = [
    {
        "name": "MetaHuman", 
        "url": "https://www.unrealengine.com/marketplace/en-US/product/metahuman-creator",
        "selector": ".asset-image-grid__items > div",
        "fields": {
            "name": ".asset-image-grid__title",
            "description": ".asset-image-grid__subtitle",
            "image": ".asset-image-grid__image img",
            "type": ".asset-image-grid__category"
        }
    },
    {
        "name": "Ready Player Me", 
        "url": "https://readyplayer.me/3d-avatars",
        "selector": ".avatar-gallery-item",
        "fields": {
            "name": ".avatar-gallery-item__title",
            "description": ".avatar-gallery-item__description",
            "image": ".avatar-gallery-item__image img",
            "type": ".avatar-gallery-item__category"
        }
    },
    {
        "name": "FaceBuilder", 
        "url": "https://keentools.io/products/facebuilder-for-blender",
        "selector": ".showcase-gallery .gallery-item",
        "fields": {
            "name": ".gallery-item-title",
            "description": ".gallery-item-description",
            "image": ".gallery-item-image img",
            "type": ".gallery-item-category"
        }
    }
]

async def crawl_digital_human_templates() -> List[Dict[str, Any]]:
    """爬取所有目标网站的数字人模板"""
    all_templates = []
    
    for target in CRAWL_TARGETS:
        try:
            templates = await crawl_single_target(target)
            all_templates.extend(templates)
            logger.info(f"成功从 {target['name']} 爬取 {len(templates)} 个模板")
        except Exception as e:
            logger.error(f"从 {target['name']} 爬取失败: {str(e)}")
    
    # 如果无法爬取到任何真实数据，使用模拟数据
    if not all_templates:
        logger.warning("未能爬取到真实数据，使用模拟数据")
        all_templates = simulate_realistic_templates()
    
    # 处理结果，确保数据格式正确
    processed_templates = process_templates(all_templates)
    
    # 缓存结果
    await cache_templates(processed_templates)
    
    return processed_templates

def simulate_realistic_templates() -> List[Dict[str, Any]]:
    """生成模拟的数字人模板数据（当爬虫失败时使用）"""
    return [
        {
            "id": "sim001",
            "name": "商务精英 Emma",
            "description": "现代商务风格，适合专业场景的数字人形象",
            "imageUrl": "https://img.freepik.com/free-photo/elegant-young-businesswoman-formal-wear-using-tablet_176420-7809.jpg",
            "type": "customer_service",
            "source": "模拟数据（Freepik）",
            "crawled_at": datetime.now().isoformat()
        },
        {
            "id": "sim002",
            "name": "科技专家 Alex",
            "description": "科技风格男性形象，适合技术解说和产品演示",
            "imageUrl": "https://img.freepik.com/free-photo/portrait-happy-male-with-broad-smile_176532-8175.jpg",
            "type": "assistant",
            "source": "模拟数据（Freepik）",
            "crawled_at": datetime.now().isoformat()
        },
        {
            "id": "sim003",
            "name": "教育讲师 Sarah",
            "description": "专业且亲和的教育者形象，适合各类教学场景",
            "imageUrl": "https://img.freepik.com/free-photo/portrait-female-teacher-holding-notepad-colored-pencils_176532-7144.jpg",
            "type": "teacher",
            "source": "模拟数据（Freepik）",
            "crawled_at": datetime.now().isoformat()
        },
        {
            "id": "sim004",
            "name": "直播达人 Mike",
            "description": "活力四射的年轻男性，适合娱乐和直播场景",
            "imageUrl": "https://img.freepik.com/free-photo/young-man-student-with-notebooks-showing-thumb-up-blue-wall_1301-5103.jpg",
            "type": "broadcaster",
            "source": "模拟数据（Freepik）",
            "crawled_at": datetime.now().isoformat()
        },
        {
            "id": "sim005",
            "name": "时尚达人 Zoe",
            "description": "时尚潮流女性，适合品牌营销和时尚直播",
            "imageUrl": "https://img.freepik.com/free-photo/pretty-young-stylish-sexy-woman-pink-luxury-dress-summer-fashion-trend_285396-7618.jpg",
            "type": "broadcaster",
            "source": "模拟数据（Freepik）",
            "crawled_at": datetime.now().isoformat()
        },
        {
            "id": "sim006",
            "name": "金融顾问 William",
            "description": "专业可靠的金融专家形象，适合财务顾问角色",
            "imageUrl": "https://img.freepik.com/free-photo/confident-business-man-portrait_144627-28601.jpg",
            "type": "customer_service",
            "source": "模拟数据（Freepik）",
            "crawled_at": datetime.now().isoformat()
        }
    ]

async def crawl_single_target(target: Dict[str, Any]) -> List[Dict[str, Any]]:
    """爬取单个目标网站的数字人模板"""
    templates = []
    
    async with aiohttp.ClientSession() as session:
        try:
            async with session.get(
                target["url"], 
                headers={
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
                    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
                    "Accept-Language": "en-US,en;q=0.5",
                    "Connection": "keep-alive",
                    "Upgrade-Insecure-Requests": "1"
                },
                timeout=aiohttp.ClientTimeout(total=30)
            ) as response:
                if response.status != 200:
                    logger.error(f"请求 {target['url']} 失败: {response.status}")
                    return []
                
                html = await response.text()
                soup = BeautifulSoup(html, "html.parser")
                
                # 查找所有模板元素
                items = soup.select(target["selector"])
                logger.info(f"在 {target['name']} 中找到 {len(items)} 个可能的模板元素")
                
                for idx, item in enumerate(items):
                    template = {
                        "id": f"crawled_{target['name'].lower().replace(' ', '_')}_{idx + 1}",
                        "source": target["name"],
                        "crawled_at": datetime.now().isoformat()
                    }
                    
                    # 提取各个字段
                    for field, selector in target["fields"].items():
                        if field == "image":
                            img = item.select_one(selector)
                            if img and img.has_attr("src"):
                                template["imageUrl"] = img["src"]
                                # 处理相对URL
                                if not template["imageUrl"].startswith(("http://", "https://")):
                                    base_url = "/".join(target["url"].split("/")[:3])  # 提取基本URL (schema + domain)
                                    template["imageUrl"] = f"{base_url}{template['imageUrl'] if template['imageUrl'].startswith('/') else '/' + template['imageUrl']}"
                        else:
                            elem = item.select_one(selector)
                            if elem:
                                template[field] = elem.text.strip()
                    
                    # 保证所有必要字段存在
                    if "name" in template and "imageUrl" in template:
                        templates.append(template)
        except asyncio.TimeoutError:
            logger.error(f"请求 {target['url']} 超时")
        except Exception as e:
            logger.error(f"爬取 {target['name']} 时出错: {str(e)}")
    
    return templates

def process_templates(templates: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """处理爬取的模板数据，使其符合系统格式"""
    processed = []
    
    for idx, template in enumerate(templates):
        # 处理图片URL，确保是HTTPS
        if "imageUrl" in template and template["imageUrl"].startswith("http:"):
            template["imageUrl"] = template["imageUrl"].replace("http:", "https:")
        
        # 映射类型字段
        if "type" in template:
            type_text = template["type"].lower() if template["type"] else ""
            if any(keyword in type_text for keyword in ["客服", "service", "support", "assistant", "客户", "顾问"]):
                template["type"] = "customer_service"
            elif any(keyword in type_text for keyword in ["主播", "播音", "broadcaster", "stream", "直播", "演出"]):
                template["type"] = "broadcaster"  
            elif any(keyword in type_text for keyword in ["教育", "老师", "讲师", "teach", "education", "instructor"]):
                template["type"] = "teacher"
            else:
                template["type"] = "assistant"
        else:
            # 根据名称或描述推断类型
            combined_text = f"{template.get('name', '')} {template.get('description', '')}".lower()
            if any(keyword in combined_text for keyword in ["客服", "service", "support", "顾问"]):
                template["type"] = "customer_service"
            elif any(keyword in combined_text for keyword in ["主播", "播音", "broadcaster", "stream", "直播"]):
                template["type"] = "broadcaster"  
            elif any(keyword in combined_text for keyword in ["教育", "老师", "讲师", "teach", "education"]):
                template["type"] = "teacher"
            else:
                template["type"] = "assistant"
        
        # 确保ID唯一
        template["id"] = f"t{idx+1:03d}"
        
        # 添加默认描述（如果没有）
        if "description" not in template or not template["description"]:
            if template["type"] == "customer_service":
                template["description"] = "专业的客服数字人形象，为用户提供优质服务体验"
            elif template["type"] == "broadcaster":
                template["description"] = "活力四射的主播数字人，适合直播和内容展示"
            elif template["type"] == "teacher":
                template["description"] = "知识渊博的教育数字人，为各类教学场景设计"
            else:
                template["description"] = "智能助手数字人，可为多种场景提供帮助"
        
        processed.append(template)
    
    return processed

async def cache_templates(templates: List[Dict[str, Any]]) -> None:
    """缓存爬取的模板数据"""
    cache_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "data")
    os.makedirs(cache_dir, exist_ok=True)
    
    cache_file = os.path.join(cache_dir, "cached_templates.json")
    
    try:
        with open(cache_file, "w", encoding="utf-8") as f:
            json.dump({
                "last_updated": datetime.now().isoformat(),
                "templates": templates
            }, f, ensure_ascii=False, indent=2)
        logger.info(f"模板数据已缓存到 {cache_file}，共 {len(templates)} 个模板")
    except Exception as e:
        logger.error(f"缓存模板数据失败: {str(e)}")

async def get_cached_templates() -> Optional[List[Dict[str, Any]]]:
    """获取缓存的模板数据"""
    cache_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "data")
    cache_file = os.path.join(cache_dir, "cached_templates.json")
    
    if not os.path.exists(cache_file):
        logger.info("模板缓存文件不存在")
        return None
    
    try:
        with open(cache_file, "r", encoding="utf-8") as f:
            data = json.load(f)
            
        # 检查缓存是否过期（24小时）
        last_updated = datetime.fromisoformat(data["last_updated"])
        if (datetime.now() - last_updated).total_seconds() > 86400:  # 24小时
            logger.info("模板缓存已过期")
            return None
            
        templates = data["templates"]
        logger.info(f"从缓存加载了 {len(templates)} 个模板")
        return templates
    except Exception as e:
        logger.error(f"读取缓存失败: {str(e)}")
        return None

async def crawl_and_cache_templates() -> None:
    """爬取并缓存模板，用于后台任务"""
    try:
        await crawl_digital_human_templates()
        logger.info("后台爬取任务完成")
    except Exception as e:
        logger.error(f"后台爬取任务失败: {str(e)}")

def get_default_templates() -> List[Dict[str, Any]]:
    """返回默认的模板数据（当缓存和爬取都失败时）"""
    return [
        # 教师类型
        {
            "id": "t001",
            "name": "专业女教师",
            "description": "温和亲切的女性教师形象，适合在线教育、培训讲解",
            "imageUrl": "https://images.unsplash.com/photo-1494790108755-2616c9c1e8e3?w=400&h=400&fit=crop&crop=face",
            "type": "teacher",
            "gender": "female",
            "age_range": "25-35",
            "style": "professional",
            "matchScore": 95
        },
        {
            "id": "t002",
            "name": "资深男教授",
            "description": "权威专业的男性教授形象，适合学术讲座、专业培训",
            "imageUrl": "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&h=400&fit=crop&crop=face",
            "type": "teacher",
            "gender": "male",
            "age_range": "35-50",
            "style": "academic",
            "matchScore": 92
        },

        # 客服类型
        {
            "id": "t003",
            "name": "亲和客服小姐",
            "description": "甜美亲和的客服形象，适合在线客服、产品咨询",
            "imageUrl": "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=400&h=400&fit=crop&crop=face",
            "type": "customer_service",
            "gender": "female",
            "age_range": "22-30",
            "style": "friendly",
            "matchScore": 88
        },
        {
            "id": "t004",
            "name": "专业客服经理",
            "description": "成熟稳重的客服经理形象，适合高端客户服务",
            "imageUrl": "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face",
            "type": "customer_service",
            "gender": "male",
            "age_range": "28-40",
            "style": "professional",
            "matchScore": 85
        },

        # 主播类型
        {
            "id": "t005",
            "name": "时尚女主播",
            "description": "时尚靓丽的女主播形象，适合直播带货、娱乐节目",
            "imageUrl": "https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=400&h=400&fit=crop&crop=face",
            "type": "broadcaster",
            "gender": "female",
            "age_range": "20-28",
            "style": "fashionable",
            "matchScore": 90
        },
        {
            "id": "t006",
            "name": "活力男主播",
            "description": "阳光活力的男主播形象，适合体育解说、游戏直播",
            "imageUrl": "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=400&h=400&fit=crop&crop=face",
            "type": "broadcaster",
            "gender": "male",
            "age_range": "22-32",
            "style": "energetic",
            "matchScore": 87
        },

        # 助手类型
        {
            "id": "t007",
            "name": "智能女助手",
            "description": "现代科技感的女性助手，适合AI产品演示、智能客服",
            "imageUrl": "https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?w=400&h=400&fit=crop&crop=face",
            "type": "assistant",
            "gender": "female",
            "age_range": "25-35",
            "style": "modern",
            "matchScore": 93
        },
        {
            "id": "t008",
            "name": "商务男助理",
            "description": "专业商务的男性助理形象，适合企业服务、商务咨询",
            "imageUrl": "https://images.unsplash.com/photo-1560250097-0b93528c311a?w=400&h=400&fit=crop&crop=face",
            "type": "assistant",
            "gender": "male",
            "age_range": "28-38",
            "style": "business",
            "matchScore": 89
        },

        # 销售类型
        {
            "id": "t009",
            "name": "专业销售女士",
            "description": "专业自信的销售女性，适合产品推广、商务洽谈",
            "imageUrl": "https://images.unsplash.com/photo-1580489944761-15a19d654956?w=400&h=400&fit=crop&crop=face",
            "type": "salesperson",
            "gender": "female",
            "age_range": "26-36",
            "style": "confident",
            "matchScore": 91
        },
        {
            "id": "t010",
            "name": "资深销售顾问",
            "description": "经验丰富的男性销售顾问，适合高端产品销售",
            "imageUrl": "https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?w=400&h=400&fit=crop&crop=face",
            "type": "salesperson",
            "gender": "male",
            "age_range": "30-45",
            "style": "experienced",
            "matchScore": 86
        }
    ]