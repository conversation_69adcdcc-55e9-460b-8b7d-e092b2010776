import { get, post, put } from './index';

/**
 * 获取系统设置
 * @param {string} group - 设置分组，如果不指定则获取所有设置
 * @returns {Promise}
 */
export function getSettings(group) {
  return get('/settings', { group });
}

/**
 * 更新系统设置
 * @param {Object} data - 设置数据
 * @returns {Promise}
 */
export function updateSettings(data) {
  return put('/settings', data);
}

/**
 * 获取系统基本信息
 * @returns {Promise}
 */
export function getSystemInfo() {
  return get('/settings/system-info');
}

/**
 * 获取服务器状态
 * @returns {Promise}
 */
export function getServerStatus() {
  return get('/settings/server-status');
}

/**
 * 创建系统备份
 * @param {Object} data - 备份配置
 * @returns {Promise}
 */
export function createBackup(data) {
  return post('/settings/backup', data);
}

/**
 * 获取备份列表
 * @returns {Promise}
 */
export function getBackupList() {
  return get('/settings/backups');
}

/**
 * 恢复系统备份
 * @param {string} backupId - 备份ID
 * @returns {Promise}
 */
export function restoreBackup(backupId) {
  return post(`/settings/backups/${backupId}/restore`);
}

/**
 * 下载备份文件
 * @param {string} backupId - 备份ID
 * @returns {Promise}
 */
export function downloadBackup(backupId) {
  return get(`/settings/backups/${backupId}/download`, {
    responseType: 'blob'
  });
}

/**
 * 删除备份文件
 * @param {string} backupId - 备份ID
 * @returns {Promise}
 */
export function deleteBackup(backupId) {
  return post(`/settings/backups/${backupId}/delete`);
}

/**
 * 上传备份文件
 * @param {FormData} formData - 表单数据
 * @returns {Promise}
 */
export function uploadBackup(formData) {
  return post('/settings/backups/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}

/**
 * 获取邮件服务设置
 * @returns {Promise}
 */
export function getEmailSettings() {
  return get('/settings/email');
}

/**
 * 更新邮件服务设置
 * @param {Object} data - 邮件设置数据
 * @returns {Promise}
 */
export function updateEmailSettings(data) {
  return put('/settings/email', data);
}

/**
 * 发送测试邮件
 * @param {Object} data - 测试邮件数据
 * @returns {Promise}
 */
export function sendTestEmail(data) {
  return post('/settings/email/test', data);
}

/**
 * 获取安全设置
 * @returns {Promise}
 */
export function getSecuritySettings() {
  return get('/settings/security');
}

/**
 * 更新安全设置
 * @param {Object} data - 安全设置数据
 * @returns {Promise}
 */
export function updateSecuritySettings(data) {
  return put('/settings/security', data);
}

/**
 * 获取API密钥列表
 * @returns {Promise}
 */
export function getApiKeys() {
  return get('/settings/api-keys');
}

/**
 * 创建API密钥
 * @param {Object} data - API密钥数据
 * @returns {Promise}
 */
export function createApiKey(data) {
  return post('/settings/api-keys', data);
}

/**
 * 删除API密钥
 * @param {string} id - API密钥ID
 * @returns {Promise}
 */
export function deleteApiKey(id) {
  return post(`/settings/api-keys/${id}/delete`);
}

/**
 * 重置系统缓存
 * @param {Array} cacheTypes - 缓存类型数组
 * @returns {Promise}
 */
export function clearCache(cacheTypes) {
  return post('/settings/clear-cache', { types: cacheTypes });
}

/**
 * 获取系统资源配置
 * @returns {Promise}
 */
export function getResourceSettings() {
  return get('/settings/resources');
}

/**
 * 更新系统资源配置
 * @param {Object} data - 资源配置数据
 * @returns {Promise}
 */
export function updateResourceSettings(data) {
  return put('/settings/resources', data);
}

export default {
  getSettings,
  updateSettings,
  getSystemInfo,
  getServerStatus,
  createBackup,
  getBackupList,
  restoreBackup,
  downloadBackup,
  deleteBackup,
  uploadBackup,
  getEmailSettings,
  updateEmailSettings,
  sendTestEmail,
  getSecuritySettings,
  updateSecuritySettings,
  getApiKeys,
  createApiKey,
  deleteApiKey,
  clearCache,
  getResourceSettings,
  updateResourceSettings
}; 