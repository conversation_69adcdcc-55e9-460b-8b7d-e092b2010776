#!/usr/bin/env python3
"""
口型同步服务

为语音内容生成匹配的口型动画数据，支持多种模型和格式。
"""

import os
import logging
import json
import base64
import tempfile
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple, Union
import time
import numpy as np
from datetime import datetime
import uuid

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class LipSyncService:
    """口型同步服务类"""
    
    # 中文音素到视觉音素（viseme）的映射
    # 使用简化的视觉音素分类
    PHONEME_TO_VISEME = {
        # 元音
        'a': 0,  # 大开口 (like in "car")
        'ai': 0,
        'ao': 0,
        'e': 1,  # 中开口，嘴角扁平 (like in "bed")
        'ei': 1,
        'i': 2,  # 嘴角拉伸 (like in "see")
        'ie': 2,
        'o': 3,  # 圆唇 (like in "go")
        'ou': 3,
        'u': 4,  # 突出圆唇 (like in "too")
        'v': 4,
        
        # 辅音
        'b': 5,  # 双唇闭合 (bilabial)
        'p': 5,
        'm': 5,
        'f': 6,  # 唇齿 (labiodental)
        'w': 6,
        'd': 7,  # 舌尖抵上齿龈 (alveolar)
        't': 7,
        'n': 7,
        'l': 7,
        'g': 8,  # 舌根抵软腭 (velar)
        'k': 8,
        'h': 8,
        'j': 9,  # 舌面抵硬腭 (palatal)
        'q': 9,
        'x': 9,
        'zh': 10,  # 舌尖后音 (retroflex)
        'ch': 10,
        'sh': 10,
        'r': 10,
        'z': 11,  # 舌尖前音 (dental)
        'c': 11,
        's': 11
    }
    
    # 视觉音素到口型参数的映射
    VISEME_TO_BLEND_SHAPE = {
        0: {'mouth_open': 0.8, 'mouth_wide': 0.3},  # 元音a
        1: {'mouth_open': 0.5, 'mouth_wide': 0.5},  # 元音e
        2: {'mouth_open': 0.3, 'mouth_wide': 0.8},  # 元音i
        3: {'mouth_open': 0.5, 'mouth_narrow': 0.8},  # 元音o
        4: {'mouth_open': 0.3, 'mouth_narrow': 0.9, 'mouth_forward': 0.7},  # 元音u
        5: {'mouth_close': 0.9},  # 双唇音
        6: {'mouth_close': 0.5, 'lower_lip_in': 0.7},  # 唇齿音
        7: {'mouth_open': 0.4, 'tongue_up': 0.7},  # 舌尖音
        8: {'mouth_open': 0.4, 'tongue_back': 0.7},  # 舌根音
        9: {'mouth_open': 0.3, 'tongue_mid': 0.7},  # 舌面音
        10: {'mouth_open': 0.4, 'tongue_curl': 0.7},  # 舌尖后音
        11: {'mouth_open': 0.3, 'tongue_front': 0.7}  # 舌尖前音
    }
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化口型同步服务
        
        Args:
            config: 口型同步配置
        """
        self.config = config or {}
        self.model_type = self.config.get('model_type', 'rule_based')
        self.sample_rate = self.config.get('sample_rate', 24)  # 每秒采样数
        self.smoothing = self.config.get('smoothing', 0.5)  # 平滑系数
    
    def text_to_phonemes(self, text: str, language: str = 'zh-CN') -> List[str]:
        """
        将文本转换为音素序列
        
        Args:
            text: 中文文本
            language: 语言代码
        
        Returns:
            音素序列
        """
        # 实际使用时应该使用专门的音素转换库
        # 这里使用简化的规则进行模拟
        
        # 移除标点符号
        import re
        clean_text = re.sub(r'[^\w\s]', '', text)
        
        if language.startswith('zh'):
            # 简单的中文拼音音素映射示例
            # 真实场景下应使用完整的中文分词和拼音转换
            phoneme_map = {
                '啊': ['a'],
                '爱': ['ai'],
                '饿': ['e'],
                '一': ['i'],
                '哦': ['o'],
                '乌': ['u'],
                '吧': ['b', 'a'],
                '妈': ['m', 'a'],
                '爸': ['b', 'a'],
                '大': ['d', 'a'],
                '哥': ['g', 'e'],
                '好': ['h', 'ao'],
                '你': ['n', 'i'],
                '我': ['w', 'o'],
                '他': ['t', 'a'],
                '是': ['sh', 'i'],
                '在': ['z', 'ai'],
                '吃': ['ch', 'i'],
                '日': ['r', 'i'],
                '说': ['sh', 'uo'],
                '谢': ['x', 'ie']
            }
            
            # 将文本简单拆分为单字
            chars = list(clean_text)
            
            # 转换为音素
            phonemes = []
            for char in chars:
                if char in phoneme_map:
                    phonemes.extend(phoneme_map[char])
                else:
                    # 对于未知字符，使用默认音素
                    phonemes.append('a')
            
            return phonemes
        else:
            # 英文等其他语言音素转换
            # 此处仅为占位，实际使用需实现相应逻辑
            return list(clean_text.lower())
    
    def phonemes_to_visemes(self, phonemes: List[str]) -> List[int]:
        """
        将音素序列转换为视觉音素序列
        
        Args:
            phonemes: 音素序列
        
        Returns:
            视觉音素序列
        """
        visemes = []
        
        for phoneme in phonemes:
            viseme = self.PHONEME_TO_VISEME.get(phoneme.lower(), 0)  # 默认为0
            visemes.append(viseme)
        
        return visemes
    
    def estimate_phoneme_timing(self, phonemes: List[str], total_duration: float) -> List[Tuple[float, float]]:
        """
        估计每个音素的时间
        
        Args:
            phonemes: 音素序列
            total_duration: 总时长（秒）
        
        Returns:
            音素时间列表 [(开始时间, 持续时间), ...]
        """
        # 简单估计: 假设每个音素持续时间相同
        # 实际应用中应该使用更复杂的算法或从TTS引擎获取精确时间
        
        # 元音通常比辅音长
        vowels = ['a', 'e', 'i', 'o', 'u', 'ai', 'ei', 'ao', 'ou', 'ie']
        
        # 计算总权重
        total_weight = 0
        weights = []
        
        for phoneme in phonemes:
            if phoneme in vowels:
                weight = 1.5  # 元音权重
            else:
                weight = 1.0  # 辅音权重
            weights.append(weight)
            total_weight += weight
        
        # 计算每个音素的时间
        timings = []
        start_time = 0
        
        for i, phoneme in enumerate(phonemes):
            duration = (weights[i] / total_weight) * total_duration
            timings.append((start_time, duration))
            start_time += duration
        
        return timings
    
    def generate_lip_sync_data(
        self, 
        text: str = None, 
        audio_data: bytes = None, 
        language: str = 'zh-CN', 
        duration: float = None
    ) -> Dict[str, Any]:
        """
        生成口型同步数据
        
        Args:
            text: 文本内容，如果提供则基于文本生成
            audio_data: 音频数据，如果提供则基于音频生成（优先级高于文本）
            language: 语言代码
            duration: 音频持续时间，如果未提供则尝试从音频中获取
        
        Returns:
            口型同步数据
        """
        if audio_data is None and text is None:
            return {
                'success': False,
                'error': '必须提供文本或音频数据',
                'frames': []
            }
        
        try:
            # 如果提供了音频数据，应该使用音频分析来提高准确性
            # 此处仅实现基于文本的口型同步，实际应用需要实现音频分析
            
            if text:
                # 文本转音素
                phonemes = self.text_to_phonemes(text, language)
                
                # 音素转视觉音素
                visemes = self.phonemes_to_visemes(phonemes)
                
                # 如果未提供持续时间，估算一个（假设平均每秒4个音节）
                if duration is None:
                    # 估计每个汉字对应约0.25秒
                    char_count = len(text)
                    duration = char_count * 0.25
                
                # 估计音素时间
                timings = self.estimate_phoneme_timing(phonemes, duration)
                
                # 生成采样帧
                frames = self._generate_frames(visemes, timings, duration)
                
                return {
                    'success': True,
                    'frames': frames,
                    'visemes': [{'viseme': v, 'start': t[0], 'duration': t[1]} 
                                for v, t in zip(visemes, timings)],
                    'duration': duration,
                    'frame_count': len(frames),
                    'sample_rate': self.sample_rate
                }
            else:
                # 基于音频的分析（待实现）
                return {
                    'success': False,
                    'error': '基于音频的口型同步尚未实现',
                    'frames': []
                }
        except Exception as e:
            logger.error(f"生成口型同步数据失败: {e}")
            return {
                'success': False,
                'error': f'生成口型同步数据失败: {str(e)}',
                'frames': []
            }
    
    def _generate_frames(
        self, 
        visemes: List[int], 
        timings: List[Tuple[float, float]], 
        duration: float
    ) -> List[Dict[str, Any]]:
        """
        生成口型动画帧
        
        Args:
            visemes: 视觉音素序列
            timings: 音素时间列表 [(开始时间, 持续时间), ...]
            duration: 总时长
        
        Returns:
            口型动画帧列表
        """
        # 计算总帧数
        frame_count = int(duration * self.sample_rate)
        
        # 初始化空帧
        frames = []
        for i in range(frame_count):
            frames.append({
                'time': i / self.sample_rate,
                'blend_shapes': {
                    'mouth_open': 0.0,
                    'mouth_close': 0.0,
                    'mouth_wide': 0.0,
                    'mouth_narrow': 0.0,
                    'mouth_forward': 0.0,
                    'lower_lip_in': 0.0,
                    'tongue_up': 0.0,
                    'tongue_back': 0.0,
                    'tongue_mid': 0.0,
                    'tongue_curl': 0.0,
                    'tongue_front': 0.0
                }
            })
        
        # 填充帧数据
        for i, (viseme, (start_time, phoneme_duration)) in enumerate(zip(visemes, timings)):
            # 获取该视觉音素的混合形状
            blend_shape = self.VISEME_TO_BLEND_SHAPE.get(viseme, {})
            
            # 计算影响的帧范围
            start_frame = int(start_time * self.sample_rate)
            end_frame = int((start_time + phoneme_duration) * self.sample_rate)
            
            # 应用形状到帧
            for frame_idx in range(start_frame, min(end_frame, frame_count)):
                # 计算在音素中的相对位置（0到1）
                rel_pos = (frame_idx - start_frame) / max(1, end_frame - start_frame)
                
                # 形状强度随时间变化（简单的正弦曲线）
                # 开始和结束较弱，中间较强
                intensity = np.sin(rel_pos * np.pi)
                
                # 应用混合形状
                for shape_name, shape_value in blend_shape.items():
                    if shape_name in frames[frame_idx]['blend_shapes']:
                        # 应用平滑过渡
                        current = frames[frame_idx]['blend_shapes'][shape_name]
                        target = shape_value * intensity
                        frames[frame_idx]['blend_shapes'][shape_name] = current * (1 - self.smoothing) + target * self.smoothing
        
        # 对整个动画进行平滑处理
        smoothed_frames = self._smooth_frames(frames)
        
        return smoothed_frames
    
    def _smooth_frames(self, frames: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        平滑处理动画帧
        
        Args:
            frames: 原始动画帧
        
        Returns:
            平滑后的动画帧
        """
        smoothed_frames = frames.copy()
        
        # 使用简单的移动平均进行平滑
        window_size = 3  # 平滑窗口大小
        
        for i in range(len(frames)):
            # 计算窗口范围
            window_start = max(0, i - window_size // 2)
            window_end = min(len(frames), i + window_size // 2 + 1)
            
            # 对窗口内的每个形状值求平均
            for shape_name in frames[i]['blend_shapes']:
                values = [frames[j]['blend_shapes'][shape_name] for j in range(window_start, window_end)]
                smoothed_frames[i]['blend_shapes'][shape_name] = sum(values) / len(values)
        
        return smoothed_frames
    
    def lip_sync_to_animation_data(
        self, 
        lip_sync_data: Dict[str, Any], 
        format: str = 'json'
    ) -> Dict[str, Any]:
        """
        将口型同步数据转换为特定格式的动画数据
        
        Args:
            lip_sync_data: 口型同步数据
            format: 输出格式 (json, blendshape, etc.)
        
        Returns:
            特定格式的动画数据
        """
        if not lip_sync_data.get('success', False):
            return lip_sync_data
        
        frames = lip_sync_data.get('frames', [])
        
        if format == 'json':
            # 直接返回JSON格式
            return {
                'success': True,
                'format': 'json',
                'data': frames,
                'duration': lip_sync_data.get('duration', 0),
                'frame_count': len(frames)
            }
        elif format == 'blendshape':
            # 生成BlendShape格式（用于Unity等3D引擎）
            blendshape_data = {
                'frameRate': self.sample_rate,
                'curves': {}
            }
            
            # 为每个形状创建动画曲线
            if frames and 'blend_shapes' in frames[0]:
                for shape_name in frames[0]['blend_shapes']:
                    blendshape_data['curves'][shape_name] = {
                        'times': [],
                        'values': []
                    }
            
            # 提取每个形状的时间和值
            for frame in frames:
                time = frame['time']
                for shape_name, value in frame['blend_shapes'].items():
                    blendshape_data['curves'][shape_name]['times'].append(time)
                    blendshape_data['curves'][shape_name]['values'].append(value)
            
            return {
                'success': True,
                'format': 'blendshape',
                'data': blendshape_data,
                'duration': lip_sync_data.get('duration', 0)
            }
        else:
            return {
                'success': False,
                'error': f'不支持的动画格式: {format}',
                'data': None
            }

# 单例模式
_instance = None

def get_lip_sync_service(config: Dict[str, Any] = None) -> LipSyncService:
    """
    获取口型同步服务实例
    
    Args:
        config: 服务配置
    
    Returns:
        口型同步服务实例
    """
    global _instance
    if _instance is None:
        _instance = LipSyncService(config)
    return _instance 