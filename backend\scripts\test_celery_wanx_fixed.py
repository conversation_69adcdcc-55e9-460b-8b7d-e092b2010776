#!/usr/bin/env python3
"""
测试修复后的 Celery Wanx 任务
"""
import os
import sys
import time
from pathlib import Path
from dotenv import load_dotenv

# 添加后端路径
backend_dir = Path(__file__).parent.parent
sys.path.append(str(backend_dir))

# 加载环境变量
env_path = backend_dir / ".env"
load_dotenv(env_path)

def test_simple_tasks():
    """测试简单任务"""
    print("🧪 测试简单任务")
    print("=" * 50)
    
    try:
        from app.tasks.simple_test_task import simple_add, simple_hello
        
        # 测试同步执行
        print("📤 测试同步执行...")
        result1 = simple_add(10, 20)
        print(f"✅ simple_add(10, 20) = {result1}")
        
        # 测试异步执行
        print("\n📤 测试异步执行...")
        task = simple_add.delay(30, 40)
        print(f"📋 任务 ID: {task.id}")
        
        result = task.get(timeout=15)
        print(f"✅ 异步结果: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ 简单任务测试失败: {e}")
        return False

def test_wanx_fixed_tasks():
    """测试修复的 Wanx 任务"""
    print(f"\n🎬 测试修复的 Wanx 任务")
    print("=" * 50)
    
    try:
        from app.tasks.wanx_video_tasks_fixed import wanx_text_to_video_fixed, test_wanx_simple
        
        # 1. 测试简单 Wanx 任务
        print("📤 测试简单 Wanx 任务...")
        simple_task = test_wanx_simple.delay("Hello Fixed Wanx")
        print(f"📋 简单任务 ID: {simple_task.id}")
        
        simple_result = simple_task.get(timeout=10)
        print(f"✅ 简单任务结果: {simple_result}")
        
        # 2. 测试文本转视频任务
        print(f"\n📤 测试文本转视频任务...")
        video_task = wanx_text_to_video_fixed.delay(
            prompt="一个和尚敲木鱼",
            model="t2v-1.3B",
            duration=5,
            resolution="768x512",
            fps=24,
            user_id="test-user"
        )
        print(f"📋 视频任务 ID: {video_task.id}")
        print(f"📊 任务状态: {video_task.status}")
        
        # 等待任务完成
        print("⏳ 等待任务完成...")
        video_result = video_task.get(timeout=60)  # 给足够的时间
        print(f"✅ 视频任务结果: {video_result}")
        
        return True
        
    except Exception as e:
        print(f"❌ Wanx 任务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_task_manager():
    """测试任务管理器"""
    print(f"\n📊 测试任务管理器")
    print("=" * 50)
    
    try:
        from app.core.task_manager import task_manager
        
        # 创建测试任务
        task_id = task_manager.create_task(
            task_type='test',
            task_subtype='celery_fix_test',
            user_id='test_user',
            title='Celery 修复测试',
            description='测试 Celery 修复后的功能',
            input_params={'test': True},
            estimated_duration=60
        )
        
        if task_id:
            print(f"✅ 创建任务成功: {task_id}")
            
            # 获取任务信息
            task_info = task_manager.get_task(task_id)
            if task_info:
                print(f"✅ 获取任务信息: {task_info.get('status')}")
            
            return True
        else:
            print("❌ 创建任务失败")
            return False
            
    except Exception as e:
        print(f"❌ 任务管理器测试失败: {e}")
        return False

def check_celery_worker():
    """检查 Celery Worker 状态"""
    print(f"\n👷 检查 Celery Worker 状态")
    print("=" * 50)
    
    try:
        from app.core.celery_app import celery_app
        
        inspect = celery_app.control.inspect()
        
        # 检查活跃 worker
        active_workers = inspect.active()
        if active_workers:
            print(f"✅ 发现 {len(active_workers)} 个活跃 worker:")
            for worker_name, tasks in active_workers.items():
                print(f"   📍 {worker_name}: {len(tasks)} 个活跃任务")
        else:
            print("❌ 没有发现活跃的 worker")
            print("💡 请启动 Celery Worker:")
            print("   celery -A app.core.celery_app worker --loglevel=info --pool=solo")
        
        # 检查已注册任务
        registered = inspect.registered()
        if registered:
            print(f"\n📋 已注册任务:")
            for worker_name, tasks in registered.items():
                print(f"   📍 {worker_name}: {len(tasks)} 个任务")
                # 显示修复的任务
                fixed_tasks = [t for t in tasks if 'fixed' in t or 'wanx' in t]
                if fixed_tasks:
                    print(f"      修复的任务: {fixed_tasks}")
        
        return bool(active_workers)
        
    except Exception as e:
        print(f"❌ Worker 检查失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 Celery Wanx 修复测试")
    print("=" * 60)
    
    results = {}
    
    # 1. 检查 Worker
    results['worker'] = check_celery_worker()
    
    if not results['worker']:
        print(f"\n❌ 没有活跃的 Worker，无法进行任务测试")
        print(f"💡 请先启动 Celery Worker:")
        print(f"   cd backend")
        print(f"   celery -A app.core.celery_app worker --loglevel=info --pool=solo")
        return False
    
    # 2. 测试简单任务
    results['simple'] = test_simple_tasks()
    
    # 3. 测试任务管理器
    results['task_manager'] = test_task_manager()
    
    # 4. 测试修复的 Wanx 任务
    results['wanx_fixed'] = test_wanx_fixed_tasks()
    
    # 总结
    print(f"\n📊 测试结果总结:")
    print("=" * 60)
    
    for component, status in results.items():
        status_icon = "✅" if status else "❌"
        status_text = "通过" if status else "失败"
        print(f"{status_icon} {component.upper()}: {status_text}")
    
    # 总体状态
    all_passed = all(results.values())
    if all_passed:
        print(f"\n🎉 所有测试通过！Celery 修复成功！")
        print(f"💡 现在可以正常使用视频生成功能了")
    else:
        failed_tests = [k for k, v in results.items() if not v]
        print(f"\n⚠️ 以下测试失败: {', '.join(failed_tests)}")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ Celery 系统修复成功！")
    else:
        print("\n❌ Celery 系统仍有问题")
    
    input("按 Enter 键退出...")
