"""
视频生成相关的Celery任务
"""
import time
import os
import asyncio
import logging
import traceback
from typing import Optional, Dict, Any
from datetime import datetime
from contextlib import contextmanager

from celery import shared_task
from celery.signals import task_prerun, task_postrun, task_failure

# 导入服务和工具
from services.celery_app import app
from services.wan_video_service import get_wan_video_service
from services.progress_tracker import get_progress_tracker
from services.progress_updater import ProgressUpdater
from services.minio_storage import save_video_to_storage, get_video_url
from models.video import VideoGeneration, VideoGenerationCreate
from utils.db import get_db

# 配置日志
logger = logging.getLogger(__name__)

# 定义数据库会话上下文管理器
@contextmanager
def get_db_session():
    """获取数据库会话的上下文管理器，确保会话始终被正确关闭"""
    session_generator = get_db()
    db_session = None
    try:
        db_session = next(session_generator)
        yield db_session
    except Exception as e:
        logger.error(f"数据库会话操作出错: {e}")
        raise
    finally:
        if db_session:
            try:
                db_session.close()
                logger.debug("数据库会话已正确关闭")
            except Exception as e:
                logger.error(f"关闭数据库会话时出错: {e}")

# 任务处理钩子
@task_prerun.connect
def task_prerun_handler(task_id=None, task=None, *args, **kwargs):
    """任务开始前的处理"""
    logger.info(f"开始执行任务: {task.name}[{task_id}]")

@task_postrun.connect
def task_postrun_handler(task_id=None, task=None, retval=None, state=None, *args, **kwargs):
    """任务结束后的处理"""
    logger.info(f"完成任务: {task.name}[{task_id}], 状态: {state}")

@task_failure.connect
def task_failure_handler(task_id=None, exception=None, traceback=None, *args, **kwargs):
    """任务失败处理"""
    logger.error(f"任务失败: [{task_id}], 错误: {exception}")
    # 可以在这里添加额外的失败处理逻辑，如通知或重试策略

@shared_task(name="generate_text_to_video", bind=True, max_retries=2, default_retry_delay=300)
def generate_text_to_video_task(self, 
                           task_id: str,
                           prompt: str,
                           negative_prompt: str = "",
                           num_frames: int = 81,
                           height: int = 480,
                           width: int = 832,
                           guidance_scale: float = 5.0,
                           fps: int = 16,
                           seed: Optional[int] = None,
                           model_size: str = "1.3B",
                           resolution: str = "480p",
                           user_id: int = None) -> Dict[str, Any]:
    """
    文本到视频生成任务
    
    参数:
        self: Celery任务实例
        task_id: 任务ID
        prompt: 提示文本
        negative_prompt: 负面提示
        num_frames: 帧数
        height: 高度
        width: 宽度
        guidance_scale: 指导比例
        fps: 每秒帧数
        seed: 随机种子
        model_size: 模型大小
        resolution: 分辨率
        user_id: 用户ID
        
    返回:
        包含任务结果的字典
    """
    start_time = time.time()
    success = False
    generation_time = 0
    error_message = None
    output_path = None
    progress_tracker = get_progress_tracker()
    progress_updater = ProgressUpdater(task_id)
    
    try:
        # 创建异步事件循环
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        # 更新初始状态
        loop.run_until_complete(progress_updater.update(
            status="initialization",
            progress=5,
            logs=["正在初始化..."]
        ))
        
        # 获取视频生成服务
        video_service = get_wan_video_service()
        logger.info(f"开始处理视频生成任务: {task_id}, 模型: {model_size}, 分辨率: {resolution}")
        
        # 更新任务状态
        loop.run_until_complete(progress_updater.update(
            status="processing",
            progress=10,
            logs=["开始生成视频..."]
        ))
        
        # 直接调用generate_video_from_text方法
        result = loop.run_until_complete(video_service.generate_video_from_text(
            task_id=task_id,
            prompt=prompt,
            negative_prompt=negative_prompt,
            num_frames=num_frames,
            height=height,
            width=width,
            guidance_scale=guidance_scale,
            fps=fps,
            seed=seed,
            model_size=model_size,
            resolution=resolution,
            progress_updater=progress_updater,
            force_real_mode=True
        ))
        
        # 检查生成结果
        if result.get("success", False):
            # 生成成功
            success = True
            generation_time = time.time() - start_time
            logger.info(f"视频生成成功: {task_id}, 用时: {generation_time:.2f}秒")
            
            # 获取生成的视频URL
            video_url = result.get("video_url", "")
            thumbnail_url = result.get("thumbnail_url", "")
            
            # 更新数据库中的任务状态
            try:
                with get_db_session() as db:
                    video_task = db.query(VideoGeneration).filter(VideoGeneration.task_id == task_id).first()
                    if video_task:
                        video_task.status = "completed"
                        video_task.video_url = video_url
                        video_task.thumbnail_url = thumbnail_url
                        video_task.completed_at = datetime.now()
                        video_task.generation_time = generation_time
                        db.commit()
                        logger.info(f"任务状态已更新为completed: {task_id}")
                    else:
                        logger.warning(f"未找到任务记录: {task_id}")
            except Exception as e:
                logger.error(f"更新数据库任务状态失败: {e}")
            
            # 最终更新任务状态
            loop.run_until_complete(progress_updater.update(
                status="completed",
                progress=100,
                logs=["视频生成完成"],
                result={
                    "video_url": video_url,
                    "thumbnail_url": thumbnail_url,
                    "generation_time": generation_time
                }
            ))
            
            # 记录性能指标
            record_performance_metrics("text_to_video", success=True, generation_time=generation_time)
            
            return {
                "success": True,
                "task_id": task_id,
                "video_url": video_url,
                "thumbnail_url": thumbnail_url,
                "generation_time": generation_time
            }
        else:
            # 处理失败情况
            error_message = result.get("error", "未知错误")
            logger.error(f"视频生成失败: {task_id}, 错误: {error_message}")
            
            # 更新数据库中的任务状态
            try:
                with get_db_session() as db:
                    video_task = db.query(VideoGeneration).filter(VideoGeneration.task_id == task_id).first()
                    if video_task:
                        video_task.status = "failed"
                        video_task.error = error_message
                        video_task.completed_at = datetime.now()
                        video_task.generation_time = generation_time
                        db.commit()
                        logger.info(f"任务状态已更新为failed: {task_id}")
                    else:
                        logger.warning(f"未找到任务记录: {task_id}")
            except Exception as e:
                logger.error(f"更新数据库任务状态失败: {e}")
            
            # 更新任务状态
            loop.run_until_complete(progress_updater.update(
                status="failed",
                progress=0,
                error=error_message
            ))
            
            # 记录性能指标
            record_performance_metrics("text_to_video", success=False, error=error_message)
            
            return {
                "success": False,
                "task_id": task_id,
                "error": error_message
            }
    except Exception as e:
        # 捕获所有异常
        error_message = str(e)
        logger.error(f"处理视频生成任务时发生异常: {task_id}, 错误: {error_message}")
        logger.error(traceback.format_exc())
        
        # 更新数据库中的任务状态
        try:
            with get_db_session() as db:
                video_task = db.query(VideoGeneration).filter(VideoGeneration.task_id == task_id).first()
                if video_task:
                    video_task.status = "failed"
                    video_task.error = error_message
                    video_task.completed_at = datetime.now()
                    video_task.generation_time = time.time() - start_time
                    db.commit()
                    logger.info(f"任务状态已更新为failed: {task_id}")
                else:
                    logger.warning(f"未找到任务记录: {task_id}")
        except Exception as db_error:
            logger.error(f"更新数据库任务状态失败: {db_error}")
        
        # 更新任务状态
        try:
            loop.run_until_complete(progress_updater.update(
                status="failed",
                progress=0,
                error=error_message
            ))
        except Exception as update_error:
            logger.error(f"更新进度失败: {update_error}")
        
        # 记录性能指标
        record_performance_metrics("text_to_video", success=False, error=error_message)
        
        return {
            "success": False,
            "task_id": task_id,
            "error": error_message
        }

@shared_task(name="generate_image_to_video", bind=True, max_retries=2, default_retry_delay=300)
def generate_image_to_video_task(self,
                          task_id: str,
                          image_path: str,
                          prompt: str = "",
                          negative_prompt: str = "",
                          num_frames: int = 81,
                          guidance_scale: float = 5.0,
                          fps: int = 16,
                          seed: Optional[int] = None,
                          resolution: str = "480p",
                          user_id: int = None) -> Dict[str, Any]:
    """
    图像到视频生成任务
    
    参数:
        self: Celery任务实例
        task_id: 任务ID
        image_path: 输入图像路径
        prompt: 提示文本
        negative_prompt: 负面提示
        num_frames: 帧数
        guidance_scale: 指导比例
        fps: 每秒帧数
        seed: 随机种子
        resolution: 分辨率
        user_id: 用户ID
        
    返回:
        包含任务结果的字典
    """
    start_time = time.time()
    success = False
    generation_time = 0
    error_message = None
    output_path = None
    progress_tracker = get_progress_tracker()
    progress_updater = ProgressUpdater(task_id)
    
    try:
        # 创建异步事件循环
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        # 更新初始状态
        loop.run_until_complete(progress_updater.update(
            status="initialization",
            progress=5,
            logs=["正在初始化..."]
        ))
        
        # 获取视频生成服务
        video_service = get_wan_video_service()
        logger.info(f"开始处理图像到视频任务: {task_id}, 分辨率: {resolution}")
        
        # 检查图像文件是否存在
        if not os.path.exists(image_path):
            error_message = f"输入图像文件不存在: {image_path}"
            logger.error(error_message)
            loop.run_until_complete(progress_updater.update(
                status="failed",
                progress=0,
                error=error_message,
                logs=["输入图像不存在"]
            ))
            
            # 更新数据库中的任务状态
            with get_db_session() as db:
                video_task = db.query(VideoGeneration).filter(VideoGeneration.task_id == task_id).first()
                if video_task:
                    video_task.status = "failed"
                    video_task.error = error_message
                    video_task.completed_at = datetime.now()
                    db.commit()
                    
            return {
                "success": False,
                "error": error_message,
                "task_id": task_id
            }
        
        # 更新任务状态
        loop.run_until_complete(progress_updater.update(
            status="processing",
            progress=10,
            logs=["开始从图像生成视频..."]
        ))
        
        # 直接调用generate_video_from_image方法
        result = loop.run_until_complete(video_service.generate_video_from_image(
            task_id=task_id,
            image_path=image_path,
            prompt=prompt,
            negative_prompt=negative_prompt,
            num_frames=num_frames,
            guidance_scale=guidance_scale,
            fps=fps,
            seed=seed,
            resolution=resolution,
            progress_updater=progress_updater
        ))
        
        # 检查生成结果
        if result.get("success", False):
            # 生成成功
            success = True
            generation_time = time.time() - start_time
            logger.info(f"图像到视频生成成功: {task_id}, 用时: {generation_time:.2f}秒")
            
            # 获取生成的视频URL
            video_url = result.get("video_url", "")
            thumbnail_url = result.get("thumbnail_url", "")
            
            # 更新数据库中的任务状态
            try:
                with get_db_session() as db:
                    video_task = db.query(VideoGeneration).filter(VideoGeneration.task_id == task_id).first()
                    if video_task:
                        video_task.status = "completed"
                        video_task.video_url = video_url
                        video_task.thumbnail_url = thumbnail_url
                        video_task.completed_at = datetime.now()
                        video_task.generation_time = generation_time
                        db.commit()
                        logger.info(f"任务状态已更新为completed: {task_id}")
                    else:
                        logger.warning(f"未找到任务记录: {task_id}")
            except Exception as e:
                logger.error(f"更新数据库任务状态失败: {e}")
                
            # 最终更新任务状态
            loop.run_until_complete(progress_updater.update(
                status="completed",
                progress=100,
                logs=["视频生成完成"],
                result={
                    "video_url": video_url,
                    "thumbnail_url": thumbnail_url,
                    "generation_time": generation_time
                }
            ))
            
            # 记录性能指标
            record_performance_metrics("image_to_video", success=True, generation_time=generation_time)
            
            return {
                "success": True,
                "task_id": task_id,
                "video_url": video_url,
                "thumbnail_url": thumbnail_url,
                "generation_time": generation_time
            }
        else:
            # 处理失败情况
            error_message = result.get("error", "未知错误")
            logger.error(f"图像到视频生成失败: {task_id}, 错误: {error_message}")
            
            # 更新数据库中的任务状态
            try:
                with get_db_session() as db:
                    video_task = db.query(VideoGeneration).filter(VideoGeneration.task_id == task_id).first()
                    if video_task:
                        video_task.status = "failed"
                        video_task.error = error_message
                        video_task.completed_at = datetime.now()
                        video_task.generation_time = generation_time
                        db.commit()
                        logger.info(f"任务状态已更新为failed: {task_id}")
                    else:
                        logger.warning(f"未找到任务记录: {task_id}")
            except Exception as e:
                logger.error(f"更新数据库任务状态失败: {e}")
                
            # 更新任务状态
            loop.run_until_complete(progress_updater.update(
                status="failed",
                progress=0,
                error=error_message
            ))
            
            # 记录性能指标
            record_performance_metrics("image_to_video", success=False, error=error_message)
            
            return {
                "success": False,
                "task_id": task_id,
                "error": error_message
            }
    except Exception as e:
        # 捕获所有异常
        error_message = str(e)
        logger.error(f"处理图像到视频任务时发生异常: {task_id}, 错误: {error_message}")
        logger.error(traceback.format_exc())
        
        # 更新数据库中的任务状态
        try:
            with get_db_session() as db:
                video_task = db.query(VideoGeneration).filter(VideoGeneration.task_id == task_id).first()
                if video_task:
                    video_task.status = "failed"
                    video_task.error = error_message
                    video_task.completed_at = datetime.now()
                    video_task.generation_time = time.time() - start_time
                    db.commit()
                    logger.info(f"任务状态已更新为failed: {task_id}")
                else:
                    logger.warning(f"未找到任务记录: {task_id}")
        except Exception as db_error:
            logger.error(f"更新数据库任务状态失败: {db_error}")
            
        # 更新任务状态
        try:
            loop.run_until_complete(progress_updater.update(
                status="failed",
                progress=0,
                error=error_message
            ))
        except Exception as update_error:
            logger.error(f"更新进度失败: {update_error}")
        
        # 记录性能指标
        record_performance_metrics("image_to_video", success=False, error=error_message)
        
        return {
            "success": False,
            "task_id": task_id,
            "error": error_message
        }
    finally:
        # 清理生成的临时文件
        if output_path and os.path.exists(output_path):
            try:
                os.remove(output_path)
                logger.debug(f"已删除临时视频文件: {output_path}")
            except Exception as e:
                logger.warning(f"删除临时视频文件时出错: {e}")
        
        # 清理输入图像（如果是临时文件）
        if image_path and os.path.exists(image_path) and "temp" in image_path:
            try:
                os.remove(image_path)
                logger.debug(f"已删除临时输入图像: {image_path}")
            except Exception as e:
                logger.warning(f"删除临时输入图像时出错: {e}")

def record_performance_metrics(task_type, success, generation_time=0, error=None):
    """记录性能指标"""
    try:
        # 尝试导入性能指标模块
        from api.wan_video_api import performance_metrics
        
        # 记录任务性能
        performance_metrics.record_task(
            task_type=task_type,
            success=success,
            generation_time=generation_time,
            error=error
        )
    except Exception as e:
        logger.warning(f"无法导入性能指标模块，性能数据未记录")

# 启动Celery工作进程的命令
# celery -A backend.services.celery_tasks.video_tasks worker --loglevel=info -Q video_queue 