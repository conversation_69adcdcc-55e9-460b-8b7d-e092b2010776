import os
import tempfile
from typing import Optional, Union, Dict
import logging
import uuid
import asyncio
from concurrent.futures import ThreadPoolExecutor
import wave
import numpy as np
import soundfile as sf
from gtts import gTTS
import subprocess
import shutil
from pydub import AudioSegment

logger = logging.getLogger(__name__)

class TTSService:
    """文本到语音服务，使用Google TTS"""
    
    def __init__(self):
        """初始化TTS服务"""
        self.executor = ThreadPoolExecutor(max_workers=2)
        logger.info("TTS服务初始化完成")
        
        # 确保临时目录存在
        os.makedirs("temp", exist_ok=True)
        os.makedirs("uploads", exist_ok=True)
    
    def text_to_speech(self, text: str, language: str, output_path: Optional[str] = None) -> str:
        """
        将文本转换为语音
        
        Args:
            text: 要转换的文本
            language: 语言代码 (如 'zh', 'en')
            output_path: 可选的输出文件路径
            
        Returns:
            生成的音频文件路径
        """
        if not text or text.strip() == "":
            logger.warning("收到空文本，将生成静音文件")
            if not output_path:
                output_file = tempfile.NamedTemporaryFile(suffix=".wav", delete=False)
                output_path = output_file.name
                output_file.close()
            self._create_empty_wav(output_path)
            return output_path
            
        if not output_path:
            # 创建临时文件作为输出
            output_file = tempfile.NamedTemporaryFile(suffix=".wav", delete=False)
            output_path = output_file.name
            output_file.close()
        
        try:
            # 转换为gTTS支持的语言代码
            tts_lang = self._map_language_code(language)
            
            # 为长文本分段处理
            if len(text) > 5000:
                logger.info(f"文本过长 ({len(text)} 字符)，将分段处理")
                return self._process_long_text(text, tts_lang, output_path)
            
            # 使用gTTS生成语音
            logger.info(f"使用gTTS生成语音，语言: {tts_lang}, 文本长度: {len(text)}")
            
            # 临时MP3文件
            temp_mp3 = os.path.join("temp", f"{uuid.uuid4()}.mp3")
            
            # 使用gTTS将文本转换为MP3
            tts = gTTS(text=text, lang=tts_lang, slow=False)
            tts.save(temp_mp3)
            logger.info(f"语音生成成功: {temp_mp3}")
            
            # 将MP3转换为WAV (如果输出是WAV)
            if output_path.lower().endswith('.wav'):
                self._convert_to_wav(temp_mp3, output_path)
                # 删除临时MP3
                if os.path.exists(temp_mp3):
                    os.remove(temp_mp3)
            else:
                # 如果输出不是WAV，直接复制MP3到输出路径
                shutil.copy2(temp_mp3, output_path)
                # 删除临时MP3
                if os.path.exists(temp_mp3):
                    os.remove(temp_mp3)
            
            logger.info(f"语音文件已保存到: {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"生成语音时出错: {str(e)}")
            # 尝试使用备用方法
            try:
                logger.info("尝试使用备用方法生成语音")
                # 检查是否有样本音频
                sample_path = "sample_audio.mp3"
                if os.path.exists(sample_path):
                    # 复制示例文件
                    shutil.copy2(sample_path, output_path)
                    logger.info(f"已使用样本音频: {output_path}")
                    return output_path
                else:
                    # 生成空的WAV文件
                    self._create_empty_wav(output_path)
                    logger.info(f"生成空音频文件: {output_path}")
                    return output_path
            except Exception as backup_error:
                logger.error(f"备用方法也失败: {str(backup_error)}")
                # 清理临时文件
                if os.path.exists(output_path):
                    try:
                        os.remove(output_path)
                    except:
                        pass
                raise
    
    def _process_long_text(self, text: str, lang: str, output_path: str) -> str:
        """处理长文本，将其分成多个片段并合并生成的音频"""
        # 分割文本
        max_length = 5000
        segments = []
        
        # 按句子分割
        sentences = text.replace('。', '.').replace('！', '!').replace('？', '?').split('.')
        current_segment = ""
        
        for sentence in sentences:
            if not sentence.strip():
                continue
                
            # 如果当前段加上这个句子不超过最大长度，则添加到当前段
            if len(current_segment) + len(sentence) < max_length:
                current_segment += sentence + "."
            else:
                # 否则保存当前段并开始新段
                if current_segment:
                    segments.append(current_segment)
                current_segment = sentence + "."
        
        # 添加最后一段
        if current_segment:
            segments.append(current_segment)
        
        # 为每个段落生成音频
        temp_files = []
        for i, segment in enumerate(segments):
            temp_file = os.path.join("temp", f"segment_{i}_{uuid.uuid4()}.mp3")
            try:
                tts = gTTS(text=segment, lang=lang, slow=False)
                tts.save(temp_file)
                temp_files.append(temp_file)
                logger.info(f"生成段落 {i+1}/{len(segments)}")
            except Exception as e:
                logger.error(f"生成段落 {i+1} 时出错: {str(e)}")
        
        # 合并音频文件
        if temp_files:
            # 转换和合并
            combined_output = os.path.join("temp", f"combined_{uuid.uuid4()}.mp3")
            self._combine_audio_files(temp_files, combined_output)
            
            # 转换为目标格式
            if output_path.lower().endswith('.wav'):
                self._convert_to_wav(combined_output, output_path)
            else:
                shutil.copy2(combined_output, output_path)
                
            # 清理临时文件
            for file in temp_files + [combined_output]:
                if os.path.exists(file):
                    try:
                        os.remove(file)
                    except:
                        pass
                        
            return output_path
        else:
            # 如果没有生成任何段落，则创建空文件
            self._create_empty_wav(output_path)
            return output_path
    
    def _create_empty_wav(self, filepath, duration=3.0, sample_rate=16000):
        """创建空的WAV文件"""
        # 生成静音数据
        num_samples = int(duration * sample_rate)
        audio_data = np.zeros(num_samples, dtype=np.int16)
        
        # 写入WAV文件
        with wave.open(filepath, 'w') as wav_file:
            wav_file.setnchannels(1)  # 单声道
            wav_file.setsampwidth(2)  # 16位
            wav_file.setframerate(sample_rate)
            wav_file.writeframes(audio_data.tobytes())
    
    def _map_language_code(self, lang_code: str) -> str:
        """将ISO 639-1语言代码映射到gTTS支持的语言代码"""
        # gTTS支持的语言: https://gtts.readthedocs.io/en/latest/module.html#languages-gtts-lang
        mapping = {
            'zh': 'zh-cn',  # 中文(简体)
            'zh-cn': 'zh-cn',  # 中文(简体)
            'zh-tw': 'zh-tw',  # 中文(繁体)
            'en': 'en',  # 英语
            'ja': 'ja',  # 日语
            'ko': 'ko',  # 韩语
            'fr': 'fr',  # 法语
            'de': 'de',  # 德语
            'es': 'es',  # 西班牙语
            'ru': 'ru',  # 俄语
            'ar': 'ar',  # 阿拉伯语
            'pt': 'pt',  # 葡萄牙语
            'it': 'it',  # 意大利语
        }
        return mapping.get(lang_code.lower(), 'en')  # 默认使用英语
    
    def generate_speech(self, text: str, output_path: Optional[str] = None, voice_id: str = None, speed: float = 1.0) -> str:
        """
        将文本转换为语音的公共接口，与其他TTS服务保持一致
        
        Args:
            text (str): 要转换的文本
            output_path (str, optional): 保存音频的路径
            voice_id (str, optional): 语音ID
            speed (float, optional): 语速
            
        Returns:
            str: 生成的音频文件路径
        """
        logger.info(f"调用generate_speech方法，文本: '{text[:30]}...'")
        
        # 确保输出路径有效
        if not output_path or output_path == "":
            # 生成临时文件路径
            import tempfile
            import uuid
            temp_dir = tempfile.gettempdir()
            output_path = os.path.join(temp_dir, f"tts_{uuid.uuid4()}.mp3")
            logger.info(f"输出路径为空，自动生成临时文件路径: {output_path}")
        
        # 根据voice_id推断language参数
        language = "zh"  # 默认中文
        if voice_id:
            if voice_id.startswith("en"):
                language = "en"
            elif voice_id.startswith("ja"):
                language = "ja"
            # 可以添加更多语言的判断
            
        # 调用内部实现
        try:
            return self.text_to_speech(text, language, output_path)
        except Exception as e:
            logger.error(f"语音合成失败: {e}")
            return None
    
    def _convert_to_wav(self, mp3_path: str, wav_path: str):
        """将MP3转换为WAV格式"""
        try:
            # 首先尝试使用pydub
            try:
                audio = AudioSegment.from_mp3(mp3_path)
                audio.export(wav_path, format="wav")
                logger.info(f"使用pydub转换MP3到WAV成功: {wav_path}")
                return
            except Exception as pydub_error:
                logger.warning(f"使用pydub转换失败: {str(pydub_error)}")
            
            # 尝试使用ffmpeg
            try:
                command = [
                    "ffmpeg", "-i", mp3_path, 
                    "-acodec", "pcm_s16le",
                    "-ar", "16000",
                    "-ac", "1",
                    "-y", wav_path
                ]
                subprocess.run(command, check=True, capture_output=True)
                logger.info(f"使用ffmpeg转换MP3到WAV成功: {wav_path}")
                return
            except Exception as ffmpeg_error:
                logger.warning(f"使用ffmpeg转换失败: {str(ffmpeg_error)}")
                
            # 两种方法都失败时，直接复制文件并记录警告
            shutil.copy2(mp3_path, wav_path)
            logger.warning(f"转换失败，直接复制文件: {wav_path}")
        except Exception as e:
            logger.error(f"转换音频格式时出错: {str(e)}")
            raise
    
    def _combine_audio_files(self, input_files: list, output_file: str):
        """合并多个音频文件"""
        try:
            # 使用pydub合并
            combined = AudioSegment.empty()
            for file in input_files:
                sound = AudioSegment.from_file(file)
                combined += sound
            combined.export(output_file, format="mp3")
            logger.info(f"合并{len(input_files)}个音频文件成功: {output_file}")
        except Exception as e:
            logger.error(f"合并音频文件失败: {str(e)}")
            # 如果合并失败，复制第一个文件作为输出
            if input_files and os.path.exists(input_files[0]):
                shutil.copy2(input_files[0], output_file)
                logger.warning(f"合并失败，使用第一个文件作为输出: {output_file}")
            else:
                raise
    
    async def text_to_speech_async(self, text: str, language: str, output_path: Optional[str] = None) -> str:
        """异步将文本转换为语音"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            self.executor, 
            self.text_to_speech, 
            text, 
            language, 
            output_path
        )
    
    def merge_audio_files(self, original_path: str, translated_path: str, output_path: Optional[str] = None) -> str:
        """
        合并原始音频和翻译后的音频
        
        Args:
            original_path: 原始音频文件路径
            translated_path: 翻译后的音频文件路径
            output_path: 可选的输出文件路径
            
        Returns:
            合并后的音频文件路径
        """
        if not output_path:
            # 创建临时文件作为输出
            output_file = tempfile.NamedTemporaryFile(suffix=".wav", delete=False)
            output_path = output_file.name
            output_file.close()
        
        try:
            # 检查文件是否存在
            if not os.path.exists(original_path):
                logger.error(f"原始音频文件不存在: {original_path}")
                raise FileNotFoundError(f"原始音频文件不存在: {original_path}")
                
            if not os.path.exists(translated_path):
                logger.error(f"翻译音频文件不存在: {translated_path}")
                raise FileNotFoundError(f"翻译音频文件不存在: {translated_path}")
            
            # 合并音频
            try:
                # 使用pydub
                sound1 = AudioSegment.from_file(original_path)
                sound2 = AudioSegment.from_file(translated_path)
                
                # 添加1秒静音
                silence = AudioSegment.silent(duration=1000)
                
                # 合并: 原始音频 + 1秒静音 + 翻译音频
                combined = sound1 + silence + sound2
                
                # 导出
                file_format = output_path.split('.')[-1].lower()
                if file_format not in ['mp3', 'wav', 'ogg']:
                    file_format = 'mp3'
                    
                combined.export(output_path, format=file_format)
                logger.info(f"成功合并音频文件: {output_path}")
            except Exception as pydub_error:
                logger.warning(f"使用pydub合并失败: {str(pydub_error)}")
                
                # 尝试使用ffmpeg
                try:
                    # 创建临时文件列表
                    with tempfile.NamedTemporaryFile('w', suffix='.txt', delete=False) as f:
                        list_file = f.name
                        f.write(f"file '{os.path.abspath(original_path)}'\n")
                        f.write(f"file '{os.path.abspath(translated_path)}'\n")
                    
                    # 使用ffmpeg合并
                    command = [
                        "ffmpeg", "-f", "concat", "-safe", "0",
                        "-i", list_file, "-c", "copy", "-y", output_path
                    ]
                    subprocess.run(command, check=True, capture_output=True)
                    
                    # 删除临时文件
                    os.remove(list_file)
                    
                    logger.info(f"使用ffmpeg合并音频成功: {output_path}")
                except Exception as ffmpeg_error:
                    logger.warning(f"使用ffmpeg合并失败: {str(ffmpeg_error)}")
                    # 如果都失败，简单复制第一个文件
                    shutil.copy2(original_path, output_path)
                    logger.warning(f"合并失败，复制原始音频: {output_path}")
            
            return output_path
        except Exception as e:
            logger.error(f"合并音频时出错: {str(e)}")
            # 清理临时文件
            if os.path.exists(output_path):
                try:
                    os.remove(output_path)
                except:
                    pass
            # 作为后备，返回翻译的音频
            if os.path.exists(translated_path):
                output_backup = f"{output_path}.backup"
                shutil.copy2(translated_path, output_backup)
                return output_backup
            raise 