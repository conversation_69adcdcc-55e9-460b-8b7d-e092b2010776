#!/usr/bin/env python3
"""
调试聊天 API 调用流程
"""
import os
import sys
import asyncio
import httpx
from pathlib import Path
from dotenv import load_dotenv

# 添加后端路径
backend_dir = Path(__file__).parent.parent
sys.path.append(str(backend_dir))

# 加载环境变量
env_path = backend_dir / ".env"
load_dotenv(env_path)

async def debug_chat_flow():
    """调试完整的聊天流程"""
    print("🔍 调试聊天 API 调用流程")
    print("=" * 60)
    
    agent_id = "e55f5e84-6d8b-4265-8e55-728bdb0d2455"
    test_message = "policewoman"
    
    try:
        # 1. 直接测试 AI 智能体服务
        print("1️⃣ 测试 AI 智能体服务")
        print("-" * 30)
        
        from app.services.ai_agent_service import AiAgentService
        
        ai_service = AiAgentService()
        mock_agent = {
            "id": agent_id,
            "name": "语言学习助手",
            "agent_type": "language_tutor"
        }
        
        print(f"📤 直接调用 AI 服务，消息: {test_message}")
        start_time = asyncio.get_event_loop().time()
        
        ai_response = await ai_service.chat_with_agent(mock_agent, test_message)
        
        end_time = asyncio.get_event_loop().time()
        duration = (end_time - start_time) * 1000
        
        print(f"✅ AI 服务响应时间: {duration:.0f}ms")
        print(f"📝 AI 服务回复: {ai_response}")
        print(f"📊 回复长度: {len(ai_response)} 字符")
        
        # 2. 测试简单智能体服务
        print(f"\n2️⃣ 测试简单智能体服务")
        print("-" * 30)
        
        from app.services.simple_agent_service import SimpleAgentService
        
        simple_service = SimpleAgentService()
        
        print(f"📤 调用简单服务，消息: {test_message}")
        start_time = asyncio.get_event_loop().time()
        
        simple_response = await simple_service.chat_with_agent(mock_agent, test_message)
        
        end_time = asyncio.get_event_loop().time()
        duration = (end_time - start_time) * 1000
        
        print(f"✅ 简单服务响应时间: {duration:.0f}ms")
        print(f"📝 简单服务回复: {simple_response}")
        print(f"📊 回复长度: {len(simple_response)} 字符")
        
        # 3. 测试聊天 API
        print(f"\n3️⃣ 测试聊天 API")
        print("-" * 30)
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            url = f"http://localhost:8000/api/v1/agents/{agent_id}/chat"
            data = {
                "message": test_message,
                "user_id": "debug-user"
            }
            
            print(f"📤 调用聊天 API，消息: {test_message}")
            start_time = asyncio.get_event_loop().time()
            
            response = await client.post(url, json=data)
            
            end_time = asyncio.get_event_loop().time()
            duration = (end_time - start_time) * 1000
            
            print(f"✅ API 响应时间: {duration:.0f}ms")
            print(f"📊 API 状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                api_response = result.get("response", "")
                print(f"📝 API 回复: {api_response}")
                print(f"📊 回复长度: {len(api_response)} 字符")
                print(f"🔍 完整响应: {result}")
            else:
                print(f"❌ API 错误: {response.text}")
        
        # 4. 分析结果
        print(f"\n4️⃣ 结果分析")
        print("-" * 30)
        
        print(f"🔍 服务对比:")
        print(f"   AI 服务: {len(ai_response)} 字符")
        print(f"   简单服务: {len(simple_response)} 字符")
        
        if len(ai_response) > len(simple_response):
            print(f"✅ AI 服务回复更详细，质量更高")
        else:
            print(f"⚠️ AI 服务回复可能有问题")
        
        # 检查是否使用了 AI 服务
        if "policewoman" in ai_response.lower() or len(ai_response) > 100:
            print(f"✅ AI 服务正常工作，生成了相关回复")
        else:
            print(f"⚠️ AI 服务可能没有正常工作")
            
    except Exception as e:
        print(f"❌ 调试过程失败: {e}")
        import traceback
        traceback.print_exc()

async def test_different_messages():
    """测试不同消息的回复质量"""
    print(f"\n🧪 测试不同消息的回复质量")
    print("=" * 60)
    
    test_messages = [
        "Hello, how are you?",
        "Can you help me learn English grammar?",
        "What does 'policewoman' mean?",
        "I want to practice conversation",
        "Explain the difference between 'good' and 'well'"
    ]
    
    try:
        from app.services.ai_agent_service import AiAgentService
        
        ai_service = AiAgentService()
        mock_agent = {
            "id": "test-agent",
            "name": "语言学习助手",
            "agent_type": "language_tutor"
        }
        
        for i, message in enumerate(test_messages, 1):
            print(f"\n{i}️⃣ 测试消息: {message}")
            print("-" * 40)
            
            start_time = asyncio.get_event_loop().time()
            response = await ai_service.chat_with_agent(mock_agent, message)
            end_time = asyncio.get_event_loop().time()
            
            duration = (end_time - start_time) * 1000
            
            print(f"⏱️ 响应时间: {duration:.0f}ms")
            print(f"📝 回复: {response}")
            print(f"📊 长度: {len(response)} 字符")
            
            # 简单的质量评估
            if len(response) > 50 and message.lower() in response.lower():
                print(f"✅ 回复质量: 良好（相关且详细）")
            elif len(response) > 30:
                print(f"🔶 回复质量: 一般（较详细但可能不够相关）")
            else:
                print(f"❌ 回复质量: 较差（过于简短）")
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")

async def main():
    """主函数"""
    print("🔍 聊天系统调试工具")
    print("=" * 60)
    
    await debug_chat_flow()
    await test_different_messages()
    
    print(f"\n💡 调试建议:")
    print(f"1. 如果 AI 服务响应时间 < 500ms，可能没有真正调用 Ollama")
    print(f"2. 如果回复长度 < 50 字符，可能使用了简单回复")
    print(f"3. 如果回复与输入消息无关，可能有配置问题")
    print(f"4. 检查后端控制台日志，查看实际调用的服务")

if __name__ == "__main__":
    asyncio.run(main())
