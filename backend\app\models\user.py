"""
用户模型
"""

from sqlalchemy import Column, Inte<PERSON>, String, Bo<PERSON>an, DateTime
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.core.sqlalchemy_db import Base


class User(Base):
    """用户模型"""
    __tablename__ = "users"
    __table_args__ = {'extend_existing': True}

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True, nullable=False)
    email = Column(String(100), unique=True, index=True, nullable=False)
    password_hash = Column(String(255), nullable=False)
    full_name = Column(String(100))
    is_active = Column(Boolean, default=True)
    is_admin = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 关系
    opinion_reports = relationship("OpinionReport", back_populates="user")
    opinion_search_schemes = relationship("OpinionSearchScheme", back_populates="user")
    opinion_knowledge_graphs = relationship("OpinionKnowledgeGraph", back_populates="user")
