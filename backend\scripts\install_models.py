#!/usr/bin/env python3
"""
数字人模型安装脚本
自动下载和安装LivePortrait、MuseTalk、Hallo等模型
"""

import os
import sys
import asyncio
import argparse
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from services.model_manager import get_model_manager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def install_model(model_id: str, force: bool = False):
    """安装指定模型"""
    model_manager = get_model_manager()
    
    # 检查模型状态
    model_status = model_manager.get_model_status(model_id)
    if "error" in model_status:
        logger.error(f"模型 {model_id} 不存在")
        return False
    
    if model_status["status"] == "installed" and not force:
        logger.info(f"模型 {model_id} 已安装，跳过")
        return True
    
    logger.info(f"开始安装模型: {model_status['name']}")
    logger.info(f"描述: {model_status['description']}")
    logger.info(f"大小: {model_status['file_size'] / (1024*1024):.1f} MB")
    logger.info(f"GPU需求: {model_status['gpu_memory_required']} GB")
    
    # 进度回调
    async def progress_callback(model_id: str, progress: int, message: str):
        logger.info(f"[{model_id}] {progress}% - {message}")
    
    # 开始下载
    success = await model_manager.download_model(model_id, progress_callback)
    
    if success:
        logger.info(f"模型 {model_id} 安装成功！")
        return True
    else:
        logger.error(f"模型 {model_id} 安装失败")
        return False

async def install_all_models(force: bool = False):
    """安装所有模型"""
    model_manager = get_model_manager()
    all_models = model_manager.get_all_models_status()
    
    logger.info(f"准备安装 {len(all_models)} 个模型...")
    
    success_count = 0
    for model_id in all_models.keys():
        try:
            if await install_model(model_id, force):
                success_count += 1
        except Exception as e:
            logger.error(f"安装模型 {model_id} 时出错: {e}")
    
    logger.info(f"安装完成！成功: {success_count}/{len(all_models)}")

async def list_models():
    """列出所有模型状态"""
    model_manager = get_model_manager()
    all_models = model_manager.get_all_models_status()
    
    print("\n数字人模型状态:")
    print("=" * 80)
    
    for model_id, model_info in all_models.items():
        status_icon = {
            "installed": "✅",
            "not_installed": "❌",
            "downloading": "⬇️",
            "installing": "🔧",
            "error": "❌"
        }.get(model_info["status"], "❓")
        
        print(f"{status_icon} {model_info['name']} ({model_id})")
        print(f"   状态: {model_info['status']}")
        print(f"   描述: {model_info['description']}")
        print(f"   大小: {model_info['file_size'] / (1024*1024):.1f} MB")
        print(f"   GPU需求: {model_info['gpu_memory_required']} GB")
        print()

async def uninstall_model(model_id: str):
    """卸载指定模型"""
    model_manager = get_model_manager()
    
    model_status = model_manager.get_model_status(model_id)
    if "error" in model_status:
        logger.error(f"模型 {model_id} 不存在")
        return False
    
    if model_status["status"] != "installed":
        logger.info(f"模型 {model_id} 未安装，无需卸载")
        return True
    
    logger.info(f"正在卸载模型: {model_status['name']}")
    
    success = await model_manager.uninstall_model(model_id)
    
    if success:
        logger.info(f"模型 {model_id} 卸载成功！")
        return True
    else:
        logger.error(f"模型 {model_id} 卸载失败")
        return False

def get_recommended_models():
    """获取推荐的模型安装顺序"""
    return [
        ("liveportrait", "效果最佳，推荐首选"),
        ("musetalk", "速度最快，实时性好"),
        ("hallo", "质量最高，但需要更多GPU内存")
    ]

async def interactive_install():
    """交互式安装"""
    print("\n🤖 数字人模型安装向导")
    print("=" * 50)
    
    # 显示推荐模型
    recommendations = get_recommended_models()
    print("\n推荐安装顺序:")
    for i, (model_id, description) in enumerate(recommendations, 1):
        print(f"{i}. {model_id} - {description}")
    
    print("\n选项:")
    print("1. 安装推荐模型 (LivePortrait)")
    print("2. 安装所有模型")
    print("3. 自定义选择")
    print("4. 查看模型状态")
    print("0. 退出")
    
    while True:
        try:
            choice = input("\n请选择 (0-4): ").strip()
            
            if choice == "0":
                print("退出安装向导")
                break
            elif choice == "1":
                await install_model("liveportrait")
                break
            elif choice == "2":
                await install_all_models()
                break
            elif choice == "3":
                await custom_install()
                break
            elif choice == "4":
                await list_models()
                continue
            else:
                print("无效选择，请重试")
        except KeyboardInterrupt:
            print("\n\n安装已取消")
            break

async def custom_install():
    """自定义安装"""
    model_manager = get_model_manager()
    all_models = model_manager.get_all_models_status()
    
    print("\n可用模型:")
    model_list = list(all_models.keys())
    for i, model_id in enumerate(model_list, 1):
        model_info = all_models[model_id]
        status = "已安装" if model_info["status"] == "installed" else "未安装"
        print(f"{i}. {model_info['name']} ({model_id}) - {status}")
    
    try:
        choices = input("\n请输入要安装的模型编号 (用逗号分隔，如: 1,3): ").strip()
        if not choices:
            return
        
        selected_indices = [int(x.strip()) - 1 for x in choices.split(",")]
        
        for index in selected_indices:
            if 0 <= index < len(model_list):
                model_id = model_list[index]
                await install_model(model_id)
            else:
                print(f"无效编号: {index + 1}")
                
    except ValueError:
        print("输入格式错误")
    except KeyboardInterrupt:
        print("\n安装已取消")

async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="数字人模型安装工具")
    parser.add_argument("--install", "-i", help="安装指定模型")
    parser.add_argument("--install-all", "-a", action="store_true", help="安装所有模型")
    parser.add_argument("--uninstall", "-u", help="卸载指定模型")
    parser.add_argument("--list", "-l", action="store_true", help="列出所有模型")
    parser.add_argument("--force", "-f", action="store_true", help="强制重新安装")
    parser.add_argument("--interactive", action="store_true", help="交互式安装")
    
    args = parser.parse_args()
    
    try:
        if args.list:
            await list_models()
        elif args.install:
            await install_model(args.install, args.force)
        elif args.install_all:
            await install_all_models(args.force)
        elif args.uninstall:
            await uninstall_model(args.uninstall)
        elif args.interactive:
            await interactive_install()
        else:
            # 默认显示帮助和模型状态
            parser.print_help()
            print()
            await list_models()
            
            # 如果没有安装任何模型，提示交互式安装
            model_manager = get_model_manager()
            all_models = model_manager.get_all_models_status()
            installed_count = sum(1 for m in all_models.values() if m["status"] == "installed")
            
            if installed_count == 0:
                print("\n💡 提示: 您还没有安装任何模型")
                print("   运行 'python install_models.py --interactive' 开始交互式安装")
                print("   或运行 'python install_models.py -i liveportrait' 安装推荐模型")
    
    except Exception as e:
        logger.error(f"执行失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
