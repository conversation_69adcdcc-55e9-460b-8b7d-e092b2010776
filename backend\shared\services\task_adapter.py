"""
任务适配器 - 将各种服务转换为统一的任务处理流程
"""
import os
import logging
import asyncio
import uuid
from typing import Dict, Any, Optional, List, Union, Callable
from datetime import datetime

from services.progress_updater import ProgressUpdater

# 配置日志
logger = logging.getLogger(__name__)

class TaskAdapter:
    """
    任务适配器基类
    作为各种服务的适配器基类，提供统一的任务执行接口
    """
    
    def __init__(self, service, progress_updater=None):
        """
        初始化适配器
        
        Args:
            service: 服务实例
            progress_updater: 进度更新器实例
        """
        self.service = service
        self.progress_updater = progress_updater
    
    async def execute(self, task_id: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行任务的抽象方法，子类必须实现
        
        Args:
            task_id: 任务ID
            params: 任务参数
            
        Returns:
            执行结果，包含success和data/error字段
        """
        raise NotImplementedError("子类必须实现execute方法")
    
    async def update_progress(
        self, 
        task_id: str, 
        progress: int, 
        message: Optional[str] = None, 
        status: str = "running",
        **kwargs
    ):
        """
        更新任务进度
        
        Args:
            task_id: 任务ID
            progress: 进度百分比（0-100）
            message: 进度消息
            status: 任务状态
            **kwargs: 额外的数据
        """
        if self.progress_updater:
            await self.progress_updater.update(
                status=status,
                progress=progress,
                message=message,
                **kwargs
            )
        else:
            logger.warning(f"任务 {task_id} 没有可用的进度更新器")
    
    def get_file_paths(self, params: Dict[str, Any], task_id: str) -> Dict[str, str]:
        """
        获取标准化的文件路径
        
        Args:
            params: 任务参数
            task_id: 任务ID
            
        Returns:
            包含各种文件路径的字典
        """
        # 获取存储路径
        storage_root = os.path.join("backend", "storage")
        uploads_dir = os.path.join(storage_root, "uploads")
        results_dir = os.path.join(storage_root, "results")
        temp_dir = os.path.join(storage_root, "temp")
        
        # 确保目录存在
        os.makedirs(uploads_dir, exist_ok=True)
        os.makedirs(results_dir, exist_ok=True)
        os.makedirs(temp_dir, exist_ok=True)
        
        # 生成唯一的文件名前缀
        file_prefix = f"{task_id}_{uuid.uuid4().hex[:8]}"
        
        # 返回标准化的路径
        return {
            "task_id": task_id,
            "uploads_dir": uploads_dir,
            "results_dir": results_dir,
            "temp_dir": temp_dir,
            "file_prefix": file_prefix
        }
    
    async def handle_error(self, task_id: str, error: Exception) -> Dict[str, Any]:
        """
        处理任务执行过程中的错误
        
        Args:
            task_id: 任务ID
            error: 异常对象
            
        Returns:
            错误信息
        """
        error_message = str(error)
        logger.exception(f"任务执行异常: {task_id}, 错误: {error_message}")
        
        # 更新任务状态
        await self.update_progress(
            task_id=task_id,
            progress=0,
            message=f"任务执行失败: {error_message}",
            status="failed",
            error=error_message
        )
        
        return {
            "success": False,
            "error": error_message
        }

# 注册适配器映射
_adapter_registry = {}

def register_adapter(task_type: str, adapter_class: type):
    """
    注册任务适配器
    
    Args:
        task_type: 任务类型
        adapter_class: 适配器类
    """
    _adapter_registry[task_type] = adapter_class
    logger.info(f"注册任务适配器: {task_type} -> {adapter_class.__name__}")

def get_task_adapter(task_type: str, progress_updater=None, service=None):
    """
    获取任务适配器实例
    
    Args:
        task_type: 任务类型
        progress_updater: 进度更新器
        service: 服务实例（可选）
        
    Returns:
        适配器实例或None
    """
    adapter_class = _adapter_registry.get(task_type)
    if not adapter_class:
        logger.error(f"未找到任务类型 {task_type} 的适配器")
        return None
    
    # 如果没有提供服务实例，尝试获取
    if not service:
        try:
            # 尝试动态导入对应的服务获取函数
            service_module = task_type.split('_')[0]
            module_name = f"services.{service_module}_service"
            function_name = f"get_{service_module}_service"
            
            import importlib
            module = importlib.import_module(module_name)
            get_service = getattr(module, function_name)
            service = get_service()
        except (ImportError, AttributeError) as e:
            logger.warning(f"无法自动获取服务实例: {e}")
    
    return adapter_class(service, progress_updater) if service else None 