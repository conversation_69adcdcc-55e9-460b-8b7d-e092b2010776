"""
Wanx 2.1 模型配置
"""
import os

# Wanx 2.1 模型路径（后端统一位置）
WAN_MODEL_BASE_PATH = os.path.join(os.path.dirname(__file__), "../../..", "storage", "models", "video_generation", "wan", "Wan2.1")

# 模型配置
WAN_MODELS = {
    "t2v-1.3B": {
        "name": "文本转视频 1.3B",
        "description": "轻量级文本转视频模型，生成速度快",
        "model_path": os.path.join(WAN_MODEL_BASE_PATH, "models", "t2v-1.3B"),
        "max_duration": 10,
        "supported_resolutions": ["512x512", "768x512", "1024x576"],
        "default_resolution": "768x512"
    },
    "t2v-14B": {
        "name": "文本转视频 14B",
        "description": "高质量文本转视频模型，生成效果更好",
        "model_path": os.path.join(WAN_MODEL_BASE_PATH, "models", "t2v-14B"),
        "max_duration": 30,
        "supported_resolutions": ["512x512", "768x512", "1024x576", "1280x720"],
        "default_resolution": "1024x576"
    },
    "i2v-14B": {
        "name": "图片转视频 14B",
        "description": "高质量图片转视频模型",
        "model_path": os.path.join(WAN_MODEL_BASE_PATH, "models", "i2v-14B"),
        "max_duration": 20,
        "supported_resolutions": ["512x512", "768x512", "1024x576", "1280x720"],
        "default_resolution": "1024x576",
        "supported_image_formats": ["jpg", "jpeg", "png", "webp"]
    }
}

# 生成参数配置
GENERATION_CONFIG = {
    "default_fps": 24,
    "max_fps": 30,
    "min_fps": 8,
    "default_guidance_scale": 7.5,
    "max_guidance_scale": 20.0,
    "min_guidance_scale": 1.0,
    "default_num_inference_steps": 50,
    "max_num_inference_steps": 100,
    "min_num_inference_steps": 10,
    "max_prompt_length": 500,
    "output_format": "mp4",
    "temp_dir": os.path.join(WAN_MODEL_BASE_PATH, "temp"),
    "output_dir": os.path.join(WAN_MODEL_BASE_PATH, "outputs")
}

# 确保目录存在
for dir_path in [GENERATION_CONFIG["temp_dir"], GENERATION_CONFIG["output_dir"]]:
    os.makedirs(dir_path, exist_ok=True)

def get_model_config(model_name: str) -> dict:
    """获取模型配置"""
    return WAN_MODELS.get(model_name, WAN_MODELS["t2v-1.3B"])

def validate_generation_params(model_name: str, params: dict) -> dict:
    """验证生成参数"""
    model_config = get_model_config(model_name)
    validated_params = {}
    
    # 验证分辨率
    resolution = params.get("resolution", model_config["default_resolution"])
    if resolution not in model_config["supported_resolutions"]:
        resolution = model_config["default_resolution"]
    validated_params["resolution"] = resolution
    
    # 验证时长
    duration = min(params.get("duration", 5), model_config["max_duration"])
    validated_params["duration"] = max(duration, 1)
    
    # 验证帧率
    fps = params.get("fps", GENERATION_CONFIG["default_fps"])
    fps = max(min(fps, GENERATION_CONFIG["max_fps"]), GENERATION_CONFIG["min_fps"])
    validated_params["fps"] = fps
    
    # 验证引导系数
    guidance_scale = params.get("guidance_scale", GENERATION_CONFIG["default_guidance_scale"])
    guidance_scale = max(min(guidance_scale, GENERATION_CONFIG["max_guidance_scale"]), 
                        GENERATION_CONFIG["min_guidance_scale"])
    validated_params["guidance_scale"] = guidance_scale
    
    # 验证推理步数
    num_inference_steps = params.get("num_inference_steps", GENERATION_CONFIG["default_num_inference_steps"])
    num_inference_steps = max(min(num_inference_steps, GENERATION_CONFIG["max_num_inference_steps"]), 
                             GENERATION_CONFIG["min_num_inference_steps"])
    validated_params["num_inference_steps"] = num_inference_steps
    
    # 验证提示词长度
    prompt = params.get("prompt", "")
    if len(prompt) > GENERATION_CONFIG["max_prompt_length"]:
        prompt = prompt[:GENERATION_CONFIG["max_prompt_length"]]
    validated_params["prompt"] = prompt
    
    return validated_params

def get_available_models() -> list:
    """获取可用的模型列表"""
    available_models = []
    for model_name, config in WAN_MODELS.items():
        # 检查模型文件是否存在
        model_exists = os.path.exists(config["model_path"]) or True  # 暂时跳过文件检查
        available_models.append({
            "name": model_name,
            "display_name": config["name"],
            "description": config["description"],
            "available": model_exists,
            "max_duration": config["max_duration"],
            "supported_resolutions": config["supported_resolutions"],
            "default_resolution": config["default_resolution"]
        })
    return available_models
