#!/usr/bin/env python3
"""
测试前端发送的参数格式
"""
import requests
import json

def test_frontend_params():
    """测试前端发送的参数格式"""
    print("🧪 测试前端参数格式")
    print("=" * 50)
    
    # 模拟前端发送的参数（修复后）
    frontend_params = {
        "prompt": "一个和尚敲木鱼",
        "model": "t2v-1.3B",
        "duration": 10,
        "resolution": "768x512",  # 修复后的格式
        "fps": 8,
        "guidance_scale": 7.5,
        "num_inference_steps": 50
    }
    
    print("📋 前端参数:")
    print(json.dumps(frontend_params, indent=2, ensure_ascii=False))
    
    try:
        response = requests.post(
            "http://localhost:8000/api/v1/video/text-to-video",
            json=frontend_params,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"\n📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ API 调用成功!")
            print(f"📝 响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            return result.get("task_id")
        else:
            print(f"❌ API 调用失败: {response.status_code}")
            try:
                error_detail = response.json()
                print(f"📝 错误详情: {json.dumps(error_detail, indent=2, ensure_ascii=False)}")
            except:
                print(f"📝 错误内容: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return None

def test_history_api():
    """测试历史记录 API"""
    print(f"\n🔍 测试历史记录 API")
    print("=" * 50)
    
    try:
        response = requests.get("http://localhost:8000/api/v1/video/history/demo-user")
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 历史记录 API 调用成功!")
            print(f"📝 响应格式: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            if result.get("videos"):
                print(f"📊 视频数量: {len(result['videos'])}")
                for i, video in enumerate(result["videos"][:3]):
                    print(f"   {i+1}. {video.get('title', 'N/A')} - {video.get('status', 'N/A')}")
            
            return True
        else:
            print(f"❌ 历史记录 API 调用失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 历史记录请求异常: {e}")
        return False

def test_status_api(task_id):
    """测试状态查询 API"""
    if not task_id:
        print(f"\n⚠️ 跳过状态查询测试（没有任务ID）")
        return False
    
    print(f"\n🔍 测试状态查询 API")
    print("=" * 50)
    
    try:
        response = requests.get(f"http://localhost:8000/api/v1/video/status/{task_id}")
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 状态查询 API 调用成功!")
            print(f"📝 任务状态: {result.get('status')} ({result.get('progress')}%)")
            print(f"📝 消息: {result.get('message', 'N/A')}")
            return True
        else:
            print(f"❌ 状态查询 API 调用失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 状态查询请求异常: {e}")
        return False

def main():
    """主函数"""
    print("🧪 前端 API 集成测试")
    print("=" * 60)
    
    # 测试任务创建
    task_id = test_frontend_params()
    
    # 测试历史记录
    history_ok = test_history_api()
    
    # 测试状态查询
    status_ok = test_status_api(task_id)
    
    # 总结
    print(f"\n📊 测试总结:")
    print(f"{'✅' if task_id else '❌'} 任务创建: {'成功' if task_id else '失败'}")
    print(f"{'✅' if history_ok else '❌'} 历史记录: {'正常' if history_ok else '失败'}")
    print(f"{'✅' if status_ok else '❌'} 状态查询: {'正常' if status_ok else '失败'}")
    
    if task_id and history_ok:
        print(f"\n🎉 前端 API 集成测试通过！")
        print(f"💡 现在可以在前端界面测试视频生成")
    else:
        print(f"\n⚠️ 部分测试失败，需要进一步调试")

if __name__ == "__main__":
    main()
