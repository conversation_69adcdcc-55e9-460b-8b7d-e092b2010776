"""
文件上传API路由
支持图片、音频等文件的上传和管理
"""

import os
import uuid
import base64
import io
from datetime import datetime
from pathlib import Path
from typing import Optional

from fastapi import APIRouter, HTTPException, UploadFile, File, Form
from fastapi.responses import FileResponse
from pydantic import BaseModel
from PIL import Image

from ...core.config import settings

router = APIRouter()

# 文件存储配置
UPLOAD_DIR = Path(settings.STORAGE_PATH) / "uploads"
UPLOAD_DIR.mkdir(parents=True, exist_ok=True)

# 支持的文件类型
ALLOWED_IMAGE_TYPES = {
    'image/jpeg': '.jpg',
    'image/png': '.png',
    'image/webp': '.webp',
    'image/gif': '.gif'
}

ALLOWED_AUDIO_TYPES = {
    'audio/mpeg': '.mp3',
    'audio/wav': '.wav',
    'audio/ogg': '.ogg',
    'audio/mp4': '.m4a'
}

class FileUploadResponse(BaseModel):
    """文件上传响应"""
    success: bool
    file_url: str
    file_path: str
    file_size: int
    file_type: str
    original_name: str

class Base64UploadRequest(BaseModel):
    """Base64上传请求"""
    data: str  # base64数据
    filename: Optional[str] = None
    file_type: str  # 'image' 或 'audio'

@router.post("/upload/image", response_model=FileUploadResponse)
async def upload_image(file: UploadFile = File(...)):
    """
    上传图片文件
    """
    try:
        # 验证文件类型
        if file.content_type not in ALLOWED_IMAGE_TYPES:
            raise HTTPException(
                status_code=400, 
                detail=f"不支持的图片格式: {file.content_type}"
            )
        
        # 生成唯一文件名
        file_ext = ALLOWED_IMAGE_TYPES[file.content_type]
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"img_{timestamp}_{uuid.uuid4().hex[:8]}{file_ext}"
        
        # 创建保存路径
        save_path = UPLOAD_DIR / "images" / filename
        save_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 读取并处理图片
        content = await file.read()
        
        # 使用PIL处理图片（统一格式、压缩等）
        img = Image.open(io.BytesIO(content))
        img = img.convert('RGB')
        
        # 如果图片太大，进行压缩
        if img.size[0] > 1024 or img.size[1] > 1024:
            img.thumbnail((1024, 1024), Image.Resampling.LANCZOS)
        
        # 保存处理后的图片
        img.save(save_path, 'JPEG', quality=85, optimize=True)
        
        # 获取文件大小
        file_size = save_path.stat().st_size
        
        # 生成访问URL
        file_url = f"/storage/uploads/images/{filename}"
        
        return FileUploadResponse(
            success=True,
            file_url=file_url,
            file_path=str(save_path),
            file_size=file_size,
            file_type=file.content_type,
            original_name=file.filename or "unknown"
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"图片上传失败: {str(e)}")

@router.post("/upload/audio", response_model=FileUploadResponse)
async def upload_audio(file: UploadFile = File(...)):
    """
    上传音频文件
    """
    try:
        # 验证文件类型
        if file.content_type not in ALLOWED_AUDIO_TYPES:
            raise HTTPException(
                status_code=400, 
                detail=f"不支持的音频格式: {file.content_type}"
            )
        
        # 生成唯一文件名
        file_ext = ALLOWED_AUDIO_TYPES[file.content_type]
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"audio_{timestamp}_{uuid.uuid4().hex[:8]}{file_ext}"
        
        # 创建保存路径
        save_path = UPLOAD_DIR / "audio" / filename
        save_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 保存文件
        content = await file.read()
        with open(save_path, 'wb') as f:
            f.write(content)
        
        # 获取文件大小
        file_size = save_path.stat().st_size
        
        # 生成访问URL
        file_url = f"/storage/uploads/audio/{filename}"
        
        return FileUploadResponse(
            success=True,
            file_url=file_url,
            file_path=str(save_path),
            file_size=file_size,
            file_type=file.content_type,
            original_name=file.filename or "unknown"
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"音频上传失败: {str(e)}")

@router.post("/upload/base64", response_model=FileUploadResponse)
async def upload_base64(request: Base64UploadRequest):
    """
    上传base64编码的文件
    """
    try:
        # 解析base64数据
        if request.data.startswith('data:'):
            # 格式: data:image/jpeg;base64,/9j/4AAQ...
            header, data = request.data.split(',', 1)
            content_type = header.split(';')[0].split(':')[1]
        else:
            # 纯base64数据
            data = request.data
            content_type = f"{request.file_type}/jpeg"  # 默认类型
        
        # 解码base64
        file_content = base64.b64decode(data)
        
        # 根据文件类型选择处理方式
        if request.file_type == 'image':
            return await _process_base64_image(file_content, content_type, request.filename)
        elif request.file_type == 'audio':
            return await _process_base64_audio(file_content, content_type, request.filename)
        else:
            raise HTTPException(status_code=400, detail="不支持的文件类型")
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Base64文件上传失败: {str(e)}")

async def _process_base64_image(content: bytes, content_type: str, filename: Optional[str]) -> FileUploadResponse:
    """处理base64图片"""
    # 验证图片类型
    if content_type not in ALLOWED_IMAGE_TYPES:
        content_type = 'image/jpeg'  # 默认为JPEG
    
    file_ext = ALLOWED_IMAGE_TYPES[content_type]
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    save_filename = f"img_{timestamp}_{uuid.uuid4().hex[:8]}{file_ext}"
    
    # 创建保存路径
    save_path = UPLOAD_DIR / "images" / save_filename
    save_path.parent.mkdir(parents=True, exist_ok=True)
    
    # 处理图片
    img = Image.open(io.BytesIO(content))
    img = img.convert('RGB')
    
    # 压缩大图片
    if img.size[0] > 1024 or img.size[1] > 1024:
        img.thumbnail((1024, 1024), Image.Resampling.LANCZOS)
    
    # 保存图片
    img.save(save_path, 'JPEG', quality=85, optimize=True)
    
    # 获取文件大小
    file_size = save_path.stat().st_size
    
    # 生成访问URL
    file_url = f"/storage/uploads/images/{save_filename}"
    
    return FileUploadResponse(
        success=True,
        file_url=file_url,
        file_path=str(save_path),
        file_size=file_size,
        file_type=content_type,
        original_name=filename or "base64_image"
    )

async def _process_base64_audio(content: bytes, content_type: str, filename: Optional[str]) -> FileUploadResponse:
    """处理base64音频"""
    # 验证音频类型
    if content_type not in ALLOWED_AUDIO_TYPES:
        content_type = 'audio/wav'  # 默认为WAV
    
    file_ext = ALLOWED_AUDIO_TYPES[content_type]
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    save_filename = f"audio_{timestamp}_{uuid.uuid4().hex[:8]}{file_ext}"
    
    # 创建保存路径
    save_path = UPLOAD_DIR / "audio" / save_filename
    save_path.parent.mkdir(parents=True, exist_ok=True)
    
    # 保存音频文件
    with open(save_path, 'wb') as f:
        f.write(content)
    
    # 获取文件大小
    file_size = save_path.stat().st_size
    
    # 生成访问URL
    file_url = f"/storage/uploads/audio/{save_filename}"
    
    return FileUploadResponse(
        success=True,
        file_url=file_url,
        file_path=str(save_path),
        file_size=file_size,
        file_type=content_type,
        original_name=filename or "base64_audio"
    )

@router.get("/file/{file_type}/{filename}")
async def get_file(file_type: str, filename: str):
    """
    获取上传的文件
    """
    try:
        if file_type not in ['images', 'audio']:
            raise HTTPException(status_code=400, detail="无效的文件类型")
        
        file_path = UPLOAD_DIR / file_type / filename
        
        if not file_path.exists():
            raise HTTPException(status_code=404, detail="文件不存在")
        
        return FileResponse(file_path)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取文件失败: {str(e)}")

@router.delete("/file/{file_type}/{filename}")
async def delete_file(file_type: str, filename: str):
    """
    删除上传的文件
    """
    try:
        if file_type not in ['images', 'audio']:
            raise HTTPException(status_code=400, detail="无效的文件类型")
        
        file_path = UPLOAD_DIR / file_type / filename
        
        if file_path.exists():
            file_path.unlink()
            return {"success": True, "message": "文件删除成功"}
        else:
            raise HTTPException(status_code=404, detail="文件不存在")
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除文件失败: {str(e)}")
