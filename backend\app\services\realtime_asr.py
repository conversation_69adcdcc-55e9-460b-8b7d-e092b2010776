#!/usr/bin/env python3
"""
实时语音识别服务
支持流式语音识别和实时对话
"""

import asyncio
import json
import logging
from typing import Optional, Callable, Dict, Any
import numpy as np

logger = logging.getLogger(__name__)

class RealtimeASR:
    """实时语音识别类"""
    
    def __init__(self, 
                 model_name: str = "whisper-base",
                 language: str = "zh",
                 sample_rate: int = 16000,
                 chunk_duration: float = 1.0):
        """
        初始化实时ASR
        
        Args:
            model_name: 模型名称
            language: 语言代码
            sample_rate: 采样率
            chunk_duration: 音频块时长(秒)
        """
        self.model_name = model_name
        self.language = language
        self.sample_rate = sample_rate
        self.chunk_duration = chunk_duration
        self.chunk_size = int(sample_rate * chunk_duration)
        
        self.model = None
        self.is_listening = False
        self.audio_buffer = []
        
        # 回调函数
        self.on_partial_result: Optional[Callable] = None
        self.on_final_result: Optional[Callable] = None
        self.on_error: Optional[Callable] = None
    
    async def initialize(self):
        """初始化ASR模型"""
        try:
            # 尝试使用Whisper
            import whisper
            self.model = whisper.load_model(self.model_name)
            logger.info(f"✅ Whisper模型加载成功: {self.model_name}")
            return True
        except ImportError:
            logger.warning("Whisper未安装，尝试使用其他ASR")
            try:
                # 备用方案：使用speech_recognition
                import speech_recognition as sr
                self.model = sr.Recognizer()
                logger.info("✅ SpeechRecognition初始化成功")
                return True
            except ImportError:
                logger.error("❌ 无可用的ASR模型")
                return False
        except Exception as e:
            logger.error(f"❌ ASR初始化失败: {e}")
            return False
    
    async def start_listening(self):
        """开始监听音频"""
        if not self.model:
            await self.initialize()
        
        self.is_listening = True
        self.audio_buffer = []
        
        logger.info("🎤 开始实时语音识别...")
        
        try:
            # 启动音频捕获
            await self._start_audio_capture()
        except Exception as e:
            logger.error(f"❌ 音频捕获失败: {e}")
            if self.on_error:
                await self.on_error(str(e))
    
    async def stop_listening(self):
        """停止监听"""
        self.is_listening = False
        logger.info("🛑 停止语音识别")
    
    async def _start_audio_capture(self):
        """启动音频捕获（模拟实现）"""
        try:
            import pyaudio
            
            # 音频参数
            format = pyaudio.paInt16
            channels = 1
            
            # 创建音频流
            audio = pyaudio.PyAudio()
            stream = audio.open(
                format=format,
                channels=channels,
                rate=self.sample_rate,
                input=True,
                frames_per_buffer=self.chunk_size
            )
            
            logger.info("🎤 音频流已启动")
            
            while self.is_listening:
                try:
                    # 读取音频数据
                    data = stream.read(self.chunk_size, exception_on_overflow=False)
                    audio_chunk = np.frombuffer(data, dtype=np.int16)
                    
                    # 处理音频块
                    await self._process_audio_chunk(audio_chunk)
                    
                    # 短暂等待
                    await asyncio.sleep(0.01)
                    
                except Exception as e:
                    logger.error(f"❌ 音频处理错误: {e}")
                    break
            
            # 清理资源
            stream.stop_stream()
            stream.close()
            audio.terminate()
            
        except ImportError:
            logger.warning("PyAudio未安装，使用模拟音频输入")
            await self._simulate_audio_input()
        except Exception as e:
            logger.error(f"❌ 音频捕获异常: {e}")
            if self.on_error:
                await self.on_error(str(e))
    
    async def _simulate_audio_input(self):
        """模拟音频输入（用于测试）"""
        logger.info("🎭 使用模拟音频输入")
        
        # 模拟语音识别结果
        test_phrases = [
            "你好，我想要生成一个数字人",
            "请帮我创建一个会说话的AI助手",
            "我需要一个专业的女老师形象",
            "能否调整一下说话的速度",
            "这个效果很不错"
        ]
        
        for i, phrase in enumerate(test_phrases):
            if not self.is_listening:
                break
            
            # 模拟部分结果
            if self.on_partial_result:
                await self.on_partial_result(phrase[:len(phrase)//2])
            
            await asyncio.sleep(1.0)
            
            # 模拟最终结果
            if self.on_final_result:
                await self.on_final_result(phrase)
            
            await asyncio.sleep(2.0)
    
    async def _process_audio_chunk(self, audio_chunk: np.ndarray):
        """处理音频块"""
        try:
            # 添加到缓冲区
            self.audio_buffer.extend(audio_chunk)
            
            # 检查是否有足够的数据进行识别
            if len(self.audio_buffer) >= self.sample_rate * 2:  # 2秒的音频
                # 转换为音频格式
                audio_data = np.array(self.audio_buffer, dtype=np.float32) / 32768.0
                
                # 执行语音识别
                result = await self._recognize_audio(audio_data)
                
                if result and result.strip():
                    if self.on_partial_result:
                        await self.on_partial_result(result)
                
                # 保留最后1秒的音频作为重叠
                overlap_size = self.sample_rate
                self.audio_buffer = self.audio_buffer[-overlap_size:]
                
        except Exception as e:
            logger.error(f"❌ 音频块处理错误: {e}")
    
    async def _recognize_audio(self, audio_data: np.ndarray) -> Optional[str]:
        """识别音频数据"""
        try:
            if hasattr(self.model, 'transcribe'):
                # Whisper模型
                result = self.model.transcribe(audio_data, language=self.language)
                return result.get('text', '').strip()
            else:
                # 其他ASR模型
                # 这里可以添加其他ASR的实现
                return None
                
        except Exception as e:
            logger.error(f"❌ 语音识别错误: {e}")
            return None
    
    def set_callbacks(self, 
                     on_partial: Optional[Callable] = None,
                     on_final: Optional[Callable] = None,
                     on_error: Optional[Callable] = None):
        """设置回调函数"""
        self.on_partial_result = on_partial
        self.on_final_result = on_final
        self.on_error = on_error

class ASRManager:
    """ASR管理器"""
    
    def __init__(self):
        self.asr_instances: Dict[str, RealtimeASR] = {}
    
    async def create_session(self, session_id: str, config: Dict[str, Any]) -> RealtimeASR:
        """创建ASR会话"""
        asr = RealtimeASR(
            model_name=config.get('model_name', 'whisper-base'),
            language=config.get('language', 'zh'),
            sample_rate=config.get('sample_rate', 16000),
            chunk_duration=config.get('chunk_duration', 1.0)
        )
        
        await asr.initialize()
        self.asr_instances[session_id] = asr
        
        logger.info(f"✅ ASR会话创建成功: {session_id}")
        return asr
    
    async def remove_session(self, session_id: str):
        """移除ASR会话"""
        if session_id in self.asr_instances:
            asr = self.asr_instances[session_id]
            await asr.stop_listening()
            del self.asr_instances[session_id]
            logger.info(f"🗑️ ASR会话已移除: {session_id}")
    
    def get_session(self, session_id: str) -> Optional[RealtimeASR]:
        """获取ASR会话"""
        return self.asr_instances.get(session_id)

# 全局ASR管理器实例
asr_manager = ASRManager()
