<template>
  <div class="menu-container">
    <!-- 操作按钮和树形表格 -->
    <a-card :bordered="false">
      <template #title>
        <a-button type="primary" @click="handleAdd(null)">
          <template #icon><plus-outlined /></template>
          新增根菜单
        </a-button>
        <a-button style="margin-left: 16px" @click="handleExpandAll">
          <template #icon>
            <NodeExpandOutlined v-if="!expandAll" />
            <NodeCollapseOutlined v-else />
          </template>
          {{ expandAll ? '折叠全部' : '展开全部' }}
        </a-button>
      </template>

      <!-- 菜单树形表格 -->
      <a-table
        :loading="loading"
        :columns="columns"
        :data-source="menuTree"
        :pagination="false"
        :row-key="record => record.id"
        :expandable="{
          defaultExpandAllRows: expandAll
        }"
      >
        <!-- 图标 -->
        <template #bodyCell="{ column, text, record }">
          <template v-if="column.dataIndex === 'icon'">
            <component :is="resolveIcon(text)" v-if="text" />
            <span v-else>-</span>
          </template>
          
          <!-- 菜单类型 -->
          <template v-else-if="column.dataIndex === 'type'">
            <a-tag :color="typeColors[text]">{{ typeNames[text] }}</a-tag>
          </template>
          
          <!-- 显示状态 -->
          <template v-else-if="column.dataIndex === 'hidden'">
            <a-tag :color="text ? 'red' : 'green'">
              {{ text ? '隐藏' : '显示' }}
            </a-tag>
          </template>
          
          <!-- 路由缓存 -->
          <template v-else-if="column.dataIndex === 'keepAlive'">
            <a-tag :color="text ? 'blue' : 'orange'">
              {{ text ? '是' : '否' }}
            </a-tag>
          </template>
          
          <!-- 操作 -->
          <template v-else-if="column.dataIndex === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="handleAdd(record)" v-if="record.type !== 'button'">
                新增
              </a-button>
              <a-button type="link" size="small" @click="handleEdit(record)">编辑</a-button>
              <a-popconfirm
                title="确定要删除该菜单吗？"
                ok-text="确定"
                cancel-text="取消"
                @confirm="handleDelete(record)"
              >
                <a-button type="link" danger size="small">删除</a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 菜单表单对话框 -->
    <a-modal
      v-model:visible="modalVisible"
      :title="modalTitle"
      width="650px"
      :confirm-loading="confirmLoading"
      @ok="handleModalSubmit"
    >
      <a-form
        ref="formRef"
        :model="form"
        :rules="rules"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item label="上级菜单">
          <a-tree-select
            v-model:value="form.parentId"
            :tree-data="treeSelectData"
            placeholder="请选择上级菜单"
            tree-default-expand-all
            :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
            allow-clear
          />
        </a-form-item>

        <a-form-item label="菜单类型" name="type">
          <a-radio-group v-model:value="form.type" button-style="solid">
            <a-radio-button value="menu">菜单</a-radio-button>
            <a-radio-button value="catalog">目录</a-radio-button>
            <a-radio-button value="button">按钮</a-radio-button>
          </a-radio-group>
        </a-form-item>

        <a-form-item label="菜单名称" name="title">
          <a-input v-model:value="form.title" placeholder="请输入菜单名称" />
        </a-form-item>

        <template v-if="form.type !== 'button'">
          <a-form-item label="菜单图标" v-if="form.type !== 'button'">
            <a-input v-model:value="form.icon" placeholder="请输入图标名称">
              <template #prefix>
                <component :is="resolveIcon(form.icon)" v-if="form.icon" />
                <question-circle-outlined v-else />
              </template>
              <template #addonAfter>
                <a-button type="link" size="small" @click="showIconSelector = true">
                  选择图标
                </a-button>
              </template>
            </a-input>
          </a-form-item>

          <a-form-item 
            label="路由路径" 
            name="path"
            v-if="form.type !== 'button'"
            :rules="[
              { required: form.type !== 'button', message: '请输入路由路径', trigger: 'blur' }
            ]"
          >
            <a-input v-model:value="form.path" placeholder="请输入路由路径" />
          </a-form-item>

          <a-form-item 
            label="组件路径" 
            name="component"
            v-if="form.type === 'menu'"
            :rules="[
              { required: form.type === 'menu', message: '请输入组件路径', trigger: 'blur' }
            ]"
          >
            <a-input v-model:value="form.component" placeholder="请输入组件路径" />
          </a-form-item>

          <a-form-item label="路由缓存" v-if="form.type === 'menu'">
            <a-switch v-model:checked="form.keepAlive" />
          </a-form-item>
        </template>

        <template v-else>
          <a-form-item 
            label="权限标识" 
            name="permission"
            :rules="[
              { required: form.type === 'button', message: '请输入权限标识', trigger: 'blur' }
            ]"
          >
            <a-input v-model:value="form.permission" placeholder="请输入权限标识，如：system:user:add" />
          </a-form-item>
        </template>

        <a-form-item label="显示状态">
          <a-switch 
            v-model:checked="form.hidden" 
            :checked-children="'隐藏'" 
            :un-checked-children="'显示'" 
          />
        </a-form-item>

        <a-form-item label="排序" name="sort">
          <a-input-number v-model:value="form.sort" placeholder="请输入排序值" style="width: 100%" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 图标选择器对话框 -->
    <a-modal
      v-model:visible="showIconSelector"
      title="选择图标"
      width="800px"
      :footer="null"
    >
      <div class="icon-container">
        <div 
          v-for="icon in iconList" 
          :key="icon"
          class="icon-item"
          @click="selectIcon(icon)"
        >
          <component :is="icon" />
          <div class="icon-name">{{ icon }}</div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, computed, toRaw, onMounted, h } from 'vue';
import { message } from 'ant-design-vue';
import * as Icons from '@ant-design/icons-vue';
import {
  PlusOutlined,
  QuestionCircleOutlined,
  PlusSquareOutlined,
  MinusSquareOutlined,
  NodeExpandOutlined,
  NodeCollapseOutlined
} from '@ant-design/icons-vue';

// 模拟API函数，实际应用中应该导入真实的API模块
const getMenuList = () => {
  return new Promise(resolve => {
    setTimeout(() => {
      resolve({
        data: [
          {
            id: 1,
            parentId: 0,
            title: '系统管理',
            icon: 'SettingOutlined',
            path: '/system',
            component: 'Layout',
            type: 'catalog',
            sort: 1,
            hidden: false,
            keepAlive: false,
            children: [
              {
                id: 11,
                parentId: 1,
                title: '用户管理',
                icon: 'UserOutlined',
                path: 'user',
                component: 'system/user/index',
                type: 'menu',
                sort: 1,
                hidden: false,
                keepAlive: true,
                children: [
                  {
                    id: 111,
                    parentId: 11,
                    title: '用户查询',
                    permission: 'system:user:list',
                    type: 'button',
                    sort: 1,
                    hidden: false
                  },
                  {
                    id: 112,
                    parentId: 11,
                    title: '用户新增',
                    permission: 'system:user:add',
                    type: 'button',
                    sort: 2,
                    hidden: false
                  },
                  {
                    id: 113,
                    parentId: 11,
                    title: '用户编辑',
                    permission: 'system:user:edit',
                    type: 'button',
                    sort: 3,
                    hidden: false
                  }
                ]
              },
              {
                id: 12,
                parentId: 1,
                title: '角色管理',
                icon: 'TeamOutlined',
                path: 'role',
                component: 'system/role/index',
                type: 'menu',
                sort: 2,
                hidden: false,
                keepAlive: true
              },
              {
                id: 13,
                parentId: 1,
                title: '菜单管理',
                icon: 'MenuOutlined',
                path: 'menu',
                component: 'system/menu/index',
                type: 'menu',
                sort: 3,
                hidden: false,
                keepAlive: true
              }
            ]
          },
          {
            id: 2,
            parentId: 0,
            title: '系统监控',
            icon: 'DashboardOutlined',
            path: '/monitor',
            component: 'Layout',
            type: 'catalog',
            sort: 2,
            hidden: false,
            keepAlive: false,
            children: [
              {
                id: 21,
                parentId: 2,
                title: '操作日志',
                icon: 'FileTextOutlined',
                path: 'log',
                component: 'monitor/log/index',
                type: 'menu',
                sort: 1,
                hidden: false,
                keepAlive: false
              }
            ]
          }
        ]
      });
    }, 500);
  });
};

export default defineComponent({
  name: 'MenuList',
  components: {
    PlusOutlined,
    QuestionCircleOutlined,
    PlusSquareOutlined,
    MinusSquareOutlined,
    NodeExpandOutlined,
    NodeCollapseOutlined,
    ...Icons
  },
  setup() {
    // 表格数据和加载状态
    const loading = ref(false);
    const menuList = ref([]);
    const menuTree = ref([]);
    const expandAll = ref(true);
    
    // 类型名称映射
    const typeNames = {
      catalog: '目录',
      menu: '菜单',
      button: '按钮'
    };
    
    // 类型颜色映射
    const typeColors = {
      catalog: 'purple',
      menu: 'blue',
      button: 'cyan'
    };
    
    // 表格列定义
    const columns = [
      {
        title: '菜单名称',
        dataIndex: 'title',
        key: 'title',
        width: 220
      },
      {
        title: '图标',
        dataIndex: 'icon',
        key: 'icon',
        width: 80,
        align: 'center'
      },
      {
        title: '排序',
        dataIndex: 'sort',
        key: 'sort',
        width: 80,
        align: 'center'
      },
      {
        title: '权限标识',
        dataIndex: 'permission',
        key: 'permission',
        width: 180
      },
      {
        title: '路由路径',
        dataIndex: 'path',
        key: 'path',
        width: 150
      },
      {
        title: '组件路径',
        dataIndex: 'component',
        key: 'component',
        width: 200
      },
      {
        title: '菜单类型',
        dataIndex: 'type',
        key: 'type',
        width: 100,
        align: 'center'
      },
      {
        title: '显示状态',
        dataIndex: 'hidden',
        key: 'hidden',
        width: 100,
        align: 'center'
      },
      {
        title: '缓存',
        dataIndex: 'keepAlive',
        key: 'keepAlive',
        width: 80,
        align: 'center',
        customRender: ({ text, record }) => {
          if (record.type !== 'menu') {
            return '-';
          }
          return text ? '是' : '否';
        }
      },
      {
        title: '操作',
        dataIndex: 'action',
        key: 'action',
        fixed: 'right',
        width: 200
      }
    ];
    
    // 表单对话框状态
    const modalVisible = ref(false);
    const modalType = ref('add'); // 'add' or 'edit'
    const confirmLoading = ref(false);
    
    // 图标选择器
    const showIconSelector = ref(false);
    const iconList = Object.keys(Icons)
      .filter(name => name.endsWith('Outlined') || name.endsWith('Filled'))
      .sort();
    
    // 解析图标组件
    const resolveIcon = (name) => {
      return name && Icons[name] ? Icons[name] : null;
    };
    
    // 表单对话框标题
    const modalTitle = computed(() => {
      return modalType.value === 'add' ? '新增菜单' : '编辑菜单';
    });
    
    // 生成树选择数据
    const treeSelectData = computed(() => {
      // 构建树选择数据，添加根节点和过滤当前编辑节点
      const rootNode = {
        id: 0,
        title: '根菜单',
        value: 0,
        key: 0
      };
      
      const buildTreeData = (data, excludeId = null) => {
        return data
          .filter(item => item.id !== excludeId)
          .map(item => {
            const node = {
              id: item.id,
              title: item.title,
              value: item.id,
              key: item.id,
              disabled: item.type === 'button',
              icon: item.icon ? () => h(resolveIcon(item.icon)) : null
            };
            
            if (item.children && item.children.length > 0) {
              node.children = buildTreeData(item.children, excludeId);
            }
            
            return node;
          });
      };
      
      return [rootNode, ...buildTreeData(menuTree.value, form.type === 'edit' ? form.id : null)];
    });
    
    // 表单数据
    const formRef = ref(null);
    const form = reactive({
      id: '',
      parentId: 0,
      title: '',
      type: 'menu',
      icon: '',
      path: '',
      component: '',
      permission: '',
      keepAlive: false,
      hidden: false,
      sort: 0
    });
    
    // 表单验证规则
    const rules = {
      title: [
        { required: true, message: '请输入菜单名称', trigger: 'blur' }
      ],
      sort: [
        { required: true, message: '请输入排序值', trigger: 'blur' },
        { type: 'number', message: '排序值必须是数字', trigger: 'blur' }
      ]
    };
    
    // 获取菜单列表
    const fetchMenuList = async () => {
      loading.value = true;
      
      try {
        const res = await getMenuList();
        menuList.value = res.data || [];
        menuTree.value = menuList.value;
      } catch (error) {
        console.error('获取菜单列表失败:', error);
        message.error('获取菜单列表失败');
      } finally {
        loading.value = false;
      }
    };
    
    // 展开/折叠所有
    const handleExpandAll = () => {
      expandAll.value = !expandAll.value;
    };
    
    // 新增菜单
    const handleAdd = (parent) => {
      resetForm();
      modalType.value = 'add';
      
      if (parent) {
        form.parentId = parent.id;
        
        // 如果是添加按钮类操作，默认类型为按钮
        if (parent.type === 'menu') {
          form.type = 'button';
        }
      }
      
      modalVisible.value = true;
    };
    
    // 编辑菜单
    const handleEdit = (record) => {
      resetForm();
      modalType.value = 'edit';
      
      Object.keys(form).forEach(key => {
        if (key in record) {
          form[key] = record[key];
        }
      });
      
      modalVisible.value = true;
    };
    
    // 删除菜单
    const handleDelete = async (record) => {
      if (record.children && record.children.length > 0) {
        message.error('该菜单下存在子菜单，无法删除');
        return;
      }
      
      try {
        message.success(`删除菜单成功: ${record.title}`);
        // 实际应调用删除接口
        // 重新加载数据
        fetchMenuList();
      } catch (error) {
        console.error('删除菜单失败:', error);
        message.error('删除菜单失败');
      }
    };
    
    // 重置表单
    const resetForm = () => {
      if (formRef.value) {
        formRef.value.resetFields();
      }
      
      form.id = '';
      form.parentId = 0;
      form.title = '';
      form.type = 'menu';
      form.icon = '';
      form.path = '';
      form.component = '';
      form.permission = '';
      form.keepAlive = false;
      form.hidden = false;
      form.sort = 0;
    };
    
    // 提交表单
    const handleModalSubmit = () => {
      formRef.value.validate()
        .then(async () => {
          confirmLoading.value = true;
          
          try {
            const formData = toRaw(form);
            
            // 模拟API调用
            await new Promise(resolve => setTimeout(resolve, 500));
            
            message.success(`${modalType.value === 'add' ? '新增' : '编辑'}菜单成功`);
            modalVisible.value = false;
            fetchMenuList();
          } catch (error) {
            console.error('保存菜单失败:', error);
            message.error('保存菜单失败');
          } finally {
            confirmLoading.value = false;
          }
        })
        .catch(error => {
          console.log('表单验证失败:', error);
        });
    };
    
    // 选择图标
    const selectIcon = (iconName) => {
      form.icon = iconName;
      showIconSelector.value = false;
    };
    
    // 组件挂载时加载数据
    onMounted(() => {
      fetchMenuList();
    });
    
    return {
      // 状态
      loading,
      menuTree,
      expandAll,
      typeNames,
      typeColors,
      columns,
      modalVisible,
      modalType,
      modalTitle,
      confirmLoading,
      formRef,
      form,
      rules,
      treeSelectData,
      showIconSelector,
      iconList,
      
      // 方法
      handleExpandAll,
      handleAdd,
      handleEdit,
      handleDelete,
      handleModalSubmit,
      resolveIcon,
      selectIcon
    };
  }
});
</script>

<style lang="less" scoped>
.menu-container {
  padding: 24px;
  
  :deep(.ant-table-row-expand-icon) {
    margin-right: 8px;
  }
  
  .icon-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 12px;
    max-height: 400px;
    overflow-y: auto;
    
    .icon-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 12px 8px;
      border: 1px solid #f0f0f0;
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.3s;
      
      &:hover {
        color: #1890ff;
        border-color: #1890ff;
        background-color: rgba(24, 144, 255, 0.05);
      }
      
      .anticon {
        font-size: 24px;
        margin-bottom: 8px;
      }
      
      .icon-name {
        font-size: 12px;
        text-align: center;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        width: 100%;
      }
    }
  }
}
</style> 