"""
术语智能采集服务
实现术语智能采集的核心逻辑
"""

import logging
import json
import time
import random
import asyncio
import traceback
from typing import List, Dict, Any, Optional
from datetime import datetime
import re
import requests
from bs4 import BeautifulSoup
from sqlalchemy.orm import Session
from sqlalchemy import desc
import os
import sys
import jieba
from sqlalchemy.exc import SQLAlchemyError

from models.terminology_collection import TerminologyCollectionTask, TerminologyCollectionResult

# 配置详细的日志记录
logger = logging.getLogger("terminology_collection_service")
# 为终端添加一个处理器，如果需要
if not logger.handlers:
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    ))
    logger.addHandler(console_handler)
    # 日志级别可以通过环境变量进行配置
    logger.setLevel(os.environ.get('TERMINOLOGY_LOG_LEVEL', 'INFO'))

# 爬虫请求头
USER_AGENTS = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36",
    "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:90.0) Gecko/20100101 Firefox/90.0",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:90.0) Gecko/20100101 Firefox/90.0",
]

def create_collection_task(
    db: Session,
    name: str,
    language: str,
    mode: str,
    collection_mode: str = None,
    keywords: str = None,
    target_website: str = None,
    url_list: List[str] = None,
    enable_time_range: bool = False,
    time_range: Dict[str, str] = None,
    restrict_words: str = None,
    restrict_settings: Dict[str, Any] = None,
    required_words: str = None,
    required_settings: Dict[str, Any] = None,
    user_id: int = None
) -> TerminologyCollectionTask:
    """
    创建术语采集任务
    """
    logger.info(f"[术语采集服务] 创建任务 - 名称: {name}, 语言: {language}, 模式: {mode}")
    
    # 处理mode和collection_mode，确保两者一致
    if collection_mode is None:
        collection_mode = mode
    
    # 创建任务对象
    task = TerminologyCollectionTask(
        name=name,
        language=language,
        mode=mode,
        collection_mode=collection_mode,
        keywords=keywords,
        target_website=target_website,
        url_list=url_list,
        enable_time_range=enable_time_range,
        time_range=time_range,
        restrict_words=restrict_words,
        restrict_settings=restrict_settings,
        required_words=required_words,
        required_settings=required_settings,
        status="pending",
        progress=0,
        user_id=user_id
    )
    
    # 保存到数据库
    db.add(task)
    db.commit()
    db.refresh(task)
    
    logger.info(f"[术语采集服务] 创建任务成功 - 任务ID: {task.task_id}")
    
    return task

# 添加别名函数，使routes.terminology_routes能够正确导入
def create_terminology_collection_task(
    db: Session,
    name: str,
    language: str,
    mode: str,
    collection_mode: str = None,
    keywords: str = None,
    target_website: str = None,
    url_list: List[str] = None,
    enable_time_range: bool = False,
    time_range: Dict[str, str] = None,
    restrict_words: str = None,
    restrict_settings: Dict[str, Any] = None,
    required_words: str = None,
    required_settings: Dict[str, Any] = None,
    user_id: int = None
) -> TerminologyCollectionTask:
    """
    create_terminology_collection_task函数是create_collection_task的别名
    为了保持与导入语句的兼容性而添加
    """
    logger.info(f"[术语采集服务] 使用别名函数create_terminology_collection_task创建任务")
    return create_collection_task(
        db=db,
        name=name,
        language=language,
        mode=mode,
        collection_mode=collection_mode,
        keywords=keywords,
        target_website=target_website,
        url_list=url_list,
        enable_time_range=enable_time_range,
        time_range=time_range,
        restrict_words=restrict_words,
        restrict_settings=restrict_settings,
        required_words=required_words,
        required_settings=required_settings,
        user_id=user_id
    )

# 添加get_collection_task函数
def get_collection_task(db: Session, task_id: str, user_id: Optional[int] = None) -> Optional[Dict[str, Any]]:
    """
    获取术语采集任务详情
    
    Args:
        db: 数据库会话
        task_id: 任务ID
        user_id: 用户ID，如果提供，则检查任务是否属于该用户
        
    Returns:
        Dict: 任务详情，如果任务不存在或用户无权限则返回None
    """
    logger.info(f"[术语采集服务] 获取任务详情 - 任务ID: {task_id}, 用户ID: {user_id if user_id else '未提供'}")
    
    # 查询任务
    task = db.query(TerminologyCollectionTask).filter(
        TerminologyCollectionTask.task_id == task_id
    ).first()
    
    if not task:
        logger.warning(f"[术语采集服务] 任务不存在 - 任务ID: {task_id}")
        return None
        
    # 如果提供了用户ID，检查任务是否属于该用户
    if user_id and task.user_id and task.user_id != user_id:
        logger.warning(f"[术语采集服务] 用户无权限访问任务 - 任务ID: {task_id}, 用户ID: {user_id}")
        return None
        
    # 返回任务详情
    return {
        "task_id": task.task_id,
        "name": task.name,
        "language": task.language,
        "mode": task.mode,
        "collection_mode": task.collection_mode,
        "keywords": task.keywords,
        "target_website": task.target_website,
        "url_list": task.url_list,
        "status": task.status,
        "progress": task.progress,
        "created_at": task.created_at.isoformat() if task.created_at else None,
        "updated_at": task.updated_at.isoformat() if task.updated_at else None,
        "completed_at": task.completed_at.isoformat() if task.completed_at else None,
        "user_id": task.user_id
    }

# 添加list_user_collection_tasks函数
def list_user_collection_tasks(db: Session, user_id: int, limit: int = 10, offset: int = 0) -> Dict[str, Any]:
    """
    获取用户的术语采集任务列表
    
    Args:
        db: 数据库会话
        user_id: 用户ID
        limit: 每页数量
        offset: 偏移量
        
    Returns:
        Dict: 包含任务列表和分页信息的字典
    """
    logger.info(f"[术语采集服务] 获取用户任务列表 - 用户ID: {user_id}, 偏移量: {offset}, 数量: {limit}")
    
    # 查询总数
    total = db.query(TerminologyCollectionTask).filter(
        TerminologyCollectionTask.user_id == user_id
    ).count()
    
    # 查询任务列表
    tasks = db.query(TerminologyCollectionTask).filter(
        TerminologyCollectionTask.user_id == user_id
    ).order_by(
        desc(TerminologyCollectionTask.created_at)
    ).offset(offset).limit(limit).all()
    
    # 格式化任务列表
    task_list = []
    for task in tasks:
        task_list.append({
            "task_id": task.task_id,
            "name": task.name,
            "language": task.language,
            "mode": task.mode,
            "status": task.status,
            "progress": task.progress,
            "created_at": task.created_at.isoformat() if task.created_at else None,
            "completed_at": task.completed_at.isoformat() if task.completed_at else None
        })
    
    # 返回结果
    return {
        "tasks": task_list,
        "total": total,
        "limit": limit,
        "offset": offset
    }

# 添加list_collection_task_results函数
def list_collection_task_results(db: Session, task_id: str, limit: int = 10, offset: int = 0) -> Dict[str, Any]:
    """
    获取术语采集任务的结果列表
    
    Args:
        db: 数据库会话
        task_id: 任务ID
        limit: 每页数量
        offset: 偏移量
        
    Returns:
        Dict: 包含结果列表和分页信息的字典
    """
    logger.info(f"[术语采集服务] 获取任务结果列表 - 任务ID: {task_id}, 偏移量: {offset}, 数量: {limit}")
    
    # 查询任务
    task = db.query(TerminologyCollectionTask).filter(
        TerminologyCollectionTask.task_id == task_id
    ).first()
    
    if not task:
        logger.warning(f"[术语采集服务] 任务不存在 - 任务ID: {task_id}")
        return {
            "results": [],
            "total": 0,
            "limit": limit,
            "offset": offset,
            "error": "任务不存在"
        }
    
    # 查询总数
    total = db.query(TerminologyCollectionResult).filter(
        TerminologyCollectionResult.task_id == task_id
    ).count()
    
    # 查询结果列表
    results = db.query(TerminologyCollectionResult).filter(
        TerminologyCollectionResult.task_id == task_id
    ).order_by(
        desc(TerminologyCollectionResult.confidence)
    ).offset(offset).limit(limit).all()
    
    # 格式化结果列表
    result_list = []
    for result in results:
        result_list.append({
            "id": result.id,
            "task_id": result.task_id,
            "term": result.term,
            "definition": result.definition,
            "pos": result.pos,
            "confidence": result.confidence,
            "source_url": result.source_url,
            "context": result.context,
            "created_at": result.created_at.isoformat() if result.created_at else None
        })
    
    # 返回结果
    return {
        "task": {
            "task_id": task.task_id,
            "name": task.name,
            "status": task.status
        },
        "results": result_list,
        "total": total,
        "limit": limit,
        "offset": offset
    }

async def process_collection_task_async(task_id: str, db: Session):
    """
    异步处理术语采集任务
    """
    logger.info(f"[术语采集服务] 开始处理任务 - 任务ID: {task_id}")
    
    try:
        # 获取任务
        task = db.query(TerminologyCollectionTask).filter(TerminologyCollectionTask.task_id == task_id).first()
        
        if not task:
            logger.error(f"[术语采集服务] 任务不存在 - 任务ID: {task_id}")
            return
        
        # 更新任务状态为处理中
        task.status = "processing"
        db.commit()
        
        # 根据任务模式执行不同的采集逻辑
        if task.mode == "smart":
            await process_smart_collection(db, task)
        elif task.mode == "single":
            await process_single_site_collection(db, task)
        elif task.mode == "list":
            await process_url_list_collection(db, task)
        else:
            logger.error(f"[术语采集服务] 不支持的采集模式 - 模式: {task.mode}")
            task.status = "failed"
            task.error_message = f"不支持的采集模式: {task.mode}"
            db.commit()
        
        # 如果任务状态仍为处理中，则更新为已完成
        if task.status == "processing":
            task.status = "completed"
            task.progress = 100
            task.completed_at = datetime.now()
            db.commit()
            
            logger.info(f"[术语采集服务] 任务完成 - 任务ID: {task_id}")
            
            # 查询采集结果数量
            result_count = db.query(TerminologyCollectionResult).filter(
                TerminologyCollectionResult.task_id == task_id
            ).count()
            
            logger.info(f"[术语采集服务] 任务完成 - 结果数量: {result_count}")
    
    except Exception as e:
        logger.error(f"[术语采集服务] 处理任务出错 - 任务ID: {task_id}, 错误: {str(e)}")
        logger.error(traceback.format_exc())
        
        # 更新任务状态为失败
        try:
            task = db.query(TerminologyCollectionTask).filter(TerminologyCollectionTask.task_id == task_id).first()
            if task:
                task.status = "failed"
                task.error_message = str(e)
                db.commit()
                logger.info(f"[术语采集服务] 已将任务状态更新为失败 - 任务ID: {task_id}")
        except Exception as update_error:
            logger.error(f"[术语采集服务] 更新任务状态失败 - 任务ID: {task_id}, 错误: {str(update_error)}")

def process_collection_task(task_id: str, db_session: Session):
    """
    处理术语采集任务的包装函数，用于后台任务
    """
    # 创建事件循环
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    
    try:
        # 执行异步任务
        loop.run_until_complete(process_collection_task_async(task_id, db_session))
    finally:
        # 关闭事件循环
        loop.close()

# 创建一个日志装饰器，用于记录函数的执行时间和结果
def log_execution_time(func):
    """
    装饰器，用于记录函数的执行时间和结果
    """
    async def wrapper(*args, **kwargs):
        start_time = datetime.now()
        logger.info(f"[术语采集服务] 开始执行 {func.__name__} - 开始时间: {start_time}")
        
        try:
            result = await func(*args, **kwargs)
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            logger.info(f"[术语采集服务] 完成执行 {func.__name__} - 耗时: {duration:.2f}秒")
            return result
        except Exception as e:
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            logger.error(f"[术语采集服务] 执行 {func.__name__} 出错 - 耗时: {duration:.2f}秒, 错误: {str(e)}")
            logger.error(traceback.format_exc())
            raise
    
    return wrapper

# 添加一个详细诊断函数，用于记录术语采集任务的详细信息
async def diagnose_task(task: TerminologyCollectionTask) -> Dict[str, Any]:
    """
    对术语采集任务进行诊断，并返回详细信息
    """
    try:
        task_info = {
            "task_id": task.task_id,
            "name": task.name,
            "type": task.type,
            "keywords": task.keywords,
            "language": task.language,
            "progress": task.progress,
            "status": task.status,
            "created_at": task.created_at.isoformat() if task.created_at else None,
            "updated_at": task.updated_at.isoformat() if task.updated_at else None,
            "completed_at": task.completed_at.isoformat() if task.completed_at else None,
        }
        
        # 根据不同的任务类型，添加不同的诊断信息
        if task.type == "smart":
            task_info["diagnose"] = "智能全网采集任务"
            task_info["keywords_list"] = task.keywords.split(",") if task.keywords else []
        elif task.type == "single_site":
            task_info["diagnose"] = "单一站点采集任务"
            task_info["target_website"] = task.target_website
        elif task.type == "url_list":
            task_info["diagnose"] = "链接列表采集任务"
            task_info["url_list"] = task.url_list
            task_info["url_count"] = len(task.url_list) if task.url_list else 0
        
        logger.info(f"[术语采集服务] 任务诊断: {task_info}")
        return task_info
    except Exception as e:
        logger.error(f"[术语采集服务] 任务诊断出错: {str(e)}")
        logger.error(traceback.format_exc())
        return {"error": str(e)}

@log_execution_time
async def process_smart_collection(db: Session, task: TerminologyCollectionTask):
    """
    智能全网采集模式
    """
    logger.info(f"[术语采集服务] 智能全网采集 - 任务ID: {task.task_id}, 关键词: {task.keywords}")
    
    try:
        # 实际执行智能采集过程
        keywords = task.keywords.split(',') if task.keywords else []
        if not keywords:
            raise ValueError("关键词不能为空，智能全网采集需要至少一个关键词")
        
        # 获取任务语言设置
        language = task.language or "zh"
        is_english = language.startswith("en")
        
        # 步骤1: 分析关键词
        logger.info(f"[术语采集服务] 步骤1/5: 分析关键词 - 任务ID: {task.task_id}")
        task.progress = 20
        db.commit()
        
        # 分析关键词，确定搜索策略
        search_keywords = []
        for keyword in keywords:
            search_keywords.append(keyword.strip())
            # 如果是复合词且是中文，可以考虑添加其分词结果
            if len(keyword) > 3 and ' ' not in keyword and not is_english:
                parts = jieba.lcut(keyword)
                if len(parts) > 1:
                    search_keywords.extend(parts)
        
        logger.info(f"[术语采集服务] 分析后的搜索关键词: {search_keywords}")
        
        # 步骤2: 查找相关网站
        logger.info(f"[术语采集服务] 步骤2/5: 查找相关网站 - 任务ID: {task.task_id}")
        task.progress = 40
        db.commit()
        
        # 使用搜索关键词查找相关网站
        relevant_urls = []
        
        # 根据语言设置选择不同的备选网站
        if is_english:
            # 英文备选网站
            fallback_urls = [
                "https://www.caranddriver.com/",
                "https://www.motortrend.com/",
                "https://www.edmunds.com/",
                "https://www.autocar.co.uk/",
                "https://www.carmagazine.co.uk/",
                "https://www.topgear.com/",
                "https://www.autoexpress.co.uk/",
                "https://www.automobilemag.com/",
                "https://www.roadandtrack.com/"
            ]
        else:
            # 中文备选网站
            fallback_urls = [
                "https://auto.sina.com.cn/",
                "https://auto.qq.com/",
                "https://auto.163.com/",
                "https://www.autohome.com.cn/",
                "https://www.cheshi.com/",
                "https://www.xcar.com.cn/",
                "https://www.pcauto.com.cn/",
                "https://www.dcdapp.com/",
                "https://www.dongchedi.com/"
            ]
        
        try:
            # 对每个关键词进行搜索
            for keyword in search_keywords[:3]:  # 限制只使用前3个关键词搜索
                # 根据语言选择不同的搜索引擎
                if is_english:
                    search_engines = [
                        f"https://www.google.com/search?q={keyword}",
                        f"https://www.bing.com/search?q={keyword}",
                        f"https://search.yahoo.com/search?p={keyword}"
                    ]
                else:
                    search_engines = [
                        f"https://www.baidu.com/s?wd={keyword}",
                        f"https://www.sogou.com/web?query={keyword}",
                        f"https://cn.bing.com/search?q={keyword}"
                    ]
                
                for search_url in search_engines:
                    try:
                        # 随机选择User-Agent，避免被封
                        headers = {'User-Agent': random.choice(USER_AGENTS)}
                        
                        # 添加重试机制
                        max_retries = 3
                        retry_count = 0
                        response = None
                        
                        while retry_count < max_retries:
                            try:
                                response = requests.get(search_url, headers=headers, timeout=10)
                                if response.status_code == 200:
                                    break
                                retry_count += 1
                                await asyncio.sleep(1)  # 重试间隔
                            except (requests.exceptions.Timeout, requests.exceptions.ConnectionError):
                                retry_count += 1
                                await asyncio.sleep(1)  # 重试间隔
                                continue
                        
                        if response and response.status_code == 200:
                            # 解析搜索结果页面，提取链接
                            soup = BeautifulSoup(response.text, 'html.parser')
                            links = soup.find_all('a', href=True)
                            
                            for link in links:
                                href = link['href']
                                # 过滤有效的URL（跳过搜索引擎内部链接）
                                if href.startswith('http') and 'baidu.com' not in href and 'sogou.com' not in href and 'bing.com' not in href and 'google.com' not in href and 'yahoo.com' not in href:
                                    # 检查是否是真实的URL而不是JavaScript链接
                                    if not href.startswith('javascript:') and not href.startswith('#'):
                                        relevant_urls.append(href)
                        else:
                            logger.warning(f"[术语采集服务] 搜索引擎请求失败 - URL: {search_url}, 状态码: {response.status_code if response else 'None'}")
                    except Exception as e:
                        logger.warning(f"[术语采集服务] 搜索关键词 '{keyword}' 出错: {str(e)}")
                        continue
            
            # 去重
            relevant_urls = list(set(relevant_urls))
            logger.info(f"[术语采集服务] 找到 {len(relevant_urls)} 个相关网站")
            
        except Exception as search_error:
            logger.error(f"[术语采集服务] 查找相关网站出错: {str(search_error)}")
            logger.warning(f"[术语采集服务] 使用备选网站列表")
            relevant_urls = fallback_urls
        
        # 如果没有找到任何URL，使用备选列表
        if not relevant_urls:
            logger.warning(f"[术语采集服务] 未找到任何相关网站，使用备选网站列表")
            relevant_urls = fallback_urls
        
        # 限制URL数量，避免过多请求
        relevant_urls = relevant_urls[:10]
        
        # 步骤3: 抓取网页内容
        logger.info(f"[术语采集服务] 步骤3/5: 抓取网页内容 - 任务ID: {task.task_id}")
        task.progress = 60
        db.commit()
        
        # 抓取网页内容
        content_list = []
        for url in relevant_urls:
            try:
                # 随机选择User-Agent，避免被封
                headers = {'User-Agent': random.choice(USER_AGENTS)}
                
                # 添加重试机制
                max_retries = 3
                retry_count = 0
                response = None
                
                while retry_count < max_retries:
                    try:
                        response = requests.get(url, headers=headers, timeout=15)
                        if response.status_code == 200:
                            break
                        retry_count += 1
                        await asyncio.sleep(1)  # 重试间隔
                    except (requests.exceptions.Timeout, requests.exceptions.ConnectionError):
                        retry_count += 1
                        await asyncio.sleep(1)  # 重试间隔
                        continue
                
                # 检查状态码
                if response and response.status_code == 200:
                    # 尝试检测编码
                    if response.encoding == 'ISO-8859-1':
                        response.encoding = response.apparent_encoding
                    
                    # 解析HTML
                    soup = BeautifulSoup(response.text, 'html.parser')
                    
                    # 去除脚本和样式
                    for script in soup(["script", "style"]):
                        script.extract()
                    
                    # 获取文本
                    text = soup.get_text(separator='\n')
                    
                    # 清理文本
                    lines = [line.strip() for line in text.split('\n') if line.strip()]
                    text = '\n'.join(lines)
                    
                    # 检查语言匹配度
                    text_language = detect_text_language(text)
                    if (is_english and text_language == "en") or (not is_english and text_language == "zh"):
                        # 语言匹配，保存文本和来源URL
                        if text and len(text) > 100:  # 确保文本有足够内容
                            content_list.append({
                                'text': text,
                                'url': url
                            })
                            logger.info(f"[术语采集服务] 成功抓取网页: {url}，内容长度: {len(text)}")
                        else:
                            logger.warning(f"[术语采集服务] 抓取的网页内容太少或为空: {url}")
                    else:
                        logger.warning(f"[术语采集服务] 网页语言不匹配: {url}, 期望: {language}, 检测到: {text_language}")
                else:
                    logger.warning(f"[术语采集服务] 抓取网页失败: {url}，状态码: {response.status_code if response else 'None'}")
            
            except Exception as fetch_error:
                logger.warning(f"[术语采集服务] 抓取网页出错: {url}, 错误: {str(fetch_error)}")
                continue
        
        logger.info(f"[术语采集服务] 成功抓取 {len(content_list)} 个网页")
        
        # 如果没有成功抓取任何内容，尝试使用直接抓取常见网站的方法
        if not content_list:
            logger.warning(f"[术语采集服务] 未能通过搜索引擎抓取内容，尝试直接抓取常见网站")
            
            # 根据语言选择不同的直接抓取网站
            if is_english:
                direct_urls = [
                    "https://www.caranddriver.com/news/",
                    "https://www.motortrend.com/news/",
                    "https://www.edmunds.com/car-news/",
                    "https://www.autocar.co.uk/car-news"
                ]
            else:
                direct_urls = [
                    "https://www.autohome.com.cn/all/",
                    "https://auto.qq.com/",
                    "https://auto.163.com/",
                    "https://www.dongchedi.com/article/"
                ]
            
            # 直接抓取这些网站
            for url in direct_urls:
                try:
                    # 随机选择User-Agent，避免被封
                    headers = {'User-Agent': random.choice(USER_AGENTS)}
                    
                    # 添加重试机制
                    max_retries = 3
                    retry_count = 0
                    response = None
                    
                    while retry_count < max_retries:
                        try:
                            response = requests.get(url, headers=headers, timeout=15)
                            if response.status_code == 200:
                                break
                            retry_count += 1
                            await asyncio.sleep(1)  # 重试间隔
                        except (requests.exceptions.Timeout, requests.exceptions.ConnectionError):
                            retry_count += 1
                            await asyncio.sleep(1)  # 重试间隔
                            continue
                    
                    # 检查状态码
                    if response and response.status_code == 200:
                        # 尝试检测编码
                        if response.encoding == 'ISO-8859-1':
                            response.encoding = response.apparent_encoding
                        
                        # 解析HTML
                        soup = BeautifulSoup(response.text, 'html.parser')
                        
                        # 去除脚本和样式
                        for script in soup(["script", "style"]):
                            script.extract()
                        
                        # 获取文本
                        text = soup.get_text(separator='\n')
                        
                        # 清理文本
                        lines = [line.strip() for line in text.split('\n') if line.strip()]
                        text = '\n'.join(lines)
                        
                        # 检查语言匹配度
                        text_language = detect_text_language(text)
                        if (is_english and text_language == "en") or (not is_english and text_language == "zh"):
                            # 语言匹配，保存文本和来源URL
                            if text and len(text) > 100:  # 确保文本有足够内容
                                content_list.append({
                                    'text': text,
                                    'url': url
                                })
                                logger.info(f"[术语采集服务] 成功直接抓取网页: {url}，内容长度: {len(text)}")
                            else:
                                logger.warning(f"[术语采集服务] 直接抓取的网页内容太少或为空: {url}")
                        else:
                            logger.warning(f"[术语采集服务] 网页语言不匹配: {url}, 期望: {language}, 检测到: {text_language}")
                    else:
                        logger.warning(f"[术语采集服务] 直接抓取网页失败: {url}，状态码: {response.status_code if response else 'None'}")
                
                except Exception as fetch_error:
                    logger.warning(f"[术语采集服务] 直接抓取网页出错: {url}, 错误: {str(fetch_error)}")
                    continue
        
        # 如果仍然没有抓取到内容，生成示例结果
        if not content_list:
            logger.warning(f"[术语采集服务] 未能抓取任何网页内容，生成示例结果")
            
            # 调用示例结果生成函数
            await generate_sample_results(db, task)
            
            # 更新任务状态
            task.status = "completed"
            task.progress = 100
            task.completed_at = datetime.now()
            db.commit()
            
            logger.info(f"[术语采集服务] 智能全网采集完成（示例模式） - 任务ID: {task.task_id}")
            return
        
        # 步骤4: 提取术语
        logger.info(f"[术语采集服务] 步骤4/5: 提取术语 - 任务ID: {task.task_id}")
        task.progress = 80
        db.commit()
        
        # 合并所有文本
        all_text = ""
        for content in content_list:
            all_text += content['text'] + "\n\n"
        
        # 导入术语提取器
        try:
            sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
            from terminology_extractor import TerminologyExtractor
            
            # 创建术语提取器，传入语言参数
            extractor = TerminologyExtractor(language=language)
            
            # 提取术语
            results = extractor.extract_all_methods(all_text, topK=500)
            
            # 合并结果
            merged_terms = extractor.merge_results(results)
            
            # 不限制结果数量，使用所有提取到的术语
            # merged_terms = merged_terms[:100]  # 删除这行限制
            
            logger.info(f"[术语采集服务] 成功提取 {len(merged_terms)} 个术语")
            
            # 准备术语结果
            extracted_terms = []
            for term, score in merged_terms:
                # 查找术语在哪个内容中出现
                found = False
                context = ""
                source_url = ""
                
                for content in content_list:
                    text = content['text']
                    if term in text:
                        found = True
                        source_url = content['url']
                        
                        # 提取上下文
                        try:
                            # 找到术语在文本中的位置
                            pos = text.find(term)
                            
                            # 提取前后各100个字符作为上下文
                            start = max(0, pos - 100)
                            end = min(len(text), pos + len(term) + 100)
                            
                            # 提取上下文
                            context = text[start:end]
                            
                            # 如果上下文不是以完整的句子开始或结束，尝试调整
                            if start > 0:
                                # 找到上一个句号或换行符的位置
                                prev_period = max(context.find('.', 0, 100), context.find('\n', 0, 100))
                                if prev_period > 0:
                                    context = context[prev_period+1:]
                            
                            if end < len(text):
                                # 找到下一个句号或换行符的位置
                                next_period = min(context.find('.', len(context)-100), context.find('\n', len(context)-100))
                                if next_period > 0:
                                    context = context[:next_period+1]
                            
                            break
                        except Exception as context_error:
                            logger.warning(f"[术语采集服务] 提取上下文出错: {str(context_error)}")
                            context = ""
                
                # 如果没有找到术语在任何内容中出现，使用空上下文
                if not found:
                    context = ""
                    source_url = ""
                
                # 添加到提取的术语列表
                extracted_terms.append({
                    'term': term,
                    'score': score,
                    'context': context,
                    'source': source_url
                })
            
            # 步骤5: 保存结果
            logger.info(f"[术语采集服务] 步骤5/5: 保存结果 - 任务ID: {task.task_id}")
            task.progress = 90
            db.commit()
            
            # 保存提取的术语到数据库
            await save_extracted_terms(db, task, extracted_terms)
            
            # 更新任务状态
            task.status = "completed"
            task.progress = 100
            task.completed_at = datetime.now()
            db.commit()
            
            logger.info(f"[术语采集服务] 智能全网采集完成 - 任务ID: {task.task_id}")
        
        except ImportError as e:
            logger.error(f"[术语采集服务] 导入术语提取器失败: {str(e)}")
            
            # 回退到示例结果
            logger.warning(f"[术语采集服务] 回退到示例模式")
            await generate_sample_results(db, task)
            
            # 更新任务状态
            task.status = "completed"
            task.progress = 100
            task.completed_at = datetime.now()
            db.commit()
            
            logger.info(f"[术语采集服务] 智能全网采集完成（示例模式） - 任务ID: {task.task_id}")
        
        except Exception as e:
            logger.error(f"[术语采集服务] 提取术语出错: {str(e)}")
            logger.error(traceback.format_exc())
            
            # 回退到示例结果
            logger.warning(f"[术语采集服务] 回退到示例模式")
            await generate_sample_results(db, task)
            
            # 更新任务状态
            task.status = "completed"
            task.progress = 100
            task.completed_at = datetime.now()
            db.commit()
            
            logger.info(f"[术语采集服务] 智能全网采集完成（示例模式） - 任务ID: {task.task_id}")
    
    except Exception as e:
        logger.error(f"[术语采集服务] 智能全网采集出错 - 任务ID: {task.task_id}, 错误: {str(e)}")
        logger.error(traceback.format_exc())
        
        # 更新任务状态为失败
        task.status = "failed"
        task.error_message = str(e)
        db.commit()
        
        raise

# 添加语言检测函数
def detect_text_language(text: str) -> str:
    """
    检测文本语言
    
    Args:
        text: 输入文本
        
    Returns:
        str: 语言代码，"zh"表示中文，"en"表示英文，"unknown"表示未知
    """
    try:
        # 如果文本为空，返回未知
        if not text or len(text.strip()) == 0:
            return "unknown"
        
        # 计算中文字符和英文字符的数量
        chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', text))
        english_chars = len(re.findall(r'[a-zA-Z]', text))
        
        # 计算总有效字符数（排除空格和标点）
        total_chars = len(re.findall(r'[\w\u4e00-\u9fff]', text))
        
        # 如果总字符数为0，返回未知
        if total_chars == 0:
            return "unknown"
        
        # 计算中文和英文字符的比例
        chinese_ratio = chinese_chars / total_chars
        english_ratio = english_chars / total_chars
        
        # 判断语言
        if chinese_ratio >= 0.2:  # 如果中文字符比例超过20%，认为是中文
            return "zh"
        elif english_ratio >= 0.5:  # 如果英文字符比例超过50%，认为是英文
            return "en"
        else:
            # 尝试使用langdetect库进行检测
            try:
                from langdetect import detect
                return detect(text)
            except:
                # 如果无法检测，根据字符比例决定
                return "zh" if chinese_ratio > english_ratio else "en"
    except Exception as e:
        logger.error(f"[术语采集服务] 检测文本语言出错: {str(e)}")
        return "unknown"

@log_execution_time
async def process_single_site_collection(db: Session, task: TerminologyCollectionTask):
    """
    单一站点采集模式
    """
    logger.info(f"[术语采集服务] 单一站点采集 - 任务ID: {task.task_id}, 目标网站: {task.target_website}")
    
    try:
        # 检查目标网站是否有效
        if not task.target_website:
            raise ValueError("目标网站URL不能为空")
        
        target_url = task.target_website
        # 确保URL格式正确
        if not target_url.startswith('http'):
            target_url = 'https://' + target_url
        
        # 步骤1: 分析网站结构
        logger.info(f"[术语采集服务] 步骤1/4: 分析网站结构 - 任务ID: {task.task_id}")
        task.progress = 25
        db.commit()
        
        # 尝试获取网站首页
        try:
            headers = {'User-Agent': random.choice(USER_AGENTS)}
            response = requests.get(target_url, headers=headers, timeout=15)
            
            if response.status_code != 200:
                logger.warning(f"[术语采集服务] 无法访问目标网站: {target_url}, 状态码: {response.status_code}")
                raise ValueError(f"无法访问目标网站: {target_url}, 状态码: {response.status_code}")
                
            # 尝试检测编码
            if response.encoding == 'ISO-8859-1':
                response.encoding = response.apparent_encoding
                
            # 解析HTML
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 提取所有链接，用于后续抓取
            internal_links = []
            base_domain = target_url.split('//', 1)[1].split('/', 1)[0]  # 提取域名
            
            for link in soup.find_all('a', href=True):
                href = link['href']
                # 处理相对链接
                if href.startswith('/'):
                    full_url = target_url.rstrip('/') + href
                    internal_links.append(full_url)
                # 处理同域名的链接
                elif href.startswith('http') and base_domain in href:
                    internal_links.append(href)
            
            # 去重
            internal_links = list(set(internal_links))
            # 限制链接数量
            internal_links = internal_links[:10]  # 最多抓取10个内部页面
            
            # 确保包含首页
            if target_url not in internal_links:
                internal_links.insert(0, target_url)
                
            logger.info(f"[术语采集服务] 找到 {len(internal_links)} 个内部链接")
            
        except Exception as e:
            logger.warning(f"[术语采集服务] 分析网站结构出错: {str(e)}")
            # 如果无法分析网站结构，只使用目标URL
            internal_links = [target_url]
        
        # 步骤2: 抓取网页内容
        logger.info(f"[术语采集服务] 步骤2/4: 抓取网页内容 - 任务ID: {task.task_id}")
        task.progress = 50
        db.commit()
        
        # 抓取网页内容
        content_list = []
        for url in internal_links:
            try:
                headers = {'User-Agent': random.choice(USER_AGENTS)}
                response = requests.get(url, headers=headers, timeout=15)
                
                # 检查状态码
                if response.status_code == 200:
                    # 尝试检测编码
                    if response.encoding == 'ISO-8859-1':
                        response.encoding = response.apparent_encoding
                    
                    # 解析HTML
                    soup = BeautifulSoup(response.text, 'html.parser')
                    
                    # 去除脚本和样式
                    for script in soup(["script", "style"]):
                        script.extract()
                    
                    # 获取文本
                    text = soup.get_text(separator='\n')
                    
                    # 清理文本
                    lines = [line.strip() for line in text.split('\n') if line.strip()]
                    text = '\n'.join(lines)
                    
                    # 保存文本和来源URL
                    if text and len(text) > 100:  # 确保文本有足够内容
                        content_list.append({
                            'text': text,
                            'url': url
                        })
                        logger.info(f"[术语采集服务] 成功抓取网页: {url}，内容长度: {len(text)}")
                else:
                    logger.warning(f"[术语采集服务] 抓取网页失败: {url}，状态码: {response.status_code}")
            
            except Exception as fetch_error:
                logger.warning(f"[术语采集服务] 抓取网页出错: {url}, 错误: {str(fetch_error)}")
                continue
        
        logger.info(f"[术语采集服务] 成功抓取 {len(content_list)} 个网页")
        
        # 如果没有成功抓取任何内容，抛出异常
        if not content_list:
            raise ValueError(f"未能成功抓取任何网页内容，目标网站: {target_url}")
        
        # 步骤3: 提取术语
        logger.info(f"[术语采集服务] 步骤3/4: 提取术语 - 任务ID: {task.task_id}")
        task.progress = 75
        db.commit()
        
        # 合并所有文本以进行术语提取
        all_text = "\n".join([item['text'] for item in content_list])
        
        # 使用术语提取器提取术语
        extracted_terms = []
        try:
            # 尝试导入术语提取器
            sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
            from terminology_extractor import TerminologyExtractor
            
            # 创建术语提取器
            extractor = TerminologyExtractor()
            
            # 提取术语
            results = extractor.extract_all_methods(all_text, topK=200)
            
            # 合并结果
            merged_terms = extractor.merge_results(results)
            
            # 转换为所需格式
            for term, score in merged_terms:
                # 找出术语出现的文本和URL
                term_source = None
                term_context = ""
                
                for content in content_list:
                    if term in content['text']:
                        # 找到术语所在位置
                        pos = content['text'].find(term)
                        
                        # 提取上下文（前后50个字符）
                        start = max(0, pos - 50)
                        end = min(len(content['text']), pos + len(term) + 50)
                        context = content['text'][start:end].replace('\n', ' ')
                        
                        term_source = content['url']
                        term_context = f"...{context}..."
                        break
                
                # 如果找不到具体上下文，使用第一个内容源
                if not term_source and content_list:
                    term_source = content_list[0]['url']
                    term_context = f"在网站 {term_source} 发现术语 '{term}'，但无法提取具体上下文。"
                
                extracted_terms.append({
                    'term': term,
                    'score': score,
                    'source': term_source,
                    'context': term_context
                })
            
            logger.info(f"[术语采集服务] 成功提取 {len(extracted_terms)} 个术语")
            
        except Exception as extract_error:
            logger.error(f"[术语采集服务] 提取术语出错: {str(extract_error)}")
            logger.error(traceback.format_exc())
            # 如果术语提取失败，使用关键词本身作为术语（如果有），或者使用URL中的关键词
            keywords = task.keywords.split(',') if task.keywords else []
            if not keywords:
                # 从URL中提取可能的关键词
                domain_parts = base_domain.split('.')
                if len(domain_parts) > 1:
                    keywords = [domain_parts[0]]
            
            extracted_terms = [{'term': kw, 'score': 1.0, 'source': target_url, 'context': f'从网站 {target_url} 推断的术语'} for kw in keywords]
        
        # 步骤4: 保存结果
        logger.info(f"[术语采集服务] 步骤4/4: 保存结果 - 任务ID: {task.task_id}")
        
        if extracted_terms:
            # 如果成功提取到术语，保存它们
            await save_extracted_terms(db, task, extracted_terms)
        else:
            # 如果未能提取到术语，生成示例结果
            logger.warning(f"[术语采集服务] 未能提取到真实术语，将使用示例数据 - 任务ID: {task.task_id}")
            await generate_sample_results(db, task, source_url=task.target_website)
        
        task.progress = 100
        task.status = "completed"
        task.completed_at = datetime.now()
        db.commit()
        
        logger.info(f"[术语采集服务] 单一站点采集完成 - 任务ID: {task.task_id}")
    
    except Exception as e:
        logger.error(f"[术语采集服务] 单一站点采集出错 - 任务ID: {task.task_id}, 错误: {str(e)}")
        logger.error(traceback.format_exc())
        raise

@log_execution_time
async def process_url_list_collection(db: Session, task: TerminologyCollectionTask):
    """
    链接列表采集模式
    """
    logger.info(f"[术语采集服务] 链接列表采集 - 任务ID: {task.task_id}")
    
    try:
        # 检查URL列表是否有效
        url_list = task.url_list
        if not url_list or not isinstance(url_list, list) or len(url_list) == 0:
            raise ValueError("URL列表不能为空")
        
        logger.info(f"[术语采集服务] URL列表数量: {len(url_list)}")
        
        # 验证URL格式
        valid_urls = []
        for url in url_list:
            # 跳过空URL
            if not url or not url.strip():
                continue
                
            # 确保URL格式正确
            if not url.startswith('http'):
                url = 'https://' + url
                
            valid_urls.append(url)
            
        if not valid_urls:
            raise ValueError("没有有效的URL")
            
        logger.info(f"[术语采集服务] 有效URL数量: {len(valid_urls)}")
        
        # 开始抓取网页内容
        content_list = []
        total_urls = len(valid_urls)
        
        for i, url in enumerate(valid_urls):
            # 更新进度
            progress = int((i / total_urls) * 100)
            task.progress = progress
            db.commit()
            
            logger.info(f"[术语采集服务] 处理URL {i+1}/{total_urls}: {url}")
            
            try:
                # 抓取网页内容
                headers = {'User-Agent': random.choice(USER_AGENTS)}
                response = requests.get(url, headers=headers, timeout=15)
                
                # 检查状态码
                if response.status_code == 200:
                    # 尝试检测编码
                    if response.encoding == 'ISO-8859-1':
                        response.encoding = response.apparent_encoding
                    
                    # 解析HTML
                    soup = BeautifulSoup(response.text, 'html.parser')
                    
                    # 去除脚本和样式
                    for script in soup(["script", "style"]):
                        script.extract()
                    
                    # 获取文本
                    text = soup.get_text(separator='\n')
                    
                    # 清理文本
                    lines = [line.strip() for line in text.split('\n') if line.strip()]
                    text = '\n'.join(lines)
                    
                    # 保存文本和来源URL
                    if text and len(text) > 100:  # 确保文本有足够内容
                        content_list.append({
                            'text': text,
                            'url': url
                        })
                        logger.info(f"[术语采集服务] 成功抓取网页: {url}，内容长度: {len(text)}")
                else:
                    logger.warning(f"[术语采集服务] 抓取网页失败: {url}，状态码: {response.status_code}")
            
            except Exception as fetch_error:
                logger.warning(f"[术语采集服务] 抓取网页出错: {url}, 错误: {str(fetch_error)}")
                continue
        
        logger.info(f"[术语采集服务] 成功抓取 {len(content_list)} 个网页")
        
        # 如果没有成功抓取任何内容，抛出异常
        if not content_list:
            raise ValueError("未能成功抓取任何网页内容")
        
        # 提取术语
        logger.info(f"[术语采集服务] 从抓取的内容中提取术语")
        
        # 合并所有文本以进行术语提取
        all_text = "\n".join([item['text'] for item in content_list])
        
        # 使用术语提取器提取术语
        extracted_terms = []
        try:
            # 尝试导入术语提取器
            sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
            from terminology_extractor import TerminologyExtractor
            
            # 创建术语提取器
            extractor = TerminologyExtractor()
            
            # 提取术语
            results = extractor.extract_all_methods(all_text, topK=200)
            
            # 合并结果
            merged_terms = extractor.merge_results(results)
            
            # 转换为所需格式
            for term, score in merged_terms:
                # 找出术语出现的文本和URL
                term_source = None
                term_context = ""
                
                for content in content_list:
                    if term in content['text']:
                        # 找到术语所在位置
                        pos = content['text'].find(term)
                        
                        # 提取上下文（前后50个字符）
                        start = max(0, pos - 50)
                        end = min(len(content['text']), pos + len(term) + 50)
                        context = content['text'][start:end].replace('\n', ' ')
                        
                        term_source = content['url']
                        term_context = f"...{context}..."
                        break
                
                # 如果找不到具体上下文，使用第一个内容源
                if not term_source and content_list:
                    term_source = content_list[0]['url']
                    term_context = f"在相关文本中发现术语 '{term}'，但无法提取具体上下文。"
                
                extracted_terms.append({
                    'term': term,
                    'score': score,
                    'source': term_source,
                    'context': term_context
                })
            
            logger.info(f"[术语采集服务] 成功提取 {len(extracted_terms)} 个术语")
            
        except Exception as extract_error:
            logger.error(f"[术语采集服务] 提取术语出错: {str(extract_error)}")
            logger.error(traceback.format_exc())
            # 如果术语提取失败，使用内容中的一些关键词作为术语
            extracted_terms = []
        
        # 保存结果
        logger.info(f"[术语采集服务] 保存提取的术语")
        
        if extracted_terms:
            # 如果成功提取到术语，保存它们
            await save_extracted_terms(db, task, extracted_terms)
        else:
            # 如果未能提取到术语，生成示例结果
            logger.warning(f"[术语采集服务] 未能提取到真实术语，将使用示例数据 - 任务ID: {task.task_id}")
            await generate_sample_results(db, task, url_count=len(url_list))
        
        task.progress = 100
        task.status = "completed"
        task.completed_at = datetime.now()
        db.commit()
        
        logger.info(f"[术语采集服务] 链接列表采集完成 - 任务ID: {task.task_id}")
    
    except Exception as e:
        logger.error(f"[术语采集服务] 链接列表采集出错 - 任务ID: {task.task_id}, 错误: {str(e)}")
        logger.error(traceback.format_exc())
        raise

async def generate_sample_results(db: Session, task: TerminologyCollectionTask, source_url=None, url_count=None):
    """
    生成术语结果
    使用术语提取器从文本中提取真实术语
    """
    try:
        logger.info(f"[术语采集任务] 开始生成术语结果 - 任务ID: {task.task_id}, 名称: {task.name}, 关键词: {task.keywords}")
        
        # 检查语言设置
        language = task.language or "zh"
        is_english = language.startswith("en")
        
        # 获取任务文本
        text = ""
        if hasattr(task, 'source_text') and task.source_text:
            text = task.source_text
        else:
            # 如果没有源文本，使用关键词和名称作为文本，并添加一些与领域相关的内容
            # 检测可能的领域
            domain = "通用"
            try:
                # 先进行简单的关键词匹配
                keywords_lower = task.keywords.lower() if task.keywords else ""
                name_lower = task.name.lower() if task.name else ""
                
                if "汽车" in task.keywords or "车" in task.name or "auto" in keywords_lower or "car" in keywords_lower:
                    domain = "汽车"
                    # 添加汽车领域的示例文本
                    if is_english:
                        text = f"""
                        {task.name} {task.keywords}
                        Automobiles are an important part of modern transportation, consisting of engines, chassis, body, and electrical equipment. The engine is the power source of the car, divided into gasoline engines, diesel engines, electric motors, and other types.
                        The transmission can change the output speed and torque of the engine, including manual transmissions, automatic transmissions, dual-clutch transmissions, etc. The chassis includes the transmission system, running system, steering system, and braking system.
                        Modern cars are generally equipped with ABS anti-lock braking systems, ESP electronic stability programs, traction control systems, and other safety configurations. With the development of technology, autonomous driving and new energy vehicles have become industry development trends.
                        Electric vehicles use motors as power sources, with advantages of zero emissions and low noise, but also face problems such as short range and long charging time. Hybrid vehicles combine the advantages of internal combustion engines and electric motors.
                        """
                    else:
                        text = f"""
                        {task.name} {task.keywords}
                        汽车是现代交通工具的重要组成部分，由发动机、底盘、车身和电气设备组成。发动机是汽车的动力来源，分为汽油发动机、柴油发动机、电动机等类型。
                        变速箱可以改变发动机输出的转速和转矩，包括手动变速箱、自动变速箱、双离合变速箱等。底盘包括传动系统、行驶系统、转向系统和制动系统。
                        现代汽车普遍配备ABS防抱死制动系统、ESP电子稳定程序、牵引力控制系统等安全配置。随着科技发展，自动驾驶、新能源汽车成为行业发展趋势。
                        电动汽车使用电机作为动力源，具有零排放、低噪音等优点，但也面临续航里程短、充电时间长等问题。混合动力汽车结合了内燃机和电动机的优势。
                        """
                elif "医疗" in task.keywords or "医" in task.name or "病" in task.name or "medical" in keywords_lower or "health" in keywords_lower:
                    domain = "医疗"
                    # 添加医疗领域的示例文本
                    if is_english:
                        text = f"""
                        {task.name} {task.keywords}
                        Medicine is the field concerning the prevention, diagnosis, and treatment of diseases. Hospitals provide various medical services, including outpatient, inpatient, and emergency services. Doctors are responsible for diagnosing diseases and formulating treatment plans.
                        Common diseases include colds, flu, pneumonia, etc. Treatment methods include drug therapy, surgical treatment, physical therapy, etc. Medicines are divided into prescription drugs and over-the-counter drugs, which need to be used according to medical advice.
                        Medical imaging technologies include X-rays, CT, MRI, etc., used to examine internal body conditions. Blood tests can analyze various components in the blood to help diagnose diseases.
                        Chronic diseases such as hypertension and diabetes require long-term management. Preventive measures include vaccination, regular physical examination, healthy lifestyle, etc. Medical insurance can reduce the economic burden of patients.
                        """
                    else:
                        text = f"""
                        {task.name} {task.keywords}
                        医疗是关于预防、诊断和治疗疾病的领域。医院提供各种医疗服务，包括门诊、住院、急诊等。医生负责诊断疾病并制定治疗方案。
                        常见疾病包括感冒、流感、肺炎等。治疗方法包括药物治疗、手术治疗、物理治疗等。药物分为处方药和非处方药，需要根据医嘱使用。
                        医学影像技术包括X光、CT、核磁共振等，用于检查身体内部情况。血液检查可以分析血液中的各种成分，帮助诊断疾病。
                        慢性病如高血压、糖尿病需要长期管理。预防措施包括接种疫苗、定期体检、健康生活方式等。医疗保险可以减轻患者的经济负担。
                        """
                elif "法律" in task.keywords or "法" in task.name or "legal" in keywords_lower or "law" in keywords_lower:
                    domain = "法律"
                    # 添加法律领域的示例文本
                    if is_english:
                        text = f"""
                        {task.name} {task.keywords}
                        Law is a system of rules that regulates social behavior. Courts are venues for resolving disputes, including supreme courts, intermediate courts, and basic courts. Lawyers provide legal consultation and representation services for clients.
                        Litigation includes civil litigation, criminal litigation, and administrative litigation. A contract is a legally binding agreement that requires the parties to fulfill the agreed obligations. Intellectual property includes copyright, patent rights, and trademark rights.
                        Criminal behavior will be punished by criminal law, including fines, imprisonment, etc. Legal aid provides free legal services for economically disadvantaged people. Arbitration is another way to resolve commercial disputes.
                        Marriage law stipulates the conditions and procedures for marriage and divorce. Labor law protects the rights and interests of workers, stipulating working hours, wages, social insurance, etc. Environmental law regulates environmental protection and pollution prevention.
                        """
                    else:
                        text = f"""
                        {task.name} {task.keywords}
                        法律是规范社会行为的规则体系。法院是解决争议的场所，包括最高法院、中级法院和基层法院。律师为当事人提供法律咨询和代理服务。
                        诉讼包括民事诉讼、刑事诉讼和行政诉讼。合同是具有法律约束力的协议，要求当事人履行约定的义务。知识产权包括著作权、专利权和商标权。
                        犯罪行为会受到刑法的惩罚，包括罚金、监禁等。法律援助为经济困难的人提供免费法律服务。仲裁是解决商业争议的另一种方式。
                        婚姻法规定结婚、离婚的条件和程序。劳动法保护劳动者的权益，规定工作时间、工资、社会保险等。环境法规范环境保护和污染防治。
                        """
                elif "技术" in task.keywords or "软件" in task.name or "计算机" in task.keywords or "tech" in keywords_lower or "software" in keywords_lower or "computer" in keywords_lower:
                    domain = "IT"
                    # 添加IT领域的示例文本
                    if is_english:
                        text = f"""
                        {task.name} {task.keywords}
                        Information Technology (IT) is the technology for processing information. Computer systems include two parts: hardware and software. Hardware includes physical components such as CPU, memory, hard disk, etc. Software is divided into system software and application software.
                        Programming languages are used to develop software, common ones include Java, Python, C++, etc. Databases are used to store and manage data, such as MySQL, MongoDB. Network technology enables communication between devices.
                        The Internet is a global computer network. Cloud computing provides on-demand computing resource services. Artificial intelligence simulates human intelligence, and machine learning is an important branch. Big data technology processes large amounts of data.
                        Information security protects data from unauthorized access or destruction. Software development follows the process of requirement analysis, design, coding, testing, and maintenance. Agile development is an iterative development method.
                        """
                    else:
                        text = f"""
                        {task.name} {task.keywords}
                        信息技术（IT）是处理信息的技术。计算机系统包括硬件和软件两部分。硬件包括CPU、内存、硬盘等物理组件。软件分为系统软件和应用软件。
                        编程语言用于开发软件，常见的有Java、Python、C++等。数据库用于存储和管理数据，如MySQL、MongoDB。网络技术实现设备间的通信。
                        互联网是全球性的计算机网络。云计算提供按需的计算资源服务。人工智能模拟人类智能，机器学习是其重要分支。大数据技术处理大量数据。
                        信息安全保护数据不被未授权访问或破坏。软件开发遵循需求分析、设计、编码、测试、维护的流程。敏捷开发是一种迭代式开发方法。
                        """
                else:
                    # 使用通用文本
                    if is_english:
                        text = f"""
                        {task.name} {task.keywords}
                        Professional terms and concepts about {task.keywords}. A terminology is a word or phrase with specific meaning in a particular field, used to accurately express concepts and knowledge in that field.
                        Terms are usually defined by experts and used in professional literature and communications. The accuracy and uniformity of terminology are crucial for professional communication.
                        A term can be a single word, a phrase, or an abbreviation. The definition of professional terminology may change with time and discipline development.
                        Terminology extraction is the process of identifying and extracting terms from text, which can be achieved through linguistic methods, statistical methods, or a combination of both.
                        The {task.keywords} field has a rich terminology system, which forms the basis of knowledge in this field.
                        """
                    else:
                        text = f"""
                        {task.name} {task.keywords}
                        关于{task.keywords}的专业术语和概念。专业术语是特定领域内具有特定含义的词汇或短语，用于准确表达该领域的概念和知识。
                        术语通常由专家定义，并在专业文献和通信中使用。术语的准确性和统一性对专业交流至关重要。
                        术语可以是单个词，也可以是短语或缩写。专业术语的定义可能随时间和学科发展而变化。
                        术语提取是从文本中识别和抽取术语的过程，可以通过语言学方法、统计方法或两者结合来实现。
                        {task.keywords}领域拥有丰富的专业术语系统，这些术语构成了该领域知识的基础。
                        """
                    logger.warning(f"[术语采集任务] 任务没有源文本，使用任务名称和关键词作为替代 - 任务ID: {task.task_id}")
            except Exception as e:
                # 如果出错，使用简单文本
                if is_english:
                    text = f"{task.name} {task.keywords} - Sample text for terminology extraction in English."
                else:
                    text = f"{task.name} {task.keywords} - 术语提取示例文本。"
                logger.warning(f"[术语采集任务] 生成领域文本出错: {str(e)}, 使用简单文本替代 - 任务ID: {task.task_id}")
        
        # 检测文本领域
        try:
            # 尝试导入领域检测工具
            sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
            from utils.domain_detector import detect_domain
            domain = detect_domain(text)
            logger.info(f"[术语采集任务] 检测到文本领域: {domain}")
        except ImportError:
            # 如果导入失败，使用简单的领域检测
            domain = determine_domain(task.name, task.keywords) if 'domain' not in locals() else domain
            logger.info(f"[术语采集任务] 使用简单领域检测: {domain}")
        
        # 导入术语提取器
        try:
            sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))
            from terminology_extractor import TerminologyExtractor
            # 使用任务的语言设置创建术语提取器
            extractor = TerminologyExtractor(language=language)
            logger.info(f"[术语采集任务] 成功导入术语提取器，语言设置: {language}")
        except ImportError as e:
            logger.error(f"[术语采集任务] 导入术语提取器失败: {str(e)}")
            # 回退到旧的生成样本函数
            return generate_sample_results_fallback(db, task, source_url, url_count)
        
        # 使用术语提取器提取术语
        try:
            # 提取术语
            logger.info(f"[术语采集任务] 开始提取术语...")
            results = extractor.extract_all_methods(text, topK=500)  # 增加提取数量到500
            
            # 合并结果
            merged_terms = extractor.merge_results(results)
            
            # 不再限制结果数量，使用所有提取到的术语
            # merged_terms = merged_terms[:100]  # 删除这行限制
            
            logger.info(f"[术语采集任务] 成功提取 {len(merged_terms)} 个术语")
            
            # 如果提取的术语太少，添加一些特定领域的通用术语
            if len(merged_terms) < 10:
                logger.warning(f"[术语采集任务] 提取的术语数量过少，添加领域通用术语")
                
                # 根据领域和语言添加一些通用术语
                domain_terms = []
                if domain == "汽车":
                    if is_english:
                        domain_terms = [
                            ("engine", 0.95), ("transmission", 0.93), ("chassis", 0.91), ("tire", 0.89),
                            ("suspension", 0.87), ("brake", 0.85), ("ABS", 0.83), ("ESP", 0.81),
                            ("turbocharger", 0.79), ("autonomous driving", 0.77), ("fuel consumption", 0.75), ("power", 0.73)
                        ]
                    else:
                        domain_terms = [
                            ("发动机", 0.95), ("变速箱", 0.93), ("底盘", 0.91), ("轮胎", 0.89),
                            ("悬挂", 0.87), ("刹车", 0.85), ("ABS", 0.83), ("ESP", 0.81),
                            ("涡轮增压", 0.79), ("自动驾驶", 0.77), ("油耗", 0.75), ("动力", 0.73)
                        ]
                elif domain == "医疗":
                    if is_english:
                        domain_terms = [
                            ("hospital", 0.95), ("doctor", 0.93), ("disease", 0.91), ("treatment", 0.89),
                            ("medication", 0.87), ("surgery", 0.85), ("examination", 0.83), ("diagnosis", 0.81),
                            ("symptoms", 0.79), ("prevention", 0.77), ("nursing", 0.75), ("rehabilitation", 0.73)
                        ]
                    else:
                        domain_terms = [
                            ("医院", 0.95), ("医生", 0.93), ("疾病", 0.91), ("治疗", 0.89),
                            ("药物", 0.87), ("手术", 0.85), ("检查", 0.83), ("诊断", 0.81),
                            ("症状", 0.79), ("预防", 0.77), ("护理", 0.75), ("康复", 0.73)
                        ]
                elif domain == "法律":
                    if is_english:
                        domain_terms = [
                            ("contract", 0.95), ("litigation", 0.93), ("lawyer", 0.91), ("court", 0.89),
                            ("judgment", 0.87), ("arbitration", 0.85), ("regulation", 0.83), ("rights", 0.81),
                            ("obligations", 0.79), ("responsibility", 0.77), ("damages", 0.75), ("appeal", 0.73)
                        ]
                    else:
                        domain_terms = [
                            ("合同", 0.95), ("诉讼", 0.93), ("律师", 0.91), ("法院", 0.89),
                            ("判决", 0.87), ("仲裁", 0.85), ("法规", 0.83), ("权利", 0.81),
                            ("义务", 0.79), ("责任", 0.77), ("赔偿", 0.75), ("上诉", 0.73)
                        ]
                elif domain == "IT":
                    if is_english:
                        domain_terms = [
                            ("software", 0.95), ("hardware", 0.93), ("database", 0.91), ("algorithm", 0.89),
                            ("network", 0.87), ("programming", 0.85), ("cloud computing", 0.83), ("API", 0.81),
                            ("interface", 0.79), ("framework", 0.77), ("security", 0.75), ("encryption", 0.73)
                        ]
                    else:
                        domain_terms = [
                            ("软件", 0.95), ("硬件", 0.93), ("数据库", 0.91), ("算法", 0.89),
                            ("网络", 0.87), ("编程", 0.85), ("云计算", 0.83), ("接口", 0.81),
                            ("界面", 0.79), ("框架", 0.77), ("安全", 0.75), ("加密", 0.73)
                        ]
                else:
                    # 通用领域
                    if is_english:
                        domain_terms = [
                            ("system", 0.95), ("method", 0.93), ("process", 0.91), ("analysis", 0.89),
                            ("technology", 0.87), ("concept", 0.85), ("structure", 0.83), ("function", 0.81),
                            ("development", 0.79), ("management", 0.77), ("research", 0.75), ("design", 0.73)
                        ]
                    else:
                        domain_terms = [
                            ("系统", 0.95), ("方法", 0.93), ("过程", 0.91), ("分析", 0.89),
                            ("技术", 0.87), ("概念", 0.85), ("结构", 0.83), ("功能", 0.81),
                            ("开发", 0.79), ("管理", 0.77), ("研究", 0.75), ("设计", 0.73)
                        ]
                
                # 合并提取的术语和领域术语
                merged_terms = domain_terms + merged_terms
                
                # 重新排序
                merged_terms.sort(key=lambda x: x[1], reverse=True)
                
                logger.info(f"[术语采集任务] 添加领域通用术语后，术语总数: {len(merged_terms)}")
            
            # 保存术语到数据库
            extracted_terms = []
            for term, score in merged_terms:
                extracted_terms.append({
                    'term': term,
                    'score': float(score),
                    'context': f"从示例文本中提取的术语: {term}",
                    'source': source_url or "示例模式"
                })
            
            # 保存提取的术语到数据库
            await save_extracted_terms(db, task, extracted_terms)
            
            logger.info(f"[术语采集任务] 示例结果生成完成，保存了 {len(extracted_terms)} 个术语")
            
            return len(extracted_terms)
        
        except Exception as e:
            logger.error(f"[术语采集任务] 生成示例结果出错: {str(e)}")
            logger.error(traceback.format_exc())
            
            # 回退到旧的生成样本函数
            return generate_sample_results_fallback(db, task, source_url, url_count)
    
    except Exception as e:
        logger.error(f"[术语采集任务] 生成示例结果出错: {str(e)}")
        logger.error(traceback.format_exc())
        
        # 回退到旧的生成样本函数
        return generate_sample_results_fallback(db, task, source_url, url_count)

async def save_extracted_terms(db: Session, task: TerminologyCollectionTask, extracted_terms: List[Dict]):
    """
    保存提取的术语
    
    Args:
        db: 数据库会话
        task: 术语采集任务
        extracted_terms: 提取的术语列表
    
    Returns:
        int: 保存的术语数量
    """
    logger.info(f"[术语采集服务] 开始保存术语 - 任务ID: {task.task_id}, 术语数量: {len(extracted_terms)}")
    
    # 检查语言设置
    language = task.language or "zh"
    is_english = language.startswith("en")
    
    # 获取任务领域
    domain = "通用"
    if hasattr(task, 'domain') and task.domain:
        domain = task.domain
    
    # 保存术语
    saved_count = 0
    for term_data in extracted_terms:
        try:
            term = term_data.get('term', '')
            if not term:
                continue
            
            # 获取术语数据
            confidence = term_data.get('score', 0.0)
            source_url = term_data.get('source_url', '')
            context = term_data.get('context', '')
            
            # 生成术语定义
            definition = term_data.get('definition', '')
            if not definition:
                # 如果没有提供定义，尝试从上下文提取或生成
                if context:
                    definition = extract_definition_from_context(term, context, domain, is_english)
                else:
                    definition = generate_generic_definition(term, domain, is_english)
            
            # 确定词性
            pos = term_data.get('pos', '')
            if not pos:
                pos = determine_part_of_speech(term, is_english)
            
            # 创建术语结果
            term_result = TerminologyCollectionResult(
                task_id=task.task_id,
                term=term,
                definition=definition,
                pos=pos,
                confidence=confidence,
                source_url=source_url,
                context=context
            )
            
            # 保存到数据库
            db.add(term_result)
            saved_count += 1
            
            # 每100个术语提交一次，避免事务过大
            if saved_count % 100 == 0:
                db.commit()
                logger.info(f"[术语采集服务] 已保存 {saved_count} 个术语")
        
        except Exception as e:
            logger.warning(f"[术语采集服务] 保存术语 '{term}' 出错: {str(e)}")
            continue
    
    # 最后提交
    db.commit()
    logger.info(f"[术语采集服务] 保存术语完成 - 任务ID: {task.task_id}, 保存数量: {saved_count}")
    
    return saved_count

def extract_definition_from_context(term: str, context: str, domain: str = "通用", is_english: bool = False) -> str:
    """
    从上下文中提取术语定义
    
    Args:
        term: 术语
        context: 术语出现的上下文
        domain: 领域
        is_english: 是否为英文
    
    Returns:
        str: 提取的定义
    """
    # 如果上下文为空，使用通用定义
    if not context:
        return generate_generic_definition(term, domain, is_english)
    
    try:
        # 简单的定义提取逻辑
        # 查找常见的定义模式
        definition = ""
        
        if is_english:
            # 英文定义模式
            patterns = [
                rf"{re.escape(term)}\s+is\s+([^\.]+)\.",
                rf"{re.escape(term)}\s+are\s+([^\.]+)\.",
                rf"{re.escape(term)}\s+refers\s+to\s+([^\.]+)\.",
                rf"{re.escape(term)}\s+means\s+([^\.]+)\.",
                rf"{re.escape(term)}\s+describes\s+([^\.]+)\.",
                rf"{re.escape(term)}\s+consists\s+of\s+([^\.]+)\.",
                rf"{re.escape(term)}:\s+([^\.]+)\.",
                rf"([^\.]+)\s+is\s+called\s+{re.escape(term)}\.",
                rf"([^\.]+)\s+are\s+called\s+{re.escape(term)}\."
            ]
        else:
            # 中文定义模式
            patterns = [
                rf"{re.escape(term)}是([^。，；]+)[。，；]",
                rf"{re.escape(term)}指([^。，；]+)[。，；]",
                rf"{re.escape(term)}为([^。，；]+)[。，；]",
                rf"{re.escape(term)}：([^。，；]+)[。，；]",
                rf"{re.escape(term)}包括([^。，；]+)[。，；]",
                rf"{re.escape(term)}由([^。，；]+)[。，；]",
                rf"([^。，；]+)被?称为{re.escape(term)}[。，；]",
                rf"([^。，；]+)被?叫做{re.escape(term)}[。，；]"
            ]
        
        for pattern in patterns:
            match = re.search(pattern, context)
            if match:
                definition = match.group(1).strip()
                break
        
        # 如果没有找到匹配的定义，使用包含术语的句子作为定义
        if not definition:
            # 将上下文分割成句子
            if is_english:
                sentences = re.split(r'[.!?]', context)
            else:
                sentences = re.split(r'[。！？]', context)
            
            # 查找包含术语的句子
            for sentence in sentences:
                if term in sentence:
                    # 清理和截断句子
                    definition = sentence.strip()
                    # 如果句子太长，截断它
                    if len(definition) > 100:
                        definition = definition[:100] + "..."
                    break
        
        # 如果仍然没有定义，使用通用定义
        if not definition:
            definition = generate_generic_definition(term, domain, is_english)
        
        return definition
    
    except Exception as e:
        logger.warning(f"[术语采集服务] 从上下文提取定义出错: {str(e)}")
        return generate_generic_definition(term, domain, is_english)

def generate_generic_definition(term: str, domain: str, is_english: bool) -> str:
    """
    生成通用的术语定义
    
    Args:
        term: 术语
        domain: 领域
        is_english: 是否为英文
    
    Returns:
        str: 生成的定义
    """
    try:
        # 根据领域生成不同的定义模板
        if domain == "汽车":
            if is_english:
                templates = [
                    f"{term} is an important component or concept in the automotive field.",
                    f"{term} refers to a specific part or technology used in automobiles.",
                    f"{term} is a term used in the automotive industry to describe a specific feature or process."
                ]
            else:
                templates = [
                    f"{term}是汽车领域的重要组成部分或概念。",
                    f"{term}指汽车中使用的特定零部件或技术。",
                    f"{term}是汽车行业用来描述特定功能或过程的术语。"
                ]
        elif domain == "医疗":
            if is_english:
                templates = [
                    f"{term} is a medical term related to health, disease diagnosis, or treatment.",
                    f"{term} refers to a specific condition, procedure, or medical concept.",
                    f"{term} is used in the medical field to describe a particular health-related aspect."
                ]
            else:
                templates = [
                    f"{term}是与健康、疾病诊断或治疗相关的医学术语。",
                    f"{term}指特定的疾病状况、医疗程序或医学概念。",
                    f"{term}在医学领域用于描述特定的健康相关方面。"
                ]
        elif domain == "法律":
            if is_english:
                templates = [
                    f"{term} is a legal concept or procedure in the legal system.",
                    f"{term} refers to a specific legal right, obligation, or status.",
                    f"{term} is used in legal practice to describe a particular aspect of law or legal proceedings."
                ]
            else:
                templates = [
                    f"{term}是法律体系中的法律概念或程序。",
                    f"{term}指特定的法律权利、义务或状态。",
                    f"{term}在法律实践中用于描述法律或法律程序的特定方面。"
                ]
        elif domain == "IT":
            if is_english:
                templates = [
                    f"{term} is a technical concept or component in information technology.",
                    f"{term} refers to a specific technology, protocol, or software feature.",
                    f"{term} is used in IT to describe a particular aspect of computing or data processing."
                ]
            else:
                templates = [
                    f"{term}是信息技术中的技术概念或组件。",
                    f"{term}指特定的技术、协议或软件功能。",
                    f"{term}在IT领域用于描述计算或数据处理的特定方面。"
                ]
        else:
            # 通用领域
            if is_english:
                templates = [
                    f"{term} is a specialized term or concept in its field.",
                    f"{term} refers to a specific aspect, process, or entity.",
                    f"{term} is used to describe a particular characteristic or function."
                ]
            else:
                templates = [
                    f"{term}是该领域的专业术语或概念。",
                    f"{term}指特定的方面、过程或实体。",
                    f"{term}用于描述特定的特征或功能。"
                ]
        
        # 随机选择一个模板
        import random
        definition = random.choice(templates)
        
        return definition
    
    except Exception as e:
        logger.warning(f"[术语采集服务] 生成通用定义出错: {str(e)}")
        if is_english:
            return f"{term} is a specialized term in its field."
        else:
            return f"{term}是该领域的专业术语。"

def determine_part_of_speech(term: str, is_english: bool) -> str:
    """
    确定术语的词性
    
    Args:
        term: 术语
        is_english: 是否为英文
    
    Returns:
        str: 词性标记
    """
    try:
        # 尝试使用NLTK进行词性标注（英文）
        if is_english:
            try:
                import nltk
                from nltk.tokenize import word_tokenize
                
                # 确保必要的NLTK数据已下载
                try:
                    nltk.data.find('tokenizers/punkt')
                except LookupError:
                    nltk.download('punkt', quiet=True)
                
                try:
                    nltk.data.find('taggers/averaged_perceptron_tagger')
                except LookupError:
                    nltk.download('averaged_perceptron_tagger', quiet=True)
                
                # 分词和词性标注
                tokens = word_tokenize(term)
                tagged = nltk.pos_tag(tokens)
                
                # 获取最后一个词的词性
                last_tag = tagged[-1][1]
                
                # 将NLTK的词性标签转换为更简单的形式
                if last_tag.startswith('NN'):  # 名词
                    return "noun"
                elif last_tag.startswith('VB'):  # 动词
                    return "verb"
                elif last_tag.startswith('JJ'):  # 形容词
                    return "adj"
                elif last_tag.startswith('RB'):  # 副词
                    return "adv"
                else:
                    # 根据术语的长度和结构进行简单判断
                    words = term.split()
                    if len(words) > 1:
                        return "noun"  # 多词术语通常是名词短语
                    else:
                        # 根据词尾判断
                        if term.endswith(('tion', 'sion', 'ment', 'ness', 'ity', 'ing', 'er', 'or')):
                            return "noun"
                        elif term.endswith(('ize', 'ise', 'ate', 'fy', 'en')):
                            return "verb"
                        elif term.endswith(('al', 'ful', 'ous', 'ive', 'able', 'ible')):
                            return "adj"
                        elif term.endswith('ly'):
                            return "adv"
                        else:
                            return "noun"  # 默认为名词
            except Exception as e:
                logger.warning(f"[术语采集服务] 使用NLTK标注词性失败: {str(e)}")
                # 回退到简单的启发式方法
                words = term.split()
                if len(words) > 1:
                    return "noun"  # 多词术语通常是名词短语
                else:
                    # 根据词尾判断
                    if term.endswith(('tion', 'sion', 'ment', 'ness', 'ity', 'ing', 'er', 'or')):
                        return "noun"
                    elif term.endswith(('ize', 'ise', 'ate', 'fy', 'en')):
                        return "verb"
                    elif term.endswith(('al', 'ful', 'ous', 'ive', 'able', 'ible')):
                        return "adj"
                    elif term.endswith('ly'):
                        return "adv"
                    else:
                        return "noun"  # 默认为名词
        else:
            # 中文词性标注
            try:
                import jieba.posseg as pseg
                words = pseg.cut(term)
                pos_list = [w.flag for w in words]
                
                # 获取最后一个词的词性
                if pos_list:
                    last_pos = pos_list[-1]
                    
                    # 将jieba的词性标签转换为更简单的形式
                    if last_pos.startswith('n'):  # 名词
                        return "名词"
                    elif last_pos.startswith('v'):  # 动词
                        return "动词"
                    elif last_pos.startswith('a'):  # 形容词
                        return "形容词"
                    elif last_pos.startswith('d'):  # 副词
                        return "副词"
                    else:
                        return "名词"  # 默认为名词
                else:
                    return "名词"
            except Exception as e:
                logger.warning(f"[术语采集服务] 使用jieba标注词性失败: {str(e)}")
                # 对于中文，大多数术语是名词
                return "名词"
    except Exception as e:
        logger.warning(f"[术语采集服务] 确定词性出错: {str(e)}")
        # 默认返回名词
        return "noun" if is_english else "名词"

def cancel_collection_task_service(db: Session, task_id: str, user_id: int) -> bool:
    """
    取消术语采集任务
    返回：是否成功取消
    """
    logger.info(f"[术语采集服务] 取消任务 - 任务ID: {task_id}, 用户ID: {user_id}")
    
    # 查询任务
    task = db.query(TerminologyCollectionTask).filter(
        TerminologyCollectionTask.task_id == task_id,
        TerminologyCollectionTask.user_id == user_id
    ).first()
    
    if not task:
        logger.warning(f"[术语采集服务] 未找到任务 - 任务ID: {task_id}")
        return False
    
    # 检查任务状态，只有pending和processing状态的任务才能取消
    if task.status not in ["pending", "processing"]:
        logger.warning(f"[术语采集服务] 任务状态不允许取消 - 任务ID: {task_id}, 状态: {task.status}")
        return False
    
    # 更新任务状态为已取消
    task.status = "canceled"
    task.progress = 0
    db.commit()
    
    logger.info(f"[术语采集服务] 任务已取消 - 任务ID: {task_id}")
    return True

def get_task_status(db: Session, task_id: str, user_id: int) -> Optional[Dict[str, Any]]:
    """
    获取任务状态
    """
    logger.info(f"[术语采集服务] 获取任务状态 - 任务ID: {task_id}, 用户ID: {user_id}")
    
    # 查询任务
    task = db.query(TerminologyCollectionTask).filter(
        TerminologyCollectionTask.task_id == task_id,
        TerminologyCollectionTask.user_id == user_id
    ).first()
    
    if not task:
        logger.warning(f"[术语采集服务] 未找到任务 - 任务ID: {task_id}")
        return None
    
    # 返回任务状态
    return {
        "id": task.task_id,
        "status": task.status,
        "progress": task.progress,
        "error_message": task.error_message
    }

def get_task_detail(db: Session, task_id: str, user_id: int = None) -> Optional[Dict[str, Any]]:
    """
    获取任务详情
    
    Args:
        db: 数据库会话
        task_id: 任务ID
        user_id: 用户ID，用于权限检查
    
    Returns:
        Dict: 任务详情字典
    """
    logger.info(f"[术语采集服务] 获取任务详情 - 任务ID: {task_id}")
    
    # 查询任务
    task = db.query(TerminologyCollectionTask).filter(
        TerminologyCollectionTask.task_id == task_id
    ).first()
    
    if not task:
        logger.warning(f"[术语采集服务] 任务不存在 - 任务ID: {task_id}")
        return None
    
    # 如果提供了用户ID，检查用户是否有权访问此任务
    if user_id and task.user_id and task.user_id != user_id:
        logger.warning(f"[术语采集服务] 用户无权访问任务 - 任务ID: {task_id}, 用户ID: {user_id}")
        return None
    
    # 获取URL列表
    url_list = task.url_list if task.url_list else []
    if isinstance(url_list, str):
        try:
            url_list = json.loads(url_list)
        except:
            url_list = []
    
    # 获取任务结果
    results = db.query(TerminologyCollectionResult).filter(
        TerminologyCollectionResult.task_id == task_id
    ).all()
    
    # 检查语言设置
    language = task.language or "zh"
    is_english = language.startswith("en")
    
    # 格式化结果
    formatted_results = []
    for result in results:
        # 获取结果数据
        term = result.term
        definition = result.definition
        pos = result.pos
        confidence = result.confidence
        source_url = result.source_url
        context = result.context
        
        # 创建结果字典
        result_dict = {
            "term": term,
            "definition": definition,
            "pos": pos,
            "confidence": confidence,
            "source_url": source_url,
            "context": context
        }
        
        formatted_results.append(result_dict)
    
    # 按置信度排序
    formatted_results.sort(key=lambda x: x["confidence"], reverse=True)
    
    # 创建任务详情字典
    task_detail = {
        "task_id": task.task_id,
        "name": task.name,
        "mode": task.mode,
        "keywords": task.keywords,
        "language": task.language or "zh",
        "status": task.status,
        "progress": task.progress,
        "created_at": task.created_at.isoformat() if task.created_at else None,
        "completed_at": task.completed_at.isoformat() if task.completed_at else None,
        "url_list": url_list,
        "results": formatted_results,
        "result_count": len(formatted_results),
        "error_message": task.error_message
    }
    
    return task_detail

# 提供一个测试函数用于直接测试术语提取功能
async def test_terminology_extraction(keywords: str, url: str = None):
    """
    测试术语提取功能，用于诊断问题
    
    Args:
        keywords: 关键词
        url: 可选的测试URL
    
    Returns:
        Dict: 测试结果
    """
    logger.info(f"[术语采集测试] 开始测试术语提取 - 关键词: {keywords}, URL: {url if url else '无'}")
    
    try:
        # 1. 测试网页抓取
        content = None
        if url:
            try:
                # 随机选择User-Agent，避免被封
                headers = {'User-Agent': random.choice(USER_AGENTS)}
                
                # 设置超时时间，避免长时间等待
                response = requests.get(url, headers=headers, timeout=15)
                
                # 检查状态码
                if response.status_code == 200:
                    # 尝试检测编码
                    if response.encoding == 'ISO-8859-1':
                        response.encoding = response.apparent_encoding
                    
                    # 解析HTML
                    soup = BeautifulSoup(response.text, 'html.parser')
                    
                    # 去除脚本和样式
                    for script in soup(["script", "style"]):
                        script.extract()
                    
                    # 获取文本
                    text = soup.get_text(separator='\n')
                    
                    # 清理文本
                    lines = [line.strip() for line in text.split('\n') if line.strip()]
                    text = '\n'.join(lines)
                    
                    if text and len(text) > 100:
                        content = text
                        logger.info(f"[术语采集测试] 成功抓取网页: {url}, 内容长度: {len(text)}")
                    else:
                        logger.warning(f"[术语采集测试] 抓取的网页内容太少或为空: {url}")
                else:
                    logger.warning(f"[术语采集测试] 抓取网页失败: {url}，状态码: {response.status_code}")
            
            except Exception as fetch_error:
                logger.warning(f"[术语采集测试] 抓取网页出错: {url}, 错误: {str(fetch_error)}")
        
        # 如果未指定URL或抓取失败，则使用示例文本
        if not content:
            # 根据关键词生成示例文本
            if "汽车" in keywords or "车" in keywords:
                content = """
                汽车是现代交通工具的重要组成部分，由发动机、底盘、车身和电气设备组成。发动机是汽车的动力来源，分为汽油发动机、柴油发动机、电动机等类型。
                变速箱可以改变发动机输出的转速和转矩，包括手动变速箱、自动变速箱、双离合变速箱等。底盘包括传动系统、行驶系统、转向系统和制动系统。
                现代汽车普遍配备ABS防抱死制动系统、ESP电子稳定程序、牵引力控制系统等安全配置。随着科技发展，自动驾驶、新能源汽车成为行业发展趋势。
                电动汽车使用电机作为动力源，具有零排放、低噪音等优点，但也面临续航里程短、充电时间长等问题。混合动力汽车结合了内燃机和电动机的优势。
                汽车的性能指标包括最高时速、百公里加速时间、刹车距离、油耗等。轮胎是汽车唯一与地面接触的部件，对行驶安全有重要影响。
                汽车悬挂系统可以减轻车身震动，提高乘坐舒适性，常见的有麦弗逊悬挂、多连杆悬挂、空气悬挂等。汽车空调系统可以调节车内温度和湿度。
                """
            else:
                # 通用示例文本
                content = f"""
                关于{keywords}的专业术语和概念。专业术语是特定领域内具有特定含义的词汇或短语，用于准确表达该领域的概念和知识。
                术语通常由专家定义，并在专业文献和通信中使用。术语的准确性和统一性对专业交流至关重要。
                术语可以是单个词，也可以是短语或缩写。专业术语的定义可能随时间和学科发展而变化。
                术语提取是从文本中识别和抽取术语的过程，可以通过语言学方法、统计方法或两者结合来实现。
                {keywords}领域拥有丰富的专业术语系统，这些术语构成了该领域知识的基础。
                """
            
            logger.info(f"[术语采集测试] 使用示例文本，长度: {len(content)}")
        
        # 2. 测试术语提取
        extracted_terms = []
        try:
            # 尝试导入术语提取器
            sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
            from terminology_extractor import TerminologyExtractor
            
            # 创建术语提取器
            extractor = TerminologyExtractor()
            
            # 提取术语
            results = extractor.extract_all_methods(content, topK=100)
            
            # 合并结果
            merged_terms = extractor.merge_results(results)
            
            logger.info(f"[术语采集测试] 成功提取 {len(merged_terms)} 个术语")
            
            # 转换为所需格式
            for term, score in merged_terms:
                # 找出术语出现的上下文
                context = ""
                if term in content:
                    # 找到术语所在位置
                    pos = content.find(term)
                    
                    # 提取上下文（前后50个字符）
                    start = max(0, pos - 50)
                    end = min(len(content), pos + len(term) + 50)
                    context = content[start:end].replace('\n', ' ')
                    context = f"...{context}..."
                
                # 生成或提取定义
                definition = extract_definition_from_context(term, content)
                if not definition:
                    definition = f"与{keywords}相关的术语"
                
                extracted_terms.append({
                    'term': term,
                    'definition': definition,
                    'confidence': score,
                    'context': context
                })
            
        except Exception as extract_error:
            logger.error(f"[术语采集测试] 提取术语出错: {str(extract_error)}")
            logger.error(traceback.format_exc())
            
            # 如果提取失败，生成一些示例术语
            keywords_list = [k.strip() for k in keywords.split(',') if k.strip()]
            for i, keyword in enumerate(keywords_list):
                extracted_terms.append({
                    'term': keyword,
                    'definition': f"示例术语{i+1}，由于提取失败而生成",
                    'confidence': 0.9 - (i * 0.1),
                    'context': "示例上下文，由于术语提取失败而生成"
                })
            
            logger.info(f"[术语采集测试] 生成了 {len(extracted_terms)} 个示例术语")
        
        # 返回测试结果
        return {
            "success": True,
            "message": "术语提取测试完成",
            "content_length": len(content) if content else 0,
            "terms_count": len(extracted_terms),
            "terms": extracted_terms[:10]  # 只返回前10个术语，避免响应过大
        }
    
    except Exception as e:
        logger.error(f"[术语采集测试] 测试术语提取出错: {str(e)}")
        logger.error(traceback.format_exc())
        return {
            "success": False,
            "message": f"测试过程中出错: {str(e)}",
            "error": traceback.format_exc()
        }