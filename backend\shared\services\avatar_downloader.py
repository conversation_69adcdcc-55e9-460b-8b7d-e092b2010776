"""
真实头像下载服务
从多个免费图片源下载真实人物头像到本地
"""

import os
import asyncio
import aiohttp
import aiofiles
from pathlib import Path
from typing import List, Dict, Optional
import logging
import hashlib
import json
from datetime import datetime

logger = logging.getLogger(__name__)

class AvatarDownloader:
    def __init__(self):
        self.base_path = Path(__file__).parent.parent / "static" / "avatars"
        self.base_path.mkdir(parents=True, exist_ok=True)
        self.metadata_file = self.base_path / "metadata.json"
        self.metadata = self._load_metadata()
        
        # 真实人物图片源配置 - 使用专门的人物头像API
        self.avatar_sources = {
            'female-teacher': [
                'https://images.unsplash.com/photo-1494790108755-2616c9c1e8e3?w=400&h=400&fit=crop&crop=face',
                'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=400&h=400&fit=crop&crop=face',
                'https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?w=400&h=400&fit=crop&crop=face',
            ],
            'male-teacher': [
                'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&h=400&fit=crop&crop=face',
                'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face',
                'https://images.unsplash.com/photo-1560250097-0b93528c311a?w=400&h=400&fit=crop&crop=face',
            ],
            'female-customer-service': [
                'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=400&h=400&fit=crop&crop=face',
                'https://images.unsplash.com/photo-1580489944761-15a19d654956?w=400&h=400&fit=crop&crop=face',
                'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=400&h=400&fit=crop&crop=face',
            ],
            'male-professional': [
                'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=400&h=400&fit=crop&crop=face',
                'https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?w=400&h=400&fit=crop&crop=face',
                'https://images.unsplash.com/photo-1556157382-97eda2d62296?w=400&h=400&fit=crop&crop=face',
            ],
            'female-broadcaster': [
                # 使用专门的女性人物头像
                'https://randomuser.me/api/portraits/women/1.jpg',
                'https://randomuser.me/api/portraits/women/2.jpg',
                'https://randomuser.me/api/portraits/women/3.jpg',
            ],
            'male-broadcaster': [
                # 使用专门的男性人物头像
                'https://randomuser.me/api/portraits/men/1.jpg',
                'https://randomuser.me/api/portraits/men/2.jpg',
                'https://randomuser.me/api/portraits/men/3.jpg',
            ],
            'female-assistant': [
                # 使用专门的女性人物头像
                'https://randomuser.me/api/portraits/women/4.jpg',
                'https://randomuser.me/api/portraits/women/5.jpg',
                'https://randomuser.me/api/portraits/women/6.jpg',
            ],
            'male-assistant': [
                # 使用专门的男性人物头像
                'https://randomuser.me/api/portraits/men/4.jpg',
                'https://randomuser.me/api/portraits/men/5.jpg',
                'https://randomuser.me/api/portraits/men/6.jpg',
            ],
            'female-sales': [
                # 使用专门的女性人物头像
                'https://randomuser.me/api/portraits/women/7.jpg',
                'https://randomuser.me/api/portraits/women/8.jpg',
                'https://randomuser.me/api/portraits/women/9.jpg',
            ],
            'male-sales': [
                # 使用专门的男性人物头像
                'https://randomuser.me/api/portraits/men/7.jpg',
                'https://randomuser.me/api/portraits/men/8.jpg',
                'https://randomuser.me/api/portraits/men/9.jpg',
            ]
        }

    def _load_metadata(self) -> Dict:
        """加载头像元数据"""
        if self.metadata_file.exists():
            try:
                with open(self.metadata_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"加载元数据失败: {e}")
        return {}

    def _save_metadata(self):
        """保存头像元数据"""
        try:
            with open(self.metadata_file, 'w', encoding='utf-8') as f:
                json.dump(self.metadata, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存元数据失败: {e}")

    async def download_avatar(self, avatar_type: str, url: str, index: int = 0) -> Optional[str]:
        """下载单个头像"""
        try:
            # 生成文件名
            url_hash = hashlib.md5(url.encode()).hexdigest()[:8]
            filename = f"{avatar_type}_{index}_{url_hash}.jpg"
            file_path = self.base_path / filename
            
            # 如果文件已存在，直接返回
            if file_path.exists():
                logger.info(f"头像已存在: {filename}")
                return filename
            
            # 下载图片
            async with aiohttp.ClientSession() as session:
                async with session.get(url, timeout=aiohttp.ClientTimeout(total=30)) as response:
                    if response.status == 200:
                        content = await response.read()
                        
                        # 保存文件
                        async with aiofiles.open(file_path, 'wb') as f:
                            await f.write(content)
                        
                        # 更新元数据
                        self.metadata[filename] = {
                            'avatar_type': avatar_type,
                            'url': url,
                            'downloaded_at': datetime.now().isoformat(),
                            'file_size': len(content),
                            'index': index
                        }
                        self._save_metadata()
                        
                        logger.info(f"头像下载成功: {filename} ({len(content)} bytes)")
                        return filename
                    else:
                        logger.error(f"下载失败 {url}: HTTP {response.status}")
                        return None
                        
        except Exception as e:
            logger.error(f"下载头像失败 {url}: {e}")
            return None

    async def download_all_avatars(self, progress_callback=None) -> Dict[str, List[str]]:
        """下载所有头像"""
        results = {}
        total_count = sum(len(urls) for urls in self.avatar_sources.values())
        current_count = 0
        
        for avatar_type, urls in self.avatar_sources.items():
            results[avatar_type] = []
            
            for index, url in enumerate(urls):
                if progress_callback:
                    progress = int((current_count / total_count) * 100)
                    await progress_callback(progress, f"下载 {avatar_type} 头像 {index+1}/{len(urls)}")
                
                filename = await self.download_avatar(avatar_type, url, index)
                if filename:
                    results[avatar_type].append(filename)
                
                current_count += 1
                
                # 避免请求过于频繁
                await asyncio.sleep(0.5)
        
        if progress_callback:
            await progress_callback(100, "所有头像下载完成")
        
        logger.info(f"头像下载完成，共下载 {sum(len(files) for files in results.values())} 个文件")
        return results

    def get_avatar_path(self, avatar_type: str, index: int = 0) -> Optional[Path]:
        """获取头像文件路径"""
        # 首先查找指定index的头像文件
        for filename, metadata in self.metadata.items():
            if metadata['avatar_type'] == avatar_type and metadata['index'] == index:
                file_path = self.base_path / filename
                if file_path.exists():
                    return file_path

        # 如果指定index不存在，查找该类型的第一个可用文件
        for filename, metadata in self.metadata.items():
            if metadata['avatar_type'] == avatar_type:
                file_path = self.base_path / filename
                if file_path.exists():
                    return file_path

        return None

    def get_available_avatars(self) -> Dict[str, List[str]]:
        """获取所有可用的头像"""
        available = {}
        for filename, metadata in self.metadata.items():
            avatar_type = metadata['avatar_type']
            if avatar_type not in available:
                available[avatar_type] = []
            
            file_path = self.base_path / filename
            if file_path.exists():
                available[avatar_type].append(filename)
        
        return available

    def get_avatar_url(self, avatar_type: str, index: int = 0) -> Optional[str]:
        """获取头像的URL路径"""
        # 首先查找指定index的头像文件
        for filename, metadata in self.metadata.items():
            if metadata['avatar_type'] == avatar_type and metadata['index'] == index:
                file_path = self.base_path / filename
                if file_path.exists():
                    return f"/static/avatars/{filename}"

        # 如果指定index不存在，查找该类型的第一个可用文件
        for filename, metadata in self.metadata.items():
            if metadata['avatar_type'] == avatar_type:
                file_path = self.base_path / filename
                if file_path.exists():
                    return f"/static/avatars/{filename}"

        return None

    async def ensure_avatar_exists(self, avatar_type: str, index: int = 0) -> Optional[str]:
        """确保头像存在，如果不存在则下载"""
        url = self.get_avatar_url(avatar_type, index)
        if url:
            return url
        
        # 头像不存在，尝试下载
        if avatar_type in self.avatar_sources:
            urls = self.avatar_sources[avatar_type]
            if index < len(urls):
                filename = await self.download_avatar(avatar_type, urls[index], index)
                if filename:
                    return f"/static/avatars/{filename}"
        
        return None

    def cleanup_old_avatars(self, days: int = 30):
        """清理旧的头像文件"""
        cutoff_date = datetime.now().timestamp() - (days * 24 * 60 * 60)
        
        for filename, metadata in list(self.metadata.items()):
            try:
                downloaded_at = datetime.fromisoformat(metadata['downloaded_at'])
                if downloaded_at.timestamp() < cutoff_date:
                    file_path = self.base_path / filename
                    if file_path.exists():
                        file_path.unlink()
                    del self.metadata[filename]
                    logger.info(f"清理旧头像: {filename}")
            except Exception as e:
                logger.error(f"清理头像失败 {filename}: {e}")
        
        self._save_metadata()

# 全局实例
_avatar_downloader = None

def get_avatar_downloader() -> AvatarDownloader:
    """获取头像下载器实例"""
    global _avatar_downloader
    if _avatar_downloader is None:
        _avatar_downloader = AvatarDownloader()
    return _avatar_downloader
