#!/usr/bin/env python3
"""
测试 API 参数验证
"""
import requests
import json

def test_text_to_video_params():
    """测试文本转视频参数"""
    print("🧪 测试 Wanx 2.1 文本转视频 API 参数...")
    
    # 正确的参数格式
    correct_params = {
        "prompt": "一只可爱的橙色小猫在绿色草地上快乐地奔跑",
        "model": "t2v-1.3B",
        "duration": 3,
        "resolution": "768x512",
        "fps": 24,
        "guidance_scale": 7.5,
        "num_inference_steps": 20
    }
    
    print("📋 发送参数:")
    print(json.dumps(correct_params, indent=2, ensure_ascii=False))
    
    try:
        response = requests.post(
            "http://localhost:8000/api/v1/video/text-to-video",
            json=correct_params,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"\n📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ API 调用成功!")
            print(f"📝 响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            return result.get("task_id")
        else:
            print(f"❌ API 调用失败: {response.status_code}")
            try:
                error_detail = response.json()
                print(f"📝 错误详情: {json.dumps(error_detail, indent=2, ensure_ascii=False)}")
            except:
                print(f"📝 错误内容: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return None

def test_api_docs():
    """测试 API 文档"""
    print("\n🔍 检查 API 文档...")
    
    try:
        response = requests.get("http://localhost:8000/docs")
        if response.status_code == 200:
            print("✅ API 文档可访问: http://localhost:8000/docs")
        else:
            print(f"❌ API 文档不可访问: {response.status_code}")
    except Exception as e:
        print(f"❌ API 文档检查失败: {e}")

def main():
    """主函数"""
    print("🧪 Wanx 2.1 API 参数测试")
    print("=" * 50)
    
    # 检查 API 文档
    test_api_docs()
    
    # 测试参数
    task_id = test_text_to_video_params()
    
    if task_id:
        print(f"\n🎯 测试结果:")
        print(f"✅ API 参数验证通过")
        print(f"✅ 任务创建成功: {task_id}")
        print(f"💡 可以在前端界面测试完整流程")
    else:
        print(f"\n❌ 测试失败:")
        print(f"❌ API 参数验证失败")
        print(f"💡 请检查参数格式和后端配置")

if __name__ == "__main__":
    main()
