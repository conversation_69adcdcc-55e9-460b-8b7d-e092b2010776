#!/usr/bin/env python
"""
Celery 测试脚本
用于验证 Celery 任务队列的功能
"""

import os
import sys
import time
from datetime import datetime
import logging

# 确保能够导入后端模块
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("test_celery")

# 设置环境变量
os.environ["CELERY_BROKER_URL"] = "redis://localhost:6379/0"
os.environ["CELERY_RESULT_BACKEND"] = "redis://localhost:6379/0"

def test_celery_connection():
    """测试 Celery 连接"""
    try:
        from services.celery_app import get_celery_app
        celery_app = get_celery_app()
        
        # 检查 Celery 应用是否已创建
        if celery_app is None:
            logger.error("无法创建 Celery 应用")
            return False
            
        logger.info("成功创建 Celery 应用")
        
        # 检查 Celery broker 连接
        try:
            conn = celery_app.connection()
            conn.ensure_connection(max_retries=3)
            logger.info("成功连接到 Celery broker")
            conn.close()
        except Exception as e:
            logger.error(f"无法连接到 Celery broker: {e}")
            return False
            
        return True
    except Exception as e:
        logger.error(f"测试 Celery 连接时出错: {e}")
        return False

def test_celery_task():
    """测试 Celery 任务"""
    try:
        # 导入需要测试的任务
        try:
            from services.celery_tasks.general_tasks import execute_general_task
            logger.info("成功导入通用任务")
        except ImportError:
            logger.warning("无法导入通用任务模块，跳过任务测试")
            return False
            
        # 创建测试任务
        task_id = f"test_{int(time.time())}"
        logger.info(f"创建测试任务: {task_id}")
        
        # 执行任务
        result = execute_general_task.delay(
            task_id=task_id,
            task_type="test",
            params={"test_param": "value"},
            user_id=None
        )
        
        logger.info(f"任务已提交，任务ID: {result.id}")
        
        # 等待任务完成
        logger.info("等待任务完成...")
        try:
            task_result = result.get(timeout=10)
            logger.info(f"任务结果: {task_result}")
            return True
        except Exception as e:
            logger.error(f"获取任务结果时出错: {e}")
            return False
            
    except Exception as e:
        logger.error(f"测试 Celery 任务时出错: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("开始测试 Celery...")
    
    # 测试 Celery 连接
    logger.info("正在测试 Celery 连接...")
    if test_celery_connection():
        logger.info("✅ Celery 连接测试通过")
    else:
        logger.error("❌ Celery 连接测试失败")
        return False
        
    # 测试 Celery 任务
    logger.info("正在测试 Celery 任务...")
    if test_celery_task():
        logger.info("✅ Celery 任务测试通过")
    else:
        logger.error("❌ Celery 任务测试失败")
        return False
        
    logger.info("所有 Celery 测试通过")
    return True

if __name__ == "__main__":
    try:
        # 运行主测试函数
        result = main()
        
        if result:
            logger.info("✅ 所有测试成功")
            sys.exit(0)
        else:
            logger.error("❌ 测试失败")
            sys.exit(1)
    except Exception as e:
        logger.error(f"测试过程中发生异常: {e}")
        sys.exit(1) 