"""
修复术语API中creator_id和user_id不一致问题的脚本

问题描述：
- 数据库模型MonoCorpus类中定义的是user_id字段
- 但在API路由中却尝试访问creator_id属性，导致属性不存在错误

此脚本将自动修复该问题。
"""

import os
import re
import logging
import sys

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 需要修复的文件
TARGET_FILES = [
    "backend/routes/terminology_routes.py"
]

def fix_file(file_path):
    """替换文件中的creator_id为user_id"""
    
    if not os.path.exists(file_path):
        logger.error(f"文件不存在：{file_path}")
        return False
    
    # 读取文件内容
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 创建备份
    backup_path = file_path + '.bak'
    with open(backup_path, 'w', encoding='utf-8') as f:
        f.write(content)
    logger.info(f"已创建备份：{backup_path}")
    
    # 替换creator_id为user_id
    # 替换MonoCorpus.creator_id为MonoCorpus.user_id
    new_content = re.sub(r'MonoCorpus\.creator_id', 'MonoCorpus.user_id', content)
    
    # 替换corpus.creator_id为corpus.user_id
    new_content = re.sub(r'corpus\.creator_id', 'corpus.user_id', new_content)
    
    # 替换函数参数creator_id=为user_id=
    new_content = re.sub(r'creator_id\s*=\s*', 'user_id=', new_content)
    
    # 替换digital_human.creator_id为digital_human.user_id（如果存在）
    new_content = re.sub(r'digital_human\.creator_id', 'digital_human.user_id', new_content)
    
    # 替换term.creator_id为term.user_id
    new_content = re.sub(r'term\.creator_id', 'term.user_id', new_content)
    
    # 写入修改后的内容
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    logger.info(f"已修复文件：{file_path}")
    return True

def main():
    """主函数"""
    
    success_count = 0
    error_count = 0
    
    logger.info("开始修复术语API...")
    
    # 获取当前工作目录
    cwd = os.getcwd()
    logger.info(f"当前工作目录：{cwd}")
    
    # 如果当前不在项目根目录，尝试切换
    if not os.path.exists('backend'):
        parent_dir = os.path.dirname(cwd)
        if os.path.exists(os.path.join(parent_dir, 'backend')):
            os.chdir(parent_dir)
            logger.info(f"切换到父目录：{parent_dir}")
        else:
            logger.error("无法找到项目根目录，请在项目根目录运行此脚本")
            return False
    
    # 修复目标文件
    for file_path in TARGET_FILES:
        try:
            if fix_file(file_path):
                success_count += 1
            else:
                error_count += 1
        except Exception as e:
            logger.error(f"修复文件 {file_path} 时发生错误：{str(e)}")
            error_count += 1
    
    # 打印结果
    logger.info(f"修复完成，成功：{success_count}，失败：{error_count}")
    
    if success_count > 0 and error_count == 0:
        logger.info("所有文件已成功修复，请重启后端服务")
        return True
    else:
        logger.warning("部分文件修复失败，请检查日志")
        return False

if __name__ == "__main__":
    result = main()
    sys.exit(0 if result else 1) 