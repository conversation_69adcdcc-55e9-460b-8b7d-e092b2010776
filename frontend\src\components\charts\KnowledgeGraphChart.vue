<template>
  <div class="knowledge-graph-chart">
    <div class="graph-toolbar">
      <el-space>
        <el-button-group>
          <el-button @click="zoomIn" :icon="Plus" size="small" />
          <el-button @click="zoomOut" :icon="Minus" size="small" />
          <el-button @click="fitView" :icon="FullScreen" size="small" />
        </el-button-group>
        
        <el-select
          v-model="layoutType"
          size="small"
          style="width: 120px"
          @change="changeLayout"
        >
          <el-option label="力导向" value="force" />
          <el-option label="环形" value="circular" />
          <el-option label="层次" value="dagre" />
          <el-option label="网格" value="grid" />
        </el-select>

        <el-switch
          v-model="showLabels"
          active-text="显示标签"
          inactive-text="隐藏标签"
          @change="toggleLabels"
        />
      </el-space>
    </div>

    <div 
      ref="graphContainer" 
      class="graph-container"
      :style="{ height: containerHeight + 'px' }"
    >
      <div v-if="loading" class="graph-loading">
        <el-icon class="is-loading" size="32">
          <Loading />
        </el-icon>
        <p>正在加载知识图谱...</p>
      </div>
      
      <div v-else-if="!hasData" class="graph-empty">
        <el-icon size="48">
          <Document />
        </el-icon>
        <p>暂无数据，请选择数据源</p>
      </div>
    </div>

    <!-- 节点详情弹窗 -->
    <el-dialog
      v-model="showNodeDetail"
      :title="selectedNode?.label || '节点详情'"
      width="600px"
    >
      <div v-if="selectedNode" class="node-detail">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="类型">
            <el-tag :type="getNodeTypeColor(selectedNode.type)">
              {{ getNodeTypeText(selectedNode.type) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="标签">
            {{ selectedNode.label }}
          </el-descriptions-item>
          <el-descriptions-item label="描述">
            {{ selectedNode.description || '暂无描述' }}
          </el-descriptions-item>
          <el-descriptions-item label="相关度">
            <el-progress
              :percentage="Math.round((selectedNode.relevance || 0) * 100)"
              :color="getProgressColor(selectedNode.relevance)"
            />
          </el-descriptions-item>
          <el-descriptions-item label="连接数">
            {{ selectedNode.connections || 0 }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { defineComponent, ref, onMounted, onUnmounted, nextTick, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { Plus, Minus, FullScreen, Loading, Document } from '@element-plus/icons-vue';
import G6 from '@antv/g6';

export default defineComponent({
  name: 'KnowledgeGraphChart',
  components: {
    Plus,
    Minus,
    FullScreen,
    Loading,
    Document
  },
  props: {
    data: {
      type: Object,
      default: () => ({ nodes: [], edges: [] })
    },
    loading: {
      type: Boolean,
      default: false
    },
    height: {
      type: Number,
      default: 500
    }
  },
  emits: ['node-click', 'edge-click', 'canvas-click'],
  setup(props, { emit }) {
    const graphContainer = ref();
    const graph = ref(null);
    const showNodeDetail = ref(false);
    const selectedNode = ref(null);
    const layoutType = ref('force');
    const showLabels = ref(true);
    const containerHeight = ref(props.height);

    const hasData = ref(false);

    // 监听数据变化
    watch(() => props.data, (newData) => {
      hasData.value = newData && newData.nodes && newData.nodes.length > 0;
      if (hasData.value && graph.value) {
        updateGraphData(newData);
      }
    }, { deep: true, immediate: true });

    // 监听加载状态
    watch(() => props.loading, (loading) => {
      if (!loading && hasData.value) {
        nextTick(() => {
          initGraph();
        });
      }
    });

    const initGraph = () => {
      if (!graphContainer.value || !hasData.value) return;

      // 销毁现有图实例
      if (graph.value) {
        graph.value.destroy();
      }

      const container = graphContainer.value;
      const width = container.clientWidth;
      const height = containerHeight.value;

      // 创建G6图实例
      graph.value = new G6.Graph({
        container: container,
        width,
        height,
        modes: {
          default: [
            'drag-canvas',
            'zoom-canvas',
            'drag-node',
            {
              type: 'tooltip',
              formatText: (model) => {
                return `<div style="padding: 8px;">
                  <strong>${model.label}</strong><br/>
                  类型: ${getNodeTypeText(model.type)}<br/>
                  相关度: ${Math.round((model.relevance || 0) * 100)}%
                </div>`;
              }
            }
          ]
        },
        layout: {
          type: layoutType.value,
          preventOverlap: true,
          nodeSize: 30,
          linkDistance: 100,
          nodeStrength: -300,
          edgeStrength: 0.2,
          collideStrength: 0.8
        },
        defaultNode: {
          size: [30, 30],
          style: {
            fill: '#5B8FF9',
            stroke: '#5B8FF9',
            lineWidth: 2
          },
          labelCfg: {
            style: {
              fill: '#000',
              fontSize: 12
            },
            position: 'bottom',
            offset: 5
          }
        },
        defaultEdge: {
          style: {
            stroke: '#e2e2e2',
            lineWidth: 1,
            endArrow: {
              path: G6.Arrow.triangle(8, 10, 10),
              fill: '#e2e2e2'
            }
          },
          labelCfg: {
            autoRotate: true,
            style: {
              fill: '#666',
              fontSize: 10
            }
          }
        },
        nodeStateStyles: {
          hover: {
            fill: '#1890ff',
            stroke: '#1890ff',
            lineWidth: 3
          },
          selected: {
            fill: '#f04864',
            stroke: '#f04864',
            lineWidth: 3
          }
        },
        edgeStateStyles: {
          hover: {
            stroke: '#1890ff',
            lineWidth: 2
          }
        }
      });

      // 绑定事件
      graph.value.on('node:click', (evt) => {
        const { item } = evt;
        const model = item.getModel();
        selectedNode.value = model;
        showNodeDetail.value = true;
        emit('node-click', model);
      });

      graph.value.on('edge:click', (evt) => {
        const { item } = evt;
        const model = item.getModel();
        emit('edge-click', model);
      });

      graph.value.on('canvas:click', () => {
        emit('canvas-click');
      });

      graph.value.on('node:mouseenter', (evt) => {
        const { item } = evt;
        graph.value.setItemState(item, 'hover', true);
      });

      graph.value.on('node:mouseleave', (evt) => {
        const { item } = evt;
        graph.value.setItemState(item, 'hover', false);
      });

      // 加载数据
      updateGraphData(props.data);
    };

    const updateGraphData = (data) => {
      if (!graph.value || !data) return;

      // 处理节点数据
      const nodes = data.nodes.map(node => ({
        ...node,
        style: {
          fill: getNodeColor(node.type),
          stroke: getNodeColor(node.type),
          lineWidth: 2
        },
        size: Math.max(20, Math.min(50, (node.relevance || 0.5) * 60)),
        label: showLabels.value ? node.label : ''
      }));

      // 处理边数据
      const edges = data.edges.map(edge => ({
        ...edge,
        style: {
          stroke: getEdgeColor(edge.type),
          lineWidth: Math.max(1, (edge.weight || 0.5) * 3),
          endArrow: {
            path: G6.Arrow.triangle(8, 10, 10),
            fill: getEdgeColor(edge.type)
          }
        },
        label: edge.type
      }));

      graph.value.data({ nodes, edges });
      graph.value.render();
      graph.value.fitView();
    };

    const getNodeColor = (type) => {
      const colors = {
        event: '#1890ff',
        person: '#52c41a',
        organization: '#fa8c16',
        location: '#722ed1',
        concept: '#f5222d',
        topic: '#13c2c2'
      };
      return colors[type] || '#1890ff';
    };

    const getEdgeColor = (type) => {
      const colors = {
        causal: '#ff4d4f',
        temporal: '#1890ff',
        spatial: '#52c41a',
        semantic: '#722ed1'
      };
      return colors[type] || '#d9d9d9';
    };

    const getNodeTypeColor = (type) => {
      const colors = {
        event: 'primary',
        person: 'success',
        organization: 'warning',
        location: 'info',
        concept: 'danger',
        topic: ''
      };
      return colors[type] || '';
    };

    const getNodeTypeText = (type) => {
      const texts = {
        event: '事件',
        person: '人物',
        organization: '机构',
        location: '地点',
        concept: '概念',
        topic: '主题'
      };
      return texts[type] || '未知';
    };

    const getProgressColor = (value) => {
      if (value >= 0.8) return '#67c23a';
      if (value >= 0.6) return '#e6a23c';
      if (value >= 0.4) return '#f56c6c';
      return '#909399';
    };

    const zoomIn = () => {
      if (graph.value) {
        graph.value.zoom(1.2);
      }
    };

    const zoomOut = () => {
      if (graph.value) {
        graph.value.zoom(0.8);
      }
    };

    const fitView = () => {
      if (graph.value) {
        graph.value.fitView();
      }
    };

    const changeLayout = () => {
      if (graph.value) {
        graph.value.updateLayout({
          type: layoutType.value,
          preventOverlap: true,
          nodeSize: 30,
          linkDistance: 100
        });
      }
    };

    const toggleLabels = () => {
      if (graph.value) {
        const nodes = graph.value.getNodes();
        nodes.forEach(node => {
          const model = node.getModel();
          graph.value.updateItem(node, {
            label: showLabels.value ? model.originalLabel || model.label : ''
          });
        });
      }
    };

    const handleResize = () => {
      if (graph.value && graphContainer.value) {
        const width = graphContainer.value.clientWidth;
        const height = containerHeight.value;
        graph.value.changeSize(width, height);
        graph.value.fitView();
      }
    };

    onMounted(() => {
      if (hasData.value && !props.loading) {
        nextTick(() => {
          initGraph();
        });
      }
      
      window.addEventListener('resize', handleResize);
    });

    onUnmounted(() => {
      if (graph.value) {
        graph.value.destroy();
      }
      window.removeEventListener('resize', handleResize);
    });

    return {
      graphContainer,
      showNodeDetail,
      selectedNode,
      layoutType,
      showLabels,
      containerHeight,
      hasData,
      zoomIn,
      zoomOut,
      fitView,
      changeLayout,
      toggleLabels,
      getNodeTypeColor,
      getNodeTypeText,
      getProgressColor
    };
  }
});
</script>

<style scoped>
.knowledge-graph-chart {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.graph-toolbar {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

.graph-container {
  flex: 1;
  position: relative;
  background: #fff;
}

.graph-loading,
.graph-empty {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #999;
}

.graph-loading p,
.graph-empty p {
  margin-top: 16px;
  font-size: 16px;
}

.node-detail {
  max-height: 60vh;
  overflow-y: auto;
}

@media (max-width: 768px) {
  .graph-toolbar {
    padding: 8px 12px;
  }
  
  .graph-toolbar .el-space {
    flex-wrap: wrap;
  }
}
</style>
