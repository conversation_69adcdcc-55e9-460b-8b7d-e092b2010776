"""
简化的 Wanx 视频生成任务 - 避免数据库冲突
"""
import os
import time
from datetime import datetime
from pathlib import Path

from app.core.celery_unified import celery_app
from app.core.task_manager import task_manager

# Wanx 2.1 模型路径
WAN_OUTPUT_DIR = Path(__file__).parent.parent.parent / "storage" / "videos"
WAN_OUTPUT_DIR.mkdir(parents=True, exist_ok=True)

@celery_app.task(bind=True, name="wanx_text_to_video")
def wanx_text_to_video(self, task_id: str, prompt: str, model: str = "t2v-1.3B", 
                      duration: int = 5, resolution: str = "768x512", 
                      fps: int = 24, guidance_scale: float = 7.5, 
                      num_inference_steps: int = 50, user_id: str = "demo-user"):
    """Wanx 2.1 文本转视频任务 - 简化版本"""
    
    try:
        print(f"[Wanx视频任务] 开始: {task_id}")
        print(f"[Wanx视频任务] 提示词: {prompt}")
        
        # 更新任务记录（不创建新的，因为 API 已经创建了）
        simple_task_manager.update_task(
            task_id=task_id,
            progress=0,
            message="开始处理...",
            status='running'
        )
        
        # 更新进度：开始处理
        simple_task_manager.update_task(
            task_id=task_id,
            progress=10,
            message="开始生成视频..."
        )
        
        # 模拟视频生成过程
        for i in range(1, 6):
            time.sleep(1)  # 模拟处理时间
            progress = 10 + (i * 15)
            simple_task_manager.update_task(
                task_id=task_id,
                progress=progress,
                message=f"生成中... {progress}%"
            )
        
        # 生成输出文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_filename = f"wanx_video_{task_id}_{timestamp}.mp4"
        output_path = WAN_OUTPUT_DIR / output_filename
        
        # 模拟生成视频文件（创建一个占位符文件）
        with open(output_path, 'w') as f:
            f.write(f"# Wanx Video Placeholder\n")
            f.write(f"# Task ID: {task_id}\n")
            f.write(f"# Prompt: {prompt}\n")
            f.write(f"# Model: {model}\n")
            f.write(f"# Duration: {duration}s\n")
            f.write(f"# Resolution: {resolution}\n")
            f.write(f"# Generated: {datetime.now()}\n")
        
        # 最终进度更新（同时更新两个任务管理器）
        simple_task_manager.update_task(
            task_id=task_id,
            progress=100,
            message="视频生成完成",
            status='completed'
        )

        # 同时更新 PostgreSQL 任务状态
        try:
            task_manager.update_task(
                task_id,
                status="completed",
                progress=100,
                output_data={"video_url": video_url, "message": "视频生成完成"}
            )
        except Exception as e:
            print(f"[WARNING] Failed to update PostgreSQL task: {e}")
        
        video_url = f"/api/v1/video/download/{output_filename}"
        
        print(f"[Wanx视频任务] 完成: {task_id}")
        
        return {
            "task_id": task_id,
            "status": "completed",
            "output_file": str(output_path),
            "video_url": video_url,
            "metadata": {
                "prompt": prompt,
                "model": model,
                "duration": duration,
                "resolution": resolution,
                "fps": fps,
                "guidance_scale": guidance_scale,
                "num_inference_steps": num_inference_steps
            }
        }
        
    except Exception as e:
        error_msg = str(e)
        print(f"[Wanx视频任务] 失败: {error_msg}")
        
        # 更新错误状态
        simple_task_manager.update_task(
            task_id=task_id,
            progress=0,
            message=f"生成失败: {error_msg}"
        )
        
        return {
            "task_id": task_id,
            "status": "failed",
            "error": error_msg
        }

@celery_app.task(bind=True, name="wanx_image_to_video")
def wanx_image_to_video(self, task_id: str, prompt: str, image_path: str,
                       duration: int = 5, resolution: str = "1280x720",
                       fps: int = 24, motion_strength: float = 0.5,
                       model: str = "i2v-14B", user_id: str = "demo-user"):
    """Wanx 2.1 图片转视频任务 - 简化版本"""
    
    try:
        print(f"[Wanx图片转视频] 开始: {task_id}")
        print(f"[Wanx图片转视频] 提示词: {prompt}")
        print(f"[Wanx图片转视频] 图片: {image_path}")
        
        # 创建任务记录
        simple_task_manager.create_task(
            task_id=task_id,
            task_type='wanx_image_to_video',
            prompt=prompt,
            image_path=image_path,
            model=model,
            duration=duration,
            user_id=user_id
        )
        
        # 模拟处理过程
        for i in range(1, 6):
            time.sleep(1)
            progress = i * 20
            simple_task_manager.update_task(
                task_id=task_id,
                progress=progress,
                message=f"处理中... {progress}%"
            )
        
        # 生成输出文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_filename = f"wanx_i2v_{task_id}_{timestamp}.mp4"
        output_path = WAN_OUTPUT_DIR / output_filename
        
        # 创建占位符文件
        with open(output_path, 'w') as f:
            f.write(f"# Wanx Image-to-Video Placeholder\n")
            f.write(f"# Task ID: {task_id}\n")
            f.write(f"# Prompt: {prompt}\n")
            f.write(f"# Image: {image_path}\n")
            f.write(f"# Model: {model}\n")
            f.write(f"# Generated: {datetime.now()}\n")
        
        simple_task_manager.update_task(
            task_id=task_id,
            progress=100,
            message="视频生成完成"
        )
        
        video_url = f"/api/v1/video/download/{output_filename}"
        
        print(f"[Wanx图片转视频] 完成: {task_id}")
        
        return {
            "task_id": task_id,
            "status": "completed",
            "output_file": str(output_path),
            "video_url": video_url,
            "metadata": {
                "prompt": prompt,
                "image_path": image_path,
                "model": model,
                "duration": duration,
                "resolution": resolution,
                "motion_strength": motion_strength
            }
        }
        
    except Exception as e:
        error_msg = str(e)
        print(f"[Wanx图片转视频] 失败: {error_msg}")
        
        simple_task_manager.update_task(
            task_id=task_id,
            progress=0,
            message=f"生成失败: {error_msg}"
        )
        
        return {
            "task_id": task_id,
            "status": "failed",
            "error": error_msg
        }
