import { ref, computed, onMounted, onUnmounted } from 'vue';

/**
 * 媒体查询钩子
 * @param {string} query - CSS媒体查询字符串
 * @param {boolean} defaultValue - 默认值，用于SSR
 * @returns {Object} 查询结果响应式引用
 */
export default function useMediaQuery(query, defaultValue = false) {
  // 查询结果状态
  const matches = ref(defaultValue);
  
  // 媒体查询对象
  let mediaQuery = null;
  
  // 匹配处理函数
  const updateMatches = (event) => {
    matches.value = event ? event.matches : mediaQuery.matches;
  };
  
  // 初始化媒体查询
  onMounted(() => {
    if (!window || !window.matchMedia) {
      console.warn('当前环境不支持matchMedia API');
      return;
    }
    
    // 创建媒体查询对象
    mediaQuery = window.matchMedia(query);
    
    // 设置初始值
    matches.value = mediaQuery.matches;
    
    // 添加监听器
    try {
      // 现代浏览器使用addEventListener
      mediaQuery.addEventListener('change', updateMatches);
    } catch (err) {
      // 兼容旧版浏览器
      mediaQuery.addListener(updateMatches);
    }
  });
  
  // 清理媒体查询监听器
  onUnmounted(() => {
    if (!mediaQuery) return;
    
    try {
      // 现代浏览器使用removeEventListener
      mediaQuery.removeEventListener('change', updateMatches);
    } catch (err) {
      // 兼容旧版浏览器
      mediaQuery.removeListener(updateMatches);
    }
  });
  
  return matches;
}

// 预定义的断点查询
export const useBreakpoints = () => {
  // 使用Ant Design Vue的断点
  const screens = {
    xs: useMediaQuery('(max-width: 575px)'),
    sm: useMediaQuery('(min-width: 576px) and (max-width: 767px)'),
    md: useMediaQuery('(min-width: 768px) and (max-width: 991px)'),
    lg: useMediaQuery('(min-width: 992px) and (max-width: 1199px)'),
    xl: useMediaQuery('(min-width: 1200px) and (max-width: 1599px)'),
    xxl: useMediaQuery('(min-width: 1600px)'),
  };
  
  // 计算断点比较
  const gtXs = useMediaQuery('(min-width: 576px)');
  const gtSm = useMediaQuery('(min-width: 768px)');
  const gtMd = useMediaQuery('(min-width: 992px)');
  const gtLg = useMediaQuery('(min-width: 1200px)');
  const gtXl = useMediaQuery('(min-width: 1600px)');
  
  const ltSm = useMediaQuery('(max-width: 575px)');
  const ltMd = useMediaQuery('(max-width: 767px)');
  const ltLg = useMediaQuery('(max-width: 991px)');
  const ltXl = useMediaQuery('(max-width: 1199px)');
  const ltXxl = useMediaQuery('(max-width: 1599px)');
  
  // 当前活动的断点
  const current = computed(() => {
    if (screens.xxl.value) return 'xxl';
    if (screens.xl.value) return 'xl';
    if (screens.lg.value) return 'lg';
    if (screens.md.value) return 'md';
    if (screens.sm.value) return 'sm';
    return 'xs';
  });
  
  return {
    screens,
    current,
    gtXs,
    gtSm,
    gtMd,
    gtLg,
    gtXl,
    ltSm,
    ltMd,
    ltLg,
    ltXl,
    ltXxl,
  };
}; 