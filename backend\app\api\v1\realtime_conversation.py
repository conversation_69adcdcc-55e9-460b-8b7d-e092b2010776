#!/usr/bin/env python3
"""
实时对话WebSocket API
"""

import json
import logging
from typing import Dict, Any
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, HTTPException
from fastapi.responses import JSONResponse

from app.services.realtime_conversation import conversation_manager
from app.services.multimodal_interaction import multimodal_processor

logger = logging.getLogger(__name__)
router = APIRouter()

# 活跃的WebSocket连接
active_connections: Dict[str, WebSocket] = {}

@router.websocket("/ws/conversation/{session_id}")
async def websocket_conversation(websocket: WebSocket, session_id: str):
    """WebSocket对话端点"""
    await websocket.accept()
    active_connections[session_id] = websocket
    
    logger.info(f"🔌 WebSocket连接建立: {session_id}")
    
    try:
        # 获取对话会话
        session = conversation_manager.get_session(session_id)
        if not session:
            await websocket.send_text(json.dumps({
                "type": "error",
                "message": "会话不存在"
            }))
            await websocket.close()
            return
        
        # 设置会话回调
        session.on_user_speech = lambda data: send_to_client(session_id, "user_speech", data)
        session.on_ai_response = lambda data: send_to_client(session_id, "ai_response", data)
        session.on_video_generated = lambda data: send_to_client(session_id, "video_generated", data)
        session.on_error = lambda error: send_to_client(session_id, "error", {"message": error})
        
        # 发送连接成功消息
        await websocket.send_text(json.dumps({
            "type": "connected",
            "session_id": session_id,
            "message": "实时对话已连接"
        }))
        
        # 监听客户端消息
        while True:
            try:
                data = await websocket.receive_text()
                message = json.loads(data)
                
                await handle_client_message(session_id, message)
                
            except WebSocketDisconnect:
                logger.info(f"🔌 WebSocket连接断开: {session_id}")
                break
            except json.JSONDecodeError:
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "message": "无效的JSON格式"
                }))
            except Exception as e:
                logger.error(f"❌ WebSocket消息处理错误: {e}")
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "message": str(e)
                }))
    
    except Exception as e:
        logger.error(f"❌ WebSocket连接错误: {e}")
    
    finally:
        # 清理连接
        if session_id in active_connections:
            del active_connections[session_id]
        logger.info(f"🧹 WebSocket连接已清理: {session_id}")

async def handle_client_message(session_id: str, message: Dict[str, Any]):
    """处理客户端消息"""
    message_type = message.get("type")
    session = conversation_manager.get_session(session_id)
    
    if not session:
        await send_to_client(session_id, "error", {"message": "会话不存在"})
        return
    
    if message_type == "start_listening":
        # 开始监听
        await session.start_listening()
        await send_to_client(session_id, "status", {"message": "开始监听语音"})
        
    elif message_type == "stop_listening":
        # 停止监听
        await session.stop_listening()
        await send_to_client(session_id, "status", {"message": "停止监听语音"})
        
    elif message_type == "send_text":
        # 发送文本消息
        text = message.get("text", "").strip()
        if text:
            await session._on_final_speech(text)

    elif message_type == "send_image":
        # 发送图像
        await handle_image_input(session_id, message)

    elif message_type == "send_document":
        # 发送文档
        await handle_document_input(session_id, message)

    elif message_type == "send_audio":
        # 发送音频
        await handle_audio_input(session_id, message)

    elif message_type == "ping":
        # 心跳检测
        await send_to_client(session_id, "pong", {"timestamp": message.get("timestamp")})

    else:
        await send_to_client(session_id, "error", {"message": f"未知消息类型: {message_type}"})

async def handle_image_input(session_id: str, message: Dict[str, Any]):
    """处理图像输入"""
    try:
        session = conversation_manager.get_session(session_id)
        if not session:
            await send_to_client(session_id, "error", {"message": "会话不存在"})
            return

        image_data = message.get("image_data")
        if not image_data:
            await send_to_client(session_id, "error", {"message": "缺少图像数据"})
            return

        # 处理图像
        result = await multimodal_processor.process_image_input(image_data)

        if result["success"]:
            # 生成基于图像的对话
            image_description = result["analysis"].get("description", "我看到了一张图片")
            user_input = f"[图像] {image_description}"

            await session._on_final_speech(user_input)

            # 发送图像分析结果
            await send_to_client(session_id, "image_analysis", result)
        else:
            await send_to_client(session_id, "error", {"message": f"图像处理失败: {result.get('error')}"})

    except Exception as e:
        logger.error(f"❌ 图像处理异常: {e}")
        await send_to_client(session_id, "error", {"message": f"图像处理异常: {e}"})

async def handle_document_input(session_id: str, message: Dict[str, Any]):
    """处理文档输入"""
    try:
        session = conversation_manager.get_session(session_id)
        if not session:
            await send_to_client(session_id, "error", {"message": "会话不存在"})
            return

        document_path = message.get("document_path")
        if not document_path:
            await send_to_client(session_id, "error", {"message": "缺少文档路径"})
            return

        from pathlib import Path
        doc_path = Path(document_path)

        if not doc_path.exists():
            await send_to_client(session_id, "error", {"message": "文档文件不存在"})
            return

        # 处理文档
        result = await multimodal_processor.process_document_input(doc_path)

        if result["success"]:
            # 生成基于文档的对话
            doc_summary = result["analysis"].get("summary", "我收到了一个文档")
            user_input = f"[文档] {doc_summary}"

            await session._on_final_speech(user_input)

            # 发送文档分析结果
            await send_to_client(session_id, "document_analysis", result)
        else:
            await send_to_client(session_id, "error", {"message": f"文档处理失败: {result.get('error')}"})

    except Exception as e:
        logger.error(f"❌ 文档处理异常: {e}")
        await send_to_client(session_id, "error", {"message": f"文档处理异常: {e}"})

async def handle_audio_input(session_id: str, message: Dict[str, Any]):
    """处理音频输入"""
    try:
        session = conversation_manager.get_session(session_id)
        if not session:
            await send_to_client(session_id, "error", {"message": "会话不存在"})
            return

        audio_path = message.get("audio_path")
        if not audio_path:
            await send_to_client(session_id, "error", {"message": "缺少音频路径"})
            return

        from pathlib import Path
        aud_path = Path(audio_path)

        if not aud_path.exists():
            await send_to_client(session_id, "error", {"message": "音频文件不存在"})
            return

        # 处理音频
        result = await multimodal_processor.process_audio_input(aud_path)

        if result["success"]:
            # 使用转录文本进行对话
            transcription = result.get("transcription", "我收到了一段音频")

            await session._on_final_speech(transcription)

            # 发送音频分析结果
            await send_to_client(session_id, "audio_analysis", result)
        else:
            await send_to_client(session_id, "error", {"message": f"音频处理失败: {result.get('error')}"})

    except Exception as e:
        logger.error(f"❌ 音频处理异常: {e}")
        await send_to_client(session_id, "error", {"message": f"音频处理异常: {e}"})

async def send_to_client(session_id: str, message_type: str, data: Dict[str, Any]):
    """发送消息到客户端"""
    if session_id in active_connections:
        websocket = active_connections[session_id]
        try:
            message = {
                "type": message_type,
                "data": data,
                "timestamp": data.get("timestamp") if isinstance(data, dict) else None
            }
            await websocket.send_text(json.dumps(message))
        except Exception as e:
            logger.error(f"❌ 发送消息失败: {e}")

@router.post("/conversation/create")
async def create_conversation_session(digital_human_config: Dict[str, Any]):
    """创建对话会话"""
    try:
        session_id = await conversation_manager.create_session(digital_human_config)
        
        if session_id:
            return JSONResponse({
                "success": True,
                "session_id": session_id,
                "message": "对话会话创建成功",
                "websocket_url": f"/api/v1/realtime/ws/conversation/{session_id}"
            })
        else:
            raise HTTPException(status_code=500, detail="对话会话创建失败")
            
    except Exception as e:
        logger.error(f"❌ 创建对话会话失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/conversation/{session_id}")
async def delete_conversation_session(session_id: str):
    """删除对话会话"""
    try:
        await conversation_manager.remove_session(session_id)
        
        # 关闭WebSocket连接
        if session_id in active_connections:
            websocket = active_connections[session_id]
            await websocket.close()
        
        return JSONResponse({
            "success": True,
            "message": "对话会话已删除"
        })
        
    except Exception as e:
        logger.error(f"❌ 删除对话会话失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/conversation/{session_id}/status")
async def get_conversation_status(session_id: str):
    """获取对话会话状态"""
    try:
        session = conversation_manager.get_session(session_id)
        
        if not session:
            raise HTTPException(status_code=404, detail="会话不存在")
        
        return JSONResponse({
            "success": True,
            "session_id": session_id,
            "is_active": session.is_active,
            "is_listening": session.is_listening,
            "is_generating": session.is_generating,
            "conversation_length": len(session.conversation_history),
            "websocket_connected": session_id in active_connections
        })
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ 获取会话状态失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/conversation/{session_id}/history")
async def get_conversation_history(session_id: str, limit: int = 50):
    """获取对话历史"""
    try:
        session = conversation_manager.get_session(session_id)
        
        if not session:
            raise HTTPException(status_code=404, detail="会话不存在")
        
        # 获取最近的对话历史
        history = session.conversation_history[-limit:] if limit > 0 else session.conversation_history
        
        return JSONResponse({
            "success": True,
            "session_id": session_id,
            "history": history,
            "total_messages": len(session.conversation_history)
        })
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ 获取对话历史失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/conversation/cleanup")
async def cleanup_all_conversations():
    """清理所有对话会话"""
    try:
        # 关闭所有WebSocket连接
        for session_id, websocket in list(active_connections.items()):
            try:
                await websocket.close()
            except:
                pass

        active_connections.clear()

        # 清理所有会话
        await conversation_manager.cleanup_all()

        return JSONResponse({
            "success": True,
            "message": "所有对话会话已清理"
        })

    except Exception as e:
        logger.error(f"❌ 清理对话会话失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/conversation/{session_id}/upload-image")
async def upload_image_to_conversation(session_id: str, image_data: Dict[str, Any]):
    """上传图像到对话"""
    try:
        result = await multimodal_processor.process_image_input(image_data.get("image"))

        return JSONResponse({
            "success": True,
            "session_id": session_id,
            "result": result
        })

    except Exception as e:
        logger.error(f"❌ 图像上传失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/conversation/{session_id}/upload-document")
async def upload_document_to_conversation(session_id: str, document_path: str):
    """上传文档到对话"""
    try:
        from pathlib import Path
        doc_path = Path(document_path)

        result = await multimodal_processor.process_document_input(doc_path)

        return JSONResponse({
            "success": True,
            "session_id": session_id,
            "result": result
        })

    except Exception as e:
        logger.error(f"❌ 文档上传失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/conversation/{session_id}/generate-3d")
async def generate_3d_digital_human(session_id: str, config: Dict[str, Any]):
    """生成3D数字人"""
    try:
        from app.services.digital_human_3d import digital_human_3d
        from pathlib import Path

        session = conversation_manager.get_session(session_id)
        if not session:
            raise HTTPException(status_code=404, detail="会话不存在")

        # 获取头像路径
        avatar_url = session.digital_human_config.get('avatar_url', '')
        avatar_path = Path(avatar_url.replace('/storage/', 'storage/'))

        if not avatar_path.exists():
            raise HTTPException(status_code=404, detail="头像文件不存在")

        # 生成3D模型
        output_path = Path(f"storage/3d_models/model_{session_id}.glb")
        output_path.parent.mkdir(parents=True, exist_ok=True)

        result = await digital_human_3d.generate_3d_model(
            avatar_image=avatar_path,
            output_path=output_path,
            model_type=config.get("model_type", "realistic"),
            quality=config.get("quality", "high")
        )

        return JSONResponse({
            "success": True,
            "session_id": session_id,
            "result": result
        })

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ 3D数字人生成失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))
