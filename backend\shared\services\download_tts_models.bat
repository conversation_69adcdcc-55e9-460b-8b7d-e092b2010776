@echo off
echo ========================================
echo TTS模型下载器
echo ========================================
echo 此脚本将下载所需的TTS模型文件
echo.

REM 设置工作目录
cd /d "%~dp0"

REM 检查Python环境
python --version > nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo Python未安装或未添加到PATH环境变量
    echo 请安装Python 3.8或更高版本
    echo 下载地址: https://www.python.org/downloads/
    echo.
    pause
    exit /b
)

REM 检查是否安装TTS库
python -c "import TTS" > nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo 正在安装TTS库...
    echo 这可能需要几分钟时间，请耐心等待...
    
    REM 安装TTS
    pip install TTS
    
    if %ERRORLEVEL% NEQ 0 (
        echo TTS库安装失败，请手动安装:
        echo pip install TTS
        echo.
        pause
        exit /b
    )
    
    echo TTS库安装成功！
)

echo.
echo 开始下载所有推荐的TTS模型...
echo 这将会下载多个模型文件，可能需要较长时间和较大存储空间
echo 模型下载期间，请勿关闭此窗口
echo.

REM 执行下载脚本
python download_tts_models.py --all

echo.
echo 模型下载过程已完成
echo 如果下载成功，TTS系统现在应该可以正常工作
echo.
pause 