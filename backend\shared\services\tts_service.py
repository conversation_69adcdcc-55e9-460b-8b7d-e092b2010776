import os
import logging
import tempfile
import uuid
import hashlib
from typing import Dict, List, Optional, Any
import time
from gtts import gTTS
import json
import asyncio
from concurrent.futures import ThreadPoolExecutor
from functools import lru_cache
import traceback

# 导入离线TTS服务
from services.offline_tts_service import get_offline_tts_service, initialize as init_offline_tts
# 导入Edge TTS服务
from services.edge_tts_service import get_edge_tts_service, initialize as init_edge_tts
from services.tts_service_status import get_tts_service_status

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TTSError(Exception):
    """TTS服务错误基类"""
    pass

class VoiceNotFoundError(TTSError):
    """语音未找到错误"""
    pass

class TTSGenerationError(TTSError):
    """语音生成错误"""
    pass

class TTSService:
    """TTS服务 - 集成多种TTS服务提供商"""
    
    def __init__(self):
        """初始化TTS服务"""
        # 服务状态监控
        self.status_monitor = get_tts_service_status()
        
        # 尝试初始化Edge TTS服务 - 作为首选引擎
        try:
            self.edge_tts = get_edge_tts_service()
            logger.info("Edge TTS服务已加载")
        except Exception as e:
            logger.error(f"初始化Edge TTS服务失败: {e}")
            logger.error(traceback.format_exc())
            self.edge_tts = None
        
        # 尝试初始化离线TTS服务 - 作为备选引擎
        try:
            self.offline_tts = get_offline_tts_service()
            logger.info("离线TTS服务已加载")
        except Exception as e:
            logger.error(f"初始化离线TTS服务失败: {e}")
            logger.error(traceback.format_exc())
            self.offline_tts = None
        
        # 在线TTS服务将在需要时动态加载，避免循环导入问题
        self.online_tts = None

        # Coqui TTS 服务
        self.coqui_tts = None
        
        # 服务优先级设置 - Coqui TTS 优先
        self.service_priority = ["coqui", "edge-tts", "offline", "online", "fallback"]
        
        # 可用语音字典 (默认值，将在初始化后更新)
        self.available_voices = {
            "zh-female1": {"lang": "zh-cn", "tld": "com", "gender": "female", "name": "标准女声"},
            "zh-female2": {"lang": "zh-cn", "tld": "com.hk", "gender": "female", "name": "甜美女声"},
            "zh-male1": {"lang": "zh-cn", "tld": "com", "gender": "male", "name": "标准男声"},
            "en-female1": {"lang": "en", "tld": "com", "gender": "female", "name": "英语女声"},
            "en-male1": {"lang": "en", "tld": "co.uk", "gender": "male", "name": "英语男声"},
            "ja-female1": {"lang": "ja", "tld": "co.jp", "gender": "female", "name": "日语女声"},
        }
        
        # 默认语音设置
        self.default_voice_id = "zh-female1"
        
        # 线程池执行器
        self.executor = ThreadPoolExecutor(max_workers=4)
        
        # 语音生成重试配置
        self.max_retries = 3
        self.retry_delay = 1  # 秒
        
        # 语音生成超时设置
        self.generation_timeout = 30  # 秒
        
        # 初始化标志
        self.coqui_tts_initialized = False
        self.edge_tts_initialized = False
        self.offline_tts_initialized = False
        self.online_tts_initialized = False

        logger.info("TTS服务已初始化完成")

    async def _initialize_coqui_tts(self):
        """初始化 Coqui TTS 服务"""
        if self.coqui_tts_initialized:
            return

        try:
            if self.coqui_tts is None:
                from .coqui_tts_service import coqui_tts_service
                self.coqui_tts = coqui_tts_service

            # 异步初始化
            await self.coqui_tts.initialize()
            self.coqui_tts_initialized = True
            logger.info("Coqui TTS服务异步初始化成功")
        except Exception as e:
            logger.error(f"Coqui TTS服务初始化失败: {e}")
            self.coqui_tts = None

    def validate_voice_id(self, voice_id: str) -> str:
        """
        验证并返回有效的voice_id
        
        Args:
            voice_id: 输入的voice_id
            
        Returns:
            str: 有效的voice_id
        """
        # 如果voice_id为system或None，使用默认voice_id
        if voice_id in [None, 'system', '']:
            return self.default_voice_id
            
        # 检查voice_id是否在可用列表中
        if voice_id in self.available_voices:
            return voice_id
            
        # 如果voice_id无效，返回默认voice_id
        logger.warning(f"未找到语音ID: {voice_id}，使用默认语音ID: {self.default_voice_id}")
        return self.default_voice_id
    
    @lru_cache(maxsize=100)
    def get_voice_config(self, voice_id: str) -> Dict:
        """
        获取语音配置（带缓存）
        
        Args:
            voice_id: 语音ID
            
        Returns:
            Dict: 语音配置
        """
        return self.available_voices[self.validate_voice_id(voice_id)]
    
    async def text_to_speech(self, 
                           text: str, 
                           voice_id: str = None, 
                           speed: float = 1.0,
                           output_path: str = None) -> Dict[str, str]:
        """
        将文本转换为语音
        
        Args:
            text: 要转换的文本
            voice_id: 语音ID，例如 zh-female1
            speed: 语速，范围为0.5到2.0
            output_path: 输出文件路径，如果为None则生成临时文件
            
        Returns:
            Dict: 包含生成的音频文件路径和状态的字典
        """
        try:
            # 首先尝试使用Edge TTS（如果可用）
            if self.edge_tts and hasattr(self.edge_tts, 'available') and self.edge_tts.available:
                try:
                    # 如果Edge TTS尚未初始化，初始化它
                    if not self.edge_tts_initialized:
                        self.edge_tts_initialized = await init_edge_tts()
                        if not self.edge_tts_initialized:
                            logger.warning("Edge TTS初始化失败，将尝试其他TTS服务")
                    
                    # 使用Edge TTS生成语音
                    if self.edge_tts_initialized:
                        logger.info(f"使用Edge TTS生成语音: voice_id={voice_id}")
                        result = await self.edge_tts.text_to_speech(text, voice_id, speed, output_path)
                        if result.get("success", False):
                            logger.info("Edge TTS生成成功")
                            
                            # 转换Windows路径为Web URL
                            audio_path = result.get("audio_path", "")
                            audio_url = self.convert_path_to_url(audio_path)
                            
                            # 获取音频时长
                            duration = self.get_audio_duration(audio_path)
                            
                            return {
                                "status": "success",
                                "path": audio_path,
                                "audio_url": audio_url,  # 添加Web可访问的URL
                                "voice_id": voice_id,
                                "speed": speed,
                                "engine": "edge-tts",
                                "duration": duration  # 添加音频时长
                            }
                        else:
                            logger.warning(f"Edge TTS生成失败: {result.get('message')}，将尝试其他TTS服务")
                except Exception as e:
                    logger.warning(f"Edge TTS出错，将尝试其他TTS服务: {e}")
            
            # 然后尝试使用离线TTS（如果可用）
            if self.offline_tts:
                try:
                    # 如果离线TTS尚未初始化，初始化它
                    if not self.offline_tts_initialized:
                        self.offline_tts_initialized = init_offline_tts()
                        if not self.offline_tts_initialized:
                            logger.warning("离线TTS初始化失败，将使用在线TTS")
                    
                    # 使用离线TTS生成语音
                    if self.offline_tts_initialized:
                        logger.info(f"使用离线TTS生成语音: voice_id={voice_id}")
                        result = await self.offline_tts.text_to_speech(text, voice_id, speed, output_path)
                        if result.get("status") == "success":
                            logger.info("离线TTS生成成功")
                            
                            # 确保添加时长
                            if "path" in result and not "duration" in result:
                                result["duration"] = self.get_audio_duration(result["path"])
                                
                            return result
                        else:
                            logger.warning(f"离线TTS生成失败: {result.get('message')}，将使用在线TTS")
                except Exception as e:
                    logger.warning(f"离线TTS出错，将使用在线TTS: {e}")
            
            # 最后尝试使用在线gTTS
            # 验证语音ID和获取配置
            voice_id = self.validate_voice_id(voice_id)
            voice_config = self.get_voice_config(voice_id)
            
            # 验证语速
            if not 0.5 <= speed <= 2.0:
                raise ValueError(f"语速必须在0.5到2.0之间，当前值: {speed}")
            
            # 生成临时文件名
            if output_path is None:
                temp_dir = tempfile.gettempdir()
                output_path = os.path.join(temp_dir, f"tts_{uuid.uuid4()}.mp3")
            
            # 带重试的语音生成
            for attempt in range(self.max_retries):
                try:
                    # 在线程池中执行gTTS操作
                    def generate_tts():
                        tts = gTTS(text=text, lang=voice_config["lang"], 
                                 tld=voice_config.get("tld", "com"), slow=False)
                        tts.save(output_path)
                        return output_path
                    
                    # 异步执行TTS生成（带超时）
                    path = await asyncio.wait_for(
                        asyncio.get_event_loop().run_in_executor(self.executor, generate_tts),
                        timeout=self.generation_timeout
                    )
                    
                    logger.info(f"在线语音已生成: {path}")
                    
                    # 如果速度不是1.0，需要处理语速调整
                    if speed != 1.0:
                        logger.info(f"调整语速至 {speed}x")
                        # 这里应该调用FFmpeg处理语速
                        # 演示代码省略
                    
                    # 获取音频时长
                    duration = self.get_audio_duration(path)
                    
                    # 转换为Web URL
                    audio_url = self.convert_path_to_url(path)
                    
                    return {
                        "status": "success",
                        "path": path,
                        "audio_url": audio_url,
                        "voice_id": voice_id,
                        "speed": speed,
                        "engine": "gtts",
                        "duration": duration  # 添加音频时长
                    }
                    
                except asyncio.TimeoutError:
                    if attempt < self.max_retries - 1:
                        logger.warning(f"语音生成超时，尝试重试 ({attempt + 1}/{self.max_retries})")
                        await asyncio.sleep(self.retry_delay)
                    else:
                        # 如果离线TTS已初始化，尝试回退到离线TTS
                        if self.offline_tts_initialized:
                            logger.info("在线TTS超时，尝试使用离线TTS作为回退")
                            try:
                                result = await self.offline_tts.text_to_speech(text, voice_id, speed, output_path)
                                if result.get("status") == "success":
                                    logger.info("使用离线TTS作为回退成功")
                                    return result
                            except Exception as offline_err:
                                logger.error(f"离线TTS回退也失败: {offline_err}")
                        
                        # 如果Edge TTS已初始化，尝试回退到Edge TTS
                        if self.edge_tts_initialized:
                            logger.info("尝试使用Edge TTS作为最后的回退")
                            try:
                                result = await self.edge_tts.text_to_speech(text, voice_id, speed, output_path)
                                if result.get("success", False):
                                    logger.info("使用Edge TTS作为回退成功")
                                    return {
                                        "status": "success",
                                        "path": result.get("audio_path", ""),
                                        "voice_id": voice_id,
                                        "speed": speed,
                                        "engine": "edge-tts"
                                    }
                            except Exception as edge_err:
                                logger.error(f"Edge TTS回退也失败: {edge_err}")
                                
                        raise TTSGenerationError("语音生成超时")
                        
                except Exception as e:
                    if attempt < self.max_retries - 1:
                        logger.warning(f"语音生成失败，尝试重试 ({attempt + 1}/{self.max_retries}): {e}")
                        await asyncio.sleep(self.retry_delay)
                    else:
                        # 尝试使用其他服务作为回退
                        logger.info(f"在线TTS失败: {e}，尝试使用其他TTS服务作为回退")
                        
                        # 如果Edge TTS已初始化，尝试回退到Edge TTS
                        if self.edge_tts_initialized:
                            try:
                                result = await self.edge_tts.text_to_speech(text, voice_id, speed, output_path)
                                if result.get("success", False):
                                    logger.info("使用Edge TTS作为回退成功")
                                    return {
                                        "status": "success",
                                        "path": result.get("audio_path", ""),
                                        "voice_id": voice_id,
                                        "speed": speed,
                                        "engine": "edge-tts"
                                    }
                            except Exception as edge_err:
                                logger.error(f"Edge TTS回退失败: {edge_err}")
                                
                        # 如果离线TTS已初始化，尝试回退到离线TTS
                        if self.offline_tts_initialized:
                            try:
                                result = await self.offline_tts.text_to_speech(text, voice_id, speed, output_path)
                                if result.get("status") == "success":
                                    logger.info("使用离线TTS作为回退成功")
                                    return result
                            except Exception as offline_err:
                                logger.error(f"离线TTS回退也失败: {offline_err}")
                                
                        raise TTSGenerationError(f"语音生成失败: {e}")
            
        except VoiceNotFoundError as e:
            logger.error(f"语音ID无效: {e}")
            return {"status": "error", "message": str(e)}
        except TTSGenerationError as e:
            logger.error(f"语音生成错误: {e}")
            return {"status": "error", "message": str(e)}
        except Exception as e:
            logger.error(f"语音合成失败: {e}")
            return {"status": "error", "message": str(e)}
    
    async def generate_speech(self, 
                            text: str, 
                            voice_id: str = None, 
                            speed: float = 1.0,
                            output_path: str = None) -> Dict[str, Any]:
        """
        将文本转换为语音
        
        Args:
            text: 要转换的文本
            voice_id: 语音ID
            speed: 语速，范围为0.5到2.0
            output_path: 输出文件路径
            
        Returns:
            Dict: 包含生成的音频文件路径和状态的字典
        """
        logger.info(f"调用generate_speech方法，文本: '{text[:30]}...'，语音ID: {voice_id}")
        
        result = None
        
        # 按照优先级尝试各种TTS服务

        # 1. 首先尝试 Coqui TTS
        if self.coqui_tts:
            try:
                # 确保 Coqui TTS 已初始化
                if not self.coqui_tts_initialized:
                    await self._initialize_coqui_tts()

                if self.coqui_tts_initialized:
                    logger.info(f"使用 Coqui TTS 生成语音: voice_id={voice_id}")
                    result = await self.coqui_tts.generate_speech(
                        text=text,
                        voice_id=voice_id,
                        speed=speed,
                        output_path=output_path
                    )

                    # 记录结果
                    self.status_monitor.record_tts_result(result)

                    if result["status"] == "success":
                        logger.info("Coqui TTS生成成功")
                        return result
                    else:
                        logger.warning(f"Coqui TTS生成失败: {result.get('message', '未知错误')}")
            except Exception as e:
                logger.error(f"Coqui TTS服务异常: {e}")
                logger.error(traceback.format_exc())
                result = {"status": "error", "message": str(e)}
                self.status_monitor.record_tts_result(result)

        # 2. 尝试Edge TTS
        if self.edge_tts:
            try:
                logger.info(f"使用Edge TTS生成语音: voice_id={voice_id}")
                result = await self.edge_tts.generate_speech(
                    text=text,
                    voice_id=voice_id,
                    speed=speed,
                    output_path=output_path
                )
                
                # 记录结果
                self.status_monitor.record_tts_result(result)
                
                if result["status"] == "success":
                    logger.info("Edge TTS生成成功")
                    return result
                else:
                    logger.warning(f"Edge TTS生成失败: {result.get('message', '未知错误')}")
            except Exception as e:
                logger.error(f"Edge TTS服务异常: {e}")
                logger.error(traceback.format_exc())
                result = {"status": "error", "message": str(e)}
                self.status_monitor.record_tts_result(result)
        
        # 2. 如果Edge TTS失败，尝试使用离线TTS服务
        if (result is None or result["status"] != "success") and self.offline_tts:
            try:
                logger.info(f"Edge TTS失败，使用离线TTS生成语音: voice_id={voice_id}")
                result = await self.offline_tts.generate_speech(
                    text=text,
                    voice_id=voice_id,
                    speed=speed,
                    output_path=output_path
                )
                
                # 记录结果
                self.status_monitor.record_tts_result(result)
                
                if result["status"] == "success":
                    logger.info("离线TTS生成成功")
                    return result
                else:
                    logger.warning(f"离线TTS生成失败: {result.get('message', '未知错误')}")
            except Exception as e:
                logger.error(f"离线TTS服务异常: {e}")
                logger.error(traceback.format_exc())
                if not result:
                    result = {"status": "error", "message": str(e)}
                self.status_monitor.record_tts_result(result)
        
        # 3. 如果离线TTS也失败，尝试使用在线TTS服务
        if result is None or result["status"] != "success":
            try:
                # 动态导入在线TTS服务，避免循环导入问题
                from services.online_tts_service import get_online_tts_service
                online_tts = get_online_tts_service()
                
                logger.info(f"尝试使用在线TTS服务: voice_id={voice_id}")
                online_result = await online_tts.generate_speech(
                    text=text,
                    voice_id=voice_id,
                    speed=speed,
                    output_path=output_path
                )
                
                # 记录结果
                self.status_monitor.record_tts_result(online_result)
                
                if online_result["status"] == "success":
                    logger.info("在线TTS生成成功")
                    return online_result
                else:
                    logger.warning(f"在线TTS生成失败: {online_result.get('message', '未知错误')}")
                    result = online_result  # 保存结果以便返回
            except Exception as e:
                logger.error(f"在线TTS服务异常: {e}")
                logger.error(traceback.format_exc())
                # 错误信息添加到结果中
                if not result:
                    result = {"status": "error", "message": f"在线TTS服务异常: {str(e)}"}
        
        # 4. 如果所有服务都失败，尝试使用后备TTS服务
        if result is None or result["status"] != "success":
            try:
                # 动态导入后备TTS服务，避免循环导入问题
                from services.fallback_tts_service import get_fallback_tts_service
                fallback_tts = get_fallback_tts_service()
                
                logger.info(f"所有TTS服务失败，尝试使用后备TTS服务: voice_id={voice_id}")
                fallback_result = await fallback_tts.generate_speech(
                    text=text,
                    voice_id=voice_id,
                    speed=speed,
                    output_path=output_path
                )
                
                # 记录结果
                self.status_monitor.record_tts_result(fallback_result)
                
                if fallback_result["status"] == "success":
                    logger.info("后备TTS生成成功")
                    return fallback_result
                else:
                    logger.warning(f"后备TTS生成失败: {fallback_result.get('message', '未知错误')}")
                    result = fallback_result  # 保存结果以便返回
            except Exception as e:
                logger.error(f"后备TTS服务异常: {e}")
                logger.error(traceback.format_exc())
                # 错误信息添加到结果中
                if not result:
                    result = {"status": "error", "message": f"后备TTS服务异常: {str(e)}"}
        
        # 5. 最后的后备方案：创建静音文件
        if result is None or result["status"] != "success":
            try:
                logger.warning("所有TTS服务都失败，创建静音文件作为最后的后备方案")
                
                # 生成或使用指定的输出路径
                if not output_path:
                    temp_dir = tempfile.gettempdir()
                    output_path = os.path.join(temp_dir, f"silent_{uuid.uuid4()}.mp3")
                
                # 创建静音文件
                with open(output_path, 'wb') as f:
                    # 写入一个最小的MP3文件头
                    f.write(b'\xff\xfb\x90\x04\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00')
                
                result = {
                    "status": "success",
                    "path": output_path,
                    "voice_id": voice_id,
                    "speed": speed,
                    "message": "所有TTS服务失败，创建静音文件",
                    "engine": "silent"
                }
                
                # 记录结果
                self.status_monitor.record_tts_result(result)
                
                return result
            except Exception as e:
                logger.error(f"创建静音文件也失败: {e}")
                logger.error(traceback.format_exc())
                # 错误信息添加到结果中
                result = {"status": "error", "message": f"所有TTS服务和静音文件创建都失败: {str(e)}"}
                self.status_monitor.record_tts_result(result)
        
        # 返回结果
        if result:
            return result
        else:
            error_result = {
                "status": "error", 
                "message": "所有TTS服务均不可用或生成失败"
            }
            self.status_monitor.record_tts_result(error_result)
            return error_result
    
    def get_available_voices(self) -> Dict:
        """
        获取可用的语音列表
        
        Returns:
            available_voices: 可用语音字典
        """
        voices = {}

        # 获取 Coqui TTS 提供的语音
        if self.coqui_tts:
            try:
                coqui_voices = self.coqui_tts.get_available_voices()
                for voice_id, voice_info in coqui_voices.items():
                    voice_info["provider"] = "coqui-tts"
                    voices[voice_id] = voice_info
            except Exception as e:
                logger.error(f"获取 Coqui TTS 语音列表失败: {e}")

        # 获取Edge TTS提供的语音
        if self.edge_tts:
            try:
                edge_voices = self.edge_tts.get_available_voices()
                for voice_id, voice_info in edge_voices.items():
                    voice_info["provider"] = "edge-tts"
                    voices[voice_id] = voice_info
            except Exception as e:
                logger.error(f"获取Edge TTS语音列表失败: {e}")
        
        # 获取离线TTS提供的语音
        if self.offline_tts:
            try:
                offline_voices = self.offline_tts.get_available_voices()
                for voice_id, voice_info in offline_voices.items():
                    voice_info["provider"] = "offline"
                    voices[voice_id] = voice_info
            except Exception as e:
                logger.error(f"获取离线TTS语音列表失败: {e}")
        
        # 获取在线TTS提供的语音
        try:
            # 动态导入，避免循环导入问题
            from services.online_tts_service import get_online_tts_service
            online_tts = get_online_tts_service()
            online_voices = online_tts.get_available_voices()
            
            # 合并语音列表，避免ID冲突
            for voice_id, voice_info in online_voices.items():
                if voice_id not in voices:  # 避免覆盖已有语音
                    voice_info["provider"] = "online"
                    voices[voice_id] = voice_info
        except Exception as e:
            logger.error(f"获取在线TTS语音列表失败: {e}")
        
        # 更新可用语音列表
        self.available_voices = voices
        
        return voices
    
    def generate_phonemes(self, text: str, lang: str = "zh-cn") -> List[Dict]:
        """
        生成文本的音素序列（用于唇形同步）
        
        Args:
            text: 要处理的文本
            lang: 语言代码
            
        Returns:
            phonemes: 音素列表，每个音素包含类型和持续时间
        """
        # 实际项目应使用专业的音素分析工具，如Festival或MFA
        # 这里使用简化实现做演示
        
        # 创建基本音素映射（演示用）
        simple_phoneme_map = {
            'a': 'ah', 'e': 'eh', 'i': 'ih', 'o': 'oh', 'u': 'uh',
            'b': 'b', 'p': 'p', 'm': 'm', 'f': 'f', 'v': 'v',
            'd': 'd', 't': 't', 'l': 'l', 'n': 'n',
            'g': 'g', 'k': 'k', 'h': 'h',
            'j': 'j', 'q': 'k', 'x': 'sh',
            'z': 'z', 'c': 'ch', 's': 's',
            'r': 'r', 'y': 'y', 'w': 'w'
        }
        
        phonemes = []
        
        # 简单分割字符（实际应分析音节）
        for char in text.lower():
            if char in simple_phoneme_map:
                phoneme_type = simple_phoneme_map[char]
            else:
                phoneme_type = 'sil'  # 静音
            
            # 添加音素及其持续时间
            phonemes.append({
                "phoneme": phoneme_type,
                "duration": 0.1  # 假设每个音素持续0.1秒
            })
        
        return phonemes
    
    def save_phonemes_to_file(self, phonemes: List[Dict], output_path: str) -> bool:
        """
        将音素序列保存到文件
        
        Args:
            phonemes: 音素列表
            output_path: 输出文件路径
            
        Returns:
            bool: 保存是否成功
        """
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(phonemes, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            logger.error(f"保存音素文件失败: {e}")
            return False
    
    def load_phonemes_from_file(self, file_path: str) -> List[Dict]:
        """
        从文件加载音素序列
        
        Args:
            file_path: 文件路径
            
        Returns:
            phonemes: 音素列表
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"加载音素文件失败: {e}")
            return []
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取TTS服务状态
        
        Returns:
            Dict: 服务状态信息
        """
        status = self.status_monitor.get_status()
        
        # 添加离线TTS性能信息
        if self.offline_tts:
            try:
                offline_stats = self.offline_tts.get_performance_stats()
                status["offline_tts_stats"] = offline_stats
            except Exception as e:
                logger.error(f"获取离线TTS性能统计失败: {e}")
        
        return status
    
    def get_health_check(self) -> Dict[str, Any]:
        """
        获取健康检查结果
        
        Returns:
            Dict: 健康状态信息
        """
        return self.status_monitor.get_health_check()

    def is_initialized(self) -> bool:
        """检查TTS服务是否已初始化"""
        # 检查Edge TTS是否已初始化
        if self.edge_tts_initialized:
            return True
        
        # 检查离线TTS是否已初始化
        if self.offline_tts_initialized:
            return True
        
        # 检查在线TTS是否已初始化
        if self.online_tts_initialized:
            return True
        
        # 所有服务都未初始化
        return False

    def convert_path_to_url(self, file_path: str) -> str:
        """
        将本地文件路径转换为Web可访问的URL
        
        Args:
            file_path: 本地文件路径
            
        Returns:
            str: Web可访问的URL
        """
        if not file_path or not os.path.exists(file_path):
            logger.warning(f"文件不存在或路径为空: {file_path}")
            return None
            
        try:
            # 创建静态文件目录
            static_dir = os.path.join("static", "audio")
            os.makedirs(static_dir, exist_ok=True)
            
            # 创建唯一的文件名以避免冲突
            file_name = os.path.basename(file_path)
            file_hash = hashlib.md5(f"{file_path}_{time.time()}".encode()).hexdigest()
            new_file_name = f"{file_hash}.mp3"
            
            # 复制文件到静态目录
            new_path = os.path.join(static_dir, new_file_name)
            import shutil
            shutil.copy2(file_path, new_path)
            
            # 计算音频文件时长
            duration_str = self.get_audio_duration(new_path)
            
            # 返回Web可访问的URL
            url = f"/static/audio/{new_file_name}"
            logger.info(f"文件路径转换为URL: {file_path} -> {url}, 时长: {duration_str}")
            return url
        
        except Exception as e:
            logger.error(f"转换文件路径到URL时出错: {str(e)}")
            return None
            
    def get_audio_duration(self, file_path: str) -> str:
        """
        获取音频文件时长
        
        Args:
            file_path: 音频文件路径
            
        Returns:
            str: 格式化的时长字符串 (MM:SS)
        """
        try:
            # 尝试使用ffprobe获取音频文件时长
            import subprocess
            result = subprocess.run(
                ['ffprobe', '-v', 'error', '-show_entries', 'format=duration', '-of', 
                 'default=noprint_wrappers=1:nokey=1', file_path],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                timeout=5
            )
            
            if result.returncode == 0 and result.stdout.strip():
                # 解析时长（秒）
                seconds = float(result.stdout.strip())
                # 格式化为MM:SS
                minutes = int(seconds // 60)
                remaining_seconds = int(seconds % 60)
                return f"{minutes:02d}:{remaining_seconds:02d}"
            
            # 如果ffprobe失败，尝试使用音频库
            try:
                import wave
                with wave.open(file_path, 'rb') as wf:
                    # 计算时长
                    frames = wf.getnframes()
                    rate = wf.getframerate()
                    seconds = frames / float(rate)
                    # 格式化
                    minutes = int(seconds // 60)
                    remaining_seconds = int(seconds % 60)
                    return f"{minutes:02d}:{remaining_seconds:02d}"
            except:
                # 针对MP3文件尝试其他方法
                try:
                    from mutagen.mp3 import MP3
                    audio = MP3(file_path)
                    seconds = audio.info.length
                    # 格式化
                    minutes = int(seconds // 60)
                    remaining_seconds = int(seconds % 60)
                    return f"{minutes:02d}:{remaining_seconds:02d}"
                except:
                    logger.warning(f"无法获取音频文件时长: {file_path}")
        
        except Exception as e:
            logger.error(f"获取音频文件时长时出错: {e}")
        
        # 如果所有方法都失败，返回默认值
        return "00:30"  # 默认30秒

# 全局单例
_tts_service = None

def get_tts_service() -> TTSService:
    """获取TTS服务实例"""
    global _tts_service
    if _tts_service is None:
        _tts_service = TTSService()
    return _tts_service

async def initialize() -> bool:
    """初始化TTS服务"""
    service = get_tts_service()
    success = False
    
    # 初始化Edge TTS（优先）
    try:
        service.edge_tts_initialized = await init_edge_tts()
        if service.edge_tts_initialized:
            logger.info("Edge TTS服务初始化成功")
            success = True
        else:
            logger.warning("Edge TTS服务初始化失败，将尝试其他TTS服务")
    except Exception as e:
        logger.error(f"初始化Edge TTS服务时出错: {e}")
        service.edge_tts_initialized = False
    
    # 初始化离线TTS
    try:
        service.offline_tts_initialized = init_offline_tts()
        if service.offline_tts_initialized:
            logger.info("离线TTS服务初始化成功")
            success = True
        else:
            logger.warning("离线TTS服务初始化失败，将尝试使用在线TTS")
    except Exception as e:
        logger.error(f"初始化离线TTS服务时出错: {e}")
        service.offline_tts_initialized = False
    
    # 初始化在线TTS
    try:
        # 动态导入，避免循环导入问题
        from services.online_tts_service import initialize as init_online_tts
        service.online_tts_initialized = init_online_tts()
        if service.online_tts_initialized:
            logger.info("在线TTS服务初始化成功")
            success = True
        else:
            logger.warning("在线TTS服务初始化失败")
    except Exception as e:
        logger.error(f"初始化在线TTS服务时出错: {e}")
        service.online_tts_initialized = False
    
    # 更新可用语音列表
    service.get_available_voices()
    
    return success

def is_initialized() -> bool:
    """检查TTS服务是否已初始化"""
    service = get_tts_service()
    return service.is_initialized()