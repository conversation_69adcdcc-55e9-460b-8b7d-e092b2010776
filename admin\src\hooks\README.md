# Vue3 Hooks 工具库

这是一个基于 Vue3 的 Hooks 工具库，提供了一系列可复用的 Hooks 函数，用于处理常见的业务逻辑。

## 安装

本工具库已内置于项目中，无需额外安装。

## 使用方法

```javascript
// 按需导入
import { useModal, useForm } from '@/hooks';

// 或者导入全部
import hooks from '@/hooks';
```

## 可用 Hooks

### 请求相关

#### useRequest
处理API请求，包含加载状态、错误处理、请求取消等功能。

```javascript
const { 
  data, 
  loading, 
  error, 
  run, 
  refresh, 
  cancel 
} = useRequest(fetchUsers, {
  defaultParams: { page: 1 },
  manual: false,
  onSuccess: (data) => {
    console.log('请求成功:', data);
  },
  onError: (error) => {
    console.error('请求失败:', error);
  }
});
```

### 表单相关

#### useForm
处理表单数据、验证和提交。

```javascript
const {
  formData,
  formErrors,
  isDirty,
  isValid,
  submitForm,
  resetForm,
  validateForm,
  setFieldValue
} = useForm({
  defaultValues: { username: '', password: '' },
  rules: {
    username: { required: true, message: '请输入用户名' },
    password: { required: true, min: 6, message: '密码至少6位' }
  },
  onSubmit: async (values) => {
    await api.login(values);
  }
});
```

### UI相关

#### useModal
管理模态框的打开、关闭和数据传递。

```javascript
const { 
  visible, 
  confirmLoading, 
  modalData, 
  openModal, 
  closeModal,
  handleOk,
  handleCancel
} = useModal({
  afterClose: () => {
    console.log('模态框已关闭');
  }
});

// 打开模态框并传递数据
openModal({ id: 1, name: '用户信息' });
```

### 文件上传

#### useUpload
处理文件上传，包含上传状态和进度。

```javascript
const {
  fileList,
  uploading,
  uploadProgress,
  uploadSuccess,
  uploadError,
  handleUpload,
  removeFile,
  reset
} = useUpload({
  action: '/api/upload',
  accept: '.jpg,.png',
  maxSize: 2 * 1024 * 1024,
  maxCount: 5,
  onSuccess: (file) => {
    console.log('上传成功:', file);
  }
});
```

### 剪贴板

#### useClipboard
复制文本到剪贴板，支持富文本。

```javascript
const { copied, copy, copyHtml, readText } = useClipboard({
  successMessage: '已复制到剪贴板',
  timeout: 2000
});

// 复制文本
copy('要复制的文本');

// 复制HTML
copyHtml('<b>加粗文本</b>', '加粗文本');
```

### 倒计时

#### useCountdown
管理倒计时，支持暂停、恢复和重置。

```javascript
const {
  remainingTime,
  formattedTime,
  progress,
  isRunning,
  isPaused,
  start,
  pause,
  resume,
  reset
} = useCountdown({
  initialTime: 60,
  autoStart: true,
  onEnd: () => {
    console.log('倒计时结束');
  }
});
```

### 函数优化

#### useThrottleFn
函数节流，控制函数执行频率。

```javascript
const { run, cancel, flush } = useThrottleFn((value) => {
  console.log('节流执行:', value);
}, {
  wait: 300,
  leading: true,
  trailing: true
});

// 调用节流函数
run('参数');
```

#### useDebounceFn
函数防抖，延迟函数执行。

```javascript
const { run, cancel, flush } = useDebounceFn((value) => {
  console.log('防抖执行:', value);
}, {
  wait: 300,
  leading: false,
  trailing: true
});

// 调用防抖函数
run('参数');
```

### 事件监听

#### useEventListener
添加和管理事件监听器。

```javascript
// 监听窗口大小变化
const { isListening, remove } = useEventListener(window, 'resize', () => {
  console.log('窗口大小变化');
});

// 监听DOM元素点击
useEventListener('#btn', 'click', () => {
  console.log('按钮被点击');
});
```

### 存储相关

#### useLocalStorage
管理本地存储数据，支持自动序列化和跨标签页同步。

```javascript
const [theme, setTheme] = useLocalStorage('theme', 'light');

// 更新存储
setTheme('dark');
```

### 表格相关

#### useTable
处理表格数据加载、分页、排序和筛选。

```javascript
const {
  tableData,
  loading,
  pagination,
  queryParams,
  selectedRows,
  loadData,
  refresh,
  reset,
  search,
  handleTableChange,
  getRowSelection
} = useTable({
  fetchData: api.getUsers,
  defaultParams: { status: 'active' },
  pagination: { pageSize: 10 },
  immediate: true
});
```

## 贡献

如果你有好的建议，欢迎提交 PR 或创建 Issue。 