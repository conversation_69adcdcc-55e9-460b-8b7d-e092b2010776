from sqlalchemy import Column, String, Integer, ForeignKey, DateTime, Text, JSON, Boolean
from sqlalchemy.orm import relationship, foreign
from sqlalchemy.sql import func
import uuid
from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field, ConfigDict
from enum import Enum

from utils.db import Base

class TaskStatus(Enum):
    """任务状态枚举"""
    CREATED = "created"
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

# 注意：在类定义前导入可能会导致循环导入，所以我们在这里使用字符串引用

class TaskModel(Base):
    """任务模型 - 所有异步任务的统一数据结构"""
    __tablename__ = "tasks"
    __table_args__ = {'extend_existing': True}  # 允许表被多次定义，解决SQLAlchemy映射冲突
    
    # 添加自增整数主键
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    # 将原id字段重命名为task_id，用于存储UUID
    task_id = Column(String(50), unique=True, index=True, nullable=False)
    task_type = Column(String(50), nullable=False)  # 任务类型（翻译、视频生成等）
    status = Column(String(20), default="pending")  # pending, running, completed, failed
    progress = Column(Integer, default=0)       # 进度百分比
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)  # 添加更新时间字段
    started_at = Column(DateTime, nullable=True)
    completed_at = Column(DateTime, nullable=True)
    input_data = Column(JSON, nullable=True)    # 输入参数
    output_data = Column(JSON, nullable=True)   # 输出结果
    error = Column(JSON, nullable=True)         # 错误信息
    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=True)
    celery_task_id = Column(String(50), nullable=True)  # Celery任务ID
    
    # 旧的特定资源关联 - 保留以向后兼容
    digital_human_id = Column(String, ForeignKey("digital_humans.digital_human_id"), nullable=True)    # 关联的数字人ID

    # 新的通用资源关联 - 更灵活的设计
    resource_type = Column(String(50), nullable=True)   # 资源类型 (digital_human, document, audio等)
    resource_id = Column(String, nullable=True)         # 资源ID

    # 存储相关字段
    input_files = Column(JSON, nullable=True)   # 输入文件路径列表
    output_files = Column(JSON, nullable=True)  # 输出文件路径列表

    # 添加缺失的字段
    message = Column(Text, nullable=True)       # 任务消息/描述
    result_url = Column(String(500), nullable=True)  # 结果文件URL
    audio_url = Column(String(500), nullable=True)   # 音频文件URL
    thumbnail_url = Column(String(500), nullable=True)  # 缩略图文件URL
    
    # 关系定义 - 使用更明确的方式
    # 用户关系 - 简单引用
    user = relationship("User", back_populates="tasks")
    
    # 数字人关系 - 使用更简单的方式定义多对一关系
    digital_human = relationship(
        "DigitalHuman", 
        back_populates="tasks",
        foreign_keys=[digital_human_id]
    )
    
    # 任务日志关系 - 简化关系定义
    logs = relationship(
        "TaskLog", 
        back_populates="task", 
        cascade="all, delete-orphan"
    )

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "task_id": self.task_id,
            "task_type": self.task_type,
            "status": self.status,
            "progress": self.progress,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "input_data": self.input_data,
            "output_data": self.output_data,
            "error": self.error,
            "user_id": self.user_id,
            "celery_task_id": self.celery_task_id,
            "digital_human_id": self.digital_human_id,
            "resource_type": self.resource_type,
            "resource_id": self.resource_id,
            "input_files": self.input_files,
            "output_files": self.output_files,
            "message": self.message,
            "result_url": self.result_url,
            "audio_url": self.audio_url,
            "thumbnail_url": self.thumbnail_url
        }

# 为了保持向后兼容性，创建Task类作为TaskModel的别名
Task = TaskModel

class TaskLog(Base):
    """任务日志 - 记录任务执行过程中的日志"""
    __tablename__ = "task_logs"
    __table_args__ = {'extend_existing': True}  # 允许表被多次定义
    
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    task_id = Column(String(50), ForeignKey("tasks.task_id", ondelete="CASCADE"), nullable=False)  # 关联到tasks.task_id
    log_type = Column(String(20), default="info")  # info, warning, error
    message = Column(Text, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # 关系 - 简化定义，不使用foreign()函数
    task = relationship(
        "TaskModel", 
        back_populates="logs"
    )
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "task_id": self.task_id,
            "log_type": self.log_type,
            "message": self.message,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }

# Pydantic模型，用于API请求和响应
class TaskCreate(BaseModel):
    """任务创建模型"""
    task_type: str
    digital_human_id: Optional[str] = None
    resource_type: Optional[str] = None
    resource_id: Optional[str] = None
    user_id: Optional[int] = None
    status: str = "queued"
    progress: int = 0
    message: Optional[str] = None
    parameters: Optional[Dict[str, Any]] = None

class TaskUpdate(BaseModel):
    """任务更新模型"""
    status: Optional[str] = None
    progress: Optional[int] = None
    result_url: Optional[str] = None
    audio_url: Optional[str] = None
    thumbnail_url: Optional[str] = None
    message: Optional[str] = None
    logs: Optional[List[Dict[str, Any]]] = None
    celery_task_id: Optional[str] = None
    parameters: Optional[Dict[str, Any]] = None

class TaskOutput(BaseModel):
    """任务输出模型"""
    task_id: str
    task_type: str
    digital_human_id: Optional[str] = None
    resource_type: Optional[str] = None
    resource_id: Optional[str] = None
    status: str
    progress: int
    result_url: Optional[str] = None
    audio_url: Optional[str] = None
    thumbnail_url: Optional[str] = None
    logs: List[Dict[str, Any]] = []
    message: Optional[str] = None
    parameters: Optional[Dict[str, Any]] = None
    created_at: Optional[str] = None
    updated_at: Optional[str] = None
    time_remaining: Optional[int] = None
    
    model_config = ConfigDict(
        from_attributes=True,
        arbitrary_types_allowed=True
    )