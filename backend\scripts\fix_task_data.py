#!/usr/bin/env python3
"""
修复任务数据脚本
此脚本用于修复tasks表中的数据问题:
1. 将id字段从整数转换为字符串
2. 确保task_type字段有有效值
"""
import os
import sys
import logging
import psycopg2
from psycopg2 import sql
from dotenv import load_dotenv
import uuid

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def fix_task_data():
    """修复tasks表中的数据问题"""
    try:
        # 加载环境变量
        load_dotenv()
        
        # 获取数据库URL
        database_url = os.getenv("DATABASE_URL", "")
        
        # 如果环境变量中没有设置，使用默认连接信息
        if not database_url or not database_url.startswith("postgresql://"):
            logger.warning("未在环境变量中找到DATABASE_URL，使用默认连接信息")
            # 默认连接信息 - 根据实际情况修改这些值
            hostname = "localhost"
            port = 5432
            username = "postgres"
            password = "postgres"
            dbname = "ai_platform"
        else:
            # 解析连接URL
            url_parts = database_url.replace("postgresql://", "").split("/")
            if len(url_parts) < 2:
                logger.error("数据库URL格式不正确")
                return False
                
            # 获取主机部分和数据库名
            auth_host = url_parts[0]
            dbname = url_parts[1].split("?")[0]
            
            # 分离用户名密码和主机
            if "@" in auth_host:
                auth, host = auth_host.split("@")
                if ":" in auth:
                    username, password = auth.split(":")
                else:
                    username = auth
                    password = ""
            else:
                host = auth_host
                username = "postgres"
                password = ""
                
            # 分离端口
            if ":" in host:
                hostname, port = host.split(":")
            else:
                hostname = host
                port = 5432
            
        logger.info(f"连接到数据库: {dbname} 在 {hostname}:{port}")
        
        # 连接到数据库
        conn = psycopg2.connect(
            host=hostname,
            port=int(port),
            user=username,
            password=password,
            dbname=dbname
        )
        conn.autocommit = True  # 自动提交是必须的
        
        success = True
        
        try:
            with conn.cursor() as cursor:
                # 1. 检查id字段的类型
                cursor.execute("""
                    SELECT column_name, data_type 
                    FROM information_schema.columns 
                    WHERE table_name = 'tasks' AND column_name = 'id'
                """)
                
                id_column_info = cursor.fetchone()
                if id_column_info:
                    column_name, data_type = id_column_info
                    logger.info(f"ID列的当前类型: {data_type}")
                    
                    # 如果id列不是字符串类型(varchar或text)
                    if 'char' not in data_type and 'text' not in data_type:
                        logger.info("ID列类型不是字符串，需要转换")
                        
                        try:
                            # 创建临时列存储原始ID
                            cursor.execute("ALTER TABLE tasks ADD COLUMN original_id INTEGER")
                            cursor.execute("UPDATE tasks SET original_id = id::INTEGER")
                            
                            # 获取现有任务
                            cursor.execute("SELECT id FROM tasks ORDER BY id")
                            tasks = cursor.fetchall()
                            
                            # 创建新的字符串ID并更新
                            for task_id in tasks:
                                # 生成UUID或使用字符串格式化的原始ID
                                new_id = str(uuid.uuid4()) if args.use_uuid else str(task_id[0])
                                cursor.execute(
                                    "UPDATE tasks SET id = %s WHERE id = %s",
                                    (new_id, task_id[0])
                                )
                                
                            logger.info(f"已将 {len(tasks)} 个任务的ID从整数转换为字符串")
                        except Exception as e:
                            logger.error(f"转换ID列时出错: {e}")
                            success = False
                
                # 2. 修复task_type字段的值
                try:
                    # 检查task_type字段
                    cursor.execute("""
                        SELECT count(*) 
                        FROM tasks 
                        WHERE task_type IS NULL OR task_type = 'unknown'
                    """)
                    
                    null_task_types = cursor.fetchone()[0]
                    if null_task_types > 0:
                        logger.info(f"找到 {null_task_types} 个任务的task_type为NULL或'unknown'，尝试修复")
                        
                        # 基于任务的其他属性推断task_type
                        cursor.execute("""
                            UPDATE tasks
                            SET task_type = 
                                CASE 
                                    WHEN input_data->>'source_language' IS NOT NULL AND input_data->>'target_language' IS NOT NULL THEN 
                                        CASE 
                                            WHEN input_data->>'audio_url' IS NOT NULL THEN 'audio_translation'
                                            WHEN input_data->>'video_url' IS NOT NULL THEN 'video_translation'
                                            ELSE 'text_translation' 
                                        END
                                    WHEN input_data->>'text' IS NOT NULL AND input_data->>'voice_id' IS NOT NULL THEN 'text_to_speech'
                                    WHEN input_data->>'audio_url' IS NOT NULL AND input_data->>'transcript' IS NULL THEN 'speech_to_text'
                                    WHEN digital_human_id IS NOT NULL THEN 'digital_human'
                                    ELSE 'general_task'
                                END
                            WHERE task_type IS NULL OR task_type = 'unknown'
                        """)
                        
                        logger.info(f"已更新 {cursor.rowcount} 个任务的task_type值")
                        
                        # 检查是否还有未设置task_type的任务
                        cursor.execute("""
                            SELECT count(*) 
                            FROM tasks 
                            WHERE task_type IS NULL OR task_type = 'unknown'
                        """)
                        
                        remaining_null = cursor.fetchone()[0]
                        if remaining_null > 0:
                            logger.warning(f"仍有 {remaining_null} 个任务的task_type未能推断，设置为'general_task'")
                            cursor.execute("""
                                UPDATE tasks
                                SET task_type = 'general_task'
                                WHERE task_type IS NULL OR task_type = 'unknown'
                            """)
                except Exception as e:
                    logger.error(f"修复task_type值时出错: {e}")
                    success = False
                
                # 3. 确保output_data列不为NULL
                try:
                    cursor.execute("""
                        SELECT count(*) 
                        FROM tasks 
                        WHERE output_data IS NULL
                    """)
                    
                    null_output_data = cursor.fetchone()[0]
                    if null_output_data > 0:
                        logger.info(f"找到 {null_output_data} 个任务的output_data为NULL，设置为空JSON对象")
                        cursor.execute("""
                            UPDATE tasks
                            SET output_data = '{}'::jsonb
                            WHERE output_data IS NULL
                        """)
                        
                        logger.info(f"已更新 {cursor.rowcount} 个任务的output_data值")
                except Exception as e:
                    logger.error(f"修复output_data值时出错: {e}")
                    success = False
                
                # 4. 验证修复结果
                cursor.execute("""
                    SELECT 
                        (SELECT COUNT(*) FROM tasks WHERE task_type IS NULL) AS null_types,
                        (SELECT COUNT(*) FROM tasks WHERE output_data IS NULL) AS null_outputs
                """)
                
                result = cursor.fetchone()
                if result[0] == 0 and result[1] == 0:
                    logger.info("验证成功: 没有NULL的task_type和output_data")
                else:
                    logger.warning(f"验证结果: 有 {result[0]} 个NULL的task_type 和 {result[1]} 个NULL的output_data")
                    
                return success
        finally:
            conn.close()
            
    except psycopg2.Error as e:
        logger.error(f"PostgreSQL错误: {e}", exc_info=True)
        return False
    except Exception as e:
        logger.error(f"修复数据时发生未知错误: {e}", exc_info=True)
        return False

def get_connection_params():
    """交互式获取数据库连接参数"""
    print("\n请输入PostgreSQL数据库连接信息：")
    hostname = input("主机名 [localhost]: ") or "localhost"
    port = input("端口 [5432]: ") or "5432"
    dbname = input("数据库名 [ai_platform]: ") or "ai_platform"
    username = input("用户名 [postgres]: ") or "postgres"
    password = input("密码 [postgres]: ") or "postgres"
    
    return {
        "hostname": hostname,
        "port": port,
        "dbname": dbname,
        "username": username,
        "password": password
    }

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='修复任务数据')
    parser.add_argument('--interactive', action='store_true', help='交互式输入数据库连接信息')
    parser.add_argument('--use-uuid', action='store_true', help='使用UUID替代原始ID')
    parser.add_argument('--yes', '-y', action='store_true', help='自动确认所有操作，不提示')
    
    args = parser.parse_args()
    
    print("===== 修复任务数据 =====")
    
    if not args.yes:
        confirm = input("此操作将修改数据库中的任务数据。确认继续? [y/N]: ")
        if confirm.lower() != 'y':
            print("操作已取消")
            sys.exit(0)
    
    # 交互式模式
    if args.interactive:
        print("您选择了交互模式，将引导您输入数据库连接信息")
        conn_params = get_connection_params()
        # 设置环境变量
        os.environ["DATABASE_URL"] = f"postgresql://{conn_params['username']}:{conn_params['password']}@{conn_params['hostname']}:{conn_params['port']}/{conn_params['dbname']}"
    
    # 修复任务数据
    if fix_task_data():
        print("\n✅ 任务数据修复成功")
    else:
        print("\n❌ 任务数据修复失败")
        sys.exit(1)
        
    print("\n✅ 操作完成")
    sys.exit(0) 