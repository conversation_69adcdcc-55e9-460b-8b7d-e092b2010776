import os
import subprocess
import logging
import tempfile
import uuid
import asyncio
import json
import shutil
from typing import Dict, Any, Optional, List
from services.progress_updater import ProgressUpdater

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class Wan2_1Adapter:
    """
    适配器类，用于调用Wan2.1官方脚本进行视频生成
    """
    
    def __init__(self, 
                 wan2_1_dir: str = None,
                 output_dir: str = None,
                 device: str = "cuda",
                 mock_mode: bool = False):
        """
        初始化Wan2.1适配器
        
        Args:
            wan2_1_dir: Wan2.1官方仓库路径
            output_dir: 输出目录
            device: 设备 (cuda/cpu)
            mock_mode: 是否使用模拟模式
        """
        # 设置Wan2.1目录
        if wan2_1_dir is None:
            # 默认Wan2.1目录位于当前项目根目录下的Wan2.1文件夹
            self.wan2_1_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "Wan2.1")
        else:
            self.wan2_1_dir = wan2_1_dir
        
        # 检查Wan2.1目录是否存在
        if not os.path.exists(self.wan2_1_dir):
            logger.warning(f"Wan2.1目录不存在: {self.wan2_1_dir}")
            logger.info("请克隆官方仓库: git clone https://github.com/Wan-Video/Wan2.1.git")
        
        # 设置模型缓存目录
        self.model_cache_dir = os.path.join(self.wan2_1_dir, "models_cache")
        os.makedirs(self.model_cache_dir, exist_ok=True)
        
        # 设置输出目录
        if output_dir is None:
            self.output_dir = os.path.join(os.getcwd(), "media")
        else:
            self.output_dir = output_dir
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 其他设置
        self.device = device
        self.mock_mode = mock_mode
        
        logger.info(f"Wan2.1适配器初始化完成: 目录={self.wan2_1_dir}, 输出目录={self.output_dir}")
    
    async def generate_video_from_text(
        self,
        task_id: str,
        prompt: str,
        negative_prompt: str = "",
        num_frames: int = 16,
        height: int = 576,
        width: int = 1024,
        num_inference_steps: int = 50,
        guidance_scale: float = 7.5,
        seed: int = None,
        output_format: str = "mp4",
        fps: int = 8,
        model_size: str = "1.3B",
        progress_updater: Optional[ProgressUpdater] = None,
        use_mock: bool = False,
        force_real_mode: bool = False,
        **kwargs
    ) -> Dict[str, Any]:
        """
        从文本生成视频，调用Wan2.1官方脚本
        
        Args:
            task_id: 任务ID
            prompt: 提示词
            negative_prompt: 负面提示词
            num_frames: 帧数
            height: 高度
            width: 宽度
            num_inference_steps: 推理步数
            guidance_scale: 引导比例
            seed: 随机种子
            output_format: 输出格式
            fps: 帧率
            model_size: 模型大小 ('14B', '1.3B')
            progress_updater: 进度更新器
            use_mock: 是否使用模拟模式
            force_real_mode: 是否强制使用真实模式
            **kwargs: 其他参数
            
        Returns:
            Dict[str, Any]: 包含视频生成结果的字典
        """
        try:
            # 设置进度更新器
            if progress_updater:
                await progress_updater.update(
                    progress=5,
                    logs=[f"开始准备Wan2.1文本到视频生成..."]
                )
            
            # 准备模型目录
            ckpt_dir = os.path.join(self.model_cache_dir, f"Wan2.1-T2V-{model_size}")
            
            if not os.path.exists(ckpt_dir):
                # 如果模型目录不存在，提示下载
                if progress_updater:
                    await progress_updater.update(
                        progress=10,
                        logs=[f"模型目录不存在: {ckpt_dir}，请先下载模型"]
                    )
                return {
                    "success": False,
                    "error": f"模型目录不存在: {ckpt_dir}，请先下载模型",
                    "task_id": task_id
                }
            
            # 准备命令参数
            size_param = f"{width}*{height}"
            task_param = f"t2v-{model_size}"
            
            # 准备命令行参数
            cmd_args = [
                "python", "generate.py",
                "--task", task_param,
                "--size", size_param,
                "--ckpt_dir", ckpt_dir,
                "--prompt", prompt
            ]
            
            # 添加可选参数
            if negative_prompt:
                cmd_args.extend(["--negative_prompt", negative_prompt])
            
            if seed is not None:
                cmd_args.extend(["--base_seed", str(seed)])
            
            # 添加帧数
            cmd_args.extend(["--frame_num", str(num_frames)])
            
            # 添加采样步数
            cmd_args.extend(["--sample_steps", str(num_inference_steps)])
            
            # 添加引导系数
            cmd_args.extend(["--sample_guide_scale", str(guidance_scale)])
            
            # 添加特殊处理 - 根据Wan2.1文档的建议
            if model_size == "1.3B":
                cmd_args.extend(["--sample_shift", "8"])
                if not any("--sample_guide_scale" in arg for arg in cmd_args):
                    cmd_args.extend(["--sample_guide_scale", "6"])
            
            # 是否使用离线模式
            if use_mock or self.mock_mode:
                cmd_args.extend(["--offload_model", "True", "--t5_cpu"])
            
            # 添加输出路径
            output_filename = f"video_{task_id}_{uuid.uuid4()}.{output_format}"
            output_path = os.path.join(self.output_dir, "videos", output_filename)
            os.makedirs(os.path.join(self.output_dir, "videos"), exist_ok=True)
            
            if progress_updater:
                await progress_updater.update(
                    progress=20,
                    logs=[f"准备运行Wan2.1生成脚本..."]
                )
            
            # 记录命令
            cmd_str = " ".join(cmd_args)
            logger.info(f"运行命令: {cmd_str}")
            
            # 运行命令
            process = await asyncio.create_subprocess_exec(
                *cmd_args,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=self.wan2_1_dir
            )
            
            # 实时处理输出
            progress = 20
            async for line in process.stdout:
                line_text = line.decode('utf-8').strip()
                logger.info(f"Wan2.1输出: {line_text}")
                
                # 根据输出更新进度
                if "Generating prompt embeddings" in line_text:
                    progress = 30
                elif "Preparing video generation" in line_text:
                    progress = 40
                elif "Running inference" in line_text:
                    progress = 50
                elif "step" in line_text and "/" in line_text:
                    # 尝试从步骤信息中提取进度
                    try:
                        step_info = line_text.split("step")[1].split("/")
                        current_step = int(step_info[0])
                        total_steps = int(step_info[1].split()[0])
                        step_progress = int(50 * current_step / total_steps)
                        progress = 50 + step_progress
                    except:
                        pass
                elif "Saving output" in line_text:
                    progress = 95
                
                if progress_updater:
                    await progress_updater.update(
                        progress=progress,
                        logs=[line_text]
                    )
            
            # 获取错误输出
            stderr_data = await process.stderr.read()
            stderr_text = stderr_data.decode('utf-8')
            if stderr_text:
                logger.error(f"Wan2.1错误输出: {stderr_text}")
            
            # 等待进程结束
            await process.wait()
            
            # 检查是否生成成功
            return_code = process.returncode
            if return_code != 0:
                if progress_updater:
                    await progress_updater.update(
                        progress=100,
                        logs=[f"生成失败，返回码: {return_code}"]
                    )
                return {
                    "success": False,
                    "error": f"Wan2.1生成失败，返回码: {return_code}，错误信息: {stderr_text}",
                    "task_id": task_id
                }
            
            # 查找生成的视频文件
            video_files = []
            for root, _, files in os.walk(os.path.join(self.wan2_1_dir, "outputs")):
                for file in files:
                    if file.endswith((".mp4", ".avi", ".mov", ".webm")):
                        video_files.append(os.path.join(root, file))
            
            video_files.sort(key=os.path.getmtime, reverse=True)
            
            if not video_files:
                if progress_updater:
                    await progress_updater.update(
                        progress=100,
                        logs=[f"生成成功，但未找到视频文件"]
                    )
                return {
                    "success": False,
                    "error": "未找到生成的视频文件",
                    "task_id": task_id
                }
            
            # 使用最新的视频文件
            generated_video = video_files[0]
            
            # 复制到输出目录
            shutil.copy2(generated_video, output_path)
            
            # 生成缩略图
            thumbnail_path = self._generate_thumbnail(output_path)
            
            if progress_updater:
                await progress_updater.update(
                    progress=100,
                    logs=[f"视频生成完成: {output_path}"]
                )
            
            # 返回结果
            return {
                "success": True,
                "task_id": task_id,
                "video_url": self._convert_path_to_url(output_path, "video"),
                "thumbnail_url": self._convert_path_to_url(thumbnail_path, "image") if thumbnail_path else "",
                "video_path": output_path,
                "seed": seed,
                "frames": num_frames,
                "fps": fps,
                "width": width,
                "height": height,
                "prompt": prompt,
                "negative_prompt": negative_prompt,
                "guidance_scale": guidance_scale,
                "model_size": model_size,
                "resolution": f"{width}x{height}",
                "mock_mode": use_mock or self.mock_mode
            }
        
        except Exception as e:
            logger.error(f"Wan2.1生成时出错: {str(e)}", exc_info=True)
            if progress_updater:
                await progress_updater.update(
                    progress=100,
                    logs=[f"生成时出错: {str(e)}"]
                )
            return {
                "success": False,
                "error": f"生成时出错: {str(e)}",
                "task_id": task_id
            }
    
    async def generate_video_from_image(
        self,
        task_id: str,
        image_path: str,
        prompt: str = "",
        negative_prompt: str = "",
        num_frames: int = 16,
        num_inference_steps: int = 25,
        guidance_scale: float = 9.0,
        seed: int = None,
        output_format: str = "mp4",
        fps: int = 8,
        progress_updater: Optional[ProgressUpdater] = None,
        use_mock: bool = False,
        **kwargs
    ) -> Dict[str, Any]:
        """
        从图像生成视频，调用Wan2.1官方脚本
        
        Args:
            task_id: 任务ID
            image_path: 图像路径
            prompt: 提示词
            negative_prompt: 负面提示词
            num_frames: 帧数
            num_inference_steps: 推理步数
            guidance_scale: 引导比例
            seed: 随机种子
            output_format: 输出格式
            fps: 帧率
            progress_updater: 进度更新器
            use_mock: 是否使用模拟模式
            **kwargs: 其他参数
            
        Returns:
            Dict[str, Any]: 包含视频生成结果的字典
        """
        try:
            # 设置进度更新器
            if progress_updater:
                await progress_updater.update(
                    progress=5,
                    logs=[f"开始准备Wan2.1图像到视频生成..."]
                )
            
            # 准备模型目录
            # 默认使用720p模型
            ckpt_dir = os.path.join(self.model_cache_dir, f"Wan2.1-I2V-14B-720P")
            
            if not os.path.exists(ckpt_dir):
                # 尝试使用480p模型
                ckpt_dir = os.path.join(self.model_cache_dir, f"Wan2.1-I2V-14B-480P")
                
                if not os.path.exists(ckpt_dir):
                    # 如果模型目录不存在，提示下载
                    if progress_updater:
                        await progress_updater.update(
                            progress=10,
                            logs=[f"模型目录不存在，请先下载模型"]
                        )
                    return {
                        "success": False,
                        "error": f"模型目录不存在，请先下载模型",
                        "task_id": task_id
                    }
            
            # 准备命令行参数
            cmd_args = [
                "python", "generate.py",
                "--task", "i2v-14B",
                "--ckpt_dir", ckpt_dir,
                "--image", image_path
            ]
            
            # 添加提示词
            if prompt:
                cmd_args.extend(["--prompt", prompt])
            
            # 添加可选参数
            if negative_prompt:
                cmd_args.extend(["--negative_prompt", negative_prompt])
            
            if seed is not None:
                cmd_args.extend(["--base_seed", str(seed)])
            
            # 添加帧数
            cmd_args.extend(["--frame_num", str(num_frames)])
            
            # 添加采样步数
            cmd_args.extend(["--sample_steps", str(num_inference_steps)])
            
            # 添加引导系数
            cmd_args.extend(["--sample_guide_scale", str(guidance_scale)])
            
            # 是否使用离线模式
            if use_mock or self.mock_mode:
                cmd_args.extend(["--offload_model", "True", "--t5_cpu"])
            
            # 添加输出路径
            output_filename = f"video_{task_id}_{uuid.uuid4()}.{output_format}"
            output_path = os.path.join(self.output_dir, "videos", output_filename)
            os.makedirs(os.path.join(self.output_dir, "videos"), exist_ok=True)
            
            if progress_updater:
                await progress_updater.update(
                    progress=20,
                    logs=[f"准备运行Wan2.1生成脚本..."]
                )
            
            # 记录命令
            cmd_str = " ".join(cmd_args)
            logger.info(f"运行命令: {cmd_str}")
            
            # 运行命令
            process = await asyncio.create_subprocess_exec(
                *cmd_args,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=self.wan2_1_dir
            )
            
            # 实时处理输出
            progress = 20
            async for line in process.stdout:
                line_text = line.decode('utf-8').strip()
                logger.info(f"Wan2.1输出: {line_text}")
                
                # 根据输出更新进度
                if "Generating prompt embeddings" in line_text:
                    progress = 30
                elif "Preparing video generation" in line_text:
                    progress = 40
                elif "Running inference" in line_text:
                    progress = 50
                elif "step" in line_text and "/" in line_text:
                    # 尝试从步骤信息中提取进度
                    try:
                        step_info = line_text.split("step")[1].split("/")
                        current_step = int(step_info[0])
                        total_steps = int(step_info[1].split()[0])
                        step_progress = int(50 * current_step / total_steps)
                        progress = 50 + step_progress
                    except:
                        pass
                elif "Saving output" in line_text:
                    progress = 95
                
                if progress_updater:
                    await progress_updater.update(
                        progress=progress,
                        logs=[line_text]
                    )
            
            # 获取错误输出
            stderr_data = await process.stderr.read()
            stderr_text = stderr_data.decode('utf-8')
            if stderr_text:
                logger.error(f"Wan2.1错误输出: {stderr_text}")
            
            # 等待进程结束
            await process.wait()
            
            # 检查是否生成成功
            return_code = process.returncode
            if return_code != 0:
                if progress_updater:
                    await progress_updater.update(
                        progress=100,
                        logs=[f"生成失败，返回码: {return_code}"]
                    )
                return {
                    "success": False,
                    "error": f"Wan2.1生成失败，返回码: {return_code}，错误信息: {stderr_text}",
                    "task_id": task_id
                }
            
            # 查找生成的视频文件
            video_files = []
            for root, _, files in os.walk(os.path.join(self.wan2_1_dir, "outputs")):
                for file in files:
                    if file.endswith((".mp4", ".avi", ".mov", ".webm")):
                        video_files.append(os.path.join(root, file))
            
            video_files.sort(key=os.path.getmtime, reverse=True)
            
            if not video_files:
                if progress_updater:
                    await progress_updater.update(
                        progress=100,
                        logs=[f"生成成功，但未找到视频文件"]
                    )
                return {
                    "success": False,
                    "error": "未找到生成的视频文件",
                    "task_id": task_id
                }
            
            # 使用最新的视频文件
            generated_video = video_files[0]
            
            # 复制到输出目录
            shutil.copy2(generated_video, output_path)
            
            # 生成缩略图
            thumbnail_path = self._generate_thumbnail(output_path)
            
            if progress_updater:
                await progress_updater.update(
                    progress=100,
                    logs=[f"视频生成完成: {output_path}"]
                )
            
            # 返回结果
            return {
                "success": True,
                "task_id": task_id,
                "video_url": self._convert_path_to_url(output_path, "video"),
                "thumbnail_url": self._convert_path_to_url(thumbnail_path, "image") if thumbnail_path else "",
                "video_path": output_path,
                "seed": seed,
                "frames": num_frames,
                "fps": fps,
                "prompt": prompt,
                "negative_prompt": negative_prompt,
                "guidance_scale": guidance_scale,
                "resolution": "720p",
                "mock_mode": use_mock or self.mock_mode
            }
        
        except Exception as e:
            logger.error(f"Wan2.1生成时出错: {str(e)}", exc_info=True)
            if progress_updater:
                await progress_updater.update(
                    progress=100,
                    logs=[f"生成时出错: {str(e)}"]
                )
            return {
                "success": False,
                "error": f"生成时出错: {str(e)}",
                "task_id": task_id
            }
    
    def _generate_thumbnail(self, video_path: str) -> str:
        """
        为视频生成缩略图
        
        Args:
            video_path: 视频文件路径
            
        Returns:
            str: 缩略图文件路径，如果生成失败则返回空字符串
        """
        try:
            import cv2
            
            # 检查视频文件是否存在
            if not os.path.exists(video_path):
                logger.error(f"视频文件不存在: {video_path}")
                return ""
            
            # 打开视频文件
            cap = cv2.VideoCapture(video_path)
            
            # 检查视频是否成功打开
            if not cap.isOpened():
                logger.error(f"无法打开视频文件: {video_path}")
                return ""
            
            # 从视频中获取第一帧作为缩略图
            ret, frame = cap.read()
            
            # 如果获取帧失败
            if not ret:
                logger.error(f"无法从视频获取帧: {video_path}")
                cap.release()
                return ""
            
            # 创建缩略图文件路径
            thumbnail_dir = os.path.join(self.output_dir, "thumbnails")
            os.makedirs(thumbnail_dir, exist_ok=True)
            
            thumbnail_filename = os.path.basename(video_path).split('.')[0] + ".jpg"
            thumbnail_path = os.path.join(thumbnail_dir, thumbnail_filename)
            
            # 保存缩略图
            cv2.imwrite(thumbnail_path, frame)
            
            # 释放视频对象
            cap.release()
            
            return thumbnail_path
        except Exception as e:
            logger.error(f"生成缩略图时出错: {str(e)}")
            return ""
    
    def _convert_path_to_url(self, file_path: str, file_type: str = "video") -> str:
        """
        将本地文件路径转换为可通过HTTP访问的URL
        
        Args:
            file_path: 本地文件路径
            file_type: 文件类型(video/image)
            
        Returns:
            str: HTTP URL
        """
        if not file_path:
            return ""
        
        # 获取文件名
        filename = os.path.basename(file_path)
        
        # 构建URL
        if file_type == "video":
            return f"/api/media/videos/{filename}"
        elif file_type == "image":
            return f"/api/media/thumbnails/{filename}"
        else:
            return f"/api/media/files/{filename}"
    
    @staticmethod
    def get_instance(wan2_1_dir: str = None, output_dir: str = None):
        """获取单例实例"""
        if not hasattr(Wan2_1Adapter, "_instance") or Wan2_1Adapter._instance is None:
            Wan2_1Adapter._instance = Wan2_1Adapter(wan2_1_dir, output_dir)
        return Wan2_1Adapter._instance 