"""
Coqui TTS 服务
提供高质量的多语言文本转语音功能
"""

import os
import logging
import tempfile
import time
import asyncio
import traceback
from typing import Dict, Any, List, Optional
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor

# 配置日志
logger = logging.getLogger(__name__)

# 导入 Coqui TTS
try:
    from TTS.api import TTS
    from TTS.utils.manage import ModelManager
    COQUI_TTS_AVAILABLE = True
    logger.info("Coqui TTS 库已成功导入")
except ImportError as e:
    COQUI_TTS_AVAILABLE = False
    logger.error(f"Coqui TTS 库导入失败: {e}")

class CoquiTTSService:
    """Coqui TTS 服务类"""
    
    def __init__(self):
        """初始化 Coqui TTS 服务"""
        self.initialized = False
        self.tts_models = {}
        self.current_model = None
        self.model_manager = None

        # 设置模型缓存目录
        self.model_cache_dir = os.path.join(
            os.path.dirname(os.path.dirname(__file__)),  # backend/
            "local_models",
            "coqui_tts"
        )
        os.makedirs(self.model_cache_dir, exist_ok=True)

        # 设置环境变量，让 Coqui TTS 使用我们的缓存目录
        os.environ["COQUI_TTS_CACHE_PATH"] = self.model_cache_dir

        # 检测GPU支持
        self.device = "cpu"
        self.gpu_available = False
        try:
            import torch
            if torch.cuda.is_available():
                self.device = "cuda"
                self.gpu_available = True
                logger.info(f"检测到GPU: {torch.cuda.get_device_name(0)}")
            else:
                logger.info("未检测到GPU，使用CPU")
        except ImportError:
            logger.info("PyTorch未安装，使用CPU")

        # 线程池执行器
        self.executor = ThreadPoolExecutor(max_workers=2)
        
        # 统计信息
        self.stats = {
            "requests": 0,
            "successes": 0,
            "failures": 0,
            "total_duration": 0.0
        }
        
        # 支持的语音配置
        self.voice_configs = {
            # 中文语音
            "zh-female1": {
                "model_name": "tts_models/zh-CN/baker/tacotron2-DDC-GST",
                "lang": "zh-CN",
                "gender": "female",
                "name": "中文女声(Baker)",
                "description": "高质量中文女声，适合朗读和对话"
            },
            "zh-female2": {
                "model_name": "tts_models/multilingual/multi-dataset/xtts_v2",
                "lang": "zh-cn",
                "gender": "female", 
                "name": "多语言女声(XTTS)",
                "description": "支持多语言的高质量女声，包含中文"
            },
            # 英文语音
            "en-female1": {
                "model_name": "tts_models/en/ljspeech/tacotron2-DDC",
                "lang": "en",
                "gender": "female",
                "name": "英文女声(LJSpeech)",
                "description": "清晰的英文女声"
            },
            "en-male1": {
                "model_name": "tts_models/en/sam/tacotron-DDC",
                "lang": "en", 
                "gender": "male",
                "name": "英文男声(SAM)",
                "description": "自然的英文男声"
            },
            # 多语言语音
            "multilingual-female1": {
                "model_name": "tts_models/multilingual/multi-dataset/xtts_v2",
                "lang": "multilingual",
                "gender": "female",
                "name": "多语言女声",
                "description": "支持100+语言的高质量女声"
            }
        }
        
        # 默认语音
        self.default_voice_id = "zh-female1"
        
        logger.info("Coqui TTS 服务初始化完成")
    
    async def initialize(self):
        """异步初始化服务"""
        if self.initialized:
            return True
            
        if not COQUI_TTS_AVAILABLE:
            logger.error("Coqui TTS 库未安装，无法初始化")
            return False
        
        try:
            # 在线程池中初始化模型管理器
            loop = asyncio.get_event_loop()

            def init_model_manager():
                # 确保在模型管理器初始化前设置缓存路径
                os.environ["COQUI_TTS_CACHE_PATH"] = self.model_cache_dir
                return ModelManager()

            self.model_manager = await loop.run_in_executor(
                self.executor,
                init_model_manager
            )
            
            # 预加载默认模型
            await self._load_model(self.default_voice_id)
            
            self.initialized = True
            logger.info("Coqui TTS 服务初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"初始化 Coqui TTS 服务失败: {e}")
            logger.error(traceback.format_exc())
            return False
    
    async def _load_model(self, voice_id: str) -> bool:
        """加载指定的TTS模型"""
        try:
            if voice_id not in self.voice_configs:
                logger.warning(f"未知的语音ID: {voice_id}")
                return False
            
            config = self.voice_configs[voice_id]
            model_name = config["model_name"]
            
            # 如果模型已加载，直接返回
            if voice_id in self.tts_models:
                self.current_model = self.tts_models[voice_id]
                return True
            
            logger.info(f"正在加载 Coqui TTS 模型: {model_name}")
            
            # 在线程池中加载模型
            loop = asyncio.get_event_loop()

            def load_model():
                # 确保在模型加载前设置缓存路径
                os.environ["COQUI_TTS_CACHE_PATH"] = self.model_cache_dir
                return TTS(model_name=model_name, progress_bar=False).to(self.device)

            tts_model = await loop.run_in_executor(
                self.executor,
                load_model
            )
            
            # 缓存模型
            self.tts_models[voice_id] = tts_model
            self.current_model = tts_model
            
            logger.info(f"模型 {model_name} 加载成功")
            return True
            
        except Exception as e:
            logger.error(f"加载模型失败: {e}")
            logger.error(traceback.format_exc())
            return False
    
    def get_available_voices(self) -> Dict[str, Dict]:
        """获取可用的语音列表"""
        return self.voice_configs
    
    def validate_voice_id(self, voice_id: str) -> str:
        """验证并返回有效的语音ID"""
        if voice_id in [None, 'system', '']:
            return self.default_voice_id
            
        # 支持原生 Edge TTS 语音名称映射
        edge_tts_mapping = {
            "zh-CN-XiaoxiaoNeural": "zh-female1",
            "zh-CN-XiaoyiNeural": "zh-female2", 
            "zh-CN-YunjianNeural": "zh-female1",  # 暂时映射到女声
            "en-US-JennyNeural": "en-female1",
            "en-US-GuyNeural": "en-male1"
        }
        
        if voice_id in edge_tts_mapping:
            return edge_tts_mapping[voice_id]
            
        if voice_id in self.voice_configs:
            return voice_id
            
        logger.warning(f"无效的语音ID: {voice_id}，使用默认值: {self.default_voice_id}")
        return self.default_voice_id
    
    async def generate_speech(self, 
                            text: str, 
                            voice_id: str = None, 
                            speed: float = 1.0,
                            output_path: str = None) -> Dict[str, Any]:
        """
        生成语音
        
        Args:
            text: 要转换的文本
            voice_id: 语音ID
            speed: 语速 (暂不支持，保留接口兼容性)
            output_path: 输出路径
            
        Returns:
            Dict: 包含结果的字典
        """
        start_time = time.time()
        self.stats["requests"] += 1
        
        # 检查服务是否可用
        if not COQUI_TTS_AVAILABLE:
            return {
                "status": "error",
                "message": "Coqui TTS 库未安装",
                "audio_path": None,
                "duration": 0
            }
        
        # 确保服务已初始化
        if not self.initialized:
            await self.initialize()
            
        if not self.initialized:
            return {
                "status": "error", 
                "message": "Coqui TTS 服务初始化失败",
                "audio_path": None,
                "duration": 0
            }
        
        try:
            # 验证语音ID
            voice_id = self.validate_voice_id(voice_id)
            
            # 加载对应模型
            if not await self._load_model(voice_id):
                return {
                    "status": "error",
                    "message": f"加载语音模型失败: {voice_id}",
                    "audio_path": None,
                    "duration": 0
                }
            
            # 生成输出路径
            if output_path is None:
                output_path = os.path.join(
                    tempfile.gettempdir(),
                    f"coqui_tts_{int(time.time())}_{hash(text) % 10000}.wav"
                )
            
            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # 在线程池中生成语音
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(
                self.executor,
                lambda: self.current_model.tts_to_file(
                    text=text,
                    file_path=output_path
                )
            )
            
            # 检查文件是否生成成功
            if os.path.exists(output_path) and os.path.getsize(output_path) > 0:
                duration = time.time() - start_time
                self.stats["successes"] += 1
                self.stats["total_duration"] += duration
                
                logger.info(f"Coqui TTS 生成成功: {output_path}, 耗时: {duration:.2f}s")
                
                return {
                    "status": "success",
                    "message": "语音生成成功",
                    "audio_path": output_path,
                    "duration": duration,
                    "voice_id": voice_id,
                    "model": self.voice_configs[voice_id]["model_name"]
                }
            else:
                raise Exception("生成的音频文件无效或为空")
                
        except Exception as e:
            duration = time.time() - start_time
            self.stats["failures"] += 1
            
            error_msg = f"Coqui TTS 生成失败: {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            
            return {
                "status": "error",
                "message": error_msg,
                "audio_path": None,
                "duration": duration
            }
    
    def get_stats(self) -> Dict[str, Any]:
        """获取服务统计信息"""
        avg_duration = (
            self.stats["total_duration"] / self.stats["requests"] 
            if self.stats["requests"] > 0 else 0
        )
        
        return {
            "service": "Coqui TTS",
            "requests": self.stats["requests"],
            "successes": self.stats["successes"], 
            "failures": self.stats["failures"],
            "success_rate": (
                self.stats["successes"] / self.stats["requests"] * 100
                if self.stats["requests"] > 0 else 0
            ),
            "average_duration": avg_duration,
            "available_voices": len(self.voice_configs),
            "loaded_models": len(self.tts_models)
        }
    
    async def cleanup(self):
        """清理资源"""
        try:
            # 清理模型缓存
            self.tts_models.clear()
            self.current_model = None
            
            # 关闭线程池
            if self.executor:
                self.executor.shutdown(wait=True)
                
            logger.info("Coqui TTS 服务资源清理完成")
            
        except Exception as e:
            logger.error(f"清理 Coqui TTS 服务资源时出错: {e}")

# 创建全局实例
coqui_tts_service = CoquiTTSService()
