<template>
  <div class="avatar-upload-container">
    <a-upload
      list-type="picture-card"
      :show-upload-list="false"
      :action="uploadUrl"
      :before-upload="beforeUpload"
      @change="handleChange"
    >
      <div v-if="loading" class="upload-loading">
        <a-spin />
      </div>
      <img v-else-if="imageUrl" :src="imageUrl" alt="avatar" class="avatar-image" />
      <div v-else class="upload-placeholder">
        <upload-outlined />
        <div class="upload-text">上传头像</div>
      </div>
    </a-upload>
    
    <!-- 裁剪对话框 -->
    <a-modal
      v-model:visible="cropVisible"
      title="裁剪头像"
      :width="600"
      :footer="null"
      :mask-closable="false"
      @cancel="cancelCrop"
    >
      <div class="crop-container">
        <vue-cropper
          ref="cropperRef"
          :img="cropImg"
          :info="true"
          :auto-crop="true"
          :auto-crop-width="200"
          :auto-crop-height="200"
          :fixed="true"
          :fixed-number="[1, 1]"
          :center-box="true"
          :high="true"
          output-type="png"
          @real-time="realTimeCrop"
        />
      </div>
      
      <div class="crop-control">
        <a-space>
          <a-button @click="cancelCrop">取消</a-button>
          <a-button type="primary" @click="confirmCrop" :loading="uploadLoading">确定</a-button>
        </a-space>
      </div>
    </a-modal>
  </div>
</template>

<script>
import { defineComponent, ref, computed, watch } from 'vue';
import { message } from 'ant-design-vue';
import { UploadOutlined } from '@ant-design/icons-vue';
import VueCropper from 'vue-cropper';
import 'vue-cropper/dist/index.css';

export default defineComponent({
  name: 'AvatarUpload',
  components: {
    UploadOutlined,
    VueCropper
  },
  props: {
    // 双向绑定的值
    value: {
      type: String,
      default: ''
    },
    // 上传地址
    action: {
      type: String,
      default: '/api/upload'
    },
    // 上传文件名
    fileName: {
      type: String,
      default: 'file'
    },
    // 是否开启裁剪
    cropEnabled: {
      type: Boolean,
      default: true
    },
    // 最大上传大小(MB)
    maxSize: {
      type: Number,
      default: 2
    }
  },
  emits: ['update:value', 'change', 'success', 'error'],
  setup(props, { emit }) {
    // 裁剪相关状态
    const cropperRef = ref(null);
    const cropVisible = ref(false);
    const cropImg = ref('');
    const previewUrl = ref('');
    const uploadLoading = ref(false);
    
    // 上传相关状态
    const loading = ref(false);
    const fileList = ref([]);
    
    // 当前头像URL
    const imageUrl = computed(() => props.value);
    
    // 上传地址
    const uploadUrl = computed(() => props.action);
    
    // 监听外部传入的value变化
    watch(() => props.value, (newVal) => {
      // 如果外部设置了新的URL，更新预览
      if (newVal && newVal !== previewUrl.value) {
        previewUrl.value = newVal;
      }
    }, { immediate: true });
    
    // 上传前检查
    const beforeUpload = (file) => {
      // 检查文件类型
      const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
      if (!isJpgOrPng) {
        message.error('只能上传JPG/PNG格式的图片！');
        return Upload.LIST_IGNORE;
      }
      
      // 检查文件大小
      const isLt2M = file.size / 1024 / 1024 < props.maxSize;
      if (!isLt2M) {
        message.error(`图片大小不能超过${props.maxSize}MB！`);
        return Upload.LIST_IGNORE;
      }
      
      // 如果开启了裁剪功能，阻止默认上传，显示裁剪对话框
      if (props.cropEnabled) {
        const reader = new FileReader();
        reader.onload = (e) => {
          cropImg.value = e.target.result;
          cropVisible.value = true;
        };
        reader.readAsDataURL(file);
        return false;
      }
      
      return true;
    };
    
    // 处理上传变化
    const handleChange = (info) => {
      if (info.file.status === 'uploading') {
        loading.value = true;
        return;
      }
      
      if (info.file.status === 'done') {
        // 处理上传成功
        loading.value = false;
        
        if (info.file.response && info.file.response.success) {
          const url = info.file.response.data || info.file.response.url;
          previewUrl.value = url;
          
          // 更新外部绑定的值
          emit('update:value', url);
          emit('change', url);
          emit('success', {
            file: info.file,
            url
          });
          
          message.success('上传成功');
        } else {
          message.error('上传失败：' + (info.file.response.message || '未知错误'));
          emit('error', info.file.response);
        }
      } else if (info.file.status === 'error') {
        loading.value = false;
        message.error('上传失败：' + (info.file.response?.message || '服务器错误'));
        emit('error', info.file.response);
      }
    };
    
    // 实时预览
    const realTimeCrop = (data) => {
      // 可以在这里获取实时的裁剪数据
    };
    
    // 确认裁剪
    const confirmCrop = () => {
      if (!cropperRef.value) return;
      
      uploadLoading.value = true;
      
      // 获取裁剪后的图片数据
      cropperRef.value.getCropBlob((blob) => {
        // 创建表单数据
        const formData = new FormData();
        formData.append(props.fileName, blob, 'avatar.png');
        
        // 上传裁剪后的图片
        fetch(props.action, {
          method: 'POST',
          body: formData,
          headers: {
            // 可以在这里添加自定义头信息，如认证信息等
          }
        })
          .then(response => response.json())
          .then(result => {
            uploadLoading.value = false;
            cropVisible.value = false;
            
            if (result.success) {
              const url = result.data || result.url;
              previewUrl.value = url;
              
              // 更新外部绑定的值
              emit('update:value', url);
              emit('change', url);
              emit('success', {
                url
              });
              
              message.success('上传成功');
            } else {
              message.error('上传失败：' + (result.message || '未知错误'));
              emit('error', result);
            }
          })
          .catch(error => {
            uploadLoading.value = false;
            cropVisible.value = false;
            message.error('上传失败：' + (error.message || '网络错误'));
            emit('error', error);
          });
      });
    };
    
    // 取消裁剪
    const cancelCrop = () => {
      cropVisible.value = false;
      cropImg.value = '';
    };
    
    return {
      cropperRef,
      cropVisible,
      cropImg,
      loading,
      uploadLoading,
      fileList,
      imageUrl,
      uploadUrl,
      beforeUpload,
      handleChange,
      realTimeCrop,
      confirmCrop,
      cancelCrop
    };
  }
});
</script>

<style lang="less" scoped>
.avatar-upload-container {
  text-align: center;
  
  :deep(.ant-upload) {
    width: 128px;
    height: 128px;
    border-radius: 50%;
    overflow: hidden;
    
    .avatar-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    
    .upload-loading {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.03);
    }
    
    .upload-placeholder {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 100%;
      
      .anticon {
        font-size: 24px;
        margin-bottom: 8px;
      }
      
      .upload-text {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.45);
      }
    }
  }
}

.crop-container {
  height: 300px;
  margin-bottom: 20px;
}

.crop-control {
  text-align: right;
}
</style> 