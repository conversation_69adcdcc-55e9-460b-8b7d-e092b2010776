#!/usr/bin/env python3
"""
高级AI对话服务
集成GPT和其他大语言模型
"""

import asyncio
import json
import logging
from typing import Optional, Dict, Any, List
import openai
from datetime import datetime
from .performance_optimizer import performance_optimizer
from .multilingual_service import multilingual_service

logger = logging.getLogger(__name__)

class AdvancedAIChat:
    """高级AI对话类"""
    
    def __init__(self, 
                 model_type: str = "openai",
                 model_name: str = "gpt-3.5-turbo",
                 api_key: Optional[str] = None,
                 base_url: Optional[str] = None):
        """
        初始化AI对话
        
        Args:
            model_type: 模型类型 (openai, azure, local)
            model_name: 模型名称
            api_key: API密钥
            base_url: API基础URL
        """
        self.model_type = model_type
        self.model_name = model_name
        self.api_key = api_key
        self.base_url = base_url
        
        # 系统提示词
        self.system_prompt = """你是一个专业的AI数字人助手，具有以下特点：

1. 友好亲切：始终保持温暖、友好的语调
2. 专业知识：拥有广泛的知识储备，能够回答各种问题
3. 情感理解：能够理解用户的情感状态并给予适当回应
4. 简洁明了：回答简洁有力，避免冗长
5. 个性化：根据对话历史调整回应风格

请用自然、流畅的中文回答，保持对话的连贯性。"""
        
        # 初始化客户端
        self.client = None
        self._initialize_client()
    
    def _initialize_client(self):
        """初始化AI客户端"""
        try:
            if self.model_type == "openai":
                if self.api_key:
                    openai.api_key = self.api_key
                if self.base_url:
                    openai.api_base = self.base_url
                self.client = openai
                logger.info(f"✅ OpenAI客户端初始化成功: {self.model_name}")
            
            elif self.model_type == "local":
                # 本地模型集成（如Ollama）
                self._initialize_local_model()
            
            else:
                logger.warning(f"⚠️  未知的模型类型: {self.model_type}")
                
        except Exception as e:
            logger.error(f"❌ AI客户端初始化失败: {e}")
    
    def _initialize_local_model(self):
        """初始化本地Ollama模型"""
        try:
            import requests

            # 测试Ollama连接
            test_url = self.base_url or "http://localhost:11434"
            response = requests.get(f"{test_url}/api/tags", timeout=5)

            if response.status_code == 200:
                models_data = response.json()
                available_models = [model["name"] for model in models_data.get("models", [])]

                # 选择最佳模型
                preferred_models = [
                    "qwen2.5:7b",
                    "llama3.2:3b",
                    "mistral:7b",
                    "gemma2:9b"
                ]

                selected_model = None
                for model in preferred_models:
                    if model in available_models:
                        selected_model = model
                        break

                if not selected_model and available_models:
                    selected_model = available_models[0]

                if selected_model:
                    self.client = "local"
                    self.model_name = selected_model
                    logger.info(f"✅ Ollama模型连接成功: {test_url}, 使用模型: {selected_model}")
                else:
                    logger.error(f"❌ 没有可用的Ollama模型")
            else:
                logger.error(f"❌ Ollama连接失败: {response.status_code}")

        except Exception as e:
            logger.error(f"❌ Ollama初始化失败: {e}")
    
    async def generate_response(self, 
                              user_input: str, 
                              conversation_history: List[Dict[str, str]] = None,
                              user_emotion: Optional[str] = None,
                              context: Optional[Dict[str, Any]] = None) -> str:
        """
        生成AI回复
        
        Args:
            user_input: 用户输入
            conversation_history: 对话历史
            user_emotion: 用户情感状态
            context: 额外上下文信息
            
        Returns:
            AI回复文本
        """
        try:
            # 构建消息列表
            messages = self._build_messages(user_input, conversation_history, user_emotion, context)
            
            if self.model_type == "openai" and self.client:
                return await self._generate_openai_response(messages)
            elif self.model_type == "local":
                return await self._generate_local_response(messages)
            else:
                # 回退到规则回复
                return self._generate_rule_based_response(user_input, user_emotion)
                
        except Exception as e:
            logger.error(f"❌ AI回复生成失败: {e}")
            return self._generate_fallback_response(user_input)
    
    def _build_messages(self, 
                       user_input: str, 
                       conversation_history: List[Dict[str, str]] = None,
                       user_emotion: Optional[str] = None,
                       context: Optional[Dict[str, Any]] = None) -> List[Dict[str, str]]:
        """构建消息列表"""
        messages = [{"role": "system", "content": self.system_prompt}]
        
        # 添加情感上下文
        if user_emotion:
            emotion_context = f"\n\n用户当前情感状态：{user_emotion}，请据此调整回应语调。"
            messages[0]["content"] += emotion_context
        
        # 添加额外上下文
        if context:
            context_info = f"\n\n当前上下文：{json.dumps(context, ensure_ascii=False)}"
            messages[0]["content"] += context_info
        
        # 添加对话历史（最近10轮）
        if conversation_history:
            recent_history = conversation_history[-10:]
            for msg in recent_history:
                messages.append({
                    "role": msg["role"],
                    "content": msg["content"]
                })
        
        # 添加当前用户输入
        messages.append({"role": "user", "content": user_input})
        
        return messages
    
    async def _generate_openai_response(self, messages: List[Dict[str, str]]) -> str:
        """生成OpenAI回复"""
        try:
            response = await asyncio.to_thread(
                self.client.ChatCompletion.create,
                model=self.model_name,
                messages=messages,
                max_tokens=200,
                temperature=0.7,
                top_p=0.9,
                frequency_penalty=0.1,
                presence_penalty=0.1
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            logger.error(f"❌ OpenAI API调用失败: {e}")
            raise
    
    async def _generate_local_response(self, messages: List[Dict[str, str]]) -> str:
        """生成Ollama模型回复（优化版）"""
        try:
            logger.info(f"🤖 调用优化的Ollama模型: {self.model_name}")

            # 使用性能优化器
            options = {
                "temperature": 0.7,
                "top_p": 0.9,
                "num_predict": 200,
                "num_ctx": 4096,
                "repeat_penalty": 1.1
            }

            result = await performance_optimizer.optimize_ollama_request(
                self.model_name, messages, options
            )

            ai_response = result.get("message", {}).get("content", "").strip()

            if ai_response:
                logger.info(f"✅ 优化Ollama回复成功: {ai_response[:50]}...")
                return ai_response
            else:
                raise Exception("Ollama返回空回复")

        except Exception as e:
            logger.error(f"❌ 优化Ollama调用失败: {e}")
            raise
    
    def _messages_to_prompt(self, messages: List[Dict[str, str]]) -> str:
        """将消息列表转换为提示词"""
        prompt_parts = []
        
        for msg in messages:
            role = msg["role"]
            content = msg["content"]
            
            if role == "system":
                prompt_parts.append(f"系统: {content}")
            elif role == "user":
                prompt_parts.append(f"用户: {content}")
            elif role == "assistant":
                prompt_parts.append(f"助手: {content}")
        
        prompt_parts.append("助手: ")
        return "\n\n".join(prompt_parts)
    
    def _generate_rule_based_response(self, user_input: str, user_emotion: Optional[str] = None) -> str:
        """生成基于规则的回复"""
        user_input_lower = user_input.lower()
        
        # 情感化回复
        if user_emotion == "happy":
            emotion_prefix = "我很高兴看到你心情不错！"
        elif user_emotion == "sad":
            emotion_prefix = "我理解你现在可能有些难过，"
        elif user_emotion == "angry":
            emotion_prefix = "我感受到你的情绪，让我们冷静地聊聊，"
        elif user_emotion == "surprised":
            emotion_prefix = "哇，这确实令人惊讶！"
        else:
            emotion_prefix = ""
        
        # 基础规则回复
        if "你好" in user_input_lower or "hello" in user_input_lower:
            return f"{emotion_prefix}你好！我是你的AI数字人助手，很高兴与你对话！有什么我可以帮助你的吗？"
        elif "数字人" in user_input_lower:
            return f"{emotion_prefix}我是一个先进的数字人，可以与你进行自然对话，理解你的情感，并提供个性化的回应。"
        elif "怎么样" in user_input_lower or "如何" in user_input_lower:
            return f"{emotion_prefix}我觉得很棒！我可以帮你解答问题，聊天，或者协助你完成各种任务。你想聊什么呢？"
        elif "再见" in user_input_lower or "拜拜" in user_input_lower:
            return f"{emotion_prefix}再见！很高兴与你聊天，希望我们的对话对你有帮助。期待下次见面！"
        elif "谢谢" in user_input_lower:
            return f"{emotion_prefix}不客气！我很乐意帮助你。还有什么其他问题或想聊的话题吗？"
        elif "心情" in user_input_lower or "感觉" in user_input_lower:
            return f"{emotion_prefix}我能感受到你的情感状态。无论你现在感觉如何，我都在这里陪伴你。想和我分享更多吗？"
        else:
            return f"{emotion_prefix}你说得很有趣！关于'{user_input}'，我想了解更多你的想法。能详细说说吗？"
    
    def _generate_fallback_response(self, user_input: str) -> str:
        """生成回退回复"""
        return f"我听到你说：{user_input}。虽然我现在无法给出完美的回答，但我很想继续和你聊天。能换个话题试试吗？"

class EmotionAnalyzer:
    """情感分析器"""
    
    def __init__(self):
        self.emotion_keywords = {
            "happy": ["开心", "高兴", "快乐", "兴奋", "愉快", "满意", "棒", "好", "哈哈"],
            "sad": ["难过", "伤心", "沮丧", "失望", "痛苦", "悲伤", "哭", "不好"],
            "angry": ["生气", "愤怒", "烦躁", "讨厌", "气死", "火大", "恼火"],
            "surprised": ["惊讶", "震惊", "意外", "没想到", "哇", "天哪", "不敢相信"],
            "fear": ["害怕", "恐惧", "担心", "紧张", "焦虑", "不安"],
            "neutral": ["好的", "知道", "明白", "了解", "可以", "行"]
        }
    
    def analyze_emotion(self, text: str) -> str:
        """分析文本情感"""
        text_lower = text.lower()
        
        emotion_scores = {}
        for emotion, keywords in self.emotion_keywords.items():
            score = sum(1 for keyword in keywords if keyword in text_lower)
            if score > 0:
                emotion_scores[emotion] = score
        
        if emotion_scores:
            return max(emotion_scores, key=emotion_scores.get)
        else:
            return "neutral"

# 全局实例
advanced_ai_chat = AdvancedAIChat()
emotion_analyzer = EmotionAnalyzer()
