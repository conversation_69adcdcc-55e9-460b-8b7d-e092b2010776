#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
网络爬虫模块
用于智能采集网页内容，支持多种爬取策略和过滤机制
"""

import logging
import re
import time
import random
import requests
from typing import Dict, List, Set, Any, Optional, Tuple
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
import concurrent.futures
from datetime import datetime, timedelta

# 创建日志记录器
logger = logging.getLogger("web_crawler")

# 请求头列表
USER_AGENTS = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36",
    "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1"
]

class WebCrawler:
    """网络爬虫类"""
    
    def __init__(self, max_depth: int = 2, max_pages: int = 10, delay: float = 1.0, timeout: int = 10, 
                 respect_robots: bool = True, parallel: bool = False, max_workers: int = 4):
        """
        初始化爬虫
        
        Args:
            max_depth: 最大爬取深度
            max_pages: 最大爬取页面数
            delay: 请求延迟时间（秒）
            timeout: 请求超时时间（秒）
            respect_robots: 是否尊重robots.txt规则
            parallel: 是否使用并行爬取
            max_workers: 并行爬取的最大工作线程数
        """
        self.max_depth = max_depth
        self.max_pages = max_pages
        self.delay = delay
        self.timeout = timeout
        self.respect_robots = respect_robots
        self.parallel = parallel
        self.max_workers = max_workers
        
        # 状态变量
        self.visited_urls = set()
        self.robots_rules = {}
        self.pages_crawled = 0
    
    def get_random_user_agent(self) -> str:
        """获取随机User-Agent"""
        return random.choice(USER_AGENTS)
    
    def get_headers(self) -> Dict[str, str]:
        """获取HTTP请求头"""
        return {
            "User-Agent": self.get_random_user_agent(),
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1"
        }
    
    def is_allowed_by_robots(self, url: str) -> bool:
        """
        检查URL是否被robots.txt允许爬取
        
        Args:
            url: 要检查的URL
            
        Returns:
            bool: 是否允许爬取
        """
        if not self.respect_robots:
            return True
        
        parsed_url = urlparse(url)
        base_url = f"{parsed_url.scheme}://{parsed_url.netloc}"
        
        # 如果已经缓存了该网站的robots规则，直接使用
        if base_url in self.robots_rules:
            rules = self.robots_rules[base_url]
        else:
            # 否则获取robots.txt并解析
            robots_url = urljoin(base_url, "/robots.txt")
            try:
                response = requests.get(robots_url, headers=self.get_headers(), timeout=self.timeout)
                if response.status_code == 200:
                    # 简单解析robots.txt
                    rules = self._parse_robots_txt(response.text)
                    self.robots_rules[base_url] = rules
                else:
                    # 如果无法获取robots.txt，假设允许所有
                    rules = []
                    self.robots_rules[base_url] = rules
            except Exception as e:
                logger.warning(f"[网络爬虫] 获取robots.txt失败: {robots_url}, 错误: {str(e)}")
                rules = []
                self.robots_rules[base_url] = rules
        
        # 检查URL是否被禁止
        path = parsed_url.path
        for rule in rules:
            if rule.startswith("Disallow:"):
                disallow_path = rule.replace("Disallow:", "").strip()
                if disallow_path and path.startswith(disallow_path):
                    return False
        
        return True
    
    def _parse_robots_txt(self, text: str) -> List[str]:
        """
        简单解析robots.txt内容
        
        Args:
            text: robots.txt内容
            
        Returns:
            list: 规则列表
        """
        rules = []
        user_agent_matched = False
        
        for line in text.split("\n"):
            line = line.strip()
            
            # 跳过空行和注释
            if not line or line.startswith("#"):
                continue
            
            # 检查User-agent
            if line.startswith("User-agent:"):
                agent = line.replace("User-agent:", "").strip()
                # 如果是通用规则或匹配当前爬虫
                if agent == "*" or "python" in agent.lower():
                    user_agent_matched = True
                else:
                    user_agent_matched = False
            
            # 如果当前User-agent匹配，记录规则
            elif user_agent_matched and (line.startswith("Disallow:") or line.startswith("Allow:")):
                rules.append(line)
        
        return rules
    
    def fetch_page(self, url: str) -> Optional[str]:
        """
        获取页面内容
        
        Args:
            url: 页面URL
            
        Returns:
            str: 页面HTML内容，如果获取失败则返回None
        """
        try:
            # 检查robots.txt规则
            if not self.is_allowed_by_robots(url):
                logger.info(f"[网络爬虫] URL被robots.txt禁止爬取: {url}")
                return None
            
            # 发送请求
            response = requests.get(url, headers=self.get_headers(), timeout=self.timeout)
            
            # 检查状态码
            if response.status_code != 200:
                logger.warning(f"[网络爬虫] 获取页面失败: {url}, 状态码: {response.status_code}")
                return None
            
            # 检查内容类型
            content_type = response.headers.get('Content-Type', '')
            if 'text/html' not in content_type.lower():
                logger.info(f"[网络爬虫] 非HTML内容，跳过: {url}, 内容类型: {content_type}")
                return None
            
            # 尝试检测编码
            if response.encoding == 'ISO-8859-1':
                # 可能是编码检测错误，尝试使用apparent_encoding
                response.encoding = response.apparent_encoding
            
            return response.text
        except Exception as e:
            logger.error(f"[网络爬虫] 获取页面出错: {url}, 错误: {str(e)}")
            return None
    
    def extract_title(self, html: str) -> str:
        """
        从HTML中提取页面标题
        
        Args:
            html: HTML内容
            
        Returns:
            str: 页面标题
        """
        try:
            soup = BeautifulSoup(html, 'html.parser')
            title = soup.find('title')
            return title.text.strip() if title else ""
        except Exception as e:
            logger.error(f"[网络爬虫] 提取标题出错: {str(e)}")
            return ""
    
    def extract_article_content(self, html: str) -> str:
        """
        从HTML中提取文章内容
        
        Args:
            html: HTML内容
            
        Returns:
            str: 文章内容
        """
        try:
            soup = BeautifulSoup(html, 'html.parser')
            
            # 移除脚本、样式和导航元素
            for script in soup(["script", "style", "nav", "footer", "header"]):
                script.decompose()
            
            # 尝试提取主要内容区域
            main_content = None
            
            # 1. 尝试查找常见的内容容器
            content_candidates = soup.select("article, .article, .post, .content, .entry, main, #content, #main, .main-content")
            if content_candidates:
                # 选择文本内容最多的候选元素
                main_content = max(content_candidates, key=lambda x: len(x.get_text()))
            
            # 2. 如果没有找到明确的内容容器，使用整个body
            if not main_content:
                main_content = soup.body
            
            # 提取文本
            if main_content:
                # 获取所有段落文本
                paragraphs = main_content.find_all(['p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6'])
                text = ' '.join(p.get_text().strip() for p in paragraphs)
                
                # 如果段落提取的文本太少，可能是非标准HTML，直接使用所有文本
                if len(text) < 100 and main_content:
                    text = main_content.get_text()
            else:
                text = soup.get_text()
            
            # 清理文本
            lines = (line.strip() for line in text.splitlines())
            chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
            text = ' '.join(chunk for chunk in chunks if chunk)
            
            return text
        except Exception as e:
            logger.error(f"[网络爬虫] 提取文章内容出错: {str(e)}")
            return ""
    
    def extract_links(self, html: str, base_url: str) -> List[str]:
        """
        从HTML中提取链接
        
        Args:
            html: HTML内容
            base_url: 基础URL，用于解析相对链接
            
        Returns:
            list: 链接列表
        """
        try:
            soup = BeautifulSoup(html, 'html.parser')
            
            # 提取所有链接
            links = []
            for a in soup.find_all('a', href=True):
                href = a['href'].strip()
                
                # 跳过空链接、锚点链接和JavaScript链接
                if not href or href.startswith('#') or href.startswith('javascript:'):
                    continue
                
                # 解析相对链接
                absolute_url = urljoin(base_url, href)
                
                # 解析URL
                parsed_url = urlparse(absolute_url)
                
                # 只保留HTTP和HTTPS链接
                if parsed_url.scheme not in ('http', 'https'):
                    continue
                
                # 检查是否是同一域名（可选，取决于是否只爬取同一网站）
                base_domain = urlparse(base_url).netloc
                if parsed_url.netloc != base_domain:
                    continue
                
                # 移除URL中的片段标识符
                absolute_url = absolute_url.split('#')[0]
                
                # 添加到链接列表
                links.append(absolute_url)
            
            # 去重
            return list(set(links))
        except Exception as e:
            logger.error(f"[网络爬虫] 提取链接出错: {str(e)}")
            return []
    
    def filter_content_by_keywords(self, text: str, keywords: List[str], match_all: bool = False, case_sensitive: bool = False) -> bool:
        """
        根据关键词过滤内容
        
        Args:
            text: 要过滤的文本
            keywords: 关键词列表
            match_all: 是否要求匹配所有关键词
            case_sensitive: 是否区分大小写
            
        Returns:
            bool: 是否匹配
        """
        if not keywords:
            return True
        
        # 如果不区分大小写，将文本和关键词转为小写
        if not case_sensitive:
            text = text.lower()
            keywords = [k.lower() for k in keywords]
        
        # 检查每个关键词
        matches = [keyword in text for keyword in keywords]
        
        # 根据匹配模式返回结果
        if match_all:
            return all(matches)  # 所有关键词都必须匹配
        else:
            return any(matches)  # 任一关键词匹配即可
    
    def filter_by_date_range(self, html: str, start_date: datetime = None, end_date: datetime = None) -> bool:
        """
        根据日期范围过滤内容
        
        Args:
            html: HTML内容
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            bool: 是否在日期范围内
        """
        if not start_date and not end_date:
            return True
        
        try:
            soup = BeautifulSoup(html, 'html.parser')
            
            # 尝试从meta标签中获取日期
            meta_date = None
            for meta in soup.find_all('meta'):
                if meta.get('property') in ['article:published_time', 'og:published_time', 'article:modified_time']:
                    meta_date = meta.get('content')
                    break
            
            # 如果meta标签没有日期，尝试从常见的日期容器中获取
            if not meta_date:
                date_containers = soup.select('.date, .time, .published, .modified, time')
                for container in date_containers:
                    # 尝试从datetime属性获取
                    if container.name == 'time' and container.get('datetime'):
                        meta_date = container.get('datetime')
                        break
                    # 尝试从文本内容获取
                    date_text = container.get_text().strip()
                    if date_text:
                        # 这里可以添加更复杂的日期解析逻辑
                        meta_date = date_text
                        break
            
            # 如果找到日期，解析并比较
            if meta_date:
                try:
                    # 尝试多种日期格式
                    for fmt in ['%Y-%m-%dT%H:%M:%S', '%Y-%m-%d', '%Y/%m/%d', '%d-%m-%Y', '%d/%m/%Y']:
                        try:
                            page_date = datetime.strptime(meta_date[:10], fmt)
                            
                            # 检查日期范围
                            if start_date and page_date < start_date:
                                return False
                            if end_date and page_date > end_date:
                                return False
                            
                            return True
                        except ValueError:
                            continue
                except Exception:
                    pass
        except Exception as e:
            logger.error(f"[网络爬虫] 日期过滤出错: {str(e)}")
        
        # 如果无法确定日期，默认通过过滤
        return True
    
    def crawl_page(self, url: str, depth: int, keywords: Optional[List[str]] = None, 
                  required_words: Optional[List[str]] = None, required_settings: Optional[Dict[str, bool]] = None,
                  restrict_words: Optional[List[str]] = None, restrict_settings: Optional[Dict[str, bool]] = None,
                  time_range: Optional[Dict[str, str]] = None) -> Optional[Dict[str, Any]]:
        """
        爬取单个页面
        
        Args:
            url: 页面URL
            depth: 当前深度
            keywords: 关键词列表
            required_words: 必须包含的词
            required_settings: 必须包含词的设置
            restrict_words: 限制词
            restrict_settings: 限制词的设置
            time_range: 时间范围
            
        Returns:
            dict: 页面信息，如果页面不符合条件则返回None
        """
        # 获取页面内容
        html = self.fetch_page(url)
        if not html:
            return None
        
        # 提取文本
        text = self.extract_article_content(html)
        if not text:
            return None
        
        # 检查必须包含的词
        if required_words:
            match_all = required_settings.get('matchAll', True) if required_settings else True
            case_sensitive = required_settings.get('caseSensitive', False) if required_settings else False
            if not self.filter_content_by_keywords(text, required_words, match_all, case_sensitive):
                logger.info(f"[网络爬虫] 页面不包含必要词语，跳过: {url}")
                return None
        
        # 检查限制词
        if restrict_words:
            match_any = restrict_settings.get('matchAny', True) if restrict_settings else True
            case_sensitive = restrict_settings.get('caseSensitive', False) if restrict_settings else False
            if self.filter_content_by_keywords(text, restrict_words, not match_any, case_sensitive):
                logger.info(f"[网络爬虫] 页面包含限制词语，跳过: {url}")
                return None
        
        # 检查时间范围
        if time_range:
            start_date = datetime.strptime(time_range.get('start', '2000-01-01'), '%Y-%m-%d') if time_range.get('start') else None
            end_date = datetime.strptime(time_range.get('end', '2100-01-01'), '%Y-%m-%d') if time_range.get('end') else None
            
            if not self.filter_by_date_range(html, start_date, end_date):
                logger.info(f"[网络爬虫] 页面不在指定时间范围内，跳过: {url}")
                return None
        
        # 检查关键词
        if keywords and not self.filter_content_by_keywords(text, keywords, False, False):
            logger.info(f"[网络爬虫] 页面不包含关键词，跳过: {url}")
            return None
        
        # 提取标题和链接
        title = self.extract_title(html)
        links = self.extract_links(html, url)
        
        # 返回页面信息
        return {
            "url": url,
            "title": title,
            "text": text,
            "depth": depth,
            "links": links
        }
    
    def crawl(self, start_url: str, keywords: Optional[List[str]] = None, 
              required_words: Optional[List[str]] = None, required_settings: Optional[Dict[str, bool]] = None,
              restrict_words: Optional[List[str]] = None, restrict_settings: Optional[Dict[str, bool]] = None,
              time_range: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
        """
        爬取网站
        
        Args:
            start_url: 起始URL
            keywords: 关键词列表
            required_words: 必须包含的词
            required_settings: 必须包含词的设置
            restrict_words: 限制词
            restrict_settings: 限制词的设置
            time_range: 时间范围
            
        Returns:
            dict: 爬取结果
        """
        # 重置状态
        self.visited_urls = set()
        self.pages_crawled = 0
        
        # 初始化结果
        result = {
            "start_url": start_url,
            "pages": [],
            "total_text": "",
            "crawled_pages": 0
        }
        
        logger.info(f"[网络爬虫] 开始爬取: {start_url}")
        
        # 使用BFS算法爬取
        queue = [(start_url, 0)]  # (url, depth)
        
        # 并行爬取
        if self.parallel:
            with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                while queue and self.pages_crawled < self.max_pages:
                    # 获取当前批次要爬取的URL
                    batch = []
                    while queue and len(batch) < self.max_workers:
                        url, depth = queue.pop(0)
                        if url not in self.visited_urls:
                            self.visited_urls.add(url)
                            batch.append((url, depth))
                    
                    if not batch:
                        continue
                    
                    # 提交爬取任务
                    future_to_url = {
                        executor.submit(
                            self.crawl_page, url, depth, keywords, 
                            required_words, required_settings,
                            restrict_words, restrict_settings,
                            time_range
                        ): (url, depth) for url, depth in batch
                    }
                    
                    # 处理结果
                    for future in concurrent.futures.as_completed(future_to_url):
                        url, depth = future_to_url[future]
                        try:
                            page_info = future.result()
                            if page_info:
                                result["pages"].append(page_info)
                                result["total_text"] += page_info["text"] + " "
                                self.pages_crawled += 1
                                
                                # 如果未达到最大深度，将新链接加入队列
                                if depth < self.max_depth:
                                    new_links = [(link, depth + 1) for link in page_info["links"] 
                                                if link not in self.visited_urls]
                                    queue.extend(new_links)
                        except Exception as e:
                            logger.error(f"[网络爬虫] 处理页面出错: {url}, 错误: {str(e)}")
                        
                        # 添加延迟
                        time.sleep(self.delay)
                        
                        # 检查是否达到最大页面数
                        if self.pages_crawled >= self.max_pages:
                            break
        # 串行爬取
        else:
            while queue and self.pages_crawled < self.max_pages:
                url, depth = queue.pop(0)
                
                # 跳过已访问的URL
                if url in self.visited_urls:
                    continue
                
                self.visited_urls.add(url)
                
                # 爬取页面
                page_info = self.crawl_page(
                    url, depth, keywords, 
                    required_words, required_settings,
                    restrict_words, restrict_settings,
                    time_range
                )
                
                # 添加延迟
                time.sleep(self.delay)
                
                if page_info:
                    result["pages"].append(page_info)
                    result["total_text"] += page_info["text"] + " "
                    self.pages_crawled += 1
                    
                    # 如果未达到最大深度，将新链接加入队列
                    if depth < self.max_depth:
                        new_links = [(link, depth + 1) for link in page_info["links"] 
                                    if link not in self.visited_urls]
                        queue.extend(new_links)
        
        result["crawled_pages"] = self.pages_crawled
        logger.info(f"[网络爬虫] 爬取完成: {start_url}, 共爬取 {self.pages_crawled} 个页面")
        
        return result 