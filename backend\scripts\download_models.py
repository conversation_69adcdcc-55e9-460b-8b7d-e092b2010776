"""
下载和安装数字人生成所需的模型
"""

import os
import sys
import asyncio
import logging
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.services.tts_model_manager import tts_model_manager
from app.services.model_manager import model_manager

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def download_essential_models():
    """下载基础必需的模型"""
    logger.info("开始下载基础模型...")
    
    # 1. 安装Edge TTS（最轻量级的TTS）
    logger.info("安装Edge TTS...")
    try:
        result = await tts_model_manager.install_model("edge_tts")
        if result["success"]:
            logger.info("✅ Edge TTS 安装成功")
        else:
            logger.error(f"❌ Edge TTS 安装失败: {result['error']}")
    except Exception as e:
        logger.error(f"❌ Edge TTS 安装异常: {str(e)}")
    
    # 2. 检查数字人模型
    logger.info("检查数字人模型...")
    available_models = model_manager.get_available_models()
    
    if available_models:
        logger.info("✅ 发现可用的数字人模型:")
        for model in available_models:
            logger.info(f"  - {model['display_name']}: {model['description']}")
    else:
        logger.warning("⚠️  未发现可用的数字人模型")
        logger.info("请确保以下模型已正确安装:")
        logger.info("  - MuseTalk: storage/models/digital_human/musetalk/")
        logger.info("  - SadTalker: storage/models/digital_human/sadtalker/")
        logger.info("  - Wav2Lip: storage/models/digital_human/wav2lip/")
        logger.info("  - LivePortrait: storage/models/digital_human/liveportrait/")

async def download_recommended_models():
    """下载推荐的高质量模型"""
    logger.info("开始下载推荐模型...")
    
    # 1. 安装Coqui TTS（高质量TTS）
    logger.info("安装Coqui TTS...")
    try:
        result = await tts_model_manager.install_model("coqui_tts")
        if result["success"]:
            logger.info("✅ Coqui TTS 安装成功")
        else:
            logger.error(f"❌ Coqui TTS 安装失败: {result['error']}")
    except Exception as e:
        logger.error(f"❌ Coqui TTS 安装异常: {str(e)}")

async def download_advanced_models():
    """下载高级模型（需要GPU）"""
    logger.info("开始下载高级模型...")
    
    # 1. 安装Bark（AI语音克隆）
    logger.info("安装Bark...")
    try:
        result = await tts_model_manager.install_model("bark")
        if result["success"]:
            logger.info("✅ Bark 安装成功")
        else:
            logger.error(f"❌ Bark 安装失败: {result['error']}")
    except Exception as e:
        logger.error(f"❌ Bark 安装异常: {str(e)}")

def check_system_requirements():
    """检查系统要求"""
    logger.info("检查系统要求...")
    
    # 检查Python版本
    python_version = sys.version_info
    if python_version.major == 3 and python_version.minor >= 8:
        logger.info(f"✅ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    else:
        logger.error(f"❌ Python版本过低: {python_version.major}.{python_version.minor}.{python_version.micro}")
        logger.error("   需要Python 3.8或更高版本")
    
    # 检查存储空间
    models_path = Path("storage/models")
    if models_path.exists():
        logger.info(f"✅ 模型存储目录存在: {models_path.absolute()}")
    else:
        logger.info(f"📁 创建模型存储目录: {models_path.absolute()}")
        models_path.mkdir(parents=True, exist_ok=True)
    
    # 检查GPU
    try:
        import torch
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            gpu_name = torch.cuda.get_device_name(0)
            logger.info(f"✅ 检测到GPU: {gpu_name} (共{gpu_count}个)")
        else:
            logger.warning("⚠️  未检测到GPU，将使用CPU模式（速度较慢）")
    except ImportError:
        logger.warning("⚠️  PyTorch未安装，无法检测GPU")

def print_usage_guide():
    """打印使用指南"""
    logger.info("\n" + "="*60)
    logger.info("🎉 模型安装完成！使用指南:")
    logger.info("="*60)
    logger.info("1. 启动后端服务:")
    logger.info("   cd backend && python main.py")
    logger.info("")
    logger.info("2. 启动前端服务:")
    logger.info("   cd frontend && npm run dev")
    logger.info("")
    logger.info("3. 访问数字人创建页面:")
    logger.info("   http://localhost:3000/digital-human/create")
    logger.info("")
    logger.info("4. TTS模型说明:")
    logger.info("   - Edge TTS: 免费，质量好，推荐日常使用")
    logger.info("   - Coqui TTS: 开源，高质量，支持多语言")
    logger.info("   - Bark: AI克隆，最高质量，需要GPU")
    logger.info("")
    logger.info("5. 数字人模型说明:")
    logger.info("   - Wav2Lip: 快速，适合实时应用")
    logger.info("   - MuseTalk: 高质量嘴型同步")
    logger.info("   - SadTalker: 表情丰富，适合演示")
    logger.info("   - LivePortrait: 最高质量，需要GPU")
    logger.info("="*60)

async def main():
    """主函数"""
    print("🚀 数字人模型下载器")
    print("="*60)
    
    # 检查系统要求
    check_system_requirements()
    
    print("\n请选择要安装的模型:")
    print("1. 基础模型（推荐新手）")
    print("2. 推荐模型（平衡质量和性能）")
    print("3. 高级模型（最高质量，需要GPU）")
    print("4. 全部安装")
    print("5. 仅检查现有模型")
    
    try:
        choice = input("\n请输入选择 (1-5): ").strip()
        
        if choice == "1":
            await download_essential_models()
        elif choice == "2":
            await download_essential_models()
            await download_recommended_models()
        elif choice == "3":
            await download_essential_models()
            await download_recommended_models()
            await download_advanced_models()
        elif choice == "4":
            await download_essential_models()
            await download_recommended_models()
            await download_advanced_models()
        elif choice == "5":
            logger.info("检查现有模型...")
            # 重新初始化模型管理器
            tts_model_manager.check_installed_models()
            model_manager.initialize_models()
            
            # 显示TTS模型状态
            tts_models = tts_model_manager.get_available_models()
            logger.info("TTS模型状态:")
            for model in tts_models:
                status = "✅ 可用" if model["available"] else "❌ 不可用"
                logger.info(f"  - {model['name']}: {status}")
            
            # 显示数字人模型状态
            dh_models = model_manager.get_available_models()
            logger.info("数字人模型状态:")
            if dh_models:
                for model in dh_models:
                    logger.info(f"  - {model['display_name']}: ✅ 可用")
            else:
                logger.info("  - 未发现可用的数字人模型")
        else:
            logger.error("无效的选择")
            return
        
        print_usage_guide()
        
    except KeyboardInterrupt:
        logger.info("\n用户取消安装")
    except Exception as e:
        logger.error(f"安装过程中发生错误: {str(e)}")

if __name__ == "__main__":
    asyncio.run(main())
