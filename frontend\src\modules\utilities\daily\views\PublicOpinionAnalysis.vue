<template>
  <div class="public-opinion-analysis">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">舆情分析平台</h1>
        <p class="page-description">基于AI的区域国别研究与舆情监测分析平台</p>
      </div>
    </div>

    <!-- 功能导航卡片 -->
    <div class="feature-cards">
      <a-row :gutter="[24, 24]">
        <a-col :xs="24" :sm="12" :lg="6">
          <a-card 
            hoverable 
            class="feature-card"
            @click="navigateToFeature('periodic-report')"
          >
            <template #cover>
              <div class="card-icon">
                <file-text-outlined />
              </div>
            </template>
            <a-card-meta 
              title="定期简报" 
              description="AI智能体自动生成舆情热点汇编，支持定时推送"
            />
          </a-card>
        </a-col>

        <a-col :xs="24" :sm="12" :lg="6">
          <a-card 
            hoverable 
            class="feature-card"
            @click="navigateToFeature('knowledge-graph')"
          >
            <template #cover>
              <div class="card-icon">
                <apartment-outlined />
              </div>
            </template>
            <a-card-meta 
              title="知识图谱" 
              description="可视化展示热点事件的复杂关联关系"
            />
          </a-card>
        </a-col>

        <a-col :xs="24" :sm="12" :lg="6">
          <a-card 
            hoverable 
            class="feature-card"
            @click="navigateToFeature('search-tracking')"
          >
            <template #cover>
              <div class="card-icon">
                <radar-chart-outlined />
              </div>
            </template>
            <a-card-meta 
              title="检索追踪预警" 
              description="保存检索方案，设置预警条件，及时识别舆情风险"
            />
          </a-card>
        </a-col>

        <a-col :xs="24" :sm="12" :lg="6">
          <a-card 
            hoverable 
            class="feature-card"
            @click="navigateToFeature('opinion-search')"
          >
            <template #cover>
              <div class="card-icon">
                <search-outlined />
              </div>
            </template>
            <a-card-meta 
              title="舆情检索" 
              description="多维度舆情数据检索与可视化分析"
            />
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 平台特色功能介绍 -->
    <div class="platform-features">
      <a-card title="平台核心功能" :bordered="false">
        <a-row :gutter="[32, 32]">
          <a-col :xs="24" :md="12">
            <div class="feature-item">
              <div class="feature-icon">
                <robot-outlined />
              </div>
              <div class="feature-content">
                <h3>AI智能简报</h3>
                <p>睡一觉的时间，AI为您生成数万字的舆情热点汇编。支持自定义模板、参考来源和生成周期。</p>
              </div>
            </div>
          </a-col>

          <a-col :xs="24" :md="12">
            <div class="feature-item">
              <div class="feature-icon">
                <mail-outlined />
              </div>
              <div class="feature-content">
                <h3>邮件推送</h3>
                <p>简报自动推送至指定邮箱，平台化身"专属送报员"，按设定周期定时推送最新简报。</p>
              </div>
            </div>
          </a-col>

          <a-col :xs="24" :md="12">
            <div class="feature-item">
              <div class="feature-icon">
                <eye-outlined />
              </div>
              <div class="feature-content">
                <h3>实时监测</h3>
                <p>支持舆情检索方案追踪预警，可设置触发条件，及时识别舆情风险。</p>
              </div>
            </div>
          </a-col>

          <a-col :xs="24" :md="12">
            <div class="feature-item">
              <div class="feature-icon">
                <bar-chart-outlined />
              </div>
              <div class="feature-content">
                <h3>多维分析</h3>
                <p>提供地域、趋势、情感、来源等六个维度的可视化分析，全面把握舆情态势。</p>
              </div>
            </div>
          </a-col>
        </a-row>
      </a-card>
    </div>

    <!-- 快速开始 -->
    <div class="quick-start">
      <a-card title="快速开始" :bordered="false">
        <a-steps :current="0" direction="horizontal">
          <a-step title="创建简报任务" description="设置简报模板和生成周期" />
          <a-step title="配置数据源" description="选择舆情站点和知识库" />
          <a-step title="设置预警" description="配置检索追踪预警条件" />
          <a-step title="获取简报" description="自动生成并推送简报" />
        </a-steps>
        
        <div class="start-actions">
          <a-button type="primary" size="large" @click="navigateToFeature('periodic-report')">
            立即开始
          </a-button>
          <a-button size="large" @click="showDemo">
            查看演示
          </a-button>
        </div>
      </a-card>
    </div>
  </div>
</template>

<script>
import { defineComponent } from 'vue';
import { useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import {
  FileTextOutlined,
  ApartmentOutlined,
  RadarChartOutlined,
  SearchOutlined,
  RobotOutlined,
  MailOutlined,
  EyeOutlined,
  BarChartOutlined
} from '@ant-design/icons-vue';

export default defineComponent({
  name: 'PublicOpinionAnalysis',
  components: {
    FileTextOutlined,
    ApartmentOutlined,
    RadarChartOutlined,
    SearchOutlined,
    RobotOutlined,
    MailOutlined,
    EyeOutlined,
    BarChartOutlined
  },
  setup() {
    const router = useRouter();

    const navigateToFeature = (feature) => {
      const routes = {
        'periodic-report': '/utilities/daily/opinion-analysis/periodic-report',
        'knowledge-graph': '/utilities/daily/opinion-analysis/knowledge-graph',
        'search-tracking': '/utilities/daily/opinion-analysis/search-tracking',
        'opinion-search': '/utilities/daily/opinion-analysis/opinion-search'
      };
      
      if (routes[feature]) {
        router.push(routes[feature]);
      } else {
        message.info('功能开发中，敬请期待！');
      }
    };

    const showDemo = () => {
      message.info('演示功能开发中，敬请期待！');
    };

    return {
      navigateToFeature,
      showDemo
    };
  }
});
</script>

<style scoped>
.public-opinion-analysis {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 48px 32px;
  margin-bottom: 32px;
  color: white;
  text-align: center;
}

.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 16px;
  color: white;
}

.page-description {
  font-size: 1.2rem;
  opacity: 0.9;
  margin: 0;
}

.feature-cards {
  margin-bottom: 32px;
}

.feature-card {
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 12px;
  overflow: hidden;
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.card-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 120px;
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
  font-size: 3rem;
}

.platform-features {
  margin-bottom: 32px;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.feature-icon {
  flex-shrink: 0;
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
}

.feature-content h3 {
  margin: 0 0 8px 0;
  color: #1f2937;
  font-weight: 600;
}

.feature-content p {
  margin: 0;
  color: #6b7280;
  line-height: 1.6;
}

.quick-start {
  margin-bottom: 32px;
}

.start-actions {
  margin-top: 32px;
  text-align: center;
  gap: 16px;
  display: flex;
  justify-content: center;
}

.start-actions .ant-btn {
  margin: 0 8px;
}

@media (max-width: 768px) {
  .public-opinion-analysis {
    padding: 16px;
  }
  
  .page-header {
    padding: 32px 24px;
  }
  
  .page-title {
    font-size: 2rem;
  }
  
  .start-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .start-actions .ant-btn {
    margin: 8px 0;
    width: 200px;
  }
}
</style>
