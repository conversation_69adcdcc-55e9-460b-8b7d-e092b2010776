#!/usr/bin/env python3
"""
下载真实人物头像脚本
从AI生成人物网站下载真实的头像图片
"""

import requests
import json
from pathlib import Path
from PIL import Image
import io
import time

# 项目根目录
PROJECT_ROOT = Path(__file__).parent.parent.parent
AVATAR_STORAGE_PATH = PROJECT_ROOT / "backend" / "storage" / "avatars"

# 头像配置 - 与前端模板匹配
AVATAR_CONFIGS = {
    "female-teacher": {
        "name": "温和女教师",
        "description": "温和亲切的女性教师形象",
        "keywords": ["professional", "teacher", "female", "friendly"]
    },
    "male-teacher": {
        "name": "资深男教授", 
        "description": "权威专业的男性教授形象",
        "keywords": ["professional", "teacher", "male", "authoritative"]
    },
    "female-business": {
        "name": "职场女性",
        "description": "现代职场女性形象",
        "keywords": ["business", "professional", "female", "modern"]
    },
    "male-business": {
        "name": "商务精英",
        "description": "专业商务男性形象", 
        "keywords": ["business", "professional", "male", "executive"]
    },
    "female-service": {
        "name": "亲和客服",
        "description": "友好亲和的客服形象",
        "keywords": ["service", "friendly", "female", "customer"]
    },
    "male-host": {
        "name": "专业主持",
        "description": "专业主持人形象",
        "keywords": ["host", "professional", "male", "presenter"]
    }
}

def download_from_thispersondoesnotexist(avatar_type, config):
    """从ThisPersonDoesNotExist下载AI生成的人物头像"""
    try:
        print(f"正在下载 {config['name']} 头像...")
        
        # 下载图片
        response = requests.get("https://thispersondoesnotexist.com/", timeout=30)
        response.raise_for_status()
        
        # 处理图片
        img = Image.open(io.BytesIO(response.content))
        
        # 调整大小为512x512
        img = img.resize((512, 512), Image.Resampling.LANCZOS)
        
        # 保存图片
        avatar_path = AVATAR_STORAGE_PATH / f"{avatar_type}.jpg"
        img.save(avatar_path, "JPEG", quality=90)
        
        print(f"✅ {config['name']} 下载完成: {avatar_path}")
        return True
        
    except Exception as e:
        print(f"❌ 下载 {config['name']} 失败: {e}")
        return False

def download_from_generated_photos(avatar_type, config):
    """从Generated Photos API下载头像（需要API key）"""
    try:
        # 这里可以集成Generated Photos API
        # 需要API key，暂时跳过
        print(f"⏭️  跳过 Generated Photos API (需要API key)")
        return False
    except Exception as e:
        print(f"❌ Generated Photos API 失败: {e}")
        return False

def create_avatar_info(avatar_type, config):
    """创建头像信息文件"""
    avatar_path = AVATAR_STORAGE_PATH / f"{avatar_type}.jpg"
    
    if not avatar_path.exists():
        return None
    
    file_size = avatar_path.stat().st_size
    
    info = {
        "type": avatar_type,
        "name": config["name"],
        "description": config["description"],
        "keywords": config["keywords"],
        "size": file_size,
        "resolution": "512x512",
        "format": "JPEG",
        "source": "AI Generated",
        "created_at": time.strftime("%Y-%m-%dT%H:%M:%SZ"),
        "is_placeholder": False
    }
    
    info_file = AVATAR_STORAGE_PATH / f"{avatar_type}_info.json"
    with open(info_file, "w", encoding="utf-8") as f:
        json.dump(info, f, indent=2, ensure_ascii=False)
    
    return info

def download_avatar(avatar_type, config):
    """下载单个头像"""
    avatar_path = AVATAR_STORAGE_PATH / f"{avatar_type}.jpg"
    
    # 如果头像已存在且不是占位图，跳过
    info_file = AVATAR_STORAGE_PATH / f"{avatar_type}_info.json"
    if avatar_path.exists() and info_file.exists():
        try:
            with open(info_file, 'r', encoding='utf-8') as f:
                info = json.load(f)
                if not info.get("is_placeholder", True):
                    print(f"⏭️  {config['name']} 已存在真实头像，跳过")
                    return True
        except:
            pass
    
    # 尝试不同的下载源
    download_methods = [
        download_from_thispersondoesnotexist,
        # download_from_generated_photos,  # 需要API key
    ]
    
    for method in download_methods:
        if method(avatar_type, config):
            # 创建信息文件
            create_avatar_info(avatar_type, config)
            return True
        
        # 等待一下再尝试下一个方法
        time.sleep(2)
    
    return False

def main():
    """主函数"""
    print("=" * 60)
    print("下载真实人物头像")
    print("=" * 60)
    
    # 创建头像存储目录
    AVATAR_STORAGE_PATH.mkdir(parents=True, exist_ok=True)
    
    success_count = 0
    total_count = len(AVATAR_CONFIGS)
    
    for avatar_type, config in AVATAR_CONFIGS.items():
        print(f"\n[{success_count + 1}/{total_count}] 处理 {avatar_type}")
        
        if download_avatar(avatar_type, config):
            success_count += 1
        
        # 避免请求过于频繁
        time.sleep(3)
    
    print("\n" + "=" * 60)
    print("下载完成统计:")
    print(f"- 总头像数: {total_count}")
    print(f"- 成功下载: {success_count}")
    print(f"- 失败数量: {total_count - success_count}")
    print(f"\n头像存储路径: {AVATAR_STORAGE_PATH}")
    print("=" * 60)
    
    # 创建通用占位图（如果不存在）
    placeholder_path = AVATAR_STORAGE_PATH / "placeholder.jpg"
    if not placeholder_path.exists():
        try:
            print("\n创建通用占位图...")
            from PIL import Image, ImageDraw, ImageFont
            
            img = Image.new('RGB', (512, 512), color='#CCCCCC')
            draw = ImageDraw.Draw(img)
            
            try:
                font = ImageFont.truetype("arial.ttf", 60)
            except:
                font = ImageFont.load_default()
            
            text = "头像"
            bbox = draw.textbbox((0, 0), text, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
            
            x = (512 - text_width) // 2
            y = (512 - text_height) // 2
            
            draw.text((x, y), text, fill='white', font=font)
            img.save(placeholder_path, "JPEG", quality=85)
            
            print(f"✅ 通用占位图创建完成: {placeholder_path}")
        except Exception as e:
            print(f"❌ 创建通用占位图失败: {e}")

if __name__ == "__main__":
    main()
