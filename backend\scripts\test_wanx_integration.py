#!/usr/bin/env python3
"""
测试 Wanx 2.1 集成
"""
import os
import sys
import uuid
import requests
import time
from pathlib import Path

# 添加后端路径
backend_dir = Path(__file__).parent.parent
sys.path.append(str(backend_dir))

def test_wanx_models():
    """测试 Wanx 2.1 模型是否正确安装"""
    print("🔍 检查 Wanx 2.1 模型...")
    
    model_path = backend_dir / "storage" / "models" / "video_generation" / "wan" / "Wan2.1"
    
    if not model_path.exists():
        print(f"❌ Wanx 2.1 模型不存在: {model_path}")
        return False
    
    # 检查关键文件
    key_files = [
        "generate.py",
        "wan",
        "models"
    ]
    
    missing_files = []
    for file_name in key_files:
        file_path = model_path / file_name
        if not file_path.exists():
            missing_files.append(file_name)
    
    if missing_files:
        print(f"⚠️ 缺少关键文件: {missing_files}")
        print("💡 请确保 Wanx 2.1 模型完整安装")
    else:
        print("✅ Wanx 2.1 模型文件检查通过")
    
    return len(missing_files) == 0

def test_celery_connection():
    """测试 Celery 连接"""
    print("🔍 检查 Celery 连接...")
    
    try:
        from app.config.celery_config import celery_app
        
        # 检查 Celery 状态
        inspect = celery_app.control.inspect()
        stats = inspect.stats()
        
        if stats:
            print("✅ Celery 连接正常")
            print(f"📊 活跃 Worker: {list(stats.keys())}")
            return True
        else:
            print("⚠️ 没有活跃的 Celery Worker")
            return False
            
    except Exception as e:
        print(f"❌ Celery 连接失败: {e}")
        return False

def test_api_endpoints():
    """测试 API 端点"""
    print("🔍 测试 API 端点...")
    
    base_url = "http://localhost:8000/api/v1/video"
    
    # 测试端点列表
    endpoints = [
        "/history/demo-user",
    ]
    
    for endpoint in endpoints:
        try:
            response = requests.get(f"{base_url}{endpoint}", timeout=5)
            if response.status_code == 200:
                print(f"✅ {endpoint} - 正常")
            else:
                print(f"⚠️ {endpoint} - 状态码: {response.status_code}")
        except requests.exceptions.RequestException as e:
            print(f"❌ {endpoint} - 连接失败: {e}")

def test_text_to_video_task():
    """测试文本转视频任务"""
    print("🔍 测试文本转视频任务...")
    
    try:
        from app.tasks.wanx_video_tasks import wanx_text_to_video
        
        # 创建测试任务
        task_id = str(uuid.uuid4())
        
        print(f"📝 创建测试任务: {task_id}")
        print("⏳ 这可能需要几分钟时间...")
        
        # 启动任务（异步）
        result = wanx_text_to_video.delay(
            task_id=task_id,
            prompt="一只可爱的猫咪在草地上奔跑",
            model="t2v-1.3B",
            duration=3,
            resolution="512x512",
            fps=24
        )
        
        print(f"🚀 任务已启动: {result.id}")
        print("💡 请检查 Celery Worker 日志查看进度")
        
        return True
        
    except Exception as e:
        print(f"❌ 任务创建失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 Wanx 2.1 集成测试")
    print("=" * 50)
    
    tests = [
        ("模型文件检查", test_wanx_models),
        ("Celery 连接测试", test_celery_connection),
        ("API 端点测试", test_api_endpoints),
        ("文本转视频任务测试", test_text_to_video_task),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔬 {test_name}")
        print("-" * 30)
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n📊 测试总结")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 通过率: {passed}/{len(results)} ({passed/len(results)*100:.1f}%)")
    
    if passed == len(results):
        print("🎉 所有测试通过！Wanx 2.1 集成成功！")
    else:
        print("⚠️ 部分测试失败，请检查配置")

if __name__ == "__main__":
    main()
