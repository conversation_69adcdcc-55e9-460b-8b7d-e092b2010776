"""
最基础的智能体服务 - 预设回复方案
"""

import random
from typing import Dict, Any


class BasicAgentService:
    """基础智能体服务 - 使用预设回复确保正常对话"""

    def __init__(self):
        # 预设的自然对话回复
        self.language_tutor_responses = {
            "greetings": [
                "Hello! Nice to meet you! I'm your English teacher. What's your name?",
                "Hi there! I'm so glad you're here to practice English with me. How are you today?",
                "Hello! Welcome to our English lesson. I'm excited to help you learn!",
                "Hi! I'm your English tutor. What would you like to practice today?"
            ],
            "name_introduction": [
                "Nice to meet you, {}! I'm your English teacher. How long have you been learning English?",
                "Hello {}! It's wonderful to meet you. What brings you to English practice today?",
                "Hi {}! That's a lovely name. What would you like to work on in our lesson?",
                "Great to meet you, {}! Are you ready for some English practice?"
            ],
            "chinese_greeting": [
                "Hello! I see you said '你好' - that means 'hello' in English! Nice to meet you!",
                "Hi there! I noticed you greeted me in Chinese. Let's practice in English together!",
                "Hello! Thanks for the Chinese greeting. Let's chat in English now - what's your name?",
                "Hi! I understand you said 'hello' in Chinese. Let's practice English conversation!"
            ],
            "general": [
                "That's interesting! Can you tell me more about that in English?",
                "Great! How do you feel about practicing English today?",
                "Wonderful! What would you like to learn or practice next?",
                "That's good to hear! Let's continue our English conversation."
            ],
            "encouragement": [
                "You're doing great! Keep practicing!",
                "Excellent! Your English is improving!",
                "Well done! I'm proud of your progress!",
                "That's perfect! You're learning so well!"
            ]
        }

    async def chat_with_agent(self, agent: Dict[str, Any], message: str) -> str:
        """与智能体对话 - 基础版本使用预设回复"""
        try:
            agent_type = agent.get("agent_type", "chat")
            agent_name = agent.get("name", "Teacher")
            
            if agent_type == "language_tutor":
                return self._get_language_tutor_response(message, agent_name)
            else:
                return f"Hello! I'm {agent_name}. How can I help you today?"

        except Exception as e:
            print(f"[ERROR] 基础智能体对话失败: {str(e)}")
            return "Hello! I'm here to help you learn English. How can I assist you?"

    def _get_language_tutor_response(self, message: str, agent_name: str) -> str:
        """获取语言教师回复"""
        message_lower = message.lower().strip()
        
        # 检测中文问候
        if any(word in message for word in ["你好", "您好", "hi", "hello"]):
            if any(word in message for word in ["你好", "您好"]):
                return random.choice(self.language_tutor_responses["chinese_greeting"])
            else:
                return random.choice(self.language_tutor_responses["greetings"])
        
        # 检测姓名介绍
        if any(phrase in message_lower for phrase in ["my name is", "i'm", "i am", "call me"]):
            # 尝试提取姓名
            name = self._extract_name(message)
            if name:
                response_template = random.choice(self.language_tutor_responses["name_introduction"])
                return response_template.format(name)
            else:
                return "Nice to meet you! What would you like to practice in English today?"
        
        # 检测问题或请求
        if any(word in message_lower for word in ["how", "what", "where", "when", "why", "can you", "help"]):
            return "That's a great question! Let me help you with that. " + random.choice(self.language_tutor_responses["general"])
        
        # 检测积极回应
        if any(word in message_lower for word in ["good", "great", "fine", "ok", "yes", "sure"]):
            return random.choice(self.language_tutor_responses["encouragement"])
        
        # 默认回复
        return random.choice(self.language_tutor_responses["general"])

    def _extract_name(self, message: str) -> str:
        """从消息中提取姓名"""
        try:
            message_lower = message.lower()
            
            # 常见的姓名介绍模式
            patterns = [
                "my name is ",
                "i'm ",
                "i am ",
                "call me "
            ]
            
            for pattern in patterns:
                if pattern in message_lower:
                    # 找到模式后的内容
                    start_index = message_lower.find(pattern) + len(pattern)
                    remaining = message[start_index:].strip()
                    
                    # 提取第一个单词作为姓名
                    name_parts = remaining.split()
                    if name_parts:
                        name = name_parts[0].strip(".,!?")
                        # 简单验证姓名（只包含字母）
                        if name.replace("-", "").replace("'", "").isalpha():
                            return name.capitalize()
            
            return ""
        except Exception:
            return ""

    def get_conversation_starter(self) -> str:
        """获取对话开场白"""
        starters = [
            "Hello! I'm your English teacher. What's your name?",
            "Hi there! Welcome to our English practice session. How are you today?",
            "Hello! I'm excited to help you practice English. What would you like to work on?",
            "Hi! Let's start our English conversation. What brings you here today?"
        ]
        return random.choice(starters)
