#!/usr/bin/env python3
"""
深度诊断 Celery 问题
"""
import os
import sys
import uuid
import time
from pathlib import Path
from dotenv import load_dotenv

# 添加后端路径
backend_dir = Path(__file__).parent.parent
sys.path.append(str(backend_dir))

# 加载环境变量
env_path = backend_dir / ".env"
load_dotenv(env_path)

def check_celery_app_config():
    """检查 Celery 应用配置"""
    print("🔍 检查 Celery 应用配置")
    print("=" * 50)
    
    try:
        from app.core.celery_app import celery_app
        
        print(f"✅ Celery 应用导入成功")
        print(f"📊 应用名称: {celery_app.main}")
        print(f"📊 Broker URL: {celery_app.conf.broker_url}")
        print(f"📊 Result Backend: {celery_app.conf.result_backend}")
        
        # 检查任务路由
        print(f"\n📋 任务路由配置:")
        if hasattr(celery_app.conf, 'task_routes'):
            for pattern, config in celery_app.conf.task_routes.items():
                print(f"   {pattern} -> {config}")
        else:
            print("   ❌ 没有任务路由配置")
        
        # 检查已注册的任务
        print(f"\n📋 已注册的任务:")
        wanx_tasks = []
        for task_name in sorted(celery_app.tasks.keys()):
            if not task_name.startswith('celery.'):
                print(f"   ✅ {task_name}")
                if 'wanx' in task_name:
                    wanx_tasks.append(task_name)
        
        print(f"\n🎬 Wanx 相关任务: {len(wanx_tasks)}")
        for task in wanx_tasks:
            print(f"   🎬 {task}")
        
        return celery_app, wanx_tasks
        
    except Exception as e:
        print(f"❌ Celery 应用检查失败: {e}")
        import traceback
        traceback.print_exc()
        return None, []

def check_worker_status():
    """检查 Worker 状态"""
    print(f"\n🔍 检查 Worker 状态")
    print("=" * 50)
    
    try:
        from app.core.celery_app import celery_app
        
        # 检查活跃的 Worker
        inspect = celery_app.control.inspect()
        
        # 获取活跃的 Worker
        active_workers = inspect.active()
        if active_workers:
            print(f"✅ 找到 {len(active_workers)} 个活跃的 Worker:")
            for worker_name, tasks in active_workers.items():
                print(f"   🔧 {worker_name}: {len(tasks)} 个活跃任务")
        else:
            print(f"❌ 没有找到活跃的 Worker")
        
        # 检查注册的任务
        registered = inspect.registered()
        if registered:
            print(f"\n📋 Worker 注册的任务:")
            for worker_name, tasks in registered.items():
                print(f"   🔧 {worker_name}: {len(tasks)} 个注册任务")
                wanx_tasks = [t for t in tasks if 'wanx' in t]
                if wanx_tasks:
                    print(f"      🎬 Wanx 任务: {wanx_tasks}")
        else:
            print(f"❌ 无法获取 Worker 注册信息")
        
        # 检查队列统计
        stats = inspect.stats()
        if stats:
            print(f"\n📊 Worker 统计:")
            for worker_name, stat in stats.items():
                print(f"   🔧 {worker_name}:")
                print(f"      进程数: {stat.get('pool', {}).get('max-concurrency', 'N/A')}")
                print(f"      总任务数: {stat.get('total', 'N/A')}")
        
        return active_workers is not None
        
    except Exception as e:
        print(f"❌ Worker 状态检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_direct_task_call():
    """直接测试任务调用"""
    print(f"\n🧪 直接测试任务调用")
    print("=" * 50)
    
    try:
        from app.tasks.wanx_video_tasks import wanx_text_to_video
        from app.core.task_manager import task_manager
        from app.models.unified_tasks import TaskTypes
        
        # 创建任务记录
        task_id = task_manager.create_task(
            task_type=TaskTypes.VIDEO_GENERATION,
            task_subtype=TaskTypes.VideoGeneration.TEXT_TO_VIDEO,
            user_id="test-user",
            title="Celery 诊断测试",
            description="测试 Celery 任务调用",
            input_params={
                "prompt": "Celery 诊断测试",
                "model": "t2v-1.3B"
            }
        )
        
        print(f"📝 任务ID: {task_id}")
        
        # 直接调用任务（不通过 delay）
        print(f"🔧 尝试直接调用任务...")
        try:
            # 这会直接执行任务，不通过 Celery
            result = wanx_text_to_video(
                task_id=task_id,
                prompt="Celery 诊断测试",
                model="t2v-1.3B",
                duration=3,
                resolution="512x512",
                fps=24,
                guidance_scale=7.5,
                num_inference_steps=20,
                user_id="test-user"
            )
            print(f"✅ 直接调用成功: {result}")
            return True
        except Exception as direct_error:
            print(f"❌ 直接调用失败: {direct_error}")
            import traceback
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"❌ 任务调用测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_task_delay():
    """测试任务 delay 调用"""
    print(f"\n🚀 测试任务 delay 调用")
    print("=" * 50)
    
    try:
        from app.tasks.wanx_video_tasks import wanx_text_to_video
        from app.core.task_manager import task_manager
        from app.models.unified_tasks import TaskTypes
        
        # 创建任务记录
        task_id = task_manager.create_task(
            task_type=TaskTypes.VIDEO_GENERATION,
            task_subtype=TaskTypes.VideoGeneration.TEXT_TO_VIDEO,
            user_id="test-user",
            title="Celery delay 测试",
            description="测试 Celery delay 调用",
            input_params={
                "prompt": "Celery delay 测试",
                "model": "t2v-1.3B"
            }
        )
        
        print(f"📝 任务ID: {task_id}")
        
        # 通过 delay 调用任务
        print(f"🚀 尝试 delay 调用任务...")
        result = wanx_text_to_video.delay(
            task_id=task_id,
            prompt="Celery delay 测试",
            model="t2v-1.3B",
            duration=3,
            resolution="512x512",
            fps=24,
            guidance_scale=7.5,
            num_inference_steps=20,
            user_id="test-user"
        )
        
        print(f"✅ delay 调用成功")
        print(f"📊 Celery 任务ID: {result.id}")
        print(f"📊 任务状态: {result.status}")
        
        # 等待一下看看状态变化
        print(f"⏳ 等待 5 秒查看状态变化...")
        time.sleep(5)
        
        print(f"📊 5秒后任务状态: {result.status}")
        
        # 检查数据库中的任务状态
        task_info = task_manager.get_task(task_id)
        if task_info:
            print(f"📊 数据库中任务状态: {task_info['status']} ({task_info['progress']}%)")
        
        return result.id, task_id
        
    except Exception as e:
        print(f"❌ delay 调用测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None

def check_redis_detailed():
    """详细检查 Redis"""
    print(f"\n🔍 详细检查 Redis")
    print("=" * 50)
    
    try:
        import redis
        
        r = redis.Redis(host='localhost', port=6379, db=0)
        r.ping()
        print("✅ Redis 连接正常")
        
        # 检查所有队列
        all_queues = ['wanx_generation', 'celery', 'digital_human', 'image_processing', 
                     'voice_processing', 'video_generation', 'translation']
        
        print(f"\n📊 所有队列状态:")
        for queue in all_queues:
            length = r.llen(queue)
            print(f"   {queue}: {length} 个任务")
            
            if length > 0:
                # 查看队列中的任务
                tasks = r.lrange(queue, 0, 2)
                for i, task in enumerate(tasks):
                    task_str = task.decode()[:100]
                    print(f"     任务 {i+1}: {task_str}...")
        
        # 检查 Celery 相关的键
        celery_keys = [k.decode() for k in r.keys('*') if b'celery' in k]
        print(f"\n📊 Celery 相关键数量: {len(celery_keys)}")
        
        # 检查最近的任务元数据
        task_meta_keys = [k for k in celery_keys if 'task-meta' in k]
        print(f"📊 任务元数据键数量: {len(task_meta_keys)}")
        
        if task_meta_keys:
            # 查看最近的几个任务元数据
            recent_keys = task_meta_keys[-3:]
            print(f"📊 最近的任务元数据:")
            for key in recent_keys:
                try:
                    meta = r.get(key)
                    if meta:
                        import json
                        meta_data = json.loads(meta.decode())
                        status = meta_data.get('status', 'N/A')
                        task_name = meta_data.get('task', 'N/A')
                        print(f"   {key}: {task_name} - {status}")
                except:
                    print(f"   {key}: 无法解析")
        
        return True
        
    except Exception as e:
        print(f"❌ Redis 详细检查失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 Celery 深度诊断")
    print("=" * 60)
    
    # 1. 检查 Celery 应用配置
    celery_app, wanx_tasks = check_celery_app_config()
    if not celery_app:
        return
    
    # 2. 检查 Worker 状态
    worker_ok = check_worker_status()
    
    # 3. 详细检查 Redis
    redis_ok = check_redis_detailed()
    
    # 4. 测试直接任务调用
    direct_ok = test_direct_task_call()
    
    # 5. 测试 delay 调用
    celery_task_id, task_id = test_task_delay()
    
    # 总结
    print(f"\n📊 诊断总结:")
    print(f"✅ Celery 应用: 正常")
    print(f"{'✅' if worker_ok else '❌'} Worker 状态: {'正常' if worker_ok else '异常'}")
    print(f"{'✅' if redis_ok else '❌'} Redis 连接: {'正常' if redis_ok else '异常'}")
    print(f"{'✅' if direct_ok else '❌'} 直接调用: {'成功' if direct_ok else '失败'}")
    print(f"{'✅' if celery_task_id else '❌'} Delay 调用: {'成功' if celery_task_id else '失败'}")
    
    if not worker_ok:
        print(f"\n💡 Worker 问题可能的原因:")
        print(f"   1. Worker 进程没有正确启动")
        print(f"   2. Worker 没有连接到正确的 Broker")
        print(f"   3. Worker 没有加载任务模块")
        print(f"   4. 任务路由配置有问题")
    
    if direct_ok and not worker_ok:
        print(f"\n🔧 建议的解决方案:")
        print(f"   1. 重启 Celery Worker")
        print(f"   2. 检查 Worker 启动命令")
        print(f"   3. 确保 Worker 加载了正确的任务模块")
        print(f"   4. 检查环境变量配置")

if __name__ == "__main__":
    main()
