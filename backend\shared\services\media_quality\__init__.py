# Media Quality Service
from .quality_analyzer import MediaQualityAnalyzer

# 单例模式质量分析器实例
_quality_analyzer_instance = None

def get_quality_analyzer() -> MediaQualityAnalyzer:
    """
    获取媒体质量分析器实例（单例模式）
    
    Returns:
        MediaQualityAnalyzer: 媒体质量分析器实例
    """
    global _quality_analyzer_instance
    
    if _quality_analyzer_instance is None:
        _quality_analyzer_instance = MediaQualityAnalyzer()
        
    return _quality_analyzer_instance

__all__ = ['MediaQualityAnalyzer', 'get_quality_analyzer'] 