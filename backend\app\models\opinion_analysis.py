"""
舆情分析相关数据模型
"""

from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, JSON, Float, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.core.sqlalchemy_db import Base


class OpinionReport(Base):
    """定期简报模型"""
    __tablename__ = "opinion_reports"
    __table_args__ = {'extend_existing': True}

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(200), nullable=False, comment="简报名称")
    description = Column(Text, comment="简报描述")
    
    # 生成配置
    template_type = Column(String(50), default="standard", comment="模板类型: standard, detailed, brief")
    cycle = Column(String(20), nullable=False, comment="生成周期: daily, weekly, monthly")
    sources = Column(JSON, comment="数据源配置")
    keywords = Column(JSON, comment="关键词列表")
    regions = Column(JSON, comment="地域范围")
    language = Column(String(10), default="zh", comment="语言")
    
    # 执行配置
    execution_time = Column(String(10), comment="执行时间 HH:MM")
    next_execution = Column(DateTime, comment="下次执行时间")
    last_execution = Column(DateTime, comment="最后执行时间")
    
    # 邮件配置
    recipients = Column(JSON, comment="邮件接收人列表")
    email_enabled = Column(Boolean, default=True, comment="是否启用邮件推送")
    
    # 状态信息
    status = Column(String(20), default="active", comment="状态: active, paused, stopped")
    execution_count = Column(Integer, default=0, comment="执行次数")
    
    # 关联信息
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 关系
    user = relationship("User", back_populates="opinion_reports")
    report_instances = relationship("OpinionReportInstance", back_populates="report", cascade="all, delete-orphan")


class OpinionReportInstance(Base):
    """简报实例模型 - 每次生成的具体简报"""
    __tablename__ = "opinion_report_instances"
    __table_args__ = {'extend_existing': True}

    id = Column(Integer, primary_key=True, index=True)
    report_id = Column(Integer, ForeignKey("opinion_reports.id"), nullable=False)
    
    # 简报内容
    title = Column(String(500), nullable=False, comment="简报标题")
    content = Column(Text, comment="简报内容")
    summary = Column(Text, comment="简报摘要")
    
    # 统计信息
    total_items = Column(Integer, default=0, comment="包含条目数")
    data_sources_count = Column(Integer, default=0, comment="数据源数量")
    time_range_start = Column(DateTime, comment="数据时间范围开始")
    time_range_end = Column(DateTime, comment="数据时间范围结束")
    
    # 文件信息
    pdf_path = Column(String(500), comment="PDF文件路径")
    word_path = Column(String(500), comment="Word文件路径")
    markdown_path = Column(String(500), comment="Markdown文件路径")
    
    # 生成信息
    generation_status = Column(String(20), default="pending", comment="生成状态: pending, processing, completed, failed")
    generation_progress = Column(Float, default=0.0, comment="生成进度 0-100")
    error_message = Column(Text, comment="错误信息")
    
    # 邮件发送状态
    email_sent = Column(Boolean, default=False, comment="是否已发送邮件")
    email_sent_at = Column(DateTime, comment="邮件发送时间")
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 关系
    report = relationship("OpinionReport", back_populates="report_instances")


class OpinionSearchScheme(Base):
    """检索方案模型"""
    __tablename__ = "opinion_search_schemes"
    __table_args__ = {'extend_existing': True}

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(200), nullable=False, comment="方案名称")
    description = Column(Text, comment="方案描述")
    
    # 检索配置
    keywords = Column(JSON, nullable=False, comment="关键词列表")
    sources = Column(JSON, comment="数据源配置")
    regions = Column(JSON, comment="地域范围")
    language = Column(String(10), default="all", comment="语言")
    date_range_type = Column(String(20), default="relative", comment="时间范围类型: relative, absolute")
    date_range_value = Column(JSON, comment="时间范围配置")
    
    # 高级配置
    exclude_keywords = Column(JSON, comment="排除关键词")
    sentiment_filter = Column(JSON, comment="情感过滤")
    source_weight = Column(JSON, comment="数据源权重")
    
    # 执行统计
    last_execution = Column(DateTime, comment="最后执行时间")
    execution_count = Column(Integer, default=0, comment="执行次数")
    last_result_count = Column(Integer, default=0, comment="最后结果数量")
    
    # 状态信息
    status = Column(String(20), default="active", comment="状态: active, paused, stopped")
    
    # 关联信息
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 关系
    user = relationship("User", back_populates="opinion_search_schemes")
    alerts = relationship("OpinionAlert", back_populates="search_scheme", cascade="all, delete-orphan")
    search_results = relationship("OpinionSearchResult", back_populates="search_scheme", cascade="all, delete-orphan")


class OpinionAlert(Base):
    """预警配置模型"""
    __tablename__ = "opinion_alerts"
    __table_args__ = {'extend_existing': True}

    id = Column(Integer, primary_key=True, index=True)
    search_scheme_id = Column(Integer, ForeignKey("opinion_search_schemes.id"), nullable=False)
    
    # 预警配置
    enabled = Column(Boolean, default=False, comment="是否启用")
    trigger_type = Column(String(20), nullable=False, comment="触发类型: time, threshold, change")
    
    # 时间触发配置
    frequency = Column(String(20), comment="检查频率: hourly, daily, weekly")
    check_time = Column(String(10), comment="检查时间 HH:MM")
    
    # 阈值触发配置
    threshold_value = Column(Integer, comment="阈值数量")
    threshold_operator = Column(String(10), comment="阈值操作符: >, <, >=, <=, =")
    
    # 变化触发配置
    change_rate = Column(Float, comment="变化比例 0-100")
    change_period = Column(String(20), comment="比较周期: hour, day, week")
    
    # 预警级别和通知
    alert_level = Column(String(20), default="medium", comment="预警级别: low, medium, high, critical")
    recipients = Column(JSON, comment="接收人列表")
    notification_methods = Column(JSON, comment="通知方式: email, sms, webhook")
    
    # 执行状态
    last_check = Column(DateTime, comment="最后检查时间")
    next_check = Column(DateTime, comment="下次检查时间")
    alert_count = Column(Integer, default=0, comment="预警次数")
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 关系
    search_scheme = relationship("OpinionSearchScheme", back_populates="alerts")
    alert_records = relationship("OpinionAlertRecord", back_populates="alert", cascade="all, delete-orphan")


class OpinionAlertRecord(Base):
    """预警记录模型"""
    __tablename__ = "opinion_alert_records"
    __table_args__ = {'extend_existing': True}

    id = Column(Integer, primary_key=True, index=True)
    alert_id = Column(Integer, ForeignKey("opinion_alerts.id"), nullable=False)
    
    # 预警信息
    title = Column(String(500), nullable=False, comment="预警标题")
    description = Column(Text, comment="预警描述")
    alert_level = Column(String(20), nullable=False, comment="预警级别")
    
    # 触发数据
    trigger_value = Column(Float, comment="触发值")
    previous_value = Column(Float, comment="之前的值")
    change_rate = Column(Float, comment="变化率")
    
    # 相关数据
    related_data = Column(JSON, comment="相关数据")
    data_snapshot = Column(JSON, comment="数据快照")
    
    # 处理状态
    status = Column(String(20), default="new", comment="状态: new, acknowledged, resolved, ignored")
    acknowledged_by = Column(Integer, ForeignKey("users.id"), comment="确认人")
    acknowledged_at = Column(DateTime, comment="确认时间")
    resolved_at = Column(DateTime, comment="解决时间")
    
    # 通知状态
    notification_sent = Column(Boolean, default=False, comment="是否已发送通知")
    notification_methods_used = Column(JSON, comment="使用的通知方式")
    
    triggered_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 关系
    alert = relationship("OpinionAlert", back_populates="alert_records")
    acknowledged_user = relationship("User", foreign_keys=[acknowledged_by])


class OpinionSearchResult(Base):
    """检索结果模型"""
    __tablename__ = "opinion_search_results"
    __table_args__ = {'extend_existing': True}

    id = Column(Integer, primary_key=True, index=True)
    search_scheme_id = Column(Integer, ForeignKey("opinion_search_schemes.id"), nullable=False)
    
    # 结果基本信息
    title = Column(String(1000), nullable=False, comment="标题")
    title_cn = Column(String(1000), comment="中文标题")
    url = Column(String(2000), comment="原文链接")
    content = Column(Text, comment="内容")
    summary = Column(Text, comment="摘要")
    
    # 来源信息
    source = Column(String(200), comment="来源")
    source_type = Column(String(50), comment="来源类型")
    author = Column(String(200), comment="作者")
    publish_time = Column(DateTime, comment="发布时间")
    
    # 地理和语言信息
    region = Column(String(100), comment="地区")
    country = Column(String(100), comment="国家")
    language = Column(String(10), comment="语言")
    
    # 分析结果
    sentiment_score = Column(Float, comment="情感分数 -1到1")
    sentiment_label = Column(String(20), comment="情感标签: positive, neutral, negative")
    relevance_score = Column(Float, comment="相关度分数 0-1")
    importance_score = Column(Float, comment="重要性分数 0-1")
    
    # 统计信息
    view_count = Column(Integer, default=0, comment="浏览量")
    comment_count = Column(Integer, default=0, comment="评论数")
    share_count = Column(Integer, default=0, comment="分享数")
    like_count = Column(Integer, default=0, comment="点赞数")
    
    # 提取的实体和关键词
    entities = Column(JSON, comment="提取的实体")
    keywords = Column(JSON, comment="关键词")
    topics = Column(JSON, comment="主题标签")
    
    # 技术信息
    crawl_time = Column(DateTime(timezone=True), server_default=func.now(), comment="抓取时间")
    last_updated = Column(DateTime, comment="最后更新时间")
    hash_value = Column(String(64), comment="内容哈希值，用于去重")
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # 关系
    search_scheme = relationship("OpinionSearchScheme", back_populates="search_results")


class OpinionKnowledgeGraph(Base):
    """知识图谱模型"""
    __tablename__ = "opinion_knowledge_graphs"
    __table_args__ = {'extend_existing': True}

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(200), nullable=False, comment="图谱名称")
    description = Column(Text, comment="图谱描述")
    
    # 图谱数据
    nodes = Column(JSON, nullable=False, comment="节点数据")
    edges = Column(JSON, nullable=False, comment="边数据")
    layout_config = Column(JSON, comment="布局配置")
    
    # 生成配置
    data_source = Column(String(100), comment="数据源")
    time_range_start = Column(DateTime, comment="时间范围开始")
    time_range_end = Column(DateTime, comment="时间范围结束")
    keywords = Column(JSON, comment="关键词")
    
    # 统计信息
    node_count = Column(Integer, default=0, comment="节点数量")
    edge_count = Column(Integer, default=0, comment="边数量")
    max_degree = Column(Integer, default=0, comment="最大度数")
    
    # 状态信息
    status = Column(String(20), default="active", comment="状态")
    generation_time = Column(Float, comment="生成耗时(秒)")
    
    # 关联信息
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 关系
    user = relationship("User", back_populates="opinion_knowledge_graphs")
