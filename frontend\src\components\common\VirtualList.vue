<template>
  <div class="virtual-list" ref="containerRef" @scroll="handleScroll">
    <div class="virtual-list-phantom" :style="{ height: totalHeight + 'px' }"></div>
    <div class="virtual-list-content" :style="{ transform: `translateY(${offsetY}px)` }">
      <div
        v-for="item in visibleItems"
        :key="getItemKey(item)"
        class="virtual-list-item"
        :style="{ height: itemHeight + 'px' }"
      >
        <slot :item="item" :index="item.index">
          {{ item }}
        </slot>
      </div>
    </div>
    
    <!-- 加载更多指示器 -->
    <div v-if="loading" class="virtual-list-loading">
      <el-icon class="is-loading">
        <Loading />
      </el-icon>
      <span>加载中...</span>
    </div>
    
    <!-- 无更多数据指示器 -->
    <div v-if="!hasMore && items.length > 0" class="virtual-list-no-more">
      没有更多数据了
    </div>
  </div>
</template>

<script>
import { defineComponent, ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue';
import { Loading } from '@element-plus/icons-vue';

export default defineComponent({
  name: 'VirtualList',
  components: {
    Loading
  },
  props: {
    // 数据列表
    items: {
      type: Array,
      default: () => []
    },
    // 每项高度
    itemHeight: {
      type: Number,
      default: 60
    },
    // 容器高度
    height: {
      type: Number,
      default: 400
    },
    // 缓冲区大小（额外渲染的项目数）
    buffer: {
      type: Number,
      default: 5
    },
    // 获取项目唯一键的函数
    itemKey: {
      type: [String, Function],
      default: 'id'
    },
    // 是否正在加载
    loading: {
      type: Boolean,
      default: false
    },
    // 是否还有更多数据
    hasMore: {
      type: Boolean,
      default: true
    },
    // 触发加载更多的阈值（距离底部的像素）
    loadMoreThreshold: {
      type: Number,
      default: 100
    }
  },
  emits: ['load-more', 'scroll'],
  setup(props, { emit }) {
    const containerRef = ref();
    const scrollTop = ref(0);
    const containerHeight = ref(props.height);

    // 计算总高度
    const totalHeight = computed(() => {
      return props.items.length * props.itemHeight;
    });

    // 计算可见区域的开始和结束索引
    const visibleRange = computed(() => {
      const start = Math.floor(scrollTop.value / props.itemHeight);
      const visibleCount = Math.ceil(containerHeight.value / props.itemHeight);
      const end = start + visibleCount;

      // 添加缓冲区
      const bufferedStart = Math.max(0, start - props.buffer);
      const bufferedEnd = Math.min(props.items.length, end + props.buffer);

      return {
        start: bufferedStart,
        end: bufferedEnd
      };
    });

    // 计算可见项目
    const visibleItems = computed(() => {
      const { start, end } = visibleRange.value;
      return props.items.slice(start, end).map((item, index) => ({
        ...item,
        index: start + index
      }));
    });

    // 计算偏移量
    const offsetY = computed(() => {
      return visibleRange.value.start * props.itemHeight;
    });

    // 获取项目键值
    const getItemKey = (item) => {
      if (typeof props.itemKey === 'function') {
        return props.itemKey(item);
      }
      return item[props.itemKey] || item.index;
    };

    // 处理滚动事件
    const handleScroll = (event) => {
      const target = event.target;
      scrollTop.value = target.scrollTop;
      
      emit('scroll', {
        scrollTop: scrollTop.value,
        scrollHeight: target.scrollHeight,
        clientHeight: target.clientHeight
      });

      // 检查是否需要加载更多
      if (props.hasMore && !props.loading) {
        const distanceToBottom = target.scrollHeight - target.scrollTop - target.clientHeight;
        if (distanceToBottom <= props.loadMoreThreshold) {
          emit('load-more');
        }
      }
    };

    // 滚动到指定位置
    const scrollTo = (index) => {
      if (containerRef.value) {
        const targetScrollTop = index * props.itemHeight;
        containerRef.value.scrollTop = targetScrollTop;
      }
    };

    // 滚动到顶部
    const scrollToTop = () => {
      scrollTo(0);
    };

    // 滚动到底部
    const scrollToBottom = () => {
      if (containerRef.value) {
        containerRef.value.scrollTop = containerRef.value.scrollHeight;
      }
    };

    // 更新容器高度
    const updateContainerHeight = () => {
      if (containerRef.value) {
        containerHeight.value = containerRef.value.clientHeight;
      }
    };

    // 监听容器大小变化
    let resizeObserver = null;

    onMounted(() => {
      updateContainerHeight();
      
      // 使用ResizeObserver监听容器大小变化
      if (window.ResizeObserver) {
        resizeObserver = new ResizeObserver(() => {
          updateContainerHeight();
        });
        resizeObserver.observe(containerRef.value);
      }
    });

    onUnmounted(() => {
      if (resizeObserver) {
        resizeObserver.disconnect();
      }
    });

    // 监听数据变化，重置滚动位置
    watch(() => props.items.length, (newLength, oldLength) => {
      // 如果是新数据（长度从0变为有值），滚动到顶部
      if (oldLength === 0 && newLength > 0) {
        nextTick(() => {
          scrollToTop();
        });
      }
    });

    return {
      containerRef,
      totalHeight,
      visibleItems,
      offsetY,
      getItemKey,
      handleScroll,
      scrollTo,
      scrollToTop,
      scrollToBottom
    };
  }
});
</script>

<style scoped>
.virtual-list {
  position: relative;
  overflow-y: auto;
  height: v-bind(height + 'px');
}

.virtual-list-phantom {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: -1;
}

.virtual-list-content {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
}

.virtual-list-item {
  box-sizing: border-box;
}

.virtual-list-loading,
.virtual-list-no-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px;
  color: #909399;
  font-size: 14px;
}

.virtual-list-loading .el-icon {
  margin-right: 8px;
}

/* 滚动条样式 */
.virtual-list::-webkit-scrollbar {
  width: 6px;
}

.virtual-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.virtual-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.virtual-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
