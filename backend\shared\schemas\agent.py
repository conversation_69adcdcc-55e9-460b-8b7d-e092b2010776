from pydantic import BaseModel, Field, ConfigDict
from typing import List, Optional, Any, Dict
from datetime import datetime

# 基础模型
class AgentBase(BaseModel):
    name: str
    description: Optional[str] = None
    category: Optional[str] = None
    avatar: Optional[str] = None
    cover_image: Optional[str] = None
    pricing_type: Optional[str] = "free"
    price: Optional[float] = 0.0
    agent_type: Optional[str] = "general"  # 新增: general, language_learning

# 语言学习设置模型
class LanguageLearningSettings(BaseModel):
    language: str  # 学习的语言，如 "english", "japanese"
    default_accent: Optional[str] = None  # 默认口音，如 "american", "british"
    default_level: Optional[str] = "intermediate"  # 默认难度，如 "beginner", "intermediate", "advanced"
    focus_areas: Optional[List[str]] = []  # 重点领域，如 "business", "travel"
    custom_instructions: Optional[str] = None  # 自定义教学指示

# 创建Agent请求模型
class AgentCreate(AgentBase):
    tags: Optional[List[str]] = []
    capabilities: Optional[List[str]] = []
    examples: Optional[List[str]] = []
    # 新增语言学习相关字段
    learning_settings: Optional[LanguageLearningSettings] = None
    corpus_id: Optional[int] = None  # 关联的语料库ID

# 更新Agent请求模型
class AgentUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    category: Optional[str] = None
    avatar: Optional[str] = None
    cover_image: Optional[str] = None
    pricing_type: Optional[str] = None
    price: Optional[float] = None
    tags: Optional[List[str]] = None
    capabilities: Optional[List[str]] = None
    examples: Optional[List[str]] = None
    is_featured: Optional[bool] = None
    # 新增语言学习相关字段
    agent_type: Optional[str] = None
    learning_settings: Optional[LanguageLearningSettings] = None
    corpus_id: Optional[int] = None

# Agent标签模型
class AgentTagBase(BaseModel):
    tag: str

class AgentTagCreate(AgentTagBase):
    pass

class AgentTagResponse(AgentTagBase):
    id: str
    agent_id: str

    model_config = ConfigDict(from_attributes=True)

# Agent能力模型
class AgentCapabilityBase(BaseModel):
    capability: str

class AgentCapabilityCreate(AgentCapabilityBase):
    pass

class AgentCapabilityResponse(AgentCapabilityBase):
    id: str
    agent_id: str

    model_config = ConfigDict(from_attributes=True)

# Agent示例模型
class AgentExampleBase(BaseModel):
    example: str

class AgentExampleCreate(AgentExampleBase):
    pass

class AgentExampleResponse(AgentExampleBase):
    id: str
    agent_id: str

    model_config = ConfigDict(from_attributes=True)

# 用户简略信息
class UserBrief(BaseModel):
    id: str
    username: str
    avatar: Optional[str] = None

    model_config = ConfigDict(from_attributes=True)

# Agent评价模型
class AgentReviewBase(BaseModel):
    rating: float
    comment: Optional[str] = None

class AgentReviewCreate(AgentReviewBase):
    pass

class AgentReviewResponse(AgentReviewBase):
    id: str
    agent_id: str
    user_id: str
    created_at: datetime
    updated_at: datetime
    user: Optional[UserBrief] = None

    model_config = ConfigDict(from_attributes=True)

# Agent收藏模型
class AgentFavoriteCreate(BaseModel):
    agent_id: str
    category: Optional[str] = None

class AgentFavoriteResponse(BaseModel):
    id: str
    user_id: str
    agent_id: str
    created_at: datetime
    category: Optional[str] = None
    is_favorite: bool = True

    model_config = ConfigDict(from_attributes=True)

# 批量收藏请求和响应模型
class AgentBatchFavoriteCreate(BaseModel):
    agent_ids: List[str]
    category: Optional[str] = None

class AgentFavoriteResult(BaseModel):
    agent_id: str
    success: bool
    is_favorite: bool

class AgentBatchFavoriteResponse(BaseModel):
    results: List[AgentFavoriteResult]

# Agent详情响应模型
class AgentResponse(AgentBase):
    id: str
    author_id: Optional[str] = None
    rating: float
    review_count: int
    usage_count: int
    is_featured: bool
    created_at: datetime
    updated_at: datetime
    tags: List[str] = []
    capabilities: List[str] = []
    examples: List[str] = []
    author: Optional[UserBrief] = None
    is_favorite: Optional[bool] = False
    # 新增语言学习相关字段
    agent_type: str = "general"
    learning_settings: Optional[Dict[str, Any]] = None
    corpus_id: Optional[int] = None

    model_config = ConfigDict(from_attributes=True)

# Agent列表项响应模型（简化版本）
class AgentListItem(BaseModel):
    id: str
    name: str
    description: Optional[str] = None
    category: Optional[str] = None
    avatar: Optional[str] = None
    rating: float
    review_count: int
    usage_count: int
    is_featured: bool
    tags: List[str] = []
    author: Optional[UserBrief] = None
    pricing_type: str
    price: float
    is_favorite: Optional[bool] = False
    # 新增语言学习相关字段
    agent_type: str = "general"

    model_config = ConfigDict(from_attributes=True)

# 带分页的Agent列表响应
class AgentListResponse(BaseModel):
    items: List[AgentListItem]
    total: int
    page: int
    size: int
    pages: int

# Agent聊天消息模型
class AgentMessageBase(BaseModel):
    content: str

class AgentMessageCreate(AgentMessageBase):
    pass

class AgentMessageResponse(AgentMessageBase):
    id: str
    session_id: str
    sender_type: str
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)

# Agent会话模型
class AgentSessionCreate(BaseModel):
    agent_id: str

class AgentSessionResponse(BaseModel):
    id: str
    agent_id: str
    user_id: str
    created_at: datetime
    updated_at: datetime
    messages: List[AgentMessageResponse] = []

    model_config = ConfigDict(from_attributes=True)

# 分类列表响应
class CategoriesResponse(BaseModel):
    categories: List[str]

# 标签列表响应
class TagsResponse(BaseModel):
    tags: List[str] 