import { ref, computed, onUnmounted } from 'vue';

/**
 * 倒计时钩子
 * @param {Object} options - 配置选项
 * @param {number} options.initialTime - 初始时间(秒)
 * @param {boolean} options.autoStart - 是否自动开始
 * @param {Function} options.onEnd - 倒计时结束回调
 * @param {Function} options.onChange - 时间变化回调
 * @param {Function} options.format - 自定义时间格式化函数
 * @returns {Object} 倒计时控制对象
 */
export default function useCountdown(options = {}) {
  const {
    initialTime = 60,
    autoStart = false,
    onEnd,
    onChange,
    format
  } = options;
  
  // 倒计时剩余时间(秒)
  const remainingTime = ref(initialTime);
  // 是否正在运行
  const isRunning = ref(false);
  // 是否暂停
  const isPaused = ref(false);
  // 定时器
  let timer = null;
  // 开始时间戳
  let startTime = 0;
  // 暂停时剩余时间
  let pausedTimeRemaining = 0;
  
  // 进度百分比
  const progress = computed(() => {
    if (initialTime <= 0) return 0;
    return Math.max(0, Math.min(100, (remainingTime.value / initialTime) * 100));
  });
  
  // 格式化时间
  const formattedTime = computed(() => {
    if (typeof format === 'function') {
      return format(remainingTime.value);
    }
    
    // 默认格式化为分:秒
    const minutes = Math.floor(remainingTime.value / 60);
    const seconds = Math.floor(remainingTime.value % 60);
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  });
  
  // 清除定时器
  const clearTimer = () => {
    if (timer) {
      clearInterval(timer);
      timer = null;
    }
  };
  
  // 结束倒计时
  const endCountdown = () => {
    clearTimer();
    remainingTime.value = 0;
    isRunning.value = false;
    isPaused.value = false;
    
    // 调用结束回调
    if (typeof onEnd === 'function') {
      onEnd();
    }
  };
  
  // 更新倒计时
  const updateCountdown = () => {
    // 计算已经过去的时间
    const elapsedTime = Math.floor((Date.now() - startTime) / 1000);
    const newRemainingTime = Math.max(0, initialTime - elapsedTime);
    
    // 如果时间有变化
    if (remainingTime.value !== newRemainingTime) {
      remainingTime.value = newRemainingTime;
      
      // 调用变化回调
      if (typeof onChange === 'function') {
        onChange(remainingTime.value);
      }
      
      // 如果倒计时结束
      if (remainingTime.value <= 0) {
        endCountdown();
      }
    }
  };
  
  // 开始倒计时
  const start = () => {
    // 已经在运行中
    if (isRunning.value && !isPaused.value) return;
    
    // 如果已经结束，则重置
    if (remainingTime.value <= 0) {
      remainingTime.value = initialTime;
    }
    
    // 处理暂停后继续的情况
    if (isPaused.value) {
      isPaused.value = false;
      startTime = Date.now() - ((initialTime - pausedTimeRemaining) * 1000);
    } else {
      startTime = Date.now();
    }
    
    isRunning.value = true;
    
    // 设置定时器
    clearTimer();
    updateCountdown();
    timer = setInterval(updateCountdown, 1000);
  };
  
  // 暂停倒计时
  const pause = () => {
    if (!isRunning.value || isPaused.value) return;
    
    clearTimer();
    isPaused.value = true;
    pausedTimeRemaining = remainingTime.value;
  };
  
  // 继续倒计时
  const resume = () => {
    if (!isPaused.value) return;
    start();
  };
  
  // 重置倒计时
  const reset = () => {
    clearTimer();
    remainingTime.value = initialTime;
    isRunning.value = false;
    isPaused.value = false;
    
    // 如果设置了自动开始，则重新开始
    if (autoStart) {
      start();
    }
  };
  
  // 设置新的倒计时时间
  const setTime = (seconds) => {
    const newTime = parseInt(seconds, 10);
    if (isNaN(newTime) || newTime < 0) return;
    
    const wasRunning = isRunning.value && !isPaused.value;
    
    // 停止当前倒计时
    clearTimer();
    
    // 更新时间
    initialTime = newTime;
    remainingTime.value = newTime;
    
    // 如果之前正在运行，则重新开始
    if (wasRunning) {
      start();
    }
  };
  
  // 添加秒数
  const addTime = (seconds) => {
    const newTime = remainingTime.value + parseInt(seconds, 10);
    if (isNaN(newTime)) return;
    
    const wasRunning = isRunning.value && !isPaused.value;
    
    // 如果正在运行，则调整开始时间
    if (wasRunning) {
      startTime -= seconds * 1000;
    } else {
      remainingTime.value = Math.max(0, newTime);
    }
  };
  
  // 组件卸载时清除定时器
  onUnmounted(() => {
    clearTimer();
  });
  
  // 自动开始
  if (autoStart) {
    start();
  }
  
  return {
    remainingTime,
    formattedTime,
    progress,
    isRunning,
    isPaused,
    start,
    pause,
    resume,
    reset,
    setTime,
    addTime
  };
} 