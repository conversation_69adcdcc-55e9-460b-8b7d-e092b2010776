<template>
  <div class="role-container">
    <!-- 搜索栏 -->
    <a-card class="search-card" :bordered="false">
      <a-form layout="inline" :model="queryParams">
        <a-form-item label="角色名称">
          <a-input
            v-model:value="queryParams.name"
            placeholder="请输入角色名称"
            allow-clear
          />
        </a-form-item>
        <a-form-item label="角色编码">
          <a-input
            v-model:value="queryParams.code"
            placeholder="请输入角色编码"
            allow-clear
          />
        </a-form-item>
        <a-form-item label="状态">
          <a-select
            v-model:value="queryParams.status"
            placeholder="请选择状态"
            style="min-width: 120px"
            allow-clear
          >
            <a-select-option value="1">正常</a-select-option>
            <a-select-option value="0">禁用</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item>
          <a-button type="primary" @click="handleSearch">
            <template #icon><search-outlined /></template>
            查询
          </a-button>
          <a-button style="margin-left: 8px" @click="handleReset">
            <template #icon><clear-outlined /></template>
            重置
          </a-button>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 表格卡片 -->
    <a-card :bordered="false" style="margin-top: 16px">
      <template #title>
        <a-button type="primary" @click="handleAdd">
          <template #icon><plus-outlined /></template>
          新增角色
        </a-button>
      </template>

      <!-- 表格 -->
      <a-table
        :loading="loading"
        :columns="columns"
        :data-source="dataSource"
        :pagination="pagination"
        :row-key="record => record.id"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, text, record }">
          <!-- 状态 -->
          <template v-if="column.dataIndex === 'status'">
            <a-switch
              :checked="text === '1'"
              :loading="record.statusLoading"
              @change="status => handleStatusChange(record, status)"
            />
          </template>

          <!-- 操作 -->
          <template v-else-if="column.dataIndex === 'action'">
            <a-button type="link" size="small" @click="handleEdit(record)">编辑</a-button>
            <a-divider type="vertical" />
            <a-button type="link" size="small" @click="handlePermission(record)">权限分配</a-button>
            <a-divider type="vertical" />
            <a-popconfirm
              title="确定要删除该角色吗？"
              ok-text="确定"
              cancel-text="取消"
              @confirm="handleDelete(record)"
            >
              <a-button type="link" danger size="small">删除</a-button>
            </a-popconfirm>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 角色表单对话框 -->
    <a-modal
      v-model:visible="modalVisible"
      :title="modalTitle"
      width="650px"
      :confirm-loading="confirmLoading"
      @ok="handleModalSubmit"
    >
      <a-form
        ref="formRef"
        :model="form"
        :rules="rules"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item label="角色名称" name="name">
          <a-input v-model:value="form.name" placeholder="请输入角色名称" />
        </a-form-item>

        <a-form-item label="角色编码" name="code">
          <a-input v-model:value="form.code" placeholder="请输入角色编码" :disabled="modalType === 'edit'" />
        </a-form-item>

        <a-form-item label="排序" name="sort">
          <a-input-number v-model:value="form.sort" placeholder="请输入排序值" style="width: 100%" />
        </a-form-item>

        <a-form-item label="状态" name="status">
          <a-radio-group v-model:value="form.status">
            <a-radio value="1">正常</a-radio>
            <a-radio value="0">禁用</a-radio>
          </a-radio-group>
        </a-form-item>

        <a-form-item label="备注" name="remark">
          <a-textarea v-model:value="form.remark" placeholder="请输入备注" :rows="4" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 权限分配对话框 -->
    <a-modal
      v-model:visible="permissionVisible"
      title="权限分配"
      width="600px"
      :confirm-loading="permissionLoading"
      @ok="handlePermissionSubmit"
    >
      <a-spin :spinning="permissionLoading">
        <div class="permission-container">
          <div class="role-info">
            <span class="label">角色名称：</span>
            <span class="value">{{ currentRole.name }}</span>
          </div>
          <div class="role-info">
            <span class="label">角色编码：</span>
            <span class="value">{{ currentRole.code }}</span>
          </div>
          
          <a-divider />
          
          <a-tree
            v-model:checkedKeys="checkedKeys"
            :tree-data="permissionTree"
            show-line
            checkable
            :auto-expand-parent="true"
            :default-expand-all="true"
          />
        </div>
      </a-spin>
    </a-modal>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, computed, toRaw, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import {
  PlusOutlined,
  SearchOutlined,
  ClearOutlined
} from '@ant-design/icons-vue';

// 模拟API函数，实际应用中应该导入真实的API模块
const getRoleList = (params) => {
  return new Promise(resolve => {
    setTimeout(() => {
      resolve({
        data: {
          list: [
            {
              id: 1,
              name: '超级管理员',
              code: 'ADMIN',
              sort: 1,
              status: '1',
              createTime: '2023-03-01 12:00:00',
              remark: '系统超级管理员'
            },
            {
              id: 2,
              name: '普通用户',
              code: 'USER',
              sort: 2,
              status: '1',
              createTime: '2023-03-02 14:30:00',
              remark: '普通用户'
            },
            {
              id: 3,
              name: '访客',
              code: 'VISITOR',
              sort: 3,
              status: '1',
              createTime: '2023-03-03 09:15:00',
              remark: '只读访问权限'
            },
            {
              id: 4,
              name: '编辑人员',
              code: 'EDITOR',
              sort: 4,
              status: '1',
              createTime: '2023-03-04 11:20:00',
              remark: '内容编辑权限'
            },
            {
              id: 5,
              name: '审核人员',
              code: 'REVIEWER',
              sort: 5,
              status: '1',
              createTime: '2023-03-05 16:45:00',
              remark: '内容审核权限'
            }
          ],
          total: 5
        }
      });
    }, 500);
  });
};

const getPermissions = () => {
  return new Promise(resolve => {
    setTimeout(() => {
      resolve({
        data: [
          {
            id: 1,
            name: '系统管理',
            code: 'system',
            children: [
              {
                id: 11,
                name: '用户管理',
                code: 'system:user',
                children: [
                  { id: 111, name: '查看用户', code: 'system:user:list' },
                  { id: 112, name: '新增用户', code: 'system:user:add' },
                  { id: 113, name: '编辑用户', code: 'system:user:edit' },
                  { id: 114, name: '删除用户', code: 'system:user:delete' }
                ]
              },
              {
                id: 12,
                name: '角色管理',
                code: 'system:role',
                children: [
                  { id: 121, name: '查看角色', code: 'system:role:list' },
                  { id: 122, name: '新增角色', code: 'system:role:add' },
                  { id: 123, name: '编辑角色', code: 'system:role:edit' },
                  { id: 124, name: '删除角色', code: 'system:role:delete' }
                ]
              },
              {
                id: 13,
                name: '菜单管理',
                code: 'system:menu',
                children: [
                  { id: 131, name: '查看菜单', code: 'system:menu:list' },
                  { id: 132, name: '新增菜单', code: 'system:menu:add' },
                  { id: 133, name: '编辑菜单', code: 'system:menu:edit' },
                  { id: 134, name: '删除菜单', code: 'system:menu:delete' }
                ]
              }
            ]
          },
          {
            id: 2,
            name: '内容管理',
            code: 'content',
            children: [
              {
                id: 21,
                name: '文章管理',
                code: 'content:article',
                children: [
                  { id: 211, name: '查看文章', code: 'content:article:list' },
                  { id: 212, name: '新增文章', code: 'content:article:add' },
                  { id: 213, name: '编辑文章', code: 'content:article:edit' },
                  { id: 214, name: '删除文章', code: 'content:article:delete' }
                ]
              },
              {
                id: 22,
                name: '评论管理',
                code: 'content:comment',
                children: [
                  { id: 221, name: '查看评论', code: 'content:comment:list' },
                  { id: 222, name: '回复评论', code: 'content:comment:reply' },
                  { id: 223, name: '删除评论', code: 'content:comment:delete' }
                ]
              }
            ]
          }
        ]
      });
    }, 500);
  });
};

const getRolePermissions = (roleId) => {
  return new Promise(resolve => {
    setTimeout(() => {
      let permissionIds = [];
      
      // 模拟不同角色的权限
      if (roleId === 1) {  // 超级管理员拥有所有权限
        permissionIds = [1, 11, 111, 112, 113, 114, 12, 121, 122, 123, 124, 13, 131, 132, 133, 134, 2, 21, 211, 212, 213, 214, 22, 221, 222, 223];
      } else if (roleId === 2) {  // 普通用户
        permissionIds = [1, 11, 111, 2, 21, 211, 22, 221];
      } else if (roleId === 3) {  // 访客
        permissionIds = [1, 11, 111, 2, 21, 211, 22, 221];
      } else if (roleId === 4) {  // 编辑人员
        permissionIds = [2, 21, 211, 212, 213, 22, 221, 222];
      } else if (roleId === 5) {  // 审核人员
        permissionIds = [2, 21, 211, 213, 22, 221, 222, 223];
      }
      
      resolve({
        data: permissionIds
      });
    }, 300);
  });
};

export default defineComponent({
  name: 'RoleList',
  components: {
    PlusOutlined,
    SearchOutlined,
    ClearOutlined
  },
  setup() {
    // 表格数据和加载状态
    const loading = ref(false);
    const dataSource = ref([]);
    
    // 查询参数
    const queryParams = reactive({
      name: '',
      code: '',
      status: undefined
    });
    
    // 分页信息
    const pagination = reactive({
      current: 1,
      pageSize: 10,
      total: 0,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: total => `共 ${total} 条记录`
    });
    
    // 表格列定义
    const columns = [
      {
        title: 'ID',
        dataIndex: 'id',
        key: 'id',
        width: 80
      },
      {
        title: '角色名称',
        dataIndex: 'name',
        key: 'name',
        width: 150
      },
      {
        title: '角色编码',
        dataIndex: 'code',
        key: 'code',
        width: 150
      },
      {
        title: '排序',
        dataIndex: 'sort',
        key: 'sort',
        width: 80
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        width: 100
      },
      {
        title: '创建时间',
        dataIndex: 'createTime',
        key: 'createTime',
        width: 180
      },
      {
        title: '备注',
        dataIndex: 'remark',
        key: 'remark',
        ellipsis: true
      },
      {
        title: '操作',
        dataIndex: 'action',
        key: 'action',
        fixed: 'right',
        width: 250
      }
    ];
    
    // 表单对话框状态
    const modalVisible = ref(false);
    const modalType = ref('add'); // 'add' or 'edit'
    const confirmLoading = ref(false);
    
    // 表单对话框标题
    const modalTitle = computed(() => {
      return modalType.value === 'add' ? '新增角色' : '编辑角色';
    });
    
    // 表单数据
    const formRef = ref(null);
    const form = reactive({
      id: '',
      name: '',
      code: '',
      sort: 0,
      status: '1',
      remark: ''
    });
    
    // 表单验证规则
    const rules = {
      name: [
        { required: true, message: '请输入角色名称', trigger: 'blur' }
      ],
      code: [
        { required: true, message: '请输入角色编码', trigger: 'blur' },
        { pattern: /^[A-Z_]+$/, message: '角色编码只能是大写字母和下划线', trigger: 'blur' }
      ],
      sort: [
        { required: true, message: '请输入排序值', trigger: 'blur' },
        { type: 'number', message: '排序值必须是数字', trigger: 'blur' }
      ]
    };
    
    // 权限分配对话框状态
    const permissionVisible = ref(false);
    const permissionLoading = ref(false);
    const currentRole = ref({});
    const permissionTree = ref([]);
    const checkedKeys = ref([]);
    
    // 获取角色列表
    const fetchRoleList = async () => {
      loading.value = true;
      
      try {
        const res = await getRoleList({
          page: pagination.current,
          pageSize: pagination.pageSize,
          ...queryParams
        });
        
        dataSource.value = res.data.list || [];
        pagination.total = res.data.total || 0;
      } catch (error) {
        console.error('获取角色列表失败:', error);
        message.error('获取角色列表失败');
      } finally {
        loading.value = false;
      }
    };
    
    // 查询
    const handleSearch = () => {
      pagination.current = 1;
      fetchRoleList();
    };
    
    // 重置查询
    const handleReset = () => {
      Object.keys(queryParams).forEach(key => {
        queryParams[key] = '';
      });
      pagination.current = 1;
      fetchRoleList();
    };
    
    // 表格变化处理
    const handleTableChange = ({ current, pageSize }) => {
      pagination.current = current;
      pagination.pageSize = pageSize;
      fetchRoleList();
    };
    
    // 新增角色
    const handleAdd = () => {
      resetForm();
      modalType.value = 'add';
      modalVisible.value = true;
    };
    
    // 编辑角色
    const handleEdit = (record) => {
      resetForm();
      modalType.value = 'edit';
      
      Object.keys(form).forEach(key => {
        if (key in record) {
          form[key] = record[key];
        }
      });
      
      modalVisible.value = true;
    };
    
    // 删除角色
    const handleDelete = async (record) => {
      try {
        message.success(`删除角色成功: ${record.name}`);
        // 重新加载数据
        fetchRoleList();
      } catch (error) {
        console.error('删除角色失败:', error);
        message.error('删除角色失败');
      }
    };
    
    // 修改角色状态
    const handleStatusChange = async (record, status) => {
      // 设置临时loading状态
      record.statusLoading = true;
      
      try {
        record.status = status ? '1' : '0';
        message.success(`${status ? '启用' : '禁用'}角色成功`);
      } catch (error) {
        console.error('修改角色状态失败:', error);
        message.error('修改角色状态失败');
      } finally {
        record.statusLoading = false;
      }
    };
    
    // 重置表单
    const resetForm = () => {
      if (formRef.value) {
        formRef.value.resetFields();
      }
      form.id = '';
      form.name = '';
      form.code = '';
      form.sort = 0;
      form.status = '1';
      form.remark = '';
    };
    
    // 提交表单
    const handleModalSubmit = () => {
      formRef.value.validate()
        .then(async () => {
          confirmLoading.value = true;
          
          try {
            const formData = toRaw(form);
            
            // 模拟API调用
            await new Promise(resolve => setTimeout(resolve, 500));
            
            message.success(`${modalType.value === 'add' ? '新增' : '编辑'}角色成功`);
            modalVisible.value = false;
            fetchRoleList();
          } catch (error) {
            console.error('保存角色失败:', error);
            message.error('保存角色失败');
          } finally {
            confirmLoading.value = false;
          }
        })
        .catch(error => {
          console.log('表单验证失败:', error);
        });
    };
    
    // 打开权限分配对话框
    const handlePermission = async (record) => {
      permissionVisible.value = true;
      currentRole.value = { ...record };
      permissionLoading.value = true;
      
      try {
        // 获取权限树
        const permRes = await getPermissions();
        permissionTree.value = permRes.data || [];
        
        // 获取角色已有权限
        const rolePermRes = await getRolePermissions(record.id);
        checkedKeys.value = rolePermRes.data || [];
      } catch (error) {
        console.error('获取权限数据失败:', error);
        message.error('获取权限数据失败');
      } finally {
        permissionLoading.value = false;
      }
    };
    
    // 提交权限分配
    const handlePermissionSubmit = async () => {
      permissionLoading.value = true;
      
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 500));
        
        message.success('分配权限成功');
        permissionVisible.value = false;
      } catch (error) {
        console.error('分配权限失败:', error);
        message.error('分配权限失败');
      } finally {
        permissionLoading.value = false;
      }
    };
    
    // 组件挂载时加载数据
    onMounted(() => {
      fetchRoleList();
    });
    
    return {
      // 状态
      loading,
      dataSource,
      queryParams,
      pagination,
      columns,
      modalVisible,
      modalType,
      modalTitle,
      confirmLoading,
      formRef,
      form,
      rules,
      permissionVisible,
      permissionLoading,
      currentRole,
      permissionTree,
      checkedKeys,
      
      // 方法
      handleSearch,
      handleReset,
      handleTableChange,
      handleAdd,
      handleEdit,
      handleDelete,
      handleStatusChange,
      handleModalSubmit,
      handlePermission,
      handlePermissionSubmit
    };
  }
});
</script>

<style lang="less" scoped>
.role-container {
  padding: 24px;
  
  .search-card {
    margin-bottom: 16px;
  }
  
  .permission-container {
    .role-info {
      margin-bottom: 12px;
      
      .label {
        font-weight: bold;
        margin-right: 8px;
      }
    }
  }
}
</style> 