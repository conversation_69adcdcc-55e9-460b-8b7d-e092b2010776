import { ref, reactive, computed, onMounted, watch } from 'vue';
import { createPagination, getSorterParams, getFilterParams } from '../utils/table';

/**
 * 表格数据管理钩子
 * @param {Object} options - 配置选项
 * @param {Function} options.fetchData - 获取数据的函数，接收参数 (params)
 * @param {Object} options.defaultParams - 默认参数
 * @param {number} options.defaultPageSize - 默认每页条数
 * @param {boolean} options.immediate - 是否立即加载数据
 * @param {Function} options.onSuccess - 成功回调
 * @param {Function} options.onError - 错误回调
 * @returns {Object} 表格数据和操作方法
 */
export default function useTable(options = {}) {
  const {
    fetchData,
    defaultParams = {},
    defaultPageSize = 10,
    immediate = true,
    onSuccess,
    onError
  } = options;
  
  if (typeof fetchData !== 'function') {
    throw new Error('fetchData必须是一个函数');
  }
  
  // 表格数据
  const dataSource = ref([]);
  // 加载状态
  const loading = ref(false);
  // 错误信息
  const error = ref(null);
  // 选中行的keys
  const selectedRowKeys = ref([]);
  // 选中的行数据
  const selectedRows = ref([]);
  
  // 分页信息
  const pagination = reactive(createPagination({
    current: 1,
    pageSize: defaultPageSize,
    total: 0
  }));
  
  // 排序信息
  const sorter = reactive({
    field: '',
    order: ''
  });
  
  // 筛选信息
  const filters = reactive({});
  
  // 查询参数
  const queryParams = reactive({
    ...defaultParams
  });
  
  // 完整请求参数
  const requestParams = computed(() => {
    return {
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
      ...getSorterParams(sorter),
      ...getFilterParams(filters),
      ...queryParams
    };
  });
  
  // 重置分页到第一页
  const resetPagination = () => {
    pagination.current = 1;
  };
  
  // 重置所有状态
  const reset = () => {
    resetPagination();
    
    // 重置排序
    sorter.field = '';
    sorter.order = '';
    
    // 重置筛选
    Object.keys(filters).forEach(key => {
      filters[key] = undefined;
    });
    
    // 重置选中行
    selectedRowKeys.value = [];
    selectedRows.value = [];
    
    // 重置查询参数（保留默认参数）
    Object.keys(queryParams).forEach(key => {
      if (key in defaultParams) {
        queryParams[key] = defaultParams[key];
      } else {
        delete queryParams[key];
      }
    });
  };
  
  // 获取表格数据
  const getList = async (params = {}) => {
    loading.value = true;
    error.value = null;
    
    try {
      // 合并查询参数
      const mergedParams = {
        ...requestParams.value,
        ...params
      };
      
      // 调用接口获取数据
      const res = await fetchData(mergedParams);
      
      // 更新表格数据
      if (res && res.data) {
        dataSource.value = res.data.list || res.data || [];
        
        // 更新分页信息
        if (res.data.total !== undefined) {
          pagination.total = res.data.total;
        }
        
        // 调用成功回调
        if (typeof onSuccess === 'function') {
          onSuccess(res);
        }
      }
      
      return res;
    } catch (e) {
      console.error('获取表格数据失败:', e);
      error.value = e.message || '获取数据失败';
      
      // 调用错误回调
      if (typeof onError === 'function') {
        onError(e);
      }
      
      throw e;
    } finally {
      loading.value = false;
    }
  };
  
  // 刷新表格数据
  const refresh = () => {
    return getList();
  };
  
  // 处理表格变化（分页、排序、筛选）
  const handleTableChange = (pag, filt, sort) => {
    // 更新分页
    if (pag) {
      pagination.current = pag.current;
      pagination.pageSize = pag.pageSize;
    }
    
    // 更新筛选
    if (filt) {
      Object.keys(filt).forEach(key => {
        filters[key] = filt[key];
      });
    }
    
    // 更新排序
    if (sort) {
      sorter.field = sort.field;
      sorter.order = sort.order;
    }
    
    // 重新获取数据
    return getList();
  };
  
  // 设置查询参数并刷新
  const search = (params = {}) => {
    // 重置到第一页
    resetPagination();
    
    // 更新查询参数
    Object.keys(params).forEach(key => {
      queryParams[key] = params[key];
    });
    
    // 重新获取数据
    return getList();
  };
  
  // 重置查询参数并刷新
  const resetSearch = () => {
    reset();
    return getList();
  };
  
  // 处理行选择变化
  const onSelectChange = (rowKeys, rows) => {
    selectedRowKeys.value = rowKeys;
    selectedRows.value = rows;
  };
  
  // 获取行属性
  const getRowProps = (record) => {
    return {
      onClick: () => {
        // 可以在这里添加行点击事件处理
      }
    };
  };
  
  // 初始加载数据
  if (immediate) {
    getList();
  }
  
  // 监听参数变化，自动刷新数据（可选，根据需要启用）
  // watch(requestParams, () => {
  //   getList();
  // }, { deep: true });
  
  return {
    // 数据
    dataSource,
    pagination,
    loading,
    error,
    queryParams,
    sorter,
    filters,
    selectedRowKeys,
    selectedRows,
    
    // 方法
    getList,
    refresh,
    reset,
    search,
    resetSearch,
    handleTableChange,
    onSelectChange,
    getRowProps
  };
} 