"""
头像模板数据模型
"""

from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, Float, JSON
from sqlalchemy.sql import func
from ..core.sqlalchemy_db import Base

class AvatarTemplate(Base):
    """头像模板模型"""
    __tablename__ = "avatar_templates"

    id = Column(String(50), primary_key=True, index=True)
    name = Column(String(100), nullable=False, comment="模板名称")
    description = Column(Text, comment="模板描述")
    
    # 基本属性
    gender = Column(String(10), nullable=False, comment="性别: male/female")
    age_range = Column(String(20), comment="年龄范围，如: 25-35")
    ethnicity = Column(String(50), comment="种族/民族")
    
    # 分类和标签
    category = Column(String(50), nullable=False, comment="分类: teacher/business/service/host等")
    type = Column(String(50), nullable=False, comment="类型")
    style = Column(String(50), comment="风格: professional/casual/modern等")
    tags = Column(JSON, comment="标签数组")
    
    # 外观特征
    hair_color = Column(String(30), comment="发色")
    hair_style = Column(String(50), comment="发型")
    eye_color = Column(String(30), comment="眼色")
    skin_tone = Column(String(30), comment="肤色")
    
    # 服装和配饰
    clothing_style = Column(String(100), comment="服装风格")
    accessories = Column(JSON, comment="配饰列表")
    
    # 文件信息
    image_url = Column(String(500), comment="头像图片URL")
    image_path = Column(String(500), comment="本地图片路径")
    thumbnail_url = Column(String(500), comment="缩略图URL")
    
    # 质量和匹配
    quality_score = Column(Float, default=0.0, comment="质量评分 0-100")
    match_score = Column(Float, default=0.0, comment="匹配度评分 0-100")
    features = Column(JSON, comment="特征列表")
    
    # 使用统计
    usage_count = Column(Integer, default=0, comment="使用次数")
    popularity_score = Column(Float, default=0.0, comment="受欢迎程度")
    
    # 状态和管理
    is_active = Column(Boolean, default=True, comment="是否启用")
    is_featured = Column(Boolean, default=False, comment="是否推荐")
    is_premium = Column(Boolean, default=False, comment="是否高级模板")
    
    # 排序和显示
    sort_order = Column(Integer, default=0, comment="排序顺序")
    display_priority = Column(Integer, default=0, comment="显示优先级")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), onupdate=func.now(), comment="更新时间")
    
    # 创建者信息
    created_by = Column(String(100), comment="创建者")
    source = Column(String(100), comment="来源: ai_generated/uploaded/manual")
    
    def to_dict(self):
        """转换为字典"""
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "gender": self.gender,
            "age_range": self.age_range,
            "ethnicity": self.ethnicity,
            "category": self.category,
            "type": self.type,
            "style": self.style,
            "tags": self.tags or [],
            "hair_color": self.hair_color,
            "hair_style": self.hair_style,
            "eye_color": self.eye_color,
            "skin_tone": self.skin_tone,
            "clothing_style": self.clothing_style,
            "accessories": self.accessories or [],
            "image_url": self.image_url,
            "image_path": self.image_path,
            "thumbnail_url": self.thumbnail_url,
            "quality_score": self.quality_score,
            "match_score": self.match_score,
            "features": self.features or [],
            "usage_count": self.usage_count,
            "popularity_score": self.popularity_score,
            "is_active": self.is_active,
            "is_featured": self.is_featured,
            "is_premium": self.is_premium,
            "sort_order": self.sort_order,
            "display_priority": self.display_priority,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "created_by": self.created_by,
            "source": self.source
        }
