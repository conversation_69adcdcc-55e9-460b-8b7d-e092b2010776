#!/usr/bin/env python3
"""
测试最简单的任务
"""
import os
import sys
import time
from pathlib import Path
from dotenv import load_dotenv

# 添加后端路径
backend_dir = Path(__file__).parent.parent
sys.path.append(str(backend_dir))

# 加载环境变量
env_path = backend_dir / ".env"
load_dotenv(env_path)

def test_simple_add():
    """测试简单加法任务"""
    print("🧮 测试简单加法任务")
    print("=" * 50)
    
    try:
        from app.tasks.simple_test_task import simple_add
        
        # 提交任务
        result = simple_add.delay(5, 3)
        
        print(f"✅ 加法任务提交成功")
        print(f"📊 任务ID: {result.id}")
        print(f"📊 初始状态: {result.status}")
        
        # 等待结果
        print(f"⏳ 等待结果（超时 10 秒）...")
        try:
            final_result = result.get(timeout=10)
            print(f"✅ 加法任务完成: 5 + 3 = {final_result}")
            return True
        except Exception as e:
            print(f"❌ 获取结果失败: {e}")
            print(f"📊 最终状态: {result.status}")
            return False
            
    except Exception as e:
        print(f"❌ 加法任务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_simple_hello():
    """测试简单问候任务"""
    print(f"\n👋 测试简单问候任务")
    print("=" * 50)
    
    try:
        from app.tasks.simple_test_task import simple_hello
        
        # 提交任务
        result = simple_hello.delay("Celery")
        
        print(f"✅ 问候任务提交成功")
        print(f"📊 任务ID: {result.id}")
        print(f"📊 初始状态: {result.status}")
        
        # 等待结果
        print(f"⏳ 等待结果（超时 10 秒）...")
        try:
            final_result = result.get(timeout=10)
            print(f"✅ 问候任务完成: {final_result}")
            return True
        except Exception as e:
            print(f"❌ 获取结果失败: {e}")
            print(f"📊 最终状态: {result.status}")
            return False
            
    except Exception as e:
        print(f"❌ 问候任务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_worker_status():
    """检查 Worker 状态"""
    print(f"\n🔍 检查 Worker 状态")
    print("=" * 50)
    
    try:
        from app.core.celery_app import celery_app
        
        inspect = celery_app.control.inspect()
        
        # 检查活跃的 Worker
        active = inspect.active()
        if active:
            print(f"✅ 活跃 Worker: {list(active.keys())}")
            for worker, tasks in active.items():
                print(f"   {worker}: {len(tasks)} 个活跃任务")
        else:
            print(f"❌ 没有活跃的 Worker")
            return False
        
        # 检查注册的任务
        registered = inspect.registered()
        if registered:
            for worker, tasks in registered.items():
                simple_tasks = [t for t in tasks if 'simple' in t]
                if simple_tasks:
                    print(f"   {worker} 简单任务: {simple_tasks}")
        
        return True
        
    except Exception as e:
        print(f"❌ Worker 状态检查失败: {e}")
        return False

def main():
    """主函数"""
    print("🧪 最简单任务测试")
    print("=" * 60)
    
    # 1. 检查 Worker 状态
    worker_ok = check_worker_status()
    if not worker_ok:
        print(f"\n❌ Worker 未运行，无法继续测试")
        return
    
    # 2. 测试简单加法任务
    add_ok = test_simple_add()
    
    # 3. 测试简单问候任务
    hello_ok = test_simple_hello()
    
    # 总结
    print(f"\n📊 简单任务测试总结:")
    print(f"✅ Worker 状态: 正常")
    print(f"{'✅' if add_ok else '❌'} 加法任务: {'成功' if add_ok else '失败'}")
    print(f"{'✅' if hello_ok else '❌'} 问候任务: {'成功' if hello_ok else '失败'}")
    
    if add_ok and hello_ok:
        print(f"\n🎉 基本 Celery 功能完全正常！")
        print(f"💡 问题在于统一任务管理器的实现")
        print(f"💡 现在可以专注于修复统一任务问题")
    elif add_ok or hello_ok:
        print(f"\n✅ 部分 Celery 功能正常")
        print(f"❌ 但仍有问题需要解决")
    else:
        print(f"\n❌ Celery 基本功能有问题")
        print(f"💡 需要检查 Worker 日志获取更多信息")

if __name__ == "__main__":
    main()
