#!/usr/bin/env python3
"""
修复翻译服务中的语言检测问题
"""

import os
import sys
import re
import logging
from pathlib import Path

# 添加父目录到路径，以便导入模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def patch_mt_service():
    """
    修复翻译服务中的语言检测问题
    """
    # 获取mt_service.py文件路径
    mt_service_path = os.path.join(os.path.dirname(__file__), '..', 'services', 'mt_service.py')
    
    # 检查文件是否存在
    if not os.path.exists(mt_service_path):
        logger.error(f"找不到文件: {mt_service_path}")
        return False
    
    # 读取文件内容
    with open(mt_service_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否已包含contains_chinese方法
    if 'def contains_chinese(self, text: str)' in content:
        logger.info("文件已包含contains_chinese方法，无需修改")
        return True
    
    # 如果没有contains_chinese方法，添加该方法
    contains_chinese_method = '''
    def contains_chinese(self, text: str) -> bool:
        """
        检查文本是否包含中文字符
        
        参数:
            text: 待检查文本
            
        返回:
            是否包含中文字符
        """
        for char in text:
            if '\\u4e00' <= char <= '\\u9fff':
                return True
        return False
    '''
    
    # 查找class MachineTranslationService:的位置
    class_match = re.search(r'class\s+MachineTranslationService\s*:', content)
    if not class_match:
        logger.error("找不到MachineTranslationService类定义")
        return False
    
    # 查找detect_language方法的位置
    detect_method_match = re.search(r'def\s+detect_language\s*\(\s*self\s*,\s*text\s*:\s*str\s*\)\s*->\s*str\s*:', content)
    if not detect_method_match:
        logger.error("找不到detect_language方法定义")
        return False
    
    # 在detect_language方法之后插入contains_chinese方法
    insert_pos = detect_method_match.end()
    new_content = content[:insert_pos] + contains_chinese_method + content[insert_pos:]
    
    # 写入修改后的内容
    with open(mt_service_path, 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    logger.info("成功添加contains_chinese方法")
    return True

def patch_celery_task():
    """
    修复异步翻译任务中的语言检测问题
    """
    # 获取translation_tasks.py文件路径
    tasks_path = os.path.join(os.path.dirname(__file__), '..', 'services', 'celery_tasks', 'translation_tasks.py')
    
    # 检查文件是否存在
    if not os.path.exists(tasks_path):
        logger.error(f"找不到文件: {tasks_path}")
        return False
    
    # 读取文件内容
    with open(tasks_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找async_text_translate_task函数定义
    task_match = re.search(r'@shared_task\(name="async_text_translate_task".*?\)\s*\ndef\s+async_text_translate_task\s*\(.*?\):', content, re.DOTALL)
    if not task_match:
        logger.error("找不到async_text_translate_task函数定义")
        return False
    
    # 查找函数体开始的位置
    task_body_start = content.find(':', task_match.start()) + 1
    
    # 查找第一个try语句
    try_match = re.search(r'\s+try\s*:', content[task_body_start:])
    if not try_match:
        logger.error("找不到try语句")
        return False
    
    # 查找mt_service初始化的位置
    mt_service_init = re.search(r'\s+mt_service\s*=\s*MachineTranslationService\(\)', content[task_body_start:])
    if not mt_service_init:
        logger.error("找不到mt_service初始化")
        return False
    
    # 计算插入位置
    mt_service_end = task_body_start + mt_service_init.end()
    
    # 准备要插入的代码
    language_detection_code = '''
        
        # 如果源语言为auto，先检测是否包含中文
        if source_lang == "auto":
            # 检查是否包含中文字符
            if mt_service.contains_chinese(text):
                logger.info(f"检测到中文字符，设置源语言为中文")
                source_lang = "zh"  # 设置源语言为中文
    '''
    
    # 插入代码
    new_content = content[:mt_service_end] + language_detection_code + content[mt_service_end:]
    
    # 写入修改后的内容
    with open(tasks_path, 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    logger.info("成功修改async_text_translate_task函数")
    return True

def main():
    """
    主函数
    """
    logger.info("开始修复翻译服务中的语言检测问题")
    
    # 修复mt_service.py
    mt_service_fixed = patch_mt_service()
    
    # 修复translation_tasks.py
    celery_task_fixed = patch_celery_task()
    
    if mt_service_fixed and celery_task_fixed:
        logger.info("修复完成，请重启服务以应用更改")
    elif mt_service_fixed:
        logger.info("翻译服务修复成功，但异步任务修复失败")
    elif celery_task_fixed:
        logger.info("异步任务修复成功，但翻译服务修复失败")
    else:
        logger.error("修复失败")
        return False
    
    print("\n修复完成，请执行以下命令重启服务:")
    print("1. 停止当前服务: Ctrl+C")
    print("2. 重启Celery Worker: celery -A services.celery_app worker --loglevel=info")
    print("3. 重启FastAPI服务: python main.py")
    
    return True

if __name__ == "__main__":
    main() 