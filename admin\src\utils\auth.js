import Cookies from 'js-cookie';

const TokenKey = 'Admin-Token';
const RefreshTokenKey = 'Admin-Refresh-Token';

/**
 * 获取Token
 * @returns {string}
 */
export function getToken() {
  return Cookies.get(TokenKey);
}

/**
 * 设置Token
 * @param {string} token
 */
export function setToken(token) {
  return Cookies.set(TokenKey, token);
}

/**
 * 删除Token
 */
export function removeToken() {
  return Cookies.remove(TokenKey);
}

/**
 * 获取刷新Token
 * @returns {string}
 */
export function getRefreshToken() {
  return Cookies.get(RefreshTokenKey);
}

/**
 * 设置刷新Token
 * @param {string} token
 */
export function setRefreshToken(token) {
  return Cookies.set(RefreshTokenKey, token);
}

/**
 * 删除刷新Token
 */
export function removeRefreshToken() {
  return Cookies.remove(RefreshTokenKey);
}

/**
 * 获取用户信息
 * @returns {Object}
 */
export function getUserInfo() {
  const userInfo = localStorage.getItem('userInfo');
  return userInfo ? JSON.parse(userInfo) : null;
}

/**
 * 设置用户信息
 * @param {Object} userInfo
 */
export function setUserInfo(userInfo) {
  localStorage.setItem('userInfo', JSON.stringify(userInfo));
}

/**
 * 删除用户信息
 */
export function removeUserInfo() {
  localStorage.removeItem('userInfo');
}

/**
 * 获取用户权限
 * @returns {Array}
 */
export function getPermissions() {
  const permissions = localStorage.getItem('permissions');
  return permissions ? JSON.parse(permissions) : [];
}

/**
 * 设置用户权限
 * @param {Array} permissions
 */
export function setPermissions(permissions) {
  localStorage.setItem('permissions', JSON.stringify(permissions));
}

/**
 * 删除用户权限
 */
export function removePermissions() {
  localStorage.removeItem('permissions');
}

/**
 * 清除所有身份认证信息
 */
export function clearAuth() {
  removeToken();
  removeRefreshToken();
  removeUserInfo();
  removePermissions();
}

/**
 * 检查是否拥有权限
 * @param {string|Array} permission - 权限标识或权限标识数组
 * @returns {boolean}
 */
export function hasPermission(permission) {
  const userPermissions = getPermissions();
  
  // 超级管理员拥有所有权限
  if (userPermissions.includes('*')) {
    return true;
  }
  
  if (Array.isArray(permission)) {
    return permission.some(p => userPermissions.includes(p));
  }
  
  return userPermissions.includes(permission);
}

/**
 * 是否已登录
 * @returns {boolean}
 */
export function isLoggedIn() {
  return !!getToken();
}

export default {
  getToken,
  setToken,
  removeToken,
  getRefreshToken,
  setRefreshToken,
  removeRefreshToken,
  getUserInfo,
  setUserInfo,
  removeUserInfo,
  getPermissions,
  setPermissions,
  removePermissions,
  clearAuth,
  hasPermission,
  isLoggedIn
}; 