"""
简单智能体服务 - 避免推理模型的思考过程
"""

import httpx
from typing import Dict, Any
import re


class SimpleAgentService:
    """简单智能体服务类 - 专注于直接对话"""

    def __init__(self):
        self.ollama_base_url = "http://localhost:11434"
        # 使用标准对话模型，避免推理模型
        self.available_models = [
            "llama3.2:3b",                     # 轻量级对话模型
            "llama3.1:8b",                     # 标准对话模型
            "qwen2.5:7b",                      # 中文友好对话模型
            "gemma2:9b",                       # Google对话模型
            "mistral:7b"                       # Mistral对话模型
        ]
        self.default_model = "llama3.2:3b"
        self._model_cache = None

    async def chat_with_agent(self, agent: Dict[str, Any], message: str) -> str:
        """与智能体对话 - 简化版本"""
        try:
            agent_type = agent.get("agent_type", "chat")
            agent_name = agent.get("name", "智能体")

            # 构建简洁的系统提示词
            if agent_type == "language_tutor":
                system_prompt = f"""You are {agent_name}, a friendly English teacher. 

Rules:
1. Always respond directly as the teacher
2. Keep responses short and natural (1-2 sentences)
3. Ask simple follow-up questions
4. Be encouraging and patient

Example:
User: "你好"
You: "Hello! Nice to meet you! What's your name?"

Respond naturally as a teacher would."""
            else:
                system_prompt = f"You are {agent_name}, a helpful assistant. Keep responses short and direct."

            # 调用 Ollama API
            response = await self._call_ollama_simple(system_prompt, message)
            return response

        except Exception as e:
            print(f"[ERROR] 简单智能体对话失败: {str(e)}")
            return "Hello! I'm your English teacher. How can I help you today?"

    async def _get_best_model(self) -> str:
        """获取最佳可用模型"""
        if self._model_cache:
            return self._model_cache

        try:
            async with httpx.AsyncClient(timeout=5.0) as client:
                response = await client.get(f"{self.ollama_base_url}/api/tags")
                if response.status_code == 200:
                    models_data = response.json()
                    available_models = [model["name"] for model in models_data.get("models", [])]

                    # 按优先级选择模型
                    for model in self.available_models:
                        if model in available_models:
                            print(f"[DEBUG] 选择模型: {model}")
                            self._model_cache = model
                            return model

                    # 使用第一个可用的
                    if available_models:
                        self._model_cache = available_models[0]
                        return available_models[0]

        except Exception as e:
            print(f"[ERROR] 获取模型失败: {str(e)}")

        return self.default_model

    async def _call_ollama_simple(self, system_prompt: str, user_message: str) -> str:
        """调用 Ollama API - 简化版本"""
        try:
            model = await self._get_best_model()

            async with httpx.AsyncClient(timeout=30.0) as client:
                payload = {
                    "model": model,
                    "messages": [
                        {
                            "role": "system",
                            "content": system_prompt
                        },
                        {
                            "role": "user",
                            "content": user_message
                        }
                    ],
                    "stream": False,
                    "options": {
                        "temperature": 0.8,      # 稍高的创造性
                        "top_p": 0.9,
                        "num_predict": 100,      # 限制回复长度
                        "num_ctx": 2048,         # 较小的上下文
                        "repeat_penalty": 1.1,
                        "top_k": 40
                    }
                }

                print(f"[DEBUG] 使用简单智能体模型: {model}")
                print(f"[DEBUG] 用户消息: {user_message}")

                response = await client.post(
                    f"{self.ollama_base_url}/api/chat",
                    json=payload
                )

                if response.status_code == 200:
                    result = response.json()
                    assistant_message = result.get("message", {}).get("content", "")

                    if not assistant_message:
                        return "Hello! How can I help you today?"

                    # 简单清理
                    cleaned_message = self._simple_clean(assistant_message)
                    print(f"[DEBUG] 简单智能体响应: {cleaned_message}")
                    return cleaned_message
                else:
                    print(f"[ERROR] Ollama API 错误: {response.status_code}")
                    return "Hello! I'm here to help you learn English."

        except Exception as e:
            print(f"[ERROR] 调用 Ollama 失败: {str(e)}")
            # 根据用户消息生成不同的回复
            return self._generate_simple_fallback(user_message)

    def _simple_clean(self, response: str) -> str:
        """简单清理响应"""
        if not response:
            return "Hello! How can I help you?"

        # 移除明显的思考标记
        response = re.sub(r'<think>.*?</think>', '', response, flags=re.DOTALL | re.IGNORECASE)
        response = re.sub(r'<thinking>.*?</thinking>', '', response, flags=re.DOTALL | re.IGNORECASE)
        
        # 移除分析性开头
        lines = response.split('\n')
        clean_lines = []
        
        for line in lines:
            line = line.strip()
            if line and not any(phrase in line.lower() for phrase in [
                'i need to', 'let me think', 'the user', 'i should respond'
            ]):
                clean_lines.append(line)
        
        result = ' '.join(clean_lines).strip()
        
        # 如果太短，提供默认回复
        if len(result) < 5:
            return "Hello! How can I help you today?"
            
        return result

    def _generate_simple_fallback(self, user_message: str) -> str:
        """生成简单的回退回复"""
        message_lower = user_message.lower()

        # 根据用户消息内容生成不同回复
        if any(word in message_lower for word in ['hello', 'hi', 'hey', '你好', 'good morning', 'good afternoon']):
            return "Hello! Nice to meet you! How can I help you today?"
        elif any(word in message_lower for word in ['how are you', '怎么样', 'how do you do']):
            return "I'm doing great, thank you for asking! How are you doing?"
        elif any(word in message_lower for word in ['what', 'how', 'why', 'when', 'where']):
            return "That's a great question! Let me help you with that. Could you tell me more details?"
        elif any(word in message_lower for word in ['learn', 'study', 'practice', '学习', '练习']):
            return "Excellent! I love helping students learn. What would you like to practice today?"
        elif any(word in message_lower for word in ['thank', 'thanks', '谢谢']):
            return "You're very welcome! I'm happy to help. Is there anything else you'd like to know?"
        else:
            return f"I understand you said '{user_message}'. That's interesting! Could you tell me more about what you'd like to learn or practice?"
