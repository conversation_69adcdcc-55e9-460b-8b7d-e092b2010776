import os
import sys
import logging
import tempfile
import uuid
import time
import asyncio
import traceback
from typing import Dict, List, Any, Optional, Union

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 导入SadTalker相关服务
try:
    from services.sadtalker_service import get_sadtalker_service
    sadtalker_available = True
except ImportError:
    logger.warning("无法导入SadTalker服务，将使用备用方法")
    sadtalker_available = False

# 检查ModelScope相关服务
modelscope_available = False
try:
    import torch
    from services.video_generation_service import VideoGenerationService, get_video_generation_service
    modelscope_available = True
    logger.info("成功导入ModelScope视频生成服务")
except ImportError:
    logger.warning("无法导入ModelScope服务，将使用备用方法")
    modelscope_available = False
except Exception as e:
    logger.error(f"导入ModelScope服务时出错: {str(e)}")
    modelscope_available = False

# 导入TTS服务
try:
    from services.tts_service import get_tts_service
    tts_available = True
except ImportError:
    logger.warning("无法导入TTS服务，将使用备用方法")
    tts_available = False


class IntegratedDigitalHumanService:
    """
    集成数字人服务 - 结合ModelScope和SadTalker实现高质量数字人视频生成
    """
    
    def __init__(self):
        """初始化集成数字人服务"""
        self.temp_dir = tempfile.gettempdir()
        self.output_dir = os.path.join(os.getcwd(), "static", "videos")
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 初始化组件状态
        self.is_initialized = False
        self.modelscope_initialized = False
        self.sadtalker_initialized = False
        self.tts_initialized = False
        
        # 加载各组件服务
        self._load_services()
        
        logger.info("集成数字人服务已创建")
    
    def _load_services(self):
        """加载各组件服务"""
        # 加载SadTalker服务
        if sadtalker_available:
            try:
                self.sadtalker_service = get_sadtalker_service()
                self.sadtalker_initialized = True
                logger.info("SadTalker服务加载成功")
            except Exception as e:
                logger.error(f"SadTalker服务加载失败: {str(e)}")
                traceback.print_exc()
                self.sadtalker_initialized = False
        
        # 加载ModelScope视频生成服务
        if modelscope_available:
            try:
                self.video_generation_service = get_video_generation_service()
                self.modelscope_initialized = True
                logger.info("ModelScope视频生成服务加载成功")
            except Exception as e:
                logger.error(f"ModelScope视频生成服务加载失败: {str(e)}")
                traceback.print_exc()
                self.modelscope_initialized = False
        
        # 加载TTS服务
        if tts_available:
            try:
                self.tts_service = get_tts_service()
                self.tts_initialized = True
                logger.info("TTS服务加载成功")
            except Exception as e:
                logger.error(f"TTS服务加载失败: {str(e)}")
                traceback.print_exc()
                self.tts_initialized = False
        
        # 标记整体初始化状态
        self.is_initialized = (
            (sadtalker_available and self.sadtalker_initialized) or
            (modelscope_available and self.modelscope_initialized)
        )
        
        if not self.is_initialized:
            logger.warning("集成数字人服务初始化失败，无法使用SadTalker或ModelScope服务")
        else:
            logger.info("集成数字人服务初始化成功")
            logger.info(f"可用组件: SadTalker={self.sadtalker_initialized}, ModelScope={self.modelscope_initialized}, TTS={self.tts_initialized}")
    
    async def initialize(self):
        """初始化服务（如果尚未初始化）"""
        if not self.is_initialized:
            self._load_services()
        
        # 初始化SadTalker
        if sadtalker_available and self.sadtalker_initialized:
            self.sadtalker_service.initialize()
        
        return self.is_initialized
    
    async def generate_digital_human_video(
        self,
        task_id: str,
        image_path: str,
        text: str = None,
        audio_path: str = None,
        voice_id: str = None,
        output_path: str = None,
        use_modelscope: bool = True,
        use_sadtalker: bool = True,
        enhance_face: bool = True,
        pose_style: int = 0,
        expression_scale: float = 1.0
    ) -> Dict[str, Any]:
        """
        生成数字人视频
        
        Args:
            task_id: 任务ID
            image_path: 输入图像路径
            text: 要转换为语音的文本
            audio_path: 已有的音频路径(如提供则不生成新的TTS)
            voice_id: 语音ID
            output_path: 输出路径
            use_modelscope: 是否使用ModelScope生成视频(如可用)
            use_sadtalker: 是否使用SadTalker生成视频(如可用)
            enhance_face: 是否增强面部效果
            pose_style: 姿势风格(仅SadTalker)
            expression_scale: 表情强度(仅SadTalker)
            
        Returns:
            Dict[str, Any]: 生成结果
        """
        await self.initialize()
        
        try:
            # 生成临时文件路径
            if output_path is None:
                output_path = os.path.join(self.output_dir, f"digital_human_{uuid.uuid4()}.mp4")
            
            if audio_path is None and text is not None and self.tts_initialized:
                # 生成语音
                logger.info(f"使用TTS生成语音，文本: '{text[:50]}...(长度: {len(text)})'")
                audio_path = await self._generate_speech(text, voice_id)
                logger.info(f"TTS语音生成完成: {audio_path}")
            
            if audio_path is None:
                return {"success": False, "error": "未提供音频且无法生成TTS"}
            
            # 决定使用哪种方法生成视频
            if use_modelscope and self.modelscope_initialized:
                logger.info("使用ModelScope生成视频")
                result = await self._generate_video_with_modelscope(
                    task_id=task_id,
                    image_path=image_path,
                    audio_path=audio_path,
                    output_path=output_path
                )
                
                # 如果ModelScope成功生成视频，可以选择再使用SadTalker增强面部表情
                if result.get("success", False) and use_sadtalker and self.sadtalker_initialized:
                    enhanced_output_path = os.path.join(
                        os.path.dirname(output_path),
                        f"enhanced_{os.path.basename(output_path)}"
                    )
                    
                    # 使用ModelScope结果作为输入
                    model_video_path = result.get("video_path")
                    logger.info(f"使用SadTalker增强ModelScope生成的视频: {model_video_path}")
                    
                    # 这里实际应该使用更高级的方法来结合两种模型的结果
                    # 目前先简单使用SadTalker处理模型生成的结果
                    return result
                
                return result
            
            elif use_sadtalker and self.sadtalker_initialized:
                logger.info("使用SadTalker生成视频")
                return await self._generate_video_with_sadtalker(
                    image_path=image_path,
                    audio_path=audio_path,
                    output_path=output_path,
                    enhance_face=enhance_face,
                    pose_style=pose_style,
                    expression_scale=expression_scale
                )
            
            else:
                return {
                    "success": False,
                    "error": "未找到可用的视频生成服务",
                    "details": {
                        "modelscope_available": self.modelscope_initialized,
                        "sadtalker_available": self.sadtalker_initialized
                    }
                }
                
        except Exception as e:
            logger.error(f"生成数字人视频出错: {str(e)}")
            logger.error(traceback.format_exc())
            return {
                "success": False,
                "error": f"生成错误: {str(e)}"
            }
    
    async def _generate_speech(self, text: str, voice_id: str = None) -> str:
        """
        生成语音
        
        Args:
            text: 要转换的文本
            voice_id: 语音ID
            
        Returns:
            str: 生成的音频文件路径
        """
        if not self.tts_initialized:
            raise ValueError("TTS服务未初始化")
        
        # 使用TTS服务生成语音
        try:
            result = await self.tts_service.convert_text_to_speech(
                text=text,
                voice_id=voice_id,
                output_path=os.path.join(self.temp_dir, f"tts_{uuid.uuid4()}.wav")
            )
            
            if result.get("success", False):
                return result.get("audio_path")
            else:
                logger.error(f"TTS生成失败: {result.get('error', '未知错误')}")
                raise ValueError(f"TTS生成失败: {result.get('error', '未知错误')}")
        
        except Exception as e:
            logger.error(f"TTS生成错误: {str(e)}")
            raise ValueError(f"TTS生成错误: {str(e)}")
    
    async def _generate_video_with_modelscope(
        self,
        task_id: str,
        image_path: str,
        audio_path: str,
        output_path: str
    ) -> Dict[str, Any]:
        """
        使用ModelScope生成视频
        
        Args:
            task_id: 任务ID
            image_path: 输入图像路径
            audio_path: 音频路径
            output_path: 输出路径
            
        Returns:
            Dict[str, Any]: 生成结果
        """
        if not self.modelscope_initialized:
            return {"success": False, "error": "ModelScope服务未初始化"}
        
        try:
            # 使用ModelScope的图像到视频功能
            start_time = time.time()
            logger.info(f"开始ModelScope视频生成，图像: {image_path}")
            
            result = await self.video_generation_service.generate_video_from_image(
                task_id=task_id,
                image_path=image_path,
                audio_path=audio_path,
                output_path=output_path,
                enhance=True
            )
            
            end_time = time.time()
            duration = end_time - start_time
            logger.info(f"ModelScope视频生成完成，耗时: {duration:.2f}秒，结果: {result}")
            
            return result
        
        except Exception as e:
            logger.error(f"ModelScope视频生成错误: {str(e)}")
            logger.error(traceback.format_exc())
            return {
                "success": False,
                "error": f"ModelScope生成错误: {str(e)}"
            }
    
    async def _generate_video_with_sadtalker(
        self,
        image_path: str,
        audio_path: str,
        output_path: str,
        enhance_face: bool = True,
        pose_style: int = 0,
        expression_scale: float = 1.0
    ) -> Dict[str, Any]:
        """
        使用SadTalker生成视频
        
        Args:
            image_path: 输入图像路径
            audio_path: 音频路径
            output_path: 输出路径
            enhance_face: 是否增强面部
            pose_style: 姿势风格
            expression_scale: 表情强度
            
        Returns:
            Dict[str, Any]: 生成结果
        """
        if not self.sadtalker_initialized:
            return {"success": False, "error": "SadTalker服务未初始化"}
        
        try:
            # 使用SadTalker生成视频
            start_time = time.time()
            logger.info(f"开始SadTalker视频生成，图像: {image_path}")
            
            # 生成视频 - 使用异步版本
            initial_result = await self.sadtalker_service.generate_talking_video_async(
                image_path=image_path,
                audio_path=audio_path,
                enhance=enhance_face,
                pose_style=pose_style,
                expression_scale=expression_scale,
                output_path=output_path,
                use_enhancer=enhance_face,
                preprocess="full"
            )
            
            # 检查是否成功提交到队列
            if not initial_result["success"]:
                return {"success": False, "error": initial_result.get("error", "未知错误")}
            
            # 获取任务ID，并等待任务完成
            task_id = initial_result.get("task_id")
            if not task_id:
                return {"success": False, "error": "未返回任务ID"}
                
            logger.info(f"SadTalker任务已提交，任务ID: {task_id}，等待完成...")
            
            # 轮询任务状态直到完成或失败
            max_wait_time = 600  # 最大等待时间（秒）
            poll_interval = 2    # 轮询间隔（秒）
            wait_time = 0
            
            while wait_time < max_wait_time:
                # 等待一段时间
                await asyncio.sleep(poll_interval)
                wait_time += poll_interval
                
                # 获取任务状态
                task_status = self.sadtalker_service.get_task_status(task_id)
                
                # 日志记录进度
                if "progress" in task_status:
                    progress = task_status.get("progress", 0)
                    if progress % 20 == 0 and progress > 0:
                        logger.info(f"SadTalker任务 {task_id} 进度: {progress}%")
                
                # 检查是否完成或失败
                status = task_status.get("status", "")
                
                if status == "completed":
                    # 任务成功完成
                    result = task_status.get("result", {})
                    
                    end_time = time.time()
                    duration = end_time - start_time
                    logger.info(f"SadTalker视频生成完成，耗时: {duration:.2f}秒")
                    
                    # 返回完整的结果信息
                    return result
                    
                elif status == "failed":
                    # 任务失败
                    error_message = task_status.get("error", "未知错误")
                    logger.error(f"SadTalker任务失败: {error_message}")
                    return {"success": False, "error": f"SadTalker生成失败: {error_message}"}
            
            # 超时处理
            logger.error(f"SadTalker任务超时 ({max_wait_time}秒)")
            return {
                "success": False,
                "error": f"SadTalker任务超时 ({max_wait_time}秒)"
            }
            
        except Exception as e:
            logger.error(f"SadTalker视频生成错误: {str(e)}")
            logger.error(traceback.format_exc())
            return {
                "success": False,
                "error": f"SadTalker生成错误: {str(e)}"
            }

# 单例模式
_integrated_digital_human_service = None

def get_integrated_digital_human_service() -> IntegratedDigitalHumanService:
    """获取集成数字人服务实例（单例模式）"""
    global _integrated_digital_human_service
    if _integrated_digital_human_service is None:
        _integrated_digital_human_service = IntegratedDigitalHumanService()
    return _integrated_digital_human_service 