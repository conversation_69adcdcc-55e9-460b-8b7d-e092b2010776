#!/usr/bin/env python3
"""
测试Ollama服务连接和模型功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.ollama_service import ollama_service

def test_ollama_connection():
    """测试Ollama连接"""
    print("🔍 测试Ollama服务连接...")
    
    if ollama_service.available:
        print("✅ Ollama服务可用")
        print(f"📍 服务地址: {ollama_service.base_url}")
        print(f"🤖 最佳模型: {ollama_service.best_model}")
    else:
        print("❌ Ollama服务不可用")
        print("💡 请确保Ollama服务已启动: ollama serve")
        return False
    
    return True

def test_list_models():
    """测试获取模型列表"""
    print("\n📋 获取可用模型列表...")
    
    models = ollama_service.list_models()
    if models:
        print(f"✅ 找到 {len(models)} 个模型:")
        for model in models:
            name = model.get('name', 'Unknown')
            size = model.get('size', 0)
            size_gb = size / (1024**3) if size > 0 else 0
            print(f"  🤖 {name} ({size_gb:.1f} GB)")
    else:
        print("❌ 未找到可用模型")
        return False
    
    return True

def test_text_generation():
    """测试文本生成"""
    print(f"\n🧠 测试文本生成 (使用模型: {ollama_service.best_model})...")
    
    test_prompt = "请简要介绍人工智能技术的发展现状。"
    
    try:
        response = ollama_service.generate_text(
            model=ollama_service.best_model,
            prompt=test_prompt,
            temperature=0.7,
            max_tokens=200
        )
        
        if response:
            print("✅ 文本生成成功:")
            print(f"📝 {response[:200]}...")
            return True
        else:
            print("❌ 文本生成失败")
            return False
            
    except Exception as e:
        print(f"❌ 文本生成异常: {e}")
        return False

def test_opinion_analysis():
    """测试舆情分析"""
    print(f"\n📊 测试舆情分析 (使用模型: {ollama_service.best_model})...")
    
    test_text = """
    人工智能技术在各行业加速应用，为社会带来深刻变革。
    AI技术正在医疗、教育、金融等多个领域快速发展，
    提升了工作效率，改善了用户体验。
    """
    
    try:
        result = ollama_service.analyze_opinion(test_text)
        
        print("✅ 舆情分析成功:")
        print(f"  😊 情感倾向: {result.get('sentiment', 'unknown')}")
        print(f"  🔑 关键词: {result.get('keywords', [])}")
        print(f"  📄 摘要: {result.get('summary', 'N/A')[:100]}...")
        print(f"  🎯 置信度: {result.get('confidence', 0):.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 舆情分析异常: {e}")
        return False

def test_report_generation():
    """测试简报生成"""
    print(f"\n📰 测试简报生成 (使用模型: {ollama_service.best_model})...")
    
    test_data = [
        {
            'title': '人工智能技术在各行业加速应用',
            'content': 'AI技术正在医疗、教育、金融等多个领域快速发展，为社会带来深刻变革...',
            'source': '科技日报',
            'publish_time': '2025-07-28 10:00:00',
            'sentiment': 'positive'
        },
        {
            'title': '数字化转型成为企业发展新趋势',
            'content': '越来越多的企业开始重视数字化转型，通过技术创新提升竞争力...',
            'source': '经济观察报',
            'publish_time': '2025-07-28 09:30:00',
            'sentiment': 'positive'
        }
    ]
    
    try:
        report = ollama_service.generate_report(test_data, "standard")
        
        if report and len(report) > 100:
            print("✅ 简报生成成功:")
            print(f"📄 简报长度: {len(report)} 字符")
            print(f"📝 简报预览:\n{report[:300]}...")
            return True
        else:
            print("❌ 简报生成失败或内容过短")
            return False
            
    except Exception as e:
        print(f"❌ 简报生成异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 Ollama服务测试开始\n")
    
    tests = [
        ("连接测试", test_ollama_connection),
        ("模型列表", test_list_models),
        ("文本生成", test_text_generation),
        ("舆情分析", test_opinion_analysis),
        ("简报生成", test_report_generation)
    ]
    
    passed = 0
    total = len(tests)
    
    for name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"⚠️  {name} 测试失败")
        except Exception as e:
            print(f"❌ {name} 测试异常: {e}")
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！Ollama服务工作正常")
    elif passed > 0:
        print("⚠️  部分测试通过，请检查失败的功能")
    else:
        print("❌ 所有测试失败，请检查Ollama服务状态")
        print("💡 启动命令: ollama serve")

if __name__ == "__main__":
    main()
