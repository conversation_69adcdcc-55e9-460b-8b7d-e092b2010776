<template>
  <div class="profile-container">
    <a-row :gutter="24">
      <a-col :span="8">
        <!-- 个人基本信息卡片 -->
        <a-card :bordered="false" class="profile-card">
          <div class="profile-avatar-container">
            <a-avatar :size="104" :src="userInfo.avatar || '/avatar-default.png'" />
            <div class="profile-avatar-upload">
              <a-upload
                name="avatar"
                :show-upload-list="false"
                :before-upload="beforeAvatarUpload"
                @change="handleAvatarChange"
              >
                <a-button type="primary" shape="circle" size="small">
                  <template #icon><camera-outlined /></template>
                </a-button>
              </a-upload>
            </div>
          </div>
          
          <div class="profile-info">
            <h2 class="profile-name">{{ userInfo.name }}</h2>
            <p class="profile-role">{{ userInfo.roleName }}</p>
          </div>
          
          <a-divider />
          
          <a-descriptions :column="1" size="small" class="profile-details">
            <a-descriptions-item label="用户名">{{ userInfo.username }}</a-descriptions-item>
            <a-descriptions-item label="邮箱">{{ userInfo.email }}</a-descriptions-item>
            <a-descriptions-item label="手机">{{ userInfo.phone }}</a-descriptions-item>
            <a-descriptions-item label="部门">{{ userInfo.department }}</a-descriptions-item>
            <a-descriptions-item label="最后登录">{{ userInfo.lastLogin }}</a-descriptions-item>
          </a-descriptions>
        </a-card>
        
        <!-- 标签卡片 -->
        <a-card title="标签" :bordered="false" class="mt-4 tags-card">
          <template v-for="tag in userInfo.tags" :key="tag">
            <a-tag class="tag-item">{{ tag }}</a-tag>
          </template>
          <a-tag
            v-if="inputVisible"
            class="site-tag-plus"
          >
            <a-input
              ref="inputRef"
              v-model:value="inputValue"
              type="text"
              size="small"
              :style="{ width: '78px' }"
              @blur="handleInputConfirm"
              @keyup.enter="handleInputConfirm"
            />
          </a-tag>
          <a-tag v-else class="site-tag-plus" @click="showInput">
            <plus-outlined /> 新标签
          </a-tag>
        </a-card>
      </a-col>
      
      <a-col :span="16">
        <!-- 选项卡 -->
        <a-card :bordered="false">
          <a-tabs v-model:activeKey="activeTab">
            <a-tab-pane key="basic" tab="基本设置">
              <!-- 表单 -->
              <a-form
                :model="profileForm"
                :rules="rules"
                ref="profileFormRef"
                :label-col="{ span: 4 }"
                :wrapper-col="{ span: 16 }"
              >
                <a-form-item label="昵称" name="name">
                  <a-input v-model:value="profileForm.name" />
                </a-form-item>
                
                <a-form-item label="邮箱" name="email">
                  <a-input v-model:value="profileForm.email" />
                </a-form-item>
                
                <a-form-item label="手机号" name="phone">
                  <a-input v-model:value="profileForm.phone" />
                </a-form-item>
                
                <a-form-item label="部门" name="department">
                  <a-input v-model:value="profileForm.department" />
                </a-form-item>
                
                <a-form-item label="个人简介" name="bio">
                  <a-textarea v-model:value="profileForm.bio" :rows="4" />
                </a-form-item>
                
                <a-form-item :wrapper-col="{ span: 16, offset: 4 }">
                  <a-button type="primary" @click="handleSubmitProfile">保存更改</a-button>
                </a-form-item>
              </a-form>
            </a-tab-pane>
            
            <a-tab-pane key="security" tab="安全设置">
              <a-list item-layout="horizontal" :data-source="securityList">
                <template #renderItem="{ item }">
                  <a-list-item>
                    <a-list-item-meta>
                      <template #title>{{ item.title }}</template>
                      <template #description>{{ item.description }}</template>
                    </a-list-item-meta>
                    <template #actions>
                      <a @click="() => item.action()">{{ item.actionText }}</a>
                    </template>
                  </a-list-item>
                </template>
              </a-list>
            </a-tab-pane>
            
            <a-tab-pane key="activity" tab="最近活动">
              <a-timeline>
                <a-timeline-item v-for="(activity, index) in activities" :key="index" :color="activity.color">
                  <div class="activity-time">{{ activity.time }}</div>
                  <div class="activity-content">{{ activity.content }}</div>
                </a-timeline-item>
              </a-timeline>
            </a-tab-pane>
          </a-tabs>
        </a-card>
      </a-col>
    </a-row>
    
    <!-- 修改密码模态框 -->
    <a-modal
      v-model:visible="passwordModalVisible"
      title="修改密码"
      @ok="handlePasswordChange"
      :confirmLoading="passwordLoading"
    >
      <a-form
        :model="passwordForm"
        :rules="passwordRules"
        ref="passwordFormRef"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-form-item label="旧密码" name="oldPassword">
          <a-input-password v-model:value="passwordForm.oldPassword" />
        </a-form-item>
        
        <a-form-item label="新密码" name="newPassword">
          <a-input-password v-model:value="passwordForm.newPassword" />
        </a-form-item>
        
        <a-form-item label="确认新密码" name="confirmPassword">
          <a-input-password v-model:value="passwordForm.confirmPassword" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script>
import { defineComponent, reactive, ref, onMounted, nextTick } from 'vue';
import { message } from 'ant-design-vue';
import { CameraOutlined, PlusOutlined } from '@ant-design/icons-vue';

export default defineComponent({
  name: 'Profile',
  components: {
    CameraOutlined,
    PlusOutlined
  },
  setup() {
    // 用户信息
    const userInfo = reactive({
      id: 1,
      username: 'admin',
      name: '管理员',
      email: '<EMAIL>',
      phone: '13800138000',
      department: '技术部',
      roleName: '超级管理员',
      lastLogin: '2023-09-21 14:32:10',
      avatar: '',
      tags: ['技术', '管理', '产品']
    });
    
    // 个人资料表单
    const profileFormRef = ref(null);
    const profileForm = reactive({
      name: userInfo.name,
      email: userInfo.email,
      phone: userInfo.phone,
      department: userInfo.department,
      bio: '前端开发工程师，负责系统的前端架构设计和开发。'
    });
    
    // 表单验证规则
    const rules = {
      name: [
        { required: true, message: '请输入昵称', trigger: 'blur' },
        { min: 2, max: 20, message: '昵称长度应在2-20个字符之间', trigger: 'blur' }
      ],
      email: [
        { required: true, message: '请输入邮箱', trigger: 'blur' },
        { type: 'email', message: '请输入有效的邮箱地址', trigger: 'blur' }
      ],
      phone: [
        { required: true, message: '请输入手机号', trigger: 'blur' },
        { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号', trigger: 'blur' }
      ]
    };
    
    // 活动标签页
    const activeTab = ref('basic');
    
    // 标签相关
    const inputVisible = ref(false);
    const inputValue = ref('');
    const inputRef = ref(null);
    
    // 显示标签输入框
    const showInput = () => {
      inputVisible.value = true;
      nextTick(() => {
        inputRef.value?.focus();
      });
    };
    
    // 处理标签输入确认
    const handleInputConfirm = () => {
      if (inputValue.value && !userInfo.tags.includes(inputValue.value)) {
        userInfo.tags.push(inputValue.value);
      }
      inputVisible.value = false;
      inputValue.value = '';
    };
    
    // 处理个人资料提交
    const handleSubmitProfile = () => {
      profileFormRef.value.validate().then(() => {
        // 更新用户信息
        userInfo.name = profileForm.name;
        userInfo.email = profileForm.email;
        userInfo.phone = profileForm.phone;
        userInfo.department = profileForm.department;
        
        message.success('个人资料更新成功');
      }).catch(error => {
        console.log('验证失败:', error);
      });
    };
    
    // 处理头像上传前的校验
    const beforeAvatarUpload = (file) => {
      const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
      if (!isJpgOrPng) {
        message.error('只能上传JPG或PNG格式的图片!');
      }
      const isLt2M = file.size / 1024 / 1024 < 2;
      if (!isLt2M) {
        message.error('图片大小不能超过2MB!');
      }
      return isJpgOrPng && isLt2M;
    };
    
    // 处理头像变更
    const handleAvatarChange = (info) => {
      if (info.file.status === 'uploading') {
        return;
      }
      if (info.file.status === 'done') {
        // 获取上传后的URL
        // 实际项目中这里应该从响应中获取
        userInfo.avatar = URL.createObjectURL(info.file.originFileObj);
        message.success('头像上传成功');
      } else if (info.file.status === 'error') {
        message.error('头像上传失败');
      }
    };
    
    // 安全设置列表
    const passwordModalVisible = ref(false);
    const passwordLoading = ref(false);
    const passwordFormRef = ref(null);
    const passwordForm = reactive({
      oldPassword: '',
      newPassword: '',
      confirmPassword: ''
    });
    
    // 密码验证规则
    const passwordRules = {
      oldPassword: [
        { required: true, message: '请输入旧密码', trigger: 'blur' }
      ],
      newPassword: [
        { required: true, message: '请输入新密码', trigger: 'blur' },
        { min: 6, max: 20, message: '密码长度应在6-20个字符之间', trigger: 'blur' }
      ],
      confirmPassword: [
        { required: true, message: '请确认新密码', trigger: 'blur' },
        {
          validator: (rule, value) => {
            if (value !== passwordForm.newPassword) {
              return Promise.reject('两次输入的密码不一致');
            }
            return Promise.resolve();
          },
          trigger: 'blur'
        }
      ]
    };
    
    // 处理密码修改
    const handlePasswordChange = () => {
      passwordFormRef.value.validate().then(() => {
        passwordLoading.value = true;
        
        // 模拟API请求
        setTimeout(() => {
          passwordLoading.value = false;
          passwordModalVisible.value = false;
          message.success('密码修改成功');
          
          // 清空表单
          passwordForm.oldPassword = '';
          passwordForm.newPassword = '';
          passwordForm.confirmPassword = '';
        }, 1000);
      }).catch(error => {
        console.log('验证失败:', error);
      });
    };
    
    // 安全设置列表
    const securityList = [
      {
        title: '账户密码',
        description: '当前密码强度：中',
        actionText: '修改',
        action: () => {
          passwordModalVisible.value = true;
        }
      },
      {
        title: '绑定手机',
        description: userInfo.phone ? `已绑定手机：${userInfo.phone}` : '未绑定手机',
        actionText: userInfo.phone ? '更换' : '绑定',
        action: () => {
          message.info('功能开发中');
        }
      },
      {
        title: '双因素认证',
        description: '未开启双因素认证',
        actionText: '开启',
        action: () => {
          message.info('功能开发中');
        }
      }
    ];
    
    // 活动列表
    const activities = [
      {
        time: '2023-09-21 14:32:10',
        content: '登录系统',
        color: 'green'
      },
      {
        time: '2023-09-20 16:12:35',
        content: '修改了用户"张三"的权限',
        color: 'blue'
      },
      {
        time: '2023-09-19 10:45:22',
        content: '创建了新角色"测试人员"',
        color: 'blue'
      },
      {
        time: '2023-09-18 09:30:45',
        content: '更新了系统配置',
        color: 'orange'
      },
      {
        time: '2023-09-17 15:22:08',
        content: '添加了新用户"李四"',
        color: 'blue'
      }
    ];
    
    // 组件挂载
    onMounted(() => {
      // 从本地存储或API获取用户信息
      const storedUserInfo = localStorage.getItem('userInfo');
      if (storedUserInfo) {
        const parsedInfo = JSON.parse(storedUserInfo);
        userInfo.id = parsedInfo.id;
        userInfo.username = parsedInfo.username;
        userInfo.name = parsedInfo.name;
        userInfo.avatar = parsedInfo.avatar;
        
        // 更新表单
        profileForm.name = parsedInfo.name;
      }
    });
    
    return {
      userInfo,
      profileForm,
      profileFormRef,
      rules,
      activeTab,
      inputVisible,
      inputValue,
      inputRef,
      passwordModalVisible,
      passwordLoading,
      passwordForm,
      passwordFormRef,
      passwordRules,
      securityList,
      activities,
      showInput,
      handleInputConfirm,
      handleSubmitProfile,
      beforeAvatarUpload,
      handleAvatarChange,
      handlePasswordChange
    };
  }
});
</script>

<style lang="less" scoped>
.profile-container {
  .profile-card {
    text-align: center;
    
    .profile-avatar-container {
      position: relative;
      display: inline-block;
      margin-bottom: 16px;
      
      .profile-avatar-upload {
        position: absolute;
        right: 0;
        bottom: 0;
      }
    }
    
    .profile-info {
      .profile-name {
        margin: 0 0 4px;
        font-size: 20px;
        font-weight: 500;
      }
      
      .profile-role {
        margin: 0;
        color: rgba(0, 0, 0, 0.45);
      }
    }
    
    .profile-details {
      text-align: left;
    }
  }
  
  .tags-card {
    .tag-item {
      margin-bottom: 8px;
    }
  }
  
  .activity-time {
    font-size: 12px;
    color: rgba(0, 0, 0, 0.45);
    margin-bottom: 4px;
  }
  
  .activity-content {
    font-size: 14px;
    margin-bottom: 8px;
  }
}
</style> 