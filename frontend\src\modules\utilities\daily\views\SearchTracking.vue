<template>
  <div class="search-tracking">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1>检索追踪预警</h1>
      <p>保存检索方案，设置预警条件，及时识别舆情风险</p>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <el-row :gutter="24">
        <!-- 左侧：检索方案管理 -->
        <el-col :xs="24" :lg="16">
          <el-card header="检索方案管理" shadow="never">
            <!-- 操作按钮 -->
            <div class="actions">
              <el-button type="primary" @click="showCreateModal = true">
                <el-icon><Plus /></el-icon>
                创建检索方案
              </el-button>
              <el-button @click="importScheme">
                <el-icon><Upload /></el-icon>
                导入方案
              </el-button>
            </div>

            <!-- 方案列表 -->
            <div class="scheme-list">
              <div v-loading="loading">
                <div v-for="item in searchSchemes" :key="item.id" class="scheme-item">
                  <div class="scheme-header">
                    <div class="scheme-title">
                      {{ item.name }}
                      <el-tag :type="getStatusType(item.status)" size="small">
                        {{ getStatusText(item.status) }}
                      </el-tag>
                      <el-tag v-if="item.alertEnabled" type="warning" size="small">
                        <el-icon><Bell /></el-icon>
                        预警开启
                      </el-tag>
                    </div>
                    <div class="scheme-actions">
                      <el-button link @click="viewResults(item)">查看结果</el-button>
                      <el-button link @click="editScheme(item)">编辑</el-button>
                      <el-button link @click="configureAlert(item)">预警设置</el-button>
                      <el-popconfirm
                        title="确定要删除这个方案吗？"
                        @confirm="deleteScheme(item.id)"
                      >
                        <template #reference>
                          <el-button link type="danger">删除</el-button>
                        </template>
                      </el-popconfirm>
                    </div>
                  </div>
                  <div class="scheme-description">
                    <p><strong>关键词：</strong>{{ item.keywords.join(', ') }}</p>
                    <p><strong>数据源：</strong>{{ item.sources.join(', ') }}</p>
                    <div class="scheme-meta">
                      <span><el-icon><Calendar /></el-icon> 创建时间：{{ item.createdAt }}</span>
                      <span><el-icon><Clock /></el-icon> 最后执行：{{ item.lastExecution }}</span>
                      <span><el-icon><Document /></el-icon> 结果数：{{ item.resultCount }}</span>
                    </div>
                  </div>
                </div>

                <el-empty v-if="!searchSchemes.length && !loading" description="暂无检索方案" />
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 右侧：预警统计和最近预警 -->
        <el-col :xs="24" :lg="8">
          <!-- 统计卡片 -->
          <el-card header="预警统计" shadow="never" class="stats-card">
            <el-row :gutter="16">
              <el-col :span="12">
                <div class="stat-item">
                  <div class="stat-value" style="color: #3f8600;">{{ activeSchemesCount }}</div>
                  <div class="stat-title">活跃方案</div>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="stat-item">
                  <div class="stat-value" style="color: #cf1322;">{{ todayAlertsCount }}</div>
                  <div class="stat-title">今日预警</div>
                </div>
              </el-col>
            </el-row>
          </el-card>

          <!-- 最近预警 -->
          <el-card header="最近预警" shadow="never" class="recent-alerts">
            <div class="alert-list">
              <div v-for="item in recentAlerts" :key="item.id" class="alert-item">
                <div class="alert-header">
                  <div class="alert-title">
                    <a @click="viewAlert(item)">{{ item.title }}</a>
                    <el-tag :type="getAlertLevelType(item.level)" size="small">
                      {{ getAlertLevelText(item.level) }}
                    </el-tag>
                  </div>
                  <el-button link @click="viewAlert(item)">查看</el-button>
                </div>
                <div class="alert-meta">
                  <div>{{ item.description }}</div>
                  <div class="alert-time">{{ item.triggeredAt }}</div>
                </div>
              </div>

              <el-empty v-if="!recentAlerts.length" description="暂无预警记录" />
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 创建检索方案模态框 -->
    <el-dialog
      v-model="showCreateModal"
      title="创建检索方案"
      width="800px"
      @close="resetForm"
    >
      <div class="create-form">
        <p>创建检索方案功能开发中...</p>
      </div>
      <template #footer>
        <el-button @click="resetForm">取消</el-button>
        <el-button type="primary" @click="createScheme">创建</el-button>
      </template>
    </el-dialog>


    <!-- 预警配置模态框 -->
    <el-dialog
      v-model="showAlertModal"
      title="预警配置"
      width="600px"
      @close="resetAlertForm"
    >
      <div class="alert-form">
        <p>预警配置功能开发中...</p>
      </div>
      <template #footer>
        <el-button @click="resetAlertForm">取消</el-button>
        <el-button type="primary" @click="saveAlertConfig">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, computed, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import dayjs from 'dayjs';
import {
  Plus,
  Upload,
  Bell,
  Calendar,
  Clock,
  Document
} from '@element-plus/icons-vue';

export default defineComponent({
  name: 'SearchTracking',
  components: {
    Plus,
    Upload,
    Bell,
    Calendar,
    Clock,
    Document
  },
  setup() {
    const loading = ref(false);
    const showCreateModal = ref(false);
    const showAlertModal = ref(false);
    const formRef = ref();
    const alertFormRef = ref();

    // 检索方案表单
    const schemeForm = reactive({
      name: '',
      description: '',
      keywords: [],
      sources: ['news', 'social'],
      regions: ['china'],
      language: 'zh'
    });

    // 预警配置表单
    const alertForm = reactive({
      enabled: false,
      triggerType: 'time',
      frequency: 'daily',
      threshold: 100,
      changeRate: 50,
      level: 'medium',
      recipients: []
    });

    // 表单验证规则
    const formRules = {
      name: [{ required: true, message: '请输入方案名称' }],
      keywords: [{ required: true, message: '请添加至少一个关键词' }],
      sources: [{ required: true, message: '请选择至少一个数据源' }],
      regions: [{ required: true, message: '请选择地域范围' }]
    };

    const alertFormRules = {
      frequency: [{ required: true, message: '请选择检查频率' }],
      threshold: [{ required: true, message: '请设置阈值' }],
      changeRate: [{ required: true, message: '请设置变化比例' }],
      level: [{ required: true, message: '请选择预警级别' }],
      recipients: [{ required: true, message: '请添加接收人员' }]
    };

    // 模拟数据
    const searchSchemes = ref([
      {
        id: 1,
        name: '中美关系舆情监测',
        description: '监测中美关系相关的舆情动态',
        keywords: ['中美关系', '贸易战', '外交'],
        sources: ['新闻媒体', '社交媒体'],
        regions: ['中国', '美国'],
        status: 'active',
        alertEnabled: true,
        createdAt: '2024-01-01',
        lastExecution: '2024-01-07 09:00',
        resultCount: 1250
      },
      {
        id: 2,
        name: '东南亚政治动态',
        description: '跟踪东南亚地区的政治变化',
        keywords: ['东南亚', '政治', '选举'],
        sources: ['新闻媒体', '政府网站'],
        regions: ['亚洲'],
        status: 'active',
        alertEnabled: false,
        createdAt: '2024-01-02',
        lastExecution: '2024-01-07 08:30',
        resultCount: 856
      }
    ]);

    const recentAlerts = ref([
      {
        id: 1,
        title: '中美关系舆情异常增长',
        description: '相关舆情数量较昨日增长150%',
        level: 'high',
        triggeredAt: '2024-01-07 10:30',
        schemeId: 1
      },
      {
        id: 2,
        title: '东南亚政治话题热度上升',
        description: '检测到相关讨论量显著增加',
        level: 'medium',
        triggeredAt: '2024-01-07 09:15',
        schemeId: 2
      }
    ]);

    // 计算属性
    const activeSchemesCount = computed(() => {
      return searchSchemes.value.filter(scheme => scheme.status === 'active').length;
    });

    const todayAlertsCount = computed(() => {
      const today = dayjs().format('YYYY-MM-DD');
      return recentAlerts.value.filter(alert => 
        alert.triggeredAt.startsWith(today)
      ).length;
    });

    // 方法
    const getStatusColor = (status) => {
      const colors = {
        active: 'green',
        paused: 'orange',
        stopped: 'red'
      };
      return colors[status] || 'default';
    };

    const getStatusType = (status) => {
      const types = {
        active: 'success',
        paused: 'warning',
        stopped: 'danger'
      };
      return types[status] || 'info';
    };

    const getStatusText = (status) => {
      const texts = {
        active: '运行中',
        paused: '已暂停',
        stopped: '已停止'
      };
      return texts[status] || '未知';
    };

    const getAlertLevelColor = (level) => {
      const colors = {
        low: 'blue',
        medium: 'orange',
        high: 'red',
        critical: 'purple'
      };
      return colors[level] || 'default';
    };

    const getAlertLevelType = (level) => {
      const types = {
        low: 'info',
        medium: 'warning',
        high: 'danger',
        critical: 'danger'
      };
      return types[level] || 'info';
    };

    const getAlertLevelText = (level) => {
      const texts = {
        low: '低',
        medium: '中',
        high: '高',
        critical: '紧急'
      };
      return texts[level] || '未知';
    };

    const createScheme = async () => {
      try {
        await formRef.value.validate();
        
        const newScheme = {
          id: Date.now(),
          name: schemeForm.name,
          description: schemeForm.description,
          keywords: schemeForm.keywords,
          sources: schemeForm.sources,
          regions: schemeForm.regions,
          status: 'active',
          alertEnabled: false,
          createdAt: dayjs().format('YYYY-MM-DD'),
          lastExecution: '未执行',
          resultCount: 0
        };

        searchSchemes.value.unshift(newScheme);
        ElMessage.success('检索方案创建成功！');
        showCreateModal.value = false;
        resetForm();
      } catch (error) {
        console.error('表单验证失败:', error);
      }
    };

    const resetForm = () => {
      Object.assign(schemeForm, {
        name: '',
        description: '',
        keywords: [],
        sources: ['news', 'social'],
        regions: ['china'],
        language: 'zh'
      });
    };

    const resetAlertForm = () => {
      Object.assign(alertForm, {
        enabled: false,
        triggerType: 'time',
        frequency: 'daily',
        threshold: 100,
        changeRate: 50,
        level: 'medium',
        recipients: []
      });
    };

    const editScheme = (scheme) => {
      ElMessage.info('编辑功能开发中');
    };

    const deleteScheme = (schemeId) => {
      const index = searchSchemes.value.findIndex(scheme => scheme.id === schemeId);
      if (index > -1) {
        searchSchemes.value.splice(index, 1);
        ElMessage.success('方案删除成功');
      }
    };

    const configureAlert = (scheme) => {
      showAlertModal.value = true;
      // 这里应该加载该方案的预警配置
    };

    const saveAlertConfig = async () => {
      if (alertForm.enabled) {
        try {
          await alertFormRef.value.validate();
        } catch (error) {
          return;
        }
      }
      
      ElMessage.success('预警配置保存成功！');
      showAlertModal.value = false;
      resetAlertForm();
    };

    const viewResults = (scheme) => {
      ElMessage.info('查看结果功能开发中');
    };

    const viewAlert = (alert) => {
      ElMessage.info('查看预警详情功能开发中');
    };

    const importScheme = () => {
      ElMessage.info('导入方案功能开发中');
    };

    onMounted(() => {
      // 初始化数据
    });

    return {
      loading,
      showCreateModal,
      showAlertModal,
      formRef,
      alertFormRef,
      schemeForm,
      alertForm,
      formRules,
      alertFormRules,
      searchSchemes,
      recentAlerts,
      activeSchemesCount,
      todayAlertsCount,
      getStatusColor,
      getStatusType,
      getStatusText,
      getAlertLevelColor,
      getAlertLevelType,
      getAlertLevelText,
      createScheme,
      resetForm,
      resetAlertForm,
      editScheme,
      deleteScheme,
      configureAlert,
      saveAlertConfig,
      viewResults,
      viewAlert,
      importScheme
    };
  }
});
</script>

<style scoped>
.search-tracking {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 32px;
  margin-bottom: 24px;
  color: white;
  text-align: center;
}

.page-header h1 {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 8px;
  color: white;
}

.page-header p {
  font-size: 1.1rem;
  opacity: 0.9;
  margin: 0;
}

.actions {
  margin-bottom: 24px;
  display: flex;
  gap: 12px;
}

.scheme-item {
  padding: 16px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  margin-bottom: 16px;
  background: #fff;
}

.scheme-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.scheme-actions {
  display: flex;
  gap: 8px;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 4px;
}

.stat-title {
  font-size: 14px;
  color: #666;
}

.alert-list {
  max-height: 400px;
  overflow-y: auto;
}

.alert-item {
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.alert-item:last-child {
  border-bottom: none;
}

.alert-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.alert-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.alert-title a {
  color: #1890ff;
  text-decoration: none;
}

.alert-title a:hover {
  text-decoration: underline;
}

.alert-meta {
  font-size: 12px;
  color: #999;
}

.alert-time {
  margin-top: 4px;
}

.scheme-title {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.scheme-description p {
  margin-bottom: 4px;
  color: #666;
}

.scheme-meta {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #999;
  margin-top: 8px;
}

.scheme-meta span {
  display: flex;
  align-items: center;
  gap: 4px;
}

.stats-card {
  margin-bottom: 24px;
}

.recent-alerts {
  max-height: 400px;
  overflow-y: auto;
}

.alert-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.alert-meta {
  color: #666;
}

.alert-time {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

@media (max-width: 768px) {
  .search-tracking {
    padding: 16px;
  }
  
  .page-header {
    padding: 24px 16px;
  }
  
  .actions {
    flex-direction: column;
  }
  
  .scheme-meta {
    flex-direction: column;
    gap: 8px;
  }
}
</style>
