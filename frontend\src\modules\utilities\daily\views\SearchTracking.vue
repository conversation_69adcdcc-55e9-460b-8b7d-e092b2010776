<template>
  <div class="search-tracking">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1>检索追踪预警</h1>
      <p>保存检索方案，设置预警条件，及时识别舆情风险</p>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <el-row :gutter="24">
        <!-- 左侧：检索方案管理 -->
        <el-col :xs="24" :lg="16">
          <el-card header="检索方案管理" shadow="never">
            <!-- 操作按钮 -->
            <div class="actions">
              <el-button type="primary" @click="showCreateModal = true">
                <el-icon><Plus /></el-icon>
                创建检索方案
              </el-button>
              <el-button @click="importScheme">
                <el-icon><Upload /></el-icon>
                导入方案
              </el-button>
            </div>

            <!-- 方案列表 -->
            <div class="scheme-list">
              <div v-loading="loading">
                <div v-for="item in searchSchemes" :key="item.id" class="scheme-item">
                  <div class="scheme-header">
                    <div class="scheme-title">
                      {{ item.name }}
                      <el-tag :type="getStatusType(item.status)" size="small">
                        {{ getStatusText(item.status) }}
                      </el-tag>
                      <el-tag v-if="item.alertEnabled" type="warning" size="small">
                        <el-icon><Bell /></el-icon>
                        预警开启
                      </el-tag>
                    </div>
                    <div class="scheme-actions">
                      <el-button link @click="viewResults(item)">查看结果</el-button>
                      <el-button link @click="editScheme(item)">编辑</el-button>
                      <el-button link @click="configureAlert(item)">预警设置</el-button>
                      <el-popconfirm
                        title="确定要删除这个方案吗？"
                        @confirm="deleteScheme(item.id)"
                      >
                        <template #reference>
                          <el-button link type="danger">删除</el-button>
                        </template>
                      </el-popconfirm>
                    </div>
                  </div>
                  <div class="scheme-description">
                    <p><strong>关键词：</strong>{{ item.keywords.join(', ') }}</p>
                    <p><strong>数据源：</strong>{{ item.sources.join(', ') }}</p>
                    <div class="scheme-meta">
                      <span><el-icon><Calendar /></el-icon> 创建时间：{{ item.createdAt }}</span>
                      <span><el-icon><Clock /></el-icon> 最后执行：{{ item.lastExecution }}</span>
                      <span><el-icon><Document /></el-icon> 结果数：{{ item.resultCount }}</span>
                    </div>
                  </div>
                </div>

                <el-empty v-if="!searchSchemes.length && !loading" description="暂无检索方案" />
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 右侧：预警统计和最近预警 -->
        <el-col :xs="24" :lg="8">
          <!-- 统计卡片 -->
          <el-card header="预警统计" shadow="never" class="stats-card">
            <el-row :gutter="16">
              <el-col :span="12">
                <div class="stat-item">
                  <div class="stat-value" style="color: #3f8600;">{{ activeSchemesCount }}</div>
                  <div class="stat-title">活跃方案</div>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="stat-item">
                  <div class="stat-value" style="color: #cf1322;">{{ todayAlertsCount }}</div>
                  <div class="stat-title">今日预警</div>
                </div>
              </el-col>
            </el-row>
          </el-card>

          <!-- 最近预警 -->
          <el-card header="最近预警" shadow="never" class="recent-alerts">
            <div class="alert-list">
              <div v-for="item in recentAlerts" :key="item.id" class="alert-item">
                <div class="alert-header">
                  <div class="alert-title">
                    <a @click="viewAlert(item)">{{ item.title }}</a>
                    <el-tag :type="getAlertLevelType(item.level)" size="small">
                      {{ getAlertLevelText(item.level) }}
                    </el-tag>
                  </div>
                  <el-button link @click="viewAlert(item)">查看</el-button>
                </div>
                <div class="alert-meta">
                  <div>{{ item.description }}</div>
                  <div class="alert-time">{{ item.triggeredAt }}</div>
                </div>
              </div>

              <el-empty v-if="!recentAlerts.length" description="暂无预警记录" />
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 创建检索方案模态框 -->
    <el-dialog
      v-model="showCreateModal"
      :title="currentEditingScheme ? '编辑检索方案' : '创建检索方案'"
      width="800px"
      @close="resetForm"
    >
      <el-form
        ref="formRef"
        :model="schemeForm"
        :rules="formRules"
        label-width="100px"
      >
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="方案名称" prop="name">
              <el-input v-model="schemeForm.name" placeholder="请输入方案名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="检索语言" prop="language">
              <el-select v-model="schemeForm.language" placeholder="选择检索语言">
                <el-option label="中文" value="zh" />
                <el-option label="英文" value="en" />
                <el-option label="全部语言" value="all" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="关键词" prop="keywords">
          <el-select
            v-model="schemeForm.keywords"
            multiple
            filterable
            allow-create
            placeholder="输入关键词，按回车添加"
          />
        </el-form-item>

        <el-form-item label="数据源" prop="sources">
          <el-checkbox-group v-model="schemeForm.sources">
            <el-checkbox value="news">新闻媒体</el-checkbox>
            <el-checkbox value="social">社交媒体</el-checkbox>
            <el-checkbox value="forum">论坛博客</el-checkbox>
            <el-checkbox value="government">政府网站</el-checkbox>
            <el-checkbox value="academic">学术期刊</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item label="地域范围" prop="regions">
          <el-select
            v-model="schemeForm.regions"
            multiple
            placeholder="选择地域范围"
          >
            <el-option label="中国" value="china" />
            <el-option label="美国" value="usa" />
            <el-option label="欧洲" value="europe" />
            <el-option label="亚洲" value="asia" />
            <el-option label="全球" value="global" />
          </el-select>
        </el-form-item>

        <el-form-item label="方案描述" prop="description">
          <el-input
            v-model="schemeForm.description"
            type="textarea"
            placeholder="请描述检索方案的目的和要求"
            :rows="3"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="resetForm">取消</el-button>
        <el-button type="primary" @click="createScheme">
          {{ currentEditingScheme ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>


    <!-- 预警配置模态框 -->
    <el-dialog
      v-model="showAlertModal"
      title="预警配置"
      width="600px"
      @close="resetAlertForm"
    >
      <el-form
        ref="alertFormRef"
        :model="alertForm"
        :rules="alertFormRules"
        label-width="100px"
      >
        <el-form-item label="启用预警" prop="enabled">
          <el-switch v-model="alertForm.enabled" />
        </el-form-item>

        <div v-if="alertForm.enabled">
          <el-form-item label="触发条件" prop="triggerType">
            <el-radio-group v-model="alertForm.triggerType">
              <el-radio value="time">定时触发</el-radio>
              <el-radio value="threshold">阈值触发</el-radio>
              <el-radio value="change">变化触发</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item v-if="alertForm.triggerType === 'time'" label="检查频率" prop="frequency">
            <el-select v-model="alertForm.frequency">
              <el-option label="每小时" value="hourly" />
              <el-option label="每日" value="daily" />
              <el-option label="每周" value="weekly" />
            </el-select>
          </el-form-item>

          <el-form-item v-if="alertForm.triggerType === 'threshold'" label="结果数量阈值" prop="threshold">
            <el-input-number
              v-model="alertForm.threshold"
              :min="1"
              placeholder="当结果数量超过此值时触发预警"
              style="width: 100%"
            />
          </el-form-item>

          <el-form-item v-if="alertForm.triggerType === 'change'" label="变化比例" prop="changeRate">
            <el-input-number
              v-model="alertForm.changeRate"
              :min="0"
              :max="100"
              placeholder="当结果数量变化超过此比例时触发预警"
              style="width: 100%"
            />
            <span style="margin-left: 8px;">%</span>
          </el-form-item>

          <el-form-item label="预警级别" prop="level">
            <el-select v-model="alertForm.level">
              <el-option label="低" value="low" />
              <el-option label="中" value="medium" />
              <el-option label="高" value="high" />
              <el-option label="紧急" value="critical" />
            </el-select>
          </el-form-item>

          <el-form-item label="接收人员" prop="recipients">
            <el-select
              v-model="alertForm.recipients"
              multiple
              filterable
              allow-create
              placeholder="输入邮箱地址，按回车添加"
            />
          </el-form-item>
        </div>
      </el-form>
      <template #footer>
        <el-button @click="resetAlertForm">取消</el-button>
        <el-button type="primary" @click="saveAlertConfig">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, computed, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import dayjs from 'dayjs';
import {
  Plus,
  Upload,
  Bell,
  Calendar,
  Clock,
  Document
} from '@element-plus/icons-vue';

export default defineComponent({
  name: 'SearchTracking',
  components: {
    Plus,
    Upload,
    Bell,
    Calendar,
    Clock,
    Document
  },
  setup() {
    const loading = ref(false);
    const showCreateModal = ref(false);
    const showAlertModal = ref(false);
    const formRef = ref(null);
    const alertFormRef = ref(null);
    const currentEditingScheme = ref(null);

    // 表单数据
    const schemeForm = ref({
      name: '',
      language: 'zh',
      keywords: [],
      sources: ['news'],
      regions: ['china'],
      description: ''
    });

    // 表单验证规则
    const formRules = {
      name: [
        { required: true, message: '请输入方案名称', trigger: 'blur' }
      ],
      keywords: [
        { required: true, message: '请至少添加一个关键词', trigger: 'change' }
      ],
      sources: [
        { required: true, message: '请至少选择一个数据源', trigger: 'change' }
      ]
    };
    const formRef = ref();
    const alertFormRef = ref();

    // 检索方案表单
    const schemeForm = reactive({
      name: '',
      description: '',
      keywords: [],
      sources: ['news', 'social'],
      regions: ['china'],
      language: 'zh'
    });

    // 预警配置表单
    const alertForm = reactive({
      enabled: false,
      triggerType: 'time',
      frequency: 'daily',
      threshold: 100,
      changeRate: 50,
      level: 'medium',
      recipients: []
    });

    // 表单验证规则
    const formRules = {
      name: [{ required: true, message: '请输入方案名称' }],
      keywords: [{ required: true, message: '请添加至少一个关键词' }],
      sources: [{ required: true, message: '请选择至少一个数据源' }],
      regions: [{ required: true, message: '请选择地域范围' }]
    };

    const alertFormRules = {
      frequency: [{ required: true, message: '请选择检查频率' }],
      threshold: [{ required: true, message: '请设置阈值' }],
      changeRate: [{ required: true, message: '请设置变化比例' }],
      level: [{ required: true, message: '请选择预警级别' }],
      recipients: [{ required: true, message: '请添加接收人员' }]
    };

    // 模拟数据
    const searchSchemes = ref([
      {
        id: 1,
        name: '中美关系舆情监测',
        description: '监测中美关系相关的舆情动态',
        keywords: ['中美关系', '贸易战', '外交'],
        sources: ['新闻媒体', '社交媒体'],
        regions: ['中国', '美国'],
        status: 'active',
        alertEnabled: true,
        createdAt: '2024-01-01',
        lastExecution: '2024-01-07 09:00',
        resultCount: 1250
      },
      {
        id: 2,
        name: '东南亚政治动态',
        description: '跟踪东南亚地区的政治变化',
        keywords: ['东南亚', '政治', '选举'],
        sources: ['新闻媒体', '政府网站'],
        regions: ['亚洲'],
        status: 'active',
        alertEnabled: false,
        createdAt: '2024-01-02',
        lastExecution: '2024-01-07 08:30',
        resultCount: 856
      }
    ]);

    const recentAlerts = ref([
      {
        id: 1,
        title: '中美关系舆情异常增长',
        description: '相关舆情数量较昨日增长150%',
        level: 'high',
        triggeredAt: '2024-01-07 10:30',
        schemeId: 1
      },
      {
        id: 2,
        title: '东南亚政治话题热度上升',
        description: '检测到相关讨论量显著增加',
        level: 'medium',
        triggeredAt: '2024-01-07 09:15',
        schemeId: 2
      }
    ]);

    // 计算属性
    const activeSchemesCount = computed(() => {
      return searchSchemes.value.filter(scheme => scheme.status === 'active').length;
    });

    const todayAlertsCount = computed(() => {
      const today = dayjs().format('YYYY-MM-DD');
      return recentAlerts.value.filter(alert => 
        alert.triggeredAt.startsWith(today)
      ).length;
    });

    // 方法
    const getStatusColor = (status) => {
      const colors = {
        active: 'green',
        paused: 'orange',
        stopped: 'red'
      };
      return colors[status] || 'default';
    };

    const getStatusType = (status) => {
      const types = {
        active: 'success',
        paused: 'warning',
        stopped: 'danger'
      };
      return types[status] || 'info';
    };

    const getStatusText = (status) => {
      const texts = {
        active: '运行中',
        paused: '已暂停',
        stopped: '已停止'
      };
      return texts[status] || '未知';
    };

    const getAlertLevelColor = (level) => {
      const colors = {
        low: 'blue',
        medium: 'orange',
        high: 'red',
        critical: 'purple'
      };
      return colors[level] || 'default';
    };

    const getAlertLevelType = (level) => {
      const types = {
        low: 'info',
        medium: 'warning',
        high: 'danger',
        critical: 'danger'
      };
      return types[level] || 'info';
    };

    const getAlertLevelText = (level) => {
      const texts = {
        low: '低',
        medium: '中',
        high: '高',
        critical: '紧急'
      };
      return texts[level] || '未知';
    };

    const createScheme = async () => {
      try {
        await formRef.value.validate();

        if (currentEditingScheme.value) {
          // 编辑模式
          const index = searchSchemes.value.findIndex(s => s.id === currentEditingScheme.value);
          if (index !== -1) {
            const updatedScheme = {
              ...searchSchemes.value[index],
              name: schemeForm.name,
              description: schemeForm.description,
              keywords: schemeForm.keywords,
              sources: schemeForm.sources.map(source => {
                const sourceMap = {
                  'news': '新闻媒体',
                  'social': '社交媒体',
                  'forum': '论坛博客',
                  'government': '政府网站',
                  'academic': '学术期刊'
                };
                return sourceMap[source] || source;
              }),
              regions: schemeForm.regions.map(region => {
                const regionMap = {
                  'china': '中国',
                  'usa': '美国',
                  'europe': '欧洲',
                  'asia': '亚洲',
                  'global': '全球'
                };
                return regionMap[region] || region;
              }),
              language: schemeForm.language
            };
            searchSchemes.value[index] = updatedScheme;
            ElMessage.success('检索方案更新成功！');
          }
        } else {
          // 创建模式
          const newScheme = {
            id: Date.now(),
            name: schemeForm.name,
            description: schemeForm.description,
            keywords: schemeForm.keywords,
            sources: schemeForm.sources.map(source => {
              const sourceMap = {
                'news': '新闻媒体',
                'social': '社交媒体',
                'forum': '论坛博客',
                'government': '政府网站',
                'academic': '学术期刊'
              };
              return sourceMap[source] || source;
            }),
            regions: schemeForm.regions.map(region => {
              const regionMap = {
                'china': '中国',
                'usa': '美国',
                'europe': '欧洲',
                'asia': '亚洲',
                'global': '全球'
              };
              return regionMap[region] || region;
            }),
            status: 'active',
            alertEnabled: false,
            createdAt: new Date().toISOString().split('T')[0],
            lastExecution: '未执行',
            resultCount: 0
          };
          searchSchemes.value.unshift(newScheme);
          ElMessage.success('检索方案创建成功！');
        }

        showCreateModal.value = false;
        resetForm();
      } catch (error) {
        console.error('表单验证失败:', error);
      }
    };

    const resetForm = () => {
      Object.assign(schemeForm, {
        name: '',
        description: '',
        keywords: [],
        sources: ['news', 'social'],
        regions: ['china'],
        language: 'zh'
      });
      currentEditingScheme.value = null;
    };

    const resetAlertForm = () => {
      Object.assign(alertForm, {
        enabled: false,
        triggerType: 'time',
        frequency: 'daily',
        threshold: 100,
        changeRate: 50,
        level: 'medium',
        recipients: []
      });
    };

    const editScheme = (scheme) => {
      // 填充表单数据
      Object.assign(schemeForm, {
        name: scheme.name,
        description: scheme.description,
        keywords: scheme.keywords,
        sources: scheme.sources.map(source => {
          const sourceMap = {
            '新闻媒体': 'news',
            '社交媒体': 'social',
            '论坛博客': 'forum',
            '政府网站': 'government',
            '学术期刊': 'academic'
          };
          return sourceMap[source] || source;
        }),
        regions: scheme.regions.map(region => {
          const regionMap = {
            '中国': 'china',
            '美国': 'usa',
            '欧洲': 'europe',
            '亚洲': 'asia',
            '全球': 'global'
          };
          return regionMap[region] || region;
        }),
        language: scheme.language || 'zh'
      });

      // 保存当前编辑的方案ID
      currentEditingScheme.value = scheme.id;
      showCreateModal.value = true;
    };

    const deleteScheme = (schemeId) => {
      const index = searchSchemes.value.findIndex(scheme => scheme.id === schemeId);
      if (index > -1) {
        searchSchemes.value.splice(index, 1);
        ElMessage.success('方案删除成功');
      }
    };

    const configureAlert = (scheme) => {
      showAlertModal.value = true;
      // 这里应该加载该方案的预警配置
    };

    const saveAlertConfig = async () => {
      if (alertForm.enabled) {
        try {
          await alertFormRef.value.validate();
        } catch (error) {
          return;
        }
      }
      
      ElMessage.success('预警配置保存成功！');
      showAlertModal.value = false;
      resetAlertForm();
    };

    const viewResults = (scheme) => {
      // 模拟执行检索并显示结果
      ElMessageBox.alert(
        `方案"${scheme.name}"的检索结果：\n\n` +
        `关键词：${scheme.keywords.join(', ')}\n` +
        `数据源：${scheme.sources.join(', ')}\n` +
        `地域范围：${scheme.regions.join(', ')}\n` +
        `结果数量：${scheme.resultCount}条\n` +
        `最后执行：${scheme.lastExecution}\n\n` +
        `详细结果请查看舆情检索页面。`,
        '检索结果',
        {
          confirmButtonText: '前往舆情检索',
          cancelButtonText: '关闭',
          showCancelButton: true,
          type: 'info'
        }
      ).then(() => {
        // 跳转到舆情检索页面
        window.location.href = '#/utilities/daily/opinion-analysis/opinion-search';
      }).catch(() => {
        // 用户取消
      });
    };

    const viewAlert = (alert) => {
      const scheme = searchSchemes.value.find(s => s.id === alert.schemeId);
      const schemeName = scheme ? scheme.name : '未知方案';

      ElMessageBox.alert(
        `预警详情：\n\n` +
        `预警标题：${alert.title}\n` +
        `预警级别：${getAlertLevelText(alert.level)}\n` +
        `触发时间：${alert.triggeredAt}\n` +
        `关联方案：${schemeName}\n` +
        `预警描述：${alert.description}\n\n` +
        `建议：请及时关注相关舆情动态，必要时采取应对措施。`,
        '预警详情',
        {
          confirmButtonText: '查看方案详情',
          cancelButtonText: '关闭',
          showCancelButton: true,
          type: getAlertLevelType(alert.level) === 'danger' ? 'error' : 'warning'
        }
      ).then(() => {
        if (scheme) {
          viewResults(scheme);
        }
      }).catch(() => {
        // 用户取消
      });
    };

    const importScheme = () => {
      // 创建文件输入元素
      const input = document.createElement('input');
      input.type = 'file';
      input.accept = '.json';
      input.onchange = (event) => {
        const file = event.target.files[0];
        if (file) {
          const reader = new FileReader();
          reader.onload = (e) => {
            try {
              const importedSchemes = JSON.parse(e.target.result);
              if (Array.isArray(importedSchemes)) {
                // 批量导入
                importedSchemes.forEach(scheme => {
                  const newScheme = {
                    ...scheme,
                    id: Date.now() + Math.random(), // 确保ID唯一
                    createdAt: new Date().toISOString().split('T')[0],
                    lastExecution: '未执行',
                    resultCount: 0
                  };
                  searchSchemes.value.push(newScheme);
                });
                ElMessage.success(`成功导入 ${importedSchemes.length} 个检索方案！`);
              } else if (typeof importedSchemes === 'object') {
                // 单个导入
                const newScheme = {
                  ...importedSchemes,
                  id: Date.now(),
                  createdAt: new Date().toISOString().split('T')[0],
                  lastExecution: '未执行',
                  resultCount: 0
                };
                searchSchemes.value.push(newScheme);
                ElMessage.success('检索方案导入成功！');
              } else {
                ElMessage.error('导入文件格式不正确！');
              }
            } catch (error) {
              ElMessage.error('导入文件解析失败，请检查文件格式！');
            }
          };
          reader.readAsText(file);
        }
      };
      input.click();
    };

    onMounted(() => {
      // 初始化数据
    });

    return {
      loading,
      showCreateModal,
      showAlertModal,
      formRef,
      alertFormRef,
      currentEditingScheme,
      schemeForm,
      alertForm,
      formRules,
      alertFormRules,
      searchSchemes,
      recentAlerts,
      activeSchemesCount,
      todayAlertsCount,
      getStatusColor,
      getStatusType,
      getStatusText,
      getAlertLevelColor,
      getAlertLevelType,
      getAlertLevelText,
      createScheme,
      resetForm,
      resetAlertForm,
      editScheme,
      deleteScheme,
      configureAlert,
      saveAlertConfig,
      viewResults,
      viewAlert,
      importScheme
    };
  }
});
</script>

<style scoped>
.search-tracking {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 32px;
  margin-bottom: 24px;
  color: white;
  text-align: center;
}

.page-header h1 {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 8px;
  color: white;
}

.page-header p {
  font-size: 1.1rem;
  opacity: 0.9;
  margin: 0;
}

.actions {
  margin-bottom: 24px;
  display: flex;
  gap: 12px;
}

.scheme-item {
  padding: 16px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  margin-bottom: 16px;
  background: #fff;
}

.scheme-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.scheme-actions {
  display: flex;
  gap: 8px;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 4px;
}

.stat-title {
  font-size: 14px;
  color: #666;
}

.alert-list {
  max-height: 400px;
  overflow-y: auto;
}

.alert-item {
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.alert-item:last-child {
  border-bottom: none;
}

.alert-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.alert-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.alert-title a {
  color: #1890ff;
  text-decoration: none;
}

.alert-title a:hover {
  text-decoration: underline;
}

.alert-meta {
  font-size: 12px;
  color: #999;
}

.alert-time {
  margin-top: 4px;
}

.scheme-title {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.scheme-description p {
  margin-bottom: 4px;
  color: #666;
}

.scheme-meta {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #999;
  margin-top: 8px;
}

.scheme-meta span {
  display: flex;
  align-items: center;
  gap: 4px;
}

.stats-card {
  margin-bottom: 24px;
}

.recent-alerts {
  max-height: 400px;
  overflow-y: auto;
}

.alert-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.alert-meta {
  color: #666;
}

.alert-time {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

@media (max-width: 768px) {
  .search-tracking {
    padding: 16px;
  }
  
  .page-header {
    padding: 24px 16px;
  }
  
  .actions {
    flex-direction: column;
  }
  
  .scheme-meta {
    flex-direction: column;
    gap: 8px;
  }
}
</style>
