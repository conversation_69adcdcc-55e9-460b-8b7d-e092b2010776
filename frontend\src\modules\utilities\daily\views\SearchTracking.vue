<template>
  <div class="search-tracking">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1>检索追踪预警</h1>
      <p>保存检索方案，设置预警条件，及时识别舆情风险</p>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <a-row :gutter="[24, 24]">
        <!-- 左侧：检索方案管理 -->
        <a-col :xs="24" :lg="16">
          <a-card title="检索方案管理" :bordered="false">
            <!-- 操作按钮 -->
            <div class="actions">
              <a-button type="primary" @click="showCreateModal = true">
                <plus-outlined />
                创建检索方案
              </a-button>
              <a-button @click="importScheme">
                <import-outlined />
                导入方案
              </a-button>
            </div>

            <!-- 方案列表 -->
            <div class="scheme-list">
              <a-list
                :data-source="searchSchemes"
                :loading="loading"
              >
                <template #renderItem="{ item }">
                  <a-list-item>
                    <template #actions>
                      <a @click="viewResults(item)">查看结果</a>
                      <a @click="editScheme(item)">编辑</a>
                      <a @click="configureAlert(item)">预警设置</a>
                      <a-popconfirm
                        title="确定要删除这个方案吗？"
                        @confirm="deleteScheme(item.id)"
                      >
                        <a style="color: #ff4d4f;">删除</a>
                      </a-popconfirm>
                    </template>
                    
                    <a-list-item-meta>
                      <template #title>
                        <div class="scheme-title">
                          {{ item.name }}
                          <a-tag :color="getStatusColor(item.status)">
                            {{ getStatusText(item.status) }}
                          </a-tag>
                          <a-tag v-if="item.alertEnabled" color="orange">
                            <bell-outlined />
                            预警开启
                          </a-tag>
                        </div>
                      </template>
                      <template #description>
                        <div class="scheme-description">
                          <p><strong>关键词：</strong>{{ item.keywords.join(', ') }}</p>
                          <p><strong>数据源：</strong>{{ item.sources.join(', ') }}</p>
                          <div class="scheme-meta">
                            <span><calendar-outlined /> 创建时间：{{ item.createdAt }}</span>
                            <span><clock-circle-outlined /> 最后执行：{{ item.lastExecution }}</span>
                            <span><file-text-outlined /> 结果数：{{ item.resultCount }}</span>
                          </div>
                        </div>
                      </template>
                    </a-list-item-meta>
                  </a-list-item>
                </template>
              </a-list>
            </div>
          </a-card>
        </a-col>

        <!-- 右侧：预警统计和最近预警 -->
        <a-col :xs="24" :lg="8">
          <!-- 统计卡片 -->
          <a-card title="预警统计" :bordered="false" class="stats-card">
            <a-row :gutter="16">
              <a-col :span="12">
                <a-statistic
                  title="活跃方案"
                  :value="activeSchemesCount"
                  :value-style="{ color: '#3f8600' }"
                />
              </a-col>
              <a-col :span="12">
                <a-statistic
                  title="今日预警"
                  :value="todayAlertsCount"
                  :value-style="{ color: '#cf1322' }"
                />
              </a-col>
            </a-row>
          </a-card>

          <!-- 最近预警 -->
          <a-card title="最近预警" :bordered="false" class="recent-alerts">
            <a-list
              :data-source="recentAlerts"
              size="small"
            >
              <template #renderItem="{ item }">
                <a-list-item>
                  <template #actions>
                    <a @click="viewAlert(item)">查看</a>
                  </template>
                  <a-list-item-meta>
                    <template #title>
                      <div class="alert-title">
                        <a @click="viewAlert(item)">{{ item.title }}</a>
                        <a-tag :color="getAlertLevelColor(item.level)" size="small">
                          {{ getAlertLevelText(item.level) }}
                        </a-tag>
                      </div>
                    </template>
                    <template #description>
                      <div class="alert-meta">
                        <div>{{ item.description }}</div>
                        <div class="alert-time">{{ item.triggeredAt }}</div>
                      </div>
                    </template>
                  </a-list-item-meta>
                </a-list-item>
              </template>
            </a-list>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 创建检索方案模态框 -->
    <a-modal
      v-model:open="showCreateModal"
      title="创建检索方案"
      width="800px"
      @ok="createScheme"
      @cancel="resetForm"
    >
      <a-form
        ref="formRef"
        :model="schemeForm"
        :rules="formRules"
        layout="vertical"
      >
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="方案名称" name="name">
              <a-input v-model:value="schemeForm.name" placeholder="请输入方案名称" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="检索语言" name="language">
              <a-select v-model:value="schemeForm.language" placeholder="选择检索语言">
                <a-select-option value="zh">中文</a-select-option>
                <a-select-option value="en">英文</a-select-option>
                <a-select-option value="all">全部语言</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item label="关键词" name="keywords">
          <a-select
            v-model:value="schemeForm.keywords"
            mode="tags"
            placeholder="输入关键词，按回车添加"
            :token-separators="[',', ';', ' ']"
          />
        </a-form-item>

        <a-form-item label="数据源" name="sources">
          <a-checkbox-group v-model:value="schemeForm.sources">
            <a-checkbox value="news">新闻媒体</a-checkbox>
            <a-checkbox value="social">社交媒体</a-checkbox>
            <a-checkbox value="forum">论坛博客</a-checkbox>
            <a-checkbox value="government">政府网站</a-checkbox>
            <a-checkbox value="academic">学术期刊</a-checkbox>
          </a-checkbox-group>
        </a-form-item>

        <a-form-item label="地域范围" name="regions">
          <a-select
            v-model:value="schemeForm.regions"
            mode="multiple"
            placeholder="选择地域范围"
          >
            <a-select-option value="china">中国</a-select-option>
            <a-select-option value="usa">美国</a-select-option>
            <a-select-option value="europe">欧洲</a-select-option>
            <a-select-option value="asia">亚洲</a-select-option>
            <a-select-option value="global">全球</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="方案描述" name="description">
          <a-textarea 
            v-model:value="schemeForm.description" 
            placeholder="请描述检索方案的目的和要求"
            :rows="3"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 预警配置模态框 -->
    <a-modal
      v-model:open="showAlertModal"
      title="预警配置"
      width="600px"
      @ok="saveAlertConfig"
      @cancel="resetAlertForm"
    >
      <a-form
        ref="alertFormRef"
        :model="alertForm"
        :rules="alertFormRules"
        layout="vertical"
      >
        <a-form-item label="启用预警" name="enabled">
          <a-switch v-model:checked="alertForm.enabled" />
        </a-form-item>

        <div v-if="alertForm.enabled">
          <a-form-item label="触发条件" name="triggerType">
            <a-radio-group v-model:value="alertForm.triggerType">
              <a-radio value="time">定时触发</a-radio>
              <a-radio value="threshold">阈值触发</a-radio>
              <a-radio value="change">变化触发</a-radio>
            </a-radio-group>
          </a-form-item>

          <a-form-item v-if="alertForm.triggerType === 'time'" label="检查频率" name="frequency">
            <a-select v-model:value="alertForm.frequency">
              <a-select-option value="hourly">每小时</a-select-option>
              <a-select-option value="daily">每日</a-select-option>
              <a-select-option value="weekly">每周</a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item v-if="alertForm.triggerType === 'threshold'" label="结果数量阈值" name="threshold">
            <a-input-number
              v-model:value="alertForm.threshold"
              :min="1"
              placeholder="当结果数量超过此值时触发预警"
              style="width: 100%"
            />
          </a-form-item>

          <a-form-item v-if="alertForm.triggerType === 'change'" label="变化比例" name="changeRate">
            <a-input-number
              v-model:value="alertForm.changeRate"
              :min="0"
              :max="100"
              formatter="value => `${value}%`"
              parser="value => value.replace('%', '')"
              placeholder="当结果数量变化超过此比例时触发预警"
              style="width: 100%"
            />
          </a-form-item>

          <a-form-item label="预警级别" name="level">
            <a-select v-model:value="alertForm.level">
              <a-select-option value="low">低</a-select-option>
              <a-select-option value="medium">中</a-select-option>
              <a-select-option value="high">高</a-select-option>
              <a-select-option value="critical">紧急</a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="接收人员" name="recipients">
            <a-select
              v-model:value="alertForm.recipients"
              mode="tags"
              placeholder="输入邮箱地址，按回车添加"
              :token-separators="[',', ';']"
            />
          </a-form-item>
        </div>
      </a-form>
    </a-modal>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, computed, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import dayjs from 'dayjs';
import {
  Plus,
  Upload,
  Bell,
  Calendar,
  Clock,
  Document
} from '@element-plus/icons-vue';

export default defineComponent({
  name: 'SearchTracking',
  components: {
    Plus,
    Upload,
    Bell,
    Calendar,
    Clock,
    Document
  },
  setup() {
    const loading = ref(false);
    const showCreateModal = ref(false);
    const showAlertModal = ref(false);
    const formRef = ref();
    const alertFormRef = ref();

    // 检索方案表单
    const schemeForm = reactive({
      name: '',
      description: '',
      keywords: [],
      sources: ['news', 'social'],
      regions: ['china'],
      language: 'zh'
    });

    // 预警配置表单
    const alertForm = reactive({
      enabled: false,
      triggerType: 'time',
      frequency: 'daily',
      threshold: 100,
      changeRate: 50,
      level: 'medium',
      recipients: []
    });

    // 表单验证规则
    const formRules = {
      name: [{ required: true, message: '请输入方案名称' }],
      keywords: [{ required: true, message: '请添加至少一个关键词' }],
      sources: [{ required: true, message: '请选择至少一个数据源' }],
      regions: [{ required: true, message: '请选择地域范围' }]
    };

    const alertFormRules = {
      frequency: [{ required: true, message: '请选择检查频率' }],
      threshold: [{ required: true, message: '请设置阈值' }],
      changeRate: [{ required: true, message: '请设置变化比例' }],
      level: [{ required: true, message: '请选择预警级别' }],
      recipients: [{ required: true, message: '请添加接收人员' }]
    };

    // 模拟数据
    const searchSchemes = ref([
      {
        id: 1,
        name: '中美关系舆情监测',
        description: '监测中美关系相关的舆情动态',
        keywords: ['中美关系', '贸易战', '外交'],
        sources: ['新闻媒体', '社交媒体'],
        regions: ['中国', '美国'],
        status: 'active',
        alertEnabled: true,
        createdAt: '2024-01-01',
        lastExecution: '2024-01-07 09:00',
        resultCount: 1250
      },
      {
        id: 2,
        name: '东南亚政治动态',
        description: '跟踪东南亚地区的政治变化',
        keywords: ['东南亚', '政治', '选举'],
        sources: ['新闻媒体', '政府网站'],
        regions: ['亚洲'],
        status: 'active',
        alertEnabled: false,
        createdAt: '2024-01-02',
        lastExecution: '2024-01-07 08:30',
        resultCount: 856
      }
    ]);

    const recentAlerts = ref([
      {
        id: 1,
        title: '中美关系舆情异常增长',
        description: '相关舆情数量较昨日增长150%',
        level: 'high',
        triggeredAt: '2024-01-07 10:30',
        schemeId: 1
      },
      {
        id: 2,
        title: '东南亚政治话题热度上升',
        description: '检测到相关讨论量显著增加',
        level: 'medium',
        triggeredAt: '2024-01-07 09:15',
        schemeId: 2
      }
    ]);

    // 计算属性
    const activeSchemesCount = computed(() => {
      return searchSchemes.value.filter(scheme => scheme.status === 'active').length;
    });

    const todayAlertsCount = computed(() => {
      const today = dayjs().format('YYYY-MM-DD');
      return recentAlerts.value.filter(alert => 
        alert.triggeredAt.startsWith(today)
      ).length;
    });

    // 方法
    const getStatusColor = (status) => {
      const colors = {
        active: 'green',
        paused: 'orange',
        stopped: 'red'
      };
      return colors[status] || 'default';
    };

    const getStatusText = (status) => {
      const texts = {
        active: '运行中',
        paused: '已暂停',
        stopped: '已停止'
      };
      return texts[status] || '未知';
    };

    const getAlertLevelColor = (level) => {
      const colors = {
        low: 'blue',
        medium: 'orange',
        high: 'red',
        critical: 'purple'
      };
      return colors[level] || 'default';
    };

    const getAlertLevelText = (level) => {
      const texts = {
        low: '低',
        medium: '中',
        high: '高',
        critical: '紧急'
      };
      return texts[level] || '未知';
    };

    const createScheme = async () => {
      try {
        await formRef.value.validate();
        
        const newScheme = {
          id: Date.now(),
          name: schemeForm.name,
          description: schemeForm.description,
          keywords: schemeForm.keywords,
          sources: schemeForm.sources,
          regions: schemeForm.regions,
          status: 'active',
          alertEnabled: false,
          createdAt: dayjs().format('YYYY-MM-DD'),
          lastExecution: '未执行',
          resultCount: 0
        };

        searchSchemes.value.unshift(newScheme);
        ElMessage.success('检索方案创建成功！');
        showCreateModal.value = false;
        resetForm();
      } catch (error) {
        console.error('表单验证失败:', error);
      }
    };

    const resetForm = () => {
      Object.assign(schemeForm, {
        name: '',
        description: '',
        keywords: [],
        sources: ['news', 'social'],
        regions: ['china'],
        language: 'zh'
      });
    };

    const resetAlertForm = () => {
      Object.assign(alertForm, {
        enabled: false,
        triggerType: 'time',
        frequency: 'daily',
        threshold: 100,
        changeRate: 50,
        level: 'medium',
        recipients: []
      });
    };

    const editScheme = (scheme) => {
      ElMessage.info('编辑功能开发中');
    };

    const deleteScheme = (schemeId) => {
      const index = searchSchemes.value.findIndex(scheme => scheme.id === schemeId);
      if (index > -1) {
        searchSchemes.value.splice(index, 1);
        ElMessage.success('方案删除成功');
      }
    };

    const configureAlert = (scheme) => {
      showAlertModal.value = true;
      // 这里应该加载该方案的预警配置
    };

    const saveAlertConfig = async () => {
      if (alertForm.enabled) {
        try {
          await alertFormRef.value.validate();
        } catch (error) {
          return;
        }
      }
      
      ElMessage.success('预警配置保存成功！');
      showAlertModal.value = false;
      resetAlertForm();
    };

    const viewResults = (scheme) => {
      ElMessage.info('查看结果功能开发中');
    };

    const viewAlert = (alert) => {
      ElMessage.info('查看预警详情功能开发中');
    };

    const importScheme = () => {
      ElMessage.info('导入方案功能开发中');
    };

    onMounted(() => {
      // 初始化数据
    });

    return {
      loading,
      showCreateModal,
      showAlertModal,
      formRef,
      alertFormRef,
      schemeForm,
      alertForm,
      formRules,
      alertFormRules,
      searchSchemes,
      recentAlerts,
      activeSchemesCount,
      todayAlertsCount,
      getStatusColor,
      getStatusText,
      getAlertLevelColor,
      getAlertLevelText,
      createScheme,
      resetForm,
      resetAlertForm,
      editScheme,
      deleteScheme,
      configureAlert,
      saveAlertConfig,
      viewResults,
      viewAlert,
      importScheme
    };
  }
});
</script>

<style scoped>
.search-tracking {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 32px;
  margin-bottom: 24px;
  color: white;
  text-align: center;
}

.page-header h1 {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 8px;
  color: white;
}

.page-header p {
  font-size: 1.1rem;
  opacity: 0.9;
  margin: 0;
}

.actions {
  margin-bottom: 24px;
  display: flex;
  gap: 12px;
}

.scheme-title {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.scheme-description p {
  margin-bottom: 4px;
  color: #666;
}

.scheme-meta {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #999;
  margin-top: 8px;
}

.scheme-meta span {
  display: flex;
  align-items: center;
  gap: 4px;
}

.stats-card {
  margin-bottom: 24px;
}

.recent-alerts {
  max-height: 400px;
  overflow-y: auto;
}

.alert-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.alert-meta {
  color: #666;
}

.alert-time {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

@media (max-width: 768px) {
  .search-tracking {
    padding: 16px;
  }
  
  .page-header {
    padding: 24px 16px;
  }
  
  .actions {
    flex-direction: column;
  }
  
  .scheme-meta {
    flex-direction: column;
    gap: 8px;
  }
}
</style>
