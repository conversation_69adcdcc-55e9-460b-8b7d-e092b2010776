#!/usr/bin/env python3
"""
检查数据库中的 Wanx 任务
"""
import psycopg2
import psycopg2.extras
from datetime import datetime

def check_wanx_tasks():
    """检查 Wanx 任务"""
    try:
        conn = psycopg2.connect('postgresql://postgres:langpro8@localhost:5432/ai_platform')
        cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
        
        # 检查最近的任务
        cursor.execute("""
            SELECT task_id, status, progress, message, created_at 
            FROM wanx_video_tasks 
            ORDER BY created_at DESC 
            LIMIT 10
        """)
        
        tasks = cursor.fetchall()
        
        print("📋 最近的 Wanx 任务:")
        print("-" * 50)
        
        if tasks:
            for task in tasks:
                task_id = task['task_id'][:8] + "..."
                status = task['status']
                progress = task['progress']
                message = task['message'] or "无消息"
                created_at = task['created_at'].strftime("%H:%M:%S") if task['created_at'] else "N/A"
                
                print(f"🎬 {task_id} - {status} ({progress}%) - {message} - {created_at}")
        else:
            print("❌ 没有找到任务记录")
        
        # 检查任务统计
        cursor.execute("SELECT status, COUNT(*) FROM wanx_video_tasks GROUP BY status")
        stats = cursor.fetchall()
        
        print(f"\n📊 任务统计:")
        for stat in stats:
            print(f"   {stat['status']}: {stat['count']} 个")
        
        cursor.close()
        conn.close()
        
        return len(tasks)
        
    except Exception as e:
        print(f"❌ 数据库查询失败: {e}")
        return 0

if __name__ == "__main__":
    check_wanx_tasks()
