import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import Antd from 'ant-design-vue'
import 'ant-design-vue/dist/antd.css'
import './assets/styles/index.less'

// 创建Vue应用实例
const app = createApp(App)

// 注册全局组件
// 这里可以添加全局组件注册

// 注册全局属性
app.config.globalProperties.$message = Antd.message

// 配置全局错误处理
app.config.errorHandler = (err, vm, info) => {
  console.error('全局错误:', err, info)
  Antd.message.error('系统错误，请稍后再试')
}

// 使用插件
app.use(router)
app.use(Antd)

// 挂载应用
app.mount('#app') 