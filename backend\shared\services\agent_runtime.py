"""
智能体运行时引擎
负责工具调用、知识库查询、记忆管理等核心功能
"""

import asyncio
import json
import time
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime

import requests
import openai
from sqlalchemy.orm import Session

from models.true_agent import TrueAgent, AgentMemory, ToolExecution, ConversationMessage
from services.tool_executor import ToolExecutor
from services.knowledge_manager import KnowledgeManager
from services.memory_manager import MemoryManager
from services.ai_service import get_ai_service
from utils.db import get_db

logger = logging.getLogger(__name__)

class AgentRuntime:
    """智能体运行时引擎"""
    
    def __init__(self):
        self.tool_executor = ToolExecutor()
        self.knowledge_manager = KnowledgeManager()
        self.memory_manager = MemoryManager()
        self.ai_service = get_ai_service()
        
    async def process_message(
        self,
        agent_id: str,
        message: str,
        conversation_id: str,
        user_id: str = None,
        db: Session = None
    ) -> Dict[str, Any]:
        """处理用户消息"""

        if not db:
            logger.error("数据库会话为None，无法处理消息")
            raise ValueError("数据库会话不能为None")

        try:
            # 获取智能体配置
            agent = db.query(TrueAgent).filter(TrueAgent.id == agent_id).first()
            if not agent:
                raise ValueError(f"智能体不存在: {agent_id}")
            
            logger.info(f"[智能体 {agent.name}] 处理消息: {message}")
            
            # 1. 分析消息，确定需要使用的工具
            tool_calls = await self._analyze_message(agent, message)
            
            # 2. 执行工具调用
            tool_results = []
            for tool_call in tool_calls:
                try:
                    result = await self.tool_executor.execute_tool(
                        tool_call["type"], 
                        tool_call["params"],
                        agent_id,
                        conversation_id,
                        db
                    )
                    tool_results.append({
                        "tool": tool_call["type"],
                        "result": result
                    })
                except Exception as e:
                    logger.error(f"工具执行失败 {tool_call['type']}: {e}")
                    tool_results.append({
                        "tool": tool_call["type"],
                        "error": str(e)
                    })
            
            # 3. 查询知识库
            knowledge_results = await self.knowledge_manager.query_knowledge(
                agent_id=agent_id,
                query=message,
                db=db
            )
            
            # 4. 获取相关记忆
            memories = await self.memory_manager.get_relevant_memories(
                agent_id=agent_id,
                query=message,
                user_id=user_id,
                db=db
            )
            
            # 5. 生成回复
            response = await self._generate_response(
                agent, message, tool_results, knowledge_results, memories
            )
            
            # 6. 保存记忆
            await self.memory_manager.add_memory(
                agent_id=agent_id,
                memory_type="short_term",
                content={"user_message": message, "agent_response": response},
                conversation_id=conversation_id,
                user_id=user_id,
                db=db
            )
            
            # 7. 更新统计信息
            agent.usage_count += 1
            db.commit()
            
            return {
                "response": response,
                "tool_results": tool_results,
                "knowledge_results": knowledge_results,
                "memories_used": len(memories),
                "metadata": {
                    "agent": agent.name,
                    "timestamp": datetime.utcnow().isoformat(),
                    "tools_used": [t["tool"] for t in tool_results],
                    "processing_time": time.time()
                }
            }
            
        except Exception as e:
            logger.error(f"消息处理失败: {e}")
            raise
    
    async def _analyze_message(self, agent: TrueAgent, message: str) -> List[Dict[str, Any]]:
        """分析消息，确定需要使用的工具"""
        
        tool_calls = []
        message_lower = message.lower()
        
        # 获取智能体可用的工具
        available_tools = agent.tools or []
        tool_types = [tool.get("type") for tool in available_tools if tool.get("type")]
        
        # 简单的关键词匹配（实际应该使用LLM分析）
        if "web_search" in tool_types and any(keyword in message_lower for keyword in ["搜索", "查找", "search", "find"]):
            # 提取搜索关键词
            query = message
            for keyword in ["搜索", "查找", "search", "find"]:
                query = query.replace(keyword, "").strip()
            
            tool_calls.append({
                "type": "web_search",
                "params": {"query": query, "max_results": 5}
            })
        
        if "code_execution" in tool_types and any(keyword in message_lower for keyword in ["代码", "编程", "code", "python"]):
            # 提取代码内容
            code = self._extract_code_from_message(message)
            if code:
                tool_calls.append({
                    "type": "code_execution",
                    "params": {"code": code, "timeout": 30}
                })
        
        if "translation" in tool_types and any(keyword in message_lower for keyword in ["翻译", "translate"]):
            # 提取翻译内容
            text = message
            for keyword in ["翻译", "translate"]:
                text = text.replace(keyword, "").strip()
            
            tool_calls.append({
                "type": "translation",
                "params": {
                    "text": text,
                    "target_language": "zh",
                    "source_language": "auto"
                }
            })
        
        if "data_analysis" in tool_types and any(keyword in message_lower for keyword in ["分析", "数据", "analysis", "chart"]):
            tool_calls.append({
                "type": "data_analysis",
                "params": {
                    "data": {"sample": "data"},
                    "analysis_type": "summary"
                }
            })
        
        if "image_processing" in tool_types and any(keyword in message_lower for keyword in ["图片", "图像", "image", "photo"]):
            tool_calls.append({
                "type": "image_processing",
                "params": {
                    "image_url": "placeholder",
                    "operation": "analyze"
                }
            })
        
        return tool_calls
    
    def _extract_code_from_message(self, message: str) -> str:
        """从消息中提取代码"""
        # 简单的代码提取逻辑
        if "```" in message:
            parts = message.split("```")
            if len(parts) >= 3:
                return parts[1].strip()
        
        # 如果没有代码块，返回简单的示例代码
        return "print('Hello, World!')"
    
    async def _generate_response(
        self,
        agent: TrueAgent,
        message: str,
        tool_results: List[Dict],
        knowledge_results: List[Dict],
        memories: List[Dict]
    ) -> str:
        """使用AI服务生成智能体回复"""

        try:
            # 构建系统提示词
            system_prompt = self._build_system_prompt(agent, tool_results, knowledge_results, memories)

            # 构建对话消息
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": message}
            ]

            # 调用AI服务生成回复
            response = self.ai_service.chat_completion(
                messages=messages,
                max_tokens=agent.max_tokens or 1000,
                temperature=agent.temperature or 0.7
            )

            # 提取AI生成的回复
            ai_response = response.get("message", {}).get("content", "")

            if ai_response:
                # 过滤掉<think>标签和其内容
                cleaned_response = self._clean_ai_response(ai_response)
                return cleaned_response.strip()
            else:
                # 如果AI服务失败，使用备用回复
                return self._generate_fallback_response(agent, message, tool_results, knowledge_results, memories)

        except Exception as e:
            logger.error(f"AI服务生成回复失败: {e}")
            # 使用备用回复
            return self._generate_fallback_response(agent, message, tool_results, knowledge_results, memories)

    def _build_system_prompt(
        self,
        agent: TrueAgent,
        tool_results: List[Dict],
        knowledge_results: List[Dict],
        memories: List[Dict]
    ) -> str:
        """构建系统提示词"""

        prompt_parts = []

        # 智能体身份和角色
        prompt_parts.append(f"你是 {agent.name}，{agent.description}")

        if agent.system_prompt:
            prompt_parts.append(f"\n{agent.system_prompt}")

        # 工具执行结果
        if tool_results:
            prompt_parts.append("\n\n刚刚执行的工具结果：")
            for result in tool_results:
                tool_name = self._get_tool_name(result["tool"])
                if "result" in result and result["result"].get("success"):
                    prompt_parts.append(f"- {tool_name}: 执行成功")

                    # 添加具体结果信息
                    if result["tool"] == "translation":
                        translated = result["result"].get("translated_text", "")
                        prompt_parts.append(f"  翻译结果: {translated}")
                    elif result["tool"] == "web_search":
                        results = result["result"].get("results", [])
                        if results:
                            prompt_parts.append(f"  找到 {len(results)} 个搜索结果")
                    elif result["tool"] == "code_execution":
                        output = result["result"].get("output", "")
                        prompt_parts.append(f"  执行输出: {output}")
                else:
                    error_msg = result.get("error", "未知错误")
                    prompt_parts.append(f"- {tool_name}: 执行失败 ({error_msg})")

        # 知识库信息
        if knowledge_results:
            prompt_parts.append("\n\n相关知识库信息：")
            for kb in knowledge_results[:2]:
                content = kb.get('content', '')[:200]  # 限制长度
                prompt_parts.append(f"- {content}")

        # 记忆信息
        if memories:
            prompt_parts.append("\n\n相关历史记忆：")
            for memory in memories[:3]:
                content = str(memory.get('content', ''))[:100]  # 限制长度
                prompt_parts.append(f"- {content}")

        # 回复指导
        prompt_parts.append("\n\n请基于以上信息，以自然、友好、专业的方式回复用户。")
        prompt_parts.append("如果执行了工具，请自然地融入工具结果到回复中。")
        prompt_parts.append("保持你的角色特点，提供有价值的帮助。")

        return "".join(prompt_parts)

    def _generate_fallback_response(
        self,
        agent: TrueAgent,
        message: str,
        tool_results: List[Dict],
        knowledge_results: List[Dict],
        memories: List[Dict]
    ) -> str:
        """生成备用回复（当AI服务不可用时）"""

        response_parts = []

        # 基础回复
        response_parts.append(f"我是 {agent.name}，")

        # 基于工具结果生成回复
        if tool_results:
            response_parts.append("我已经为您执行了以下操作：\n")
            for result in tool_results:
                if "result" in result and result["result"].get("success"):
                    tool_name = self._get_tool_name(result["tool"])
                    response_parts.append(f"✅ {tool_name}: 执行成功\n")

                    # 添加具体结果
                    if result["tool"] == "translation":
                        translated = result["result"].get("translated_text", "")
                        response_parts.append(f"翻译结果：{translated}\n")

                else:
                    tool_name = self._get_tool_name(result["tool"])
                    error_msg = result.get("error", "未知错误")
                    response_parts.append(f"❌ {tool_name}: {error_msg}\n")

        # 基于记忆
        if memories:
            response_parts.append(f"\n我记得我们之前讨论过相关话题。")

        # 如果没有工具调用，生成通用回复
        if not tool_results:
            response_parts.append(f"我理解您的问题：\"{message}\"。")

            if agent.tools:
                response_parts.append("\n\n我可以为您提供以下帮助：\n")
                for tool in agent.tools[:3]:
                    tool_name = self._get_tool_name(tool.get("type", ""))
                    response_parts.append(f"• {tool_name}\n")

        return "".join(response_parts)

    def _clean_ai_response(self, response: str) -> str:
        """清理AI回复，移除<think>标签等"""
        import re

        # 移除<think>...</think>标签及其内容
        cleaned = re.sub(r'<think>.*?</think>', '', response, flags=re.DOTALL)

        # 移除多余的空行
        cleaned = re.sub(r'\n\s*\n', '\n\n', cleaned)

        # 移除开头和结尾的空白
        cleaned = cleaned.strip()

        return cleaned

    def _get_tool_name(self, tool_type: str) -> str:
        """获取工具显示名称"""
        tool_names = {
            "web_search": "网络搜索",
            "code_execution": "代码执行",
            "translation": "文本翻译",
            "data_analysis": "数据分析",
            "image_processing": "图像处理",
            "file_operation": "文件操作",
            "api_call": "API调用"
        }
        return tool_names.get(tool_type, tool_type)
    
    async def get_agent_status(self, agent_id: str, db: Session) -> Dict[str, Any]:
        """获取智能体状态"""
        
        agent = db.query(TrueAgent).filter(TrueAgent.id == agent_id).first()
        if not agent:
            return None
        
        # 获取统计信息
        memory_count = db.query(AgentMemory).filter(AgentMemory.agent_id == agent_id).count()
        tool_executions = db.query(ToolExecution).filter(ToolExecution.agent_id == agent_id).count()
        
        return {
            "agent": agent.name,
            "status": "active" if agent.is_active else "inactive",
            "tools": len(agent.tools) if agent.tools else 0,
            "knowledge_bases": len(agent.knowledge_bases) if agent.knowledge_bases else 0,
            "memories": memory_count,
            "tool_executions": tool_executions,
            "usage_count": agent.usage_count,
            "success_rate": agent.success_rate,
            "last_active": agent.updated_at.isoformat() if agent.updated_at else None
        }

# 创建全局运行时实例
agent_runtime = AgentRuntime()
