import logging
from typing import List, Optional, Dict, Any, Tuple
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import func, or_, desc
import uuid
from datetime import datetime
import os

from models.agent import (
    Agent, AgentTag, AgentCapability, AgentExample, 
    AgentReview, UserFavorite, AgentSession, AgentMessage
)
from models.user import User
from schemas.agent import AgentCreate, AgentUpdate
from services.ai_service import AIService  # 导入AIService
from models.corpus import Corpus, CorpusEntry # 导入Corpus和CorpusEntry

logger = logging.getLogger(__name__)

# 初始化AI服务
ai_service = AIService()

class AgentService:
    @staticmethod
    def normalize_agent_for_response(agent):
        """
        规范化Agent对象，确保所有字段符合API响应模式要求
        - 将AgentTag对象转换为字符串列表
        - 确保author.id是字符串类型
        - 处理其他可能的序列化问题
        
        不直接修改SQLAlchemy ORM对象，而是创建一个新的字典
        """
        if agent is None:
            return None
            
        # 创建一个新的字典，而不是修改原始对象
        result = {}
        
        # 复制基本属性
        for attr in ['id', 'name', 'description', 'category', 'author_id', 
                     'avatar', 'cover_image', 'rating', 'review_count', 
                     'usage_count', 'is_featured', 'created_at', 'updated_at',
                     'pricing_type', 'price']:
            if hasattr(agent, attr):
                value = getattr(agent, attr)
                # 确保ID字段是字符串类型
                if attr == 'id' or attr == 'author_id':
                    value = str(value) if value is not None else None
                result[attr] = value
        
        # 处理tags - 将AgentTag对象转换为字符串列表
        if hasattr(agent, 'tags') and agent.tags:
            result['tags'] = [tag.tag for tag in agent.tags]
        else:
            result['tags'] = []
            
        # 处理capabilities - 将AgentCapability对象转换为字符串列表
        if hasattr(agent, 'capabilities') and agent.capabilities:
            result['capabilities'] = [cap.capability for cap in agent.capabilities]
        else:
            result['capabilities'] = []
            
        # 处理examples - 将AgentExample对象转换为字符串列表
        if hasattr(agent, 'examples') and agent.examples:
            result['examples'] = [ex.example for ex in agent.examples]
        else:
            result['examples'] = []
            
        # 处理author - 确保author.id是字符串类型
        if hasattr(agent, 'author') and agent.author:
            author = {}
            # 复制作者基本属性
            for attr in ['id', 'username', 'email', 'full_name', 'avatar']:
                if hasattr(agent.author, attr):
                    value = getattr(agent.author, attr)
                    # 确保ID字段是字符串类型
                    if attr == 'id':
                        value = str(value) if value is not None else None
                    author[attr] = value
            result['author'] = author
                
        # 处理is_favorite标志
        if hasattr(agent, 'is_favorite'):
            result['is_favorite'] = agent.is_favorite
        else:
            result['is_favorite'] = False
            
        return result
        
    @staticmethod
    def normalize_session_for_response(session):
        """
        规范化AgentSession对象，确保所有字段符合API响应模式要求
        - 确保user_id是字符串类型
        - 处理消息列表
        
        不直接修改SQLAlchemy ORM对象，而是创建一个新的字典
        """
        if session is None:
            return None
            
        # 创建一个新的字典，而不是修改原始对象
        result = {}
        
        # 复制基本属性
        for attr in ['id', 'agent_id', 'user_id', 'created_at', 'updated_at']:
            if hasattr(session, attr):
                value = getattr(session, attr)
                # 确保ID字段是字符串类型，尤其是user_id
                if attr in ['id', 'agent_id', 'user_id']:
                    value = str(value) if value is not None else None
                result[attr] = value
        
        # 处理消息列表
        result['messages'] = []
        if hasattr(session, 'messages') and session.messages:
            for message in session.messages:
                msg_dict = {}
                for msg_attr in ['id', 'session_id', 'sender_type', 'content', 'created_at']:
                    if hasattr(message, msg_attr):
                        msg_value = getattr(message, msg_attr)
                        # 确保ID字段是字符串类型
                        if msg_attr in ['id', 'session_id']:
                            msg_value = str(msg_value) if msg_value is not None else None
                        msg_dict[msg_attr] = msg_value
                result['messages'].append(msg_dict)
                
        return result

    @staticmethod
    def get_agents(
        db: Session, 
        skip: int = 0, 
        limit: int = 20, 
        category: Optional[str] = None,
        search: Optional[str] = None,
        sort_by: str = "popular",
        user_id: Optional[str] = None,
        favorites_only: bool = False
    ) -> Tuple[List[Dict[str, Any]], int]:
        """
        获取智能体列表，支持筛选、搜索和排序
        
        返回值:
        - 第一个元素是一个规范化的字典列表，每个字典代表一个Agent
        - 第二个元素是总记录数
        """
        query = db.query(Agent)
        
        # 应用筛选条件
        if category:
            query = query.filter(Agent.category == category)
            
        # 搜索
        if search:
            search_term = f"%{search}%"
            # 搜索名称和描述
            query = query.filter(
                or_(
                    Agent.name.ilike(search_term),
                    Agent.description.ilike(search_term)
                )
            )
            
        # 如果需要只获取收藏的智能体
        if favorites_only and user_id:
            query = query.join(UserFavorite).filter(
                UserFavorite.user_id == user_id
            )
            
        # 计算总数
        total = query.count()
        
        # 应用排序
        if sort_by == "popular":
            query = query.order_by(desc(Agent.usage_count))
        elif sort_by == "newest":
            query = query.order_by(desc(Agent.created_at))
        elif sort_by == "rating":
            query = query.order_by(desc(Agent.rating))
        else:
            query = query.order_by(desc(Agent.usage_count))
            
        # 应用分页
        query = query.offset(skip).limit(limit)
        
        # 预加载相关数据
        query = query.options(
            joinedload(Agent.tags),
            joinedload(Agent.author)
        )
        
        agents = query.all()
        
        # 如果提供了用户ID，检查每个智能体是否被收藏
        if user_id:
            agent_ids = [agent.id for agent in agents]
            favorites = db.query(UserFavorite).filter(
                UserFavorite.user_id == user_id,
                UserFavorite.agent_id.in_(agent_ids)
            ).all()
            
            # 创建收藏的智能体ID集合，用于快速查找
            favorite_agent_ids = {favorite.agent_id for favorite in favorites}
            
            # 为每个智能体设置is_favorite属性
            for agent in agents:
                setattr(agent, "is_favorite", agent.id in favorite_agent_ids)

        # 规范化每个Agent对象为字典
        normalized_agents = [AgentService.normalize_agent_for_response(agent) for agent in agents]
        
        return normalized_agents, total
    
    @staticmethod
    def get_agent(db: Session, agent_id: str, user_id: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """
        获取智能体详情
        
        返回值:
        - 规范化的字典，表示Agent对象
        - 如果找不到智能体，则返回None
        """
        query = db.query(Agent).filter(Agent.id == agent_id)
        
        # 预加载相关数据
        query = query.options(
            joinedload(Agent.tags),
            joinedload(Agent.capabilities),
            joinedload(Agent.examples),
            joinedload(Agent.author)
        )
        
        agent = query.first()
        
        if agent is None:
            return None
        
        if agent and user_id:
            # 检查是否被收藏
            favorite = db.query(UserFavorite).filter(
                UserFavorite.user_id == user_id,
                UserFavorite.agent_id == agent_id
            ).first()
            
            setattr(agent, "is_favorite", favorite is not None)
        
        # 规范化Agent对象为字典
        return AgentService.normalize_agent_for_response(agent)
    
    @staticmethod
    def create_agent(db: Session, agent_data: AgentCreate, author_id: str) -> Dict[str, Any]:
        """
        创建新的智能体
        
        返回值:
        - 规范化的字典，表示创建的Agent对象
        """
        # 创建智能体基本信息
        agent_id = str(uuid.uuid4())
        db_agent = Agent(
            id=agent_id,
            name=agent_data.name,
            description=agent_data.description,
            category=agent_data.category,
            author_id=author_id,
            avatar=agent_data.avatar,
            cover_image=agent_data.cover_image,
            pricing_type=agent_data.pricing_type,
            price=agent_data.price,
            # 添加语言学习相关字段
            agent_type=agent_data.agent_type,
            learning_settings=agent_data.learning_settings.dict() if agent_data.learning_settings else None,
            corpus_id=agent_data.corpus_id
        )
        
        db.add(db_agent)
        db.flush()  # 刷新数据库会话，获取ID
        
        # 创建标签（使用集合去重）
        added_tags = set()
        if agent_data.tags:
            for tag in agent_data.tags:
                # 只添加尚未添加过的标签
                if tag not in added_tags:
                    db_tag = AgentTag(agent_id=agent_id, tag=tag)
                    db.add(db_tag)
                    added_tags.add(tag)
        
        # 创建能力
        if agent_data.capabilities:
            for capability in agent_data.capabilities:
                db_capability = AgentCapability(agent_id=agent_id, capability=capability)
                db.add(db_capability)
        
        # 创建示例
        if agent_data.examples:
            for example in agent_data.examples:
                db_example = AgentExample(agent_id=agent_id, example=example)
                db.add(db_example)
        
        # 如果是语言学习类型，自动添加"语言学习"标签（如果尚未添加）
        if agent_data.agent_type == "language_learning" and "语言学习" not in added_tags:
            db_tag = AgentTag(agent_id=agent_id, tag="语言学习")
            db.add(db_tag)
            added_tags.add("语言学习")
            
            # 添加语言作为标签（如英语、日语等）（如果尚未添加）
            if agent_data.learning_settings and agent_data.learning_settings.language:
                language_tag = agent_data.learning_settings.language.capitalize()  # 首字母大写
                if language_tag not in added_tags:
                    db_tag = AgentTag(agent_id=agent_id, tag=language_tag)
                    db.add(db_tag)
                    added_tags.add(language_tag)
        
        db.commit()
        db.refresh(db_agent)
        
        # 规范化返回的Agent对象为字典
        return AgentService.normalize_agent_for_response(db_agent)
    
    @staticmethod
    def update_agent(db: Session, agent_id: str, agent_data: AgentUpdate, author_id: str) -> Optional[Dict[str, Any]]:
        """
        更新智能体信息
        
        返回值:
        - 规范化的字典，表示更新后的Agent对象
        - 如果找不到智能体或用户无权限，则返回None
        """
        # 查找智能体
        db_agent = db.query(Agent).filter(Agent.id == agent_id).first()
        
        if not db_agent:
            return None
            
        # 检查权限（只有作者或管理员可以更新）
        if db_agent.author_id != author_id:
            # 此处可以添加管理员权限检查
            return None
            
        # 更新基本字段
        update_data = agent_data.dict(exclude_unset=True)
        for key, value in update_data.items():
            if key not in ["tags", "capabilities", "examples", "learning_settings"]:
                setattr(db_agent, key, value)
        
        # 特殊处理learning_settings字段
        if "learning_settings" in update_data and update_data["learning_settings"]:
            # 如果提供了learning_settings，则将其转换为字典存储
            db_agent.learning_settings = update_data["learning_settings"].dict()
        
        # 更新标签
        if "tags" in update_data:
            # 删除现有标签
            db.query(AgentTag).filter(AgentTag.agent_id == agent_id).delete()
            
            # 添加新标签（使用集合去重）
            added_tags = set()
            for tag in update_data["tags"]:
                # 只添加尚未添加过的标签
                if tag not in added_tags:
                    db_tag = AgentTag(agent_id=agent_id, tag=tag)
                    db.add(db_tag)
                    added_tags.add(tag)
                
            # 如果是语言学习类型，自动添加"语言学习"标签（如果尚未添加）
            if db_agent.agent_type == "language_learning" and "语言学习" not in added_tags:
                db_tag = AgentTag(agent_id=agent_id, tag="语言学习")
                db.add(db_tag)
                added_tags.add("语言学习")
                
                # 添加语言作为标签（如英语、日语等）（如果尚未添加）
                if db_agent.learning_settings and "language" in db_agent.learning_settings:
                    language_tag = db_agent.learning_settings["language"].capitalize()  # 首字母大写
                    if language_tag not in added_tags:
                        db_tag = AgentTag(agent_id=agent_id, tag=language_tag)
                        db.add(db_tag)
                        added_tags.add(language_tag)
        
        # 更新能力
        if "capabilities" in update_data:
            # 删除现有能力
            db.query(AgentCapability).filter(AgentCapability.agent_id == agent_id).delete()
            
            # 添加新能力
            for capability in update_data["capabilities"]:
                db_capability = AgentCapability(agent_id=agent_id, capability=capability)
                db.add(db_capability)
        
        # 更新示例
        if "examples" in update_data:
            # 删除现有示例
            db.query(AgentExample).filter(AgentExample.agent_id == agent_id).delete()
            
            # 添加新示例
            for example in update_data["examples"]:
                db_example = AgentExample(agent_id=agent_id, example=example)
                db.add(db_example)
        
        # 更新时间
        db_agent.updated_at = datetime.utcnow()
        
        db.commit()
        db.refresh(db_agent)
        
        # 规范化返回的Agent对象为字典
        return AgentService.normalize_agent_for_response(db_agent)
    
    @staticmethod
    def delete_agent(db: Session, agent_id: str, author_id: str) -> bool:
        """
        删除智能体
        """
        # 查找智能体
        db_agent = db.query(Agent).filter(Agent.id == agent_id).first()
        
        if not db_agent:
            return False
            
        # 检查权限（只有作者或管理员可以删除）
        if db_agent.author_id != author_id:
            # 此处可以添加管理员权限检查
            return False
            
        db.delete(db_agent)
        db.commit()
        return True
    
    @staticmethod
    def toggle_favorite(db: Session, agent_id: str, user_id: int, category: Optional[str] = None) -> Tuple[bool, bool]:
        """
        切换收藏状态
        参数:
            db: 数据库会话
            agent_id: 智能体ID
            user_id: 用户ID
            category: 可选的收藏分类
        返回：(成功状态, 当前收藏状态)
        """
        # 查找智能体
        db_agent = db.query(Agent).filter(Agent.id == agent_id).first()
        
        if not db_agent:
            return False, False
            
        # 检查是否已收藏
        favorite = db.query(UserFavorite).filter(
            UserFavorite.user_id == user_id,
            UserFavorite.agent_id == agent_id
        ).first()
        
        if favorite:
            # 如果提供了新的分类且与当前不同，则更新分类
            if category is not None and favorite.category != category:
                favorite.category = category
                db.commit()
                return True, True
            else:
                # 取消收藏
                db.delete(favorite)
                is_favorite = False
        else:
            # 添加收藏
            favorite = UserFavorite(
                id=str(uuid.uuid4()),
                user_id=user_id,
                agent_id=agent_id,
                category=category  # 添加分类
            )
            db.add(favorite)
            is_favorite = True
            
        db.commit()
        return True, is_favorite
    
    @staticmethod
    def add_review(db: Session, agent_id: str, user_id: str, rating: float, comment: Optional[str] = None) -> Optional[AgentReview]:
        """
        添加或更新评价
        """
        # 查找智能体
        db_agent = db.query(Agent).filter(Agent.id == agent_id).first()
        
        if not db_agent:
            return None
            
        # 检查是否已评价
        db_review = db.query(AgentReview).filter(
            AgentReview.agent_id == agent_id,
            AgentReview.user_id == user_id
        ).first()
        
        if db_review:
            # 更新评价
            db_review.rating = rating
            db_review.comment = comment
            db_review.updated_at = datetime.utcnow()
        else:
            # 添加新评价
            db_review = AgentReview(
                id=str(uuid.uuid4()),
                agent_id=agent_id,
                user_id=user_id,
                rating=rating,
                comment=comment
            )
            db.add(db_review)
            
        # 更新智能体的评分和评价数
        reviews = db.query(AgentReview).filter(AgentReview.agent_id == agent_id).all()
        db_agent.review_count = len(reviews)
        
        if reviews:
            # 计算平均评分
            total_rating = sum(review.rating for review in reviews)
            db_agent.rating = total_rating / len(reviews)
        
        db.commit()
        db.refresh(db_review)
        return db_review
    
    @staticmethod
    def get_reviews(db: Session, agent_id: str, skip: int = 0, limit: int = 20) -> Tuple[List[AgentReview], int]:
        """
        获取智能体评价列表
        """
        query = db.query(AgentReview).filter(AgentReview.agent_id == agent_id)
        
        # 计算总数
        total = query.count()
        
        # 排序和分页
        query = query.order_by(desc(AgentReview.created_at)).offset(skip).limit(limit)
        
        # 预加载用户信息
        query = query.options(joinedload(AgentReview.user))
        
        reviews = query.all()
        return reviews, total
    
    @staticmethod
    def increment_usage(db: Session, agent_id: str) -> bool:
        """
        增加智能体使用次数
        """
        db_agent = db.query(Agent).filter(Agent.id == agent_id).first()
        
        if not db_agent:
            return False
            
        db_agent.usage_count += 1
        db.commit()
        return True
    
    @staticmethod
    def create_session(db: Session, agent_id: str, user_id: str) -> Optional[AgentSession]:
        """
        创建智能体会话
        
        返回值:
        - AgentSession对象
        - 如果找不到智能体，则返回None
        """
        # 查找智能体
        db_agent = db.query(Agent).filter(Agent.id == agent_id).first()
        
        if not db_agent:
            return None
            
        # 创建会话
        session = AgentSession(
            id=str(uuid.uuid4()),
            agent_id=agent_id,
            user_id=user_id
        )
        
        db.add(session)
        db.commit()
        db.refresh(session)
        
        return session
    
    @staticmethod
    def get_session(db: Session, session_id: str, user_id: str) -> Optional[AgentSession]:
        """
        获取会话详情
        
        返回值:
        - AgentSession对象
        - 如果找不到会话，则返回None
        """
        query = db.query(AgentSession).filter(
            AgentSession.id == session_id,
            AgentSession.user_id == user_id
        )
        
        # 预加载消息
        query = query.options(joinedload(AgentSession.messages))
        
        session = query.first()
        return session
    
    @staticmethod
    def add_message(db: Session, session_id: str, sender_type: str, content: str,
                   language_learning_mode: bool = False,
                   accent: Optional[str] = None,
                   language_level: Optional[str] = None) -> Optional[AgentMessage]:
        """
        添加会话消息
        
        参数:
            language_learning_mode: 是否开启语言学习模式
            accent: 口音设置 (例如 "american", "british")
            language_level: 语言水平 (例如 "beginner", "intermediate", "advanced")
        """
        # 查找会话
        db_session = db.query(AgentSession).filter(AgentSession.id == session_id).first()
        
        if not db_session:
            return None
            
        # 创建消息
        db_message = AgentMessage(
            id=str(uuid.uuid4()),
            session_id=session_id,
            sender_type=sender_type,
            content=content,
            created_at=datetime.utcnow()
        )
        
        db.add(db_message)
        db.commit()
        db.refresh(db_message)
        
        # 如果是用户消息，生成智能体回复
        if sender_type == 'user':
            try:
                # 获取智能体ID
                agent_id = db_session.agent_id
                
                # 生成AI回复
                ai_response = AgentService.generate_ai_response(
                    db=db, 
                    agent_id=agent_id, 
                    session_id=session_id, 
                    user_message=content,
                    language_learning_mode=language_learning_mode,
                    accent=accent,
                    language_level=language_level
                )
                
                if ai_response:
                    # 创建智能体回复消息
                    agent_message = AgentMessage(
                        id=str(uuid.uuid4()),
                        session_id=session_id,
                        sender_type='agent',
                        content=ai_response,
                        created_at=datetime.utcnow()
                    )
                    
                    db.add(agent_message)
                    db.commit()
                    db.refresh(agent_message)
                    
                    # 增加使用次数
                    AgentService.increment_usage(db, agent_id)
            except Exception as e:
                logger.error(f"生成AI回复失败: {str(e)}")
        
        return db_message
    
    @staticmethod
    def get_messages(db: Session, session_id: str, skip: int = 0, limit: int = 50) -> List[AgentMessage]:
        """
        获取会话消息列表
        """
        messages = db.query(AgentMessage).filter(
            AgentMessage.session_id == session_id
        ).order_by(AgentMessage.created_at).offset(skip).limit(limit).all()
        
        return messages
    
    @staticmethod
    def get_categories(db: Session) -> List[str]:
        """
        获取所有分类
        """
        categories = db.query(Agent.category).distinct().filter(Agent.category != None).all()
        return [category[0] for category in categories if category[0]]
    
    @staticmethod
    def get_tags(db: Session) -> List[str]:
        """
        获取所有标签
        """
        tags = db.query(AgentTag.tag).distinct().all()
        return [tag[0] for tag in tags]
    
    @staticmethod
    def search_agents(db: Session, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """
        搜索智能体
        
        返回值:
        - 规范化的字典列表，每个字典表示一个Agent对象
        """
        search_term = f"%{query}%"
        agents = db.query(Agent).filter(
            or_(
                Agent.name.ilike(search_term),
                Agent.description.ilike(search_term)
            )
        ).limit(limit).all()
        
        # 规范化每个Agent对象为字典
        normalized_agents = [AgentService.normalize_agent_for_response(agent) for agent in agents]
        
        return normalized_agents
    
    @staticmethod
    def get_user_favorites(db: Session, user_id: str, skip: int = 0, limit: int = 20) -> Tuple[List[Dict[str, Any]], int]:
        """
        获取用户收藏的智能体
        
        返回值:
        - 第一个元素是规范化的字典列表，每个字典表示一个Agent对象
        - 第二个元素是总记录数
        """
        query = db.query(Agent).join(UserFavorite).filter(UserFavorite.user_id == user_id)
        
        # 计算总数
        total = query.count()
        
        # 排序和分页
        query = query.order_by(desc(UserFavorite.created_at)).offset(skip).limit(limit)
        
        # 预加载相关数据
        query = query.options(
            joinedload(Agent.tags),
            joinedload(Agent.author)
        )
        
        agents = query.all()
        
        # 设置所有智能体为已收藏
        for agent in agents:
            setattr(agent, "is_favorite", True)
            
        # 规范化每个Agent对象为字典
        normalized_agents = [AgentService.normalize_agent_for_response(agent) for agent in agents]
        
        return normalized_agents, total
    
    @staticmethod
    def get_featured_agents(db: Session, limit: int = 4) -> List[Dict[str, Any]]:
        """
        获取推荐的智能体
        
        返回值:
        - 规范化的字典列表，每个字典表示一个Agent对象
        """
        query = db.query(Agent).filter(Agent.is_featured == True)
        
        # 按评分和使用次数排序
        query = query.order_by(desc(Agent.rating), desc(Agent.usage_count))
        
        # 限制数量
        query = query.limit(limit)
        
        # 预加载相关数据
        query = query.options(
            joinedload(Agent.tags),
            joinedload(Agent.author)
        )
        
        agents = query.all()
        
        # 规范化每个Agent对象为字典
        normalized_agents = [AgentService.normalize_agent_for_response(agent) for agent in agents]
        
        return normalized_agents
    
    @staticmethod
    def batch_toggle_favorite(db: Session, agent_ids: List[str], user_id: int, category: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        批量切换收藏状态
        参数:
            db: 数据库会话
            agent_ids: 智能体ID列表
            user_id: 用户ID
            category: 可选的收藏分类
        返回：
            操作结果列表，每项包含agent_id和is_favorite状态
        """
        results = []
        
        for agent_id in agent_ids:
            success, is_favorite = AgentService.toggle_favorite(db, agent_id, user_id, category)
            results.append({
                "agent_id": agent_id,
                "success": success,
                "is_favorite": is_favorite
            })
            
        return results 
    
    @staticmethod
    def generate_ai_response(db: Session, agent_id: str, session_id: str, user_message: str,
                           language_learning_mode: bool = False,
                           accent: Optional[str] = None,
                           language_level: Optional[str] = None) -> Optional[str]:
        """
        使用AI服务生成智能体回复
        
        参数:
            db: 数据库会话
            agent_id: 智能体ID
            session_id: 会话ID
            user_message: 用户消息内容
            language_learning_mode: 是否为语言学习模式
            accent: 语言口音 (如 'american', 'british', 'australian')
            language_level: 语言级别 (如 'beginner', 'intermediate', 'advanced')
            
        返回值:
            生成的AI回复，如果生成失败则返回None
        """
        try:
            # 获取智能体信息
            agent = db.query(Agent).filter(Agent.id == agent_id).first()
            if not agent:
                logger.error(f"生成AI回复失败: 智能体ID {agent_id} 不存在")
                return None
                
            # 获取会话历史消息
            messages = AgentService.get_messages(db, session_id)
            
            # 构建对话历史
            conversation_history = []
            for msg in messages:
                role = "assistant" if msg.sender_type == "agent" else "user"
                conversation_history.append({
                    "role": role,
                    "content": msg.content
                })
            
            # 确定系统提示词
            system_prompt = ""
            
            # 智能体类型判断 - 如果是语言学习类型的智能体，自动启用语言学习模式
            if agent.agent_type == "language_learning":
                language_learning_mode = True
                
                # 从agent.learning_settings获取默认设置
                if agent.learning_settings:
                    language = agent.learning_settings.get("language", "english")
                    if not accent and "default_accent" in agent.learning_settings:
                        accent = agent.learning_settings["default_accent"]
                    if not language_level and "default_level" in agent.learning_settings:
                        language_level = agent.learning_settings["default_level"]
                    
            # 语言学习模式
            if language_learning_mode:
                # 获取口音描述
                accent_desc = "标准" if not accent else {
                    "american": "美式",
                    "british": "英式",
                    "australian": "澳式",
                    "canadian": "加拿大式",
                    "indian": "印度式"
                }.get(accent.lower(), "标准")
                
                # 获取级别描述
                level_desc = {
                    "beginner": "初级",
                    "intermediate": "中级",
                    "advanced": "高级"
                }.get(language_level, "中级") if language_level else "中级"
                
                # 构建语言学习系统提示
                base_prompt = f"""你是一个名为{agent.name}的专业语言教师，专注于商务{agent.category or '英语'}教学。
你的教学风格是{accent_desc}口音，你正在与一位{level_desc}水平的学生对话。

教学要求:
1. 使用{accent_desc}口音的表达方式和词汇
2. 始终以{accent_desc}口音为基础回答问题
3. 根据学生{level_desc}水平调整语言难度
4. 保持友好、专业的语气
5. 提供语言学习建议和纠正

在每次回复后，请提供对学生表达的简短评估，包括:
### 评估
- 准确度: [0-100分]
- 流利度: [0-100分]
- 语法: [0-100分]
- 建议: [1-2条改进建议]

商务场景中，注重专业词汇、礼仪表达和商务习惯的讲解。根据需要提供例句和替代表达方式。
"""
                
                # 如果有关联的语料库，获取自定义语料
                custom_corpus = ""
                if agent.corpus_id:
                    try:
                        # 获取语料库信息
                        corpus = db.query(Corpus).filter(Corpus.corpus_id == agent.corpus_id).first()
                        if corpus:
                            # 获取语料条目
                            entries = db.query(CorpusEntry).filter(
                                CorpusEntry.corpus_id == agent.corpus_id,
                                CorpusEntry.language == agent.learning_settings.get("language", "english") if agent.learning_settings else "english"
                            ).limit(20).all()  # 限制条目数量避免提示词过长
                            
                            if entries:
                                custom_corpus = "\n\n以下是本次教学的重点语料和词汇，请在对话中有机会时自然地引入这些内容：\n"
                                for entry in entries:
                                    custom_corpus += f"- {entry.content}\n"
                    except Exception as e:
                        logger.error(f"获取自定义语料失败: {str(e)}")
                
                # 合并自定义语料到系统提示
                system_prompt = base_prompt + custom_corpus
                
            else:
                # 常规模式系统提示
                system_prompt = f"""你是一个名为{agent.name}的智能助手。
类别: {agent.category or '通用助手'}
简介: {agent.description or '我是一个乐于助人的助手，随时准备回答您的问题。'}

请以{agent.name}的身份回复用户。保持友好、专业和有帮助性。
回应要简洁、清晰，不要说"作为AI助手"等类似的话。
直接以第一人称回应用户的问题或请求。"""

            # 添加用户最新消息
            conversation_history.append({
                "role": "user",
                "content": user_message
            })
            
            # 调整温度参数 - 语言学习模式下降低随机性
            temperature = 0.5 if language_learning_mode else 0.7
            
            # 调用AI服务生成回复
            try:
                logger.info(f"调用AI服务生成回复: 会话ID {session_id}, 用户消息: '{user_message[:50]}...'")
                logger.info(f"模式: {'语言学习' if language_learning_mode else '标准'}, 口音: {accent}, 级别: {language_level}")
                
                response = ai_service.chat_completion(
                    messages=[{"role": "system", "content": system_prompt}] + conversation_history,
                    max_tokens=1500 if language_learning_mode else 1000,  # 语言学习模式需要更多标记来包含评估
                    temperature=temperature
                )
                
                # 提取回复文本
                if response and isinstance(response, dict):
                    if "text" in response:
                        # 使用ai_service.chat_completion自定义返回格式
                        return response["text"]
                    elif "message" in response and "content" in response["message"]:
                        # 使用标准OpenAI格式
                        return response["message"]["content"]
                    else:
                        logger.warning(f"AI服务返回了未知格式的响应: {response}")
                        return None
                else:
                    logger.warning(f"AI服务未返回有效响应: {response}")
                    return None
                    
            except Exception as e:
                logger.error(f"调用AI服务生成回复时出错: {str(e)}")
                return None
                
        except Exception as e:
            logger.error(f"生成AI回复时发生异常: {str(e)}")
            return None 