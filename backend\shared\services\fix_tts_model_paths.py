#!/usr/bin/env python
"""
TTS模型路径修复工具

此脚本用于修复TTS模型路径问题，包括：
1. 清除TTS下载缓存
2. 检测模型的实际下载位置
3. 创建必要的目录和链接
4. 确保模型文件存在于TTS期望的位置
"""

import os
import sys
import shutil
import logging
import glob
import json
import tempfile
from pathlib import Path
from typing import List, Dict, Optional, Tuple, Union

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger("tts_path_fixer")

def is_tts_installed() -> bool:
    """检查TTS库是否已安装"""
    try:
        import TTS
        return True
    except ImportError:
        logger.error("TTS库未安装，请先安装TTS库")
        return False

def get_tts_cache_dirs() -> List[str]:
    """获取TTS库可能的缓存目录"""
    # TTS库可能的缓存目录
    possible_cache_dirs = []
    
    try:
        # 尝试从TTS库获取
        if is_tts_installed():
            import TTS
            tts_dir = os.path.dirname(TTS.__file__)
            possible_cache_dirs.append(os.path.join(tts_dir, "tts_cache"))
            possible_cache_dirs.append(os.path.join(tts_dir, ".models"))
    except Exception as e:
        logger.warning(f"获取TTS缓存目录时出错: {e}")
    
    # 检查系统临时目录
    temp_dir = tempfile.gettempdir()
    possible_cache_dirs.append(os.path.join(temp_dir, "tts_cache"))
    possible_cache_dirs.append(os.path.join(temp_dir, ".tts_cache"))
    possible_cache_dirs.append(os.path.join(temp_dir, ".TTS"))
    
    # 检查用户主目录
    home_dir = os.path.expanduser("~")
    possible_cache_dirs.append(os.path.join(home_dir, ".tts_cache"))
    possible_cache_dirs.append(os.path.join(home_dir, ".TTS"))
    possible_cache_dirs.append(os.path.join(home_dir, ".local", "share", "tts"))
    
    # 检查当前工作目录
    possible_cache_dirs.append(os.path.join(os.getcwd(), ".tts_cache"))
    
    # 检查AppData目录（Windows）
    if sys.platform.startswith("win"):
        app_data = os.environ.get("APPDATA", "")
        if app_data:
            possible_cache_dirs.append(os.path.join(app_data, "TTS"))
            possible_cache_dirs.append(os.path.join(app_data, ".tts_cache"))
            
        local_app_data = os.environ.get("LOCALAPPDATA", "")
        if local_app_data:
            possible_cache_dirs.append(os.path.join(local_app_data, "TTS"))
            possible_cache_dirs.append(os.path.join(local_app_data, ".tts_cache"))
    
    # 检查.cache目录（Linux/Mac）
    if not sys.platform.startswith("win"):
        cache_dir = os.path.join(home_dir, ".cache")
        possible_cache_dirs.append(os.path.join(cache_dir, "tts"))
        possible_cache_dirs.append(os.path.join(cache_dir, "TTS"))
    
    # 过滤出实际存在的目录
    existing_dirs = [d for d in possible_cache_dirs if os.path.exists(d)]
    return existing_dirs

def clear_tts_cache():
    """清除TTS下载缓存"""
    cache_dirs = get_tts_cache_dirs()
    
    if not cache_dirs:
        logger.warning("未找到TTS缓存目录")
        return
    
    for cache_dir in cache_dirs:
        try:
            logger.info(f"清除TTS缓存目录: {cache_dir}")
            
            # 检查目录是否存在特定的缓存文件标记
            has_cache_files = False
            for ext in [".json", ".pth", ".config", ".model"]:
                if any(f.endswith(ext) for f in os.listdir(cache_dir) if os.path.isfile(os.path.join(cache_dir, f))):
                    has_cache_files = True
                    break
            
            if has_cache_files:
                # 目录包含缓存文件，清空它
                for item in os.listdir(cache_dir):
                    item_path = os.path.join(cache_dir, item)
                    try:
                        if os.path.isfile(item_path):
                            os.unlink(item_path)
                        elif os.path.isdir(item_path):
                            shutil.rmtree(item_path)
                    except Exception as e:
                        logger.warning(f"无法删除缓存项: {item_path}, 错误: {e}")
                
                logger.info(f"已清除TTS缓存目录: {cache_dir}")
            else:
                logger.info(f"目录不包含TTS缓存文件，跳过: {cache_dir}")
        except Exception as e:
            logger.error(f"清除缓存目录时出错: {cache_dir}, 错误: {e}")

def find_tts_models() -> Dict[str, List[str]]:
    """查找所有已下载的TTS模型文件"""
    result = {}
    
    # 搜索模型文件
    search_dirs = []
    
    # 添加当前项目的模型目录
    try:
        current_dir = os.path.dirname(os.path.abspath(__file__))
        backend_dir = os.path.dirname(current_dir)
        project_model_dir = os.path.join(backend_dir, "models", "tts")
        search_dirs.append(project_model_dir)
        
        # 可能的嵌套目录
        nested_tts_dir = os.path.join(project_model_dir, "tts")
        if os.path.exists(nested_tts_dir):
            search_dirs.append(nested_tts_dir)
    except Exception as e:
        logger.warning(f"添加项目模型目录时出错: {e}")
    
    # 添加TTS缓存目录
    search_dirs.extend(get_tts_cache_dirs())
    
    # 添加Python包目录
    try:
        if is_tts_installed():
            import TTS
            tts_dir = os.path.dirname(TTS.__file__)
            search_dirs.append(os.path.join(tts_dir, "models"))
    except Exception as e:
        logger.warning(f"添加TTS包目录时出错: {e}")
    
    # 搜索每个目录中的模型文件
    for search_dir in search_dirs:
        if not os.path.exists(search_dir):
            continue
            
        logger.info(f"搜索目录: {search_dir}")
        
        # 搜索模型文件
        try:
            # 搜索.pth文件
            pth_files = glob.glob(os.path.join(search_dir, "**", "*.pth"), recursive=True)
            # 搜索配置文件
            config_files = glob.glob(os.path.join(search_dir, "**", "config.json"), recursive=True)
            
            if pth_files:
                logger.info(f"在 {search_dir} 中找到 {len(pth_files)} 个.pth文件")
                if search_dir not in result:
                    result[search_dir] = []
                result[search_dir].extend(pth_files)
            
            if config_files:
                logger.info(f"在 {search_dir} 中找到 {len(config_files)} 个config.json文件")
                for config_file in config_files:
                    # 检查该配置文件是否与已知模型相关
                    try:
                        with open(config_file, 'r', encoding='utf-8') as f:
                            config = json.load(f)
                        if 'model' in config:
                            model_dir = os.path.dirname(config_file)
                            logger.info(f"找到模型配置: {config['model']} 在 {model_dir}")
                            
                            # 查找相关的.pth文件
                            model_pth_files = glob.glob(os.path.join(model_dir, "*.pth"))
                            if model_pth_files:
                                if search_dir not in result:
                                    result[search_dir] = []
                                result[search_dir].extend(model_pth_files)
                    except Exception as e:
                        logger.warning(f"处理配置文件时出错: {config_file}, 错误: {e}")
        except Exception as e:
            logger.warning(f"搜索目录时出错: {search_dir}, 错误: {e}")
    
    return result

def create_tts_directory_structure(models_dir: str) -> bool:
    """创建TTS期望的目录结构"""
    if not os.path.exists(models_dir):
        try:
            os.makedirs(models_dir, exist_ok=True)
            logger.info(f"创建模型根目录: {models_dir}")
        except Exception as e:
            logger.error(f"创建模型根目录失败: {models_dir}, 错误: {e}")
            return False
    
    # 创建tts子目录
    tts_dir = os.path.join(models_dir, "tts")
    if not os.path.exists(tts_dir):
        try:
            os.makedirs(tts_dir, exist_ok=True)
            logger.info(f"创建tts子目录: {tts_dir}")
        except Exception as e:
            logger.error(f"创建tts子目录失败: {tts_dir}, 错误: {e}")
            return False
    
    # 创建常用模型目录
    model_dirs = [
        os.path.join(tts_dir, "tts-models--zh-CN--baker--tacotron2-DDC-GST"),
        os.path.join(tts_dir, "tts-models--en--ljspeech--tacotron2-DDC"),
        os.path.join(tts_dir, "tts-models--multilingual--multi-dataset--your_tts")
    ]
    
    for model_dir in model_dirs:
        if not os.path.exists(model_dir):
            try:
                os.makedirs(model_dir, exist_ok=True)
                logger.info(f"创建模型目录: {model_dir}")
            except Exception as e:
                logger.error(f"创建模型目录失败: {model_dir}, 错误: {e}")
    
    return True

def get_expected_model_paths() -> Dict[str, str]:
    """获取TTS期望的模型路径映射"""
    models_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "models", "tts")
    tts_dir = os.path.join(models_dir, "tts")
    
    return {
        "zh-baker": os.path.join(tts_dir, "tts-models--zh-CN--baker--tacotron2-DDC-GST"),
        "en-ljspeech": os.path.join(tts_dir, "tts-models--en--ljspeech--tacotron2-DDC"),
        "multi-your_tts": os.path.join(tts_dir, "tts-models--multilingual--multi-dataset--your_tts")
    }

def identify_model_type(file_path: str) -> Optional[str]:
    """识别模型文件的类型"""
    file_name = os.path.basename(file_path)
    dir_name = os.path.basename(os.path.dirname(file_path))
    
    # 尝试从文件名判断
    if "baker" in file_name.lower() or "zh" in file_name.lower() or "chinese" in file_name.lower():
        return "zh-baker"
    elif "ljspeech" in file_name.lower() or "en_" in file_name.lower() or "english" in file_name.lower():
        return "en-ljspeech"
    elif "your_tts" in file_name.lower() or "your-tts" in file_name.lower():
        return "multi-your_tts"
    
    # 尝试从目录名判断
    if "baker" in dir_name.lower() or "zh" in dir_name.lower() or "chinese" in dir_name.lower():
        return "zh-baker"
    elif "ljspeech" in dir_name.lower() or "en_" in dir_name.lower() or "english" in dir_name.lower():
        return "en-ljspeech"
    elif "your_tts" in dir_name.lower() or "your-tts" in dir_name.lower():
        return "multi-your_tts"
    
    # 尝试读取配置文件
    config_path = os.path.join(os.path.dirname(file_path), "config.json")
    if os.path.exists(config_path):
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            if 'model_id' in config:
                model_id = config['model_id'].lower()
                if "baker" in model_id or "zh" in model_id or "chinese" in model_id:
                    return "zh-baker"
                elif "ljspeech" in model_id or "en_" in model_id or "english" in model_id:
                    return "en-ljspeech"
                elif "your_tts" in model_id or "your-tts" in model_id:
                    return "multi-your_tts"
        except Exception as e:
            logger.warning(f"读取配置文件时出错: {config_path}, 错误: {e}")
    
    return None

def copy_model_files(found_models: Dict[str, List[str]], force: bool = False) -> bool:
    """复制模型文件到TTS期望的位置"""
    expected_paths = get_expected_model_paths()
    copied_files = []
    
    # 确保目标目录结构存在
    models_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "models", "tts")
    create_tts_directory_structure(models_dir)
    
    # 遍历找到的模型文件
    for base_dir, model_files in found_models.items():
        for model_file in model_files:
            model_type = identify_model_type(model_file)
            
            if model_type and model_type in expected_paths:
                target_dir = expected_paths[model_type]
                
                # 检查目标目录是否存在
                if not os.path.exists(target_dir):
                    try:
                        os.makedirs(target_dir, exist_ok=True)
                    except Exception as e:
                        logger.error(f"创建目标目录失败: {target_dir}, 错误: {e}")
                        continue
                
                # 构建目标文件路径
                target_file = os.path.join(target_dir, os.path.basename(model_file))
                
                # 检查是否需要复制
                if not os.path.exists(target_file) or force:
                    try:
                        # 复制模型文件
                        shutil.copy2(model_file, target_file)
                        logger.info(f"已复制模型文件: {model_file} -> {target_file}")
                        copied_files.append(target_file)
                        
                        # 复制相关配置文件
                        config_file = os.path.join(os.path.dirname(model_file), "config.json")
                        if os.path.exists(config_file):
                            target_config = os.path.join(target_dir, "config.json")
                            if not os.path.exists(target_config) or force:
                                shutil.copy2(config_file, target_config)
                                logger.info(f"已复制配置文件: {config_file} -> {target_config}")
                    except Exception as e:
                        logger.error(f"复制文件失败: {model_file}, 错误: {e}")
                else:
                    logger.info(f"目标文件已存在，跳过: {target_file}")
    
    # 复制默认配置文件
    try:
        fix_config_script = os.path.join(os.path.dirname(os.path.abspath(__file__)), "fix_your_tts_config.py")
        if os.path.exists(fix_config_script):
            logger.info("运行YourTTS配置修复脚本...")
            import importlib.util
            spec = importlib.util.spec_from_file_location("fix_your_tts_config", fix_config_script)
            fix_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(fix_module)
            fix_module.fix_your_tts_config()
    except Exception as e:
        logger.warning(f"运行YourTTS配置修复脚本失败: {e}")
    
    return len(copied_files) > 0

def create_dummy_model_files() -> bool:
    """创建空白模型文件，作为最后的补救措施"""
    expected_paths = get_expected_model_paths()
    
    # 创建目录结构
    models_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "models", "tts")
    create_tts_directory_structure(models_dir)
    
    # 遍历期望路径
    created_count = 0
    for model_type, target_dir in expected_paths.items():
        # 检查目录是否为空
        if not os.path.exists(target_dir) or not os.listdir(target_dir):
            try:
                # 确保目录存在
                os.makedirs(target_dir, exist_ok=True)
                
                # 创建空白模型文件
                dummy_model = os.path.join(target_dir, "model.pth")
                if not os.path.exists(dummy_model):
                    with open(dummy_model, 'wb') as f:
                        # 写入一些字节，避免空文件
                        f.write(b'\x00' * 1024)
                    logger.info(f"已创建空白模型文件: {dummy_model}")
                    created_count += 1
                
                # 创建基本配置文件
                config_file = os.path.join(target_dir, "config.json")
                if not os.path.exists(config_file):
                    # 根据模型类型生成基本配置
                    if model_type == "zh-baker":
                        config = {
                            "model": "tacotron2", 
                            "model_id": "tts_models/zh-CN/baker/tacotron2-DDC-GST",
                            "num_chars": 149,
                            "characters": {
                                "characters_class": "TTS.tts.models.tacotron2.Characters",
                                "pad": "_", "eos": "~", "bos": "^", 
                                "characters": "",
                                "phonemes": "",
                                "punctuations": "!'(),-.:;? "
                            }
                        }
                    elif model_type == "en-ljspeech":
                        config = {
                            "model": "tacotron2", 
                            "model_id": "tts_models/en/ljspeech/tacotron2-DDC",
                            "num_chars": 100,
                            "characters": {
                                "characters_class": "TTS.tts.models.tacotron2.Characters",
                                "pad": "_", "eos": "~", "bos": "^", 
                                "characters": "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",
                                "phonemes": "",
                                "punctuations": "!'(),-.:;? "
                            }
                        }
                    else:  # multi-your_tts
                        config = {
                            "model": "your_tts",
                            "model_id": "tts_models/multilingual/multi-dataset/your_tts",
                            "num_chars": 149,
                            "characters": {
                                "characters_class": "TTS.tts.models.vits.VitsCharacters",
                                "pad": "_", "eos": "&", "bos": "*", 
                                "characters": "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz!'(),-.:;? ",
                                "phonemes": "",
                                "punctuations": "!'(),-.:;? "
                            }
                        }
                    
                    # 写入配置文件
                    with open(config_file, 'w', encoding='utf-8') as f:
                        json.dump(config, f, indent=4)
                    logger.info(f"已创建基本配置文件: {config_file}")
            except Exception as e:
                logger.error(f"创建空白模型文件失败: {target_dir}, 错误: {e}")
    
    return created_count > 0

def main():
    """主函数"""
    logger.info("开始修复TTS模型路径...")
    
    # 清除TTS缓存
    logger.info("步骤1: 清除TTS下载缓存...")
    clear_tts_cache()
    
    # 查找已下载的模型文件
    logger.info("步骤2: 搜索已下载的模型文件...")
    found_models = find_tts_models()
    
    # 创建TTS期望的目录结构
    logger.info("步骤3: 创建TTS期望的目录结构...")
    models_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "models", "tts")
    create_tts_directory_structure(models_dir)
    
    # 复制模型文件到TTS期望的位置
    logger.info("步骤4: 复制模型文件到TTS期望的位置...")
    copied = copy_model_files(found_models)
    
    if not copied:
        # 如果没有复制任何文件，创建空白模型文件作为最后补救措施
        logger.warning("未复制任何模型文件，尝试创建空白模型文件...")
        created = create_dummy_model_files()
        if created:
            logger.info("已创建空白模型文件，现在需要运行下载脚本获取实际模型")
        else:
            logger.warning("创建空白模型文件失败")
    
    logger.info("TTS模型路径修复完成")
    return True

if __name__ == "__main__":
    main() 