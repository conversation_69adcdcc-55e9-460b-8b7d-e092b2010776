"""
数字人生成分析和监控服务
提供性能监控、质量评估和优化建议
"""

import os
import time
import logging
import json
import asyncio
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
import cv2
import numpy as np
from PIL import Image
import psutil
import torch

logger = logging.getLogger(__name__)

@dataclass
class PerformanceMetrics:
    """性能指标"""
    task_id: str
    start_time: float
    end_time: Optional[float] = None
    processing_time: Optional[float] = None
    cpu_usage: List[float] = None
    memory_usage: List[float] = None
    gpu_usage: List[float] = None
    gpu_memory_usage: List[float] = None
    
    def __post_init__(self):
        if self.cpu_usage is None:
            self.cpu_usage = []
        if self.memory_usage is None:
            self.memory_usage = []
        if self.gpu_usage is None:
            self.gpu_usage = []
        if self.gpu_memory_usage is None:
            self.gpu_memory_usage = []

@dataclass
class QualityMetrics:
    """质量指标"""
    task_id: str
    video_path: str
    resolution: Tuple[int, int]
    fps: float
    duration: float
    file_size: int
    
    # 视频质量指标
    sharpness_score: Optional[float] = None
    brightness_score: Optional[float] = None
    contrast_score: Optional[float] = None
    noise_level: Optional[float] = None
    
    # 面部检测指标
    face_detection_confidence: Optional[float] = None
    face_landmarks_accuracy: Optional[float] = None
    lip_sync_accuracy: Optional[float] = None
    
    # 整体质量评分
    overall_quality_score: Optional[float] = None

class VideoQualityAnalyzer:
    """视频质量分析器"""
    
    def __init__(self):
        self.face_cascade = None
        self._load_face_detector()
    
    def _load_face_detector(self):
        """加载面部检测器"""
        try:
            # 尝试加载OpenCV的面部检测器
            cascade_path = cv2.data.haarcascades + 'haarcascade_frontalface_default.xml'
            if os.path.exists(cascade_path):
                self.face_cascade = cv2.CascadeClassifier(cascade_path)
                logger.info("面部检测器加载成功")
            else:
                logger.warning("面部检测器文件不存在")
        except Exception as e:
            logger.error(f"加载面部检测器失败: {e}")
    
    def analyze_video_quality(self, video_path: str, task_id: str) -> QualityMetrics:
        """
        分析视频质量
        
        Args:
            video_path: 视频文件路径
            task_id: 任务ID
            
        Returns:
            质量指标对象
        """
        try:
            # 获取基本视频信息
            cap = cv2.VideoCapture(video_path)
            
            if not cap.isOpened():
                raise ValueError(f"无法打开视频文件: {video_path}")
            
            # 基本属性
            fps = cap.get(cv2.CAP_PROP_FPS)
            frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            duration = frame_count / fps if fps > 0 else 0
            file_size = os.path.getsize(video_path)
            
            # 创建质量指标对象
            metrics = QualityMetrics(
                task_id=task_id,
                video_path=video_path,
                resolution=(width, height),
                fps=fps,
                duration=duration,
                file_size=file_size
            )
            
            # 分析视频帧质量
            self._analyze_frame_quality(cap, metrics)
            
            # 分析面部检测
            if self.face_cascade is not None:
                self._analyze_face_detection(cap, metrics)
            
            # 计算整体质量评分
            self._calculate_overall_quality(metrics)
            
            cap.release()
            return metrics
            
        except Exception as e:
            logger.error(f"视频质量分析失败: {e}")
            # 返回基本指标
            return QualityMetrics(
                task_id=task_id,
                video_path=video_path,
                resolution=(0, 0),
                fps=0,
                duration=0,
                file_size=0
            )
    
    def _analyze_frame_quality(self, cap: cv2.VideoCapture, metrics: QualityMetrics):
        """分析帧质量"""
        try:
            sharpness_scores = []
            brightness_scores = []
            contrast_scores = []
            
            # 采样分析（每10帧分析一次）
            frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            sample_interval = max(1, frame_count // 20)  # 最多分析20帧
            
            for i in range(0, frame_count, sample_interval):
                cap.set(cv2.CAP_PROP_POS_FRAMES, i)
                ret, frame = cap.read()
                
                if not ret:
                    break
                
                # 转换为灰度图
                gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
                
                # 计算锐度（拉普拉斯方差）
                sharpness = cv2.Laplacian(gray, cv2.CV_64F).var()
                sharpness_scores.append(sharpness)
                
                # 计算亮度
                brightness = np.mean(gray)
                brightness_scores.append(brightness)
                
                # 计算对比度
                contrast = np.std(gray)
                contrast_scores.append(contrast)
            
            # 计算平均值
            if sharpness_scores:
                metrics.sharpness_score = np.mean(sharpness_scores)
                metrics.brightness_score = np.mean(brightness_scores)
                metrics.contrast_score = np.mean(contrast_scores)
                
                # 估算噪声水平（基于锐度的倒数）
                metrics.noise_level = 1.0 / (metrics.sharpness_score + 1.0)
            
        except Exception as e:
            logger.error(f"帧质量分析失败: {e}")
    
    def _analyze_face_detection(self, cap: cv2.VideoCapture, metrics: QualityMetrics):
        """分析面部检测质量"""
        try:
            face_confidences = []
            
            # 采样分析面部检测
            frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            sample_interval = max(1, frame_count // 10)  # 分析10帧
            
            for i in range(0, frame_count, sample_interval):
                cap.set(cv2.CAP_PROP_POS_FRAMES, i)
                ret, frame = cap.read()
                
                if not ret:
                    break
                
                gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
                
                # 检测面部
                faces = self.face_cascade.detectMultiScale(
                    gray, 
                    scaleFactor=1.1, 
                    minNeighbors=5, 
                    minSize=(30, 30)
                )
                
                # 计算面部检测置信度（基于检测到的面部数量和大小）
                if len(faces) > 0:
                    # 选择最大的面部
                    largest_face = max(faces, key=lambda x: x[2] * x[3])
                    face_area = largest_face[2] * largest_face[3]
                    frame_area = gray.shape[0] * gray.shape[1]
                    confidence = min(1.0, face_area / (frame_area * 0.1))  # 面部应占帧的至少10%
                    face_confidences.append(confidence)
                else:
                    face_confidences.append(0.0)
            
            if face_confidences:
                metrics.face_detection_confidence = np.mean(face_confidences)
                # 简单的唇同步准确性估算（基于面部检测稳定性）
                metrics.lip_sync_accuracy = 1.0 - np.std(face_confidences)
                metrics.face_landmarks_accuracy = metrics.face_detection_confidence * 0.8
            
        except Exception as e:
            logger.error(f"面部检测分析失败: {e}")
    
    def _calculate_overall_quality(self, metrics: QualityMetrics):
        """计算整体质量评分"""
        try:
            scores = []
            
            # 锐度评分 (0-100)
            if metrics.sharpness_score is not None:
                sharpness_normalized = min(100, max(0, metrics.sharpness_score / 1000 * 100))
                scores.append(sharpness_normalized)
            
            # 亮度评分 (0-100)
            if metrics.brightness_score is not None:
                # 理想亮度范围 100-150
                brightness_score = 100 - abs(metrics.brightness_score - 125) / 125 * 100
                brightness_score = max(0, brightness_score)
                scores.append(brightness_score)
            
            # 对比度评分 (0-100)
            if metrics.contrast_score is not None:
                contrast_normalized = min(100, metrics.contrast_score / 50 * 100)
                scores.append(contrast_normalized)
            
            # 面部检测评分 (0-100)
            if metrics.face_detection_confidence is not None:
                face_score = metrics.face_detection_confidence * 100
                scores.append(face_score)
            
            # 分辨率评分 (0-100)
            width, height = metrics.resolution
            resolution_score = min(100, (width * height) / (1920 * 1080) * 100)
            scores.append(resolution_score)
            
            # 计算加权平均
            if scores:
                metrics.overall_quality_score = np.mean(scores)
            else:
                metrics.overall_quality_score = 0.0
                
        except Exception as e:
            logger.error(f"整体质量评分计算失败: {e}")
            metrics.overall_quality_score = 0.0

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.active_monitors = {}
        self.gpu_available = torch.cuda.is_available()
    
    def start_monitoring(self, task_id: str) -> PerformanceMetrics:
        """开始监控任务性能"""
        metrics = PerformanceMetrics(
            task_id=task_id,
            start_time=time.time()
        )
        
        self.active_monitors[task_id] = {
            'metrics': metrics,
            'monitoring': True
        }
        
        # 启动监控线程
        asyncio.create_task(self._monitor_task(task_id))
        
        return metrics
    
    async def _monitor_task(self, task_id: str):
        """监控任务的资源使用情况"""
        try:
            monitor_data = self.active_monitors.get(task_id)
            if not monitor_data:
                return
            
            metrics = monitor_data['metrics']
            
            while monitor_data.get('monitoring', False):
                # CPU使用率
                cpu_percent = psutil.cpu_percent(interval=1)
                metrics.cpu_usage.append(cpu_percent)
                
                # 内存使用率
                memory = psutil.virtual_memory()
                metrics.memory_usage.append(memory.percent)
                
                # GPU使用率（如果可用）
                if self.gpu_available:
                    try:
                        gpu_memory = torch.cuda.memory_allocated() / torch.cuda.max_memory_allocated() * 100
                        metrics.gpu_memory_usage.append(gpu_memory)
                        # 简单的GPU使用率估算
                        metrics.gpu_usage.append(min(100, gpu_memory))
                    except:
                        pass
                
                await asyncio.sleep(2)  # 每2秒采样一次
                
        except Exception as e:
            logger.error(f"性能监控失败: {e}")
    
    def stop_monitoring(self, task_id: str) -> Optional[PerformanceMetrics]:
        """停止监控并返回性能指标"""
        monitor_data = self.active_monitors.get(task_id)
        if not monitor_data:
            return None
        
        metrics = monitor_data['metrics']
        metrics.end_time = time.time()
        metrics.processing_time = metrics.end_time - metrics.start_time
        
        # 停止监控
        monitor_data['monitoring'] = False
        del self.active_monitors[task_id]
        
        return metrics

class DigitalHumanAnalytics:
    """数字人生成分析服务"""
    
    def __init__(self):
        self.quality_analyzer = VideoQualityAnalyzer()
        self.performance_monitor = PerformanceMonitor()
        self.analytics_data = {}
    
    def start_task_analysis(self, task_id: str) -> PerformanceMetrics:
        """开始任务分析"""
        return self.performance_monitor.start_monitoring(task_id)
    
    def complete_task_analysis(self, task_id: str, video_path: str) -> Dict[str, Any]:
        """完成任务分析"""
        # 停止性能监控
        performance_metrics = self.performance_monitor.stop_monitoring(task_id)
        
        # 分析视频质量
        quality_metrics = self.quality_analyzer.analyze_video_quality(video_path, task_id)
        
        # 生成分析报告
        report = self._generate_analysis_report(performance_metrics, quality_metrics)
        
        # 保存分析数据
        self.analytics_data[task_id] = {
            'performance': asdict(performance_metrics) if performance_metrics else None,
            'quality': asdict(quality_metrics),
            'report': report,
            'timestamp': datetime.now().isoformat()
        }
        
        return report
    
    def _generate_analysis_report(self, performance: Optional[PerformanceMetrics], quality: QualityMetrics) -> Dict[str, Any]:
        """生成分析报告"""
        report = {
            'task_id': quality.task_id,
            'timestamp': datetime.now().isoformat(),
            'quality_summary': {
                'overall_score': quality.overall_quality_score or 0,
                'resolution': quality.resolution,
                'duration': quality.duration,
                'file_size_mb': quality.file_size / (1024 * 1024) if quality.file_size else 0
            },
            'recommendations': []
        }
        
        # 性能摘要
        if performance:
            report['performance_summary'] = {
                'processing_time': performance.processing_time,
                'avg_cpu_usage': np.mean(performance.cpu_usage) if performance.cpu_usage else 0,
                'avg_memory_usage': np.mean(performance.memory_usage) if performance.memory_usage else 0,
                'avg_gpu_usage': np.mean(performance.gpu_usage) if performance.gpu_usage else 0
            }
        
        # 生成优化建议
        self._generate_recommendations(report, performance, quality)
        
        return report
    
    def _generate_recommendations(self, report: Dict[str, Any], performance: Optional[PerformanceMetrics], quality: QualityMetrics):
        """生成优化建议"""
        recommendations = []
        
        # 质量相关建议
        if quality.overall_quality_score and quality.overall_quality_score < 70:
            recommendations.append({
                'type': 'quality',
                'priority': 'high',
                'message': '视频质量较低，建议提高输入图像质量或使用更高的生成参数'
            })
        
        if quality.sharpness_score and quality.sharpness_score < 100:
            recommendations.append({
                'type': 'quality',
                'priority': 'medium',
                'message': '视频锐度不足，建议启用图像增强功能'
            })
        
        if quality.face_detection_confidence and quality.face_detection_confidence < 0.8:
            recommendations.append({
                'type': 'quality',
                'priority': 'high',
                'message': '面部检测置信度较低，建议使用更清晰的面部图像'
            })
        
        # 性能相关建议
        if performance and performance.processing_time and performance.processing_time > 300:  # 5分钟
            recommendations.append({
                'type': 'performance',
                'priority': 'medium',
                'message': '处理时间较长，建议使用GPU加速或降低质量设置'
            })
        
        if performance and performance.cpu_usage and np.mean(performance.cpu_usage) > 90:
            recommendations.append({
                'type': 'performance',
                'priority': 'medium',
                'message': 'CPU使用率过高，建议减少并发任务或升级硬件'
            })
        
        report['recommendations'] = recommendations
    
    def get_analytics_data(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取分析数据"""
        return self.analytics_data.get(task_id)
    
    def get_analytics_summary(self, days: int = 7) -> Dict[str, Any]:
        """获取分析摘要"""
        cutoff_date = datetime.now() - timedelta(days=days)
        
        recent_data = [
            data for data in self.analytics_data.values()
            if datetime.fromisoformat(data['timestamp']) > cutoff_date
        ]
        
        if not recent_data:
            return {'message': '没有最近的分析数据'}
        
        # 计算统计信息
        quality_scores = [data['quality']['overall_quality_score'] for data in recent_data if data['quality']['overall_quality_score']]
        processing_times = [data['performance']['processing_time'] for data in recent_data if data['performance'] and data['performance']['processing_time']]
        
        summary = {
            'total_tasks': len(recent_data),
            'avg_quality_score': np.mean(quality_scores) if quality_scores else 0,
            'avg_processing_time': np.mean(processing_times) if processing_times else 0,
            'quality_distribution': {
                'excellent': len([s for s in quality_scores if s >= 90]),
                'good': len([s for s in quality_scores if 70 <= s < 90]),
                'fair': len([s for s in quality_scores if 50 <= s < 70]),
                'poor': len([s for s in quality_scores if s < 50])
            }
        }
        
        return summary

# 全局分析服务实例
_analytics_service = None

def get_digital_human_analytics() -> DigitalHumanAnalytics:
    """获取数字人分析服务实例"""
    global _analytics_service
    if _analytics_service is None:
        _analytics_service = DigitalHumanAnalytics()
    return _analytics_service
