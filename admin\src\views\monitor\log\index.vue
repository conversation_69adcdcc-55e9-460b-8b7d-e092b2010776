<template>
  <div class="log-container">
    <!-- 搜索区域 -->
    <a-card :bordered="false" class="search-card">
      <a-form layout="inline" :model="queryParams">
        <a-form-item label="用户名">
          <a-input
            v-model:value="queryParams.username"
            placeholder="请输入用户名"
            allow-clear
          />
        </a-form-item>
        <a-form-item label="操作类型">
          <a-select
            v-model:value="queryParams.operation"
            placeholder="请选择操作类型"
            style="width: 180px"
            allow-clear
          >
            <a-select-option value="登录">登录</a-select-option>
            <a-select-option value="登出">登出</a-select-option>
            <a-select-option value="新增">新增</a-select-option>
            <a-select-option value="修改">修改</a-select-option>
            <a-select-option value="删除">删除</a-select-option>
            <a-select-option value="查询">查询</a-select-option>
            <a-select-option value="导出">导出</a-select-option>
            <a-select-option value="导入">导入</a-select-option>
            <a-select-option value="其他">其他</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="操作模块">
          <a-select
            v-model:value="queryParams.module"
            placeholder="请选择操作模块"
            style="width: 180px"
            allow-clear
          >
            <a-select-option value="用户管理">用户管理</a-select-option>
            <a-select-option value="角色管理">角色管理</a-select-option>
            <a-select-option value="菜单管理">菜单管理</a-select-option>
            <a-select-option value="系统监控">系统监控</a-select-option>
            <a-select-option value="数据字典">数据字典</a-select-option>
            <a-select-option value="系统设置">系统设置</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="操作时间">
          <a-range-picker
            v-model:value="dateRange"
            format="YYYY-MM-DD"
            @change="handleDateRangeChange"
            style="width: 240px"
          />
        </a-form-item>
        <a-form-item>
          <a-button type="primary" @click="handleSearch">
            <template #icon><search-outlined /></template>
            搜索
          </a-button>
          <a-button style="margin-left: 8px" @click="handleReset">
            <template #icon><clear-outlined /></template>
            重置
          </a-button>
        </a-form-item>
      </a-form>
    </a-card>
    
    <!-- 统计卡片 -->
    <div class="stat-cards">
      <a-card :bordered="false" :loading="statLoading">
        <a-statistic 
          title="今日操作" 
          :value="statistics.today" 
          :value-style="{ color: '#1890ff' }"
        />
        <template #extra>
          <file-outlined />
        </template>
      </a-card>
      <a-card :bordered="false" :loading="statLoading">
        <a-statistic 
          title="本周操作" 
          :value="statistics.week" 
          :value-style="{ color: '#52c41a' }"
        />
        <template #extra>
          <clock-circle-outlined />
        </template>
      </a-card>
      <a-card :bordered="false" :loading="statLoading">
        <a-statistic 
          title="本月操作" 
          :value="statistics.month" 
          :value-style="{ color: '#faad14' }"
        />
        <template #extra>
          <calendar-outlined />
        </template>
      </a-card>
      <a-card :bordered="false" :loading="statLoading">
        <a-statistic 
          title="操作用户数" 
          :value="statistics.userCount" 
          :value-style="{ color: '#f5222d' }"
        />
        <template #extra>
          <user-outlined />
        </template>
      </a-card>
    </div>
    
    <!-- 操作按钮栏 -->
    <a-card :bordered="false" class="table-card">
      <template #title>
        <div class="table-header">
          <div class="left">
            <a-button type="primary" @click="handleExport">
              <template #icon><download-outlined /></template>
              导出
            </a-button>
            <a-button 
              style="margin-left: 8px" 
              danger 
              :disabled="selectedRowKeys.length === 0"
              @click="handleBatchDelete"
            >
              <template #icon><delete-outlined /></template>
              批量删除
            </a-button>
            <a-button 
              style="margin-left: 8px" 
              @click="handleClear"
            >
              <template #icon><clear-outlined /></template>
              清空日志
            </a-button>
          </div>
          <div class="right">
            <a-radio-group v-model:value="logType" button-style="solid" @change="handleLogTypeChange">
              <a-radio-button value="operation">操作日志</a-radio-button>
              <a-radio-button value="login">登录日志</a-radio-button>
              <a-radio-button value="api">API调用</a-radio-button>
              <a-radio-button value="error">错误日志</a-radio-button>
            </a-radio-group>
          </div>
        </div>
      </template>
      
      <!-- 操作日志表格 -->
      <a-table
        :columns="columns"
        :data-source="dataSource"
        :pagination="pagination"
        :loading="loading"
        :row-key="record => record.id"
        :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
        @change="handleTableChange"
      >
        <!-- 表格列自定义渲染 -->
        <template #bodyCell="{ column, text, record }">
          <!-- 操作类型列 -->
          <template v-if="column.dataIndex === 'operation'">
            <a-tag 
              :color="getOperationTypeColor(text)"
            >
              {{ text }}
            </a-tag>
          </template>
          
          <!-- 状态列 -->
          <template v-else-if="column.dataIndex === 'status'">
            <a-badge 
              :status="text === '成功' ? 'success' : 'error'" 
              :text="text" 
            />
          </template>
          
          <!-- 操作列 -->
          <template v-else-if="column.dataIndex === 'action'">
            <a @click="handleView(record)">查看</a>
            <a-divider type="vertical" />
            <a @click="handleDelete(record)">删除</a>
          </template>
        </template>
      </a-table>
    </a-card>
    
    <!-- 日志详情抽屉 -->
    <a-drawer
      v-model:visible="drawerVisible"
      title="日志详情"
      width="550px"
      :body-style="{ paddingBottom: '80px' }"
    >
      <a-descriptions :column="1" bordered>
        <a-descriptions-item label="操作ID">
          {{ currentLog.id }}
        </a-descriptions-item>
        <a-descriptions-item label="用户名">
          {{ currentLog.username }}
        </a-descriptions-item>
        <a-descriptions-item label="IP地址">
          {{ currentLog.ip }}
        </a-descriptions-item>
        <a-descriptions-item label="操作类型">
          <a-tag :color="getOperationTypeColor(currentLog.operation)">
            {{ currentLog.operation }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="操作模块">
          {{ currentLog.module }}
        </a-descriptions-item>
        <a-descriptions-item label="操作描述">
          {{ currentLog.description }}
        </a-descriptions-item>
        <a-descriptions-item label="操作方法">
          <a-typography-paragraph copyable>
            {{ currentLog.method }}
          </a-typography-paragraph>
        </a-descriptions-item>
        <a-descriptions-item label="请求参数">
          <a-collapse>
            <a-collapse-panel key="1" header="请求参数详情">
              <pre class="json-content">{{ formatJson(currentLog.params) }}</pre>
            </a-collapse-panel>
          </a-collapse>
        </a-descriptions-item>
        <a-descriptions-item label="请求结果">
          <a-badge 
            :status="currentLog.status === '成功' ? 'success' : 'error'" 
            :text="currentLog.status" 
          />
        </a-descriptions-item>
        <a-descriptions-item label="耗时">
          {{ currentLog.time }}ms
        </a-descriptions-item>
        <a-descriptions-item label="操作时间">
          {{ currentLog.createTime }}
        </a-descriptions-item>
        <a-descriptions-item v-if="currentLog.error" label="错误信息">
          <div class="error-message">{{ currentLog.error }}</div>
        </a-descriptions-item>
      </a-descriptions>
      
      <div class="drawer-footer">
        <a-button @click="drawerVisible = false">关闭</a-button>
        <a-button type="primary" style="margin-left: 8px" @click="handleExportDetail">
          导出详情
        </a-button>
      </div>
    </a-drawer>
    
    <!-- 清空日志确认对话框 -->
    <a-modal
      v-model:visible="clearModalVisible"
      title="清空日志"
      @ok="confirmClear"
      :confirm-loading="clearLoading"
    >
      <div class="clear-log-form">
        <p class="warning-text">清空日志操作不可恢复，请谨慎操作！</p>
        <a-form layout="vertical">
          <a-form-item label="日志类型">
            <a-select v-model:value="clearParams.type" style="width: 100%">
              <a-select-option value="all">所有日志</a-select-option>
              <a-select-option value="operation">操作日志</a-select-option>
              <a-select-option value="login">登录日志</a-select-option>
              <a-select-option value="api">API调用日志</a-select-option>
              <a-select-option value="error">错误日志</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="清除时间范围">
            <a-select v-model:value="clearParams.timeRange" style="width: 100%">
              <a-select-option value="all">所有</a-select-option>
              <a-select-option value="7">一周前</a-select-option>
              <a-select-option value="30">一个月前</a-select-option>
              <a-select-option value="90">三个月前</a-select-option>
              <a-select-option value="180">六个月前</a-select-option>
            </a-select>
          </a-form-item>
        </a-form>
      </div>
    </a-modal>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, computed, onMounted } from 'vue';
import { message, Modal } from 'ant-design-vue';
import {
  SearchOutlined,
  ClearOutlined,
  DeleteOutlined,
  DownloadOutlined,
  FileOutlined,
  ClockCircleOutlined,
  CalendarOutlined,
  UserOutlined
} from '@ant-design/icons-vue';
import {
  getOperationLogs,
  getLoginLogs,
  getApiLogs,
  getErrorLogs,
  getLogStats,
  deleteLog,
  clearLogs,
  exportLogs
} from '@/api/logs';
import useTable from '@/hooks/useTable';
import dayjs from 'dayjs';

export default defineComponent({
  name: 'LogMonitor',
  components: {
    SearchOutlined,
    ClearOutlined,
    DeleteOutlined,
    DownloadOutlined,
    FileOutlined,
    ClockCircleOutlined,
    CalendarOutlined,
    UserOutlined
  },
  setup() {
    // 日志类型
    const logType = ref('operation');
    
    // 日期范围
    const dateRange = ref([]);
    
    // 统计数据
    const statistics = reactive({
      today: 0,
      week: 0,
      month: 0,
      userCount: 0
    });
    const statLoading = ref(false);
    
    // 查询参数
    const queryParams = reactive({
      username: '',
      operation: undefined,
      module: undefined,
      timeRange: undefined
    });
    
    // 清空日志参数
    const clearParams = reactive({
      type: 'operation',
      timeRange: '30'
    });
    const clearModalVisible = ref(false);
    const clearLoading = ref(false);
    
    // 日志详情抽屉
    const drawerVisible = ref(false);
    const currentLog = reactive({});
    
    // 根据日志类型获取不同的表格列
    const columns = computed(() => {
      const baseColumns = [
        {
          title: '序号',
          dataIndex: 'index',
          width: 80,
          customRender: ({ index }) => index + 1
        },
        {
          title: '用户名',
          dataIndex: 'username',
          ellipsis: true,
          width: 120
        },
        {
          title: 'IP地址',
          dataIndex: 'ip',
          width: 140
        },
        {
          title: '操作时间',
          dataIndex: 'createTime',
          width: 170,
          sorter: true
        }
      ];
      
      // 根据日志类型返回不同的列配置
      if (logType.value === 'operation') {
        return [
          ...baseColumns,
          {
            title: '操作类型',
            dataIndex: 'operation',
            width: 120
          },
          {
            title: '操作模块',
            dataIndex: 'module',
            ellipsis: true,
            width: 120
          },
          {
            title: '操作描述',
            dataIndex: 'description',
            ellipsis: true
          },
          {
            title: '状态',
            dataIndex: 'status',
            width: 100
          },
          {
            title: '耗时(ms)',
            dataIndex: 'time',
            width: 100,
            sorter: true
          },
          {
            title: '操作',
            dataIndex: 'action',
            width: 120,
            fixed: 'right'
          }
        ];
      } else if (logType.value === 'login') {
        return [
          ...baseColumns,
          {
            title: '浏览器',
            dataIndex: 'browser',
            ellipsis: true,
            width: 180
          },
          {
            title: '操作系统',
            dataIndex: 'os',
            ellipsis: true,
            width: 180
          },
          {
            title: '状态',
            dataIndex: 'status',
            width: 100
          },
          {
            title: '消息',
            dataIndex: 'message',
            ellipsis: true
          },
          {
            title: '操作',
            dataIndex: 'action',
            width: 120,
            fixed: 'right'
          }
        ];
      } else if (logType.value === 'api') {
        return [
          ...baseColumns,
          {
            title: '请求方法',
            dataIndex: 'method',
            width: 100
          },
          {
            title: '请求路径',
            dataIndex: 'path',
            ellipsis: true
          },
          {
            title: '状态码',
            dataIndex: 'status',
            width: 100
          },
          {
            title: '耗时(ms)',
            dataIndex: 'time',
            width: 100,
            sorter: true
          },
          {
            title: '操作',
            dataIndex: 'action',
            width: 120,
            fixed: 'right'
          }
        ];
      } else if (logType.value === 'error') {
        return [
          ...baseColumns,
          {
            title: '错误类型',
            dataIndex: 'type',
            width: 150
          },
          {
            title: '错误信息',
            dataIndex: 'message',
            ellipsis: true
          },
          {
            title: '操作',
            dataIndex: 'action',
            width: 120,
            fixed: 'right'
          }
        ];
      }
      
      return baseColumns;
    });
    
    // 使用表格钩子
    const { 
      dataSource, 
      pagination, 
      loading, 
      selectedRowKeys, 
      getList, 
      handleTableChange, 
      search, 
      resetSearch, 
      onSelectChange 
    } = useTable({
      fetchData: params => {
        // 根据日志类型调用不同的API
        switch (logType.value) {
          case 'operation':
            return getOperationLogs(params);
          case 'login':
            return getLoginLogs(params);
          case 'api':
            return getApiLogs(params);
          case 'error':
            return getErrorLogs(params);
          default:
            return getOperationLogs(params);
        }
      },
      defaultParams: { pageSize: 10 }
    });
    
    // 获取统计数据
    const fetchStatistics = async () => {
      statLoading.value = true;
      
      try {
        const res = await getLogStats({ type: logType.value });
        
        if (res && res.data) {
          statistics.today = res.data.today || 0;
          statistics.week = res.data.week || 0;
          statistics.month = res.data.month || 0;
          statistics.userCount = res.data.userCount || 0;
        }
      } catch (error) {
        console.error('获取日志统计失败:', error);
        message.error('获取日志统计失败');
      } finally {
        statLoading.value = false;
      }
    };
    
    // 处理日期范围变化
    const handleDateRangeChange = (dates, dateStrings) => {
      if (dates && dates.length === 2) {
        queryParams.timeRange = `${dateStrings[0]},${dateStrings[1]}`;
      } else {
        queryParams.timeRange = undefined;
      }
    };
    
    // 搜索
    const handleSearch = () => {
      search(queryParams);
    };
    
    // 重置
    const handleReset = () => {
      dateRange.value = [];
      resetSearch();
    };
    
    // 导出
    const handleExport = async () => {
      try {
        message.loading('正在导出数据...');
        await exportLogs({
          type: logType.value,
          ...queryParams
        });
        message.success('导出成功');
      } catch (error) {
        console.error('导出失败:', error);
        message.error('导出失败');
      }
    };
    
    // 导出详情
    const handleExportDetail = () => {
      if (!currentLog.id) return;
      
      const content = formatJson(currentLog);
      const blob = new Blob([content], { type: 'text/plain' });
      const link = document.createElement('a');
      link.href = URL.createObjectURL(blob);
      link.download = `log_${currentLog.id}.json`;
      link.click();
      URL.revokeObjectURL(link.href);
    };
    
    // 查看日志详情
    const handleView = (record) => {
      Object.assign(currentLog, record);
      drawerVisible.value = true;
    };
    
    // 删除日志
    const handleDelete = (record) => {
      Modal.confirm({
        title: '确认删除',
        content: '确定要删除该条日志吗？',
        okText: '确定',
        okType: 'danger',
        cancelText: '取消',
        async onOk() {
          try {
            await deleteLog(record.id);
            message.success('删除成功');
            getList();
          } catch (error) {
            console.error('删除失败:', error);
            message.error('删除失败');
          }
        }
      });
    };
    
    // 批量删除
    const handleBatchDelete = () => {
      if (selectedRowKeys.value.length === 0) {
        message.warning('请选择要删除的日志');
        return;
      }
      
      Modal.confirm({
        title: '确认批量删除',
        content: `确定要删除选中的 ${selectedRowKeys.value.length} 条日志吗？`,
        okText: '确定',
        okType: 'danger',
        cancelText: '取消',
        async onOk() {
          message.loading('正在删除...');
          try {
            // 实际应用中应该调用批量删除的API
            await Promise.all(selectedRowKeys.value.map(id => deleteLog(id)));
            message.success('删除成功');
            getList();
          } catch (error) {
            console.error('批量删除失败:', error);
            message.error('批量删除失败');
          }
        }
      });
    };
    
    // 打开清空日志对话框
    const handleClear = () => {
      clearParams.type = logType.value;
      clearParams.timeRange = '30';
      clearModalVisible.value = true;
    };
    
    // 确认清空日志
    const confirmClear = async () => {
      clearLoading.value = true;
      
      try {
        await clearLogs({
          type: clearParams.type,
          before: clearParams.timeRange !== 'all' ? dayjs().subtract(parseInt(clearParams.timeRange), 'day').format('YYYY-MM-DD') : undefined
        });
        
        message.success('清空日志成功');
        clearModalVisible.value = false;
        getList();
        fetchStatistics();
      } catch (error) {
        console.error('清空日志失败:', error);
        message.error('清空日志失败');
      } finally {
        clearLoading.value = false;
      }
    };
    
    // 日志类型变化处理
    const handleLogTypeChange = () => {
      // 重置分页
      pagination.current = 1;
      // 重新获取数据
      getList();
      // 获取统计数据
      fetchStatistics();
    };
    
    // 根据操作类型获取标签颜色
    const getOperationTypeColor = (type) => {
      if (!type) return 'default';
      
      const colorMap = {
        '登录': 'blue',
        '登出': 'cyan',
        '新增': 'green',
        '修改': 'orange',
        '删除': 'red',
        '导出': 'purple',
        '导入': 'gold',
        '查询': 'lime'
      };
      
      return colorMap[type] || 'default';
    };
    
    // 格式化JSON
    const formatJson = (json) => {
      try {
        if (typeof json === 'string') {
          return JSON.stringify(JSON.parse(json), null, 2);
        }
        return JSON.stringify(json, null, 2);
      } catch (e) {
        return json;
      }
    };
    
    // 初始化
    onMounted(() => {
      getList();
      fetchStatistics();
    });
    
    return {
      // 数据
      logType,
      dateRange,
      statistics,
      statLoading,
      queryParams,
      columns,
      dataSource,
      pagination,
      loading,
      selectedRowKeys,
      drawerVisible,
      currentLog,
      clearModalVisible,
      clearLoading,
      clearParams,
      
      // 方法
      handleSearch,
      handleReset,
      handleExport,
      handleExportDetail,
      handleView,
      handleDelete,
      handleBatchDelete,
      handleClear,
      confirmClear,
      handleDateRangeChange,
      handleTableChange,
      handleLogTypeChange,
      onSelectChange,
      getOperationTypeColor,
      formatJson
    };
  }
});
</script>

<style scoped>
.log-container {
  padding: 16px;
}

.search-card {
  margin-bottom: 16px;
}

.stat-cards {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.stat-cards .ant-card {
  flex: 1;
  min-width: 200px;
}

.table-card {
  margin-bottom: 16px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.json-content {
  white-space: pre-wrap;
  word-break: break-all;
  max-height: 300px;
  overflow: auto;
  font-family: Consolas, Monaco, monospace;
  font-size: 12px;
  background-color: #f5f5f5;
  padding: 8px;
  border-radius: 4px;
}

.drawer-footer {
  position: absolute;
  bottom: 0;
  width: 100%;
  border-top: 1px solid #e8e8e8;
  padding: 10px 16px;
  text-align: right;
  left: 0;
  background: #fff;
}

.warning-text {
  color: #ff4d4f;
  font-weight: bold;
  margin-bottom: 16px;
}

.error-message {
  color: #ff4d4f;
  max-height: 200px;
  overflow: auto;
  white-space: pre-wrap;
  background-color: #fff2f0;
  padding: 8px;
  border-radius: 4px;
  border: 1px solid #ffccc7;
}
</style> 