#!/usr/bin/env python3
"""
下载 MuseTalk 需要的 Whisper 模型
"""

import os
import sys
import requests
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def download_whisper_model():
    """下载 Whisper 模型文件"""
    try:
        # 设置路径
        base_dir = Path(__file__).parent
        whisper_dir = base_dir / "models" / "whisper"
        
        # 确保目录存在
        whisper_dir.mkdir(parents=True, exist_ok=True)
        
        # Whisper tiny 模型文件
        model_files = {
            "pytorch_model.bin": "https://huggingface.co/openai/whisper-tiny/resolve/main/pytorch_model.bin",
            "config.json": "https://huggingface.co/openai/whisper-tiny/resolve/main/config.json",
            "tokenizer.json": "https://huggingface.co/openai/whisper-tiny/resolve/main/tokenizer.json",
            "vocab.json": "https://huggingface.co/openai/whisper-tiny/resolve/main/vocab.json",
            "merges.txt": "https://huggingface.co/openai/whisper-tiny/resolve/main/merges.txt"
        }
        
        for filename, url in model_files.items():
            file_path = whisper_dir / filename
            
            if file_path.exists():
                logger.info(f"文件已存在，跳过: {filename}")
                continue
            
            logger.info(f"下载 {filename}...")
            
            try:
                response = requests.get(url, stream=True)
                response.raise_for_status()
                
                total_size = int(response.headers.get('content-length', 0))
                downloaded = 0
                
                with open(file_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
                            downloaded += len(chunk)
                            
                            if total_size > 0:
                                progress = (downloaded / total_size) * 100
                                print(f"\r下载进度: {progress:.1f}%", end='', flush=True)
                
                print()  # 换行
                logger.info(f"✅ {filename} 下载完成")
                
            except Exception as e:
                logger.error(f"下载 {filename} 失败: {str(e)}")
                if file_path.exists():
                    file_path.unlink()  # 删除不完整的文件
                return False
        
        logger.info("🎉 Whisper 模型下载完成！")
        return True
        
    except Exception as e:
        logger.error(f"下载过程中出错: {str(e)}")
        return False

def verify_whisper_model():
    """验证 Whisper 模型文件"""
    try:
        base_dir = Path(__file__).parent
        whisper_dir = base_dir / "models" / "whisper"
        
        required_files = [
            "pytorch_model.bin",
            "config.json",
            "preprocessor_config.json"
        ]
        
        missing_files = []
        for filename in required_files:
            file_path = whisper_dir / filename
            if not file_path.exists():
                missing_files.append(filename)
        
        if missing_files:
            logger.warning(f"缺少文件: {missing_files}")
            return False
        else:
            logger.info("✅ Whisper 模型文件验证通过")
            return True
            
    except Exception as e:
        logger.error(f"验证过程中出错: {str(e)}")
        return False

if __name__ == "__main__":
    print("开始下载 MuseTalk Whisper 模型...")
    
    # 验证现有文件
    if verify_whisper_model():
        print("✅ Whisper 模型已存在且完整")
        sys.exit(0)
    
    # 下载模型
    success = download_whisper_model()
    
    if success:
        # 再次验证
        if verify_whisper_model():
            print("✅ Whisper 模型下载并验证成功！")
            sys.exit(0)
        else:
            print("❌ 模型验证失败")
            sys.exit(1)
    else:
        print("❌ 模型下载失败")
        sys.exit(1)
