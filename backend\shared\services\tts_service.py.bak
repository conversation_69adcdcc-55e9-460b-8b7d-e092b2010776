import os
import logging
import tempfile
import uuid
from typing import Dict, List, Optional, Any
import time
from gtts import gTTS
import json
import asyncio
from concurrent.futures import ThreadPoolExecutor
from functools import lru_cache
import traceback

# 导入离线TTS服务
from services.offline_tts_service import get_offline_tts_service, initialize as init_offline_tts
from services.tts_service_status import get_tts_service_status

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TTSError(Exception):
    """TTS服务错误基类"""
    pass

class VoiceNotFoundError(TTSError):
    """语音未找到错误"""
    pass

class TTSGenerationError(TTSError):
    """语音生成错误"""
    pass

class TTSService:
    """TTS服务 - 集成多种TTS服务提供商"""
    
    def __init__(self):
        """初始化TTS服务"""
        # 服务状态监控
        self.status_monitor = get_tts_service_status()
        
        # 尝试初始化离线TTS服务
        try:
            self.offline_tts = get_offline_tts_service()
            logger.info("离线TTS服务已加载")
        except Exception as e:
            logger.error(f"初始化离线TTS服务失败: {e}")
            logger.error(traceback.format_exc())
            self.offline_tts = None
        
        # 默认设置
        self.prefer_offline = True  # 优先使用离线TTS
        
        # 可用语音字典
        self.available_voices = {
            "zh-female1": {"lang": "zh-cn", "tld": "com", "gender": "female", "name": "标准女声"},
            "zh-female2": {"lang": "zh-cn", "tld": "com.hk", "gender": "female", "name": "甜美女声"},
            "zh-male1": {"lang": "zh-cn", "tld": "com", "gender": "male", "name": "标准男声"},
            "en-female1": {"lang": "en", "tld": "com", "gender": "female", "name": "英语女声"},
            "en-male1": {"lang": "en", "tld": "co.uk", "gender": "male", "name": "英语男声"},
            "ja-female1": {"lang": "ja", "tld": "co.jp", "gender": "female", "name": "日语女声"},
        }
        
        # 默认语音设置
        self.default_voice_id = "zh-female1"
        
        # 线程池执行器
        self.executor = ThreadPoolExecutor(max_workers=4)
        
        # 语音生成重试配置
        self.max_retries = 3
        self.retry_delay = 1  # 秒
        
        # 语音生成超时设置
        self.generation_timeout = 30  # 秒
        
        logger.info("TTS服务已初始化完成")
    
    def validate_voice_id(self, voice_id: str) -> str:
        """
        验证并返回有效的voice_id
        
        Args:
            voice_id: 输入的voice_id
            
        Returns:
            str: 有效的voice_id
        """
        # 如果voice_id为system或None，使用默认voice_id
        if voice_id in [None, 'system', '']:
            return self.default_voice_id
            
        # 检查voice_id是否在可用列表中
        if voice_id in self.available_voices:
            return voice_id
            
        # 如果voice_id无效，返回默认voice_id
        logger.warning(f"未找到语音ID: {voice_id}，使用默认语音ID: {self.default_voice_id}")
        return self.default_voice_id
    
    @lru_cache(maxsize=100)
    def get_voice_config(self, voice_id: str) -> Dict:
        """
        获取语音配置（带缓存）
        
        Args:
            voice_id: 语音ID
            
        Returns:
            Dict: 语音配置
        """
        return self.available_voices[self.validate_voice_id(voice_id)]
    
    async def text_to_speech(self, 
                           text: str, 
                           voice_id: str = None, 
                           speed: float = 1.0,
                           output_path: str = None) -> Dict[str, str]:
        """
        将文本转换为语音
        
        Args:
            text: 要转换的文本
            voice_id: 语音ID，例如 zh-female1
            speed: 语速，范围为0.5到2.0
            output_path: 输出文件路径，如果为None则生成临时文件
            
        Returns:
            Dict: 包含生成的音频文件路径和状态的字典
        """
        try:
            # 如果优先使用离线TTS，先尝试离线TTS
            if self.prefer_offline:
                try:
                    # 获取离线TTS服务
                    offline_tts = get_offline_tts_service()
                    
                    # 如果离线TTS尚未初始化，初始化它
                    if not self.offline_tts_initialized:
                        self.offline_tts_initialized = init_offline_tts()
                        if not self.offline_tts_initialized:
                            logger.warning("离线TTS初始化失败，将使用在线TTS")
                    
                    # 使用离线TTS生成语音
                    if self.offline_tts_initialized:
                        logger.info(f"使用离线TTS生成语音: voice_id={voice_id}")
                        result = await offline_tts.text_to_speech(text, voice_id, speed, output_path)
                        if result.get("status") == "success":
                            logger.info("离线TTS生成成功")
                            return result
                        else:
                            logger.warning(f"离线TTS生成失败: {result.get('message')}，将使用在线TTS")
                except Exception as e:
                    logger.warning(f"离线TTS出错，将使用在线TTS: {e}")
            
            # 在线TTS处理
            # 验证语音ID和获取配置
            voice_id = self.validate_voice_id(voice_id)
            voice_config = self.get_voice_config(voice_id)
            
            # 验证语速
            if not 0.5 <= speed <= 2.0:
                raise ValueError(f"语速必须在0.5到2.0之间，当前值: {speed}")
            
            # 生成临时文件名
            if output_path is None:
                temp_dir = tempfile.gettempdir()
                output_path = os.path.join(temp_dir, f"tts_{uuid.uuid4()}.mp3")
            
            # 带重试的语音生成
            for attempt in range(self.max_retries):
                try:
                    # 在线程池中执行gTTS操作
                    def generate_tts():
                        tts = gTTS(text=text, lang=voice_config["lang"], 
                                 tld=voice_config["tld"], slow=False)
                        tts.save(output_path)
                        return output_path
                    
                    # 异步执行TTS生成（带超时）
                    path = await asyncio.wait_for(
                        asyncio.get_event_loop().run_in_executor(self.executor, generate_tts),
                        timeout=self.generation_timeout
                    )
                    
                    logger.info(f"在线语音已生成: {path}")
                    
                    # 如果速度不是1.0，需要处理语速调整
                    if speed != 1.0:
                        logger.info(f"调整语速至 {speed}x")
                        # 这里应该调用FFmpeg处理语速
                        # 演示代码省略
                    
                    return {
                        "status": "success",
                        "path": path,
                        "voice_id": voice_id,
                        "speed": speed,
                        "offline": False
                    }
                    
                except asyncio.TimeoutError:
                    if attempt < self.max_retries - 1:
                        logger.warning(f"语音生成超时，尝试重试 ({attempt + 1}/{self.max_retries})")
                        await asyncio.sleep(self.retry_delay)
                    else:
                        # 如果离线TTS已初始化，尝试回退到离线TTS
                        if not self.prefer_offline and self.offline_tts_initialized:
                            logger.info("在线TTS超时，尝试使用离线TTS作为回退")
                            try:
                                offline_tts = get_offline_tts_service()
                                result = await offline_tts.text_to_speech(text, voice_id, speed, output_path)
                                if result.get("status") == "success":
                                    logger.info("使用离线TTS作为回退成功")
                                    return result
                            except Exception as offline_err:
                                logger.error(f"离线TTS回退也失败: {offline_err}")
                        raise TTSGenerationError("语音生成超时")
                        
                except Exception as e:
                    if attempt < self.max_retries - 1:
                        logger.warning(f"语音生成失败，尝试重试 ({attempt + 1}/{self.max_retries}): {e}")
                        await asyncio.sleep(self.retry_delay)
                    else:
                        # 如果离线TTS已初始化，尝试回退到离线TTS
                        if not self.prefer_offline and self.offline_tts_initialized:
                            logger.info(f"在线TTS失败: {e}，尝试使用离线TTS作为回退")
                            try:
                                offline_tts = get_offline_tts_service()
                                result = await offline_tts.text_to_speech(text, voice_id, speed, output_path)
                                if result.get("status") == "success":
                                    logger.info("使用离线TTS作为回退成功")
                                    return result
                            except Exception as offline_err:
                                logger.error(f"离线TTS回退也失败: {offline_err}")
                        raise TTSGenerationError(f"语音生成失败: {e}")
            
        except VoiceNotFoundError as e:
            logger.error(f"语音ID无效: {e}")
            return {"status": "error", "message": str(e)}
        except TTSGenerationError as e:
            logger.error(f"语音生成错误: {e}")
            return {"status": "error", "message": str(e)}
        except Exception as e:
            logger.error(f"语音合成失败: {e}")
            return {"status": "error", "message": str(e)}
    
    async def generate_speech(self, 
                            text: str, 
                            voice_id: str = None, 
                            speed: float = 1.0,
                            output_path: str = None) -> Dict[str, Any]:
        """
        将文本转换为语音
        
        Args:
            text: 要转换的文本
            voice_id: 语音ID
            speed: 语速，范围为0.5到2.0
            output_path: 输出文件路径
            
        Returns:
            Dict: 包含生成的音频文件路径和状态的字典
        """
        logger.info(f"调用generate_speech方法，文本: '{text[:30]}...'，语音ID: {voice_id}")
        
        result = None
        
        # 优先使用离线TTS
        if self.prefer_offline and self.offline_tts:
            try:
                logger.info(f"使用离线TTS生成语音: voice_id={voice_id}")
                result = await self.offline_tts.generate_speech(
                    text=text,
                    voice_id=voice_id,
                    speed=speed,
                    output_path=output_path
                )
                
                # 记录结果
                self.status_monitor.record_tts_result(result)
                
                if result["status"] == "success":
                    logger.info("离线TTS生成成功")
                    return result
                else:
                    logger.warning(f"离线TTS生成失败: {result.get('message', '未知错误')}")
            except Exception as e:
                logger.error(f"离线TTS服务异常: {e}")
                logger.error(traceback.format_exc())
                result = {"status": "error", "message": str(e)}
                self.status_monitor.record_tts_result(result)
        
        # 如果离线生成失败，可以在这里添加在线TTS服务的调用
        
        # 返回结果
        if result:
            return result
        else:
            error_result = {
                "status": "error", 
                "message": "所有TTS服务均不可用或生成失败"
            }
            self.status_monitor.record_tts_result(error_result)
            return error_result
    
    def get_available_voices(self) -> Dict:
        """
        获取可用的语音列表
        
        Returns:
            available_voices: 可用语音字典
        """
        voices = {}
        
        # 获取离线TTS提供的语音
        if self.offline_tts:
            try:
                offline_voices = self.offline_tts.get_available_voices()
                for voice_id, voice_info in offline_voices.items():
                    voice_info["provider"] = "offline"
                    voices[voice_id] = voice_info
            except Exception as e:
                logger.error(f"获取离线TTS语音列表失败: {e}")
        
        # 这里可以添加其他TTS提供商的语音
        
        return voices
    
    def generate_phonemes(self, text: str, lang: str = "zh-cn") -> List[Dict]:
        """
        生成文本的音素序列（用于唇形同步）
        
        Args:
            text: 要处理的文本
            lang: 语言代码
            
        Returns:
            phonemes: 音素列表，每个音素包含类型和持续时间
        """
        # 实际项目应使用专业的音素分析工具，如Festival或MFA
        # 这里使用简化实现做演示
        
        # 创建基本音素映射（演示用）
        simple_phoneme_map = {
            'a': 'ah', 'e': 'eh', 'i': 'ih', 'o': 'oh', 'u': 'uh',
            'b': 'b', 'p': 'p', 'm': 'm', 'f': 'f', 'v': 'v',
            'd': 'd', 't': 't', 'l': 'l', 'n': 'n',
            'g': 'g', 'k': 'k', 'h': 'h',
            'j': 'j', 'q': 'k', 'x': 'sh',
            'z': 'z', 'c': 'ch', 's': 's',
            'r': 'r', 'y': 'y', 'w': 'w'
        }
        
        phonemes = []
        
        # 简单分割字符（实际应分析音节）
        for char in text.lower():
            if char in simple_phoneme_map:
                phoneme_type = simple_phoneme_map[char]
            else:
                phoneme_type = 'sil'  # 静音
            
            # 添加音素及其持续时间
            phonemes.append({
                "phoneme": phoneme_type,
                "duration": 0.1  # 假设每个音素持续0.1秒
            })
        
        return phonemes
    
    def save_phonemes_to_file(self, phonemes: List[Dict], output_path: str) -> bool:
        """
        将音素序列保存到文件
        
        Args:
            phonemes: 音素列表
            output_path: 输出文件路径
            
        Returns:
            bool: 保存是否成功
        """
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(phonemes, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            logger.error(f"保存音素文件失败: {e}")
            return False
    
    def load_phonemes_from_file(self, file_path: str) -> List[Dict]:
        """
        从文件加载音素序列
        
        Args:
            file_path: 文件路径
            
        Returns:
            phonemes: 音素列表
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"加载音素文件失败: {e}")
            return []
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取TTS服务状态
        
        Returns:
            Dict: 服务状态信息
        """
        status = self.status_monitor.get_status()
        
        # 添加离线TTS性能信息
        if self.offline_tts:
            try:
                offline_stats = self.offline_tts.get_performance_stats()
                status["offline_tts_stats"] = offline_stats
            except Exception as e:
                logger.error(f"获取离线TTS性能统计失败: {e}")
        
        return status
    
    def get_health_check(self) -> Dict[str, Any]:
        """
        获取健康检查结果
        
        Returns:
            Dict: 健康状态信息
        """
        return self.status_monitor.get_health_check()

# 全局单例
_tts_service = None

def get_tts_service() -> TTSService:
    """获取TTS服务实例"""
    global _tts_service
    if _tts_service is None:
        _tts_service = TTSService()
    return _tts_service

def initialize() -> bool:
    """初始化TTS服务"""
    service = get_tts_service()
    
    # 初始化离线TTS
    try:
        service.offline_tts_initialized = init_offline_tts()
        if service.offline_tts_initialized:
            logger.info("离线TTS服务初始化成功")
        else:
            logger.warning("离线TTS服务初始化失败，将仅使用在线TTS")
    except Exception as e:
        logger.error(f"初始化离线TTS服务时出错: {e}")
        service.offline_tts_initialized = False
    
    return True