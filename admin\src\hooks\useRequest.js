import { ref, reactive, onMounted, onUnmounted } from 'vue';
import { message } from 'ant-design-vue';
import { createCancelToken, cancelRequest } from '@/utils/request';

/**
 * 请求钩子
 * @param {Function} requestFn - 请求函数
 * @param {Object} options - 配置选项
 * @param {boolean} options.immediate - 是否立即请求
 * @param {Array|Object} options.defaultParams - 默认参数
 * @param {boolean} options.showErrorMessage - 是否显示错误消息
 * @param {Function} options.onSuccess - 成功回调
 * @param {Function} options.onError - 错误回调
 * @param {Function} options.onFinally - 结束回调
 * @param {boolean} options.useCancel - 是否使用取消令牌
 * @returns {Object} 请求相关状态和方法
 */
export default function useRequest(requestFn, options = {}) {
  if (typeof requestFn !== 'function') {
    throw new Error('requestFn必须是一个函数');
  }
  
  const {
    immediate = false,
    defaultParams = null,
    showErrorMessage = true,
    onSuccess,
    onError,
    onFinally,
    useCancel = true
  } = options;
  
  // 加载状态
  const loading = ref(false);
  
  // 响应数据
  const data = ref(null);
  
  // 错误信息
  const error = ref(null);
  
  // 是否执行过请求
  const executed = ref(false);
  
  // 取消令牌源
  const cancelTokenSource = ref(useCancel ? createCancelToken() : null);
  
  // 请求参数
  const params = reactive(defaultParams ? 
    (Array.isArray(defaultParams) ? [...defaultParams] : { ...defaultParams }) : 
    null
  );
  
  // 取消请求
  const cancel = (message = '请求已取消') => {
    if (cancelTokenSource.value) {
      cancelRequest(cancelTokenSource.value, message);
      // 创建新的取消令牌源
      cancelTokenSource.value = createCancelToken();
    }
  };
  
  // 更新参数
  const updateParams = (newParams) => {
    if (!params) return;
    
    if (Array.isArray(params)) {
      Object.keys(newParams).forEach((key, index) => {
        if (index < params.length) {
          params[index] = newParams[key];
        }
      });
    } else {
      Object.keys(newParams).forEach(key => {
        params[key] = newParams[key];
      });
    }
  };
  
  // 执行请求
  const execute = async (executeParams = null) => {
    // 如果正在加载，取消之前的请求
    if (loading.value && useCancel) {
      cancel('新请求已发起');
    }
    
    // 合并参数
    const requestParams = executeParams || params;
    
    loading.value = true;
    error.value = null;
    
    try {
      // 添加取消令牌
      const requestOptions = useCancel ? 
        { cancelToken: cancelTokenSource.value.token } : 
        {};
      
      let result;
      if (Array.isArray(requestParams)) {
        result = await requestFn(...requestParams, requestOptions);
      } else if (requestParams) {
        result = await requestFn(requestParams, requestOptions);
      } else {
        result = await requestFn(requestOptions);
      }
      
      data.value = result;
      executed.value = true;
      
      // 调用成功回调
      if (typeof onSuccess === 'function') {
        onSuccess(result, requestParams);
      }
      
      return result;
    } catch (err) {
      error.value = err;
      executed.value = true;
      
      // 不处理取消的请求
      if (err.message === '请求已取消' || err.message === '新请求已发起') {
        return;
      }
      
      // 显示错误消息
      if (showErrorMessage) {
        message.error(err.message || '请求失败');
      }
      
      // 调用错误回调
      if (typeof onError === 'function') {
        onError(err, requestParams);
      }
      
      throw err;
    } finally {
      loading.value = false;
      
      // 调用结束回调
      if (typeof onFinally === 'function') {
        onFinally(requestParams);
      }
    }
  };
  
  // 刷新请求
  const refresh = () => {
    return execute();
  };
  
  // 重置请求
  const reset = () => {
    data.value = null;
    error.value = null;
    executed.value = false;
    
    // 重置参数
    if (params && defaultParams) {
      if (Array.isArray(params)) {
        defaultParams.forEach((value, index) => {
          params[index] = value;
        });
      } else {
        Object.keys(defaultParams).forEach(key => {
          params[key] = defaultParams[key];
        });
      }
    }
  };
  
  // 组件挂载时执行请求
  onMounted(() => {
    if (immediate) {
      execute();
    }
  });
  
  // 组件卸载时取消请求
  onUnmounted(() => {
    if (useCancel && loading.value) {
      cancel('组件已卸载');
    }
  });
  
  return {
    loading,
    data,
    error,
    executed,
    params,
    execute,
    refresh,
    reset,
    cancel,
    updateParams
  };
} 