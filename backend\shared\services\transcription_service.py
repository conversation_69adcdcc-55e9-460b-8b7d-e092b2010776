import os
import uuid
import json
import time
import logging
import threading
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, List, Optional, Tuple
import asyncio
import shutil
import tempfile

# 修改导入方式，使用多种路径尝试
try:
    # 首先尝试从当前目录导入
    from .asr_service import ASRService, get_asr_service
    HAS_ASR_SERVICE = True
    logger_prefix = "从相对路径导入ASR服务成功"
except ImportError:
    try:
        # 尝试从顶级模块导入
        from backend.services.asr_service import ASRService, get_asr_service
        HAS_ASR_SERVICE = True
        logger_prefix = "从顶级模块导入ASR服务成功"
    except ImportError:
        try:
            # 尝试从系统路径导入
            from services.asr_service import ASRService, get_asr_service
            HAS_ASR_SERVICE = True
            logger_prefix = "从系统路径导入ASR服务成功"
        except ImportError:
            HAS_ASR_SERVICE = False
            logger_prefix = "导入ASR服务失败"
            get_asr_service = None

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 定义上传和结果目录
UPLOAD_DIR = os.path.join(os.getcwd(), "uploads", "audio")
RESULT_DIR = os.path.join(os.getcwd(), "results", "transcriptions")

# 确保目录存在
os.makedirs(UPLOAD_DIR, exist_ok=True)
os.makedirs(RESULT_DIR, exist_ok=True)

# 转写任务状态
TASK_STATUS = {}  # 任务ID -> 任务状态

class TranscriptionService:
    """音频转写服务"""
    
    def __init__(self):
        self.initialized = False
        self.asr_service = None
        self.mock_mode = False  # 默认使用真实ASR服务，不使用模拟模式
        self.tasks = {}  # 任务ID -> 任务状态
        self.active_threads = {}  # 任务ID -> 线程
    
    def initialize(self) -> bool:
        """初始化转写服务"""
        try:
            # 检查是否存在ASR服务
            if HAS_ASR_SERVICE:
                try:
                    # 使用已导入的get_asr_service函数
                    if get_asr_service:
                        self.asr_service = get_asr_service()
                        logger.info(f"{logger_prefix}, 正在初始化ASR服务...")
                        initialize_result = self.asr_service.initialize()
                        if initialize_result:
                            self.mock_mode = False
                            logger.info("ASR服务初始化成功，使用实际转写服务")
                        else:
                            logger.warning("ASR服务初始化失败，使用模拟转写服务")
                            self.mock_mode = True
                    else:
                        logger.warning("找不到get_asr_service函数，使用模拟转写服务")
                        self.mock_mode = True
                except Exception as e:
                    logger.warning(f"初始化ASR服务时出错: {str(e)}")
                    self.mock_mode = True
            else:
                logger.info("未找到ASR服务，使用模拟转写服务")
                self.mock_mode = True
            
            # 确保目录存在
            os.makedirs(UPLOAD_DIR, exist_ok=True)
            os.makedirs(RESULT_DIR, exist_ok=True)
            
            self.initialized = True
            return True
        except Exception as e:
            logger.error(f"转写服务初始化失败: {str(e)}")
            return False
    
    def get_supported_languages(self) -> List[Dict[str, str]]:
        """获取支持的语言列表"""
        # 返回支持的语言列表
        return [
            {"code": "zh-CN", "name": "中文（简体）"},
            {"code": "en-US", "name": "英语（美国）"},
            {"code": "ja-JP", "name": "日语"},
            {"code": "ko-KR", "name": "韩语"},
            {"code": "fr-FR", "name": "法语"},
            {"code": "de-DE", "name": "德语"},
            {"code": "es-ES", "name": "西班牙语"},
            {"code": "ru-RU", "name": "俄语"}
        ]
    
    def get_supported_formats(self) -> List[str]:
        """获取支持的音频格式"""
        return ["mp3", "wav", "ogg", "m4a", "aac", "flac", "wma", "aiff"]
    
    def save_audio_file(self, file_data: bytes, filename: str, user_id: int) -> Tuple[bool, str, str]:
        """保存上传的音频文件
        
        Args:
            file_data: 文件数据
            filename: 原始文件名
            user_id: 用户ID
            
        Returns:
            (成功标志, 文件路径, 错误信息)
        """
        try:
            # 创建用户上传目录
            user_upload_dir = os.path.join(UPLOAD_DIR, str(user_id))
            os.makedirs(user_upload_dir, exist_ok=True)
            
            # 生成安全的文件名
            timestamp = int(time.time())
            unique_id = str(uuid.uuid4())[:8]
            safe_filename = f"{timestamp}_{unique_id}_{filename}"
            file_path = os.path.join(user_upload_dir, safe_filename)
            
            # 保存文件
            with open(file_path, "wb") as f:
                f.write(file_data)
            
            return True, file_path, ""
        except Exception as e:
            logger.error(f"保存音频文件失败: {str(e)}")
            return False, "", str(e)
    
    def start_transcription(self, task_id: str, file_path: str, options: Dict[str, Any]) -> bool:
        """开始转写任务
        
        Args:
            task_id: 任务ID
            file_path: 音频文件路径
            options: 转写选项
            
        Returns:
            成功标志
        """
        try:
            # 获取文件信息
            file_name = os.path.basename(file_path)
            file_size = os.path.getsize(file_path) if os.path.exists(file_path) else 0
            language = options.get("language", "zh-CN")
            
            # 初始化任务状态
            TASK_STATUS[task_id] = {
                "status": "processing",
                "progress": 0,
                "stage": "transcribing",
                "start_time": time.time(),
                "file_path": file_path,
                "fileName": file_name,  # 添加文件名
                "fileSize": file_size,  # 添加文件大小
                "language": language,   # 添加语言
                "options": options
            }
            
            # 开始转写任务
            if self.mock_mode:
                # 使用模拟模式
                thread = threading.Thread(
                    target=self._mock_transcription_process,
                    args=(task_id, file_path, options)
                )
                thread.daemon = True
                thread.start()
                self.active_threads[task_id] = thread
            else:
                # 使用真实ASR服务
                if self.asr_service:
                    thread = threading.Thread(
                        target=self._real_transcription_process,
                        args=(task_id, file_path, options)
                    )
                    thread.daemon = True
                    thread.start()
                    self.active_threads[task_id] = thread
                else:
                    logger.error(f"ASR服务未初始化")
                    TASK_STATUS[task_id]["status"] = "error"
                    TASK_STATUS[task_id]["message"] = "ASR服务未初始化"
                    return False
            
            return True
        except Exception as e:
            logger.error(f"开始转写任务失败: {str(e)}")
            TASK_STATUS[task_id] = {
                "status": "error",
                "message": str(e)
            }
            return False
    
    def _mock_transcription_process(self, task_id: str, file_path: str, options: Dict[str, Any]):
        """模拟转写过程（用于开发测试）"""
        try:
            # 检查文件是否存在
            if not os.path.exists(file_path):
                TASK_STATUS[task_id]["status"] = "error"
                TASK_STATUS[task_id]["message"] = f"文件不存在: {file_path}"
                return
            
            # 获取音频文件大小
            file_size = os.path.getsize(file_path)
            
            # 模拟转写过程
            total_stages = 100
            for i in range(1, total_stages + 1):
                if task_id not in TASK_STATUS:
                    # 任务可能被取消
                    return
                
                # 更新进度
                TASK_STATUS[task_id]["progress"] = i
                
                # 更新阶段
                if i < 70:
                    TASK_STATUS[task_id]["stage"] = "transcribing"
                elif i < 90:
                    TASK_STATUS[task_id]["stage"] = "processing"
                else:
                    TASK_STATUS[task_id]["stage"] = "finished"
                
                # 睡眠一小段时间模拟处理
                time.sleep(0.1)  # 快速模拟，约10秒完成
            
            # 模拟生成转写结果
            user_id = options.get("user_id", "unknown")
            language = options.get("language", "zh-CN")
            
            # 创建结果目录
            user_result_dir = os.path.join(RESULT_DIR, str(user_id))
            os.makedirs(user_result_dir, exist_ok=True)
            
            # 生成结果文件名
            result_filename = f"{task_id}_result.json"
            result_path = os.path.join(user_result_dir, result_filename)
            
            # 模拟转写结果
            paragraphs = [
                {"time": "00:00:03", "text": "欢迎参加本次会议，今天我们将讨论项目的最新进展。"},
                {"time": "00:00:12", "text": "首先，让我们回顾一下上周的工作完成情况。"},
                {"time": "00:00:20", "text": "开发团队已经完成了核心功能的开发，测试团队正在进行全面测试。"},
                {"time": "00:00:35", "text": "设计团队提交了新的UI设计稿，请大家在会后查看并提供反馈。"},
                {"time": "00:00:50", "text": "市场团队准备了新的推广计划，下面请市场总监为大家介绍详情。"},
                {"time": "00:01:10", "text": "感谢各位的汇报，现在我们来讨论一下接下来的工作重点。"},
                {"time": "00:01:25", "text": "我们需要加快产品迭代速度，争取在下个月正式发布产品。"},
                {"time": "00:01:40", "text": "最后，请各部门负责人在周五前提交详细的工作计划。"},
                {"time": "00:02:00", "text": "今天的会议到此结束，谢谢大家的参与。"}
            ]
            
            result_text = "\n".join([p["text"] for p in paragraphs])
            
            result = {
                "task_id": task_id,
                "file_path": file_path,
                "file_size": file_size,
                "language": language,
                "duration": "02:15",
                "duration_seconds": 135,
                "paragraphs": paragraphs,
                "text": result_text,
                "timestamp": datetime.now().isoformat()
            }
            
            # 保存结果
            with open(result_path, "w", encoding="utf-8") as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            
            # 更新任务状态
            TASK_STATUS[task_id]["status"] = "completed"
            TASK_STATUS[task_id]["result_path"] = result_path
            TASK_STATUS[task_id]["progress"] = 100
            TASK_STATUS[task_id]["stage"] = "finished"
            TASK_STATUS[task_id]["duration"] = "02:15"
            TASK_STATUS[task_id]["duration_seconds"] = 135
            TASK_STATUS[task_id]["result_text"] = result_text
            TASK_STATUS[task_id]["paragraphs"] = paragraphs
            
        except Exception as e:
            logger.error(f"模拟转写过程出错: {str(e)}")
            TASK_STATUS[task_id]["status"] = "error"
            TASK_STATUS[task_id]["message"] = str(e)
    
    def _real_transcription_process(self, task_id: str, file_path: str, options: Dict[str, Any]):
        """使用真实ASR服务进行转写"""
        try:
            # 检查文件是否存在
            if not os.path.exists(file_path):
                TASK_STATUS[task_id]["status"] = "error"
                TASK_STATUS[task_id]["message"] = f"文件不存在: {file_path}"
                return
            
            # 获取音频文件大小
            file_size = os.path.getsize(file_path)
            
            # 确保ASR服务已初始化
            if self.asr_service is None or not hasattr(self.asr_service, 'transcribe'):
                logger.error("ASR服务未正确初始化，无法转写")
                TASK_STATUS[task_id]["status"] = "error"
                TASK_STATUS[task_id]["message"] = "ASR服务未正确初始化"
                return
            
            # 更新任务状态
            TASK_STATUS[task_id]["progress"] = 10
            TASK_STATUS[task_id]["stage"] = "transcribing"
            
            # 从选项中获取语言代码
            language_code = options.get("language", "zh-CN")
            # 转换语言代码格式（zh-CN -> zh）
            if "-" in language_code:
                language_code = language_code.split("-")[0]
            
            # 使用ASR服务转写
            logger.info(f"开始实际转写任务: {task_id}, 文件: {file_path}, 语言: {language_code}")
            
            # 更新进度
            TASK_STATUS[task_id]["progress"] = 20
            
            # 调用Whisper的转写功能
            result = self.asr_service.transcribe(file_path, language=language_code)
            
            # 更新进度
            TASK_STATUS[task_id]["progress"] = 80
            TASK_STATUS[task_id]["stage"] = "processing"
            
            # 检查转写结果
            if "error" in result:
                TASK_STATUS[task_id]["status"] = "error"
                TASK_STATUS[task_id]["message"] = result["error"]
                return
            
            # 从result提取segments，转换为所需的paragraph格式
            paragraphs = []
            if "segments" in result and isinstance(result["segments"], list):
                for segment in result["segments"]:
                    # 使用段落开始时间
                    time_str = segment.get("start_str", "00:00:00")
                    if time_str and "." in time_str:
                        # 裁剪到秒级别
                        time_str = time_str.split(".")[0]
                    
                    paragraphs.append({
                        "time": time_str,
                        "text": segment.get("text", "").strip()
                    })
            
            # 创建用户结果目录
            user_id = options.get("user_id", "unknown")
            user_result_dir = os.path.join(RESULT_DIR, str(user_id))
            os.makedirs(user_result_dir, exist_ok=True)
            
            # 格式化结果
            duration_seconds = 0
            if "segments" in result and len(result["segments"]) > 0:
                last_segment = result["segments"][-1]
                if "end" in last_segment:
                    duration_seconds = last_segment["end"]
            
            # 格式化时长
            minutes, seconds = divmod(duration_seconds, 60)
            hours, minutes = divmod(minutes, 60)
            duration = f"{int(hours):02d}:{int(minutes):02d}:{int(seconds):02d}"
            
            # 生成结果文件名
            result_filename = f"{task_id}_result.json"
            result_path = os.path.join(user_result_dir, result_filename)
            
            # 准备保存的结果
            save_result = {
                "task_id": task_id,
                "file_path": file_path,
                "file_size": file_size,
                "language": result.get("language", language_code),
                "duration": duration,
                "duration_seconds": duration_seconds,
                "paragraphs": paragraphs,
                "text": result.get("text", ""),
                "timestamp": datetime.now().isoformat()
            }
            
            # 保存结果文件
            with open(result_path, "w", encoding="utf-8") as f:
                json.dump(save_result, f, ensure_ascii=False, indent=2)
            
            # 更新任务状态
            TASK_STATUS[task_id]["status"] = "completed"
            TASK_STATUS[task_id]["result_path"] = result_path
            TASK_STATUS[task_id]["progress"] = 100
            TASK_STATUS[task_id]["stage"] = "finished"
            TASK_STATUS[task_id]["duration"] = duration
            TASK_STATUS[task_id]["duration_seconds"] = duration_seconds
            TASK_STATUS[task_id]["result_text"] = result.get("text", "")
            TASK_STATUS[task_id]["paragraphs"] = paragraphs
            
            logger.info(f"转写任务 {task_id} 完成，结果保存至: {result_path}")
            
        except Exception as e:
            logger.error(f"实际转写过程出错: {str(e)}")
            TASK_STATUS[task_id]["status"] = "error"
            TASK_STATUS[task_id]["message"] = str(e)
    
    def get_task_progress(self, task_id: str) -> Dict[str, Any]:
        """获取任务进度
        
        Args:
            task_id: 任务ID
            
        Returns:
            任务状态信息
        """
        if task_id not in TASK_STATUS:
            return {
                "task_id": task_id,
                "status": "not_found",
                "message": "任务不存在"
            }
        
        # 返回当前任务状态
        task_status = TASK_STATUS[task_id].copy()
        task_status["task_id"] = task_id
        
        return task_status
    
    def get_transcription_result(self, task_id: str) -> Dict[str, Any]:
        """获取转写结果
        
        Args:
            task_id: 任务ID
            
        Returns:
            转写结果
        """
        if task_id not in TASK_STATUS:
            return {
                "task_id": task_id,
                "status": "not_found",
                "message": "任务不存在"
            }
        
        task_status = TASK_STATUS[task_id]
        
        # 检查任务是否完成
        if task_status.get("status") != "completed":
            return {
                "task_id": task_id,
                "status": task_status.get("status", "unknown"),
                "progress": task_status.get("progress", 0),
                "message": "任务尚未完成"
            }
        
        # 检查结果文件是否存在
        result_path = task_status.get("result_path")
        if not result_path or not os.path.exists(result_path):
            return {
                "task_id": task_id,
                "status": "error",
                "message": "结果文件不存在"
            }
        
        try:
            # 读取结果文件
            with open(result_path, "r", encoding="utf-8") as f:
                result = json.load(f)
            
            # 返回结果
            return {
                "task_id": task_id,
                "status": "completed",
                "fileName": os.path.basename(task_status.get("file_path", "")),
                "fileSize": os.path.getsize(task_status.get("file_path", "")) if task_status.get("file_path") else 0,
                "duration": result.get("duration", ""),
                "language": result.get("language", ""),
                "paragraphs": result.get("paragraphs", []),
                "text": result.get("text", ""),
                "timestamp": result.get("timestamp", "")
            }
        except Exception as e:
            logger.error(f"读取转写结果失败: {str(e)}")
            return {
                "task_id": task_id,
                "status": "error",
                "message": f"读取结果失败: {str(e)}"
            }
    
    def save_edited_result(self, task_id: str, edited_result: Dict[str, Any]) -> Dict[str, Any]:
        """保存编辑后的转写结果
        
        Args:
            task_id: 任务ID
            edited_result: 编辑后的结果
            
        Returns:
            保存结果
        """
        if task_id not in TASK_STATUS:
            return {
                "task_id": task_id,
                "status": "not_found",
                "message": "任务不存在"
            }
        
        task_status = TASK_STATUS[task_id]
        
        # 检查任务是否完成
        if task_status.get("status") != "completed":
            return {
                "task_id": task_id,
                "status": "error",
                "message": "任务尚未完成，无法保存编辑结果"
            }
        
        try:
            # 获取结果文件路径
            result_path = task_status.get("result_path")
            if not result_path:
                return {"status": "error", "message": "结果文件路径不存在"}
            
            # 读取原始结果
            with open(result_path, "r", encoding="utf-8") as f:
                original_result = json.load(f)
            
            # 更新结果
            original_result["paragraphs"] = edited_result.get("paragraphs", original_result.get("paragraphs", []))
            original_result["text"] = edited_result.get("text", original_result.get("text", ""))
            original_result["last_edited"] = datetime.now().isoformat()
            
            # 保存更新后的结果
            with open(result_path, "w", encoding="utf-8") as f:
                json.dump(original_result, f, ensure_ascii=False, indent=2)
            
            # 更新内存中的状态
            TASK_STATUS[task_id]["paragraphs"] = original_result["paragraphs"]
            TASK_STATUS[task_id]["result_text"] = original_result["text"]
            
            return {
                "task_id": task_id,
                "status": "saved",
                "message": "编辑结果已保存"
            }
        except Exception as e:
            logger.error(f"保存编辑结果失败: {str(e)}")
            return {
                "task_id": task_id,
                "status": "error",
                "message": f"保存失败: {str(e)}"
            }
    
    def get_user_tasks(self, user_id: int, limit: int = 10, offset: int = 0) -> List[Dict[str, Any]]:
        """获取用户的转写任务列表
        
        Args:
            user_id: 用户ID
            limit: 限制数量
            offset: 偏移量
            
        Returns:
            任务列表
        """
        # 在实际实现中，这里应该从数据库获取任务列表
        # 现在使用内存中的任务状态模拟
        
        tasks = []
        for task_id, status in TASK_STATUS.items():
            if status.get("options", {}).get("user_id") == user_id:
                task = {
                    "taskId": task_id,
                    "fileName": os.path.basename(status.get("file_path", "")),
                    "fileSize": f"{os.path.getsize(status.get('file_path', '')) / (1024 * 1024):.1f}MB" if status.get("file_path") and os.path.exists(status.get("file_path")) else "未知",
                    "duration": status.get("duration", "00:00"),
                    "status": status.get("status", "unknown"),
                    "createdAt": datetime.fromtimestamp(status.get("start_time", time.time())).isoformat() if status.get("start_time") else datetime.now().isoformat(),
                }
                tasks.append(task)
        
        # 排序并分页
        tasks.sort(key=lambda x: x["createdAt"], reverse=True)
        paginated_tasks = tasks[offset:offset+limit]
        
        return paginated_tasks

# 全局实例变量
_transcription_service = None

def get_transcription_service():
    """获取转写服务实例（单例模式）
    
    Returns:
        TranscriptionService: 转写服务实例
    """
    global _transcription_service
    if _transcription_service is None:
        _transcription_service = TranscriptionService()
        _transcription_service.initialize()
    return _transcription_service 