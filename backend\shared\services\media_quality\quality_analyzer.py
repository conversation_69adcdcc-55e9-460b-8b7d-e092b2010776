import os
import cv2
import numpy as np
import logging
from typing import Dict, List, Any, Optional, Tuple

logger = logging.getLogger(__name__)

class MediaQualityAnalyzer:
    """
    媒体质量分析服务，用于评估图像和视频的质量
    """
    
    def __init__(self):
        """初始化媒体质量分析器"""
        self.min_resolution = (320, 240)  # 最小分辨率
        self.ideal_resolution = (1920, 1080)  # 理想分辨率
        self.min_face_ratio = 0.1  # 最小面部比例（占图像面积）
        self.ideal_face_ratio = 0.4  # 理想面部比例
        
        logger.info("媒体质量分析器已初始化")
    
    def analyze_image(self, image_path: str) -> Dict[str, Any]:
        """
        分析图像质量
        
        Args:
            image_path: 图像文件路径
            
        Returns:
            Dict[str, Any]: 质量分析结果
        """
        try:
            # 读取图像
            image = cv2.imread(image_path)
            if image is None:
                return {
                    "success": False,
                    "message": f"无法读取图像: {image_path}",
                    "checks": []
                }
            
            h, w, _ = image.shape
            
            # 初始化检查结果
            checks = []
            
            # 检查1: 分辨率
            resolution_status, resolution_message = self._check_resolution(w, h)
            checks.append({
                "name": "分辨率",
                "status": resolution_status,
                "message": resolution_message
            })
            
            # 检查2: 面部检测
            face_status, face_message, face_ratio = self._detect_face(image)
            checks.append({
                "name": "面部检测",
                "status": face_status,
                "message": face_message
            })
            
            # 检查3: 亮度和对比度
            brightness_status, brightness_message = self._check_brightness(image)
            checks.append({
                "name": "亮度条件",
                "status": brightness_status,
                "message": brightness_message
            })
            
            # 检查4: 图像清晰度
            sharpness_status, sharpness_message = self._check_sharpness(image)
            checks.append({
                "name": "图像清晰度",
                "status": sharpness_status,
                "message": sharpness_message
            })
            
            # 计算整体质量评分
            score = self._calculate_score(checks)
            status = self._get_overall_status(score)
            
            return {
                "success": True,
                "score": score,
                "status": status,
                "message": f"图像质量评分: {score}%",
                "checks": checks,
                "dimensions": {
                    "width": w,
                    "height": h
                }
            }
            
        except Exception as e:
            logger.error(f"图像质量分析失败: {str(e)}")
            return {
                "success": False,
                "message": f"图像质量分析失败: {str(e)}",
                "checks": []
            }
    
    def analyze_video(self, video_path: str) -> Dict[str, Any]:
        """
        分析视频质量
        
        Args:
            video_path: 视频文件路径
            
        Returns:
            Dict[str, Any]: 质量分析结果
        """
        try:
            # 打开视频文件
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                return {
                    "success": False,
                    "message": f"无法打开视频: {video_path}",
                    "checks": []
                }
            
            # 获取视频属性
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            fps = cap.get(cv2.CAP_PROP_FPS)
            frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            duration = frame_count / fps if fps > 0 else 0
            
            # 初始化检查结果
            checks = []
            
            # 检查1: 分辨率
            resolution_status, resolution_message = self._check_resolution(width, height)
            checks.append({
                "name": "分辨率",
                "status": resolution_status,
                "message": resolution_message
            })
            
            # 检查2: 视频时长
            duration_status, duration_message = self._check_duration(duration)
            checks.append({
                "name": "视频时长",
                "status": duration_status,
                "message": duration_message
            })
            
            # 检查3: 帧率
            fps_status, fps_message = self._check_fps(fps)
            checks.append({
                "name": "帧率",
                "status": fps_status,
                "message": fps_message
            })
            
            # 提取关键帧进行质量分析
            frame_checks = self._analyze_video_frames(cap, frame_count)
            checks.extend(frame_checks)
            
            # 释放视频对象
            cap.release()
            
            # 计算整体质量评分
            score = self._calculate_score(checks)
            status = self._get_overall_status(score)
            
            return {
                "success": True,
                "score": score,
                "status": status,
                "message": f"视频质量评分: {score}%",
                "checks": checks,
                "dimensions": {
                    "width": width,
                    "height": height
                },
                "duration": duration,
                "fps": fps,
                "frame_count": frame_count
            }
            
        except Exception as e:
            logger.error(f"视频质量分析失败: {str(e)}")
            return {
                "success": False,
                "message": f"视频质量分析失败: {str(e)}",
                "checks": []
            }
    
    def _check_resolution(self, width: int, height: int) -> Tuple[str, str]:
        """
        检查分辨率
        
        Returns:
            Tuple[str, str]: (状态, 消息)
        """
        min_w, min_h = self.min_resolution
        ideal_w, ideal_h = self.ideal_resolution
        
        if width < min_w or height < min_h:
            return "fail", f"分辨率太低: {width}x{height}，最小要求: {min_w}x{min_h}"
        elif width >= ideal_w or height >= ideal_h:
            return "pass", f"分辨率良好: {width}x{height}"
        else:
            return "warning", f"分辨率可接受但不理想: {width}x{height}，建议: {ideal_w}x{ideal_h}"
    
    def _detect_face(self, image) -> Tuple[str, str, float]:
        """
        检测图像中的面部
        
        Returns:
            Tuple[str, str, float]: (状态, 消息, 面部比例)
        """
        try:
            # 使用OpenCV的Haar级联分类器检测面部
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
            faces = face_cascade.detectMultiScale(gray, 1.3, 5)
            
            h, w, _ = image.shape
            image_area = h * w
            
            if len(faces) == 0:
                return "fail", "未检测到面部", 0.0
            elif len(faces) > 1:
                return "warning", f"检测到多个面部({len(faces)})，请确保仅包含一个清晰的面部", 0.0
            
            # 使用第一个检测到的面部
            x, y, fw, fh = faces[0]
            face_area = fw * fh
            face_ratio = face_area / image_area
            
            if face_ratio < self.min_face_ratio:
                return "warning", f"面部太小，占图像面积的 {face_ratio:.1%}，建议至少 {self.min_face_ratio:.1%}", face_ratio
            elif face_ratio >= self.ideal_face_ratio:
                return "pass", f"面部大小良好，占图像面积的 {face_ratio:.1%}", face_ratio
            else:
                return "pass", f"面部大小可接受，占图像面积的 {face_ratio:.1%}", face_ratio
                
        except Exception as e:
            logger.error(f"面部检测失败: {str(e)}")
            return "fail", f"面部检测失败: {str(e)}", 0.0
    
    def _check_brightness(self, image) -> Tuple[str, str]:
        """
        检查图像亮度
        
        Returns:
            Tuple[str, str]: (状态, 消息)
        """
        # 转换为灰度图像
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # 计算平均亮度
        brightness = np.mean(gray)
        
        # 亮度级别判断
        if brightness < 40:
            return "fail", f"图像太暗，平均亮度: {brightness:.1f}，建议在适当光线下拍摄"
        elif brightness > 220:
            return "fail", f"图像太亮，平均亮度: {brightness:.1f}，建议避免强光或过曝"
        elif 60 <= brightness <= 180:
            return "pass", f"亮度良好，平均亮度: {brightness:.1f}"
        else:
            return "warning", f"亮度可接受，平均亮度: {brightness:.1f}，但不理想"
    
    def _check_sharpness(self, image) -> Tuple[str, str]:
        """
        检查图像清晰度
        
        Returns:
            Tuple[str, str]: (状态, 消息)
        """
        # 转换为灰度图像
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # 使用拉普拉斯算子检测边缘
        laplacian = cv2.Laplacian(gray, cv2.CV_64F)
        
        # 计算拉普拉斯算子的方差作为清晰度指标
        sharpness = np.var(laplacian)
        
        # 清晰度级别判断
        if sharpness < 50:
            return "fail", f"图像模糊，清晰度评分: {sharpness:.1f}，请使用清晰的图像"
        elif sharpness < 100:
            return "warning", f"图像清晰度可接受，评分: {sharpness:.1f}，但不理想"
        else:
            return "pass", f"图像清晰，评分: {sharpness:.1f}"
    
    def _check_duration(self, duration: float) -> Tuple[str, str]:
        """
        检查视频时长
        
        Returns:
            Tuple[str, str]: (状态, 消息)
        """
        min_duration = 2.0  # 最小时长（秒）
        max_duration = 30.0  # 最大时长（秒）
        
        if duration < min_duration:
            return "fail", f"视频太短: {duration:.1f}秒，最小要求: {min_duration}秒"
        elif duration > max_duration:
            return "warning", f"视频较长: {duration:.1f}秒，推荐最大长度: {max_duration}秒"
        else:
            return "pass", f"视频时长适中: {duration:.1f}秒"
    
    def _check_fps(self, fps: float) -> Tuple[str, str]:
        """
        检查视频帧率
        
        Returns:
            Tuple[str, str]: (状态, 消息)
        """
        min_fps = 15.0
        ideal_fps = 30.0
        
        if fps < min_fps:
            return "warning", f"帧率较低: {fps:.1f}FPS，建议至少: {min_fps}FPS"
        elif fps >= ideal_fps:
            return "pass", f"帧率良好: {fps:.1f}FPS"
        else:
            return "pass", f"帧率可接受: {fps:.1f}FPS"
    
    def _analyze_video_frames(self, cap, frame_count: int) -> List[Dict[str, str]]:
        """
        分析视频帧
        
        Returns:
            List[Dict[str, str]]: 帧检查结果列表
        """
        checks = []
        
        # 计算要分析的帧数（最多分析10帧）
        sample_count = min(10, frame_count)
        if sample_count <= 0 or frame_count <= 0:
            return checks
            
        # 计算采样间隔
        interval = frame_count // sample_count
        
        # 面部检测结果统计
        face_detected_frames = 0
        total_brightness = 0
        total_sharpness = 0
        
        # 遍历采样帧
        for i in range(sample_count):
            # 设置视频位置
            frame_pos = i * interval
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_pos)
            
            # 读取帧
            ret, frame = cap.read()
            if not ret:
                continue
                
            # 检查亮度
            brightness_status, _ = self._check_brightness(frame)
            if brightness_status == "pass":
                total_brightness += 1
                
            # 检查清晰度
            sharpness_status, _ = self._check_sharpness(frame)
            if sharpness_status == "pass":
                total_sharpness += 1
                
            # 检查面部
            face_status, _, _ = self._detect_face(frame)
            if face_status != "fail":
                face_detected_frames += 1
        
        # 计算百分比
        if sample_count > 0:
            face_percentage = (face_detected_frames / sample_count) * 100
            brightness_percentage = (total_brightness / sample_count) * 100
            sharpness_percentage = (total_sharpness / sample_count) * 100
            
            # 添加面部检测结果
            face_status = "pass" if face_percentage >= 80 else "warning" if face_percentage >= 50 else "fail"
            checks.append({
                "name": "视频中的面部",
                "status": face_status,
                "message": f"在{face_percentage:.0f}%的采样帧中检测到面部"
            })
            
            # 添加亮度检查结果
            brightness_status = "pass" if brightness_percentage >= 80 else "warning" if brightness_percentage >= 50 else "fail"
            checks.append({
                "name": "视频亮度",
                "status": brightness_status,
                "message": f"{brightness_percentage:.0f}%的采样帧亮度良好"
            })
            
            # 添加清晰度检查结果
            sharpness_status = "pass" if sharpness_percentage >= 80 else "warning" if sharpness_percentage >= 50 else "fail"
            checks.append({
                "name": "视频清晰度",
                "status": sharpness_status,
                "message": f"{sharpness_percentage:.0f}%的采样帧清晰度良好"
            })
        
        return checks
    
    def _get_brightness_message(self, status: str) -> str:
        """获取亮度消息"""
        if status == "pass":
            return "亮度良好，适合处理"
        else:
            return "亮度不佳，建议在均匀光线下拍摄"
    
    def _get_sharpness_message(self, status: str) -> str:
        """获取清晰度消息"""
        if status == "pass":
            return "图像清晰，适合处理"
        else:
            return "图像不够清晰，建议使用更高质量的相机或避免运动模糊"
            
    def _calculate_score(self, checks: List[Dict[str, str]]) -> int:
        """
        根据检查结果计算总体质量评分
        
        Args:
            checks: 检查结果列表
            
        Returns:
            int: 质量评分 (0-100)
        """
        if not checks:
            return 0
            
        # 定义权重
        weights = {
            "分辨率": 0.15,
            "面部检测": 0.25,
            "亮度条件": 0.15,
            "图像清晰度": 0.25,
            "视频时长": 0.05,
            "帧率": 0.05,
            "视频中的面部": 0.25,
            "视频亮度": 0.15,
            "视频清晰度": 0.25
        }
        
        # 定义状态分数
        status_scores = {
            "pass": 1.0,
            "warning": 0.5,
            "fail": 0.0
        }
        
        total_score = 0
        total_weight = 0
        
        for check in checks:
            name = check.get("name", "")
            status = check.get("status", "fail")
            
            # 获取权重
            weight = weights.get(name, 0.1)  # 默认权重0.1
            
            # 获取状态分数
            status_score = status_scores.get(status, 0.0)
            
            # 累加加权分数
            total_score += weight * status_score
            total_weight += weight
        
        # 计算最终评分
        if total_weight > 0:
            final_score = (total_score / total_weight) * 100
            return round(final_score)
        else:
            return 0
            
    def _get_overall_status(self, score: int) -> str:
        """
        根据分数获取整体状态
        
        Args:
            score: 质量评分
            
        Returns:
            str: 整体状态 (高质量, 可接受, 低质量)
        """
        if score >= 80:
            return "high_quality"
        elif score >= 60:
            return "acceptable"
        else:
            return "low_quality" 