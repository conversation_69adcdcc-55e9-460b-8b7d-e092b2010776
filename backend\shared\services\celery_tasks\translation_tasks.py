"""
翻译相关的Celery任务
"""
import os
import time
import asyncio
import logging
from typing import Dict, Any, List, Optional
from celery import shared_task
from celery.signals import task_prerun, task_postrun, task_failure
import shutil
from datetime import datetime

# 导入服务和工具
from services.celery_app import app
from services.translation_service import get_translation_service
from services.progress_tracker import get_progress_tracker
from services.progress_updater import ProgressUpdater
from services.asr_service import ASRService
from services.mt_service import MachineTranslationService
from services.open_source_tts import TTSService
from .base_task import BaseTask, task_progress

# 配置日志
logger = logging.getLogger(__name__)

@shared_task(name="translate_text", bind=True, max_retries=2)
def translate_text_task(
    self,
    task_id: str,
    text: str,
    source_lang: str,
    target_lang: str,
    terminology_id: Optional[str] = None,
    user_id: Optional[int] = None
) -> Dict[str, Any]:
    """
    文本翻译任务
    
    参数:
        self: Celery任务实例
        task_id: 任务ID
        text: 待翻译文本
        source_lang: 源语言
        target_lang: 目标语言
        terminology_id: 术语表ID
        user_id: 用户ID
        
    返回:
        包含任务结果的字典
    """
    start_time = time.time()
    success = False
    translation_time = 0
    error_message = None
    progress_updater = ProgressUpdater(task_id)
    
    try:
        # 创建异步事件循环
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        # 更新初始状态
        loop.run_until_complete(progress_updater.update(
            status="initialization",
            progress=5,
            message="正在初始化..."
        ))
        
        # 获取翻译服务
        translation_service = get_translation_service()
        logger.info(f"开始处理翻译任务: {task_id}, 源语言: {source_lang}, 目标语言: {target_lang}")
        
        # 更新任务状态为翻译中
        loop.run_until_complete(progress_updater.update(
            status="translating",
            progress=30,
            message="正在翻译文本..."
        ))
        
        # 执行翻译
        translated_text = loop.run_until_complete(translation_service.translate_text(
            text=text,
            source_lang=source_lang,
            target_lang=target_lang,
            terminology_id=terminology_id
        ))
        
        # 检查翻译结果
        if not translated_text:
            error_message = "翻译失败，未返回翻译结果"
            logger.error(error_message)
            loop.run_until_complete(progress_updater.update(
                status="error",
                progress=0,
                message="翻译失败",
                error=error_message
            ))
            
            return {
                "success": False,
                "error": error_message,
                "task_id": task_id
            }
        
        # 翻译成功，更新状态
        success = True
        translation_time = time.time() - start_time
        logger.info(f"翻译成功: {task_id}, 用时: {translation_time:.2f}秒")
        
        loop.run_until_complete(progress_updater.update(
            status="completed",
            progress=100,
            message="翻译完成",
            translated_text=translated_text
        ))
        
        return {
            "success": True,
            "task_id": task_id,
            "translated_text": translated_text,
            "translation_time": translation_time
        }
        
    except Exception as e:
        error_message = str(e)
        logger.exception(f"翻译异常: {task_id}, 错误: {e}")
        
        try:
            # 确保异步事件循环可用
            if 'loop' not in locals() or loop is None:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
            loop.run_until_complete(progress_updater.update(
                status="error",
                progress=0,
                message="翻译异常",
                error=error_message
            ))
        except Exception as update_error:
            logger.error(f"更新任务状态失败: {update_error}")
        
        # 尝试重试任务
        retry_count = self.request.retries
        if retry_count < self.max_retries:
            logger.info(f"尝试重试翻译任务: {task_id}, 当前重试次数: {retry_count + 1}")
            self.retry(exc=e)
        
        return {
            "success": False,
            "error": error_message,
            "task_id": task_id
        }
    finally:
        # 确保关闭事件循环
        try:
            if 'loop' in locals() and loop is not None:
                loop.close()
        except Exception as close_error:
            logger.error(f"关闭事件循环失败: {close_error}")

@shared_task(name="translate_audio", bind=True, max_retries=2, base=BaseTask)
def translate_audio_task(
    self,
    task_id: str,
    file_path: str,
    target_language: str,
    user_id: Optional[int] = None,
    source_language: Optional[str] = None,
    quality: str = "standard",
    domain: str = "",
    output_format: str = "mp3",
    keep_original: bool = False
) -> Dict[str, Any]:
    """
    音频翻译任务
    
    参数:
        self: Celery任务实例
        task_id: 任务ID
        file_path: 音频文件路径
        target_language: 目标语言
        user_id: 用户ID
        source_language: 源语言（可选）
        quality: 翻译质量
        domain: 领域
        output_format: 输出音频格式
        keep_original: 是否保留原始音频
        
    返回:
        包含任务结果的字典
    """
    start_time = time.time()
    success = False
    processing_time = 0
    error_message = None
    
    # 确保task_id是字符串类型
    if not isinstance(task_id, str):
        logger.warning(f"任务ID类型错误: {type(task_id)}，尝试转换为字符串")
        try:
            task_id = str(task_id)
        except Exception as e:
            logger.error(f"转换任务ID失败: {str(e)}")
            return {
                "success": False,
                "error": f"任务ID类型错误: {type(task_id)}",
                "task_id": task_id
            }
    
    # 记录开始执行任务的日志
    logger.info(f"开始执行任务: {self.name}[{self.request.id}], 任务ID: {task_id}")
    
    try:
        # 初始化服务
        asr_service = ASRService(model_size="small")
        mt_service = MachineTranslationService()
        tts_service = TTSService()
        
        # 使用BaseTask的方法更新任务状态
        self.update_progress(task_id, 5, "正在初始化音频翻译...")
        
        # 创建异步事件循环
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        # 验证文件是否存在
        if not os.path.exists(file_path):
            error_message = "音频文件不存在或已被删除"
            logger.error(f"音频文件不存在: {file_path}")
            self.update_progress(task_id, 0, error_message)
            return {
                "success": False,
                "error": error_message,
                "task_id": task_id
            }
            
        # 验证文件大小
        file_size = os.path.getsize(file_path)
        if file_size == 0:
            error_message = "上传的音频文件为空"
            logger.error(f"音频文件为空: {file_path}")
            self.update_progress(task_id, 0, error_message)
            return {
                "success": False,
                "error": error_message,
                "task_id": task_id
            }
        
        # 更新任务状态 - 开始转写
        self.update_progress(task_id, 10, "开始音频转写")
        
        # 音频转写
        try:
            result = loop.run_until_complete(asr_service.transcribe_audio(file_path, source_language=source_language))
            transcript, segments, detected_language = result
            
            # 成功获取转写结果，更新进度
            self.update_progress(task_id, 60, "音频转写完成，准备翻译")
        except Exception as transcribe_error:
            error_message = f"音频转写失败: {str(transcribe_error)}"
            logger.error(error_message, exc_info=True)
            self.update_progress(task_id, 0, error_message)
            return {
                "success": False,
                "error": error_message,
                "task_id": task_id
            }
        
        # 检查转写结果
        if not transcript or len(transcript.strip()) == 0:
            error_message = "音频转写失败，未能识别出文本"
            self.update_progress(task_id, 0, error_message)
            return {
                "success": False,
                "error": error_message,
                "task_id": task_id
            }
        
        # 保存转写文本
        transcript_path = f"temp/{task_id}_transcript.txt"
        os.makedirs("temp", exist_ok=True)  # 确保temp目录存在
        with open(transcript_path, "w", encoding="utf-8") as f:
            f.write(transcript)
        
        # 更新任务状态 - 翻译阶段
        self.update_progress(task_id, 70, "文本转写完成，正在翻译文本")
        
        # 调用翻译API翻译文本
        try:
            logger.info(f"[DEBUG] segments结构: {segments}")
            logger.info(f"[DEBUG] segments类型: {type(segments)}, 长度: {len(segments)}")
            if len(segments) > 0:
                logger.info(f"[DEBUG] 第一个segment内容: {segments[0]}")
            # 使用机器翻译服务翻译文本
            translated_segments = mt_service.translate_segments(
                segments, detected_language, target_language
            )
            
            # 验证翻译结果的完整性
            incomplete_segments = 0
            for i, segment in enumerate(translated_segments):
                if not segment.get('translatedText') or '[翻译失败' in segment.get('translatedText', '') or '[翻译超时' in segment.get('translatedText', ''):
                    incomplete_segments += 1
                    logger.warning(f"段落 {i+1} 翻译不完整: {segment.get('translatedText', '')}")
            
            if incomplete_segments > 0:
                logger.warning(f"检测到 {incomplete_segments}/{len(translated_segments)} 个段落翻译可能不完整")
                # 只允许单独重试失败段落，不再整体翻译+拆分
                logger.info("尝试单独处理失败段落策略")
                try:
                    for i, segment in enumerate(translated_segments):
                        if not segment.get('translatedText') or '[翻译失败' in segment.get('translatedText', '') or '[翻译超时' in segment.get('translatedText', ''):
                            logger.info(f"尝试重新翻译段落 {i+1}: {segments[i].get('text', '')}")
                            # 使用安全翻译方法
                            fixed_translation = mt_service._safe_translate(
                                segments[i].get('text', ''),
                                detected_language,
                                target_language
                            )
                            if fixed_translation and not fixed_translation.startswith('[翻译失败'):
                                segment['translatedText'] = fixed_translation
                                logger.info(f"段落 {i+1} 修复成功: {fixed_translation}")
                            else:
                                logger.error(f"段落 {i+1} 修复失败: {fixed_translation}")
                except Exception as fix2_error:
                    logger.error(f"应用修复策略2失败: {str(fix2_error)}")
                
                # 重新统计不完整段落
                incomplete_segments = sum(1 for seg in translated_segments 
                                       if not seg.get('translatedText') or 
                                       '[翻译失败' in seg.get('translatedText', '') or 
                                       '[翻译超时' in seg.get('translatedText', ''))
                
                self.update_progress(task_id, 80, f"警告: {incomplete_segments}个段落翻译不完整，尝试恢复中")
            
            # 整体译文由所有分段译文拼接而成，确保每个段落都有译文
            translated_texts = []
            for i, segment in enumerate(translated_segments):
                trans_text = segment.get('translatedText', '')
                if trans_text and not trans_text.startswith('[翻译失败') and not trans_text.startswith('[翻译超时'):
                    translated_texts.append(trans_text)
                    logger.info(f"段落 {i+1} 翻译成功: {trans_text}")
                else:
                    logger.warning(f"段落 {i+1} 翻译失败或超时: {trans_text}")
            
            # 合并所有有效的翻译文本
            if translated_texts:
                translated_text = " ".join(translated_texts)
                logger.info(f"合并后的翻译文本: {translated_text}")
            else:
                # 如果没有有效的译文段落，使用原文作为翻译结果
                logger.warning("没有有效的译文段落，直接返回原文")
                translated_text = transcript
            
            # 保存翻译文本
            translation_text_path = f"temp/{task_id}_translation.txt"
            with open(translation_text_path, "w", encoding="utf-8") as f:
                f.write(translated_text)
            
            logger.info(f"文本翻译完成，从 {detected_language} 翻译到 {target_language}，质量: {quality}, 领域: {domain}")
        except Exception as translate_error:
            logger.error(f"文本翻译失败: {str(translate_error)}", exc_info=True)
            # 翻译失败时，使用原文作为翻译结果，但标记出错误
            translated_text = f"[翻译失败] {transcript}"
            # 生成带有错误标记的段落
            translated_segments = []
            for segment in segments:
                translated_segments.append({
                    "startTime": segment.get("start", 0),
                    "endTime": segment.get("end", 0),
                    "text": segment.get("text", ""),
                    "translatedText": f"[翻译失败] {segment.get('text', '')}"
                })
        
        # 使用TTS生成翻译后的音频
        self.update_progress(task_id, 90, "文本翻译完成，正在生成翻译后的音频")
        
        # 验证翻译文本的有效性
        if not translated_text or translated_text.startswith('[翻译失败]') or len(translated_text.strip()) < 10:
            logger.warning(f"翻译文本可能无效: '{translated_text[:50]}...'")
            
            # 尝试执行修复 - 重新翻译
            try:
                logger.info("尝试使用备用方法重新翻译文本")
                # 直接翻译完整文本
                backup_translation = loop.run_until_complete(mt_service.translate_text(
                    transcript, detected_language, target_language
                ))
                
                # 检查备用翻译是否成功
                if backup_translation and not backup_translation.startswith('[翻译失败]'):
                    logger.info(f"备用翻译成功: {backup_translation}")
                    translated_text = backup_translation
                    
                    # 更新翻译段落
                    if len(segments) <= 3:  # 对于段落较少的情况，尝试重新分配
                        # 尝试将整体翻译拆分为对应段落
                        try:
                            from services.mt_service import MachineTranslationService
                            mt_helper = MachineTranslationService()
                            parts = mt_helper._split_translation(translated_text, len(segments))
                            
                            if len(parts) == len(segments):
                                for i, segment in enumerate(segments):
                                    translated_segments[i]['translatedText'] = parts[i]
                                    logger.info(f"段落 {i+1} 更新为: {parts[i]}")
                                logger.info("成功将备用翻译拆分为对应段落")
                            else:
                                logger.warning(f"拆分结果段落数不匹配: 预期 {len(segments)}, 实际 {len(parts)}")
                        except Exception as split_error:
                            logger.error(f"拆分备用翻译失败: {str(split_error)}")
                    else:
                        # 对于段落较多的情况，尝试逐段翻译
                        logger.info("段落较多，尝试逐段翻译")
                        try:
                            for i, segment in enumerate(segments):
                                segment_text = segment.get('text', '')
                                if segment_text:
                                    segment_translation = loop.run_until_complete(mt_service.translate_text(
                                        segment_text, detected_language, target_language
                                    ))
                                    if segment_translation and not segment_translation.startswith('[翻译失败]'):
                                        translated_segments[i]['translatedText'] = segment_translation
                                        logger.info(f"段落 {i+1} 单独翻译成功: {segment_translation}")
                        except Exception as segment_error:
                            logger.error(f"逐段翻译失败: {str(segment_error)}")
                else:
                    logger.warning(f"备用翻译也失败了: {backup_translation}")
            except Exception as backup_error:
                logger.error(f"备用翻译失败: {str(backup_error)}")
                
        translated_audio_path = None
        try:
            # 生成翻译音频文件路径，按照指定的输出格式
            translated_audio_path = f"temp/{task_id}_translated.{output_format}"
            
            # 调用TTS服务生成翻译后的音频
            logger.info(f"开始生成翻译音频，目标语言: {target_language}，格式: {output_format}")
            loop.run_until_complete(tts_service.text_to_speech_async(
                text=translated_text,
                language=target_language,
                output_path=translated_audio_path
            ))
            logger.info(f"翻译音频生成成功: {translated_audio_path}")
            
            # 检查生成的音频文件
            if not os.path.exists(translated_audio_path) or os.path.getsize(translated_audio_path) == 0:
                logger.warning("生成的翻译音频文件为空或不存在")
                translated_audio_path = None
                
                # 更新错误状态
                error_message = "生成翻译音频失败"
                self.update_progress(task_id, 0, error_message)
                return {
                    "success": False,
                    "error": error_message,
                    "task_id": task_id
                }
        except Exception as tts_error:
            logger.error(f"生成翻译音频失败: {str(tts_error)}", exc_info=True)
            
            # 更新错误状态
            error_message = f"生成翻译音频失败: {str(tts_error)}"
            self.update_progress(task_id, 0, error_message)
            return {
                "success": False,
                "error": error_message,
                "task_id": task_id
            }
        
        # 确保音频文件目录存在
        os.makedirs("results/audio", exist_ok=True)
        
        # 将临时文件复制到结果目录
        result_audio_path = f"results/audio/{task_id}_translated.{output_format}"
        shutil.copy2(translated_audio_path, result_audio_path)
        
        # 完成所有处理后，更新状态为已完成
        processing_time = time.time() - start_time
        success = True
        
        # 构建结果数据
        result_data = {
            "original_audio": file_path,
            "translated_audio": result_audio_path,
            "transcript": transcript,
            "translated_text": translated_text,
            "source_language": detected_language,
            "target_language": target_language,
            "segments": segments,  # 原始段落
            "translated_segments": translated_segments,  # 翻译后的段落
            "processing_time": processing_time,
            # 添加元数据以便前端判断翻译质量
            "metadata": {
                "segments_count": len(segments),
                "transcript_length": len(transcript),
                "translation_length": len(translated_text),
                "incomplete_segments": sum(1 for seg in translated_segments 
                                         if not seg.get('translatedText') or 
                                         '[翻译失败' in seg.get('translatedText', '') or 
                                         '[翻译超时' in seg.get('translatedText', '')),
                "translation_quality": "normal"  # 可能的值: normal, partial, failed
            }
        }
        
        # 评估翻译质量
        incomplete_ratio = result_data["metadata"]["incomplete_segments"] / result_data["metadata"]["segments_count"] if result_data["metadata"]["segments_count"] > 0 else 0
        if incomplete_ratio > 0.5:
            result_data["metadata"]["translation_quality"] = "failed"
            logger.warning(f"翻译质量评估: 失败 (超过一半段落翻译不完整)")
        elif incomplete_ratio > 0:
            result_data["metadata"]["translation_quality"] = "partial"
            logger.warning(f"翻译质量评估: 部分成功 ({incomplete_ratio:.0%}段落翻译不完整)")
        else:
            logger.info("翻译质量评估: 正常 (所有段落都正常翻译)")
        
        # 记录翻译结果详情
        logger.info(f"翻译结果详情:")
        logger.info(f"- 原文长度: {len(transcript)}")
        logger.info(f"- 译文长度: {len(translated_text)}")
        logger.info(f"- 段落数量: {len(segments)}")
        logger.info(f"- 不完整段落: {result_data['metadata']['incomplete_segments']}")
        logger.info(f"- 翻译质量: {result_data['metadata']['translation_quality']}")
        
        # 更新任务完成状态
        status_message = "音频翻译任务完成"
        if result_data["metadata"]["translation_quality"] != "normal":
            status_message += f" (翻译质量: {result_data['metadata']['translation_quality']})"
            
        self.update_progress(
            task_id, 
            100, 
            status_message,
            result_url=result_audio_path
        )
        
        # 记录任务完成日志
        logger.info(f"音频翻译任务完成: {task_id}, 用时: {processing_time:.2f}秒")
        
        return {
            "success": True,
            "task_id": task_id,
            "result": result_data
        }
        
    except Exception as e:
        error_message = str(e)
        logger.exception(f"音频翻译任务异常: {task_id}, 错误: {e}")
        
        try:
            # 更新错误状态
            self.update_progress(task_id, 0, f"音频翻译异常: {error_message}")
        except Exception as update_error:
            logger.error(f"更新任务状态失败: {update_error}")
        
        # 尝试重试任务
        retry_count = self.request.retries
        if retry_count < self.max_retries:
            logger.info(f"尝试重试音频翻译任务: {task_id}, 当前重试次数: {retry_count + 1}")
            self.retry(exc=e)
        
        return {
            "success": False,
            "error": error_message,
            "task_id": task_id
        }
    finally:
        # 确保关闭事件循环
        try:
            if 'loop' in locals() and loop is not None:
                loop.close()
        except Exception as close_error:
            logger.error(f"关闭事件循环失败: {close_error}")
        
        # 记录任务完成时间
        processing_time = time.time() - start_time
        logger.info(f"完成任务: {self.name}[{self.request.id}], 任务ID: {task_id}, 状态: {'SUCCESS' if success else 'FAILURE'}")

@shared_task(name="async_text_translate_task", bind=True, max_retries=2)
def async_text_translate_task(self, text, source_lang, target_lang, domain=None, model=None):
    """
    文本翻译Celery异步任务
    """
    try:
        mt_service = MachineTranslationService()
        
        # 如果源语言为auto，先检测是否包含中文
        if source_lang == "auto":
            # 检查是否包含中文字符
            if mt_service.contains_chinese(text):
                logger.info(f"检测到中文字符，设置源语言为中文")
                source_lang = "zh"  # 设置源语言为中文
        
        # 记录翻译请求详情
        logger.info(f"开始翻译: '{text[:50]}{'...' if len(text) > 50 else ''}' 从 {source_lang} 到 {target_lang}, 模型: {model or '默认'}")
        
        # 执行翻译 - 移除domain参数，因为translate_sync方法不接受此参数
        translated_text, detected_language = mt_service.translate_sync(
            text=text,
            source_lang=source_lang,
            target_lang=target_lang,
            model=model
        )
        
        # 确保翻译结果不为空或不是[空翻译]
        if not translated_text or translated_text.strip() == "" or translated_text == "[空翻译]":
            logger.warning(f"翻译结果为空或为[空翻译]，源文本: '{text[:50]}...'")
            
            # 尝试再次翻译，使用不同的参数
            try:
                # 使用备用模型或参数
                backup_model = "llama3" if model != "llama3" else "deepseek-r1:8b"
                logger.info(f"尝试使用备用模型 {backup_model} 重新翻译")
                translated_text, detected_language = mt_service.translate_sync(
                    text=text,
                    source_lang=source_lang,
                    target_lang=target_lang,
                    model=backup_model
                )
                
                # 检查备用翻译结果
                if translated_text and translated_text.strip() != "" and translated_text != "[空翻译]":
                    logger.info(f"备用翻译成功: '{translated_text[:50]}...'")
                else:
                    logger.warning(f"备用翻译仍然为空或为[空翻译]: '{translated_text}'")
            except Exception as backup_error:
                logger.error(f"备用翻译失败: {str(backup_error)}")
                translated_text = ""  # 确保变量已定义
            
            # 如果仍然为空，尝试使用简单翻译字典
            if not translated_text or translated_text.strip() == "" or translated_text == "[空翻译]":
                # 提供一个扩展的简单翻译字典作为后备
                if source_lang == "zh" and target_lang == "en":
                    simple_translations = {
                        "你好": "Hello",
                        "谢谢": "Thank you",
                        "早上好": "Good morning",
                        "下午好": "Good afternoon",
                        "晚上好": "Good evening",
                        "再见": "Goodbye",
                        "是的": "Yes",
                        "不是": "No",
                        "对不起": "Sorry",
                        "没关系": "It's okay",
                        "请": "Please",
                        "好的": "OK",
                        "我爱你": "I love you",
                        "多少钱": "How much",
                        "我不明白": "I don't understand",
                        "帮助": "Help",
                        "我想要": "I want",
                        "今天": "Today",
                        "明天": "Tomorrow",
                        "昨天": "Yesterday",
                        "现在": "Now",
                        "这个": "This",
                        "那个": "That",
                        "所以": "So",
                        "因为": "Because",
                        "如果": "If",
                        "但是": "But",
                        "可以": "Can",
                        "不可以": "Cannot",
                        "需要": "Need",
                        "想要": "Want",
                        "喜欢": "Like",
                        "不喜欢": "Don't like",
                        "所以直接转行吧": "So just switch careers directly"
                    }
                    
                    # 检查完全匹配
                    if text.strip() in simple_translations:
                        logger.info(f"使用预定义翻译(完全匹配): '{text}' -> '{simple_translations[text.strip()]}'")
                        return {
                            "success": True,
                            "translated_text": simple_translations[text.strip()],
                            "detected_language": detected_language,
                            "original_text": text
                        }
                    
                    # 检查部分匹配
                    for key in simple_translations:
                        if key in text:
                            logger.info(f"使用预定义翻译(部分匹配): 包含'{key}' -> 使用'{simple_translations[key]}'")
                            # 对于简短文本，直接使用匹配的翻译
                            if len(text) < 20:
                                return {
                                    "success": True,
                                    "translated_text": simple_translations[key],
                                    "detected_language": detected_language,
                                    "original_text": text
                                }
                
                # 如果没有匹配的简单翻译，返回错误信息
                logger.error(f"无法翻译文本: '{text}'")
                return {
                    "success": False,
                    "error": "无法生成有效的翻译结果，请重试",
                    "detected_language": detected_language,
                    "original_text": text
                }
        
        # 翻译成功
        logger.info(f"翻译成功: '{text[:30]}...' -> '{translated_text[:30]}...'")
        return {
            "success": True,
            "translated_text": translated_text,
            "detected_language": detected_language,
            "original_text": text
        }
    except Exception as e:
        import traceback
        logger.error(f"翻译失败: {str(e)}\n{traceback.format_exc()}")
        return {
            "success": False,
            "error": str(e),
            "traceback": traceback.format_exc(),
            "original_text": text
        } 