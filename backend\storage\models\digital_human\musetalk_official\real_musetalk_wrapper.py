#!/usr/bin/env python3
"""
真正的 MuseTalk 包装器
调用官方 MuseTalk 实现
"""

import os
import sys
import argparse
import logging
import subprocess
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='真正的 MuseTalk 数字人生成')
    parser.add_argument('--source_image', required=True, help='源图像路径')
    parser.add_argument('--driving_audio', required=True, help='驱动音频路径')
    parser.add_argument('--output', required=True, help='输出视频路径')
    parser.add_argument('--device', default='cuda', choices=['cuda', 'cpu'], help='设备类型')
    parser.add_argument('--batch_size', type=int, default=8, help='批处理大小')
    parser.add_argument('--fps', type=int, default=25, help='输出视频帧率')
    parser.add_argument('--quality', default='high', choices=['low', 'medium', 'high'], help='输出质量')
    return parser.parse_args()

def check_models():
    """检查模型文件是否存在"""
    try:
        base_dir = Path(__file__).parent
        models_dir = base_dir / "models"
        
        required_models = [
            "musetalkV15/unet.pth",
            "musetalkV15/musetalk.json",
            "sd-vae/diffusion_pytorch_model.bin",
            "sd-vae/config.json",
            "whisper/pytorch_model.bin"
        ]
        
        missing_models = []
        for model_path in required_models:
            full_path = models_dir / model_path
            if not full_path.exists():
                missing_models.append(model_path)
        
        if missing_models:
            logger.warning(f"缺少模型文件: {missing_models}")
            return False
        
        logger.info("✅ 所有必要的 MuseTalk 模型文件都存在")
        return True
        
    except Exception as e:
        logger.error(f"检查模型文件时出错: {str(e)}")
        return False

def call_official_musetalk(source_image, driving_audio, output_path, device='cuda', fps=25):
    """调用官方 MuseTalk 推理脚本"""
    try:
        base_dir = Path(__file__).parent
        inference_script = base_dir / "scripts" / "inference.py"
        
        if not inference_script.exists():
            logger.error(f"官方推理脚本不存在: {inference_script}")
            return False
        
        # 构建命令
        cmd = [
            sys.executable,
            str(inference_script),
            "--source_image", source_image,
            "--driving_audio", driving_audio,
            "--result_dir", str(Path(output_path).parent),
            "--fps", str(fps),
            "--batch_size", "8"
        ]
        
        if device == 'cuda':
            cmd.extend(["--device", "cuda"])
        else:
            cmd.extend(["--device", "cpu"])
        
        logger.info(f"执行官方 MuseTalk 命令: {' '.join(cmd)}")
        
        # 设置环境变量
        env = os.environ.copy()
        env['PYTHONPATH'] = str(base_dir)
        
        # 执行命令
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            cwd=str(base_dir),
            env=env
        )
        
        logger.info(f"MuseTalk 进程返回码: {result.returncode}")
        
        if result.stdout:
            logger.info(f"MuseTalk 标准输出: {result.stdout}")
        
        if result.stderr:
            logger.info(f"MuseTalk 错误输出: {result.stderr}")
        
        if result.returncode == 0:
            # 查找生成的视频文件
            result_dir = Path(output_path).parent
            video_files = list(result_dir.glob("*.mp4"))
            
            if video_files:
                # 移动到指定输出路径
                generated_video = video_files[0]
                if generated_video != Path(output_path):
                    import shutil
                    shutil.move(str(generated_video), output_path)
                
                logger.info(f"✅ 官方 MuseTalk 生成成功: {output_path}")
                return True
            else:
                logger.error("未找到生成的视频文件")
                return False
        else:
            logger.error(f"官方 MuseTalk 执行失败 (返回码: {result.returncode})")
            return False
        
    except Exception as e:
        logger.error(f"调用官方 MuseTalk 时出错: {str(e)}")
        return False

def fallback_to_wav2lip(source_image, driving_audio, output_path, fps=25):
    """回退到 Wav2Lip++ 实现"""
    try:
        logger.info("🔄 回退到 Wav2Lip++ 实现...")
        
        # 查找 Wav2Lip++ 脚本
        backend_dir = Path(__file__).parent.parent.parent
        wav2lip_script = backend_dir / "local_models" / "Wav2Lip" / "inference.py"
        
        if not wav2lip_script.exists():
            logger.error(f"Wav2Lip++ 脚本不存在: {wav2lip_script}")
            return False
        
        cmd = [
            sys.executable,
            str(wav2lip_script),
            "-s", source_image,
            "-a", driving_audio,
            "-o", output_path,
            "--device", "cuda",
            "--quality", "high",
            "--fps", str(fps),
            "--size", "512"
        ]
        
        logger.info(f"执行 Wav2Lip++ 命令: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            logger.info("✅ Wav2Lip++ 回退成功")
            return True
        else:
            logger.error(f"Wav2Lip++ 回退失败: {result.stderr}")
            return False
        
    except Exception as e:
        logger.error(f"Wav2Lip++ 回退时出错: {str(e)}")
        return False

def main():
    """主函数"""
    try:
        args = parse_args()
        
        logger.info("🎬 开始真正的 MuseTalk 数字人生成...")
        logger.info(f"源图像: {args.source_image}")
        logger.info(f"驱动音频: {args.driving_audio}")
        logger.info(f"输出视频: {args.output}")
        logger.info(f"设备: {args.device}")
        
        # 检查输入文件
        if not os.path.exists(args.source_image):
            raise FileNotFoundError(f"源图像不存在: {args.source_image}")
        
        if not os.path.exists(args.driving_audio):
            raise FileNotFoundError(f"驱动音频不存在: {args.driving_audio}")
        
        # 确保输出目录存在
        output_dir = os.path.dirname(args.output)
        if output_dir:
            os.makedirs(output_dir, exist_ok=True)
        
        # 检查模型文件
        models_available = check_models()
        
        success = False
        
        if models_available:
            # 尝试使用官方 MuseTalk
            logger.info("🎯 尝试使用官方 MuseTalk...")
            success = call_official_musetalk(
                args.source_image,
                args.driving_audio,
                args.output,
                args.device,
                args.fps
            )
        
        if not success:
            # 回退到 Wav2Lip++
            logger.info("🔄 回退到 Wav2Lip++ 实现...")
            success = fallback_to_wav2lip(
                args.source_image,
                args.driving_audio,
                args.output,
                args.fps
            )
        
        if success:
            # 验证输出文件
            if os.path.exists(args.output) and os.path.getsize(args.output) > 1024:
                logger.info("🎉 真正的 MuseTalk 数字人生成完成！")
                logger.info(f"输出文件: {args.output}")
                logger.info(f"文件大小: {os.path.getsize(args.output)} bytes")
                sys.exit(0)
            else:
                logger.error("生成的视频文件无效或太小")
                sys.exit(1)
        else:
            logger.error("❌ 所有方法都失败了")
            sys.exit(1)
        
    except Exception as e:
        logger.error(f"真正的 MuseTalk 推理失败: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
