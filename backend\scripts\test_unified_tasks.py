#!/usr/bin/env python3
"""
测试统一任务管理系统
"""
import os
import sys
import uuid
import time
from pathlib import Path
from dotenv import load_dotenv

# 添加后端路径
backend_dir = Path(__file__).parent.parent
sys.path.append(str(backend_dir))

# 加载环境变量
env_path = backend_dir / ".env"
load_dotenv(env_path)

def test_task_manager():
    """测试任务管理器"""
    print("🧪 测试统一任务管理器")
    print("=" * 50)
    
    try:
        from app.core.task_manager import task_manager
        from app.models.unified_tasks import TaskTypes, TaskStatus
        
        # 创建测试任务
        task_id = task_manager.create_task(
            task_type=TaskTypes.VIDEO_GENERATION,
            task_subtype=TaskTypes.VideoGeneration.TEXT_TO_VIDEO,
            user_id="test-user",
            title="测试视频生成",
            description="这是一个测试任务",
            input_params={"prompt": "测试提示词", "model": "t2v-1.3B"},
            priority=7,
            estimated_duration=60,
            tags=["test", "video", "wanx"]
        )
        
        print(f"✅ 任务创建成功: {task_id}")
        
        # 获取任务详情
        task_info = task_manager.get_task(task_id)
        if task_info:
            print(f"📋 任务详情:")
            print(f"   类型: {task_info['task_type']}")
            print(f"   子类型: {task_info['task_subtype']}")
            print(f"   状态: {task_info['status']}")
            print(f"   进度: {task_info['progress']}%")
            print(f"   标题: {task_info['title']}")
        
        # 更新任务状态
        task_manager.update_task(
            task_id,
            status=TaskStatus.PROCESSING,
            progress=50,
            message="正在处理中..."
        )
        
        print(f"✅ 任务状态更新成功")
        
        # 再次获取任务详情
        updated_task = task_manager.get_task(task_id)
        if updated_task:
            print(f"📊 更新后状态: {updated_task['status']} ({updated_task['progress']}%)")
        
        # 获取用户任务列表
        user_tasks = task_manager.get_user_tasks("test-user")
        print(f"📋 用户任务数量: {len(user_tasks)}")
        
        # 获取任务统计
        stats = task_manager.get_task_stats("test-user")
        print(f"📊 任务统计: {stats}")
        
        return True
        
    except Exception as e:
        print(f"❌ 任务管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_celery_task():
    """测试 Celery 任务"""
    print("\n🧪 测试 Celery 统一任务")
    print("=" * 50)
    
    try:
        from app.tasks.test_unified_task import test_unified_task, test_wanx_simple
        from app.core.task_manager import task_manager
        from app.models.unified_tasks import TaskTypes
        
        # 先在统一任务表中创建任务记录
        task_id = task_manager.create_task(
            task_type=TaskTypes.VIDEO_GENERATION,
            task_subtype="test",
            user_id="test-user",
            title="Celery 测试任务",
            description="测试 Celery 与统一任务管理的集成",
            input_params={"test_message": "Hello Unified Tasks!", "duration": 5}
        )
        
        print(f"📝 任务ID: {task_id}")
        
        # 启动 Celery 任务
        result = test_unified_task.delay(
            task_id=task_id,
            test_message="Hello Unified Tasks!",
            duration=5
        )
        
        print(f"✅ Celery 任务已提交")
        print(f"📊 Celery 任务ID: {result.id}")
        
        # 监控任务进度
        print(f"\n⏳ 监控任务进度...")
        for i in range(30):  # 最多等待30秒
            task_info = task_manager.get_task(task_id)
            if task_info:
                status = task_info['status']
                progress = task_info['progress']
                message = task_info['message']
                
                print(f"📊 {status} - {progress}% - {message}")
                
                if status in ['completed', 'failed']:
                    break
            
            time.sleep(1)
        
        # 最终状态
        final_task = task_manager.get_task(task_id)
        if final_task:
            print(f"\n🎯 最终状态:")
            print(f"   状态: {final_task['status']}")
            print(f"   进度: {final_task['progress']}%")
            print(f"   消息: {final_task['message']}")
            if final_task.get('output_data'):
                print(f"   输出: {final_task['output_data']}")
            if final_task.get('error_message'):
                print(f"   错误: {final_task['error_message']}")
        
        return final_task and final_task['status'] == 'completed'
        
    except Exception as e:
        print(f"❌ Celery 任务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_wanx_simulation():
    """测试 Wanx 模拟任务"""
    print("\n🎬 测试 Wanx 模拟任务")
    print("=" * 50)
    
    try:
        from app.tasks.test_unified_task import test_wanx_simple
        from app.core.task_manager import task_manager
        from app.models.unified_tasks import TaskTypes
        
        # 创建 Wanx 模拟任务
        task_id = task_manager.create_task(
            task_type=TaskTypes.VIDEO_GENERATION,
            task_subtype=TaskTypes.VideoGeneration.TEXT_TO_VIDEO,
            user_id="test-user",
            title="Wanx 2.1 模拟测试",
            description="模拟 Wanx 2.1 文本转视频任务",
            input_params={"prompt": "一只可爱的小猫在草地上玩耍"},
            estimated_duration=120
        )
        
        print(f"📝 Wanx 任务ID: {task_id}")
        
        # 启动模拟任务
        result = test_wanx_simple.delay(
            task_id=task_id,
            prompt="一只可爱的小猫在草地上玩耍"
        )
        
        print(f"✅ Wanx 模拟任务已提交")
        print(f"📊 Celery 任务ID: {result.id}")
        
        # 监控任务进度
        print(f"\n⏳ 监控 Wanx 任务进度...")
        for i in range(20):  # 最多等待20秒
            task_info = task_manager.get_task(task_id)
            if task_info:
                status = task_info['status']
                progress = task_info['progress']
                message = task_info['message']
                
                print(f"🎬 {status} - {progress}% - {message}")
                
                if status in ['completed', 'failed']:
                    break
            
            time.sleep(1)
        
        # 最终状态
        final_task = task_manager.get_task(task_id)
        if final_task:
            print(f"\n🎯 Wanx 任务最终状态:")
            print(f"   状态: {final_task['status']}")
            print(f"   进度: {final_task['progress']}%")
            print(f"   消息: {final_task['message']}")
            if final_task.get('output_data'):
                output = final_task['output_data']
                print(f"   视频URL: {output.get('video_url', 'N/A')}")
                print(f"   提示词: {output.get('prompt', 'N/A')}")
        
        return final_task and final_task['status'] == 'completed'
        
    except Exception as e:
        print(f"❌ Wanx 模拟任务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🧪 统一任务管理系统测试")
    print("=" * 60)
    
    # 测试任务管理器
    manager_ok = test_task_manager()
    
    # 测试 Celery 任务
    celery_ok = test_celery_task()
    
    # 测试 Wanx 模拟
    wanx_ok = test_wanx_simulation()
    
    # 总结
    print(f"\n📊 测试总结:")
    print(f"{'✅' if manager_ok else '❌'} 任务管理器: {'正常' if manager_ok else '失败'}")
    print(f"{'✅' if celery_ok else '❌'} Celery 集成: {'正常' if celery_ok else '失败'}")
    print(f"{'✅' if wanx_ok else '❌'} Wanx 模拟: {'正常' if wanx_ok else '失败'}")
    
    if manager_ok and celery_ok and wanx_ok:
        print(f"\n🎉 统一任务管理系统测试通过！")
        print(f"💡 现在可以更新 Wanx 视频生成任务使用统一管理")
    else:
        print(f"\n⚠️ 部分测试失败，需要进一步调试")

if __name__ == "__main__":
    main()
