"""
模板管理API路由
"""

from fastapi import APIRouter, HTTPException, Depends, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from ...core.sqlalchemy_db import get_db
from ...models.avatar_template import AvatarTemplate
from ...models.voice_template import VoiceTemplate

router = APIRouter()

@router.get("/avatars")
async def get_avatar_templates(
    db: Session = Depends(get_db),
    category: Optional[str] = Query(None, description="分类筛选"),
    gender: Optional[str] = Query(None, description="性别筛选"),
    style: Optional[str] = Query(None, description="风格筛选"),
    is_featured: Optional[bool] = Query(None, description="是否推荐"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    sort_by: str = Query("display_priority", description="排序字段"),
    sort_order: str = Query("desc", description="排序方向")
):
    """获取头像模板列表"""
    try:
        query = db.query(AvatarTemplate).filter(AvatarTemplate.is_active == True)
        
        # 应用筛选条件
        if category:
            query = query.filter(AvatarTemplate.category == category)
        if gender:
            query = query.filter(AvatarTemplate.gender == gender)
        if style:
            query = query.filter(AvatarTemplate.style == style)
        if is_featured is not None:
            query = query.filter(AvatarTemplate.is_featured == is_featured)
        
        # 排序
        if sort_order.lower() == "desc":
            query = query.order_by(getattr(AvatarTemplate, sort_by).desc())
        else:
            query = query.order_by(getattr(AvatarTemplate, sort_by).asc())
        
        # 分页
        total = query.count()
        offset = (page - 1) * page_size
        templates = query.offset(offset).limit(page_size).all()
        
        return {
            "success": True,
            "data": {
                "templates": [template.to_dict() for template in templates],
                "pagination": {
                    "page": page,
                    "page_size": page_size,
                    "total": total,
                    "pages": (total + page_size - 1) // page_size
                }
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取头像模板失败: {str(e)}")

@router.get("/avatars/categories")
async def get_avatar_categories(db: Session = Depends(get_db)):
    """获取头像分类列表"""
    try:
        categories = db.query(AvatarTemplate.category).filter(
            AvatarTemplate.is_active == True
        ).distinct().all()
        
        return {
            "success": True,
            "data": [cat[0] for cat in categories if cat[0]]
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取分类失败: {str(e)}")

@router.get("/avatars/featured")
async def get_featured_avatars(
    db: Session = Depends(get_db),
    limit: int = Query(6, ge=1, le=20, description="返回数量")
):
    """获取推荐头像模板"""
    try:
        templates = db.query(AvatarTemplate).filter(
            AvatarTemplate.is_active == True,
            AvatarTemplate.is_featured == True
        ).order_by(
            AvatarTemplate.display_priority.desc(),
            AvatarTemplate.popularity_score.desc()
        ).limit(limit).all()
        
        return {
            "success": True,
            "data": [template.to_dict() for template in templates]
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取推荐头像失败: {str(e)}")

@router.get("/voices")
async def get_voice_templates(
    db: Session = Depends(get_db),
    category: Optional[str] = Query(None, description="分类筛选"),
    gender: Optional[str] = Query(None, description="性别筛选"),
    language: Optional[str] = Query(None, description="语言筛选"),
    voice_engine: Optional[str] = Query(None, description="语音引擎筛选"),
    is_featured: Optional[bool] = Query(None, description="是否推荐"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量")
):
    """获取声音模板列表"""
    try:
        query = db.query(VoiceTemplate).filter(VoiceTemplate.is_active == True)
        
        # 应用筛选条件
        if category:
            query = query.filter(VoiceTemplate.category == category)
        if gender:
            query = query.filter(VoiceTemplate.gender == gender)
        if language:
            query = query.filter(VoiceTemplate.language == language)
        if voice_engine:
            query = query.filter(VoiceTemplate.voice_engine == voice_engine)
        if is_featured is not None:
            query = query.filter(VoiceTemplate.is_featured == is_featured)
        
        # 排序
        query = query.order_by(
            VoiceTemplate.display_priority.desc(),
            VoiceTemplate.popularity_score.desc()
        )
        
        # 分页
        total = query.count()
        offset = (page - 1) * page_size
        templates = query.offset(offset).limit(page_size).all()
        
        return {
            "success": True,
            "data": {
                "templates": [template.to_dict() for template in templates],
                "pagination": {
                    "page": page,
                    "page_size": page_size,
                    "total": total,
                    "pages": (total + page_size - 1) // page_size
                }
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取声音模板失败: {str(e)}")

@router.get("/voices/featured")
async def get_featured_voices(
    db: Session = Depends(get_db),
    limit: int = Query(8, ge=1, le=20, description="返回数量")
):
    """获取推荐声音模板"""
    try:
        templates = db.query(VoiceTemplate).filter(
            VoiceTemplate.is_active == True,
            VoiceTemplate.is_featured == True
        ).order_by(
            VoiceTemplate.display_priority.desc(),
            VoiceTemplate.popularity_score.desc()
        ).limit(limit).all()
        
        return {
            "success": True,
            "data": [template.to_dict() for template in templates]
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取推荐声音失败: {str(e)}")

@router.post("/avatars/{template_id}/use")
async def use_avatar_template(template_id: str, db: Session = Depends(get_db)):
    """使用头像模板（增加使用计数）"""
    try:
        template = db.query(AvatarTemplate).filter(
            AvatarTemplate.id == template_id
        ).first()
        
        if not template:
            raise HTTPException(status_code=404, detail="头像模板不存在")
        
        template.usage_count += 1
        db.commit()
        
        return {
            "success": True,
            "message": "使用记录已更新"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新使用记录失败: {str(e)}")

@router.post("/voices/{template_id}/use")
async def use_voice_template(template_id: str, db: Session = Depends(get_db)):
    """使用声音模板（增加使用计数）"""
    try:
        template = db.query(VoiceTemplate).filter(
            VoiceTemplate.id == template_id
        ).first()
        
        if not template:
            raise HTTPException(status_code=404, detail="声音模板不存在")
        
        template.usage_count += 1
        db.commit()
        
        return {
            "success": True,
            "message": "使用记录已更新"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新使用记录失败: {str(e)}")
