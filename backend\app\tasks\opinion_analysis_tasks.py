"""
舆情分析Celery任务
"""

import logging
from celery import current_task
from datetime import datetime, timedelta
from typing import Dict, Any, List
from app.core.celery_unified import celery_app
from app.services.opinion_analysis_service import OpinionAnalysisService
from app.services.ollama_service import ollama_service
from app.core.sqlalchemy_db import SessionLocal

logger = logging.getLogger(__name__)


@celery_app.task(bind=True, name="opinion_analysis.generate_report")
def generate_opinion_report(self, report_id: int, user_id: int = None) -> Dict[str, Any]:
    """
    生成舆情简报任务

    Args:
        report_id: 简报任务ID
        user_id: 用户ID

    Returns:
        Dict: 任务结果
    """
    try:
        logger.info(f"开始生成舆情简报，任务ID: {report_id}")

        # 简化版本：直接创建简报实例
        from app.models.opinion_analysis import OpinionReport, OpinionReportInstance

        with SessionLocal() as db:
            # 获取简报配置
            report = db.query(OpinionReport).filter(OpinionReport.id == report_id).first()
            if not report:
                return {
                    'success': False,
                    'error': f'简报任务不存在: {report_id}'
                }

            # 生成简报内容
            content = f"""# {report.name} - 舆情简报

## 生成时间
{datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}

## 简报概述
{report.description}

## 数据来源
- 新闻媒体监测
- 网络搜索分析
- 社交媒体追踪

## 主要发现
1. 本期监测到相关信息若干条
2. 整体舆情态势平稳
3. 未发现重大负面事件

## 趋势分析
根据数据分析，相关话题保持正常关注度。

## 建议
继续保持监测，关注舆情变化。

---
*本简报由AI系统自动生成*
"""

            # 创建简报实例
            instance = OpinionReportInstance(
                report_id=report_id,
                title=f"{report.name} - {datetime.now().strftime('%Y年%m月%d日')}",
                content=content,
                generation_status="completed",
                time_range_start=datetime.now() - timedelta(days=1),
                time_range_end=datetime.now()
            )

            db.add(instance)
            db.commit()
            db.refresh(instance)

            logger.info(f"舆情简报生成完成，实例ID: {instance.id}")

            return {
                'success': True,
                'report_instance_id': instance.id,
                'message': '舆情简报生成成功'
            }
        

        
    except Exception as e:
        logger.error(f"生成舆情简报失败: {str(e)}")
        current_task.update_state(
            state='FAILURE',
            meta={'error': str(e)}
        )
        return {
            'success': False,
            'error': str(e)
        }


@celery_app.task(bind=True, name="opinion_analysis.execute_search")
def execute_opinion_search(self, search_params: Dict[str, Any], user_id: int = None) -> Dict[str, Any]:
    """
    执行舆情检索任务
    
    Args:
        search_params: 检索参数
        user_id: 用户ID
        
    Returns:
        Dict: 检索结果
    """
    try:
        logger.info(f"开始执行舆情检索，用户ID: {user_id}")
        
        current_task.update_state(
            state='PROGRESS',
            meta={'current': 10, 'total': 100, 'status': '初始化检索任务...'}
        )
        
        service = OpinionAnalysisService()
        
        # 执行检索
        current_task.update_state(
            state='PROGRESS',
            meta={'current': 50, 'total': 100, 'status': '执行舆情检索...'}
        )
        
        search_results = service.search_opinions(search_params)
        
        # 保存检索结果
        current_task.update_state(
            state='PROGRESS',
            meta={'current': 80, 'total': 100, 'status': '保存检索结果...'}
        )
        
        saved_results = service.save_search_results(search_results, user_id)
        
        current_task.update_state(
            state='SUCCESS',
            meta={'current': 100, 'total': 100, 'status': '检索完成'}
        )
        
        logger.info(f"舆情检索完成，结果数量: {len(search_results)}")
        
        return {
            'success': True,
            'results': saved_results,
            'count': len(search_results),
            'message': '舆情检索完成'
        }
        
    except Exception as e:
        logger.error(f"舆情检索失败: {str(e)}")
        current_task.update_state(
            state='FAILURE',
            meta={'error': str(e)}
        )
        return {
            'success': False,
            'error': str(e)
        }


@celery_app.task(bind=True, name="opinion_analysis.check_alerts")
def check_opinion_alerts(self) -> Dict[str, Any]:
    """
    检查舆情预警任务
    
    Returns:
        Dict: 检查结果
    """
    try:
        logger.info("开始检查舆情预警")
        
        service = OpinionAnalysisService()
        
        # 获取所有活跃的预警配置
        active_alerts = service.get_active_alerts()
        
        triggered_alerts = []
        
        for alert in active_alerts:
            # 检查每个预警条件
            if service.check_alert_condition(alert):
                # 触发预警
                alert_record = service.trigger_alert(alert['id'])
                triggered_alerts.append(alert_record)
                
                # 发送预警通知
                service.send_alert_notification(alert_record)
        
        logger.info(f"预警检查完成，触发预警数量: {len(triggered_alerts)}")
        
        return {
            'success': True,
            'triggered_count': len(triggered_alerts),
            'triggered_alerts': triggered_alerts,
            'message': '预警检查完成'
        }
        
    except Exception as e:
        logger.error(f"预警检查失败: {str(e)}")
        return {
            'success': False,
            'error': str(e)
        }


@celery_app.task(bind=True, name="opinion_analysis.build_knowledge_graph")
def build_knowledge_graph(self, graph_id: int, user_id: int = None) -> Dict[str, Any]:
    """
    构建知识图谱任务
    
    Args:
        graph_id: 知识图谱ID
        user_id: 用户ID
        
    Returns:
        Dict: 构建结果
    """
    try:
        logger.info(f"开始构建知识图谱，ID: {graph_id}")
        
        current_task.update_state(
            state='PROGRESS',
            meta={'current': 10, 'total': 100, 'status': '初始化知识图谱构建...'}
        )
        
        service = OpinionAnalysisService()
        
        # 获取图谱配置
        graph_config = service.get_knowledge_graph_by_id(graph_id)
        if not graph_config:
            raise Exception(f"知识图谱不存在: {graph_id}")
        
        # 数据采集
        current_task.update_state(
            state='PROGRESS',
            meta={'current': 30, 'total': 100, 'status': '采集相关数据...'}
        )
        
        graph_data = service.collect_graph_data(graph_config)
        
        # 实体识别和关系抽取
        current_task.update_state(
            state='PROGRESS',
            meta={'current': 60, 'total': 100, 'status': '实体识别和关系抽取...'}
        )
        
        entities_relations = service.extract_entities_relations(graph_data)
        
        # 构建图谱
        current_task.update_state(
            state='PROGRESS',
            meta={'current': 90, 'total': 100, 'status': '构建知识图谱...'}
        )
        
        graph_result = service.build_graph_structure(entities_relations)
        
        # 保存图谱结果
        service.save_knowledge_graph_result(graph_id, graph_result)
        
        current_task.update_state(
            state='SUCCESS',
            meta={'current': 100, 'total': 100, 'status': '知识图谱构建完成'}
        )
        
        logger.info(f"知识图谱构建完成，ID: {graph_id}")
        
        return {
            'success': True,
            'graph_id': graph_id,
            'entities_count': len(entities_relations.get('entities', [])),
            'relations_count': len(entities_relations.get('relations', [])),
            'message': '知识图谱构建完成'
        }
        
    except Exception as e:
        logger.error(f"知识图谱构建失败: {str(e)}")
        current_task.update_state(
            state='FAILURE',
            meta={'error': str(e)}
        )
        return {
            'success': False,
            'error': str(e)
        }
