import os
import logging
import tempfile
import subprocess
import shutil
import uuid
import cv2
import numpy as np
from typing import Optional, Dict, List, Any, Tuple
from PIL import Image

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MediaUtils:
    """媒体工具服务，处理音视频合成、压缩等功能"""
    
    def __init__(self):
        """初始化媒体工具服务"""
        self.temp_dir = tempfile.gettempdir()
        # 检查是否安装了FFmpeg
        self.has_ffmpeg = self._check_ffmpeg()
        if not self.has_ffmpeg:
            logger.warning("未检测到FFmpeg，将使用备用方法进行音视频处理")
    
    def _check_ffmpeg(self) -> bool:
        """检查系统是否安装了FFmpeg"""
        try:
            result = subprocess.run(["ffmpeg", "-version"], 
                                  stdout=subprocess.PIPE, 
                                  stderr=subprocess.PIPE,
                                  timeout=5)
            return result.returncode == 0
        except Exception as e:
            logger.warning(f"检查FFmpeg安装失败: {e}")
            return False
    
    def combine_audio_video(self, 
                           video_path: str, 
                           audio_path: str, 
                           output_path: Optional[str] = None) -> str:
        """
        合并音频和视频
        
        Args:
            video_path: 视频文件路径
            audio_path: 音频文件路径
            output_path: 输出文件路径，如果为None则生成临时文件
            
        Returns:
            output_path: 合成后的视频文件路径
        """
        if not os.path.exists(video_path):
            logger.error(f"视频文件不存在: {video_path}")
            return ""
            
        if not os.path.exists(audio_path):
            logger.error(f"音频文件不存在: {audio_path}")
            return ""
        
        # 生成临时文件名
        if output_path is None:
            output_path = os.path.join(self.temp_dir, f"combined_{uuid.uuid4()}.mp4")
        
        try:
            if self.has_ffmpeg:
                # 使用FFmpeg合并音视频
                return self._combine_with_ffmpeg(video_path, audio_path, output_path)
            else:
                # 使用OpenCV合并音视频
                return self._combine_with_opencv(video_path, audio_path, output_path)
        except Exception as e:
            logger.error(f"合并音视频失败: {e}")
            return ""
    
    def _combine_with_ffmpeg(self, video_path: str, audio_path: str, output_path: str) -> str:
        """使用FFmpeg合并音视频"""
        try:
            cmd = [
                "ffmpeg", 
                "-i", video_path,  # 视频输入
                "-i", audio_path,  # 音频输入
                "-c:v", "copy",    # 复制视频流
                "-c:a", "aac",     # 音频编码为AAC
                "-strict", "experimental",
                "-shortest",       # 使用最短的流长度
                "-y",              # 覆盖输出文件
                output_path
            ]
            
            process = subprocess.run(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                timeout=60
            )
            
            if process.returncode != 0:
                logger.error(f"FFmpeg合并失败: {process.stderr.decode('utf-8', errors='ignore')}")
                return ""
            
            if not os.path.exists(output_path):
                logger.error("FFmpeg执行成功但输出文件不存在")
                return ""
                
            logger.info(f"使用FFmpeg合并音视频成功: {output_path}")
            return output_path
            
        except subprocess.TimeoutExpired:
            logger.error("FFmpeg处理超时")
            return ""
        except Exception as e:
            logger.error(f"使用FFmpeg合并音视频时出错: {e}")
            return ""
    
    def _combine_with_opencv(self, video_path: str, audio_path: str, output_path: str) -> str:
        """使用OpenCV处理视频并复制音频（简化实现）"""
        try:
            # 由于OpenCV无法直接合并音频，这里只是将视频复制到输出路径
            # 实际项目中应使用FFmpeg或其他库进行完整的音视频合并
            shutil.copy(video_path, output_path)
            logger.warning("使用OpenCV方法无法合并音频，仅复制了视频文件")
            
            return output_path
        except Exception as e:
            logger.error(f"使用OpenCV合并音视频时出错: {e}")
            return ""
    
    def compress_video(self, 
                      video_path: str, 
                      output_path: Optional[str] = None,
                      quality: str = "medium") -> str:
        """
        压缩视频
        
        Args:
            video_path: 视频文件路径
            output_path: 输出文件路径，如果为None则生成临时文件
            quality: 压缩质量，可选值: low, medium, high
            
        Returns:
            output_path: 压缩后的视频文件路径
        """
        if not os.path.exists(video_path):
            logger.error(f"视频文件不存在: {video_path}")
            return ""
        
        # 生成临时文件名
        if output_path is None:
            output_path = os.path.join(self.temp_dir, f"compressed_{uuid.uuid4()}.mp4")
        
        try:
            if self.has_ffmpeg:
                # 使用FFmpeg压缩视频
                return self._compress_with_ffmpeg(video_path, output_path, quality)
            else:
                # 使用OpenCV压缩视频
                return self._compress_with_opencv(video_path, output_path, quality)
        except Exception as e:
            logger.error(f"压缩视频失败: {e}")
            return ""
    
    def _compress_with_ffmpeg(self, video_path: str, output_path: str, quality: str) -> str:
        """使用FFmpeg压缩视频"""
        try:
            # 根据质量设置参数
            if quality == "low":
                crf = "28"
            elif quality == "high":
                crf = "18"
            else:  # medium
                crf = "23"
            
            cmd = [
                "ffmpeg", 
                "-i", video_path,       # 视频输入
                "-c:v", "libx264",      # 视频编码器
                "-crf", crf,            # 压缩质量
                "-preset", "medium",    # 编码速度
                "-c:a", "aac",          # 音频编码器
                "-b:a", "128k",         # 音频比特率
                "-y",                   # 覆盖输出文件
                output_path
            ]
            
            process = subprocess.run(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                timeout=120
            )
            
            if process.returncode != 0:
                logger.error(f"FFmpeg压缩失败: {process.stderr.decode('utf-8', errors='ignore')}")
                return ""
            
            if not os.path.exists(output_path):
                logger.error("FFmpeg执行成功但输出文件不存在")
                return ""
                
            logger.info(f"使用FFmpeg压缩视频成功: {output_path}")
            return output_path
            
        except subprocess.TimeoutExpired:
            logger.error("FFmpeg处理超时")
            return ""
        except Exception as e:
            logger.error(f"使用FFmpeg压缩视频时出错: {e}")
            return ""
    
    def _compress_with_opencv(self, video_path: str, output_path: str, quality: str) -> str:
        """使用OpenCV压缩视频"""
        try:
            # 打开输入视频
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                logger.error(f"无法打开视频文件: {video_path}")
                return ""
            
            # 获取视频信息
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            fps = cap.get(cv2.CAP_PROP_FPS)
            
            # 根据质量设置参数
            if quality == "low":
                resize_factor = 0.5
                fourcc = cv2.VideoWriter_fourcc(*'XVID')
            elif quality == "high":
                resize_factor = 1.0
                fourcc = cv2.VideoWriter_fourcc(*'H264') if cv2.videoio_registry.hasWriter('H264') else cv2.VideoWriter_fourcc(*'XVID')
            else:  # medium
                resize_factor = 0.75
                fourcc = cv2.VideoWriter_fourcc(*'XVID')
            
            # 计算新的分辨率
            new_width = int(width * resize_factor)
            new_height = int(height * resize_factor)
            
            # 创建视频写入器
            out = cv2.VideoWriter(output_path, fourcc, fps, (new_width, new_height))
            
            # 处理每一帧
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                # 调整帧大小
                resized_frame = cv2.resize(frame, (new_width, new_height))
                
                # 写入帧
                out.write(resized_frame)
            
            # 释放资源
            cap.release()
            out.release()
            
            if not os.path.exists(output_path):
                logger.error("OpenCV处理成功但输出文件不存在")
                return ""
                
            logger.info(f"使用OpenCV压缩视频成功: {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"使用OpenCV压缩视频时出错: {e}")
            return ""
    
    def create_thumbnail(self, 
                       video_path: str, 
                       output_path: Optional[str] = None,
                       time_offset: float = 1.0,
                       width: int = 320) -> str:
        """
        从视频创建缩略图
        
        Args:
            video_path: 视频文件路径
            output_path: 输出文件路径，如果为None则生成临时文件
            time_offset: 从视频开始的时间偏移(秒)
            width: 缩略图宽度
            
        Returns:
            output_path: 缩略图文件路径
        """
        if not os.path.exists(video_path):
            logger.error(f"视频文件不存在: {video_path}")
            return ""
        
        # 生成临时文件名
        if output_path is None:
            output_path = os.path.join(self.temp_dir, f"thumbnail_{uuid.uuid4()}.jpg")
        
        try:
            if self.has_ffmpeg:
                # 使用FFmpeg创建缩略图
                return self._thumbnail_with_ffmpeg(video_path, output_path, time_offset, width)
            else:
                # 使用OpenCV创建缩略图
                return self._thumbnail_with_opencv(video_path, output_path, time_offset, width)
        except Exception as e:
            logger.error(f"创建缩略图失败: {e}")
            return ""
    
    def _thumbnail_with_ffmpeg(self, video_path: str, output_path: str, time_offset: float, width: int) -> str:
        """使用FFmpeg创建缩略图"""
        try:
            cmd = [
                "ffmpeg", 
                "-i", video_path,        # 视频输入
                "-ss", str(time_offset), # 时间偏移
                "-vframes", "1",         # 只提取一帧
                "-vf", f"scale={width}:-1", # 缩放
                "-y",                    # 覆盖输出文件
                output_path
            ]
            
            process = subprocess.run(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                timeout=30
            )
            
            if process.returncode != 0:
                logger.error(f"FFmpeg创建缩略图失败: {process.stderr.decode('utf-8', errors='ignore')}")
                return ""
            
            if not os.path.exists(output_path):
                logger.error("FFmpeg执行成功但输出文件不存在")
                return ""
                
            logger.info(f"使用FFmpeg创建缩略图成功: {output_path}")
            return output_path
            
        except subprocess.TimeoutExpired:
            logger.error("FFmpeg处理超时")
            return ""
        except Exception as e:
            logger.error(f"使用FFmpeg创建缩略图时出错: {e}")
            return ""
    
    def _thumbnail_with_opencv(self, video_path: str, output_path: str, time_offset: float, width: int) -> str:
        """使用OpenCV创建缩略图"""
        try:
            # 打开输入视频
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                logger.error(f"无法打开视频文件: {video_path}")
                return ""
            
            # 计算帧位置
            fps = cap.get(cv2.CAP_PROP_FPS)
            frame_pos = int(fps * time_offset)
            
            # 设置帧位置
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_pos)
            
            # 读取帧
            ret, frame = cap.read()
            if not ret:
                logger.error("无法读取视频帧")
                return ""
            
            # 计算新的高度，保持宽高比
            height = frame.shape[0]
            width_orig = frame.shape[1]
            height_new = int(height * (width / width_orig))
            
            # 调整帧大小
            resized_frame = cv2.resize(frame, (width, height_new))
            
            # 保存为图片
            cv2.imwrite(output_path, resized_frame)
            
            # 释放资源
            cap.release()
            
            if not os.path.exists(output_path):
                logger.error("OpenCV处理成功但输出文件不存在")
                return ""
                
            logger.info(f"使用OpenCV创建缩略图成功: {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"使用OpenCV创建缩略图时出错: {e}")
            return ""
    
    async def analyze_media_quality(self, file_path: str) -> Tuple[List[Dict[str, Any]], int]:
        """
        分析媒体质量并返回质量检查结果和总体评分
        
        Args:
            file_path: 媒体文件路径
            
        Returns:
            Tuple[List[Dict[str, Any]], int]: 质量检查结果列表和总体评分
        """
        # 获取文件类型
        file_extension = os.path.splitext(file_path)[1].lower()
        is_image = file_extension in ['.jpg', '.jpeg', '.png']
        is_video = file_extension in ['.mp4', '.mov', '.avi']
        
        quality_checks = []
        
        try:
            # 图像质量分析
            if is_image:
                # 打开图像
                img = cv2.imread(file_path)
                if img is None:
                    return [
                        {"name": "文件完整性", "status": "fail", "message": "无法读取图像文件"}
                    ], 0
                
                # 检查分辨率
                height, width = img.shape[:2]
                if width >= 1920 and height >= 1080:
                    quality_checks.append({
                        "name": "分辨率", 
                        "status": "pass", 
                        "message": f"分辨率良好 ({width}x{height})"
                    })
                elif width >= 1280 and height >= 720:
                    quality_checks.append({
                        "name": "分辨率", 
                        "status": "pass", 
                        "message": f"分辨率良好 ({width}x{height})"
                    })
                elif width >= 800 and height >= 600:
                    quality_checks.append({
                        "name": "分辨率", 
                        "status": "warning", 
                        "message": f"分辨率较低 ({width}x{height})"
                    })
                else:
                    quality_checks.append({
                        "name": "分辨率", 
                        "status": "fail", 
                        "message": f"分辨率过低 ({width}x{height})"
                    })
                
                # 检查亮度
                gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
                brightness = np.mean(gray)
                if 80 <= brightness <= 200:
                    quality_checks.append({
                        "name": "光线条件",
                        "status": "pass",
                        "message": "光线均匀适中"
                    })
                elif 60 <= brightness < 80 or 200 < brightness <= 220:
                    quality_checks.append({
                        "name": "光线条件",
                        "status": "warning",
                        "message": "光线略暗/略亮，可能影响效果"
                    })
                else:
                    quality_checks.append({
                        "name": "光线条件",
                        "status": "fail",
                        "message": "光线过暗/过亮，建议重新拍摄"
                    })
                
                # 检查面部清晰度（使用面部检测）
                try:
                    # 尝试加载人脸级联分类器
                    cascade_path = cv2.data.haarcascades + 'haarcascade_frontalface_default.xml'
                    if os.path.exists(cascade_path):
                        face_cascade = cv2.CascadeClassifier(cascade_path)
                        faces = face_cascade.detectMultiScale(gray, 1.1, 4)
                        
                        if len(faces) > 0:
                            quality_checks.append({
                                "name": "面部识别",
                                "status": "pass",
                                "message": f"成功检测到{len(faces)}个面部"
                            })
                            
                            # 检查面部大小
                            face_ratio = 0
                            for (x, y, w, h) in faces:
                                face_area = w * h
                                image_area = width * height
                                face_ratio = max(face_ratio, face_area / image_area)
                            
                            if face_ratio > 0.15:
                                quality_checks.append({
                                    "name": "面部大小",
                                    "status": "pass",
                                    "message": "面部比例适中"
                                })
                            elif face_ratio > 0.05:
                                quality_checks.append({
                                    "name": "面部大小",
                                    "status": "warning",
                                    "message": "面部比例略小，可能影响细节"
                                })
                            else:
                                quality_checks.append({
                                    "name": "面部大小",
                                    "status": "fail",
                                    "message": "面部比例过小，建议重新拍摄"
                                })
                            
                            # 获取最大面部区域进行模糊度检查
                            max_face = max(faces, key=lambda f: f[2] * f[3])
                            x, y, w, h = max_face
                            face_roi = gray[y:y+h, x:x+w]
                            
                            # 计算拉普拉斯方差（评估清晰度）
                            if face_roi.size > 0:
                                laplacian_var = cv2.Laplacian(face_roi, cv2.CV_64F).var()
                                
                                if laplacian_var > 100:
                                    quality_checks.append({
                                        "name": "面部清晰度",
                                        "status": "pass",
                                        "message": "面部特征清晰可辨"
                                    })
                                elif laplacian_var > 50:
                                    quality_checks.append({
                                        "name": "面部清晰度",
                                        "status": "warning",
                                        "message": "面部细节略模糊"
                                    })
                                else:
                                    quality_checks.append({
                                        "name": "面部清晰度",
                                        "status": "fail",
                                        "message": "面部细节模糊，建议重新拍摄"
                                    })
                        else:
                            quality_checks.append({
                                "name": "面部识别",
                                "status": "warning",
                                "message": "未检测到面部，可能影响生成效果"
                            })
                    else:
                        # 如果无法加载级联分类器，添加一个警告
                        quality_checks.append({
                            "name": "面部分析",
                            "status": "warning",
                            "message": "无法进行面部分析"
                        })
                except Exception as face_error:
                    logger.error(f"面部分析过程出错: {face_error}")
                    quality_checks.append({
                        "name": "面部分析",
                        "status": "warning",
                        "message": "分析过程中出错，无法确定面部质量"
                    })
                
                # 计算整体质量评分
                score_mapping = {
                    "pass": 100,
                    "warning": 50,
                    "fail": 0
                }
                
                if quality_checks:
                    total_score = sum(score_mapping[check["status"]] for check in quality_checks)
                    quality_score = round(total_score / len(quality_checks))
                else:
                    quality_score = 0
                
                return quality_checks, quality_score
                
            # 视频质量分析
            elif is_video:
                # 打开视频
                cap = cv2.VideoCapture(file_path)
                if not cap.isOpened():
                    return [
                        {"name": "文件完整性", "status": "fail", "message": "无法读取视频文件"}
                    ], 0
                
                # 获取视频基本信息
                width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                fps = cap.get(cv2.CAP_PROP_FPS)
                frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                duration = frame_count / fps if fps > 0 else 0
                
                # 检查分辨率
                if width >= 1920 and height >= 1080:
                    quality_checks.append({
                        "name": "分辨率", 
                        "status": "pass", 
                        "message": f"分辨率良好 ({width}x{height})"
                    })
                elif width >= 1280 and height >= 720:
                    quality_checks.append({
                        "name": "分辨率", 
                        "status": "pass", 
                        "message": f"分辨率良好 ({width}x{height})"
                    })
                elif width >= 800 and height >= 600:
                    quality_checks.append({
                        "name": "分辨率", 
                        "status": "warning", 
                        "message": f"分辨率较低 ({width}x{height})"
                    })
                else:
                    quality_checks.append({
                        "name": "分辨率", 
                        "status": "fail", 
                        "message": f"分辨率过低 ({width}x{height})"
                    })
                
                # 检查帧率
                if fps >= 25:
                    quality_checks.append({
                        "name": "帧率",
                        "status": "pass",
                        "message": f"帧率良好 ({fps:.1f} fps)"
                    })
                elif fps >= 15:
                    quality_checks.append({
                        "name": "帧率",
                        "status": "warning",
                        "message": f"帧率较低 ({fps:.1f} fps)"
                    })
                else:
                    quality_checks.append({
                        "name": "帧率",
                        "status": "fail",
                        "message": f"帧率过低 ({fps:.1f} fps)"
                    })
                
                # 检查时长
                if 10 <= duration <= 60:
                    quality_checks.append({
                        "name": "视频时长",
                        "status": "pass",
                        "message": f"时长适中 ({duration:.1f} 秒)"
                    })
                elif 5 <= duration < 10 or 60 < duration <= 120:
                    quality_checks.append({
                        "name": "视频时长",
                        "status": "warning",
                        "message": f"时长略{'短' if duration < 10 else '长'} ({duration:.1f} 秒)"
                    })
                else:
                    quality_checks.append({
                        "name": "视频时长",
                        "status": "fail",
                        "message": f"时长{'过短' if duration < 5 else '过长'} ({duration:.1f} 秒)"
                    })
                
                # 简单抽取几帧进行质量评估
                frame_samples = min(5, frame_count)
                step = max(1, frame_count // frame_samples)
                
                brightness_values = []
                clarity_values = []
                
                for i in range(0, frame_count, step):
                    if len(brightness_values) >= frame_samples:
                        break
                    
                    cap.set(cv2.CAP_PROP_POS_FRAMES, i)
                    ret, frame = cap.read()
                    if not ret:
                        continue
                    
                    # 检查亮度
                    gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
                    brightness_values.append(np.mean(gray))
                    
                    # 检查清晰度
                    laplacian_var = cv2.Laplacian(gray, cv2.CV_64F).var()
                    clarity_values.append(laplacian_var)
                
                # 释放视频资源
                cap.release()
                
                # 分析亮度
                if brightness_values:
                    avg_brightness = sum(brightness_values) / len(brightness_values)
                    if 80 <= avg_brightness <= 200:
                        quality_checks.append({
                            "name": "光线条件",
                            "status": "pass",
                            "message": "光线均匀适中"
                        })
                    elif 60 <= avg_brightness < 80 or 200 < avg_brightness <= 220:
                        quality_checks.append({
                            "name": "光线条件",
                            "status": "warning",
                            "message": "光线略暗/略亮，可能影响效果"
                        })
                    else:
                        quality_checks.append({
                            "name": "光线条件",
                            "status": "fail",
                            "message": "光线过暗/过亮，建议重新拍摄"
                        })
                
                # 分析清晰度
                if clarity_values:
                    avg_clarity = sum(clarity_values) / len(clarity_values)
                    if avg_clarity > 100:
                        quality_checks.append({
                            "name": "视频清晰度",
                            "status": "pass",
                            "message": "视频清晰度良好"
                        })
                    elif avg_clarity > 50:
                        quality_checks.append({
                            "name": "视频清晰度",
                            "status": "warning",
                            "message": "视频清晰度一般"
                        })
                    else:
                        quality_checks.append({
                            "name": "视频清晰度",
                            "status": "fail",
                            "message": "视频清晰度较低，建议重新拍摄"
                        })
                
                # 计算整体质量评分
                score_mapping = {
                    "pass": 100,
                    "warning": 50,
                    "fail": 0
                }
                
                if quality_checks:
                    total_score = sum(score_mapping[check["status"]] for check in quality_checks)
                    quality_score = round(total_score / len(quality_checks))
                else:
                    quality_score = 0
                
                return quality_checks, quality_score
            
            else:
                return [
                    {"name": "文件类型", "status": "fail", "message": "不支持的文件类型"}
                ], 0
                
        except Exception as e:
            logger.error(f"分析媒体质量时出错: {e}")
            return [
                {"name": "处理错误", "status": "fail", "message": f"分析过程中出错: {str(e)}"}
            ], 0
            
    async def create_thumbnail_from_image(self, source_path: str, target_path: str, size: Tuple[int, int] = (200, 200)) -> bool:
        """
        创建图片缩略图
        
        Args:
            source_path: 源图片路径
            target_path: 目标缩略图路径
            size: 缩略图尺寸 (宽, 高)
            
        Returns:
            bool: 成功返回True，失败返回False
        """
        try:
            with Image.open(source_path) as img:
                img.thumbnail(size)
                img.save(target_path)
            return True
        except Exception as e:
            logger.error(f"创建缩略图失败: {e}")
            return False

# 全局单例实例
_media_utils_instance = None

def get_media_utils() -> MediaUtils:
    """获取媒体工具实例（单例模式）"""
    global _media_utils_instance
    if _media_utils_instance is None:
        _media_utils_instance = MediaUtils()
    return _media_utils_instance 