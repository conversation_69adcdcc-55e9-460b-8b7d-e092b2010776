"""
聊天会话模型

用于存储用户与数字人的会话记录
"""

from sqlalchemy import Column, String, Integer, DateTime, ForeignKey, Text, JSON, Float, Boolean
from sqlalchemy.orm import relationship
from datetime import datetime
import uuid

from utils.db import Base

class ChatSession(Base):
    """聊天会话模型"""
    
    __tablename__ = "chat_sessions"
    __table_args__ = {'extend_existing': True}  # 允许表被多次定义，解决SQLAlchemy注册冲突
    
    id = Column(Integer, primary_key=True, index=True)
    session_id = Column(String(64), unique=True, index=True, nullable=False)
    digital_human_id = Column(String(64), ForeignKey("digital_humans.digital_human_id"), nullable=False)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    start_time = Column(DateTime, default=datetime.now)
    end_time = Column(DateTime, nullable=True)
    duration = Column(Float, nullable=True)  # 会话持续时间（秒）
    messages_count = Column(Integer, default=0)  # 消息数量
    summary = Column(Text, nullable=True)  # 会话摘要
    tags = Column(JSON, nullable=True)  # 会话标签
    sentiment_score = Column(Float, nullable=True)  # 整体情感得分
    feedback_rating = Column(Integer, nullable=True)  # 用户反馈评分
    feedback_comment = Column(Text, nullable=True)  # 用户反馈评论
    is_active = Column(Boolean, default=True)  # 会话是否活跃
    session_metadata = Column(JSON, nullable=True)  # 额外元数据，改名避免与SQLAlchemy保留字冲突
    
    # 关系
    digital_human = relationship("DigitalHuman", back_populates="chat_sessions")
    user = relationship("User", back_populates="chat_sessions")
    messages = relationship("ChatMessage", back_populates="session", cascade="all, delete-orphan")
    
    def __init__(self, **kwargs):
        """初始化聊天会话"""
        super().__init__(**kwargs)
        
        # 确保session_id有值
        if not self.session_id:
            self.session_id = str(uuid.uuid4())
            
        # 初始化标签和元数据为空列表和字典
        if self.tags is None:
            self.tags = []
            
        if self.session_metadata is None:
            self.session_metadata = {}
            
    def update_duration(self):
        """更新会话持续时间"""
        if self.end_time and self.start_time:
            self.duration = (self.end_time - self.start_time).total_seconds()
            
    def update_messages_count(self, db_session):
        """更新消息数量"""
        from models.chat_message import ChatMessage
        self.messages_count = db_session.query(ChatMessage).filter(
            ChatMessage.session_id == self.session_id
        ).count()
        
    def close(self):
        """关闭会话"""
        self.end_time = datetime.now()
        self.is_active = False
        self.update_duration()
        
    def reopen(self):
        """重新打开会话"""
        self.is_active = True
        self.end_time = None
        
    def add_tag(self, tag):
        """添加标签"""
        if self.tags is None:
            self.tags = []
        if tag not in self.tags:
            self.tags.append(tag)
            
    def remove_tag(self, tag):
        """移除标签"""
        if self.tags and tag in self.tags:
            self.tags.remove(tag)
            
    def add_metadata(self, key, value):
        """添加元数据"""
        if self.session_metadata is None:
            self.session_metadata = {}
        self.session_metadata[key] = value
        
    def set_feedback(self, rating, comment=None):
        """设置用户反馈"""
        self.feedback_rating = rating
        if comment:
            self.feedback_comment = comment 