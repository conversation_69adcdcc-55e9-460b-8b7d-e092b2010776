"""
Wanx 2.1 视频生成 Celery 任务
"""
import os
import sys
import uuid
import subprocess
import json
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional

from celery import current_task
from app.core.celery_unified import celery_app
from app.core.task_manager import task_manager
from app.core.database import db_manager
from app.models.unified_tasks import TaskTypes, TaskStatus

# 简单的任务基类
class SimpleTask:
    def start_task(self, task_id, celery_task_id):
        """开始任务"""
        task_manager.update_task(task_id,
                               status=TaskStatus.RUNNING,
                               celery_task_id=celery_task_id)

    def update_progress(self, task_id, progress, message=""):
        """更新进度"""
        task_manager.update_task(task_id,
                               progress=progress,
                               message=message)

    def complete_task(self, task_id, result):
        """完成任务"""
        task_manager.update_task(task_id,
                               status=TaskStatus.COMPLETED,
                               progress=100,
                               result=result)

    def fail_task(self, task_id, error):
        """任务失败"""
        task_manager.update_task(task_id,
                               status=TaskStatus.FAILED,
                               error_message=str(error))

# 获取数据库管理器的辅助函数
def get_db_manager():
    """获取数据库管理器"""
    return db_manager

# Wanx 2.1 模型路径
WAN_MODEL_PATH = Path(__file__).parent.parent.parent / "storage" / "models" / "video_generation" / "wan" / "Wan2.1"
WAN_OUTPUT_DIR = Path(__file__).parent.parent.parent / "storage" / "models" / "video_generation" / "wan" / "outputs"
WAN_TEMP_DIR = Path(__file__).parent.parent.parent / "storage" / "models" / "video_generation" / "wan" / "temp"

# 确保目录存在
WAN_OUTPUT_DIR.mkdir(parents=True, exist_ok=True)
WAN_TEMP_DIR.mkdir(parents=True, exist_ok=True)

# 旧的保存函数已被统一任务管理器替代

@celery_app.task(bind=True, name="wanx_text_to_video")
def wanx_text_to_video(self, task_id: str, prompt: str, model: str = "t2v-1.3B", 
                      duration: int = 5, resolution: str = "768x512", 
                      fps: int = 24, guidance_scale: float = 7.5, 
                      num_inference_steps: int = 50, user_id: str = "demo-user"):
    """Wanx 2.1 文本转视频任务"""
    
    try:
        # 标记任务开始
        task_manager.update_task(task_id,
                               status=TaskStatus.RUNNING,
                               celery_task_id=self.request.id)

        # 更新进度：开始处理
        self.update_task_progress(task_id, 10, "正在初始化 Wanx 2.1 模型...")
        
        # 检查模型路径
        if not WAN_MODEL_PATH.exists():
            raise Exception(f"Wanx 2.1 模型不存在: {WAN_MODEL_PATH}")
        
        # 生成输出文件名
        output_filename = f"wanx_{task_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.mp4"
        output_path = WAN_OUTPUT_DIR / output_filename
        
        # 构建生成命令
        generate_script = WAN_MODEL_PATH / "generate.py"
        if not generate_script.exists():
            # 如果没有 generate.py，尝试使用其他脚本
            possible_scripts = ["run_inference.py", "inference.py", "main.py"]
            for script_name in possible_scripts:
                script_path = WAN_MODEL_PATH / script_name
                if script_path.exists():
                    generate_script = script_path
                    break
            else:
                raise Exception("未找到 Wanx 2.1 生成脚本")
        
        # 映射模型参数到 Wanx generate.py 期望的格式
        wanx_model_type = "local"  # Wanx generate.py 只支持 local 或 remote

        command = [
            sys.executable, str(generate_script),
            "--model", wanx_model_type,
            "--text", prompt,  # Wanx 使用 --text 而不是 --prompt
            "--width", resolution.split('x')[0],
            "--height", resolution.split('x')[1],
            "--length", str(duration),
            "--fps", str(fps),
            "--quality", str(int(guidance_scale * 5)),  # 映射 guidance_scale 到 quality (1-51)
            "--steps", str(num_inference_steps),
            "--output", str(output_path)
        ]
        
        self.update_task_progress(task_id, 20, "正在生成视频...")
        
        # 执行生成命令
        process = subprocess.Popen(
            command,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            cwd=str(WAN_MODEL_PATH),
            text=True
        )
        
        # 监控进度
        progress = 20
        while process.poll() is None:
            progress = min(progress + 15, 90)
            self.update_task_progress(task_id, progress, "正在生成视频...")
            
            # 等待一段时间
            try:
                process.wait(timeout=60)  # 每分钟检查一次
            except subprocess.TimeoutExpired:
                continue
        
        stdout, stderr = process.communicate()
        
        if process.returncode == 0:
            # 检查输出文件
            if output_path.exists():
                # 生成成功
                video_url = f"/api/v1/video/download/{output_filename}"
                
                # 标记任务完成
                self.complete_task(
                    task_id,
                    output_data={"video_url": video_url, "message": "视频生成完成"},
                    output_files=[str(output_path)]
                )
                
                return {
                    "status": "completed",
                    "video_url": video_url,
                    "message": "视频生成完成",
                    "output_path": str(output_path)
                }
            else:
                raise Exception("生成的视频文件不存在")
        else:
            raise Exception(f"生成失败: {stderr}")
            
    except Exception as e:
        error_msg = str(e)
        print(f"Wanx 文本转视频任务失败: {error_msg}")
        
        # 标记任务失败
        self.fail_task(task_id, error_msg)
        
        return {
            "status": "failed",
            "error": error_msg
        }

@celery_app.task(bind=True, name="wanx_image_to_video")
def wanx_image_to_video(self, task_id: str, image_path: str, prompt: str, 
                       model: str = "i2v-14B", duration: int = 5, 
                       resolution: str = "768x512", fps: int = 24, 
                       motion_strength: float = 0.5, user_id: str = "demo-user"):
    """Wanx 2.1 图片转视频任务"""
    
    try:
        # 标记任务开始
        self.start_task(task_id, self.request.id)

        # 更新进度：开始处理
        self.update_task_progress(task_id, 10, "正在初始化 Wanx 2.1 模型...")
        
        # 检查模型和图片路径
        if not WAN_MODEL_PATH.exists():
            raise Exception(f"Wanx 2.1 模型不存在: {WAN_MODEL_PATH}")
        
        if not os.path.exists(image_path):
            raise Exception(f"输入图片不存在: {image_path}")
        
        # 生成输出文件名
        output_filename = f"wanx_i2v_{task_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.mp4"
        output_path = WAN_OUTPUT_DIR / output_filename
        
        # 构建生成命令
        generate_script = WAN_MODEL_PATH / "generate.py"
        if not generate_script.exists():
            # 如果没有 generate.py，尝试使用其他脚本
            possible_scripts = ["run_inference.py", "inference.py", "main.py"]
            for script_name in possible_scripts:
                script_path = WAN_MODEL_PATH / script_name
                if script_path.exists():
                    generate_script = script_path
                    break
            else:
                raise Exception("未找到 Wanx 2.1 生成脚本")
        
        command = [
            sys.executable, str(generate_script),
            "--model", model,
            "--prompt", prompt,
            "--image", image_path,
            "--size", resolution,
            "--duration", str(duration),
            "--fps", str(fps),
            "--motion_strength", str(motion_strength),
            "--output", str(output_path)
        ]
        
        self.update_task_progress(task_id, 20, "正在生成视频...")
        
        # 执行生成命令
        process = subprocess.Popen(
            command,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            cwd=str(WAN_MODEL_PATH),
            text=True
        )
        
        # 监控进度
        progress = 20
        while process.poll() is None:
            progress = min(progress + 15, 90)
            self.update_task_progress(task_id, progress, "正在生成视频...")
            
            # 等待一段时间
            try:
                process.wait(timeout=60)  # 每分钟检查一次
            except subprocess.TimeoutExpired:
                continue
        
        stdout, stderr = process.communicate()
        
        if process.returncode == 0:
            # 检查输出文件
            if output_path.exists():
                # 生成成功
                video_url = f"/api/v1/video/download/{output_filename}"
                
                # 更新数据库
                db_manager = get_db_manager()
                if db_manager.use_postgres:
                    # PostgreSQL 版本
                    db_manager.execute_query(
                        """
                        UPDATE wanx_video_tasks
                        SET status = %s, progress = %s, message = %s, video_url = %s, updated_at = %s
                        WHERE task_id = %s
                        """,
                        ["completed", 100, "视频生成完成", video_url, datetime.now(), task_id]
                    )
                else:
                    # SQLite 版本
                    db_manager.execute_query(
                        """
                        UPDATE wanx_video_tasks
                        SET status = ?, progress = ?, message = ?, video_url = ?, updated_at = ?
                        WHERE task_id = ?
                        """,
                        ["completed", 100, "视频生成完成", video_url, datetime.now(), task_id]
                    )
                
                self.update_task_progress(task_id, 100, "视频生成完成")
                
                return {
                    "status": "completed",
                    "video_url": video_url,
                    "message": "视频生成完成",
                    "output_path": str(output_path)
                }
            else:
                raise Exception("生成的视频文件不存在")
        else:
            raise Exception(f"生成失败: {stderr}")
            
    except Exception as e:
        error_msg = str(e)
        print(f"Wanx 图片转视频任务失败: {error_msg}")
        
        # 更新数据库错误状态
        try:
            db_manager = get_db_manager()
            if db_manager.use_postgres:
                # PostgreSQL 版本
                db_manager.execute_query(
                    """
                    UPDATE wanx_video_tasks
                    SET status = %s, error_message = %s, updated_at = %s
                    WHERE task_id = %s
                    """,
                    ["failed", error_msg, datetime.now(), task_id]
                )
            else:
                # SQLite 版本
                db_manager.execute_query(
                    """
                    UPDATE wanx_video_tasks
                    SET status = ?, error_message = ?, updated_at = ?
                    WHERE task_id = ?
                    """,
                    ["failed", error_msg, datetime.now(), task_id]
                )
        except:
            pass
        
        self.update_task_progress(task_id, 0, f"生成失败: {error_msg}")
        
        return {
            "status": "failed",
            "error": error_msg
        }
