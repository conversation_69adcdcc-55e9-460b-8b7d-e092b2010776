"""
任务持久化工具，确保任务数据被正确保存到数据库
"""
import logging
import uuid
from typing import Dict, Any, Optional, List
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import desc
import asyncio
from datetime import datetime
from sqlalchemy import func
from sqlalchemy import text

from models.task import Task, TaskCreate, TaskUpdate, TaskLog
from utils.db import get_db

logger = logging.getLogger(__name__)

class TaskPersistenceManager:
    """任务数据持久化管理器"""
    
    @classmethod
    async def create_task_record(cls, task_id: str, task_type: str, status: str = "pending", user_id: int = None, parameters: dict = None) -> Task:
        """
        创建任务记录
        
        参数:
            task_id: 任务ID
            task_type: 任务类型
            status: 任务状态
            user_id: 用户ID
            parameters: 任务参数
            
        返回:
            创建的任务记录
        """
        from utils.db import get_async_session
        # 确保task_id是字符串类型
        if not isinstance(task_id, str):
            logger.warning(f"任务ID类型错误: {type(task_id)}，尝试转换为字符串")
            try:
                task_id = str(task_id)
            except Exception as e:
                logger.error(f"转换任务ID失败: {str(e)}")
                raise ValueError(f"任务ID类型错误: {type(task_id)}")
        
        try:
            task = Task(
                task_id=task_id,
                task_type=task_type,
                status=status,
                progress=0,
                user_id=user_id,
                input_data=parameters or {},
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            async with get_async_session() as session:
                try:
                    session.add(task)
                    await session.commit()
                    await session.refresh(task)
                    logger.info(f"已创建任务记录: {task_id}, 类型: {task_type}, 状态: {status}")
                    return task
                except Exception as e:
                    await session.rollback()
                    logger.error(f"创建任务记录时数据库操作失败: {str(e)}", exc_info=True)
                    raise
        except Exception as e:
            logger.error(f"创建任务记录失败: {str(e)}", exc_info=True)
            raise
    
    @classmethod
    def update_task_status(cls, task_id: str, status: str, progress: int = None, error: Any = None, result_url: str = None, celery_task_id: str = None) -> None:
        """
        更新任务状态
        
        参数:
            task_id: 任务ID
            status: 任务状态
            progress: 任务进度
            error: 错误信息
            result_url: 结果URL
            celery_task_id: Celery任务ID
        """
        # 确保task_id是字符串类型
        if not isinstance(task_id, str):
            logger.warning(f"任务ID类型错误: {type(task_id)}，尝试转换为字符串")
            try:
                task_id = str(task_id)
            except Exception as e:
                logger.error(f"转换任务ID失败: {str(e)}")
                return
        
        # 如果任务完成或成功，确保清除错误信息
        if status in ["completed", "success"] and (progress is None or progress == 100):
            error = None
        
        # 处理error参数，确保它是字符串类型
        if error is not None and not isinstance(error, str):
            try:
                import json
                error = json.dumps(error)  # 将字典或其他对象转换为JSON字符串
            except Exception as e:
                logger.warning(f"转换错误信息失败，尝试字符串转换: {str(e)}")
                error = str(error)
                
        # 准备任务结果字典
        result = None
        if result_url:
            result = {"result_url": result_url}
            if celery_task_id:
                result["celery_task_id"] = celery_task_id
        
        # 直接使用同步方法更新，避免事件循环相关问题
        cls._update_task_status_sync_fallback(task_id, status, progress, result, error)

    @classmethod
    async def update_task_status_async(cls, task_id: str, status: str, progress: int = None, result: dict = None, error: Any = None) -> bool:
        """
        异步更新任务状态
        
        参数:
            task_id: 任务ID
            status: 任务状态
            progress: 任务进度
            result: 任务结果
            error: 错误信息
            
        返回:
            更新是否成功
        """
        try:
            # 检查当前事件循环状态并立即切换到同步方法
            try:
                loop = asyncio.get_running_loop()
                if loop.is_closed():
                    logger.warning(f"当前事件循环已关闭，直接使用同步方法更新任务状态: {task_id}")
                    return cls._update_task_status_sync_fallback(task_id, status, progress, result, error)
            except RuntimeError:
                logger.warning(f"没有运行中的事件循环，直接使用同步方法更新任务状态: {task_id}")
                return cls._update_task_status_sync_fallback(task_id, status, progress, result, error)
            
            import json
            from utils.db import get_async_session
            
            # 如果任务完成或成功，清除错误信息
            if status in ["completed", "success"] and progress == 100:
                error = None
            
            # 处理error参数，确保它是字符串类型
            if error is not None and not isinstance(error, str):
                try:
                    error = json.dumps(error)  # 将字典或其他对象转换为JSON字符串
                except Exception as e:
                    logger.warning(f"转换错误信息失败，尝试字符串转换: {str(e)}")
                    error = str(error)  # 如果JSON序列化失败，使用str()函数转换
            
            # 尝试使用异步会话更新
            try:
                # 最后再次检查事件循环状态
                if asyncio.get_event_loop().is_closed():
                    logger.warning(f"事件循环在获取会话前已关闭，直接使用同步方法更新任务状态: {task_id}")
                    return cls._update_task_status_sync_fallback(task_id, status, progress, result, error)
                    
                # 使用上下文管理器安全地获取和使用会话
                async with get_async_session() as session:
                    # 查询任务 - 使用task_id而不是id
                    result_query = await session.execute(select(Task).where(Task.task_id == task_id))
                    task = result_query.scalars().first()
                    if not task:
                        logger.warning(f"任务不存在: {task_id}")
                        return False
                    
                    # 更新任务状态
                    task.status = status
                    if progress is not None:
                        task.progress = progress
                    if result is not None:
                        task.result = result
                    
                    # 设置或清除错误信息
                    if status in ["completed", "success"] and progress == 100:
                        task.error = None
                        task.completed_at = datetime.now()
                    elif error is not None:
                        task.error = error
                    
                    task.updated_at = datetime.now()
                    
                    # 提交更改
                    await session.commit()
                    logger.info(f"已更新任务状态: {task_id}, 状态: {status}, 进度: {progress}")
                    return True
            except Exception as e:
                # 任何异常直接尝试同步方法
                logger.warning(f"异步更新任务状态时发生异常: {str(e)}, 使用同步方法")
                return cls._update_task_status_sync_fallback(task_id, status, progress, result, error)
        except Exception as e:
            logger.error(f"更新任务状态失败: {str(e)}", exc_info=True)
            # 最后尝试使用同步方法
            return cls._update_task_status_sync_fallback(task_id, status, progress, result, error)

    @classmethod
    def _update_task_status_sync_fallback(cls, task_id: str, status: str, progress: int = None, result: dict = None, error: Any = None) -> bool:
        """
        同步方式更新任务状态（作为异步方法的备选方案）
        
        参数:
            task_id: 任务ID
            status: 任务状态
            progress: 任务进度
            result: 任务结果
            error: 错误信息
            
        返回:
            更新是否成功
        """
        max_retries = 3
        retry_count = 0
        
        while retry_count < max_retries:
            try:
                from utils.db import get_db
                
                # 获取同步会话
                try:
                    db = next(get_db())
                except Exception as db_error:
                    logger.error(f"[sync] 获取数据库连接失败: {str(db_error)}")
                    retry_count += 1
                    if retry_count < max_retries:
                        import time
                        time.sleep(0.5)
                        continue
                    else:
                        return False
                
                # 检查连接状态
                try:
                    # 不要使用db.bind.closed属性，因为SQLAlchemy的Engine对象没有closed属性
                    # 尝试执行一个简单的查询来检查连接状态
                    db.execute(text("SELECT 1")).scalar()
                    connection_ok = True
                except Exception as e:
                    logger.warning(f"[sync] 数据库连接检查失败: {str(e)}")
                    connection_ok = False
                
                # 如果连接已关闭，尝试获取新连接
                if not connection_ok:
                    logger.warning(f"[sync] 检测到数据库连接可能已关闭，尝试获取新连接")
                    try:
                        from utils.db import get_db
                        db = next(get_db())
                    except Exception as conn_error:
                        logger.error(f"[sync] 获取新数据库连接失败: {str(conn_error)}")
                        retry_count += 1
                        if retry_count < max_retries:
                            import time
                            time.sleep(0.5)
                            continue
                        else:
                            return False
                
                try:
                    # 查询任务
                    from sqlalchemy.orm import Session
                    from sqlalchemy import select as sync_select
                    
                    stmt = sync_select(Task).where(Task.task_id == task_id)
                    task = db.execute(stmt).scalar_one_or_none()
                    
                    if not task:
                        logger.warning(f"[sync] 未找到任务: {task_id}")
                        return False
                    
                    # 更新任务状态
                    task.status = status
                    if progress is not None:
                        task.progress = progress
                    if result is not None:
                        task.result = result
                        
                    # 如果任务完成或成功，清除错误信息并设置完成时间
                    if status in ["completed", "success"] and (progress is None or progress == 100):
                        task.error = None
                        task.completed_at = datetime.now()
                    elif error is not None:
                        # 处理error参数，确保它是字符串类型
                        if not isinstance(error, str):
                            try:
                                import json
                                error = json.dumps(error)  # 将字典或其他对象转换为JSON字符串
                            except Exception as e:
                                logger.warning(f"[sync] 转换错误信息失败，尝试字符串转换: {str(e)}")
                                error = str(error)  # 如果JSON序列化失败，使用str()函数转换
                        task.error = error
                    task.updated_at = datetime.now()
                    
                    # 提交更改
                    try:
                        db.commit()
                        db.refresh(task)
                        logger.info(f"[sync] 已更新任务状态: {task_id}, 状态: {status}, 进度: {progress}")
                        return True
                    except Exception as commit_error:
                        if "connection was closed" in str(commit_error) or "was closed" in str(commit_error):
                            logger.warning(f"[sync] 提交更改时连接已关闭: {str(commit_error)}")
                            try:
                                db.rollback()
                            except:
                                pass
                            retry_count += 1
                            if retry_count < max_retries:
                                import time
                                time.sleep(0.5)
                                continue
                            else:
                                # 作为最后的尝试，直接使用SQL更新
                                return cls._update_with_raw_sql(task_id, status, progress, error)
                        else:
                            logger.error(f"[sync] 提交更改失败: {str(commit_error)}")
                            try:
                                db.rollback()
                            except:
                                pass
                            retry_count += 1
                            if retry_count < max_retries:
                                import time
                                time.sleep(0.5)
                                continue
                            else:
                                # 作为最后的尝试，直接使用SQL更新
                                return cls._update_with_raw_sql(task_id, status, progress, error)
                except Exception as db_error:
                    logger.error(f"[sync] 更新任务状态时数据库操作失败: {str(db_error)}")
                    try:
                        db.rollback()
                    except:
                        pass
                    retry_count += 1
                    if retry_count < max_retries:
                        import time
                        time.sleep(0.5)
                        continue
                    else:
                        # 作为最后的尝试，直接使用SQL更新
                        return cls._update_with_raw_sql(task_id, status, progress, error)
            except Exception as e:
                logger.error(f"[sync] 更新任务状态失败: {str(e)}")
                retry_count += 1
                if retry_count < max_retries:
                    import time
                    time.sleep(0.5)
                    continue
                else:
                    # 作为最后的尝试，直接使用SQL更新
                    return cls._update_with_raw_sql(task_id, status, progress, error)
        
        # 不应该到达这里，但为了安全返回False
        return False

    @classmethod
    def _update_with_raw_sql(cls, task_id: str, status: str, progress: int = None, error: Any = None) -> bool:
        """
        使用原始SQL更新任务状态（最后的备选方案）
        """
        try:
            from utils.db import get_db
            from sqlalchemy import text
            db = next(get_db())
            
            # 转义可能的单引号
            safe_status = status.replace("'", "''") if status else "pending"
            
            # 如果任务完成或成功，清除错误信息并设置完成时间
            if status in ["completed", "success"] and (progress is None or progress == 100):
                error = None
                completed_at_sql = ", completed_at = CURRENT_TIMESTAMP"
            else:
                completed_at_sql = ""
            
            safe_error = str(error).replace("'", "''") if error else None
            
            # 构建SQL语句
            sql = f"UPDATE tasks SET status = '{safe_status}'"
            
            if progress is not None:
                sql += f", progress = {progress}"
            
            if safe_error:
                sql += f", error = '{safe_error}'"
            elif status in ["completed", "success"] and (progress is None or progress == 100):
                sql += ", error = NULL"
            
            sql += f"{completed_at_sql}, updated_at = CURRENT_TIMESTAMP WHERE task_id = '{task_id}'"
            
            # 执行SQL
            db.execute(text(sql))
            db.commit()
            logger.info(f"[sync] 使用原始SQL成功更新任务状态: {task_id}")
            return True
        except Exception as final_error:
            logger.error(f"[sync] 最终SQL更新尝试也失败: {str(final_error)}")
            return False
    
    @classmethod
    async def get_task(cls, task_id: str) -> Optional[Task]:
        """
        获取任务记录
        
        参数:
            task_id: 任务ID
            
        返回:
            任务记录，如果不存在则返回None
        """
        # 确保task_id是字符串类型
        if not isinstance(task_id, str):
            logger.warning(f"任务ID类型错误: {type(task_id)}，尝试转换为字符串")
            try:
                task_id = str(task_id)
            except Exception as e:
                logger.error(f"转换任务ID失败: {str(e)}")
                return None
        
        try:
            # 检查当前事件循环状态
            try:
                loop = asyncio.get_running_loop()
                if loop.is_closed():
                    logger.warning(f"当前事件循环已关闭，尝试使用同步方法获取任务: {task_id}")
                    return cls._get_task_sync_fallback(task_id)
            except RuntimeError:
                logger.warning(f"没有运行中的事件循环，尝试使用同步方法获取任务: {task_id}")
                return cls._get_task_sync_fallback(task_id)
            
            from utils.db import get_async_session
            
            # 检查事件循环状态
            if asyncio.get_event_loop().is_closed():
                logger.warning(f"事件循环在获取会话前已关闭，使用同步方法获取任务: {task_id}")
                return cls._get_task_sync_fallback(task_id)
                
            # 尝试使用异步会话获取任务
            try:
                async with get_async_session() as session:
                    try:
                        # 检查事件循环状态
                        if asyncio.get_event_loop().is_closed():
                            logger.warning(f"事件循环在获取会话后已关闭，使用同步方法获取任务: {task_id}")
                            return cls._get_task_sync_fallback(task_id)
                            
                        # 查询任务 - 确保使用task_id而不是id字段
                        query = select(Task).where(Task.task_id == task_id)
                        result = await session.execute(query)
                        task = result.scalar_one_or_none()
                        
                        if not task:
                            logger.warning(f"未找到任务: {task_id}")
                            return None
                        
                        return task
                    except Exception as e:
                        # 检查是否与事件循环关闭相关
                        if "Event loop is closed" in str(e) or "connection was closed" in str(e):
                            logger.warning(f"事件循环已关闭或连接已关闭，使用同步方法获取任务: {task_id}, 错误: {str(e)}")
                            return cls._get_task_sync_fallback(task_id)
                        
                        logger.error(f"获取任务记录时数据库操作失败: {str(e)}", exc_info=True)
                        return None
            except Exception as session_error:
                # 检查是否与事件循环关闭相关
                if "Event loop is closed" in str(session_error) or "connection was closed" in str(session_error):
                    logger.warning(f"创建异步会话时事件循环已关闭或连接已关闭: {str(session_error)}")
                    return cls._get_task_sync_fallback(task_id)
                
                logger.error(f"创建异步会话失败: {str(session_error)}", exc_info=True)
                return None
        except Exception as e:
            logger.error(f"获取任务记录失败: {str(e)}", exc_info=True)
            return None
            
    @classmethod
    def _get_task_sync_fallback(cls, task_id: str) -> Optional[Task]:
        """
        同步方式获取任务（作为异步方法的备选方案）
        
        参数:
            task_id: 任务ID
            
        返回:
            任务记录，如果不存在则返回None
        """
        try:
            from utils.db import get_db
            
            # 获取同步会话
            db = next(get_db())
            
            try:
                # 查询任务
                from sqlalchemy.orm import Session
                from sqlalchemy import select as sync_select
                
                stmt = sync_select(Task).where(Task.task_id == task_id)
                task = db.execute(stmt).scalar_one_or_none()
                
                if not task:
                    logger.warning(f"[sync] 未找到任务: {task_id}")
                    return None
                
                return task
            except Exception as db_error:
                logger.error(f"[sync] 获取任务记录时数据库操作失败: {str(db_error)}", exc_info=True)
                return None
        except Exception as e:
            logger.error(f"[sync] 获取任务记录失败: {str(e)}", exc_info=True)
            return None
        
    @classmethod
    async def get_user_tasks(
        cls,
        user_id: int,
        task_type: str = None,
        status: str = None,
        skip: int = 0,
        limit: int = 10,
        db: AsyncSession = None
    ) -> List[Task]:
        """
        获取用户的任务列表，支持多条件过滤和分页
        """
        try:
            if db is None:
                from utils.db import get_async_session
                async with get_async_session() as session:
                    query = select(Task).where(Task.user_id == user_id)
                    if task_type:
                        query = query.where(Task.task_type == task_type)
                    if status:
                        query = query.where(Task.status == status)
                    query = query.order_by(desc(Task.created_at)).limit(limit).offset(skip)
                    result = await session.execute(query)
                    tasks = result.scalars().all()
                    return tasks
            else:
                query = select(Task).where(Task.user_id == user_id)
                if task_type:
                    query = query.where(Task.task_type == task_type)
                if status:
                    query = query.where(Task.status == status)
                query = query.order_by(desc(Task.created_at)).limit(limit).offset(skip)
                result = await db.execute(query)
                tasks = result.scalars().all()
                return tasks
        except Exception as e:
            logger.error(f"获取用户任务列表失败: {str(e)}", exc_info=True)
            return []
        
    @classmethod
    async def count_tasks(cls, task_type: str = None, status: str = None, user_id: int = None) -> int:
        """
        统计任务数量
        
        参数:
            task_type: 任务类型过滤
            status: 状态过滤
            user_id: 用户ID过滤
            
        返回:
            任务数量
        """
        try:
            from utils.db import get_async_session
            async with get_async_session() as session:
                # 构建查询
                query = select(func.count()).select_from(Task)
                
                # 添加过滤条件
                if task_type:
                    query = query.where(Task.task_type == task_type)
                
                if status:
                    query = query.where(Task.status == status)
                
                if user_id is not None:
                    query = query.where(Task.user_id == user_id)
                
                # 执行查询
                result = await session.execute(query)
                count = result.scalar_one()
                
                return count
        except Exception as e:
            logger.error(f"统计任务数量失败: {str(e)}", exc_info=True)
            return 0

    @classmethod
    def add_task_log(cls, task_id: str, message: str, level: str = "info") -> None:
        """
        添加任务日志
        
        参数:
            task_id: 任务ID
            message: 日志消息
            level: 日志级别 (info, warning, error)
        """
        # 确保task_id是字符串类型
        if not isinstance(task_id, str):
            logger.warning(f"任务ID类型错误: {type(task_id)}，尝试转换为字符串")
            try:
                task_id = str(task_id)
            except Exception as e:
                logger.error(f"转换任务ID失败: {str(e)}")
                return
        
        # 直接使用同步方法添加日志，避免事件循环相关问题
        cls._add_task_log_sync_fallback(task_id, message, level)

    @classmethod
    async def add_task_log_async(cls, task_id: str, message: str, level: str = "info") -> bool:
        """
        异步添加任务日志
        
        参数:
            task_id: 任务ID
            message: 日志消息
            level: 日志级别
            
        返回:
            添加是否成功
        """
        try:
            # 检查当前事件循环状态并立即切换到同步方法
            try:
                loop = asyncio.get_running_loop()
                if loop.is_closed():
                    logger.warning(f"当前事件循环已关闭，直接使用同步方法添加任务日志: {task_id}")
                    return cls._add_task_log_sync_fallback(task_id, message, level)
            except RuntimeError:
                logger.warning(f"没有运行中的事件循环，直接使用同步方法添加任务日志: {task_id}")
                return cls._add_task_log_sync_fallback(task_id, message, level)
            
            from utils.db import get_async_session
            
            # 尝试使用异步会话添加日志
            try:
                # 最后再次检查事件循环状态
                if asyncio.get_event_loop().is_closed():
                    logger.warning(f"事件循环在获取会话前已关闭，直接使用同步方法添加任务日志: {task_id}")
                    return cls._add_task_log_sync_fallback(task_id, message, level)
                    
                # 使用上下文管理器安全地获取和使用会话
                async with get_async_session() as session:
                    # 创建日志记录
                    log = TaskLog(
                        task_id=task_id,
                        message=message,
                        log_type=level,
                        created_at=datetime.now()
                    )
                    
                    # 添加到数据库
                    session.add(log)
                    await session.commit()
                    logger.debug(f"已添加任务日志: {task_id}, 级别: {level}, 消息: {message}")
                    return True
            except Exception as e:
                # 任何异常直接尝试同步方法
                logger.warning(f"异步添加任务日志时发生异常: {str(e)}, 使用同步方法")
                return cls._add_task_log_sync_fallback(task_id, message, level)
        except Exception as e:
            logger.error(f"添加任务日志失败: {str(e)}", exc_info=True)
            # 最后尝试使用同步方法
            return cls._add_task_log_sync_fallback(task_id, message, level)

    @classmethod
    def _add_task_log_sync_fallback(cls, task_id: str, message: str, level: str = "info") -> bool:
        """
        同步方式添加任务日志（作为异步方法的备选方案）
        
        参数:
            task_id: 任务ID
            message: 日志消息
            level: 日志级别 (info, warning, error)
            
        返回:
            是否成功添加日志
        """
        max_retries = 3
        retry_count = 0
        
        while retry_count < max_retries:
            try:
                from utils.db import get_db
                
                # 获取同步会话
                try:
                    db = next(get_db())
                except Exception as db_error:
                    logger.error(f"[sync] 获取数据库连接失败: {str(db_error)}")
                    retry_count += 1
                    if retry_count < max_retries:
                        import time
                        time.sleep(0.5)
                        continue
                    else:
                        return False
                
                # 检查连接状态
                try:
                    # 不要使用db.bind.closed属性，因为SQLAlchemy的Engine对象没有closed属性
                    # 尝试执行一个简单的查询来检查连接状态
                    db.execute(text("SELECT 1")).scalar()
                    connection_ok = True
                except Exception as e:
                    logger.warning(f"[sync] 数据库连接检查失败: {str(e)}")
                    connection_ok = False
                
                # 如果连接已关闭，尝试获取新连接
                if not connection_ok:
                    logger.warning(f"[sync] 检测到数据库连接可能已关闭，尝试获取新连接")
                    try:
                        from utils.db import get_db
                        db = next(get_db())
                    except Exception as conn_error:
                        logger.error(f"[sync] 获取新数据库连接失败: {str(conn_error)}")
                        retry_count += 1
                        if retry_count < max_retries:
                            import time
                            time.sleep(0.5)
                            continue
                        else:
                            return False
                
                try:
                    # 创建日志记录
                    from models.task import TaskLog
                    
                    log = TaskLog(
                        task_id=task_id,
                        message=message,
                        log_type=level,
                        created_at=datetime.now()
                    )
                    
                    # 添加到数据库
                    db.add(log)
                    
                    # 提交更改
                    try:
                        db.commit()
                        logger.info(f"[sync] 已添加任务日志: {task_id}, 级别: {level}")
                        return True
                    except Exception as commit_error:
                        if "connection was closed" in str(commit_error) or "was closed" in str(commit_error):
                            logger.warning(f"[sync] 提交更改时连接已关闭: {str(commit_error)}")
                            try:
                                db.rollback()
                            except:
                                pass
                            retry_count += 1
                            if retry_count < max_retries:
                                import time
                                time.sleep(0.5)
                                continue
                            else:
                                # 作为最后的尝试，直接使用SQL添加
                                return cls._add_log_with_raw_sql(task_id, message, level)
                        else:
                            logger.error(f"[sync] 提交更改失败: {str(commit_error)}")
                            try:
                                db.rollback()
                            except:
                                pass
                            retry_count += 1
                            if retry_count < max_retries:
                                import time
                                time.sleep(0.5)
                                continue
                            else:
                                # 作为最后的尝试，直接使用SQL添加
                                return cls._add_log_with_raw_sql(task_id, message, level)
                except Exception as db_error:
                    logger.error(f"[sync] 添加任务日志时数据库操作失败: {str(db_error)}")
                    try:
                        db.rollback()
                    except:
                        pass
                    retry_count += 1
                    if retry_count < max_retries:
                        import time
                        time.sleep(0.5)
                        continue
                    else:
                        # 作为最后的尝试，直接使用SQL添加
                        return cls._add_log_with_raw_sql(task_id, message, level)
            except Exception as e:
                logger.error(f"[sync] 添加任务日志失败: {str(e)}")
                retry_count += 1
                if retry_count < max_retries:
                    import time
                    time.sleep(0.5)
                    continue
                else:
                    # 作为最后的尝试，直接使用SQL添加
                    return cls._add_log_with_raw_sql(task_id, message, level)
        
        # 不应该到达这里，但为了安全返回False
        return False

    @classmethod
    def _add_log_with_raw_sql(cls, task_id: str, message: str, level: str = "info") -> bool:
        """
        使用原始SQL添加任务日志（最后的备选方案）
        """
        try:
            from utils.db import get_db
            db = next(get_db())
            
            # 转义可能的单引号
            safe_message = message.replace("'", "''") if message else ""
            safe_level = level.replace("'", "''") if level else "info"
            
            # 构建SQL语句
            import datetime
            now = datetime.datetime.now().isoformat()
            sql = f"INSERT INTO task_logs (task_id, message, log_type, created_at) VALUES ('{task_id}', '{safe_message}', '{safe_level}', '{now}')"
            
            # 执行SQL
            db.execute(text(sql))
            db.commit()
            logger.info(f"[sync] 使用原始SQL成功添加任务日志: {task_id}")
            return True
        except Exception as final_error:
            logger.error(f"[sync] 最终SQL添加尝试也失败: {str(final_error)}")
            return False

    @classmethod
    async def get_task_logs(cls, task_id: str) -> List[Dict[str, Any]]:
        """
        获取任务日志
        
        参数:
            task_id: 任务ID
            
        返回:
            任务日志列表
        """
        try:
            from utils.db import get_async_session
            async with get_async_session() as session:
                # 查询任务日志
                query = select(TaskLog).where(TaskLog.task_id == task_id).order_by(TaskLog.created_at)
                result = await session.execute(query)
                logs = result.scalars().all()
                
                # 转换为字典列表
                log_list = []
                for log in logs:
                    log_list.append({
                        "id": log.id,
                        "task_id": log.task_id,
                        "message": log.message,
                        "level": log.log_type,
                        "timestamp": log.created_at
                    })
                
                return log_list
        except Exception as e:
            logger.error(f"获取任务日志失败: {str(e)}", exc_info=True)
            return []

    @classmethod
    async def delete_task(cls, task_id: str) -> bool:
        """
        删除任务记录
        
        参数:
            task_id: 任务ID
            
        返回:
            是否删除成功
        """
        try:
            from utils.db import get_async_session
            async with get_async_session() as session:
                # 查询任务
                query = select(Task).where(Task.task_id == task_id)
                result = await session.execute(query)
                task = result.scalar_one_or_none()
                
                if not task:
                    logger.warning(f"未找到任务: {task_id}")
                    return False
                
                # 删除任务
                await session.delete(task)
                await session.commit()
                
                logger.info(f"已删除任务: {task_id}")
                return True
        except Exception as e:
            logger.error(f"删除任务失败: {str(e)}", exc_info=True)
            return False

    @classmethod
    async def get_all_tasks(cls, limit: int = 10, offset: int = 0, task_type: str = None, status: str = None) -> List[Task]:
        """
        获取所有任务
        
        参数:
            limit: 限制数量
            offset: 偏移量
            task_type: 任务类型过滤
            status: 状态过滤
            
        返回:
            任务列表
        """
        try:
            from utils.db import get_async_session
            async with get_async_session() as session:
                # 构建查询
                query = select(Task)
                
                # 添加过滤条件
                if task_type:
                    query = query.where(Task.task_type == task_type)
                
                if status:
                    query = query.where(Task.status == status)
                
                # 添加排序和分页
                query = query.order_by(desc(Task.created_at)).limit(limit).offset(offset)
                
                # 执行查询
                result = await session.execute(query)
                tasks = result.scalars().all()
                
                return tasks
        except Exception as e:
            logger.error(f"获取所有任务失败: {str(e)}", exc_info=True)
            return []

    @classmethod
    async def get_task_by_celery_id(cls, celery_task_id: str) -> Optional[Task]:
        """
        通过Celery任务ID获取任务
        
        参数:
            celery_task_id: Celery任务ID
            
        返回:
            任务记录，如果不存在则返回None
        """
        try:
            from utils.db import get_async_session
            async with get_async_session() as session:
                # 查询任务
                query = select(Task).where(Task.celery_task_id == celery_task_id)
                result = await session.execute(query)
                task = result.scalar_one_or_none()
                
                if not task:
                    logger.warning(f"未找到Celery任务: {celery_task_id}")
                    return None
                
                return task
        except Exception as e:
            logger.error(f"通过Celery任务ID获取任务失败: {str(e)}", exc_info=True)
            return None

    @classmethod
    def update_task_result(cls, task_id: str, result: dict) -> None:
        """
        更新任务结果
        
        参数:
            task_id: 任务ID
            result: 任务结果
        """
        # 确保task_id是字符串类型
        if not isinstance(task_id, str):
            logger.warning(f"任务ID类型错误: {type(task_id)}，尝试转换为字符串")
            try:
                task_id = str(task_id)
            except Exception as e:
                logger.error(f"转换任务ID失败: {str(e)}")
                return
        
        # 尝试使用同步方法更新任务结果
        try:
            # 检查是否有运行中的事件循环
            try:
                loop = asyncio.get_running_loop()
                # 如果有事件循环，使用异步方式更新
                asyncio.create_task(cls.update_task_result_async(task_id, result))
            except RuntimeError:
                # 没有运行中的事件循环，使用同步方法
                logger.warning(f"没有运行中的事件循环，使用同步方法更新任务结果: {task_id}")
                cls._update_task_result_sync(task_id, result)
        except Exception as e:
            logger.error(f"更新任务结果失败: {str(e)}", exc_info=True)
            # 尝试使用同步方法作为备选
            try:
                cls._update_task_result_sync(task_id, result)
            except Exception as sync_error:
                logger.error(f"同步更新任务结果也失败: {str(sync_error)}", exc_info=True)
                
    @classmethod
    def _update_task_result_sync(cls, task_id: str, result: dict) -> bool:
        """
        同步更新任务结果
        
        参数:
            task_id: 任务ID
            result: 任务结果
            
        返回:
            更新是否成功
        """
        try:
            from utils.db import get_db
            
            # 获取同步会话
            db = next(get_db())
            
            try:
                # 查询任务
                from sqlalchemy.orm import Session
                from sqlalchemy import select as sync_select
                
                stmt = sync_select(Task).where(Task.task_id == task_id)
                task = db.execute(stmt).scalar_one_or_none()
                
                if not task:
                    logger.warning(f"[sync] 更新任务结果失败: 未找到任务 {task_id}")
                    return False
                
                # 更新任务结果
                task.output_data = result
                task.updated_at = datetime.now()
                
                # 如果更新结果成功，也可以更新状态
                if task.status == "running" or task.status == "processing":
                    task.status = "completed"
                    task.completed_at = datetime.now()
                
                # 提交更改
                db.commit()
                db.refresh(task)
                logger.info(f"[sync] 已更新任务结果: {task_id}")
                return True
            except Exception as db_error:
                logger.error(f"[sync] 更新任务结果时数据库操作失败: {str(db_error)}", exc_info=True)
                try:
                    db.rollback()
                except:
                    pass
                return False
        except Exception as e:
            logger.error(f"[sync] 更新任务结果失败: {str(e)}", exc_info=True)
            return False

    @classmethod
    async def update_task_result_async(cls, task_id: str, result: dict) -> None:
        """
        异步更新任务结果
        
        参数:
            task_id: 任务ID
            result: 任务结果
        """
        try:
            import json
            from utils.db import get_async_session
            
            # 处理结果对象，确保它可以序列化
            if result is not None and not isinstance(result, str):
                try:
                    serialized_result = json.dumps(result)
                    # 只是测试序列化成功，但保留原始对象
                except Exception as e:
                    logger.warning(f"任务结果无法被序列化为JSON: {str(e)}")
                    result = str(result)  # 如果无法序列化，转为字符串
            
            async with get_async_session() as session:
                try:
                    # 查询任务 - 确保使用task_id而不是id
                    result_query = await session.execute(select(Task).where(Task.task_id == task_id))
                    task = result_query.scalars().first()
                    if not task:
                        logger.warning(f"任务不存在: {task_id}")
                        return
                    
                    # 更新任务结果
                    task.output_data = result  # 使用output_data而不是result字段
                    task.updated_at = datetime.now()
                    
                    # 如果更新结果成功，也可以更新状态
                    if task.status == "running" or task.status == "processing":
                        task.status = "completed"
                        task.completed_at = datetime.now()
                    
                    # 提交更改
                    await session.commit()
                    logger.info(f"已更新任务结果: {task_id}")
                except Exception as e:
                    await session.rollback()
                    logger.error(f"更新任务结果时数据库操作失败: {str(e)}", exc_info=True)
        except Exception as e:
            logger.error(f"更新任务结果失败: {str(e)}", exc_info=True)

    @staticmethod
    def update_task_status_sync(
        db,
        task_id: str,
        status: str,
        progress: Optional[int] = None,
        message: Optional[str] = None,
        result_url: Optional[str] = None,
        audio_url: Optional[str] = None,
        thumbnail_url: Optional[str] = None,
        celery_task_id: Optional[str] = None,
        logs: Optional[List[Dict[str, Any]]] = None
    ) -> bool:
        """
        同步：更新任务状态
        """
        max_retries = 3
        retry_count = 0
        
        while retry_count < max_retries:
            try:
                # 尝试获取现有连接的状态
                try:
                    connection_ok = not db.bind.closed
                except:
                    connection_ok = False
                
                # 如果连接已关闭，尝试获取新连接
                if not connection_ok:
                    logger.warning(f"[sync] 检测到数据库连接已关闭，尝试获取新连接")
                    try:
                        from utils.db import get_db
                        db = next(get_db())
                        # 验证新连接是否有效
                        if db.bind.closed:
                            logger.warning(f"[sync] 新获取的数据库连接仍然关闭，尝试重新初始化连接")
                            # 尝试重新初始化数据库连接
                            from utils.db import init_db
                            init_db()
                            db = next(get_db())
                    except Exception as conn_error:
                        logger.error(f"[sync] 获取新数据库连接失败: {str(conn_error)}")
                        retry_count += 1
                        import time
                        time.sleep(0.5)
                        continue
                
                # 查询并更新任务
                try:
                    stmt = select(Task).where(Task.task_id == task_id)
                    result = db.execute(stmt)
                    task = result.scalar_one_or_none()
                except Exception as query_error:
                    if "connection was closed" in str(query_error) or "was closed" in str(query_error):
                        logger.warning(f"[sync] 执行查询时连接已关闭: {str(query_error)}")
                        retry_count += 1
                        import time
                        time.sleep(0.5)
                        continue
                    else:
                        raise
                
                if not task:
                    logger.warning(f"[sync] 更新任务状态失败: 未找到任务 {task_id}")
                    return False
                
                # 更新任务状态
                task.status = status
                if progress is not None:
                    task.progress = progress
                if message:
                    task.message = message
                if result_url:
                    task.result_url = result_url
                if audio_url:
                    task.audio_url = audio_url
                if thumbnail_url:
                    task.thumbnail_url = thumbnail_url
                if celery_task_id:
                    task.celery_task_id = celery_task_id
                if logs:
                    if not task.logs:
                        task.logs = []
                    task.logs.extend(logs)
                
                # 如果任务完成或成功，清除错误信息
                if status in ["completed", "success"] and progress == 100:
                    task.error = None
                    task.completed_at = datetime.now()
                
                task.updated_at = datetime.now()
                
                # 提交更改
                try:
                    db.commit()
                    db.refresh(task)
                    logger.info(f"[sync] 已更新任务状态: {task_id}, 状态: {status}, 进度: {progress}")
                    return True
                except Exception as commit_error:
                    if "connection was closed" in str(commit_error) or "was closed" in str(commit_error):
                        logger.warning(f"[sync] 提交更改时连接已关闭: {str(commit_error)}")
                        db.rollback()
                        retry_count += 1
                        import time
                        time.sleep(0.5)
                        continue
                    else:
                        logger.error(f"[sync] 提交更改时出错: {str(commit_error)}")
                        db.rollback()
                        # 重试前增加重试计数
                        retry_count += 1
                        if retry_count < max_retries:
                            logger.info(f"[sync] 重试更新任务状态 ({retry_count}/{max_retries}): {task_id}")
                            continue
                        else:
                            logger.error(f"[sync] 达到最大重试次数，无法更新任务状态: {task_id}")
                            return False
                
            except Exception as e:
                # 重试前增加重试计数
                retry_count += 1
                logger.error(f"[sync] 更新任务状态时发生错误 (尝试 {retry_count}/{max_retries}): {str(e)}")
                
                # 尝试回滚事务，但忽略可能的回滚错误
                try:
                    db.rollback()
                except:
                    pass
                
                # 如果可以继续重试
                if retry_count < max_retries:
                    logger.info(f"[sync] 重试更新任务状态: {task_id}")
                    
                    # 在重试前尝试获取新的数据库连接
                    try:
                        from utils.db import get_db
                        db = next(get_db())
                        logger.info(f"[sync] 获取了新的数据库连接用于重试")
                    except Exception as db_error:
                        logger.error(f"[sync] 获取新数据库连接失败: {str(db_error)}")
                    
                    # 短暂延迟后重试
                    import time
                    time.sleep(0.5)
                    continue
                else:
                    logger.error(f"[sync] 达到最大重试次数，无法更新任务状态: {task_id}")
                    
                    # 作为最后的尝试，直接使用SQL更新
                    try:
                        from utils.db import get_db
                        from sqlalchemy import text
                        new_db = next(get_db())
                        logger.info(f"[sync] 尝试使用原始SQL进行最终更新尝试")
                        
                        # 转义可能的单引号
                        safe_status = status.replace("'", "''") if status else "pending"
                        safe_message = message.replace("'", "''") if message else None
                        
                        # 构建SQL语句
                        sql = f"UPDATE tasks SET status = '{safe_status}', "
                        if progress is not None:
                            sql += f"progress = {progress}, "
                        else:
                            sql += "progress = progress, "
                        
                        if safe_message:
                            sql += f"message = '{safe_message}', "
                        
                        # 如果任务完成或成功，清除错误信息并设置完成时间
                        if status in ["completed", "success"] and progress == 100:
                            sql += "error = NULL, completed_at = CURRENT_TIMESTAMP, "
                        
                        sql += f"updated_at = CURRENT_TIMESTAMP WHERE task_id = '{task_id}'"
                        
                        # 执行SQL
                        new_db.execute(text(sql))
                        new_db.commit()
                        logger.info(f"[sync] 使用原始SQL成功更新任务状态: {task_id}")
                        return True
                    except Exception as final_error:
                        logger.error(f"[sync] 最终更新尝试也失败: {str(final_error)}")
                        return False
                    
        # 不应该到达这里，但为了安全返回False
        return False

    @staticmethod
    def add_task_log_sync(
        db,
        task_id: str,
        message: str,
        log_type: str = "info"
    ) -> bool:
        """
        同步：添加任务日志
        """
        max_retries = 3
        retry_count = 0
        
        while retry_count < max_retries:
            try:
                # 确保task_id是字符串类型
                if not isinstance(task_id, str):
                    logger.warning(f"任务ID类型错误: {type(task_id)}，尝试转换为字符串")
                    try:
                        task_id = str(task_id)
                    except Exception as e:
                        logger.error(f"转换任务ID失败: {str(e)}")
                        return False
                
                # 尝试获取现有连接的状态
                try:
                    connection_ok = not db.bind.closed
                except:
                    connection_ok = False
                
                # 如果连接已关闭，尝试获取新连接
                if not connection_ok:
                    logger.warning(f"[sync] 检测到数据库连接已关闭，尝试获取新连接")
                    try:
                        from utils.db import get_db
                        db = next(get_db())
                        # 验证新连接是否有效
                        if db.bind.closed:
                            logger.warning(f"[sync] 新获取的数据库连接仍然关闭，尝试重新初始化连接")
                            # 尝试重新初始化数据库连接
                            from utils.db import init_db
                            init_db()
                            db = next(get_db())
                    except Exception as conn_error:
                        logger.error(f"[sync] 获取新数据库连接失败: {str(conn_error)}")
                        retry_count += 1
                        import time
                        time.sleep(0.5)
                        continue
                
                # 使用同步ORM创建日志记录
                try:
                    from models.task import TaskLog
                    
                    log = TaskLog(
                        task_id=task_id,
                        message=message,
                        log_type=log_type,
                        created_at=datetime.now()
                    )
                    
                    # 添加到数据库
                    db.add(log)
                    db.commit()
                    
                    logger.info(f"[sync] 添加任务日志成功: {task_id}, 类型: {log_type}")
                    return True
                except Exception as db_error:
                    if "connection was closed" in str(db_error) or "was closed" in str(db_error):
                        logger.warning(f"[sync] 添加日志时连接已关闭: {str(db_error)}")
                        try:
                            db.rollback()
                        except:
                            pass
                        retry_count += 1
                        import time
                        time.sleep(0.5)
                        continue
                    else:
                        logger.error(f"[sync] 添加任务日志时出错: {str(db_error)}")
                        try:
                            db.rollback()
                        except:
                            pass
                        retry_count += 1
                        if retry_count < max_retries:
                            logger.info(f"[sync] 重试添加任务日志 ({retry_count}/{max_retries}): {task_id}")
                            import time
                            time.sleep(0.5)
                            continue
                        else:
                            logger.error(f"[sync] 达到最大重试次数，无法添加任务日志: {task_id}")
                            return False
            except Exception as e:
                # 重试前增加重试计数
                retry_count += 1
                logger.error(f"[sync] 添加任务日志时发生错误 (尝试 {retry_count}/{max_retries}): {str(e)}")
                
                # 尝试回滚事务，但忽略可能的回滚错误
                try:
                    db.rollback()
                except:
                    pass
                
                # 如果可以继续重试
                if retry_count < max_retries:
                    logger.info(f"[sync] 重试添加任务日志: {task_id}")
                    
                    # 在重试前尝试获取新的数据库连接
                    try:
                        from utils.db import get_db
                        db = next(get_db())
                        logger.info(f"[sync] 获取了新的数据库连接用于重试")
                    except Exception as db_error:
                        logger.error(f"[sync] 获取新数据库连接失败: {str(db_error)}")
                    
                    # 短暂延迟后重试
                    import time
                    time.sleep(0.5)
                    continue
                else:
                    logger.error(f"[sync] 达到最大重试次数，无法添加任务日志: {task_id}")
                    
                    # 作为最后的尝试，直接使用SQL添加日志
                    try:
                        from utils.db import get_db
                        from sqlalchemy import text
                        new_db = next(get_db())
                        logger.info(f"[sync] 尝试使用原始SQL进行最终添加日志尝试")
                        
                        # 转义可能的单引号
                        safe_message = message.replace("'", "''") if message else ""
                        safe_log_type = log_type.replace("'", "''") if log_type else "info"
                        
                        # 使用原始SQL添加日志
                        sql = (
                            f"INSERT INTO task_logs (task_id, message, log_type, created_at) "
                            f"VALUES ('{task_id}', '{safe_message}', '{safe_log_type}', CURRENT_TIMESTAMP)"
                        )
                        new_db.execute(text(sql))
                        new_db.commit()
                        logger.info(f"[sync] 使用原始SQL成功添加任务日志: {task_id}")
                        return True
                    except Exception as final_error:
                        logger.error(f"[sync] 最终添加日志尝试也失败: {str(final_error)}")
                        return False
        
        # 不应该到达这里，但为了安全返回False
        return False 