#!/usr/bin/env python3
"""
测试最简单的任务
"""
import os
import sys
import time
from pathlib import Path
from dotenv import load_dotenv

# 添加后端路径
backend_dir = Path(__file__).parent.parent
sys.path.append(str(backend_dir))

# 加载环境变量
env_path = backend_dir / ".env"
load_dotenv(env_path)

def test_simple_task():
    """测试最简单的任务"""
    print("🧪 测试最简单的任务")
    print("=" * 50)
    
    try:
        from app.tasks.test_unified_task import test_unified_task
        from app.core.task_manager import task_manager
        
        # 创建任务记录
        task_id = task_manager.create_task(
            task_type="test",
            task_subtype="simple",
            user_id="test-user",
            title="简单测试任务",
            description="测试路由修复后的任务",
            input_params={"test_message": "路由修复测试", "duration": 3}
        )
        
        print(f"📝 任务ID: {task_id}")
        
        # 提交任务
        result = test_unified_task.delay(
            task_id=task_id,
            test_message="路由修复测试",
            duration=3
        )
        
        print(f"✅ 任务提交成功")
        print(f"📊 Celery 任务ID: {result.id}")
        print(f"📊 初始状态: {result.status}")
        
        # 监控任务
        print(f"⏳ 监控任务进度...")
        for i in range(15):
            time.sleep(1)
            
            # 检查 Celery 状态
            try:
                celery_status = result.status
            except Exception as e:
                celery_status = f"错误: {e}"
            
            # 检查数据库状态
            task_info = task_manager.get_task(task_id)
            if task_info:
                db_status = task_info['status']
                progress = task_info['progress']
                message = task_info.get('message', 'N/A')
                
                print(f"   [{i+1:2d}s] Celery: {celery_status} | DB: {db_status} ({progress}%) - {message}")
                
                if db_status == 'completed':
                    print(f"🎉 任务完成！")
                    return True
                elif db_status == 'failed':
                    error_msg = task_info.get('error_message', 'N/A')
                    print(f"❌ 任务失败: {error_msg}")
                    return False
            else:
                print(f"   [{i+1:2d}s] Celery: {celery_status} | DB: 任务未找到")
        
        print(f"⏰ 监控超时")
        return False
        
    except Exception as e:
        print(f"❌ 简单任务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🧪 简单任务测试（路由修复后）")
    print("=" * 60)
    
    success = test_simple_task()
    
    if success:
        print(f"\n🎉 简单任务测试成功！")
        print(f"💡 Celery 路由修复完成，现在可以测试 Wanx 任务")
    else:
        print(f"\n⚠️ 简单任务仍有问题")
        print(f"💡 请检查 Celery Worker 日志获取更多信息")

if __name__ == "__main__":
    main()
