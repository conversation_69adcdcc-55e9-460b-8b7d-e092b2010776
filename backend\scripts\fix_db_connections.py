#!/usr/bin/env python
"""
数据库连接优化与修复脚本
用于修复数据库连接泄漏问题并优化连接池设置
"""
import os
import sys
import logging
import time
from contextlib import contextmanager
from sqlalchemy import text, inspect, create_engine
from sqlalchemy.orm import sessionmaker

# 添加后端目录到系统路径
current_dir = os.path.dirname(os.path.abspath(__file__))
backend_dir = os.path.dirname(current_dir)
if backend_dir not in sys.path:
    sys.path.append(backend_dir)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def load_env_variables():
    """加载环境变量"""
    from dotenv import load_dotenv
    
    # 尝试加载不同位置的.env文件
    root_dir = os.path.dirname(backend_dir)
    env_files = [
        os.path.join(root_dir, '.env'),
        os.path.join(backend_dir, '.env'),
        '.env'
    ]
    
    for env_file in env_files:
        if os.path.exists(env_file):
            logger.info(f"加载环境变量文件: {env_file}")
            load_dotenv(env_file)
            return True
    
    logger.warning("未找到.env文件，将使用系统环境变量")
    return False

def fix_pool_settings():
    """修复数据库连接池设置"""
    from utils.db import engine, db_pool_stats, connection_usage_counter
    
    logger.info("检查当前连接池设置...")
    
    # 获取当前连接池统计信息
    pool = engine.pool
    
    pool_size = getattr(pool, "_pool_size", "未知")
    max_overflow = getattr(pool, "_max_overflow", "未知")
    pool_timeout = getattr(pool, "_pool_timeout", "未知")
    pool_recycle = getattr(pool, "_recycle", "未知")
    
    # 获取当前连接使用情况
    active_connections = pool.checkedout()
    total_connections = pool.size()
    
    logger.info(f"连接池状态: 活跃={active_connections}/{total_connections}, "
                f"池大小={pool_size}, 溢出={max_overflow}, "
                f"超时={pool_timeout}, 回收={pool_recycle}")
    
    # 计算高使用率连接数
    high_usage_count = sum(1 for usage in connection_usage_counter.values() if usage > 50)
    logger.info(f"高使用率连接数量: {high_usage_count}")
    
    # 尝试清理连接池
    logger.info("尝试清理连接池...")
    try:
        # 尝试重置连接池
        pool.dispose()
        logger.info("连接池已重置")
        
        # 清空使用计数器
        connection_usage_counter.clear()
        logger.info("连接使用计数器已清空")
        
        # 重置连接池统计信息
        for key in db_pool_stats.keys():
            if key not in ["last_error", "last_error_time"]:
                db_pool_stats[key] = 0
        logger.info("连接池统计信息已重置")
        
        return True
    except Exception as e:
        logger.error(f"清理连接池失败: {e}")
        return False

def check_tables_with_direct_session():
    """使用直接创建的会话检查数据库表"""
    # 从环境变量获取数据库URL
    DATABASE_URL = os.environ.get("DATABASE_URL")
    if not DATABASE_URL:
        logger.error("未找到DATABASE_URL环境变量")
        return False
    
    # 创建一个新的临时引擎
    temp_engine = create_engine(
        DATABASE_URL,
        pool_pre_ping=True,
        pool_size=5,
        max_overflow=10,
        pool_timeout=30,
        pool_recycle=1800,
        connect_args={
            "client_encoding": "utf8",
            "options": "-c timezone=utc -c client_encoding=utf8",
            "connect_timeout": 10
        }
    )
    
    # 创建会话
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=temp_engine)
    session = SessionLocal()
    
    try:
        # 检查连接
        result = session.execute(text("SELECT 1")).scalar()
        logger.info(f"数据库连接测试: {result}")
        
        # 检查表
        inspector = inspect(temp_engine)
        tables = inspector.get_table_names()
        logger.info(f"数据库中的表: {', '.join(tables)}")
        
        for table in ["tasks", "video_generations", "users"]:
            if table in tables:
                count = session.execute(text(f"SELECT COUNT(*) FROM {table}")).scalar()
                logger.info(f"表 {table} 中的记录数: {count}")
                
        return True
    except Exception as e:
        logger.error(f"使用直接会话查询数据库失败: {e}")
        return False
    finally:
        session.close()
        temp_engine.dispose()

def optimize_connection_settings():
    """优化连接设置，修改环境变量"""
    # 设置优化后的连接池参数
    os.environ["DB_POOL_SIZE"] = "20"         # 增加连接池大小
    os.environ["DB_MAX_OVERFLOW"] = "30"      # 增加最大溢出连接数
    os.environ["DB_POOL_TIMEOUT"] = "20"      # 增加超时时间
    os.environ["DB_POOL_RECYCLE"] = "600"     # 降低回收时间，更积极回收连接 (10分钟)
    
    # 应用新的设置
    try:
        from utils.db import refresh_pool_settings
        refresh_pool_settings()
        logger.info("已应用新的连接池设置")
        return True
    except Exception as e:
        logger.error(f"应用新的连接池设置失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("开始修复数据库连接...")
    
    # 加载环境变量
    load_env_variables()
    
    # 导入数据库模块
    try:
        from utils.db import get_db_pool_stats
    except ImportError:
        logger.error("无法导入数据库模块，请确保系统路径正确")
        return False
    
    # 输出当前连接池状态
    logger.info("当前连接池状态:")
    stats = get_db_pool_stats()
    for key, value in stats.items():
        logger.info(f"  {key}: {value}")
    
    # 使用直接会话检查数据库
    check_tables_with_direct_session()
    
    # 修复连接池设置
    fix_pool_settings()
    
    # 优化连接设置
    optimize_connection_settings()
    
    # 再次输出连接池状态
    logger.info("优化后连接池状态:")
    stats = get_db_pool_stats()
    for key, value in stats.items():
        logger.info(f"  {key}: {value}")
    
    logger.info("数据库连接修复完成")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 