<template>
  <div class="system-status">
    <div class="page-header">
      <h1>系统状态</h1>
      <p>查看舆情分析系统的运行状态和配置信息</p>
    </div>

    <el-row :gutter="24">
      <!-- 系统概览 -->
      <el-col :xs="24" :lg="12">
        <el-card title="系统概览" class="status-card">
          <template #header>
            <div class="card-header">
              <span>系统概览</span>
              <el-button type="primary" size="small" @click="refreshStatus">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
          </template>

          <div class="status-grid">
            <div class="status-item">
              <div class="status-icon">
                <el-icon :class="getStatusClass('database')"><Coin /></el-icon>
              </div>
              <div class="status-info">
                <h4>数据库</h4>
                <p>{{ systemStatus.database || '未知' }}</p>
              </div>
            </div>

            <div class="status-item">
              <div class="status-icon">
                <el-icon :class="getStatusClass('redis')"><Link /></el-icon>
              </div>
              <div class="status-info">
                <h4>Redis缓存</h4>
                <p>{{ systemStatus.redis || '未知' }}</p>
              </div>
            </div>

            <div class="status-item">
              <div class="status-icon">
                <el-icon :class="getStatusClass('ollama')"><Monitor /></el-icon>
              </div>
              <div class="status-info">
                <h4>AI服务</h4>
                <p>{{ systemStatus.ollama?.available ? '可用' : '不可用' }}</p>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- Ollama模型信息 -->
      <el-col :xs="24" :lg="12">
        <el-card title="AI模型状态" class="status-card">
          <template #header>
            <div class="card-header">
              <span>AI模型状态</span>
              <el-tag :type="systemStatus.ollama?.available ? 'success' : 'danger'">
                {{ systemStatus.ollama?.available ? '在线' : '离线' }}
              </el-tag>
            </div>
          </template>

          <div v-if="systemStatus.ollama?.available" class="ollama-info">
            <div class="info-item">
              <strong>服务地址:</strong>
              <span>{{ systemStatus.ollama.base_url }}</span>
            </div>
            <div class="info-item">
              <strong>当前模型:</strong>
              <el-tag type="primary">{{ systemStatus.ollama.best_model }}</el-tag>
            </div>
            <div class="info-item">
              <strong>可用模型:</strong>
              <span>{{ systemStatus.ollama.models?.length || 0 }} 个</span>
            </div>
          </div>

          <div v-else class="ollama-offline">
            <el-alert
              title="Ollama服务离线"
              description="请启动Ollama服务: ollama serve"
              type="warning"
              show-icon
              :closable="false"
            />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 模型详情 -->
    <el-row :gutter="24" style="margin-top: 24px;">
      <el-col :span="24">
        <el-card title="可用模型列表" class="models-card">
          <template #header>
            <div class="card-header">
              <span>可用模型列表</span>
              <span class="model-count">
                {{ systemStatus.ollama?.models?.length || 0 }} 个模型
              </span>
            </div>
          </template>

          <div v-if="systemStatus.ollama?.available && systemStatus.ollama.models?.length > 0">
            <el-table :data="systemStatus.ollama.models" style="width: 100%">
              <el-table-column prop="name" label="模型名称" min-width="200">
                <template #default="{ row }">
                  <div class="model-name">
                    <el-tag 
                      :type="row.name === systemStatus.ollama.best_model ? 'success' : 'info'"
                      size="small"
                    >
                      {{ row.name === systemStatus.ollama.best_model ? '当前' : '可用' }}
                    </el-tag>
                    <span style="margin-left: 8px;">{{ row.name }}</span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="size" label="模型大小" width="120">
                <template #default="{ row }">
                  {{ formatSize(row.size) }}
                </template>
              </el-table-column>
              <el-table-column prop="modified_at" label="修改时间" width="180">
                <template #default="{ row }">
                  {{ formatDate(row.modified_at) }}
                </template>
              </el-table-column>
              <el-table-column label="操作" width="120">
                <template #default="{ row }">
                  <el-button 
                    type="primary" 
                    size="small" 
                    @click="testModel(row.name)"
                    :disabled="testing"
                  >
                    测试
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <div v-else class="no-models">
            <el-empty description="暂无可用模型" />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 系统信息 -->
    <el-row :gutter="24" style="margin-top: 24px;">
      <el-col :span="24">
        <el-card title="系统信息" class="info-card">
          <div class="system-info">
            <div class="info-row">
              <span class="info-label">最后更新:</span>
              <span class="info-value">{{ formatDate(systemStatus.timestamp) }}</span>
            </div>
            <div class="info-row">
              <span class="info-label">系统版本:</span>
              <span class="info-value">v1.0.0</span>
            </div>
            <div class="info-row">
              <span class="info-label">运行环境:</span>
              <span class="info-value">Production</span>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import {
  Refresh,
  Coin,
  Link,
  Monitor
} from '@element-plus/icons-vue';
import { opinionAnalysisApi } from '@/services/opinion-analysis';

export default defineComponent({
  name: 'SystemStatus',
  components: {
    Refresh,
    Coin,
    Link,
    Monitor
  },
  setup() {
    const loading = ref(false);
    const testing = ref(false);
    const systemStatus = reactive({
      database: '',
      redis: '',
      ollama: {
        available: false,
        base_url: '',
        best_model: '',
        models: []
      },
      timestamp: ''
    });

    // 获取系统状态
    const loadSystemStatus = async () => {
      try {
        loading.value = true;
        const response = await opinionAnalysisApi.system.getStatus();
        
        if (response.data && response.data.success) {
          Object.assign(systemStatus, response.data.data);
        }
      } catch (error) {
        console.error('获取系统状态失败:', error);
        ElMessage.error('获取系统状态失败');
      } finally {
        loading.value = false;
      }
    };

    // 刷新状态
    const refreshStatus = async () => {
      await loadSystemStatus();
      ElMessage.success('状态已刷新');
    };

    // 获取状态样式类
    const getStatusClass = (service) => {
      if (service === 'ollama') {
        return systemStatus.ollama?.available ? 'status-success' : 'status-error';
      }
      const status = systemStatus[service];
      return status === 'connected' ? 'status-success' : 'status-error';
    };

    // 格式化文件大小
    const formatSize = (bytes) => {
      if (!bytes) return 'N/A';
      const gb = bytes / (1024 * 1024 * 1024);
      return `${gb.toFixed(1)} GB`;
    };

    // 格式化日期
    const formatDate = (dateStr) => {
      if (!dateStr) return 'N/A';
      return new Date(dateStr).toLocaleString('zh-CN');
    };

    // 测试模型
    const testModel = async (modelName) => {
      testing.value = true;
      try {
        ElMessage.info(`正在测试模型 ${modelName}...`);
        // 这里可以添加模型测试的API调用
        setTimeout(() => {
          ElMessage.success(`模型 ${modelName} 测试成功`);
          testing.value = false;
        }, 2000);
      } catch (error) {
        ElMessage.error(`模型 ${modelName} 测试失败`);
        testing.value = false;
      }
    };

    onMounted(() => {
      loadSystemStatus();
    });

    return {
      loading,
      testing,
      systemStatus,
      refreshStatus,
      getStatusClass,
      formatSize,
      formatDate,
      testModel
    };
  }
});
</script>

<style scoped>
.system-status {
  padding: 24px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #666;
}

.status-card {
  height: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16px;
}

.status-item {
  display: flex;
  align-items: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.status-icon {
  margin-right: 12px;
  font-size: 24px;
}

.status-icon .status-success {
  color: #67c23a;
}

.status-icon .status-error {
  color: #f56c6c;
}

.status-info h4 {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 600;
}

.status-info p {
  margin: 0;
  color: #666;
  font-size: 12px;
}

.ollama-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #eee;
}

.info-item:last-child {
  border-bottom: none;
}

.ollama-offline {
  padding: 16px 0;
}

.models-card {
  margin-top: 24px;
}

.model-count {
  color: #666;
  font-size: 14px;
}

.model-name {
  display: flex;
  align-items: center;
}

.no-models {
  padding: 40px 0;
  text-align: center;
}

.info-card {
  margin-top: 24px;
}

.system-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #eee;
}

.info-row:last-child {
  border-bottom: none;
}

.info-label {
  font-weight: 600;
  color: #333;
}

.info-value {
  color: #666;
}

@media (max-width: 768px) {
  .system-status {
    padding: 16px;
  }

  .status-grid {
    grid-template-columns: 1fr;
  }

  .info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}
</style>
