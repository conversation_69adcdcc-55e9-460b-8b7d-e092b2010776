#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WAN 2.1 T2V 1.3B模型下载工具

该脚本用于从HuggingFace或ModelScope下载WAN 2.1 T2V 1.3B模型，
并将其放置在正确的位置，创建必要的目录结构。
"""

import os
import sys
import argparse
import subprocess
import shutil
import logging
import traceback
import platform
import locale
from pathlib import Path

# 设置编码
if sys.platform.startswith('win'):
    # 设置Windows控制台编码为UTF-8
    os.system('chcp 65001 >nul 2>&1')
    # 强制Python输出UTF-8
    sys.stdout.reconfigure(encoding='utf-8')
    sys.stderr.reconfigure(encoding='utf-8')

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    encoding='utf-8'  # 确保日志使用UTF-8编码
)
logger = logging.getLogger(__name__)

# 模型信息
MODEL_ID = "Wan-AI/Wan2.1-T2V-1.3B"
MODEL_DIR_NAME = "Wan2.1-T2V-1.3B"
REQUIRED_SUBDIRS = ["vae", "scheduler", "unet"]

def ensure_directory(dir_path):
    """确保目录存在"""
    try:
        os.makedirs(dir_path, exist_ok=True)
        logger.info(f"目录已准备: {dir_path}")
        return True
    except Exception as e:
        logger.error(f"创建目录失败: {dir_path}, 错误: {e}")
        return False

def check_pip_package(package_name):
    """检查pip包是否已安装"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "show", package_name], 
                            stdout=subprocess.DEVNULL, 
                            stderr=subprocess.DEVNULL)
        logger.info(f"包已安装: {package_name}")
        return True
    except subprocess.CalledProcessError:
        logger.warning(f"包未安装: {package_name}")
        return False

def install_pip_package(package_name):
    """安装pip包"""
    try:
        logger.info(f"正在安装: {package_name}")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
        logger.info(f"成功安装: {package_name}")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"安装失败: {package_name}, 错误: {e}")
        return False

def download_model_huggingface(model_id, output_dir):
    """使用HuggingFace CLI下载模型"""
    try:
        logger.info(f"开始使用HuggingFace CLI下载模型: {model_id}")
        
        # 确保安装huggingface_hub
        if not check_pip_package("huggingface_hub"):
            logger.info("安装huggingface_hub[cli]...")
            if not install_pip_package("huggingface_hub[cli]"):
                logger.error("无法安装huggingface_hub[cli]")
                return False
        
        # 尝试查找huggingface-cli命令
        huggingface_cli_command = "huggingface-cli"
        
        # 检查命令是否可用
        try:
            # 使用where命令(Windows)或which命令(Unix)查找命令位置
            if platform.system() == "Windows":
                subprocess.check_call(["where", huggingface_cli_command], 
                                    stdout=subprocess.DEVNULL, 
                                    stderr=subprocess.DEVNULL)
            else:
                subprocess.check_call(["which", huggingface_cli_command], 
                                    stdout=subprocess.DEVNULL, 
                                    stderr=subprocess.DEVNULL)
            logger.info(f"找到命令: {huggingface_cli_command}")
        except subprocess.CalledProcessError:
            # 如果命令不可用，创建一个用Python直接调用的替代命令
            logger.warning(f"未找到 {huggingface_cli_command} 命令，尝试直接使用Python调用")
            huggingface_cli_command = [sys.executable, "-m", "huggingface_hub.cli.cli", "download"]
            model_id = [model_id]
            output_dir = ["--local-dir", output_dir]
        
            # 执行下载命令
            logger.info(f"执行命令: {' '.join(str(x) for x in huggingface_cli_command + model_id + output_dir)}")
            subprocess.check_call(huggingface_cli_command + model_id + output_dir)
            
            logger.info(f"模型下载完成: {output_dir}")
            return True
        
        # 如果huggingface-cli命令可用，直接使用命令行方式
        cmd = [huggingface_cli_command, "download", model_id, "--local-dir", output_dir]
        
        logger.info(f"执行命令: {' '.join(cmd)}")
        
        # 使用subprocess.Popen捕获输出
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            encoding='utf-8'
        )
        
        # 实时读取输出并打印
        stdout, stderr = process.communicate()
        if stdout:
            for line in stdout.splitlines():
                logger.info(line)
        
        if stderr:
            for line in stderr.splitlines():
                if "error" in line.lower() or "exception" in line.lower():
                    logger.error(line)
                else:
                    logger.warning(line)
        
        if process.returncode != 0:
            logger.error(f"下载命令失败，返回代码: {process.returncode}")
            return False
        
        logger.info(f"模型下载完成: {output_dir}")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"使用HuggingFace CLI下载模型失败: {e}")
        return False
    except Exception as e:
        logger.error(f"下载过程中出现未预期的错误: {e}")
        logger.error(traceback.format_exc())
        return False

def download_model_modelscope(model_id, output_dir):
    """使用ModelScope CLI下载模型"""
    try:
        logger.info(f"开始使用ModelScope下载模型: {model_id}")
        
        # 确保安装modelscope
        if not check_pip_package("modelscope"):
            install_pip_package("modelscope")
        
        # 下载模型
        cmd = [
            sys.executable, "-m", "modelscope.cli.download", 
            model_id, "--local_dir", output_dir
        ]
        
        logger.info(f"执行命令: {' '.join(cmd)}")
        
        # 使用subprocess.Popen捕获输出
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            encoding='utf-8'
        )
        
        # 实时读取输出并打印
        stdout, stderr = process.communicate()
        if stdout:
            for line in stdout.splitlines():
                logger.info(line)
        
        if stderr:
            for line in stderr.splitlines():
                if "error" in line.lower() or "exception" in line.lower():
                    logger.error(line)
                else:
                    logger.warning(line)
        
        if process.returncode != 0:
            logger.error(f"下载命令失败，返回代码: {process.returncode}")
            return False
        
        logger.info(f"模型下载完成: {output_dir}")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"使用ModelScope下载模型失败: {e}")
        return False
    except Exception as e:
        logger.error(f"下载过程中出现未预期的错误: {e}")
        logger.error(traceback.format_exc())
        return False

def check_model_structure(model_dir):
    """检查模型目录结构是否完整"""
    missing_subdirs = []
    
    for subdir in REQUIRED_SUBDIRS:
        subdir_path = os.path.join(model_dir, subdir)
        if not os.path.exists(subdir_path) or not os.path.isdir(subdir_path):
            missing_subdirs.append(subdir)
    
    if missing_subdirs:
        logger.warning(f"模型目录结构不完整，缺少以下子目录: {', '.join(missing_subdirs)}")
        return False, missing_subdirs
    
    logger.info(f"模型目录结构完整: {model_dir}")
    return True, []

def create_sample_config_files(model_dir):
    """创建基本配置文件样例，以避免初始化错误"""
    try:
        # 创建scheduler子目录
        scheduler_dir = os.path.join(model_dir, "scheduler")
        if not os.path.exists(scheduler_dir):
            os.makedirs(scheduler_dir, exist_ok=True)
            logger.info(f"创建scheduler子目录: {scheduler_dir}")
        
        # 创建unet子目录
        unet_dir = os.path.join(model_dir, "unet")
        if not os.path.exists(unet_dir):
            os.makedirs(unet_dir, exist_ok=True)
            logger.info(f"创建unet子目录: {unet_dir}")
        
        # 创建vae子目录
        vae_dir = os.path.join(model_dir, "vae")
        if not os.path.exists(vae_dir):
            os.makedirs(vae_dir, exist_ok=True)
            logger.info(f"创建vae子目录: {vae_dir}")

        # 创建scheduler配置文件
        scheduler_config = os.path.join(scheduler_dir, "scheduler_config.json")
        if not os.path.exists(scheduler_config):
            with open(scheduler_config, 'w', encoding='utf-8') as f:
                f.write('{"beta_schedule": "linear", "beta_start": 0.00085, "beta_end": 0.012, "clip_sample": false}')
            logger.info(f"创建调度器配置文件: {scheduler_config}")
            
        # 创建VAE配置文件
        vae_config = os.path.join(vae_dir, "config.json")
        if not os.path.exists(vae_config):
            with open(vae_config, 'w', encoding='utf-8') as f:
                f.write('{"sample_size": 32, "latent_channels": 4, "scaling_factor": 0.18215}')
            logger.info(f"创建VAE配置文件: {vae_config}")
            
        # 创建UNet配置文件
        unet_config = os.path.join(unet_dir, "config.json")
        if not os.path.exists(unet_config):
            with open(unet_config, 'w', encoding='utf-8') as f:
                f.write('{"sample_size": 32, "in_channels": 4, "out_channels": 4, "layers_per_block": 2}')
            logger.info(f"创建UNet配置文件: {unet_config}")
            
        return True
        
    except Exception as e:
        logger.error(f"创建配置文件样例失败: {e}")
        logger.error(traceback.format_exc())
        return False

def copy_model_to_location(src_dir, dest_dir):
    """将模型复制到指定位置"""
    try:
        logger.info(f"正在将模型从 {src_dir} 复制到 {dest_dir}")
        
        # 确保目标目录存在
        ensure_directory(os.path.dirname(dest_dir))
        
        # 如果目标目录已存在，先备份
        if os.path.exists(dest_dir):
            backup_dir = f"{dest_dir}_backup_{int(time.time())}"
            logger.info(f"目标目录已存在，创建备份: {backup_dir}")
            shutil.move(dest_dir, backup_dir)
        
        # 复制目录
        shutil.copytree(src_dir, dest_dir)
        logger.info(f"模型已成功复制到: {dest_dir}")
        return True
    except Exception as e:
        logger.error(f"复制模型失败: {e}")
        logger.error(traceback.format_exc())
        return False

def update_config_paths(model_path):
    """更新配置文件中的模型路径"""
    try:
        # 获取当前脚本目录
        script_dir = os.path.dirname(os.path.abspath(__file__))
        
        # 项目根目录
        project_root = os.path.dirname(os.path.dirname(script_dir))
        
        # 配置文件路径
        config_file = os.path.join(project_root, "backend", "config", "models.py")
        
        if os.path.exists(config_file):
            logger.info(f"更新配置文件: {config_file}")
            
            # 读取配置文件
            with open(config_file, 'r', encoding='utf-8') as f:
                config_content = f.read()
            
            # 更新模型路径
            import re
            pattern = r'("t2v_1_3b_model"\s*:\s*)"[^"]*"'
            replacement = r'\1"' + os.path.basename(model_path) + r'"'
            updated_content = re.sub(pattern, replacement, config_content)
            
            # 写回配置文件
            with open(config_file, 'w', encoding='utf-8') as f:
                f.write(updated_content)
            
            logger.info("配置文件已更新")
            return True
        else:
            logger.warning(f"配置文件不存在: {config_file}")
            return False
    except Exception as e:
        logger.error(f"更新配置文件失败: {e}")
        logger.error(traceback.format_exc())
        return False

def test_model_loading(model_dir):
    """测试模型是否可以正确加载"""
    try:
        # 简单测试模型目录是否存在
        if not os.path.exists(model_dir):
            logger.error(f"模型目录不存在: {model_dir}")
            return False
        
        # 检查模型结构
        complete, missing = check_model_structure(model_dir)
        if not complete:
            logger.warning(f"模型目录结构不完整，可能无法正常加载")
            # 尝试创建缺少的目录和配置文件
            create_sample_config_files(model_dir)
            
        logger.info(f"模型目录已准备: {model_dir}")
        return True
    except Exception as e:
        logger.error(f"测试模型加载失败: {e}")
        logger.error(traceback.format_exc())
        return False

def show_alternative_download_methods():
    """显示替代下载方法"""
    print("\n===== 替代下载方法 =====")
    print("如果自动下载失败，您可以尝试以下手动下载方法：")
    
    print("\n方法1：使用浏览器下载")
    print("1. 访问 https://huggingface.co/Wan-AI/Wan2.1-T2V-1.3B")
    print("2. 点击'Files and versions'标签")
    print("3. 下载所有必要的文件（主要是.safetensors和.json文件）")
    print("4. 将文件放置在正确的目录结构中")
    
    print("\n方法2：使用git lfs下载")
    print("1. 安装git lfs: https://git-lfs.github.com/")
    print("2. 使用以下命令克隆仓库:")
    print("   git lfs install")
    print("   git clone https://huggingface.co/Wan-AI/Wan2.1-T2V-1.3B")
    
    print("\n方法3：使用第三方下载工具")
    print("1. 使用如aria2c等支持断点续传的工具")
    print("2. 从HuggingFace获取直接下载链接")
    print("3. 使用下载工具进行下载")
    
    print("\n下载完成后，请确保将模型文件放置在以下目录结构中：")
    print("Wan2.1-T2V-1.3B/")
    print("├── scheduler/")
    print("│   └── scheduler_config.json")
    print("├── unet/")
    print("│   └── config.json")
    print("└── vae/")
    print("    └── config.json")
    print("以及其他必要的模型文件（.safetensors等）")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="WAN 2.1 T2V 1.3B模型下载工具")
    parser.add_argument("--download_dir", help="模型下载目录", default="./model_download")
    parser.add_argument("--target_dir", help="模型目标目录", 
                      default="backend/models_cache/Wan2.1-T2V-1.3B")
    parser.add_argument("--method", choices=["huggingface", "modelscope"], 
                      default="huggingface", help="下载方法")
    parser.add_argument("--skip_download", action="store_true", 
                      help="跳过下载步骤，仅配置模型")
    parser.add_argument("--copy_model", action="store_true", 
                      help="下载后复制模型到模型缓存目录")
    parser.add_argument("--debug", action="store_true",
                      help="显示更详细的调试信息")
    
    args = parser.parse_args()
    
    # 如果开启调试模式，设置日志级别为DEBUG
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
        logger.setLevel(logging.DEBUG)
        logger.debug("调试模式已启用")
    
    # 显示系统信息
    print("\n===== WAN 2.1 T2V 1.3B模型下载工具 =====\n")
    print(f"系统信息:")
    print(f"  操作系统: {platform.system()} {platform.release()}")
    print(f"  Python版本: {platform.python_version()}")
    print(f"  当前用户: {os.getlogin() if hasattr(os, 'getlogin') else '未知'}")
    print(f"  工作目录: {os.getcwd()}")
    print(f"  默认编码: {locale.getpreferredencoding()}")
    print(f"  控制台编码: {sys.stdout.encoding}\n")
    
    try:
        # 确保下载目录存在
        if not args.skip_download:
            download_dir = os.path.abspath(args.download_dir)
            ensure_directory(download_dir)
            
            # 完整模型下载路径
            model_download_path = os.path.join(download_dir, MODEL_DIR_NAME)
            
            # 下载模型
            download_success = False
            if args.method == "huggingface":
                download_success = download_model_huggingface(MODEL_ID, model_download_path)
            else:
                download_success = download_model_modelscope(MODEL_ID, model_download_path)
            
            if not download_success:
                logger.error("模型下载失败")
                show_alternative_download_methods()
                return 1
            
            # 测试模型目录结构
            test_model_loading(model_download_path)
            
            # 如果需要，复制模型到目标目录
            if args.copy_model:
                target_dir = os.path.abspath(args.target_dir)
                copy_model_to_location(model_download_path, target_dir)
                
                # 更新配置文件
                update_config_paths(target_dir)
                
                # 测试目标目录模型
                test_model_loading(target_dir)
        else:
            # 仅测试目标目录模型
            target_dir = os.path.abspath(args.target_dir)
            if os.path.exists(target_dir):
                test_model_loading(target_dir)
            else:
                logger.error(f"目标目录不存在: {target_dir}")
                return 1
        
        print("\n模型准备完成!")
        print("\n使用以下命令生成视频:")
        print('python generate.py --task t2v-1.3B --size 832*480 --ckpt_dir ./Wan2.1-T2V-1.3B --sample_shift 8 --sample_guide_scale 6 --prompt "你的提示词"')
        print("\n建议设置:")
        print("- sample_guide_scale 6: 推荐的引导比例")
        print("- sample_shift 8-12: 可在此范围内调整以优化性能")
        
    except Exception as e:
        logger.error(f"执行过程中出现错误: {e}")
        logger.error(traceback.format_exc())
        show_alternative_download_methods()
        return 1
    
    print("\n===== 下载过程完成 =====\n")
    return 0

if __name__ == "__main__":
    import time
    sys.exit(main()) 