#!/usr/bin/env python3
"""
快速 Celery 测试
"""
import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# 添加后端路径
backend_dir = Path(__file__).parent.parent
sys.path.append(str(backend_dir))

# 加载环境变量
env_path = backend_dir / ".env"
load_dotenv(env_path)

def main():
    print("🚀 快速 Celery 测试")
    print("=" * 40)
    
    try:
        # 1. 导入任务
        print("📦 导入任务...")
        from app.tasks.simple_test_task import simple_add, simple_hello
        print("✅ 任务导入成功")
        
        # 2. 测试同步执行
        print("\n🔄 测试同步执行...")
        result1 = simple_add(5, 3)
        print(f"✅ simple_add(5, 3) = {result1}")
        
        result2 = simple_hello("Test")
        print(f"✅ simple_hello('Test') = {result2}")
        
        # 3. 测试异步执行
        print("\n⚡ 测试异步执行...")
        task = simple_add.delay(10, 15)
        print(f"📋 任务 ID: {task.id}")
        print(f"📊 任务状态: {task.status}")
        
        # 等待结果
        try:
            result = task.get(timeout=15)
            print(f"✅ 异步结果: {result}")
            print("🎉 Celery 异步任务测试成功！")
            return True
        except Exception as e:
            print(f"❌ 异步任务失败: {e}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ Celery 系统工作正常！")
    else:
        print("\n❌ Celery 系统存在问题")
    
    input("按 Enter 键退出...")
