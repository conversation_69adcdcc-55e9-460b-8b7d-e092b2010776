#!/usr/bin/env python3
"""
多语言支持服务
支持多种语言的对话、翻译和本地化
"""

import logging
from typing import Dict, Any, List, Optional
import json
from pathlib import Path

logger = logging.getLogger(__name__)

class MultilingualService:
    """多语言服务类"""
    
    def __init__(self):
        self.supported_languages = {
            "zh": {
                "name": "中文",
                "native_name": "中文",
                "voice": "zh-cn-xiaoxiao",
                "whisper_code": "zh",
                "ollama_prompt": "请用中文回答"
            },
            "en": {
                "name": "English",
                "native_name": "English", 
                "voice": "en-us-aria",
                "whisper_code": "en",
                "ollama_prompt": "Please answer in English"
            },
            "ja": {
                "name": "Japanese",
                "native_name": "日本語",
                "voice": "ja-jp-nanami",
                "whisper_code": "ja",
                "ollama_prompt": "日本語で答えてください"
            },
            "ko": {
                "name": "Korean",
                "native_name": "한국어",
                "voice": "ko-kr-sun-hi",
                "whisper_code": "ko",
                "ollama_prompt": "한국어로 답변해 주세요"
            },
            "fr": {
                "name": "French",
                "native_name": "Français",
                "voice": "fr-fr-denise",
                "whisper_code": "fr",
                "ollama_prompt": "Veuillez répondre en français"
            },
            "de": {
                "name": "German",
                "native_name": "Deutsch",
                "voice": "de-de-katja",
                "whisper_code": "de",
                "ollama_prompt": "Bitte antworten Sie auf Deutsch"
            },
            "es": {
                "name": "Spanish",
                "native_name": "Español",
                "voice": "es-es-elvira",
                "whisper_code": "es",
                "ollama_prompt": "Por favor responde en español"
            },
            "ru": {
                "name": "Russian",
                "native_name": "Русский",
                "voice": "ru-ru-svetlana",
                "whisper_code": "ru",
                "ollama_prompt": "Пожалуйста, отвечайте на русском языке"
            }
        }
        
        self.translations = {}
        self.load_translations()
    
    def load_translations(self):
        """加载翻译文件"""
        try:
            # 这里可以加载实际的翻译文件
            # 目前使用内置翻译
            self.translations = {
                "zh": {
                    "greeting": "你好！我是你的AI数字人助手。",
                    "how_can_help": "我能为你做些什么？",
                    "goodbye": "再见！很高兴为你服务。",
                    "error": "抱歉，出现了一些问题。",
                    "thinking": "让我想想...",
                    "generating": "正在生成回复...",
                    "language_switched": "语言已切换为中文"
                },
                "en": {
                    "greeting": "Hello! I'm your AI digital human assistant.",
                    "how_can_help": "How can I help you?",
                    "goodbye": "Goodbye! It was nice serving you.",
                    "error": "Sorry, something went wrong.",
                    "thinking": "Let me think...",
                    "generating": "Generating response...",
                    "language_switched": "Language switched to English"
                },
                "ja": {
                    "greeting": "こんにちは！私はあなたのAIデジタルヒューマンアシスタントです。",
                    "how_can_help": "何かお手伝いできることはありますか？",
                    "goodbye": "さようなら！お役に立てて嬉しかったです。",
                    "error": "申し訳ございません、問題が発生しました。",
                    "thinking": "考えさせてください...",
                    "generating": "回答を生成中...",
                    "language_switched": "言語が日本語に切り替わりました"
                }
            }
            
            logger.info("✅ 翻译文件加载成功")
            
        except Exception as e:
            logger.error(f"❌ 翻译文件加载失败: {e}")
    
    def get_supported_languages(self) -> List[Dict[str, str]]:
        """获取支持的语言列表"""
        return [
            {
                "code": code,
                "name": info["name"],
                "native_name": info["native_name"]
            }
            for code, info in self.supported_languages.items()
        ]
    
    def detect_language(self, text: str) -> str:
        """
        检测文本语言
        
        Args:
            text: 输入文本
        
        Returns:
            语言代码
        """
        # 简单的语言检测逻辑
        text = text.strip()
        
        # 检测中文字符
        chinese_chars = sum(1 for char in text if '\u4e00' <= char <= '\u9fff')
        total_chars = len(text)
        
        if total_chars == 0:
            return "zh"  # 默认中文
        
        chinese_ratio = chinese_chars / total_chars
        
        if chinese_ratio > 0.3:
            return "zh"
        
        # 检测日文字符
        japanese_chars = sum(1 for char in text if '\u3040' <= char <= '\u309f' or '\u30a0' <= char <= '\u30ff')
        japanese_ratio = japanese_chars / total_chars
        
        if japanese_ratio > 0.1:
            return "ja"
        
        # 检测韩文字符
        korean_chars = sum(1 for char in text if '\uac00' <= char <= '\ud7af')
        korean_ratio = korean_chars / total_chars
        
        if korean_ratio > 0.1:
            return "ko"
        
        # 默认英文
        return "en"
    
    def get_language_config(self, language_code: str) -> Dict[str, Any]:
        """
        获取语言配置
        
        Args:
            language_code: 语言代码
        
        Returns:
            语言配置字典
        """
        return self.supported_languages.get(language_code, self.supported_languages["zh"])
    
    def translate_text(self, text: str, target_language: str, source_language: str = None) -> str:
        """
        翻译文本
        
        Args:
            text: 源文本
            target_language: 目标语言
            source_language: 源语言（可选）
        
        Returns:
            翻译后的文本
        """
        try:
            # 这里可以集成真正的翻译API
            # 目前使用简单的映射
            
            if source_language is None:
                source_language = self.detect_language(text)
            
            if source_language == target_language:
                return text
            
            # 简单的翻译逻辑（实际应该使用翻译API）
            if target_language in self.translations:
                # 检查是否有预定义的翻译
                for key, value in self.translations[source_language].items():
                    if text.lower() in value.lower():
                        return self.translations[target_language].get(key, text)
            
            # 如果没有找到翻译，返回原文
            return text
            
        except Exception as e:
            logger.error(f"❌ 翻译失败: {e}")
            return text
    
    def get_localized_text(self, key: str, language: str = "zh") -> str:
        """
        获取本地化文本
        
        Args:
            key: 文本键
            language: 语言代码
        
        Returns:
            本地化文本
        """
        try:
            return self.translations.get(language, {}).get(key, key)
        except Exception as e:
            logger.error(f"❌ 获取本地化文本失败: {e}")
            return key
    
    def format_multilingual_prompt(self, base_prompt: str, language: str) -> str:
        """
        格式化多语言提示词
        
        Args:
            base_prompt: 基础提示词
            language: 目标语言
        
        Returns:
            多语言提示词
        """
        try:
            language_config = self.get_language_config(language)
            language_instruction = language_config.get("ollama_prompt", "")
            
            if language_instruction:
                return f"{base_prompt}\n\n{language_instruction}"
            else:
                return base_prompt
                
        except Exception as e:
            logger.error(f"❌ 格式化多语言提示词失败: {e}")
            return base_prompt
    
    def get_voice_settings(self, language: str) -> Dict[str, Any]:
        """
        获取语言对应的语音设置
        
        Args:
            language: 语言代码
        
        Returns:
            语音设置字典
        """
        try:
            language_config = self.get_language_config(language)
            
            return {
                "selected_voice": language_config.get("voice", "zh-cn-xiaoxiao"),
                "language": language,
                "voice_speed": 1.0,
                "voice_pitch": 1.0
            }
            
        except Exception as e:
            logger.error(f"❌ 获取语音设置失败: {e}")
            return {
                "selected_voice": "zh-cn-xiaoxiao",
                "language": "zh",
                "voice_speed": 1.0,
                "voice_pitch": 1.0
            }

class LanguageManager:
    """语言管理器"""
    
    def __init__(self):
        self.multilingual_service = MultilingualService()
        self.user_languages = {}  # 用户语言偏好
        self.session_languages = {}  # 会话语言设置
    
    def set_user_language(self, user_id: str, language: str):
        """设置用户语言偏好"""
        self.user_languages[user_id] = language
        logger.info(f"🌐 用户 {user_id} 语言设置为: {language}")
    
    def get_user_language(self, user_id: str) -> str:
        """获取用户语言偏好"""
        return self.user_languages.get(user_id, "zh")
    
    def set_session_language(self, session_id: str, language: str):
        """设置会话语言"""
        self.session_languages[session_id] = language
        logger.info(f"🌐 会话 {session_id} 语言设置为: {language}")
    
    def get_session_language(self, session_id: str) -> str:
        """获取会话语言"""
        return self.session_languages.get(session_id, "zh")
    
    def auto_detect_and_set_language(self, session_id: str, text: str) -> str:
        """自动检测并设置语言"""
        detected_language = self.multilingual_service.detect_language(text)
        self.set_session_language(session_id, detected_language)
        return detected_language

# 全局实例
multilingual_service = MultilingualService()
language_manager = LanguageManager()
