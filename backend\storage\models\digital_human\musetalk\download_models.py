#!/usr/bin/env python3
"""
MuseTalk 模型下载脚本
下载 MuseTalk 所需的所有模型文件
"""

import os
import sys
import subprocess
import logging
from pathlib import Path
import requests

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def install_dependencies():
    """安装依赖包"""
    try:
        logger.info("安装 MuseTalk 依赖包...")
        
        dependencies = [
            "huggingface_hub[cli]",
            "gdown",
            "torch",
            "torchvision",
            "torchaudio",
            "diffusers",
            "transformers",
            "accelerate",
            "opencv-python",
            "librosa",
            "scipy",
            "scikit-image",
            "pillow",
            "tqdm",
            "omegaconf",
            "yacs"
        ]
        
        for dep in dependencies:
            logger.info(f"安装 {dep}...")
            subprocess.run([sys.executable, "-m", "pip", "install", dep], check=True)
        
        logger.info("✅ 依赖包安装完成")
        return True
        
    except Exception as e:
        logger.error(f"依赖包安装失败: {str(e)}")
        return False

def create_directories():
    """创建必要的目录"""
    try:
        # 使用正确的路径
        base_dir = Path(__file__).parent
        models_dir = base_dir / "models"

        directories = [
            "models/musetalk",
            "models/musetalkV15",
            "models/syncnet",
            "models/dwpose",
            "models/face-parse-bisent",
            "models/sd-vae",
            "models/whisper"
        ]

        for dir_path in directories:
            full_path = base_dir / dir_path
            full_path.mkdir(parents=True, exist_ok=True)
            logger.info(f"创建目录: {full_path}")

        logger.info("✅ 目录创建完成")
        return True

    except Exception as e:
        logger.error(f"目录创建失败: {str(e)}")
        return False

def download_with_huggingface_cli(repo_id, local_dir, include_patterns):
    """使用 huggingface-cli 下载模型"""
    try:
        cmd = [
            "huggingface-cli", "download", repo_id,
            "--local-dir", local_dir
        ]
        
        for pattern in include_patterns:
            cmd.extend(["--include", pattern])
        
        logger.info(f"下载 {repo_id} 到 {local_dir}")
        subprocess.run(cmd, check=True)
        return True
        
    except Exception as e:
        logger.error(f"下载失败 {repo_id}: {str(e)}")
        return False

def download_with_gdown(file_id, output_path):
    """使用 gdown 下载文件"""
    try:
        cmd = ["gdown", "--id", file_id, "-O", output_path]
        logger.info(f"下载文件到 {output_path}")
        subprocess.run(cmd, check=True)
        return True
        
    except Exception as e:
        logger.error(f"gdown 下载失败: {str(e)}")
        return False

def download_with_curl(url, output_path):
    """使用 requests 下载文件"""
    try:
        logger.info(f"下载 {url} 到 {output_path}")
        
        response = requests.get(url, stream=True)
        response.raise_for_status()
        
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        with open(output_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
        
        logger.info(f"下载完成: {output_path}")
        return True
        
    except Exception as e:
        logger.error(f"下载失败 {url}: {str(e)}")
        return False

def download_musetalk_models():
    """下载 MuseTalk 模型"""
    try:
        logger.info("开始下载 MuseTalk 模型...")

        # 使用正确的路径
        base_dir = Path(__file__).parent
        models_dir = base_dir / "models"

        success_count = 0
        total_count = 7

        # 1. MuseTalk V1.0
        if download_with_huggingface_cli(
            "TMElyralab/MuseTalk",
            str(models_dir),
            ["musetalk/musetalk.json", "musetalk/pytorch_model.bin"]
        ):
            success_count += 1

        # 2. MuseTalk V1.5
        if download_with_huggingface_cli(
            "TMElyralab/MuseTalk",
            str(models_dir),
            ["musetalkV15/musetalk.json", "musetalkV15/unet.pth"]
        ):
            success_count += 1

        # 3. SD VAE
        if download_with_huggingface_cli(
            "stabilityai/sd-vae-ft-mse",
            str(models_dir / "sd-vae"),
            ["config.json", "diffusion_pytorch_model.bin"]
        ):
            success_count += 1

        # 4. Whisper
        if download_with_huggingface_cli(
            "openai/whisper-tiny",
            str(models_dir / "whisper"),
            ["config.json", "pytorch_model.bin", "preprocessor_config.json"]
        ):
            success_count += 1

        # 5. DWPose
        if download_with_huggingface_cli(
            "yzd-v/DWPose",
            str(models_dir / "dwpose"),
            ["dw-ll_ucoco_384.pth"]
        ):
            success_count += 1

        # 6. SyncNet
        if download_with_huggingface_cli(
            "ByteDance/LatentSync",
            str(models_dir / "syncnet"),
            ["latentsync_syncnet.pt"]
        ):
            success_count += 1

        # 7. Face Parse Bisent
        face_parse_success = True
        if not download_with_gdown("154JgKpzCPW82qINcVieuPH3fZ2e0P812", str(models_dir / "face-parse-bisent" / "79999_iter.pth")):
            face_parse_success = False

        if not download_with_curl("https://download.pytorch.org/models/resnet18-5c106cde.pth", str(models_dir / "face-parse-bisent" / "resnet18-5c106cde.pth")):
            face_parse_success = False

        if face_parse_success:
            success_count += 1

        logger.info(f"模型下载完成: {success_count}/{total_count} 成功")

        if success_count >= 5:  # 至少成功下载5个模型
            logger.info("✅ MuseTalk 模型下载基本完成！")
            return True
        else:
            logger.warning(f"⚠️ 部分模型下载失败，成功率: {success_count}/{total_count}")
            return False

    except Exception as e:
        logger.error(f"模型下载过程中出现错误: {str(e)}")
        return False

def verify_models():
    """验证模型文件"""
    try:
        # 使用正确的路径
        base_dir = Path(__file__).parent
        models_dir = base_dir / "models"

        required_files = [
            "musetalk/pytorch_model.bin",
            "musetalkV15/unet.pth",
            "sd-vae/diffusion_pytorch_model.bin",
            "whisper/pytorch_model.bin"
        ]

        missing_files = []
        for file_path in required_files:
            full_path = models_dir / file_path
            if not full_path.exists():
                missing_files.append(file_path)
            else:
                size = full_path.stat().st_size
                logger.info(f"模型文件 {file_path}: {size / (1024*1024):.1f} MB")

        if missing_files:
            logger.warning(f"缺少模型文件: {missing_files}")
            return len(missing_files) < len(required_files) / 2  # 超过一半文件存在就算成功
        else:
            logger.info("✅ 所有核心模型文件验证通过！")
            return True

    except Exception as e:
        logger.error(f"模型验证失败: {str(e)}")
        return False

if __name__ == "__main__":
    logger.info("开始 MuseTalk 模型下载...")
    
    try:
        # 1. 安装依赖
        if not install_dependencies():
            sys.exit(1)
        
        # 2. 创建目录
        if not create_directories():
            sys.exit(1)
        
        # 3. 下载模型
        if not download_musetalk_models():
            logger.warning("部分模型下载失败，但继续验证...")
        
        # 4. 验证模型
        if verify_models():
            logger.info("🎉 MuseTalk 模型安装完成！")
            print("\n" + "="*50)
            print("✅ MuseTalk 已成功安装！")
            print("现在可以使用最先进的实时数字人技术了！")
            print("="*50)
            sys.exit(0)
        else:
            logger.error("❌ 模型验证失败！")
            sys.exit(1)
            
    except KeyboardInterrupt:
        logger.info("下载被用户中断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"下载过程中出现错误: {str(e)}")
        sys.exit(1)
