"""
翻译API路由
"""

from fastapi import APIRouter, HTTPException, UploadFile, File, Form, BackgroundTasks
from typing import Optional, Dict, Any
from pydantic import BaseModel
import uuid
from ...core.database import get_db_manager
from ...services.translation_service import TranslationService

router = APIRouter()
translation_service = TranslationService()


class TextTranslationRequest(BaseModel):
    """文本翻译请求模型"""
    text: str
    source_lang: str = "auto"
    target_lang: str = "zh"
    domain: Optional[str] = "general"
    style: Optional[str] = "standard"
    model: Optional[str] = None


class AsyncTranslationRequest(BaseModel):
    """异步翻译请求模型"""
    text: str
    source_lang: str = "auto"
    target_lang: str = "zh"
    domain: Optional[str] = "general"
    style: Optional[str] = "standard"
    model: Optional[str] = None


@router.post("/text")
async def translate_text(request: TextTranslationRequest):
    """文本翻译"""
    try:
        if not request.text.strip():
            return {"success": False, "error": "文本不能为空"}
        
        if len(request.text) > 5000:
            return {"success": False, "error": "文本长度不能超过5000字符"}
        
        # 调用翻译服务
        result = await translation_service.translate_text(
            text=request.text,
            source_lang=request.source_lang,
            target_lang=request.target_lang,
            domain=request.domain,
            style=request.style,
            model=request.model
        )
        
        if result["success"]:
            # 保存翻译历史
            db_manager = get_db_manager()
            db_manager.execute_query(
                """
                INSERT INTO translation_history 
                (original_text, translated_text, source_language, target_language, translation_type)
                VALUES (?, ?, ?, ?, ?)
                """,
                (request.text, result["translated_text"], request.source_lang, request.target_lang, "text")
            )
        
        return result
        
    except Exception as e:
        return {"success": False, "error": str(e)}


@router.post("/document")
async def translate_document(
    file: UploadFile = File(...),
    from_lang: str = Form("auto"),
    to_lang: str = Form("zh-CN")
):
    """文档翻译"""
    try:
        # 检查文件类型
        allowed_types = ["text/plain", "application/pdf", "application/msword", 
                        "application/vnd.openxmlformats-officedocument.wordprocessingml.document"]
        
        if file.content_type not in allowed_types:
            return {"success": False, "error": "不支持的文件类型"}
        
        # 检查文件大小
        if file.size > 10 * 1024 * 1024:  # 10MB
            return {"success": False, "error": "文件大小不能超过10MB"}
        
        # 调用文档翻译服务
        result = await translation_service.translate_document(file, from_lang, to_lang)
        
        return result
        
    except Exception as e:
        return {"success": False, "error": str(e)}


@router.post("/audio")
async def translate_audio(
    file: UploadFile = File(...),
    from_lang: str = Form("auto"),
    to_lang: str = Form("zh-CN")
):
    """音频翻译"""
    try:
        # 检查文件类型
        allowed_types = ["audio/mpeg", "audio/wav", "audio/mp3", "audio/m4a"]
        
        if file.content_type not in allowed_types:
            return {"success": False, "error": "不支持的音频格式"}
        
        # 检查文件大小
        if file.size > 50 * 1024 * 1024:  # 50MB
            return {"success": False, "error": "音频文件大小不能超过50MB"}
        
        # 调用音频翻译服务
        result = await translation_service.translate_audio(file, from_lang, to_lang)
        
        return result
        
    except Exception as e:
        return {"success": False, "error": str(e)}


@router.get("/history")
async def get_translation_history(limit: int = 50):
    """获取翻译历史"""
    try:
        db_manager = get_db_manager()
        history = db_manager.execute_query(
            """
            SELECT * FROM translation_history 
            ORDER BY created_at DESC 
            LIMIT ?
            """,
            (limit,)
        )
        
        return {
            "success": True,
            "history": [
                {
                    "id": item["id"],
                    "original_text": item["original_text"],
                    "translated_text": item["translated_text"],
                    "source_language": item["source_language"],
                    "target_language": item["target_language"],
                    "translation_type": item["translation_type"],
                    "created_at": item["created_at"]
                }
                for item in (history or [])
            ]
        }
        
    except Exception as e:
        return {"success": False, "error": str(e)}


@router.post("/text/async")
async def translate_text_async(request: AsyncTranslationRequest):
    """异步文本翻译"""
    try:
        if not request.text.strip():
            return {"success": False, "error": "文本不能为空"}

        if len(request.text) > 10000:
            return {"success": False, "error": "异步翻译文本长度不能超过10000字符"}

        # 生成任务ID
        import uuid
        task_id = str(uuid.uuid4())

        # 启动异步翻译任务
        result = await translation_service.translate_text_async(
            task_id=task_id,
            text=request.text,
            source_lang=request.source_lang,
            target_lang=request.target_lang,
            domain=request.domain,
            style=request.style,
            model=request.model
        )

        return result
    except Exception as e:
        return {"success": False, "error": str(e)}


@router.get("/text/result/{task_id}")
async def get_translation_result(task_id: str):
    """获取异步翻译结果"""
    try:
        result = await translation_service.get_translation_result(task_id)
        return result
    except Exception as e:
        return {"success": False, "error": str(e)}


@router.get("/text/history")
async def get_translation_history(limit: int = 20):
    """获取翻译历史"""
    try:
        db_manager = get_db_manager()

        # 根据数据库类型使用不同的查询语法
        if hasattr(db_manager, 'use_postgres') and db_manager.use_postgres:
            # PostgreSQL语法
            history = db_manager.execute_query(
                """
                SELECT original_text, translated_text, source_language, target_language,
                       created_at, translation_type
                FROM translation_history
                WHERE translation_type = 'text'
                ORDER BY created_at DESC
                LIMIT %s
                """,
                (limit,)
            )
        else:
            # SQLite语法
            history = db_manager.execute_query(
                """
                SELECT original_text, translated_text, source_language, target_language,
                       created_at, translation_type
                FROM translation_history
                WHERE translation_type = 'text'
                ORDER BY created_at DESC
                LIMIT ?
                """,
                (limit,)
            )

        return {
            "success": True,
            "history": [
                {
                    "id": i + 1,
                    "source_text": row[0],
                    "translated_text": row[1],
                    "source_language": row[2],
                    "target_language": row[3],
                    "created_at": row[4],
                    "timestamp": row[4],
                    "type": row[5]
                }
                for i, row in enumerate(history)
            ]
        }
    except Exception as e:
        return {"success": False, "error": str(e)}


@router.get("/languages")
async def get_supported_languages():
    """获取支持的语言列表"""
    return {
        "success": True,
        "languages": {
            "auto": "自动检测",
            "zh": "中文",
            "en": "English",
            "ja": "日本語",
            "ko": "한국어",
            "fr": "Français",
            "de": "Deutsch",
            "es": "Español",
            "ru": "Русский"
        }
    }
