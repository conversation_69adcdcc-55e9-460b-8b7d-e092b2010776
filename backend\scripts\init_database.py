#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
init_database.py - 数据库初始化脚本

此脚本提供全面的数据库初始化功能，支持:
- 初始化所有表结构
- 重建所有表(警告：会删除所有数据)
- 仅创建缺失的表
- 导出数据库结构
- 添加测试数据
"""

import os
import sys
import argparse
import logging
import json
from datetime import datetime
from sqlalchemy import inspect, text
from sqlalchemy.exc import SQLAlchemyError, ProgrammingError
import time

# 添加项目根目录到系统路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("init_database")

# 导入数据库连接和模型
try:
    from utils.db import engine, Base, get_db, init_db
    from models.agent import (
        Agent, AgentTag, AgentCapability, AgentExample, 
        AgentReview, UserFavorite, AgentSession, AgentMessage
    )
    from models.user import User
    from models.task import Task
except ImportError as e:
    logger.error(f"导入数据库模块时出错: {e}")
    logger.error("请确保您在项目根目录中运行此脚本")
    sys.exit(1)

def get_table_info():
    """获取所有表的信息"""
    table_info = {}
    inspector = inspect(engine)
    
    for table_name in inspector.get_table_names():
        columns = []
        for column in inspector.get_columns(table_name):
            column_info = {
                "name": column["name"],
                "type": str(column["type"]),
                "nullable": column["nullable"],
            }
            if column.get("default") is not None:
                column_info["default"] = str(column["default"])
            columns.append(column_info)
            
        primary_keys = []
        for pk in inspector.get_pk_constraint(table_name).get("constrained_columns", []):
            primary_keys.append(pk)
            
        foreign_keys = []
        for fk in inspector.get_foreign_keys(table_name):
            foreign_keys.append({
                "column": fk["constrained_columns"],
                "references": {
                    "table": fk["referred_table"],
                    "column": fk["referred_columns"]
                }
            })
            
        table_info[table_name] = {
            "columns": columns,
            "primary_keys": primary_keys,
            "foreign_keys": foreign_keys
        }
        
    return table_info

def export_database_structure(output_file=None):
    """导出数据库结构到JSON文件"""
    try:
        table_info = get_table_info()
        
        # 如果没有指定输出文件，则使用当前时间创建一个
        if not output_file:
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            output_file = f"db_structure_{timestamp}.json"
            
        # 如果路径不是绝对路径，则假定是相对于当前工作目录
        if not os.path.isabs(output_file):
            output_file = os.path.join(os.getcwd(), output_file)
            
        # 将表信息写入JSON文件
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(table_info, f, indent=2, ensure_ascii=False)
            
        logger.info(f"数据库结构已导出到: {output_file}")
        return True
    except Exception as e:
        logger.error(f"导出数据库结构时出错: {e}")
        return False

def drop_all_tables(confirm=False):
    """删除所有表"""
    try:
        if not confirm:
            confirm = input("警告：此操作将删除所有表和数据。输入 'DROP ALL TABLES' 确认: ")
            if confirm != "DROP ALL TABLES":
                logger.info("操作已取消")
                return False
        
        logger.info("准备删除所有表...")
        Base.metadata.drop_all(engine)
        logger.info("所有表已删除")
        return True
    except SQLAlchemyError as e:
        logger.error(f"删除表时出错: {e}")
        return False

def create_sample_data():
    """创建示例数据"""
    try:
        # 获取数据库会话
        db = next(get_db())
        
        try:
            # 创建示例用户
            logger.info("创建示例用户...")
            from utils.auth import get_password_hash
            
            # 检查是否已存在admin用户
            admin_exists = db.query(User).filter(User.username == "admin").first()
            if not admin_exists:
                admin_user = User(
                    username="admin",
                    email="<EMAIL>",
                    hashed_password=get_password_hash("admin123"),
                    full_name="管理员",
                    is_active=True,
                    is_admin=True
                )
                db.add(admin_user)
                
            # 检查是否已存在测试用户
            test_exists = db.query(User).filter(User.username == "test").first()
            if not test_exists:
                test_user = User(
                    username="test",
                    email="<EMAIL>",
                    hashed_password=get_password_hash("test123"),
                    full_name="测试用户",
                    is_active=True,
                    is_admin=False
                )
                db.add(test_user)
            
            # 提交用户
            db.commit()
            
            # 刷新以获取ID
            if not admin_exists:
                db.refresh(admin_user)
                admin_id = admin_user.id
            else:
                admin_id = admin_exists.id
                
            if not test_exists:
                db.refresh(test_user)
            
            # 创建示例智能体
            logger.info("创建示例智能体...")
            
            # 检查是否已存在示例智能体
            agent_exists = db.query(Agent).filter(Agent.name == "示例智能体").first()
            if not agent_exists:
                import uuid
                agent_id = str(uuid.uuid4())
                
                # 创建智能体
                sample_agent = Agent(
                    id=agent_id,
                    name="示例智能体",
                    description="这是一个示例智能体，用于测试。",
                    category="助手",
                    author_id=admin_id,
                    rating=4.5,
                    review_count=10,
                    usage_count=100,
                    is_featured=True
                )
                db.add(sample_agent)
                
                # 创建标签
                for tag in ["示例", "测试", "AI助手"]:
                    db.add(AgentTag(agent_id=agent_id, tag=tag))
                
                # 创建能力
                for capability in ["聊天", "回答问题", "提供帮助"]:
                    db.add(AgentCapability(agent_id=agent_id, capability=capability))
                
                # 创建示例
                db.add(AgentExample(agent_id=agent_id, example="用户: 你好\n助手: 你好！我是示例智能体，有什么可以帮助你的吗？"))
                db.add(AgentExample(agent_id=agent_id, example="用户: 你能做什么？\n助手: 我可以回答问题、提供帮助和进行简单对话。"))
            
            # 提交智能体
            db.commit()
            
            logger.info("示例数据创建完成")
            return True
        except Exception as e:
            db.rollback()
            logger.error(f"创建示例数据时出错: {e}")
            return False
        finally:
            db.close()
    except Exception as e:
        logger.error(f"获取数据库会话时出错: {e}")
        return False

def execute_sql_file(file_path):
    """执行SQL文件中的语句"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            sql_script = f.read()
            
        # 连接数据库并执行脚本
        with engine.connect() as connection:
            # 开始事务
            with connection.begin():
                # 将脚本拆分为单独的语句并执行
                statements = sql_script.split(';')
                for statement in statements:
                    # 忽略空语句
                    if statement.strip():
                        connection.execute(text(statement))
                
        logger.info(f"SQL文件 {file_path} 执行成功")
        return True
    except Exception as e:
        logger.error(f"执行SQL文件 {file_path} 时出错: {e}")
        return False

def init_database(args):
    """初始化数据库"""
    try:
        # 如果需要重建，则先删除所有表
        if args.rebuild:
            if not drop_all_tables(args.yes):
                return False
                
        # 初始化数据库结构
        logger.info("初始化数据库结构...")
        init_success = init_db(create_tables=True, force_recreate=args.rebuild, check_tables=True)
        
        if not init_success:
            logger.error("数据库初始化失败")
            return False
            
        # 如果指定了SQL文件，则执行
        if args.sql_file:
            if not execute_sql_file(args.sql_file):
                return False
                
        # 如果需要导出结构，则导出
        if args.export:
            if not export_database_structure(args.output):
                return False
                
        # 如果需要创建示例数据，则创建
        if args.sample_data:
            if not create_sample_data():
                return False
                
        logger.info("数据库初始化完成")
        return True
    except Exception as e:
        logger.error(f"初始化数据库时出错: {e}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='数据库初始化工具')
    parser.add_argument('--rebuild', action='store_true', help='重建所有表(警告：会删除所有数据)')
    parser.add_argument('--export', action='store_true', help='导出数据库结构到JSON文件')
    parser.add_argument('--output', type=str, help='指定导出文件的路径')
    parser.add_argument('--sql-file', type=str, help='执行指定的SQL文件')
    parser.add_argument('--sample-data', action='store_true', help='创建示例数据')
    parser.add_argument('--yes', '-y', action='store_true', help='自动确认所有操作，不提示')
    
    args = parser.parse_args()
    
    # 如果没有提供任何选项，则显示帮助信息
    if not any(vars(args).values()):
        parser.print_help()
        return
        
    try:
        # 初始化数据库
        if init_database(args):
            logger.info("数据库操作成功完成")
        else:
            logger.error("数据库操作失败")
            sys.exit(1)
    except KeyboardInterrupt:
        logger.info("操作已取消")
        sys.exit(1)
    except Exception as e:
        logger.error(f"发生未预期的错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 