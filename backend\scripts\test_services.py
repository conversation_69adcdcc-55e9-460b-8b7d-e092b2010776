#!/usr/bin/env python3
"""
音频翻译服务测试脚本
用于验证ASR、MT和TTS服务是否正常工作
"""

import os
import sys
import time
import logging
import argparse

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.asr_service import ASRService
from services.mt_service import MachineTranslationService
from services.open_source_tts import TTSService
from utils.audio_utils import preprocess_audio, analyze_audio_quality

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_asr_service(audio_path=None):
    """测试ASR服务"""
    logger.info("===== 测试ASR服务 =====")
    start_time = time.time()
    
    # 创建ASR服务
    asr_service = ASRService(model_size="tiny")  # 使用tiny模型以加速测试
    
    # 初始化服务
    if not asr_service.initialize():
        logger.error("ASR服务初始化失败")
        return False
    
    logger.info(f"ASR服务初始化成功，使用模型: {asr_service.model_size}")
    logger.info(f"使用设备: {asr_service.model.device}")
    
    # 如果提供了音频文件，进行识别测试
    if audio_path and os.path.exists(audio_path):
        logger.info(f"尝试识别音频文件: {audio_path}")
        
        # 分析音频质量
        quality_result = analyze_audio_quality(audio_path)
        logger.info(f"音频质量评分: {quality_result.get('quality_score', 0)}/10")
        if not quality_result.get('is_recognizable', False):
            logger.warning("警告: 音频可能无法正常识别")
            
        # 预处理音频
        preprocessed_path, success = preprocess_audio(audio_path)
        if success:
            logger.info(f"音频预处理成功: {preprocessed_path}")
            test_path = preprocessed_path
        else:
            logger.warning("音频预处理失败，使用原始文件")
            test_path = audio_path
            
        # 执行识别
        try:
            result = asr_service.transcribe(test_path)
            
            if result.get("error"):
                logger.error(f"识别错误: {result.get('error')}")
                return False
                
            transcript = result.get("text", "")
            detected_language = result.get("language", "unknown")
            
            if not transcript or transcript.strip() == "":
                logger.error("识别结果为空")
                return False
                
            logger.info(f"识别成功: 检测到语言 {detected_language}")
            logger.info(f"识别文本: {transcript[:100]}{'...' if len(transcript) > 100 else ''}")
            logger.info(f"识别用时: {time.time() - start_time:.2f}秒")
            
            return True
        except Exception as e:
            logger.error(f"识别时出错: {str(e)}")
            return False
    else:
        logger.info("未提供音频文件，仅验证服务可用性")
        logger.info(f"ASR服务验证完成，用时: {time.time() - start_time:.2f}秒")
        return True

def test_mt_service():
    """测试机器翻译服务"""
    logger.info("===== 测试MT服务 =====")
    start_time = time.time()
    
    # 创建MT服务
    mt_service = MachineTranslationService()
    
    # 测试文本
    test_texts = [
        "这是一个简单的测试文本，用于验证翻译功能。",
        "Hello, this is a test message for translation verification."
    ]
    
    # 测试翻译方向
    test_directions = [
        ("zh", "en"),
        ("en", "zh"),
        ("zh", "ja"),
        ("en", "fr")
    ]
    
    # 加载模型
    try:
        logger.info(f"初始化MT服务，加载模型: {mt_service.model_name}")
        mt_service._load_model()
        logger.info(f"模型加载成功，使用设备: {mt_service.device}")
    except Exception as e:
        logger.error(f"MT模型加载失败: {str(e)}")
        return False
    
    # 测试翻译
    success_count = 0
    failure_count = 0
    
    for text in test_texts:
        for src_lang, tgt_lang in test_directions:
            logger.info(f"测试翻译 {src_lang} -> {tgt_lang}: '{text[:30]}...'")
            
            try:
                translated = mt_service.translate(text, src_lang, tgt_lang)
                
                if translated and not translated.startswith("[翻译失败"):
                    logger.info(f"翻译成功: '{translated[:50]}...'")
                    success_count += 1
                else:
                    logger.error(f"翻译失败: {translated}")
                    failure_count += 1
            except Exception as e:
                logger.error(f"翻译出错: {str(e)}")
                failure_count += 1
    
    logger.info(f"MT服务测试完成，成功: {success_count}, 失败: {failure_count}")
    logger.info(f"MT服务验证用时: {time.time() - start_time:.2f}秒")
    
    return failure_count == 0

def test_tts_service():
    """测试TTS服务"""
    logger.info("===== 测试TTS服务 =====")
    start_time = time.time()
    
    # 创建TTS服务
    tts_service = TTSService()
    
    # 测试文本
    test_text = "这是一个测试语音合成的文本，测试中文语音生成。"
    english_test_text = "This is a test text for speech synthesis, testing English voice generation."
    
    # 测试语言
    test_languages = ["zh", "en", "ja"]
    
    # 测试目录
    test_dir = "temp/tts_test"
    os.makedirs(test_dir, exist_ok=True)
    
    success_count = 0
    failure_count = 0
    
    for lang in test_languages:
        text = english_test_text if lang == "en" else test_text
        output_path = os.path.join(test_dir, f"test_tts_{lang}.mp3")
        
        logger.info(f"测试TTS服务，语言: {lang}, 输出: {output_path}")
        
        try:
            # 生成语音文件
            tts_service.text_to_speech(text, lang, output_path)
            
            # 检查文件是否存在且大小不为0
            if os.path.exists(output_path) and os.path.getsize(output_path) > 0:
                logger.info(f"语音生成成功: {output_path}")
                success_count += 1
            else:
                logger.error(f"语音文件生成失败或为空: {output_path}")
                failure_count += 1
        except Exception as e:
            logger.error(f"生成语音时出错: {str(e)}")
            failure_count += 1
    
    # 测试音频合并
    if success_count >= 2:
        try:
            file1 = os.path.join(test_dir, f"test_tts_zh.mp3")
            file2 = os.path.join(test_dir, f"test_tts_en.mp3")
            merged_file = os.path.join(test_dir, "test_merged.mp3")
            
            if os.path.exists(file1) and os.path.exists(file2):
                logger.info(f"测试音频合并: {file1} + {file2} -> {merged_file}")
                merged_path = tts_service.merge_audio_files(file1, file2, merged_file)
                
                if os.path.exists(merged_path) and os.path.getsize(merged_path) > 0:
                    logger.info(f"音频合并成功: {merged_path}")
                else:
                    logger.error(f"音频合并失败或为空: {merged_path}")
        except Exception as e:
            logger.error(f"合并音频时出错: {str(e)}")
    
    logger.info(f"TTS服务测试完成，成功: {success_count}, 失败: {failure_count}")
    logger.info(f"TTS服务验证用时: {time.time() - start_time:.2f}秒")
    
    return failure_count == 0

def run_all_tests(audio_path=None):
    """运行所有测试"""
    logger.info("===== 开始服务测试 =====")
    
    # 确保目录存在
    os.makedirs("temp", exist_ok=True)
    os.makedirs("uploads", exist_ok=True)
    
    # 测试服务
    asr_success = test_asr_service(audio_path)
    mt_success = test_mt_service()
    tts_success = test_tts_service()
    
    # 总结
    logger.info("===== 测试总结 =====")
    logger.info(f"ASR服务: {'✓ 通过' if asr_success else '✗ 失败'}")
    logger.info(f"MT服务: {'✓ 通过' if mt_success else '✗ 失败'}")
    logger.info(f"TTS服务: {'✓ 通过' if tts_success else '✗ 失败'}")
    
    if asr_success and mt_success and tts_success:
        logger.info("✅ 所有服务测试通过，音频翻译功能可以正常工作")
        return True
    else:
        logger.warning("⚠️ 部分服务测试失败，音频翻译功能可能无法正常工作")
        return False

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="测试音频翻译服务")
    parser.add_argument("--audio", "-a", help="用于测试ASR的音频文件路径")
    
    args = parser.parse_args()
    
    success = run_all_tests(args.audio)
    
    sys.exit(0 if success else 1) 