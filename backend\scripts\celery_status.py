#!/usr/bin/env python3
"""
Celery 状态监控脚本
"""
import os
import sys
import time
from pathlib import Path
from dotenv import load_dotenv

# 添加后端路径
backend_dir = Path(__file__).parent.parent
sys.path.append(str(backend_dir))

# 加载环境变量
env_path = backend_dir / ".env"
load_dotenv(env_path)

def check_celery_status():
    """检查 Celery 状态"""
    print("📊 Celery 系统状态监控")
    print("=" * 50)
    
    try:
        from app.core.celery_app import celery_app
        
        # 获取检查器
        inspect = celery_app.control.inspect()
        
        # 1. 检查活跃 Worker
        print("👷 Worker 状态:")
        active_workers = inspect.active()
        if active_workers:
            for worker_name, tasks in active_workers.items():
                print(f"  ✅ {worker_name}: {len(tasks)} 个活跃任务")
        else:
            print("  ❌ 没有活跃的 Worker")
        
        # 2. 检查已注册任务
        print(f"\n📋 已注册任务:")
        registered = inspect.registered()
        if registered:
            for worker_name, tasks in registered.items():
                print(f"  📍 {worker_name}: {len(tasks)} 个任务")
                for task in sorted(tasks)[:5]:  # 显示前5个
                    if not task.startswith('celery.'):
                        print(f"    - {task}")
                if len(tasks) > 5:
                    print(f"    ... 还有 {len(tasks) - 5} 个任务")
        
        # 3. 检查队列状态
        print(f"\n🚛 队列状态:")
        active_queues = inspect.active_queues()
        if active_queues:
            for worker_name, queues in active_queues.items():
                print(f"  📍 {worker_name}:")
                for queue in queues:
                    print(f"    - {queue['name']}")
        
        # 4. 检查统计信息
        print(f"\n📈 统计信息:")
        stats = inspect.stats()
        if stats:
            for worker_name, stat in stats.items():
                print(f"  📍 {worker_name}:")
                print(f"    总任务数: {stat.get('total', 'Unknown')}")
                print(f"    进程池: {stat.get('pool', {}).get('max-concurrency', 'Unknown')}")
                print(f"    负载: {stat.get('rusage', {}).get('utime', 'Unknown')}")
        
        return bool(active_workers)
        
    except Exception as e:
        print(f"❌ 状态检查失败: {e}")
        return False

def send_test_tasks():
    """发送测试任务"""
    print(f"\n🧪 发送测试任务")
    print("=" * 50)
    
    try:
        from app.tasks.simple_test_task import simple_add, simple_hello
        
        # 发送多个测试任务
        tasks = []
        
        print("📤 发送任务...")
        for i in range(3):
            task1 = simple_add.delay(i * 10, i * 5)
            task2 = simple_hello.delay(f"Test-{i}")
            tasks.extend([task1, task2])
            print(f"  发送任务 {i+1}: {task1.id}, {task2.id}")
        
        print(f"\n⏳ 等待任务完成...")
        results = []
        for task in tasks:
            try:
                result = task.get(timeout=10)
                results.append(result)
                print(f"  ✅ {task.id}: {result}")
            except Exception as e:
                print(f"  ❌ {task.id}: {e}")
        
        print(f"\n📊 测试结果: {len(results)}/{len(tasks)} 任务成功")
        return len(results) == len(tasks)
        
    except Exception as e:
        print(f"❌ 测试任务失败: {e}")
        return False

def monitor_tasks():
    """监控任务执行"""
    print(f"\n👀 实时任务监控 (按 Ctrl+C 停止)")
    print("=" * 50)
    
    try:
        from app.core.celery_app import celery_app
        
        inspect = celery_app.control.inspect()
        
        while True:
            # 获取活跃任务
            active = inspect.active()
            if active:
                total_tasks = sum(len(tasks) for tasks in active.values())
                print(f"📊 {time.strftime('%H:%M:%S')} - 活跃任务: {total_tasks}")
                
                for worker_name, tasks in active.items():
                    if tasks:
                        print(f"  {worker_name}: {len(tasks)} 任务")
                        for task in tasks[:2]:  # 显示前2个任务
                            print(f"    - {task['name']} ({task['id'][:8]}...)")
            else:
                print(f"📊 {time.strftime('%H:%M:%S')} - 无活跃任务")
            
            time.sleep(2)
            
    except KeyboardInterrupt:
        print(f"\n⏹️ 监控已停止")
    except Exception as e:
        print(f"❌ 监控失败: {e}")

def main():
    """主函数"""
    print("🔍 Celery 状态监控工具")
    print("=" * 60)
    
    while True:
        print(f"\n请选择操作:")
        print("1. 检查 Celery 状态")
        print("2. 发送测试任务")
        print("3. 实时监控任务")
        print("4. 退出")
        
        choice = input("\n请输入选择 (1-4): ").strip()
        
        if choice == "1":
            check_celery_status()
        elif choice == "2":
            send_test_tasks()
        elif choice == "3":
            monitor_tasks()
        elif choice == "4":
            print("👋 再见！")
            break
        else:
            print("❌ 无效选择，请重试")
        
        if choice != "4":
            input("\n按 Enter 键继续...")

if __name__ == "__main__":
    main()
