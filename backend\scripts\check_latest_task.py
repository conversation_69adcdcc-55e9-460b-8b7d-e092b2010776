#!/usr/bin/env python3
"""
检查最新的任务
"""
import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# 添加后端路径
backend_dir = Path(__file__).parent.parent
sys.path.append(str(backend_dir))

# 加载环境变量
env_path = backend_dir / ".env"
load_dotenv(env_path)

def check_latest_tasks():
    """检查最新的任务"""
    try:
        from app.core.task_manager import task_manager
        
        # 获取最新的任务
        user_tasks = task_manager.get_user_tasks("demo-user", limit=5)
        
        print("📋 最新的任务:")
        print("=" * 50)
        
        for i, task in enumerate(user_tasks, 1):
            print(f"\n{i}. 任务 {task['task_id'][:8]}...")
            print(f"   类型: {task['task_type']} / {task.get('task_subtype', 'N/A')}")
            print(f"   状态: {task['status']} ({task['progress']}%)")
            print(f"   标题: {task.get('title', 'N/A')}")
            print(f"   消息: {task.get('message', 'N/A')}")
            print(f"   创建时间: {task['created_at']}")
            
            if task.get('input_params'):
                print(f"   输入参数: {task['input_params']}")
            
            if task.get('output_data'):
                print(f"   输出数据: {task['output_data']}")
        
        if not user_tasks:
            print("❌ 没有找到任务")
        
        return len(user_tasks)
        
    except Exception as e:
        print(f"❌ 检查任务失败: {e}")
        import traceback
        traceback.print_exc()
        return 0

if __name__ == "__main__":
    check_latest_tasks()
