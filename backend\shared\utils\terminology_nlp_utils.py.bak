import re
import logging
import nltk
from typing import List, Dict, Tuple, Optional, Union
from nltk.tokenize import word_tokenize, sent_tokenize
from nltk.tag import pos_tag
from nltk.stem import WordNetLemmatizer
from nltk.corpus import stopwords
import spacy
import jieba
import jieba.posseg as pseg
from langdetect import detect, LangDetectException

# 设置日志
logger = logging.getLogger("terminology_nlp_utils")

# 确保必要的NLTK数据已下载
try:
    nltk.data.find('tokenizers/punkt')
except LookupError:
    nltk.download('punkt', quiet=True)
try:
    nltk.data.find('taggers/averaged_perceptron_tagger')
except LookupError:
    nltk.download('averaged_perceptron_tagger', quiet=True)
try:
    nltk.data.find('corpora/stopwords')
except LookupError:
    nltk.download('stopwords', quiet=True)
try:
    nltk.data.find('corpora/wordnet')
except LookupError:
    nltk.download('wordnet', quiet=True)

# 尝试加载spaCy模型
try:
    # 英文模型
    nlp_en = spacy.load("en_core_web_sm")
except OSError:
    logger.warning("SpaCy en_core_web_sm model not found. Some features may be limited.")
    nlp_en = None

# 语言检测函数
def detect_language(text: str) -> str:
    """
    检测文本语言
    
    Args:
        text: 要检测的文本
        
    Returns:
        语言代码 (en, zh, fr, de, ru, etc.)
    """
    try:
        return detect(text)
    except LangDetectException:
        # 如果检测失败，返回英语作为默认值
        logger.warning(f"Language detection failed for text: {text[:50]}...")
        return "en"

# 分词函数
def tokenize_text(text: str, language: str = None) -> List[str]:
    """
    根据语言对文本进行分词
    
    Args:
        text: 要分词的文本
        language: 语言代码，如果为None则自动检测
        
    Returns:
        分词列表
    """
    if not text:
        return []
    
    if language is None:
        language = detect_language(text)
    
    if language == "zh":
        # 中文分词
        return list(jieba.cut(text))
    else:
        # 其他语言使用NLTK
        return word_tokenize(text)

# 词性标注函数
def pos_tag_text(tokens: List[str], language: str = None) -> List[Tuple[str, str]]:
    """
    对分词结果进行词性标注
    
    Args:
        tokens: 分词列表
        language: 语言代码，如果为None则自动检测
        
    Returns:
        (词, 词性)元组列表
    """
    if not tokens:
        return []
    
    if language is None:
        # 用第一个词尝试检测语言
        if tokens:
            language = detect_language(tokens[0])
        else:
            language = "en"
    
    if language == "zh":
        # 中文词性标注
        text = "".join(tokens)
        return [(word, tag) for word, tag in pseg.cut(text)]
    else:
        # 英文和其他语言使用NLTK
        return pos_tag(tokens)

# 术语提取函数
def extract_terms(text: str, language: str = None, domain: str = None) -> List[Dict]:
    """
    从文本中提取术语
    
    Args:
        text: 源文本
        language: 语言代码，如果为None则自动检测
        domain: 领域，用于调整术语提取规则
        
    Returns:
        术语列表，每个术语为包含term、pos、confidence等字段的字典
    """
    if not text:
        return []
    
    if language is None:
        language = detect_language(text)
    
    results = []
    
    # 分句
    sentences = sent_tokenize(text) if language != "zh" else re.split(r'[。？！；.?!;]', text)
    
    # 使用spaCy进行英文术语提取
    if language == "en" and nlp_en:
        for sent in sentences:
            if not sent.strip():
                continue
                
            doc = nlp_en(sent)
            
            # 提取名词短语
            for chunk in doc.noun_chunks:
                # 基本过滤
                if len(chunk.text) < 2 or chunk.text.lower() in stopwords.words('english'):
                    continue
                
                # 计算置信度（简化版）
                confidence = 0.5
                
                # 特殊领域的术语可能有更高的置信度
                if domain:
                    # 这里可以加入特定领域的术语识别规则
                    domain_terms = {
                        "tech": ["algorithm", "api", "framework", "interface", "database"],
                        "medical": ["diagnosis", "treatment", "symptom", "patient", "disease"],
                        "legal": ["plaintiff", "defendant", "court", "attorney", "judge"]
                    }
                    
                    if domain in domain_terms and any(term in chunk.text.lower() for term in domain_terms[domain]):
                        confidence += 0.2
                
                # 基于词性的置信度调整
                if chunk.root.pos_ == "NOUN":
                    confidence += 0.2
                
                # 根据长度调整置信度
                words = chunk.text.split()
                if len(words) >= 2 and len(words) <= 4:
                    confidence += 0.1  # 多词术语通常更可靠
                
                results.append({
                    "term": chunk.text,
                    "pos": chunk.root.pos_,
                    "confidence": min(confidence, 0.95)  # 设置最高置信度上限
                })
    
    # 中文术语提取
    elif language == "zh":
        for sent in sentences:
            if not sent.strip():
                continue
                
            # 使用jieba进行词性标注
            words_pos = pseg.cut(sent)
            
            # 提取名词和名词短语
            i = 0
            words_pos_list = list(words_pos)
            while i < len(words_pos_list):
                word, tag = words_pos_list[i]
                
                # 基本过滤
                if len(word) < 2:
                    i += 1
                    continue
                
                # 初始置信度
                confidence = 0.5
                
                # 根据词性调整置信度
                if tag.startswith('n'):  # 名词
                    confidence += 0.2
                
                # 如果是专有名词，提高置信度
                if tag == 'nr' or tag == 'ns' or tag == 'nt' or tag == 'nz':
                    confidence += 0.1
                
                # 检查是否是多字组合的专业术语
                if len(word) >= 4:
                    confidence += 0.1
                
                # 添加到结果
                results.append({
                    "term": word,
                    "pos": tag,
                    "confidence": min(confidence, 0.95)
                })
                
                i += 1
    
    # 其他语言使用基本的NLTK处理
    else:
        for sent in sentences:
            if not sent.strip():
                continue
                
            tokens = word_tokenize(sent)
            tagged = pos_tag(tokens)
            
            # 提取名词和名词短语
            i = 0
            while i < len(tagged):
                word, tag = tagged[i]
                
                # 基本过滤
                if len(word) < 2 or word.lower() in stopwords.words('english'):
                    i += 1
                    continue
                
                # 初始置信度
                confidence = 0.5
                
                # 根据词性调整置信度
                if tag.startswith('NN'):  # 名词
                    confidence += 0.2
                    
                    # 检查是否是多词术语
                    term = word
                    j = i + 1
                    while j < len(tagged) and (tagged[j][1].startswith('JJ') or tagged[j][1].startswith('NN')):
                        term += " " + tagged[j][0]
                        j += 1
                    
                    if j > i + 1:
                        confidence += 0.1  # 多词术语通常更可靠
                        i = j
                    else:
                        i += 1
                    
                    # 添加到结果
                    results.append({
                        "term": term,
                        "pos": "NOUN",
                        "confidence": min(confidence, 0.95)
                    })
                else:
                    i += 1
    
    # 去重并排序
    unique_results = {}
    for item in results:
        term = item["term"].lower()
        if term not in unique_results or item["confidence"] > unique_results[term]["confidence"]:
            unique_results[term] = item
    
    # 按置信度排序
    sorted_results = sorted(unique_results.values(), key=lambda x: x["confidence"], reverse=True)
    
    return sorted_results

# 停用词过滤函数
def filter_stopwords(tokens: List[str], language: str = "en") -> List[str]:
    """
    过滤停用词
    
    Args:
        tokens: 分词列表
        language: 语言代码
        
    Returns:
        过滤后的分词列表
    """
    if language == "zh":
        # 中文停用词需要另外加载
        zh_stopwords = set(["的", "了", "和", "是", "在", "我", "有", "他", "这", "为"])
        return [token for token in tokens if token not in zh_stopwords]
    else:
        try:
            stop_words = set(stopwords.words(language))
            return [token for token in tokens if token.lower() not in stop_words]
        except:
            # 如果没有对应语言的停用词，使用英语停用词
            stop_words = set(stopwords.words('english'))
            return [token for token in tokens if token.lower() not in stop_words]

# 文本清理函数
def clean_text(text: str) -> str:
    """
    清理文本，去除无用字符
    
    Args:
        text: 输入文本
        
    Returns:
        清理后的文本
    """
    if not text:
        return ""
    
    # 去除多余空白
    text = re.sub(r'\s+', ' ', text)
    
    # 去除URL
    text = re.sub(r'https?://\S+|www\.\S+', '', text)
    
    # 去除HTML标签
    text = re.sub(r'<.*?>', '', text)
    
    # 去除特殊字符
    text = re.sub(r'[^\w\s\.\,\?\!\:\;\-\"\']', ' ', text)
    
    # 去除数字（可选，视情况而定）
    # text = re.sub(r'\d+', '', text)
    
    return text.strip()

# 语句对齐函数（用于术语在上下文中的定位）
def align_sentences(source_text: str, target_text: str) -> List[Tuple[str, str]]:
    """
    对齐原文和译文的句子
    
    Args:
        source_text: 源语言文本
        target_text: 目标语言文本
        
    Returns:
        (源句子, 目标句子)元组列表
    """
    # 简单实现：按句子分割，假设源文和译文的句子数量相同
    source_lang = detect_language(source_text)
    target_lang = detect_language(target_text)
    
    source_sents = sent_tokenize(source_text) if source_lang != "zh" else re.split(r'[。？！；.?!;]', source_text)
    target_sents = sent_tokenize(target_text) if target_lang != "zh" else re.split(r'[。？！；.?!;]', target_text)
    
    # 去除空句子
    source_sents = [s.strip() for s in source_sents if s.strip()]
    target_sents = [s.strip() for s in target_sents if s.strip()]
    
    # 对齐
    min_len = min(len(source_sents), len(target_sents))
    return [(source_sents[i], target_sents[i]) for i in range(min_len)]

# 模拟翻译函数（实际项目中应该调用专业的翻译API或模型）
def translate_term(term: str, source_lang: str, target_lang: str) -> Dict:
    """
    模拟翻译术语
    
    Args:
        term: 源术语
        source_lang: 源语言代码
        target_lang: 目标语言代码
        
    Returns:
        包含翻译结果和置信度的字典
    """
    # 这里应该集成实际的翻译服务
    # 以下是模拟结果
    common_translations = {
        # 英中术语对照
        ("en", "zh", "artificial intelligence"): "人工智能",
        ("en", "zh", "machine learning"): "机器学习",
        ("en", "zh", "deep learning"): "深度学习",
        ("en", "zh", "neural network"): "神经网络",
        ("en", "zh", "big data"): "大数据",
        
        # 中英术语对照
        ("zh", "en", "人工智能"): "artificial intelligence",
        ("zh", "en", "机器学习"): "machine learning",
        ("zh", "en", "深度学习"): "deep learning",
        ("zh", "en", "神经网络"): "neural network",
        ("zh", "en", "大数据"): "big data",
    }
    
    # 检查常见术语对照表
    key = (source_lang, target_lang, term.lower())
    if key in common_translations:
        return {
            "translated_term": common_translations[key],
            "confidence": 0.95
        }
    
    # 模拟翻译结果（实际项目应使用专业翻译API）
    if source_lang == "en" and target_lang == "zh":
        # 简单的英译中模拟
        return {
            "translated_term": f"[翻译:{term}]",
            "confidence": 0.7
        }
    elif source_lang == "zh" and target_lang == "en":
        # 简单的中译英模拟
        return {
            "translated_term": f"[Translated:{term}]",
            "confidence": 0.7
        }
    else:
        # 其他语言对的模拟
        return {
            "translated_term": f"[{target_lang}:{term}]",
            "confidence": 0.6
        } 