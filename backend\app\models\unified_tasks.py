"""
统一任务管理模型
"""
from sqlalchemy import Column, String, Integer, Text, DateTime, JSON, Float, Boolean
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime
import uuid

Base = declarative_base()

class UnifiedTask(Base):
    """统一任务表 - 管理所有类型的异步任务"""
    __tablename__ = "unified_tasks"
    
    # 基础字段
    id = Column(Integer, primary_key=True, autoincrement=True)
    task_id = Column(String(255), unique=True, nullable=False, index=True)  # 业务任务ID
    celery_task_id = Column(String(255), nullable=True, index=True)  # Celery任务ID
    
    # 任务分类
    task_type = Column(String(50), nullable=False, index=True)  # 任务类型：video_generation, digital_human, translation, etc.
    task_subtype = Column(String(50), nullable=True, index=True)  # 子类型：text_to_video, image_to_video, etc.
    
    # 用户信息
    user_id = Column(String(255), nullable=False, index=True)
    
    # 任务状态
    status = Column(String(20), default='pending', index=True)  # pending, processing, completed, failed, cancelled
    progress = Column(Integer, default=0)  # 0-100
    
    # 任务信息
    title = Column(String(500), nullable=True)  # 任务标题
    description = Column(Text, nullable=True)  # 任务描述
    message = Column(Text, nullable=True)  # 当前状态消息
    error_message = Column(Text, nullable=True)  # 错误信息
    
    # 任务参数和结果
    input_params = Column(JSON, nullable=True)  # 输入参数
    output_data = Column(JSON, nullable=True)  # 输出数据
    
    # 文件路径
    input_files = Column(JSON, nullable=True)  # 输入文件路径列表
    output_files = Column(JSON, nullable=True)  # 输出文件路径列表
    
    # 性能指标
    estimated_duration = Column(Integer, nullable=True)  # 预估耗时（秒）
    actual_duration = Column(Integer, nullable=True)  # 实际耗时（秒）
    
    # 优先级和队列
    priority = Column(Integer, default=5)  # 1-10，数字越大优先级越高
    queue_name = Column(String(50), nullable=True)  # 队列名称
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow, index=True)
    started_at = Column(DateTime, nullable=True)
    completed_at = Column(DateTime, nullable=True)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 重试信息
    retry_count = Column(Integer, default=0)
    max_retries = Column(Integer, default=3)
    
    # 标签和元数据
    tags = Column(JSON, nullable=True)  # 标签列表
    extra_data = Column(JSON, nullable=True)  # 额外元数据
    
    def __repr__(self):
        return f"<UnifiedTask(task_id='{self.task_id}', type='{self.task_type}', status='{self.status}')>"
    
    def to_dict(self):
        """转换为字典"""
        return {
            'task_id': self.task_id,
            'celery_task_id': self.celery_task_id,
            'task_type': self.task_type,
            'task_subtype': self.task_subtype,
            'user_id': self.user_id,
            'status': self.status,
            'progress': self.progress,
            'title': self.title,
            'description': self.description,
            'message': self.message,
            'error_message': self.error_message,
            'input_params': self.input_params,
            'output_data': self.output_data,
            'input_files': self.input_files,
            'output_files': self.output_files,
            'estimated_duration': self.estimated_duration,
            'actual_duration': self.actual_duration,
            'priority': self.priority,
            'queue_name': self.queue_name,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'started_at': self.started_at.isoformat() if self.started_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'retry_count': self.retry_count,
            'max_retries': self.max_retries,
            'tags': self.tags,
            'extra_data': self.extra_data
        }
    
    @classmethod
    def create_task(cls, task_type: str, task_subtype: str = None, user_id: str = "demo-user", 
                   title: str = None, description: str = None, input_params: dict = None,
                   priority: int = 5, estimated_duration: int = None, tags: list = None):
        """创建新任务"""
        task_id = str(uuid.uuid4())
        
        return cls(
            task_id=task_id,
            task_type=task_type,
            task_subtype=task_subtype,
            user_id=user_id,
            title=title,
            description=description,
            input_params=input_params,
            priority=priority,
            estimated_duration=estimated_duration,
            tags=tags
        )

# 任务类型常量
class TaskTypes:
    """任务类型常量"""
    VIDEO_GENERATION = "video_generation"
    DIGITAL_HUMAN = "digital_human"
    TRANSLATION = "translation"
    VOICE_PROCESSING = "voice_processing"
    IMAGE_PROCESSING = "image_processing"
    
    # 子类型
    class VideoGeneration:
        TEXT_TO_VIDEO = "text_to_video"
        IMAGE_TO_VIDEO = "image_to_video"
        VIDEO_ENHANCEMENT = "video_enhancement"
    
    class DigitalHuman:
        AVATAR_GENERATION = "avatar_generation"
        TALKING_VIDEO = "talking_video"
        EMOTION_SYNTHESIS = "emotion_synthesis"
    
    class Translation:
        TEXT_TRANSLATION = "text_translation"
        DOCUMENT_TRANSLATION = "document_translation"
        BATCH_TRANSLATION = "batch_translation"
    
    class VoiceProcessing:
        TTS = "text_to_speech"
        VOICE_CLONING = "voice_cloning"
        AUDIO_ENHANCEMENT = "audio_enhancement"
    
    class ImageProcessing:
        STYLE_TRANSFER = "style_transfer"
        UPSCALING = "upscaling"
        BACKGROUND_REMOVAL = "background_removal"

# 任务状态常量
class TaskStatus:
    """任务状态常量"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    RETRYING = "retrying"
