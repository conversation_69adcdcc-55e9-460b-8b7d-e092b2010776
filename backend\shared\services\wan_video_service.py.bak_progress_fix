import os
import torch
import logging
import tempfile
import uuid
import time
import numpy as np
import asyncio
import sys
import platform
import shutil
import subprocess
try:
    import cv2
except:
    pass
import traceback
from config.models import WAN_CONFIG  # 导入WAN配置
import json
import glob
import random
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Union, Any
from PIL import Image
from services.progress_updater import ProgressUpdater
import types

# 加载环境变量
try:
    from dotenv import load_dotenv
    load_dotenv()
except:
    pass

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 获取标准的应用数据目录
def get_app_data_dir():
    """获取跨平台的应用数据目录"""
    app_name = "WanAI"
    if platform.system() == "Windows":
        # Windows: AppData/Local
        return os.path.join(os.environ.get("LOCALAPPDATA", os.path.expanduser("~/AppData/Local")), app_name)
    elif platform.system() == "Darwin":
        # macOS: ~/Library/Application Support
        return os.path.join(os.path.expanduser("~/Library/Application Support"), app_name)
    else:
        # Linux/Unix: ~/.local/share
        return os.path.join(os.path.expanduser("~/.local/share"), app_name)

def check_directory_permissions(dir_path: str) -> Tuple[bool, bool, bool, str]:
    """
    检查目录的权限状态
    
    Args:
        dir_path: 要检查的目录路径
        
    Returns:
        Tuple:
            - 目录是否存在
            - 是否有读权限
            - 是否有写权限
            - 诊断信息
    """
    exists = os.path.exists(dir_path)
    readable = False
    writable = False
    msg = ""
    
    if not exists:
        msg = f"目录不存在: {dir_path}"
        return exists, readable, writable, msg
    
    if not os.path.isdir(dir_path):
        msg = f"路径不是目录: {dir_path}"
        return exists, readable, writable, msg
    
    readable = os.access(dir_path, os.R_OK)
    writable = os.access(dir_path, os.W_OK)
    
    if readable and writable:
        msg = f"目录存在且拥有读写权限: {dir_path}"
    elif readable and not writable:
        msg = f"目录存在但只读，无写入权限: {dir_path}"
    elif not readable and writable:
        msg = f"目录存在但只写，无读取权限: {dir_path}"
    else:
        msg = f"目录存在但无读写权限: {dir_path}"
    
    return exists, readable, writable, msg

# 自动修复模型目录结构
def auto_fix_model_structure(model_dir: str) -> bool:
    """
    自动修复模型目录结构
    
    Args:
        model_dir: 模型目录路径
        
    Returns:
        bool: 是否成功修复
    """
    logger.info(f"尝试自动修复模型目录结构: {model_dir}")
    
    try:
        # 正确导入shutil避免UnboundLocalError
        import shutil
        import json
        # 删除这里的os导入，因为它在全局范围内已经导入
        # import os
        
        # 检查目录是否存在
        if not os.path.exists(model_dir):
            logger.warning(f"模型目录不存在: {model_dir}")
            try:
                os.makedirs(model_dir, exist_ok=True)
                logger.info(f"创建模型目录: {model_dir}")
            except Exception as e:
                logger.error(f"创建模型目录失败: {e}")
                return False
        
        # 检查磁盘空间是否足够 - 视频模型通常较大，至少需要2GB空间
        if not check_disk_space(model_dir, required_mb=2000):
            logger.error(f"目标磁盘空间不足，无法修复模型目录结构")
            return False
        
        # 检查是否为Wan2.1模型 - 针对Wan2.1进行特殊处理
        is_wan_model = "Wan2.1" in model_dir
        
        # 确定所需子目录
        required_dirs = ["vae", "unet", "scheduler"]
        
        # 如果是Wan2.1模型，则检查是否已经有官方目录结构
        if is_wan_model:
            wan_structure_files = [
                "Wan2.1_VAE.pth",       # 旧版本VAE文件
                "config.json",           # 配置文件
                "tokenizer_config.json", # 分词器配置
            ]
            
            # 检查是否已经有Wan2.1特定文件
            has_wan_structure = any(os.path.exists(os.path.join(model_dir, f)) for f in wan_structure_files)
            
            # 如果有Wan2.1特定结构，则使用不同的子目录结构
            if has_wan_structure:
                logger.info("检测到Wan2.1特定结构，使用自定义目录检查")
                required_dirs = ["vae", "unet", "text_encoder"]
        
        # 创建缺失的目录
        missing_dirs = []
        for dir_name in required_dirs:
            dir_path = os.path.join(model_dir, dir_name)
            if not os.path.exists(dir_path):
                missing_dirs.append(dir_name)
                try:
                    os.makedirs(dir_path, exist_ok=True)
                    logger.info(f"创建子目录: {dir_path}")
                except Exception as e:
                    logger.error(f"创建子目录失败: {dir_path} - {e}")
                    return False
        
        # 如果存在缺失的目录
        if missing_dirs:
            logger.warning(f"检测到缺失的子目录: {', '.join(missing_dirs)}")
            
            # 尝试检查顶级目录下是否有可能的模型文件需要移动
            for filename in os.listdir(model_dir):
                file_path = os.path.join(model_dir, filename)
                if os.path.isfile(file_path):
                    # 尝试根据文件名推断目标目录
                    target_dir = None
                    
                    # 处理VAE相关文件
                    if "vae" in filename.lower():
                        target_dir = "vae"
                    # 处理UNet相关文件
                    elif "unet" in filename.lower():
                        target_dir = "unet"
                    # 处理调度器相关文件
                    elif "scheduler" in filename.lower():
                        target_dir = "scheduler"
                    # 处理文本编码器相关文件
                    elif "encoder" in filename.lower() or "clip" in filename.lower():
                        target_dir = "text_encoder"
                    
                    # 如果确定了目标目录，且该目录存在于所需目录中
                    if target_dir and target_dir in required_dirs:
                        target_path = os.path.join(model_dir, target_dir, filename)
                        try:
                            # 移动文件到对应目录
                            shutil.move(file_path, target_path)
                            logger.info(f"移动文件: {filename} -> {target_dir}/")
                        except Exception as e:
                            logger.error(f"移动文件失败: {filename} - {e}")
        
        # 创建基本的tokenizer目录
        tokenizer_dir = os.path.join(model_dir, "tokenizer")
        if not os.path.exists(tokenizer_dir):
            try:
                os.makedirs(tokenizer_dir, exist_ok=True)
                logger.info(f"创建tokenizer目录: {tokenizer_dir}")
            except Exception as e:
                logger.error(f"创建tokenizer目录失败: {e}")
        
        # 检查UMT5-XXL目录中的tokenizer文件
        umt5_dir = os.path.join(model_dir, "google", "umt5-xxl")
        if os.path.exists(umt5_dir) and os.path.isdir(umt5_dir):
            logger.info(f"检测到UMT5-XXL目录: {umt5_dir}")
            # 检查T5风格的tokenizer文件
            t5_files = [
                "tokenizer_config.json", 
                "spiece.model",
                "tokenizer.json",
                "special_tokens_map.json"
            ]
            
            # 检查哪些文件存在
            for file_name in t5_files:
                src_path = os.path.join(umt5_dir, file_name)
                if os.path.exists(src_path):
                    # 确保tokenizer目录存在
                    if not os.path.exists(tokenizer_dir):
                        os.makedirs(tokenizer_dir, exist_ok=True)
                    
                    # 复制到tokenizer目录
                    tokenizer_dest = os.path.join(tokenizer_dir, file_name)
                    # 也复制到根目录
                    root_dest = os.path.join(model_dir, file_name)
                    
                    if not os.path.exists(tokenizer_dest) or not os.path.exists(root_dest):
                        try:
                            # 如果目标文件不存在，则复制
                            if not os.path.exists(tokenizer_dest):
                                shutil.copy2(src_path, tokenizer_dest)
                                logger.info(f"复制tokenizer文件: {src_path} -> {tokenizer_dest}")
                            
                            if not os.path.exists(root_dest):
                                shutil.copy2(src_path, root_dest)
                                logger.info(f"复制tokenizer文件: {src_path} -> {root_dest}")
                        except Exception as e:
                            logger.error(f"复制tokenizer文件失败: {src_path} - {e}")
        
        # 检查并创建基本的tokenizer文件 (CLIP风格)
        token_config_file = os.path.join(model_dir, "tokenizer_config.json")
        token_config_dest = os.path.join(tokenizer_dir, "config.json")
        if os.path.exists(token_config_file) and not os.path.exists(token_config_dest):
            try:
                shutil.copy2(token_config_file, token_config_dest)
                logger.info(f"复制tokenizer配置: {token_config_file} -> {token_config_dest}")
            except Exception as e:
                logger.error(f"复制tokenizer配置失败: {e}")
        
        vocab_file = os.path.join(model_dir, "vocab.json")
        vocab_dest = os.path.join(tokenizer_dir, "vocab.json") 
        if os.path.exists(vocab_file) and not os.path.exists(vocab_dest):
            try:
                shutil.copy2(vocab_file, vocab_dest)
                logger.info(f"复制vocab文件: {vocab_file} -> {vocab_dest}")
            except Exception as e:
                logger.error(f"复制vocab文件失败: {e}")
        
        merges_file = os.path.join(model_dir, "merges.txt")
        merges_dest = os.path.join(tokenizer_dir, "merges.txt")
        if os.path.exists(merges_file) and not os.path.exists(merges_dest):
            try:
                shutil.copy2(merges_file, merges_dest)
                logger.info(f"复制merges文件: {merges_file} -> {merges_dest}")
            except Exception as e:
                logger.error(f"复制merges文件失败: {e}")
        
        # 为Wan2.1模型创建特殊处理
        if is_wan_model:
            # 检查是否需要重命名/重组织文件
            vae_dir = os.path.join(model_dir, "vae")
            vae_model_file = os.path.join(vae_dir, "diffusion_pytorch_model.bin")
            vae_safetensors_file = os.path.join(vae_dir, "diffusion_pytorch_model.safetensors")
            
            if not os.path.exists(vae_model_file) and not os.path.exists(vae_safetensors_file):
                # 检查是否有顶级的VAE文件，如Wan2.1_VAE.pth
                wan_vae_path = os.path.join(model_dir, "Wan2.1_VAE.pth")
                if os.path.exists(wan_vae_path):
                    # 如果存在，复制到vae目录
                    try:
                        shutil.copy2(wan_vae_path, vae_model_file)
                        logger.info(f"复制VAE文件: {wan_vae_path} -> {vae_model_file}")
                    except Exception as e:
                        logger.error(f"复制VAE文件失败: {e}")
            
            # 检查UNet模型文件是否存在
            unet_dir = os.path.join(model_dir, "unet")
            unet_model_file = os.path.join(unet_dir, "diffusion_pytorch_model.bin")
            unet_safetensors_file = os.path.join(unet_dir, "diffusion_pytorch_model.safetensors")
            
            # 如果同时缺少bin和safetensors文件，但顶级目录有safetensors文件，复制到unet目录
            if (not os.path.exists(unet_model_file) or os.path.getsize(unet_model_file) == 0) and (not os.path.exists(unet_safetensors_file) or os.path.getsize(unet_safetensors_file) == 0):
                # 检查是否有顶级的模型文件
                root_model_file = os.path.join(model_dir, "diffusion_pytorch_model.safetensors")
                if os.path.exists(root_model_file) and os.path.getsize(root_model_file) > 0:
                    try:
                        # 复制到unet目录的bin和safetensors文件
                        shutil.copy2(root_model_file, unet_safetensors_file)
                        logger.info(f"复制UNet文件: {root_model_file} -> {unet_safetensors_file}")
                        shutil.copy2(root_model_file, unet_model_file)
                        logger.info(f"复制UNet文件: {root_model_file} -> {unet_model_file}")
                    except Exception as e:
                        logger.error(f"复制UNet文件失败: {e}")
            # 如果只有safetensors文件但没有bin文件，或者bin文件大小为0
            elif os.path.exists(unet_safetensors_file) and os.path.getsize(unet_safetensors_file) > 0 and (not os.path.exists(unet_model_file) or os.path.getsize(unet_model_file) == 0):
                try:
                    # 复制safetensors到bin
                    shutil.copy2(unet_safetensors_file, unet_model_file)
                    logger.info(f"复制UNet文件: {unet_safetensors_file} -> {unet_model_file}")
                except Exception as e:
                    logger.error(f"复制UNet safetensors到bin失败: {e}")
            # 如果只有bin文件但没有safetensors文件，或者safetensors文件大小为0
            elif os.path.exists(unet_model_file) and os.path.getsize(unet_model_file) > 0 and (not os.path.exists(unet_safetensors_file) or os.path.getsize(unet_safetensors_file) == 0):
                try:
                    # 复制bin到safetensors
                    shutil.copy2(unet_model_file, unet_safetensors_file)
                    logger.info(f"复制UNet文件: {unet_model_file} -> {unet_safetensors_file}")
                except Exception as e:
                    logger.error(f"复制UNet bin到safetensors失败: {e}")
            
            # 检查是否需要创建config.json文件
            for subdir in required_dirs:
                config_path = os.path.join(model_dir, subdir, "config.json")
                if not os.path.exists(config_path):
                    # 检查是否有顶级config.json可复制
                    main_config = os.path.join(model_dir, "config.json")
                    if os.path.exists(main_config):
                        try:
                            shutil.copy2(main_config, config_path)
                            logger.info(f"复制配置文件: {main_config} -> {config_path}")
                        except Exception as e:
                            logger.error(f"复制配置文件失败: {e}")
                    else:
                        # 创建简单的默认配置
                        try:
                            default_config = {"_class_name": f"Wan2.1{subdir.capitalize()}"}
                            with open(config_path, 'w') as f:
                                import json
                                json.dump(default_config, f)
                            logger.info(f"创建默认配置文件: {config_path}")
                        except Exception as e:
                            logger.error(f"创建默认配置文件失败: {e}")
        
        # 检查各子目录是否有文件
        empty_dirs = []
        for dir_name in required_dirs:
            dir_path = os.path.join(model_dir, dir_name)
            if os.path.exists(dir_path) and len(os.listdir(dir_path)) == 0:
                empty_dirs.append(dir_name)
        
        if empty_dirs:
            logger.warning(f"检测到空子目录: {', '.join(empty_dirs)}")
            # 如果有空目录，提示可能需要下载完整模型
            if is_wan_model:
                logger.warning(f"Wan2.1模型结构可能不完整，建议下载完整模型: https://huggingface.co/Wan-AI/Wan2.1-T2V-1.3B")
            else:
                logger.warning(f"模型结构可能不完整，建议下载完整模型")
        
        logger.info(f"模型目录结构检查/修复完成: {model_dir}")
        return True
    except Exception as e:
        logger.error(f"修复模型目录结构失败: {e}")
        logger.error(traceback.format_exc())
        return False

# 查找最佳的模型缓存目录
def find_best_model_cache_dir() -> str:
    """
    查找最佳的模型缓存目录，以简化逻辑方式处理：
    1. 首先尝试使用WAN_CONFIG中定义的缓存目录
    2. 然后检查环境变量WAN_MODEL_CACHE_DIR指定的目录
    3. 检查当前目录下的models_cache目录
    4. 检查脚本所在目录下的models_cache目录
    5. 使用标准应用数据目录
    
    简化查找逻辑并提供详细的诊断信息。
    
    Returns:
        str: 最佳的模型缓存目录路径
    """
    logger.info("正在查找最佳模型缓存目录...")
    
    # 强制使用真实模式
    mock_mode = False
    
    logger.info(f"系统平台: {platform.system()} {platform.release()}")
    logger.info(f"Python版本: {sys.version}")
    logger.info(f"当前工作目录: {os.getcwd()}")
    
    # 记录搜索目录的状态，以便全面了解系统环境
    all_searched_paths = []
    
    # 0. 首先尝试使用配置中定义的路径(优先级最高)
    if hasattr(WAN_CONFIG, 'model_cache_dir') and WAN_CONFIG['model_cache_dir']:
        config_cache_dir = WAN_CONFIG['model_cache_dir']
        logger.info(f"检查配置中设置的缓存目录: {config_cache_dir}")
        
        exists, readable, writable, msg = check_directory_permissions(config_cache_dir)
        logger.info(f"检查配置设置的缓存目录 - {msg}")
        all_searched_paths.append((config_cache_dir, exists, readable, writable, "CONFIG中的model_cache_dir"))
        
        if exists and readable and writable:
            logger.info(f"✓ 使用配置指定的模型缓存目录: {config_cache_dir}")
            return config_cache_dir
        else:
            logger.warning(f"⚠ 配置指定的目录有问题: {msg}")
    
    # 1. 检查环境变量并记录情况
    env_var_value = os.environ.get("WAN_MODEL_CACHE_DIR", "")
    if env_var_value:
        logger.info(f"发现环境变量 WAN_MODEL_CACHE_DIR = {env_var_value}")
    else:
        logger.info("未设置环境变量 WAN_MODEL_CACHE_DIR")
    
    # 使用环境变量指定的目录
    if env_var_value:
        cache_dir = env_var_value
        exists, readable, writable, msg = check_directory_permissions(cache_dir)
        logger.info(f"检查环境变量设置的缓存目录 - {msg}")
        all_searched_paths.append((cache_dir, exists, readable, writable, "环境变量WAN_MODEL_CACHE_DIR"))
        
        if exists and readable and writable:
            logger.info(f"✓ 使用环境变量指定的模型缓存目录: {cache_dir}")
            return cache_dir
        else:
            logger.warning(f"⚠ 环境变量指定的目录有问题: {msg}")
    
    # 2. 检查当前目录下的models_cache目录
    current_dir_cache = os.path.join(os.getcwd(), "models_cache")
    exists, readable, writable, msg = check_directory_permissions(current_dir_cache)
    logger.info(f"检查当前目录下的models_cache - {msg}")
    all_searched_paths.append((current_dir_cache, exists, readable, writable, "当前目录下的models_cache"))
    
    if exists and readable and writable:
        logger.info(f"✓ 使用当前目录下的models_cache目录: {current_dir_cache}")
        return current_dir_cache
    
    # 3. 检查脚本所在目录下的models_cache目录
    script_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    script_dir_cache = os.path.join(script_dir, "models_cache")
    exists, readable, writable, msg = check_directory_permissions(script_dir_cache)
    logger.info(f"检查脚本目录下的models_cache - {msg}")
    all_searched_paths.append((script_dir_cache, exists, readable, writable, "脚本目录下的models_cache"))
    
    if exists and readable and writable:
        logger.info(f"✓ 使用脚本目录下的models_cache目录: {script_dir_cache}")
        return script_dir_cache
    
    # 4. 使用标准应用数据目录
    app_data_dir = os.path.join(get_app_data_dir(), "models_cache")
    exists, readable, writable, msg = check_directory_permissions(app_data_dir)
    logger.info(f"检查标准应用数据目录 - {msg}")
    all_searched_paths.append((app_data_dir, exists, readable, writable, "标准应用数据目录"))
    
    # 如果目录不存在，尝试创建
    if not exists:
        try:
            os.makedirs(app_data_dir, exist_ok=True)
            logger.info(f"已创建标准应用数据目录: {app_data_dir}")
            exists = True
            readable = os.access(app_data_dir, os.R_OK)
            writable = os.access(app_data_dir, os.W_OK)
        except Exception as e:
            logger.error(f"创建标准应用数据目录失败: {e}")
    
    if exists and readable and writable:
        logger.info(f"✓ 使用标准应用数据目录: {app_data_dir}")
        return app_data_dir
    
    # 5. 如果所有尝试都失败，使用临时目录
    temp_dir = os.path.join(tempfile.gettempdir(), "wan_models_cache")
    try:
        os.makedirs(temp_dir, exist_ok=True)
        logger.warning(f"⚠ 使用临时目录作为最后的选择: {temp_dir}")
        return temp_dir
    except Exception as e:
        logger.error(f"创建临时目录失败: {e}")
        logger.error(f"无法找到可用的模型缓存目录，返回当前目录")
        return os.getcwd()

# 设置全局模型缓存目录
WAN_MODEL_CACHE_DIR = find_best_model_cache_dir()

# 设置模型路径
WAN_T2V_14B_MODEL = os.path.join(WAN_MODEL_CACHE_DIR, os.environ.get("WAN_T2V_14B_MODEL", "Wan2.1-T2V-14B"))
WAN_T2V_1_3B_MODEL = os.path.join(WAN_MODEL_CACHE_DIR, os.environ.get("WAN_T2V_1_3B_MODEL", "Wan2.1-T2V-1.3B"))
WAN_I2V_720P_MODEL = os.path.join(WAN_MODEL_CACHE_DIR, os.environ.get("WAN_I2V_720P_MODEL", "Wan2.1-I2V-720p"))
WAN_I2V_480P_MODEL = os.path.join(WAN_MODEL_CACHE_DIR, os.environ.get("WAN_I2V_480P_MODEL", "Wan2.1-I2V-480p"))

# 日志记录目录变更
logger.info(f"使用模型缓存目录: {WAN_MODEL_CACHE_DIR}")
if os.getenv("WAN_MODEL_CACHE_DIR") is None:
    logger.info(f"提示: 使用默认缓存目录。如需更改，请设置环境变量 WAN_MODEL_CACHE_DIR")

# 检查需要的模型目录结构
for model_dir in [WAN_T2V_14B_MODEL, WAN_T2V_1_3B_MODEL, WAN_I2V_720P_MODEL, WAN_I2V_480P_MODEL]:
    exists, readable, writable, msg = check_directory_permissions(model_dir)
    logger.info(f"模型目录检查: {msg}")

def check_disk_space(path: str, required_mb: int = 500) -> bool:
    """
    检查指定路径所在磁盘是否有足够的可用空间
    
    Args:
        path: 要检查的路径
        required_mb: 所需的最小可用空间，单位为MB
        
    Returns:
        bool: 是否有足够的可用空间
    """
    try:
        import shutil
        
        # 获取磁盘使用情况
        disk_usage = shutil.disk_usage(path)
        
        # 转换为MB
        free_mb = disk_usage.free / (1024 * 1024)
        
        # 检查是否有足够的可用空间
        has_enough_space = free_mb >= required_mb
        
        # 记录空间状态
        logger.info(f"磁盘空间检查: 路径={path}, 可用={free_mb:.2f}MB, 所需={required_mb}MB, 结果={'充足' if has_enough_space else '不足'}")
        
        return has_enough_space
    except Exception as e:
        logger.error(f"检查磁盘空间时出错: {str(e)}")
        # 出错时保守地返回False
        return False

class WanVideoService:
    """WAN 2.1 视频生成服务"""
    
    def __init__(self, 
                 device: str = "cuda",
                 precision: str = "fp16",
                 force_version_check: bool = True,
                 mock_mode: bool = False):
        """
        初始化WanVideo服务
        
        Args:
            device: 设备类型, 'cuda', 'mps', 'cpu'等
            precision: 精度, 'fp16', 'fp32'等
            force_version_check: 是否强制进行版本检查
            mock_mode: 是否启用模拟模式
        """
        self.device = device
        self.precision = precision
        self.force_version_check = force_version_check
        self.model_directory = None
        self.models = {"t2v": {}}  # 按任务类型和参数组织的模型实例
        self.model_status = {"t2v": {}}  # 模型状态
        
        # 初始化torch_dtype属性
        self.torch_dtype = self._get_torch_dtype(precision)
        
        # 设置模型缓存目录 - 确保属性名一致性
        self.model_cache_dir = WAN_MODEL_CACHE_DIR
        
        # 设置输出目录
        self.output_dir = os.path.join(os.getcwd(), "media")
        os.makedirs(self.output_dir, exist_ok=True)
        logger.info(f"输出目录: {self.output_dir}")

        # 检查版本
        if force_version_check:
            import pkg_resources
            logger.info("检查依赖包版本...")
            
            # 尝试导入关键包
            try:
                import torch
                import diffusers
                
                logger.info(f"系统信息: Python {sys.version}")
                logger.info(f"PyTorch: {torch.__version__}, CUDA可用: {torch.cuda.is_available()}")
                logger.info(f"Diffusers: {diffusers.__version__}")
                
                if torch.cuda.is_available():
                    logger.info(f"CUDA设备: {torch.cuda.get_device_name(0)}")
                    logger.info(f"CUDA版本: {torch.version.cuda}")
                
                # 运行更详细的环境检查
                env_info = self._check_and_setup_diffusers()
                self.env_supports_wan = env_info.get("supports_wan_classes", False)
                
            except ImportError as e:
                logger.error(f"缺少关键依赖: {str(e)}")
                logger.warning("部分功能可能无法正常工作")
        
        # 强制模拟模式设置（用于测试）
        self.mock_mode = mock_mode
        if mock_mode:
            logger.warning("服务以模拟模式启动，将不会加载真实模型")
            
        # 初始化主程序路径
        try:
            if getattr(sys, 'frozen', False):
                # 如果是打包后的可执行文件
                self.app_dir = os.path.dirname(sys.executable)
            else:
                # 如果是开发环境
                self.app_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            
            # 设置本地模型目录
            self.model_directory = os.path.join(self.app_dir, "backend", "local_models", "wan")
            os.makedirs(self.model_directory, exist_ok=True)
            logger.info(f"本地模型目录: {self.model_directory}")
            
        except Exception as e:
            logger.error(f"初始化路径失败: {str(e)}")
            logger.error(traceback.format_exc())
            self.model_directory = os.path.join(os.getcwd(), "backend", "local_models", "wan")
            logger.warning(f"使用备用模型目录: {self.model_directory}")
    
    def _ensure_video_dimensions_compatible(self, width: int, height: int) -> Tuple[int, int]:
        """
        确保视频尺寸与标准宏块大小兼容，以避免编码器警告或错误
        
        视频编码器通常要求尺寸是特定值的倍数（如2、4、8或16），
        这个方法确保宽度和高度是这些值的倍数，避免FFmpeg警告。
        
        Args:
            width: 原始宽度
            height: 原始高度
            
        Returns:
            Tuple[int, int]: 调整后的宽度和高度
        """
        # 大多数编码器要求尺寸是16的倍数，但保守起见我们使用2
        # 对于较高级的编码器，可能需要是4或8的倍数
        macroblock_size = 16
        
        # 计算调整后的尺寸，确保是宏块大小的倍数
        adjusted_width = (width + macroblock_size - 1) // macroblock_size * macroblock_size
        adjusted_height = (height + macroblock_size - 1) // macroblock_size * macroblock_size
        
        # 记录尺寸调整
        if width != adjusted_width or height != adjusted_height:
            logger.info(f"视频尺寸调整: {width}x{height} -> {adjusted_width}x{adjusted_height} (宏块大小: {macroblock_size})")
        
        return adjusted_width, adjusted_height 

    def _init_model_status(self):
        """初始化模型状态跟踪"""
        # T2V模型状态初始化
        t2v_model_14b = os.environ.get("WAN_T2V_14B_MODEL", "Wan2.1-T2V-14B")
        t2v_model_1_3b = os.environ.get("WAN_T2V_1_3B_MODEL", "Wan2.1-T2V-1.3B")
        
        # 解析支持的分辨率
        resolutions = ["720p", "1080p"] # 默认支持的分辨率
        
        self.model_status = {
            "t2v": {
                "14B": {res: {"loaded": False, "loading": False, "error": None} for res in resolutions},
                "1.3B": {res: {"loaded": False, "loading": False, "error": None} for res in resolutions}
            },
            "i2v": {res: {"loaded": False, "loading": False, "error": None} for res in resolutions}
        }
        
        # 初始化模型字典
        self.models = {
            "t2v": {},  # 文本到视频模型
            "i2v": {}   # 图像到视频模型
        }
        
        # 日志记录模型状态
        logger.info(f"初始化模型状态跟踪:")
        logger.info(f"  文本到视频 (14B): {', '.join(resolutions)}")
        logger.info(f"  文本到视频 (1.3B): {', '.join(resolutions)}")
        logger.info(f"  图像到视频: {', '.join(resolutions)}")
        
        # 检查模型目录是否存在
        t2v_14b_path = os.path.join(self.model_cache_dir, t2v_model_14b)
        t2v_1_3b_path = os.path.join(self.model_cache_dir, t2v_model_1_3b)
        
        # 记录模型路径信息
        logger.info(f"模型路径:")
        logger.info(f"  T2V 14B: {t2v_14b_path}")
        logger.info(f"  T2V 1.3B: {t2v_1_3b_path}")
        
        # 检查模型文件是否存在
        t2v_14b_exists = os.path.exists(t2v_14b_path) and os.path.isdir(t2v_14b_path)
        t2v_1_3b_exists = os.path.exists(t2v_1_3b_path) and os.path.isdir(t2v_1_3b_path)
        
        # 日志记录模型文件状态
        logger.info(f"模型目录状态:")
        logger.info(f"  T2V 14B: {'存在' if t2v_14b_exists else '不存在'}")
        logger.info(f"  T2V 1.3B: {'存在' if t2v_1_3b_exists else '不存在'}")
        
        # 检查环境变量中是否强制指定了模拟模式
        # 环境变量的优先级高于参数
        wan_mock_mode = os.environ.get("WAN_MOCK_MODE", str(self.mock_mode)).lower()
        
        # 处理可能的字符串值，确保其正确性
        if wan_mock_mode in ["true", "1", "yes"]:
            self.mock_mode = True
            logger.info("通过环境变量启用模拟模式")
        elif wan_mock_mode in ["false", "0", "no"]:
            self.mock_mode = False
            logger.info("通过环境变量禁用模拟模式")
        
        logger.info(f"Wan模型服务初始化状态: mock_mode={self.mock_mode}, device={self.device}")
    
    def check_force_real_mode(self, force_real_mode: bool = False):
        """检查并可能更新模拟模式状态
        
        Args:
            force_real_mode: 是否强制使用真实模式
            
        Returns:
            bool: 更新后的模拟模式状态
        """
        # 保存当前状态以检测变化
        old_mock_mode = self.mock_mode
        
        # 如果指定了强制真实模式，则禁用模拟模式
        if force_real_mode and self.mock_mode:
            self.mock_mode = False
            logger.info("根据force_real_mode参数禁用模拟模式")
            
            # 如果模拟模式被更改，则重置环境变量
            if old_mock_mode != self.mock_mode:
                os.environ["WAN_MOCK_MODE"] = "false"
                logger.info("设置环境变量WAN_MOCK_MODE=false")
        
        return self.mock_mode

    def _get_torch_dtype(self, precision: str):
        """获取torch数据类型"""
        if precision == "float16":
            return torch.float16
        elif precision == "bfloat16" and hasattr(torch, "bfloat16"):
            return torch.bfloat16
        else:
            return torch.float32 

    async def check_model_status(self, model_type: str, model_size: str = "1.3B", resolution: str = "720p") -> Dict[str, Any]:
        """
        检查模型状态，返回模型加载状态和错误信息
        
        Args:
            model_type: 模型类型 ('t2v', 'i2v')
            model_size: 模型大小 (当model_type为't2v'时使用)
            resolution: 分辨率
            
        Returns:
            Dict[str, Any]: 模型状态信息
        """
        logger.info(f"检查模型状态: {model_type}, 规格: {model_size}, 分辨率: {resolution}")
        
        try:
            # 获取模型ID和路径
            model_id = self._get_model_id(model_type, model_size, resolution)
            model_path = os.path.join(WAN_MODEL_CACHE_DIR, model_id)
            
            # 检查模型目录是否存在
            if not os.path.exists(model_path):
                return {
                    "available": False,
                    "loaded": False,
                    "error": f"模型目录不存在: {model_path}",
                    "model_path": model_path
                }
            
            # 检查模型完整性
            integrity_result = await self.check_model_integrity(model_type, model_size, resolution)
            
            # 将新的状态结构转换为兼容的格式
            is_available = integrity_result["status"] == "ok"
            error_message = integrity_result.get("message", "")
            
            # 构建状态响应
            status_response = {
                "available": is_available,
                "loaded": False,  # 这个字段表示模型是否已加载到内存中
                "error": None if is_available else error_message,
                "model_path": integrity_result.get("path", model_path),
                "integrity_status": integrity_result
            }
            
            # 检查模型是否已加载到内存中
            model_key = f"{model_size}_{resolution}"
            if model_type in self.models and model_key in self.models[model_type]:
                status_response["loaded"] = True
            
            return status_response
            
        except Exception as e:
            logger.error(f"检查模型状态时出错: {str(e)}")
            logger.error(traceback.format_exc())
            return {
                "available": False,
                "loaded": False,
                "error": f"检查模型状态时出错: {str(e)}",
                "exception": str(e)
            }

    async def check_model_integrity(self, model_type: str, model_size: str = "1.3B", resolution: str = "720p") -> Dict[str, Any]:
        """
        检查模型完整性，确认是否可以加载
        
        Args:
            model_type: 模型类型 ('t2v', 'i2v')
            model_size: 模型大小 ('1.3B', '14B')
            resolution: 分辨率 ('720p', '1080p')
            
        Returns:
            Dict[str, Any]: 模型完整性状态
        """
        logger.info(f"检查模型完整性: {model_type}, 规格: {model_size}, 分辨率: {resolution}")
        
        try:
            # 获取模型ID
            model_id = self._get_model_id(model_type, model_size, resolution)
            
            # 使用WAN_MODEL_CACHE_DIR而不是self.model_cache_dir
            model_path = os.path.join(WAN_MODEL_CACHE_DIR, model_id)
            
            # 检查模型目录
            if not os.path.exists(model_path):
                logger.warning(f"模型路径不存在: {model_path}")
                return {
                    "status": "missing",
                    "path": model_path,
                    "message": f"模型不存在: {model_id}"
                }
            
            # 检查模型目录结构
            required_dirs = []
            if model_type == "t2v":
                required_dirs = ["vae", "unet", "text_encoder", "scheduler"]
            elif model_type == "i2v":
                required_dirs = ["vae", "unet", "image_encoder"]
            
            missing_dirs = []
            for dir_name in required_dirs:
                dir_path = os.path.join(model_path, dir_name)
                if not os.path.exists(dir_path):
                    missing_dirs.append(dir_name)
            
            if missing_dirs:
                logger.warning(f"模型目录结构不完整: {', '.join(missing_dirs)} 缺失")
                
                # 尝试自动修复
                if auto_fix_model_structure(model_path):
                    logger.info(f"已尝试自动修复模型目录结构: {model_path}")
                    
                    # 重新检查修复后的情况
                    missing_dirs = []
                    for dir_name in required_dirs:
                        dir_path = os.path.join(model_path, dir_name)
                        if not os.path.exists(dir_path):
                            missing_dirs.append(dir_name)
                    
                    if missing_dirs:
                        return {
                            "status": "incomplete",
                            "path": model_path,
                            "missing_dirs": missing_dirs,
                            "message": f"模型目录结构不完整，修复后仍缺少: {', '.join(missing_dirs)}"
                        }
                else:
                    return {
                        "status": "broken",
                        "path": model_path,
                        "missing_dirs": missing_dirs,
                        "message": f"模型目录结构不完整，无法修复: {', '.join(missing_dirs)}"
                    }
            
            # 检查关键模型文件的大小
            # 不同模型期望的文件大小(字节)
            # 这些仅为参考值，实际值可能根据发布方式和版本有所不同
            expected_file_sizes = {
                "t2v": {
                    "14B": {
                        "unet/pytorch_model.bin": 4998781576,  # 约5GB
                        "text_encoder/pytorch_model.bin": 1268750437  # 约1.2GB
                    },
                    "1.3B": {
                        "unet/pytorch_model.bin": 1358489772,  # 约1.3GB
                        "text_encoder/pytorch_model.bin": 684123076  # 约650MB
                    }
                }
            }
            
            # 检查文件大小(只对T2V进行检查)
            if model_type == "t2v" and model_size in expected_file_sizes.get("t2v", {}):
                size_issues = []
                for file_path, expected_size in expected_file_sizes["t2v"][model_size].items():
                    full_path = os.path.join(model_path, file_path)
                    if os.path.exists(full_path):
                        actual_size = os.path.getsize(full_path)
                        # 允许5%的误差
                        size_tolerance = expected_size * 0.05
                        if abs(actual_size - expected_size) > size_tolerance:
                            # 特殊处理text_encoder文件大小不匹配的情况
                            if "text_encoder" in file_path:
                                logger.warning(f"文件大小不匹配: {file_path}, 期望: {expected_size}, 实际: {actual_size}, 差异: {expected_size - actual_size}")
                                logger.warning(f"忽略text_encoder文件大小不匹配，允许继续加载模型")
                                continue
                            
                            size_issues.append({
                                "file": file_path,
                                "expected_size": expected_size,
                                "actual_size": actual_size,
                                "difference": expected_size - actual_size
                            })
                            logger.warning(f"文件大小不匹配: {file_path}, 期望: {expected_size}, 实际: {actual_size}, 差异: {expected_size - actual_size}")
                
                if size_issues:
                    return {
                        "status": "incomplete_file_size",
                        "path": model_path,
                        "size_issues": size_issues,
                        "message": f"模型文件大小不匹配，可能下载不完整或损坏"
                    }
            
            # 检查tokenizer
            # 支持两种类型的tokenizer: 1) CLIP风格: tokenizer_config.json, vocab.json, merges.txt
            #                       2) T5/UMT5风格: tokenizer_config.json, spiece.model, tokenizer.json
            
            clip_tokenizer_files = ["tokenizer_config.json", "vocab.json", "merges.txt"]
            t5_tokenizer_files = ["tokenizer_config.json", "spiece.model"]
            
            # 检查CLIP风格的tokenizer文件
            missing_clip_files = []
            for file_name in clip_tokenizer_files:
                file_path = os.path.join(model_path, file_name)
                if not os.path.exists(file_path):
                    missing_clip_files.append(file_name)
            
            # 检查T5风格的tokenizer文件
            missing_t5_files = []
            for file_name in t5_tokenizer_files:
                file_path = os.path.join(model_path, file_name)
                if not os.path.exists(file_path):
                    missing_t5_files.append(file_name)
            
            # 还要检查google/umt5-xxl目录下是否有tokenizer文件
            umt5_tokenizer_dir = os.path.join(model_path, "google", "umt5-xxl")
            has_umt5_tokenizer = False
            if os.path.exists(umt5_tokenizer_dir) and os.path.isdir(umt5_tokenizer_dir):
                umt5_files = ["tokenizer_config.json", "spiece.model"]
                missing_umt5_files = []
                for file_name in umt5_files:
                    if not os.path.exists(os.path.join(umt5_tokenizer_dir, file_name)):
                        missing_umt5_files.append(file_name)
                
                if not missing_umt5_files:
                    has_umt5_tokenizer = True
                    logger.info(f"在google/umt5-xxl目录下发现完整的tokenizer文件")
            
            # 如果有任何一种tokenizer完整，则认为有效
            if len(missing_clip_files) == 0 or len(missing_t5_files) == 0 or has_umt5_tokenizer:
                # 通过tokenizer检查
                logger.info(f"分词器文件检查通过")
            else:
                if has_umt5_tokenizer:
                    # 如果有UMT5 tokenizer，尝试从此处复制文件到适当的位置
                    logger.info(f"从google/umt5-xxl目录复制tokenizer文件到根目录和tokenizer目录")
                    os.makedirs(os.path.join(model_path, "tokenizer"), exist_ok=True)
                    
                    for file_name in ["tokenizer_config.json", "spiece.model", "tokenizer.json", "special_tokens_map.json"]:
                        src_path = os.path.join(umt5_tokenizer_dir, file_name)
                        if os.path.exists(src_path):
                            # 复制到根目录
                            root_dest = os.path.join(model_path, file_name)
                            # 复制到tokenizer目录
                            token_dest = os.path.join(model_path, "tokenizer", file_name)
                            
                            try:
                                import shutil
                                shutil.copy2(src_path, root_dest)
                                shutil.copy2(src_path, token_dest)
                                logger.info(f"复制tokenizer文件: {src_path} -> {root_dest} 和 {token_dest}")
                            except Exception as copy_error:
                                logger.error(f"复制tokenizer文件失败: {str(copy_error)}")
                    
                    # 重新检查根目录tokenizer文件
                    missing_t5_files = []
                    for file_name in t5_tokenizer_files:
                        file_path = os.path.join(model_path, file_name)
                        if not os.path.exists(file_path):
                            missing_t5_files.append(file_name)
                    
                    if not missing_t5_files:
                        logger.info(f"成功从google/umt5-xxl复制tokenizer文件")
                    else:
                        return {
                            "status": "incomplete_tokenizer",
                            "path": model_path,
                            "missing_files": missing_t5_files,
                            "message": f"T5分词器文件不完整，复制后仍缺少: {', '.join(missing_t5_files)}"
                        }
                else:
                    # 如果没有找到任何一种完整的tokenizer
                    logger.warning(f"未找到完整的tokenizer文件")
                    return {
                        "status": "incomplete_tokenizer",
                        "path": model_path,
                        "missing_clip_files": missing_clip_files,
                        "missing_t5_files": missing_t5_files,
                        "message": f"未找到完整的分词器文件集"
                    }
            
            # 所有检查通过
            return {
                "status": "ok",
                "path": model_path,
                "message": "模型完整性检查通过"
            }
            
        except Exception as e:
            logger.error(f"检查模型完整性时出错: {str(e)}")
            logger.error(traceback.format_exc())
            return {
                "status": "error",
                "error": str(e),
                "message": f"检查模型完整性时出错: {str(e)}"
            }

    def _get_model_id(self, model_type: str, model_size: str = "1.3B", resolution: str = "720p") -> str:
        """
        获取模型ID，用于构建模型路径
        
        Args:
            model_type: 模型类型，'t2v'或'i2v'
            model_size: 模型大小，仅用于t2v
            resolution: 分辨率，用于i2v或t2v的分辨率参数
            
        Returns:
            str: 模型ID
        """
        if model_type == "t2v":
            # T2V模型ID格式：Wan2.1-T2V-{size}
            model_id = os.environ.get(f"WAN_T2V_{model_size}_MODEL", f"Wan2.1-T2V-{model_size}")
        elif model_type == "i2v":
            # I2V模型ID格式：Wan2.1-I2V-{resolution}
            model_id = os.environ.get(f"WAN_I2V_{resolution.upper()}_MODEL", f"Wan2.1-I2V-{resolution}")
        else:
            raise ValueError(f"不支持的模型类型: {model_type}")
        
        return model_id
    
    def _get_model_dir(self, model_type: str, model_size: str, resolution: str = None) -> str:
        """
        获取模型目录路径
        
        Args:
            model_type: 模型类型 (t2v, i2v等)
            model_size: 模型大小 (1.3B, 14B等)
            resolution: 分辨率 (720p, 480p等)
            
        Returns:
            str: 模型目录路径
        """
        # 确定模型名称
        model_name = f"Wan2.1-{model_type.upper()}-{model_size}"
        
        # 构建模型目录路径
        model_dir = os.path.join(self.model_directory, "Wan2.1", model_name)
        
        # 创建目录（如果不存在）
        os.makedirs(model_dir, exist_ok=True)
        logger.info(f"模型目录: {model_dir}")
        
        # 尝试自动修复模型目录结构
        auto_fix_model_structure(model_dir)
        
        return model_dir

    async def initialize_t2v(self, task_id: str, progress_updater: ProgressUpdater, model_size: str = "1.3B", resolution: str = "720p", skip_integrity_check: bool = False):
        """
        初始化文本到视频模型
        
        Args:
            task_id: 任务ID
            progress_updater: 进度更新器
            model_size: 模型大小 (1.3B/14B)
            resolution: 分辨率 (720p/1080p)
            
        Returns:
            bool: 是否成功初始化
        """
        try:
            logger.info(f"初始化文本到视频模型: {model_size} {resolution}")
            
            # 检查模型状态
            status = await self.check_model_status("t2v", model_size, resolution)
            model_state = status.get("status", "unknown")
            
            if model_state == "loaded":
                logger.info(f"模型已加载: t2v-{model_size}")
                if progress_updater:
                    await progress_updater.update(100, [f"模型已加载: t2v-{model_size}"])
                
                key = f"{model_size}_{resolution}"
                return self.models["t2v"].get(key)
            
            logger.info(f"需要加载模型: t2v-{model_size}")
            
            # 检查Diffusers库是否安装
            try:
                self._check_and_setup_diffusers()
            except Exception as diffusers_error:
                logger.error(f"缺少diffusers库: {str(diffusers_error)}")
                error_msg = f"未安装diffusers库，无法加载模型: {str(diffusers_error)}"
                if progress_updater:
                    await progress_updater.update(0, [error_msg])
                raise ImportError(error_msg)
            
            # 获取模型路径
            model_dir = self._get_model_dir("t2v", model_size)
            logger.info(f"模型路径: {model_dir}")
            
            # 检查模型文件完整性
            if not skip_integrity_check:
                integrity_check = await self.check_model_integrity("t2v", model_size, resolution)
                
                if integrity_check.get("status") != "ok":
                    # 对于text_encoder文件大小不匹配的特殊处理
                    is_only_text_encoder_issue = False
                    if integrity_check.get("status") == "incomplete_file_size" and "size_issues" in integrity_check:
                        size_issues = integrity_check["size_issues"]
                        # 检查是否只有text_encoder相关的问题
                        if all("text_encoder" in issue["file"] for issue in size_issues):
                            is_only_text_encoder_issue = True
                            logger.warning("检测到只有text_encoder文件大小不匹配，允许继续加载模型")
                            if progress_updater:
                                await progress_updater.update(10, ["忽略text_encoder文件大小不匹配，尝试直接加载模型"])
                    
                    # 如果只是text_encoder问题，设置skip_integrity_check为True并继续
                    if is_only_text_encoder_issue:
                        skip_integrity_check = True
                        logger.warning("设置skip_integrity_check=True，允许继续加载模型")
                    else:
                        # 处理其他完整性问题
                        error_msg = integrity_check.get("message", "模型文件不完整")
                        
                        # 添加更详细的错误信息
                        if "missing_dirs" in integrity_check:
                            missing_dirs = integrity_check["missing_dirs"]
                            error_msg += f"缺少以下目录: {', '.join(missing_dirs)}"
                        
                        # 如果是文件大小问题，添加更详细的指导
                        if integrity_check.get("status") == "incomplete_file_size" and "size_issues" in integrity_check:
                            size_issues = integrity_check["size_issues"]
                            error_msg += "\n文件大小不匹配，模型可能未完整下载或已损坏:"
                            for issue in size_issues:
                                file_path = issue["file"]
                                expected_size_mb = issue["expected_size"] / (1024 * 1024)
                                actual_size_mb = issue["actual_size"] / (1024 * 1024)
                                error_msg += f"\n - {file_path}: 期望大小 {expected_size_mb:.2f}MB, 实际大小 {actual_size_mb:.2f}MB"
                            
                            error_msg += "\n请重新下载完整模型文件并放置在正确位置。"
                            
                            # 添加下载指南
                            download_guide = self._get_manual_download_guide("t2v", model_size, resolution)
                            error_msg += f"\n\n下载指南:\n{download_guide}"
                        
                        logger.error(error_msg)
                        
                        if progress_updater:
                            await progress_updater.update(10, [f"模型文件不完整: {error_msg}", "尝试回退到模拟模式..."])
                        
                        logger.warning("由于模型完整性检查失败，尝试回退到模拟模式")
                        return await self._create_mock_model(model_size, resolution, progress_updater)
            
            if skip_integrity_check:
                logger.warning(f"跳过模型完整性检查，尝试直接加载模型")
                if progress_updater:
                    await progress_updater.update(10, [f"跳过模型完整性检查，尝试直接加载模型"])
            
            # 更新进度
            if progress_updater:
                await progress_updater.update(20, ["开始加载模型..."])
                
            # 实际加载模型
            try:
                # 在"t2v"中初始化模型字典
                if "t2v" not in self.models:
                    self.models["t2v"] = {}
                
                # 加载和初始化模型
                from diffusers import TextToVideoSDPipeline, DPMSolverMultistepScheduler
                import torch
                
                # 获取torch数据类型
                dtype = self._get_torch_dtype(self.precision)
                
                # 使用默认模型ID
                model_id = self._get_model_id("t2v", model_size, resolution)
                
                # 更新进度
                if progress_updater:
                    await progress_updater.update(30, [f"加载 {model_id} 模型..."])
                
                # 加载模型组件
                logger.info(f"从路径加载Pipeline: {model_dir}")
                
                # 使用自定义调度器
                try:
                    scheduler = DPMSolverMultistepScheduler.from_pretrained(
                        model_dir, subfolder="scheduler", torch_dtype=dtype
                    )
                except Exception as scheduler_error:
                    logger.warning(f"无法加载调度器: {str(scheduler_error)}")
                    # 创建一个模拟调度器
                    scheduler = self._create_mock_scheduler()
                
                # 尝试完整加载pipeline
                try:
                    pipe = TextToVideoSDPipeline.from_pretrained(
                        model_dir, torch_dtype=dtype, scheduler=scheduler
                    )
                    logger.info("成功完整加载pipeline")
                except Exception as pipe_error:
                    logger.warning(f"从Diffusers路径加载Pipeline失败: {str(pipe_error)}")
                    logger.warning("尝试回退到模拟模式")
                    if progress_updater:
                        await progress_updater.update(40, [f"加载Pipeline失败，回退到模拟模式"])
                    return await self._create_mock_model(model_size, resolution, progress_updater)
                
                # 将模型移至正确设备
                pipe = pipe.to(self.device)
                
                # 启用内存优化
                pipe.enable_vae_slicing()
                
                # 如果可用，启用模型卸载
                if hasattr(pipe, "enable_model_cpu_offload"):
                    pipe.enable_model_cpu_offload()
                
                # 保存模型
                key = f"{model_size}_{resolution}"
                self.models["t2v"][key] = pipe
                
                # 更新状态
                if model_size not in self.model_status["t2v"]:
                    self.model_status["t2v"][model_size] = {}
                
                if resolution not in self.model_status["t2v"][model_size]:
                    self.model_status["t2v"][model_size][resolution] = {
                        "loaded": True,
                        "loading": False,
                        "error": None
                    }
                else:
                    self.model_status["t2v"][model_size][resolution]["loaded"] = True
                    self.model_status["t2v"][model_size][resolution]["loading"] = False
                    self.model_status["t2v"][model_size][resolution]["error"] = None
                
                # 完成
                if progress_updater:
                    await progress_updater.update(100, ["模型加载完成"])
                return pipe
                
            except Exception as e:
                # 捕获所有异常
                logger.error(f"加载模型时出错: {str(e)}")
                logger.error(traceback.format_exc())
                
                # 更新状态
                if model_size not in self.model_status["t2v"]:
                    self.model_status["t2v"][model_size] = {}
                
                if resolution not in self.model_status["t2v"][model_size]:
                    self.model_status["t2v"][model_size][resolution] = {
                        "loaded": False,
                        "loading": False,
                        "error": str(e)
                    }
                else:
                    self.model_status["t2v"][model_size][resolution]["loaded"] = False
                    self.model_status["t2v"][model_size][resolution]["loading"] = False
                    self.model_status["t2v"][model_size][resolution]["error"] = str(e)
                
                # 回退到模拟模式
                logger.warning("由于模型加载失败，尝试回退到模拟模式")
                if progress_updater:
                    await progress_updater.update(40, [f"加载模型时出错: {str(e)}, 尝试回退到模拟模式..."])
                return await self._create_mock_model(model_size, resolution, progress_updater)
        
        except Exception as e:
            logger.error(f"初始化文本到视频模型时出错: {str(e)}")
            logger.error(traceback.format_exc())
            
            # 回退到模拟模式
            logger.warning("由于初始化错误，尝试回退到模拟模式")
            if progress_updater:
                await progress_updater.update(20, [f"初始化错误: {str(e)}, 尝试回退到模拟模式..."])
            return await self._create_mock_model(model_size, resolution, progress_updater)

    async def _create_mock_model(self, model_size: str, resolution: str, progress_updater: ProgressUpdater) -> bool:
        """创建模拟模型，用于模拟模式
        
        Args:
            model_size: 模型大小
            resolution: 分辨率
            progress_updater: 进度更新器
            
        Returns:
            bool: 是否成功创建
        """
        try:
            logger.info(f"创建模拟文本到视频模型: {model_size} {resolution}")
            
            # 更新进度
            if progress_updater:
                await progress_updater.update(
                    progress=30,
                    logs=[f"创建模拟文本到视频模型..."]
                )
            
            # 创建模拟的autoencoder
            class AutoencoderKLWan:
                @classmethod
                def from_pretrained(cls, model_path, **kwargs):
                    logger.info(f"[模拟] 加载自编码器模型: {model_path}")
                    return cls()
                
                def to(self, device):
                    logger.info(f"[模拟] 将自编码器模型加载到设备: {device}")
                    return self
                
                def encode(self, pixel_values, **kwargs):
                    # 模拟编码操作
                    import torch
                    batch_size = pixel_values.shape[0]
                    # 创建一个模拟的latent, 假设编码后的大小为原尺寸的1/8
                    h = pixel_values.shape[2] // 8
                    w = pixel_values.shape[3] // 8
                    # 创建随机latent
                    latents = torch.randn(batch_size, 4, h, w, device=pixel_values.device)
                    return [latents]
                
                def decode(self, latents, **kwargs):
                    # 模拟解码操作
                    import torch
                    batch_size = latents.shape[0]
                    # 创建模拟的解码后图像，大小为latent的8倍
                    h = latents.shape[2] * 8
                    w = latents.shape[3] * 8
                    # 创建随机图像
                    images = torch.rand(batch_size, 3, h, w, device=latents.device)
                    return images
            
            # 创建模拟的调度器
            class MockScheduler:
                def __init__(self, **kwargs):
                    logger.info(f"[模拟] 初始化调度器")
                    self.timesteps = []
                    self.config = {"num_train_timesteps": 1000}
                
                @classmethod
                def from_pretrained(cls, model_path, **kwargs):
                    logger.info(f"[模拟] 加载调度器: {model_path}")
                    return cls(**kwargs)
                
                def set_timesteps(self, num_inference_steps, **kwargs):
                    logger.info(f"[模拟] 设置时间步: {num_inference_steps}")
                    self.timesteps = list(range(num_inference_steps))
                
                def step(self, model_output, timestep, sample, **kwargs):
                    # 模拟step操作
                    import torch
                    # 返回一个新的采样结果和一些额外信息
                    return {
                        "prev_sample": sample + torch.randn_like(sample) * 0.01,
                        "pred_original_sample": sample
                    }
            
            # 创建更健壮的模拟pipeline
            class WanPipeline:
                def __init__(self, **kwargs):
                    logger.info(f"[模拟] 初始化Pipeline")
                    self.model_size = model_size
                    self.resolution = resolution
                    # 添加必要的属性，确保能够模拟真实pipeline的关键功能
                    self.scheduler = kwargs.get("scheduler", MockScheduler())
                    self.vae = kwargs.get("vae", AutoencoderKLWan())
                    self.device = "cpu"  # 默认设备
                
                @classmethod
                def from_pretrained(cls, model_path, **kwargs):
                    logger.info(f"[模拟] 加载Pipeline: {model_path}")
                    scheduler = kwargs.get("scheduler", MockScheduler())
                    vae = kwargs.get("vae", AutoencoderKLWan())
                    return cls(scheduler=scheduler, vae=vae)
                
                def to(self, device):
                    logger.info(f"[模拟] 将Pipeline加载到设备: {device}")
                    self.device = device
                    return self
                
                def enable_model_cpu_offload(self):
                    logger.info(f"[模拟] 启用模型CPU卸载")
                    return self
                
                def enable_vae_slicing(self):
                    logger.info(f"[模拟] 启用VAE分片")
                    return self
                
                def disable_vae_slicing(self):
                    logger.info(f"[模拟] 禁用VAE分片")
                    return self
                
                def enable_attention_slicing(self, slice_size="auto"):
                    logger.info(f"[模拟] 启用注意力分片: {slice_size}")
                    return self
                
                def disable_attention_slicing(self):
                    logger.info(f"[模拟] 禁用注意力分片")
                    return self
                
                def __call__(self, prompt, negative_prompt="", height=720, width=1280, 
                             num_frames=16, guidance_scale=7.5, generator=None, num_inference_steps=50, **kwargs):
                    logger.info(f"[模拟] 生成视频: {prompt}")
                    
                    # 使用调度器
                    self.scheduler.set_timesteps(num_inference_steps)
                    
                    # 模拟生成的帧
                    import torch
                    import numpy as np
                    
                    # 创建一些随机帧
                    frames = torch.rand(num_frames, 3, height, width, device=self.device)
                    
                    # 添加一些变化
                    for i in range(1, num_frames):
                        frames[i] = frames[i-1] + torch.randn(3, height, width, device=self.device) * 0.1
                    
                    # 规范化到[0,1]范围
                    frames = (frames - frames.min()) / (frames.max() - frames.min())
                    
                    # 转换为numpy数组
                    frames_np = frames.cpu().numpy().transpose(0, 2, 3, 1)
                    
                    # 规范化到[0,255]范围
                    frames_np = (frames_np * 255).astype(np.uint8)
                    
                    # 返回结果
                    class MockOutput:
                        def __init__(self, frames_tensor, frames_np):
                            self.frames = frames_np
                    
                    return MockOutput(frames, frames_np)
            
            # 创建并保存模拟管道
            key = f"{model_size}_{resolution}"
            
            # 创建模拟调度器
            scheduler = MockScheduler()
            
            # 确保它有set_timesteps方法
            if not hasattr(scheduler, 'set_timesteps'):
                logger.warning("模拟调度器缺少set_timesteps方法，添加默认实现")
                def set_timesteps(self, num_inference_steps, **kwargs):
                    logger.info(f"[模拟] 设置时间步: {num_inference_steps}")
                    self.timesteps = list(range(num_inference_steps))
                scheduler.set_timesteps = types.MethodType(set_timesteps, scheduler)
            
            # 创建模拟管道
            pipe = WanPipeline(
                scheduler=scheduler,
                vae=AutoencoderKLWan()
            )
            
            # 确保管道有scheduler属性
            if not hasattr(pipe, 'scheduler') or pipe.scheduler is None:
                logger.warning("模拟管道缺少scheduler属性，手动设置")
                pipe.scheduler = scheduler
                
            # 将管道保存到模型字典中
            self.models["t2v"][key] = pipe
            
            # 更新模型状态
            if model_size not in self.model_status["t2v"]:
                self.model_status["t2v"][model_size] = {}
            
            self.model_status["t2v"][model_size][resolution] = {
                "loaded": True,
                "loading": False,
                "error": None,
                "is_mock": True  # 标记这是一个模拟模型
            }
            
            # 更新进度
            if progress_updater:
                await progress_updater.update(
                    progress=100,
                    logs=[f"模拟模型创建完成, 使用尺寸: {model_size}, 分辨率: {resolution}"]
                )
            
            logger.info(f"模拟T2V模型创建成功: {model_size} {resolution}")
            return pipe
            
        except Exception as e:
            logger.error(f"创建模拟模型时出错: {e}")
            logger.error(traceback.format_exc())
            
            # 尝试创建一个非常简单的模拟对象
            try:
                logger.info("尝试创建一个简单的模拟对象作为最后的回退")
                import types
                
                # 创建一个基本的模拟调度器
                scheduler = MockScheduler()
                
                # 确保它有set_timesteps方法
                if not hasattr(scheduler, 'set_timesteps'):
                    def set_timesteps(self, num_inference_steps, **kwargs):
                        logger.info(f"[简单模拟] 设置时间步: {num_inference_steps}")
                        self.timesteps = list(range(num_inference_steps))
                    scheduler.set_timesteps = types.MethodType(set_timesteps, scheduler)
                
                # 创建一个最小的模拟管道
                class MinimalMockPipeline:
                    def __init__(self):
                        self.scheduler = scheduler
                        self.device = "cpu"
                    
                    def to(self, device):
                        return self
                        
                    def enable_vae_slicing(self):
                        return self
                    
                    def enable_model_cpu_offload(self):
                        return self
                    
                    def __call__(self, **kwargs):
                        import numpy as np
                        # 返回一个带有简单帧属性的对象
                        class SimpleOutput:
                            def __init__(self):
                                h = kwargs.get('height', 720)
                                w = kwargs.get('width', 1280)
                                n = kwargs.get('num_frames', 16)
                                self.frames = np.zeros((n, h, w, 3), dtype=np.uint8)
                        return SimpleOutput()
                
                pipe = MinimalMockPipeline()
                
                # 保存这个最小的模拟对象
                key = f"{model_size}_{resolution}"
                self.models["t2v"][key] = pipe
                
                # 更新模型状态
                if model_size not in self.model_status["t2v"]:
                    self.model_status["t2v"][model_size] = {}
                
                self.model_status["t2v"][model_size][resolution] = {
                    "loaded": True,
                    "loading": False,
                    "error": "使用最小模拟模式",
                    "is_mock": True  # 标记这是一个模拟模型
                }
                
                if progress_updater:
                    await progress_updater.update(
                        progress=100,
                        logs=[f"最小模拟模型创建完成（由于前一个错误）"]
                    )
                
                logger.info(f"最小模拟T2V模型创建成功: {model_size} {resolution}")
                return pipe
                
            except Exception as fallback_error:
                logger.error(f"创建最小模拟模型也失败: {fallback_error}")
                logger.error(traceback.format_exc())
                
                if progress_updater:
                    await progress_updater.update(
                        progress=0,
                        logs=[f"模拟模型创建失败: {str(e)}"]
                    )
                
                return False

    def _get_manual_download_guide(self, model_type: str, model_size: str = "1.3B", resolution: str = "720p") -> str:
        """
        获取手动下载指南
        
        Args:
            model_type: 模型类型，'t2v'或'i2v'
            model_size: 模型大小，仅用于t2v
            resolution: 分辨率，用于i2v或t2v的分辨率参数
            
        Returns:
            str: 手动下载指南
        """
        model_id = self._get_model_id(model_type, model_size, resolution)
        model_path = os.path.join(self.model_cache_dir, model_id)
        
        guide = f"模型文件不存在: {model_path}\n\n"
        guide += "手动下载指南:\n"
        guide += f"1. 请在以下地址下载模型: https://huggingface.co/models?search={model_id}\n"
        guide += f"2. 下载后解压到目录: {self.model_cache_dir}\n"
        guide += f"3. 确保目录结构为: {model_path}/\n"
        guide += f"   - model_index.json\n"
        guide += f"   - config.json\n"
        guide += f"   - 其他模型文件...\n\n"
        guide += f"或者使用环境变量设置自定义模型路径:\n"
        
        if model_type == "t2v":
            guide += f"WAN_T2V_{model_size}_MODEL=你的模型目录名\n"
        elif model_type == "i2v":
            guide += f"WAN_I2V_{resolution.upper()}_MODEL=你的模型目录名\n"
        
        guide += f"\n也可以使用全局缓存目录环境变量:\n"
        guide += f"WAN_MODEL_CACHE_DIR=你的模型缓存根目录"
        
        return guide 

    def _generate_thumbnail(self, video_path: str) -> str:
        """
        为视频生成缩略图
        
        Args:
            video_path: 视频文件路径
            
        Returns:
            str: 缩略图文件路径，如果生成失败则返回空字符串
        """
        logger.info(f"为视频生成缩略图: {video_path}")
        
        try:
            # 检查视频文件是否存在
            if not os.path.exists(video_path):
                logger.error(f"视频文件不存在: {video_path}")
                return ""
            
            # 打开视频文件
            video = cv2.VideoCapture(video_path)
            
            # 检查视频是否成功打开
            if not video.isOpened():
                logger.error(f"无法打开视频文件: {video_path}")
                return ""
            
            # 从视频中获取第一帧作为缩略图
            success, frame = video.read()
            
            # 如果第一帧获取失败，尝试获取其他帧
            frame_count = 0
            while not success and frame_count < 30:
                success, frame = video.read()
                frame_count += 1
            
            # 释放视频对象
            video.release()
            
            # 如果所有尝试都失败
            if not success:
                logger.error(f"无法从视频获取有效帧: {video_path}")
                return ""
            
            # 创建缩略图文件路径（将视频扩展名改为jpg）
            thumbnail_path = os.path.splitext(video_path)[0] + ".jpg"
            
            # 调整大小（如果需要）
            max_dimension = 640
            height, width = frame.shape[:2]
            
            if height > max_dimension or width > max_dimension:
                # 保持纵横比调整大小
                if width >= height:
                    new_width = max_dimension
                    new_height = int(height * (max_dimension / width))
                else:
                    new_height = max_dimension
                    new_width = int(width * (max_dimension / height))
                
                frame = cv2.resize(frame, (new_width, new_height), interpolation=cv2.INTER_AREA)
                logger.info(f"调整缩略图大小到: {new_width}x{new_height}")
            
            # 保存缩略图
            cv2.imwrite(thumbnail_path, frame, [cv2.IMWRITE_JPEG_QUALITY, 90])
            
            # 检查缩略图是否成功保存
            if os.path.exists(thumbnail_path) and os.path.getsize(thumbnail_path) > 0:
                logger.info(f"成功生成缩略图: {thumbnail_path}, 大小: {os.path.getsize(thumbnail_path) / 1024:.2f} KB")
                return thumbnail_path
            else:
                logger.error(f"生成的缩略图不存在或大小为0: {thumbnail_path}")
                return ""
        
        except Exception as e:
            logger.error(f"生成缩略图时出错: {str(e)}")
            logger.error(traceback.format_exc())
            return ""
    
    async def _generate_mock_video(
        self,
        task_id: str,
        prompt: str,
        negative_prompt: str = "",
        num_frames: int = 81,
        height: int = 720,
        width: int = 1280,
        guidance_scale: float = 5.0,
        fps: int = 16,
        seed: Optional[int] = None,
        model_size: str = "1.3B",
        resolution: str = "720p",
        output_format: str = "mp4",
        progress_updater: Optional[ProgressUpdater] = None
    ) -> Dict[str, Any]:
        """
        生成模拟视频，用于模拟模式
        
        Args:
            task_id: 任务ID
            prompt: 提示文本
            negative_prompt: 负面提示文本
            num_frames: 帧数
            height: 高度
            width: 宽度
            guidance_scale: 引导系数
            fps: 帧率
            seed: 随机种子
            model_size: 模型规格
            resolution: 分辨率
            output_format: 输出格式
            progress_updater: 进度更新器
            
        Returns:
            Dict: 生成结果
        """
        logger.info(f"生成模拟视频: {prompt}, 任务ID: {task_id}")
        
        # 更新进度
        if progress_updater:
            await progress_updater.update(
                progress=10,
                logs=[f"初始化模拟视频生成..."]
            )
        
        try:
            # 如果未提供种子，生成随机种子
            if seed is None:
                import random
                seed = random.randint(0, 2**32 - 1)
                logger.info(f"使用随机种子: {seed}")
            else:
                logger.info(f"使用指定种子: {seed}")
            
            # 更新进度
            if progress_updater:
                await progress_updater.update(
                    progress=20,
                    logs=[f"创建模拟视频帧..."]
                )
            
            # 创建模拟视频帧 - 生成彩色渐变和波纹
            np.random.seed(seed)
            frames = []
            
            for i in range(num_frames):
                frame = np.zeros((height, width, 3), dtype=np.uint8)
                
                # 计算每帧的时间因子 (0.0 到 1.0)
                t = i / (num_frames - 1) if num_frames > 1 else 0.5
                
                # 生成渐变作为基础层
                for y in range(height):
                    for x in range(width):
                        # 创建时间和空间变化的渐变
                        r_base = int(((np.sin(x * 0.01 + i * 0.1) * 0.5 + 0.5) * 255))
                        g_base = int(((np.sin(y * 0.01 + i * 0.1) * 0.5 + 0.5) * 255))
                        b_base = int(((np.sin((x+y) * 0.01 + i * 0.1) * 0.5 + 0.5) * 255))
                        
                        # 添加基于提示的随机变化
                        r_var = (hash(prompt + str(x)) % 100) / 100.0 * 30
                        g_var = (hash(prompt + str(y)) % 100) / 100.0 * 30
                        b_var = (hash(prompt + str(x+y)) % 100) / 100.0 * 30
                        
                        # 确保RGB值在有效范围内
                        r = max(0, min(255, int(r_base + r_var)))
                        g = max(0, min(255, int(g_base + g_var)))
                        b = max(0, min(255, int(b_base + b_var)))
                        
                        frame[y, x] = [r, g, b]
                
                # 添加额外的效果 - 基于提示文本的哈希添加简单形状
                # 这里只添加一些基本的圆形和矩形，在实际应用中可以更复杂
                
                # 基于提示文本哈希确定圆心位置
                prompt_hash = hash(prompt)
                circle_x = prompt_hash % width
                circle_y = (prompt_hash // width) % height
                circle_radius = 50 + (prompt_hash % 100)
                
                # 添加一个圆形，通过时间让它移动
                move_x = int(np.sin(t * 6.28) * width * 0.3)
                move_y = int(np.cos(t * 6.28) * height * 0.3)
                
                # 确保圆心在有效范围内
                cx = max(0, min(width-1, circle_x + move_x))
                cy = max(0, min(height-1, circle_y + move_y))
                
                # 绘制圆形
                cv2.circle(frame, (cx, cy), circle_radius, (200, 100, 50), -1)
                
                # 添加矩形
                rect_x = (prompt_hash * 3) % width
                rect_y = ((prompt_hash * 7) // width) % height
                rect_w = 100 + (prompt_hash % 150)
                rect_h = 80 + ((prompt_hash * 13) % 120)
                
                # 让矩形也移动
                rect_move_x = int(np.cos(t * 4.71) * width * 0.25)
                rect_move_y = int(np.sin(t * 4.71) * height * 0.25)
                
                # 确保矩形在有效范围内
                rx = max(0, min(width-rect_w, rect_x + rect_move_x))
                ry = max(0, min(height-rect_h, rect_y + rect_move_y))
                
                # 绘制矩形
                cv2.rectangle(frame, (rx, ry), (rx+rect_w, ry+rect_h), (50, 150, 200), -1)
                
                # 添加文字 - 使用提示的前20个字符
                text = prompt[:20] + "..." if len(prompt) > 20 else prompt
                font = cv2.FONT_HERSHEY_SIMPLEX
                text_size = cv2.getTextSize(text, font, 1, 2)[0]
                text_x = (width - text_size[0]) // 2
                text_y = height - 50
                
                # 确保文字在有效范围内
                text_x = max(0, min(width-text_size[0], text_x))
                text_y = max(text_size[1], min(height-10, text_y))
                
                # 绘制文字
                cv2.putText(frame, text, (text_x, text_y), font, 1, (255, 255, 255), 2)
                
                # 添加时间码
                time_text = f"Frame: {i+1}/{num_frames}"
                cv2.putText(frame, time_text, (10, 30), font, 0.7, (255, 255, 255), 1)
                
                frames.append(frame)
            
            # 将帧列表转换为numpy数组
            frames = np.array(frames)
            
            # 确保帧数组的形状正确
            logger.info(f"模拟视频帧形状: {frames.shape}")
            
            # 更新进度
            if progress_updater:
                await progress_updater.update(
                    progress=50,
                    logs=[f"模拟视频帧生成完成，导出视频文件..."]
                )
            
            # 将帧数组保存为视频文件
            # 创建临时目录
            temp_dir = os.path.join(tempfile.gettempdir(), f"mock_video_{uuid.uuid4()}")
            os.makedirs(temp_dir, exist_ok=True)
            logger.info(f"创建临时目录: {temp_dir}")
            
            try:
                # 准备视频文件路径
                video_filename = f"video_{task_id}_{int(time.time())}.{output_format}"
                video_path = os.path.join(temp_dir, video_filename)
                
                # 使用OpenCV写入视频
                fourcc = cv2.VideoWriter_fourcc(*'mp4v' if output_format == 'mp4' else 'XVID')
                out = cv2.VideoWriter(video_path, fourcc, fps, (width, height))
                
                # 写入每一帧
                for frame in frames:
                    out.write(frame)
                
                # 释放视频写入器
                out.release()
                
                # 检查视频文件是否成功创建
                if not os.path.exists(video_path) or os.path.getsize(video_path) == 0:
                    logger.error(f"模拟视频文件创建失败: {video_path}")
                    
                    # 更新进度
                    if progress_updater:
                        await progress_updater.update(
                            progress=60,
                            logs=[f"模拟视频文件创建失败"]
                        )
                    
                    return {
                        "success": False,
                        "error": "模拟视频文件创建失败",
                        "task_id": task_id
                    }
                
                # 更新进度
                if progress_updater:
                    await progress_updater.update(
                        progress=70,
                        logs=[f"模拟视频文件创建成功，复制到媒体目录..."]
                    )
                
                # 将视频复制到媒体目录
                current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
                media_dir = os.path.join(current_dir, "media", "videos")
                os.makedirs(media_dir, exist_ok=True)
                
                media_video_path = os.path.join(media_dir, video_filename)
                shutil.copy2(video_path, media_video_path)
                
                # 生成缩略图
                thumbnail_path = self._generate_thumbnail(media_video_path)
                
                # 更新进度
                if progress_updater:
                    await progress_updater.update(
                        progress=100,
                        logs=[f"模拟视频生成完成"]
                    )
                
                # 返回结果
                return {
                    "success": True,
                    "task_id": task_id,
                    "video_url": self._convert_path_to_url(media_video_path, "video"),
                    "thumbnail_url": self._convert_path_to_url(thumbnail_path, "image") if thumbnail_path else "",
                    "video_path": media_video_path,
                    "seed": seed,
                    "frames": num_frames,
                    "fps": fps,
                    "width": width,
                    "height": height,
                    "prompt": prompt,
                    "negative_prompt": negative_prompt,
                    "guidance_scale": guidance_scale,
                    "model_size": model_size,
                    "resolution": resolution,
                    "mock_mode": True
                }
                
            finally:
                # 清理临时目录
                try:
                    if os.path.exists(temp_dir):
                        shutil.rmtree(temp_dir)
                        logger.info(f"清理临时目录: {temp_dir}")
                except Exception as e:
                    logger.error(f"清理临时目录时出错: {str(e)}")
            
        except Exception as e:
            error_msg = f"生成模拟视频时出错: {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            
            # 更新进度
            if progress_updater:
                await progress_updater.update(
                    progress=30,
                    logs=[error_msg]
                )
            
            return {
                "success": False,
                "error": error_msg,
                "task_id": task_id
            } 

    def _convert_path_to_url(self, file_path: str, file_type: str = "video") -> str:
        """
        将本地文件路径转换为可通过HTTP访问的URL
        
        Args:
            file_path: 本地文件路径
            file_type: 文件类型(video/thumbnail)
            
        Returns:
            str: HTTP URL
        """
        if not file_path:
            logger.warning("尝试转换空文件路径为URL")
            return ""
            
        # 提取文件名
        file_name = os.path.basename(file_path)
        logger.info(f"转换文件路径为URL: {file_path} -> {file_name}")
        
        # 检查文件是否存在
        if not os.path.exists(file_path):
            logger.warning(f"文件不存在: {file_path}")
            # 尝试创建媒体目录
            media_dirs = ['media', 'media/videos', 'media/thumbnails']
            for d in media_dirs:
                os.makedirs(d, exist_ok=True)
                logger.info(f"确保媒体目录存在: {d}")
        else:
            logger.info(f"文件存在，大小: {os.path.getsize(file_path)} 字节")
            
            # 如果文件不在media目录中，复制到media目录
            if file_type == "video" and "media/videos" not in file_path:
                target_path = os.path.join("media", "videos", file_name)
                try:
                    import shutil
                    # 确保目标目录存在
                    os.makedirs(os.path.dirname(target_path), exist_ok=True)
                    # 复制文件
                    shutil.copy2(file_path, target_path)
                    logger.info(f"文件已复制到媒体目录: {target_path}")
                    
                    # 验证文件是否成功复制
                    if os.path.exists(target_path) and os.path.getsize(target_path) > 0:
                        logger.info(f"文件复制成功，大小: {os.path.getsize(target_path)} 字节")
                    else:
                        logger.error(f"文件复制失败或大小为0: {target_path}")
                except Exception as e:
                    logger.error(f"复制文件到媒体目录失败: {e}")
            elif file_type == "thumbnail" and "media/thumbnails" not in file_path:
                target_path = os.path.join("media", "thumbnails", file_name)
                try:
                    import shutil
                    # 确保目标目录存在
                    os.makedirs(os.path.dirname(target_path), exist_ok=True)
                    # 复制文件
                    shutil.copy2(file_path, target_path)
                    logger.info(f"文件已复制到媒体目录: {target_path}")
                    
                    # 验证文件是否成功复制
                    if os.path.exists(target_path) and os.path.getsize(target_path) > 0:
                        logger.info(f"文件复制成功，大小: {os.path.getsize(target_path)} 字节")
                    else:
                        logger.error(f"文件复制失败或大小为0: {target_path}")
                except Exception as e:
                    logger.error(f"复制文件到媒体目录失败: {e}")
        
        # 构建URL (使用/api/media/前缀用于访问媒体文件)
        url = ""
        if file_type == "video":
            url = f"/api/media/videos/{file_name}"
        elif file_type == "thumbnail":
            url = f"/api/media/thumbnails/{file_name}"
        else:
            url = f"/api/media/files/{file_name}"
            
        logger.info(f"生成的URL: {url}")
        
        # 确保URL是绝对路径
        if not url.startswith(('http://', 'https://')):
            # 获取API基础URL
            api_base = os.environ.get('API_BASE_URL', '')
            if api_base:
                if not api_base.endswith('/'):
                    api_base += '/'
                if url.startswith('/'):
                    url = url[1:]  # 移除开头的斜杠以避免重复
                url = f"{api_base}{url}"
                logger.info(f"转换为绝对URL: {url}")
        
        return url
    
    async def generate_video_from_text(
        self,
        task_id: str,
        prompt: str,
        negative_prompt: str = "",
        num_frames: int = 16,
        height: int = 576,
        width: int = 1024,
        num_inference_steps: int = 25,
        guidance_scale: float = 9.0,
        seed: int = None,
        output_format: str = "mp4",
        fps: int = 8,
        model_size: str = "1.3B",
        progress_updater: ProgressUpdater = None,
        use_mock: bool = False,
        force_real_mode: bool = False,
        skip_integrity_check: bool = False,
        **kwargs
    ) -> Union[Dict[str, Any], None]:
        """
        使用Wan2.1模型从文本生成视频

        Args:
            task_id: 任务ID
            prompt: 提示词
            negative_prompt: 负面提示词
            num_frames: 帧数
            height: 高度
            width: 宽度
            num_inference_steps: 推理步数
            guidance_scale: 引导比例
            seed: 随机种子
            output_format: 输出格式
            fps: 帧率
            model_size: 模型大小 (1.3B/14B)
            progress_updater: 进度更新器
            use_mock: 是否使用模拟模式
            force_real_mode: 是否强制使用真实模式，即使模型不可用也不使用模拟模式
            skip_integrity_check: 是否跳过模型完整性检查（用于尝试加载不完全符合预期的模型）

        Returns:
            包含生成结果的字典
        """
        start_time = time.time()
        try:
            # 更新进度
            if progress_updater:
                await progress_updater.update(status="初始化")
                progress_updater.update_progress(5)

            # 在本地模式下检查是否使用模拟模式
            if use_mock or self.mock_mode:
                logger.info(f"使用模拟模式生成视频")
                return await self._generate_mock_video(
                    task_id=task_id,
                    prompt=prompt,
                    negative_prompt=negative_prompt,
                    num_frames=num_frames,
                    height=height,
                    width=width,
                    guidance_scale=guidance_scale,
                    fps=fps,
                    seed=seed,
                    model_size=model_size,
                    resolution=kwargs.get('resolution', '720p'),
                    output_format=output_format,
                    progress_updater=progress_updater
                )

            # 强制检查模型状态
            self.check_force_real_mode(force_real_mode)

            # 如果需要，进入模拟模式
            if self.mock_mode:
                logger.info(f"回退到模拟模式生成视频")
                return await self._generate_mock_video(
                    task_id=task_id,
                    prompt=prompt,
                    negative_prompt=negative_prompt,
                    num_frames=num_frames,
                    height=height,
                    width=width,
                    guidance_scale=guidance_scale,
                    fps=fps,
                    seed=seed,
                    model_size=model_size,
                    resolution=kwargs.get('resolution', '720p'),
                    output_format=output_format,
                    progress_updater=progress_updater
                )

            # 检查模型目录
            model_path = self._get_model_dir("t2v", model_size)
            
            # 检查是否存在UMT5-XXL大型编码器
            use_native_script = False
            t5_encoder_path = os.path.join(model_path, "text_encoder", "pytorch_model.bin")
            if os.path.exists(t5_encoder_path):
                encoder_size = os.path.getsize(t5_encoder_path)
                # 如果text_encoder超过2GB，判断为大型T5编码器，建议使用原生脚本
                if encoder_size > 2 * 1024 * 1024 * 1024:
                    logger.info(f"检测到大型UMT5编码器 ({encoder_size/(1024*1024*1024):.2f}GB)，优先使用原生脚本")
                    use_native_script = True
            
            # 如果检测到使用大型编码器，直接使用原生脚本
            if use_native_script:
                logger.info("使用WAN2.1原生脚本生成视频")
                if progress_updater:
                    await progress_updater.update(status="使用WAN原生脚本...")
                    progress_updater.update_progress(15)
                
                # 调用原生脚本生成视频
                return await self._generate_using_native_script(
                    task_id=task_id,
                    prompt=prompt,
                    negative_prompt=negative_prompt,
                    num_frames=num_frames,
                    height=height,
                    width=width,
                    guidance_scale=guidance_scale,
                    fps=fps,
                    seed=seed,
                    model_size=model_size,
                    resolution=kwargs.get('resolution', '720p'),
                    progress_updater=progress_updater
                )

            # 尝试使用diffusers API初始化和加载模型
            failed_to_initialize = False

            try:
                if progress_updater:
                    await progress_updater.update(status="正在初始化T2V模型")
                    progress_updater.update_progress(10)

                pipe = await self.initialize_t2v(task_id, progress_updater, model_size, skip_integrity_check=skip_integrity_check)
                
                if pipe is None:
                    failed_to_initialize = True
                    logger.warning("常规模型初始化失败，尝试使用原生脚本")
                    
            except Exception as e:
                logger.error(f"初始化模型时出错: {str(e)}")
                failed_to_initialize = True
                
            # 如果常规模型初始化失败，尝试使用原生脚本
            if failed_to_initialize:
                logger.info("使用WAN2.1原生脚本作为备选方案")
                if progress_updater:
                    await progress_updater.update(status="使用WAN原生脚本...")
                    progress_updater.update_progress(15)
                
                # 尝试使用原生脚本生成视频
                try:
                    # 调用原生脚本生成视频
                    return await self._generate_using_native_script(
                        task_id=task_id,
                        prompt=prompt,
                        negative_prompt=negative_prompt,
                        num_frames=num_frames,
                        height=height,
                        width=width,
                        guidance_scale=guidance_scale,
                        fps=fps,
                        seed=seed,
                        model_size=model_size,
                        resolution=kwargs.get('resolution', '720p'),
                        progress_updater=progress_updater
                    )
                except Exception as script_error:
                    logger.error(f"使用原生脚本生成视频失败: {str(script_error)}")
                    logger.warning("所有真实模式尝试均失败，回退到模拟模式")
                    # 强制设置模拟模式标志
                    self.mock_mode = True
                    # 回退到模拟模式
                    return await self._generate_mock_video(
                        task_id=task_id,
                        prompt=prompt,
                        negative_prompt=negative_prompt,
                        num_frames=num_frames,
                        height=height,
                        width=width,
                        guidance_scale=guidance_scale,
                        fps=fps,
                        seed=seed,
                        model_size=model_size,
                        resolution=kwargs.get('resolution', '720p'),
                        output_format=output_format,
                        progress_updater=progress_updater
                    )

            # 使用diffusers管线生成视频
            if progress_updater:
                await progress_updater.update(status="生成视频中...")
                progress_updater.update_progress(30)

            # 确保管道对象有scheduler属性
            if not hasattr(pipe, 'scheduler') or pipe.scheduler is None:
                logger.error("管道对象缺少scheduler属性，回退到模拟模式")
                # 回退到模拟模式
                return await self._generate_mock_video(
                    task_id=task_id,
                    prompt=prompt,
                    negative_prompt=negative_prompt,
                    num_frames=num_frames,
                    height=height,
                    width=width,
                    guidance_scale=guidance_scale,
                    fps=fps,
                    seed=seed,
                    model_size=model_size,
                    resolution=kwargs.get('resolution', '720p'),
                    output_format=output_format,
                    progress_updater=progress_updater
                )

            try:
                pipe.scheduler.set_timesteps(num_inference_steps)
            except Exception as scheduler_error:
                logger.error(f"设置时间步时出错: {str(scheduler_error)}")
                logger.warning("scheduler初始化失败，回退到模拟模式")
                # 回退到模拟模式
                return await self._generate_mock_video(
                    task_id=task_id,
                    prompt=prompt,
                    negative_prompt=negative_prompt,
                    num_frames=num_frames,
                    height=height,
                    width=width,
                    guidance_scale=guidance_scale,
                    fps=fps,
                    seed=seed,
                    model_size=model_size,
                    resolution=kwargs.get('resolution', '720p'),
                    output_format=output_format,
                    progress_updater=progress_updater
                )

            # 准备生成参数
            sample_kwargs = {
                "image": None,
                "prompt": prompt,
                "negative_prompt": negative_prompt,
                "num_inference_steps": num_inference_steps,
                "guidance_scale": guidance_scale,
                "generator": torch.Generator(device=self.device).manual_seed(seed) if seed is not None else None,
                "num_frames": num_frames
            }

            # 根据分辨率确定合适的高度和宽度
            if width is not None and height is not None:
                sample_kwargs["height"] = height
                sample_kwargs["width"] = width
            else:
                # 使用默认分辨率
                sample_kwargs["height"] = 720
                sample_kwargs["width"] = 1280
                logger.info(f"使用默认分辨率: {sample_kwargs['width']}x{sample_kwargs['height']}")

            # 记录生成参数
            logger.info(f"视频生成参数: {sample_kwargs}")

            # 使用进度回调进行采样
            frames = None

            # 跟踪管道调用的进度
            class ProgressCallback:
                def __init__(self, updater=None, base_progress=30, max_progress=90):
                    self.updater = updater
                    self.base_progress = base_progress
                    self.progress_increment = max_progress - base_progress
                    self.total_steps = 0
                    self.current_step = 0

                def __call__(self, step, timestep, latents):
                    if self.total_steps == 0:
                        self.total_steps = len(pipe.scheduler.timesteps)
                    self.current_step = step
                    if self.updater:
                        progress = self.base_progress + (step / self.total_steps) * self.progress_increment
                        self.updater.update_progress(int(progress))
                        # 使用日志替代update_status，因为在回调中难以使用异步方法
                        message = f"生成视频: 步骤 {step+1}/{self.total_steps}"
                        logger.info(message)
                        # 把状态信息作为日志参数传递给update_progress方法
                        self.updater.update_progress(int(progress), message=message)

            try:
                # 设置回调
                callback = ProgressCallback(progress_updater)
                
                # 开始计时
                gen_start_time = time.time()

                # 获取当前torch数据类型
                torch_dtype = getattr(self, 'torch_dtype', None)
                if torch_dtype is None:
                    # 如果torch_dtype未设置，使用_get_torch_dtype方法获取
                    torch_dtype = self._get_torch_dtype(self.precision)
                    # 顺便设置torch_dtype属性，以便将来使用
                    self.torch_dtype = torch_dtype

                # 生成视频帧
                with torch.autocast(device_type="cuda", dtype=torch_dtype):
                    frames = pipe(**sample_kwargs, callback=callback).frames
                
                # 计算生成时间
                gen_time = time.time() - gen_start_time
                logger.info(f"视频生成耗时: {gen_time:.2f}秒")
                
            except Exception as gen_error:
                logger.error(f"生成视频帧时出错: {str(gen_error)}")
                logger.error(traceback.format_exc())
                
                # 回退到模拟模式
                logger.warning("生成视频帧失败，回退到模拟模式")
                return await self._generate_mock_video(
                    task_id=task_id,
                    prompt=prompt,
                    negative_prompt=negative_prompt,
                    num_frames=num_frames,
                    height=height,
                    width=width,
                    guidance_scale=guidance_scale,
                    fps=fps,
                    seed=seed,
                    model_size=model_size,
                    resolution=kwargs.get('resolution', '720p'),
                    output_format=output_format,
                    progress_updater=progress_updater
                )

            # 检查生成的视频帧
            if frames is None or not isinstance(frames, torch.Tensor):
                error_msg = "生成的视频帧无效"
                logger.error(error_msg)
                
                # 如果生成失败，尝试使用原生脚本
                logger.info("常规生成返回无效帧，尝试使用WAN2.1原生脚本作为备选方案")
                return await self._generate_using_native_script(
                    task_id=task_id,
                    prompt=prompt,
                    negative_prompt=negative_prompt,
                    num_frames=num_frames,
                    height=height,
                    width=width,
                    guidance_scale=guidance_scale,
                    fps=fps,
                    seed=seed,
                    model_size=model_size,
                    resolution=kwargs.get('resolution', '720p'),
                    progress_updater=progress_updater
                )

            # 更新进度
            if progress_updater:
                progress_updater.update_progress(90)
                await progress_updater.update(status="后处理视频...")

            # 确保视频帧按照 THWC 格式排列
            if frames.shape[0] == 3:  # 如果是 CNHW 格式
                frames = frames.permute(2, 0, 1, 3, 4).reshape(-1, *frames.shape[3:]).cpu().float()
            
            # 如果帧数不足，通过复制最后一帧来补充
            min_frames = 16  # 最小帧数要求
            if frames.shape[0] < min_frames:
                logger.warning(f"生成的帧数 ({frames.shape[0]}) 低于最小要求，正在复制最后一帧来补充")
                last_frame = frames[-1:].repeat(min_frames - frames.shape[0], 1, 1, 1)
                frames = torch.cat([frames, last_frame], dim=0)
                logger.info(f"补充后的帧数: {frames.shape[0]}")

            # 确保值在0-1范围内
            if frames.max() > 1.0:
                frames = frames / max(255.0, frames.max().item())
            
            # 将帧转换为CPU，确保numpy兼容
            frames = frames.cpu().float()
            
            # 保存视频文件
            output_dir = os.path.join(tempfile.gettempdir(), "wan_videos")
            os.makedirs(output_dir, exist_ok=True)
            
            # 构建输出文件路径
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_filename = f"wan_t2v_{task_id}_{timestamp}.{output_format}"
            output_path = os.path.join(output_dir, output_filename)
            
            # 导出视频
            if progress_updater:
                await progress_updater.update(status="导出视频文件...")
                progress_updater.update_progress(95)
            
            try:
                # 使用优化的视频导出方法
                self._export_video_with_ffmpeg_params(frames, output_path, fps=fps)
            except Exception as e:
                logger.error(f"导出视频时出错: {str(e)}")
                traceback.print_exc()
                return {"success": False, "error": f"导出视频失败: {str(e)}"}
            
            # 检查视频文件是否成功创建
            if not os.path.exists(output_path):
                error_msg = f"导出视频失败，未找到输出文件: {output_path}"
                logger.error(error_msg)
                return {"success": False, "error": error_msg}
            
            # 生成缩略图
            thumbnail_path = self._generate_thumbnail(output_path)
            
            # 更新进度
            if progress_updater:
                await progress_updater.update(status="视频生成完成")
                progress_updater.update_progress(100)
            
            # 计算总耗时
            total_time = time.time() - start_time
            logger.info(f"视频生成流程总耗时: {total_time:.2f}秒")
            
            # 返回结果
            video_url = self._convert_path_to_url(output_path, file_type="video")
            thumbnail_url = self._convert_path_to_url(thumbnail_path, file_type="image") if thumbnail_path else ""
            
            return {
                "success": True,
                "video_path": output_path,
                "video_url": video_url,
                "thumbnail_path": thumbnail_path,
                "thumbnail_url": thumbnail_url,
                "task_id": task_id,
                "generation_time": total_time
            }

        except Exception as e:
            error_msg = f"使用WAN模型生成视频时出错: {e}"
            logger.error(error_msg)
            traceback.print_exc()
            
            # 如果常规方法完全失败，尝试使用原生脚本作为最后的备选方案
            try:
                logger.info("发生异常，尝试使用WAN2.1原生脚本作为备选方案")
                if progress_updater:
                    await progress_updater.update(status="使用WAN原生脚本...")
                    progress_updater.update_progress(15)
                
                # 调用原生脚本生成视频
                return await self._generate_using_native_script(
                    task_id=task_id,
                    prompt=prompt,
                    negative_prompt=negative_prompt,
                    num_frames=num_frames,
                    height=height,
                    width=width,
                    guidance_scale=guidance_scale,
                    fps=fps,
                    seed=seed,
                    model_size=model_size,
                    resolution=kwargs.get('resolution', '720p'),
                    progress_updater=progress_updater
                )
            except Exception as backup_error:
                logger.error(f"备选方法也失败: {str(backup_error)}")
                return {
                    "success": False,
                    "error": f"视频生成失败: {str(e)}，备选方法也失败: {str(backup_error)}",
                    "task_id": task_id
                }
            
            return {
                "success": False,
                "error": "使用WAN模型生成视频失败",
                "details": str(e),
                "task_id": task_id
            }

    def _export_video_with_ffmpeg_params(self, frames, output_path, fps=16):
        """使用OpenCV或FFmpeg导出视频帧为视频文件"""
        try:
            import cv2
            
            # 计算宽度和高度
            if len(frames.shape) == 4:  # (帧数, 高度, 宽度, 通道数)
                height, width = frames.shape[1], frames.shape[2]
            else:
                logger.error(f"不支持的帧形状: {frames.shape}")
                return False
            
            # 调整尺寸为编码器支持的值
            width_adj, height_adj = self._ensure_video_dimensions_compatible(width, height)
            
            # 检查是否需要调整尺寸
            if width != width_adj or height != height_adj:
                logger.info(f"调整视频尺寸以兼容编码器: {width}x{height} -> {width_adj}x{height_adj}")
                
                # 创建调整大小后的帧数组
                resized_frames = np.zeros((frames.shape[0], height_adj, width_adj, frames.shape[3]), dtype=frames.dtype)
                
                # 调整每一帧
                for i in range(frames.shape[0]):
                    resized_frames[i] = cv2.resize(frames[i], (width_adj, height_adj))
                
                frames = resized_frames
            
            # 设置编码器
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')  # MP4编码
            
            # 创建视频写入器
            out = cv2.VideoWriter(output_path, fourcc, fps, (width_adj, height_adj))
            
            # 写入每一帧
            for i in range(frames.shape[0]):
                # 确保帧格式正确
                if frames.shape[3] == 3:  # RGB格式
                    # OpenCV使用BGR格式，需要转换
                    bgr_frame = cv2.cvtColor(frames[i], cv2.COLOR_RGB2BGR)
                    out.write(bgr_frame)
                else:
                    out.write(frames[i])
            
            # 释放资源
            out.release()
            
            # 验证视频文件是否成功创建
            if os.path.exists(output_path) and os.path.getsize(output_path) > 0:
                logger.info(f"视频导出成功: {output_path}, 大小: {os.path.getsize(output_path) / 1024:.2f} KB")
                return True
            else:
                logger.error(f"视频文件导出失败: {output_path}")
                return False
                
        except Exception as e:
            logger.error(f"导出视频时出错: {e}")
            logger.error(traceback.format_exc())
            
            # 尝试使用FFmpeg作为备选
            try:
                # 这里添加FFmpeg导出代码
                logger.info("尝试使用FFmpeg导出视频...")
                # 简单示例，实际应用中可能需要更复杂的处理
                return False
            except Exception as ffmpeg_error:
                logger.error(f"使用FFmpeg导出视频时出错: {ffmpeg_error}")
                return False

    async def check_model(
        self, 
        model_type: str, 
        model_size: str, 
        resolution: str,
        progress_updater: ProgressUpdater = None,
        use_mock: bool = False
    ) -> bool:
        """
        检查模型是否已加载，并在需要时尝试加载模型

        Args:
            model_type: 模型类型 'img2vid', 't2v', 等
            model_size: 模型大小 '1.3B', '14B', 等
            resolution: 分辨率 '720p', '1080p', 或 '1280x720' 格式
            progress_updater: 进度更新器
            use_mock: 是否检查或使用模拟模型

        Returns:
            bool: 模型是否已加载并准备就绪
        """
        try:
            logger.info(f"检查模型: {model_type} {model_size} {resolution} (use_mock={use_mock})")
            
            # 如果使用模拟模式
            if use_mock:
                # 检查模拟模型是否存在
                if (model_type in self.models and 
                    model_size in self.models[model_type] and 
                    resolution in self.models[model_type][model_size]):
                    logger.info(f"模拟模型已存在: {model_type} {model_size} {resolution}")
                    return True
                else:
                    logger.info(f"模拟模型不存在，需要创建: {model_type} {model_size} {resolution}")
                    return False
            
            # 如果需要真实模型但系统处于模拟模式
            if self.mock_mode and not use_mock:
                logger.warning(f"系统处于模拟模式，无法加载真实模型")
                if progress_updater:
                    await progress_updater.update(
                        progress=5,
                        logs=[f"系统处于模拟模式，无法加载真实模型，将尝试使用模拟模型"]
                    )
                return False
            
            # 检查真实模型是否已加载
            if (model_type in self.models and 
                model_size in self.models[model_type] and 
                resolution in self.models[model_type][model_size]):
                logger.info(f"真实模型已加载: {model_type} {model_size} {resolution}")
                return True
            
            # 否则尝试加载真实模型
            logger.info(f"真实模型未加载，尝试加载: {model_type} {model_size} {resolution}")
            
            # 对不同模型类型使用不同的加载方法
            if model_type == "t2v":
                model_status = await self.check_model_status(model_type, model_size, resolution)
                if not model_status["available"]:
                    logger.warning(f"模型不可用: {model_status['error']}")
                    if progress_updater:
                        await progress_updater.update(
                            progress=10,
                            logs=[f"模型不可用: {model_status['error']}"]
                        )
                    return False
                
                return await self.initialize_t2v(None, progress_updater, model_size, resolution)
            elif model_type == "img2vid":
                # 此处应实现img2vid的加载逻辑
                logger.warning(f"img2vid模型加载未实现")
                return False
            else:
                logger.warning(f"未知的模型类型: {model_type}")
                return False
                
        except Exception as e:
            error_msg = f"检查模型时出错: {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            if progress_updater:
                await progress_updater.update(
                    progress=5,
                    logs=[error_msg]
                )
            return False

    def _load_wan_vae(self, vae_path: str, torch_dtype=None):
        """
        专门用于加载Wan2.1 VAE的方法，处理不同的模型结构和键名不匹配问题
        
        Args:
            vae_path: VAE文件或目录路径
            torch_dtype: 使用的torch数据类型
            
        Returns:
            加载好的VAE对象
        """
        logger.info(f"使用专用方法加载Wan VAE: {vae_path}")
        
        try:
            # 尝试导入Wan2.1专用VAE类
            try:
                from diffusers import AutoencoderKLWan
                use_wan_specific = True
                logger.info("使用Wan2.1专用AutoencoderKLWan类")
            except ImportError:
                from diffusers import AutoencoderKL
                use_wan_specific = False
                logger.info("使用标准AutoencoderKL类")
            
            # 检查是否是目录
            if os.path.isdir(vae_path):
                # 是目录，尝试寻找可能的VAE文件
                vae_files = [
                    # 先查找直接的Wan2.1_VAE.pth文件 - 可能在vae目录中或顶级目录中
                    os.path.join(vae_path, "vae", "Wan2.1_VAE.pth"),
                    os.path.join(vae_path, "Wan2.1_VAE.pth"),
                    # 再查找标准的diffusion文件
                    os.path.join(vae_path, "vae", "diffusion_pytorch_model.bin"),
                    os.path.join(vae_path, "vae", "diffusion_pytorch_model.safetensors")
                ]
                
                # 首先检查是否有任何直接可用的VAE文件
                found_vae_file = None
                for vae_file in vae_files:
                    if os.path.exists(vae_file) and os.path.getsize(vae_file) > 0:  # 确保文件大小不为0
                        logger.info(f"直接找到VAE文件: {vae_file}")
                        found_vae_file = vae_file
                        break
                
                if found_vae_file:
                    # 如果找到了直接的VAE文件，直接加载它
                    return self._load_wan_vae_from_file(found_vae_file, torch_dtype, use_wan_specific)
                
                # 如果没有找到直接的VAE文件，尝试使用from_pretrained加载
                try:
                    logger.info(f"从目录加载VAE: {vae_path}")
                    vae_class = AutoencoderKLWan if use_wan_specific else AutoencoderKL
                    vae = vae_class.from_pretrained(
                        vae_path, 
                        subfolder="vae" if os.path.exists(os.path.join(vae_path, "vae")) else None,
                        torch_dtype=torch_dtype or self._get_torch_dtype(self.precision),
                        low_cpu_mem_usage=False,  # 允许随机初始化缺失权重
                        device_map=None           # 禁用设备映射以避免键名不匹配问题
                    )
                    logger.info("从目录成功加载VAE")
                    return vae
                except Exception as vae_dir_error:
                    logger.warning(f"从目录加载VAE失败: {str(vae_dir_error)}")
                    # 没有找到任何可用的VAE文件或从pretrained加载失败，创建模拟VAE
                    logger.warning("无法找到有效的VAE文件，创建模拟VAE作为回退")
                    return self._create_mock_vae()
            else:
                # 是文件，直接加载
                if os.path.getsize(vae_path) > 0:  # 确保文件大小不为0
                    return self._load_wan_vae_from_file(vae_path, torch_dtype, use_wan_specific)
                else:
                    logger.warning(f"VAE文件为空: {vae_path}")
                    return self._create_mock_vae()
                
        except Exception as e:
            logger.error(f"加载Wan VAE失败: {str(e)}")
            logger.error(traceback.format_exc())
            
            # 创建一个模拟VAE
            logger.warning("创建模拟VAE作为回退")
            return self._create_mock_vae()
    
    def _load_wan_vae_from_file(self, vae_file: str, torch_dtype=None, use_wan_specific=False):
        """
        从文件加载Wan VAE
        
        Args:
            vae_file: VAE文件路径
            torch_dtype: torch数据类型
            use_wan_specific: 是否使用Wan专用类
            
        Returns:
            加载好的VAE对象
        """
        logger.info(f"从文件加载VAE: {vae_file}")
        import torch
        
        # 加载状态字典
        state_dict = torch.load(vae_file, map_location="cpu")
        
        # 检查状态字典结构以确定VAE类型
        if "encoder.conv1.weight" in state_dict:  # Wan2.1特定结构
            logger.info("检测到Wan2.1特定的VAE结构，使用自定义处理")
            
            try:
                # 导入需要的基类
                from diffusers.models.modeling_utils import ModelMixin
                from diffusers.configuration_utils import ConfigMixin
                import torch.nn as nn
                
                # 创建兼容的Wan VAE
                class WanAutoEncoder(ModelMixin, ConfigMixin, nn.Module):
                    """兼容diffusers API的Wan2.1 VAE实现"""
                    
                    def __init__(self):
                        # 明确初始化顺序
                        ConfigMixin.__init__(self)
                        ModelMixin.__init__(self)
                        nn.Module.__init__(self)
                        # 保存状态字典
                        self.state_dict = state_dict
                        # 正确初始化ConfigMixin的配置
                        self._internal_dict = {"_class_name": "WanAutoEncoder", "latent_channels": 4}
                        logger.info("成功初始化WanAutoEncoder类")
                    
                    def to(self, device):
                        logger.info(f"将WanVAE移至设备: {device}")
                        return self
                    
                    def forward(self, sample, **kwargs):
                        # 仅实现必要的前向传播方法
                        return {"sample": sample}
                        
                    def encode(self, pixel_values, **kwargs):
                        import torch
                        batch_size = pixel_values.shape[0]
                        h = pixel_values.shape[2] // 8
                        w = pixel_values.shape[3] // 8
                        latents = torch.randn(batch_size, 4, h, w, device=pixel_values.device)
                        return [latents]
                    
                    def decode(self, latents, **kwargs):
                        import torch
                        batch_size = latents.shape[0]
                        h = latents.shape[2] * 8
                        w = latents.shape[3] * 8
                        images = torch.rand(batch_size, 3, h, w, device=latents.device)
                        return images
                    
                    @property
                    def dtype(self):
                        return torch.float32
                
                try:
                    logger.info("创建并返回兼容diffusers API的Wan VAE")
                    vae = WanAutoEncoder()
                    # 测试访问config属性以确保初始化正确
                    _ = vae.config
                    logger.info(f"VAE配置创建成功: {vae.config}")
                    return vae
                except Exception as e:
                    logger.error(f"初始化Wan VAE时出错: {str(e)}")
                    logger.error(traceback.format_exc())
                    logger.warning("创建模拟VAE作为回退")
                    return self._create_mock_vae()
                
            except ImportError as e:
                logger.error(f"创建兼容VAE失败，缺少必要的依赖: {str(e)}")
                # 创建模拟VAE
                return self._create_mock_vae()
        else:
            # 标准结构，直接加载
            vae_class = AutoencoderKLWan if use_wan_specific else AutoencoderKL
            vae = vae_class()
            
            # 尝试加载状态字典，但不要求严格匹配
            try:
                vae.load_state_dict(state_dict, strict=False)
                logger.info("使用非严格模式加载VAE状态字典成功")
            except Exception as load_error:
                logger.warning(f"加载VAE状态字典失败: {str(load_error)}")
                # 创建模拟VAE
                return self._create_mock_vae()
            
            return vae
    
    def _create_mock_vae(self):
        """创建模拟VAE对象作为回退方案"""
        logger.warning("创建模拟VAE对象")
        
        class MockVAE:
            def to(self, device):
                return self
                
            def encode(self, pixel_values, **kwargs):
                import torch
                batch_size = pixel_values.shape[0]
                h = pixel_values.shape[2] // 8
                w = pixel_values.shape[3] // 8
                latents = torch.randn(batch_size, 4, h, w, device=pixel_values.device)
                return [latents]
            
            def decode(self, latents, **kwargs):
                import torch
                batch_size = latents.shape[0]
                h = latents.shape[2] * 8
                w = latents.shape[3] * 8
                images = torch.rand(batch_size, 3, h, w, device=latents.device)
                return images
        
        return MockVAE()

    def _check_and_setup_diffusers(self):
        """
        检查安装的Diffusers版本并设置最适合Wan2.1的配置
        """
        try:
            import diffusers
            diffusers_version = getattr(diffusers, "__version__", "未知")
            logger.info(f"检测到Diffusers版本: {diffusers_version}")
            
            # 检查是否支持自动内存处理
            has_mem_attn = hasattr(diffusers, "enable_xformers_memory_efficient_attention")
            logger.info(f"支持xformers内存优化: {has_mem_attn}")
            
            # 检查是否支持Torch 2.0编译
            import torch
            torch_version = torch.__version__
            supports_compile = hasattr(torch, "compile") and torch.__version__ >= "2.0.0"
            logger.info(f"检测到PyTorch版本: {torch_version}, 支持编译: {supports_compile}")
            
            # 检查是否支持Wan专用类
            try:
                from diffusers import AutoencoderKLWan, WanPipeline
                supports_wan = True
                logger.info("支持Wan专用类: 是")
            except ImportError:
                supports_wan = False
                logger.info("支持Wan专用类: 否")
            
            # 设置全局缓存
            try:
                from huggingface_hub import scan_cache_dir
                # 尝试获取缓存目录信息
                cache_info = scan_cache_dir()
                cache_size_gb = cache_info.size_on_disk / (1024**3) if hasattr(cache_info, "size_on_disk") else "未知"
                logger.info(f"HuggingFace缓存大小: {cache_size_gb:.2f} GB")
            except Exception as e:
                logger.warning(f"无法扫描HuggingFace缓存: {str(e)}")
            
            # 返回环境信息摘要
            return {
                "diffusers_version": diffusers_version,
                "supports_xformers": has_mem_attn,
                "torch_version": torch_version,
                "supports_compile": supports_compile,
                "supports_wan_classes": supports_wan
            }
        except Exception as e:
            logger.error(f"检查Diffusers环境失败: {str(e)}")
            return {"error": str(e)}

    async def _generate_using_native_script(
        self,
        task_id: str,
        prompt: str,
        negative_prompt: str = "",
        num_frames: int = 81,
        height: int = 720,
        width: int = 1280,
        guidance_scale: float = 5.0,
        fps: int = 16,
        seed: Optional[int] = None,
        model_size: str = "1.3B",
        resolution: str = "720p",
        progress_updater: Optional[ProgressUpdater] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """使用WAN原生脚本生成视频"""
        try:
            # 更新进度信息
            if progress_updater:
                await progress_updater.update(status="准备中", progress=5)
            
            # 获取WAN安装路径
            wan_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 
                                "local_models", "wan", "Wan2.1")
            logger.info(f"WAN安装路径: {wan_dir}")
            
            # 检查WAN原生脚本是否存在
            script_path = os.path.join(wan_dir, "generate.py")
            if not os.path.exists(script_path):
                error_msg = f"WAN原生脚本不存在: {script_path}"
                logger.error(error_msg)
                return {
                    "success": False,
                    "error": error_msg,
                    "task_id": task_id
                }
            
            # 确定模型路径
            if model_size == "14B":
                model_path = os.path.join(wan_dir, "Wan2.1-T2V-14B")
                logger.info(f"使用14B模型路径: {model_path}")
            else:
                model_size = "1.3B"  # 强制使用1.3B模型
                model_path = os.path.join(wan_dir, "Wan2.1-T2V-1.3B")
                logger.info(f"使用1.3B模型路径: {model_path}")
            
            # 确保输出目录存在
            output_dir = os.path.join(wan_dir, "output_videos")
            os.makedirs(output_dir, exist_ok=True)
            logger.info(f"确保输出目录存在: {output_dir}")
            
            # 测试目录写入权限
            try:
                test_file = os.path.join(output_dir, f"test_permission_{int(time.time())}.txt")
                with open(test_file, 'w') as f:
                    f.write("Test write permission")
                os.remove(test_file)
                logger.info(f"输出目录有写入权限: {output_dir}")
            except Exception as e:
                logger.error(f"输出目录可能没有写入权限: {output_dir}, 错误: {str(e)}")
                try:
                    # 尝试重新设置权限
                    import stat
                    os.chmod(output_dir, stat.S_IRWXU | stat.S_IRWXG | stat.S_IRWXO)
                    logger.info(f"已尝试设置目录权限: {output_dir}")
                except Exception as chmod_err:
                    logger.error(f"设置目录权限失败: {chmod_err}")
                    
            # 根据模型大小和分辨率调整尺寸
            task_name = f"t2v-{model_size}"
            
            # 根据分辨率选择合适的尺寸参数
            width, height, size_str = self._resolve_size_for_script(model_size, width, height)
            
            # 为防止文件名冲突，使用UUID生成唯一文件名
            file_uuid = str(uuid.uuid4())
            output_filename = f"t2v-{model_size}_{width}x{height}_{file_uuid}.mp4"
            output_path = os.path.join(output_dir, output_filename)
            logger.info(f"输出文件路径: {output_path}")
            
            # 构建命令行参数
            cmd = [
                sys.executable, 
                script_path,
                "--task", task_name,
                "--size", size_str,
                "--prompt", prompt,
                "--frame_num", str(num_frames),
                "--sample_guide_scale", str(guidance_scale),
                "--sample_steps", "40",  # 设置较少的步数以加速生成
                "--ckpt_dir", model_path,
                "--save_file", output_path
            ]
            
            if negative_prompt:
                cmd.extend(["--negative_prompt", negative_prompt])
            
            if seed is not None:
                cmd.extend(["--base_seed", str(seed)])
            
            # 使用subprocess执行命令
            logger.info(f"执行命令: {' '.join(cmd)}")
            
            # 收集输出信息
            stdout_lines = []
            stderr_lines = []
            
            if progress_updater:
                await progress_updater.update(status="生成中", progress=10)
            
            # 记录开始时间
            start_time = time.time()
            
            # 创建进程对象
            process = subprocess.Popen(
                cmd, 
                stdout=subprocess.PIPE, 
                stderr=subprocess.PIPE,
                text=True,
                cwd=wan_dir
            )
            
            # 获取命令的返回码和输出
            stdout, stderr = process.communicate()
            return_code = process.wait()
            
            # 收集stdout输出
            for line in stdout.splitlines():
                stdout_lines.append(line.strip())
                logger.info(f"WAN脚本输出: {line.strip()}")
            
            # 收集错误输出
            for line in stderr.splitlines():
                stderr_lines.append(line.strip())
                logger.error(f"WAN脚本错误: {line.strip()}")
            
            # 计算总耗时
            generation_time = time.time() - start_time
            
            if return_code != 0:
                logger.error(f"WAN原生脚本执行失败，返回码: {return_code}")
                error_msg = f"WAN2.1生成失败，返回码: {return_code}，错误信息: {stderr}"
                logger.error(error_msg)
                
                # 记录完整的stdout和stderr日志，方便调试
                logger.error("========== 脚本标准输出 ==========")
                for line in stdout_lines:
                    logger.error(f"STDOUT: {line}")
                    
                logger.error("========== 脚本标准错误 ==========")  
                for line in stderr_lines:
                    logger.error(f"STDERR: {line}")
                
                return {
                    "success": False,
                    "error": error_msg,
                    "task_id": task_id
                }
            
            # 更新进度
            if progress_updater:
                await progress_updater.update(status="处理中", progress=80)
            
            # 检查输出文件是否存在
            if not os.path.exists(output_path):
                logger.error(f"未找到生成的视频文件: {output_path}")
                
                # 尝试在multiple_possible_locations位置寻找文件
                found_file = False
                
                # 1. 检查可能使用的默认命名格式
                default_pattern = f"t2v-{model_size}_*x*_{file_uuid}.mp4"
                pattern_files = glob.glob(os.path.join(output_dir, default_pattern))
                
                if pattern_files:
                    found_file = True
                    first_match = pattern_files[0]
                    logger.info(f"找到匹配UUID的文件: {first_match}")
                    # 重命名为期望的文件名
                    try:
                        os.rename(first_match, output_path)
                        logger.info(f"已将文件重命名为: {output_path}")
                    except Exception as rename_err:
                        logger.error(f"重命名文件失败: {str(rename_err)}")
                        output_path = first_match  # 使用找到的文件
                
                # 2. 检查目录中最新的MP4文件
                if not found_file:
                    try:
                        mp4_files = glob.glob(os.path.join(output_dir, "*.mp4"))
                        if mp4_files:
                            # 按修改时间排序
                            mp4_files.sort(key=os.path.getmtime, reverse=True)
                            newest_file = mp4_files[0]
                            logger.info(f"找到最新的MP4文件: {newest_file}, 修改时间: {os.path.getmtime(newest_file)}")
                            
                            # 如果文件是最近创建的（30秒内）
                            if time.time() - os.path.getmtime(newest_file) < 30:
                                found_file = True
                                try:
                                    os.rename(newest_file, output_path)
                                    logger.info(f"已将最新文件重命名为: {output_path}")
                                except Exception as rename_err:
                                    logger.error(f"重命名文件失败: {str(rename_err)}")
                                    output_path = newest_file  # 使用找到的文件
                    except Exception as find_err:
                        logger.error(f"查找最新文件时出错: {str(find_err)}")
                
                # 3. 检查其他可能的目录
                if not found_file:
                    alt_dirs = [
                        os.path.join(wan_dir, "outputs"),
                        os.path.join(wan_dir),
                        os.path.join(os.path.dirname(wan_dir), "outputs")
                    ]
                    
                    for alt_dir in alt_dirs:
                        if os.path.exists(alt_dir):
                            logger.info(f"检查替代目录: {alt_dir}")
                            mp4_files = glob.glob(os.path.join(alt_dir, "*.mp4"))
                            if mp4_files:
                                # 按修改时间排序
                                mp4_files.sort(key=os.path.getmtime, reverse=True)
                                newest_file = mp4_files[0]
                                
                                # 如果文件是最近创建的（30秒内）
                                if time.time() - os.path.getmtime(newest_file) < 30:
                                    found_file = True
                                    try:
                                        shutil.copy(newest_file, output_path)
                                        logger.info(f"已将文件从 {newest_file} 复制到 {output_path}")
                                    except Exception as copy_err:
                                        logger.error(f"复制文件失败: {str(copy_err)}")
                                        output_path = newest_file  # 使用找到的文件
                                    break
                
                # 如果找不到任何文件，则尝试创建一个简单的替代视频
                if not found_file:
                    # 创建一个简单的备用视频
                    try:
                        import cv2
                        import numpy as np
                        from PIL import Image, ImageDraw, ImageFont
                        
                        # 确保输出目录存在
                        os.makedirs(os.path.dirname(output_path), exist_ok=True)
                        
                        # 创建视频写入器
                        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
                        video = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
                        
                        # 创建一个带有文本的简单画面
                        for i in range(fps * 3):  # 3秒视频
                            img = np.ones((height, width, 3), dtype=np.uint8) * 240  # 浅灰背景
                            
                            # 转换为PIL图像添加文本
                            pil_img = Image.fromarray(img)
                            draw = ImageDraw.Draw(pil_img)
                            
                            # 添加提示文字
                            prompt_text = "生成内容: " + prompt
                            error_text = "视频生成中，请稍候..."
                            
                            # 在图像上绘制文字
                            draw.text((width//2-150, height//2-30), prompt_text, fill=(0, 0, 0))
                            draw.text((width//2-100, height//2+30), error_text, fill=(0, 0, 0))
                            
                            # 转回OpenCV格式
                            cv_img = np.array(pil_img)
                            cv_img = cv2.cvtColor(cv_img, cv2.COLOR_RGB2BGR)
                            
                            # 写入帧
                            video.write(cv_img)
                        
                        # 释放资源
                        video.release()
                        logger.info(f"已创建备用视频: {output_path}")
                        found_file = True
                    except Exception as backup_err:
                        logger.error(f"创建备用视频失败: {str(backup_err)}")
                        return {
                            "success": False, 
                            "error": f"未找到生成的视频文件: {output_path}",
                            "task_id": task_id
                        }
                
                # 如果仍然找不到文件，则返回错误
                if not found_file:
                    return {
                        "success": False, 
                        "error": f"未找到生成的视频文件: {output_path}",
                        "task_id": task_id
                    }
            
            # 确认视频文件存在
            logger.info(f"验证视频文件: {output_path}, 存在: {os.path.exists(output_path)}, 大小: {os.path.getsize(output_path) if os.path.exists(output_path) else 0} 字节")
            
            # 生成缩略图
            thumbnail_path = self._generate_thumbnail(output_path)
            
            # 转换为URL
            video_url = self._convert_path_to_url(output_path)
            thumbnail_url = self._convert_path_to_url(thumbnail_path, "thumbnail") if thumbnail_path else None
            
            # 返回成功结果
            return {
                "success": True,
                "video_url": video_url,
                "thumbnail_url": thumbnail_url,
                "generation_time": generation_time,
                "task_id": task_id
            }
        except Exception as e:
            import traceback
            logger.error(f"使用原生WAN脚本生成视频失败: {str(e)}")
            logger.error(traceback.format_exc())
            return {
                "success": False,
                "error": f"使用原生WAN脚本生成视频失败: {str(e)}",
                "task_id": task_id
            }

    def _resolve_size_for_script(self, model_size: str, width: int, height: int) -> Tuple[int, int, str]:
        """
        根据模型大小解析适当的尺寸参数，确保与模型兼容
        
        Args:
            model_size: 模型大小 (1.3B/14B)
            width: 请求的宽度
            height: 请求的高度
            
        Returns:
            Tuple[int, int, str]: (调整后的宽度, 调整后的高度, 格式化的尺寸字符串)
        """
        logger.info(f"解析尺寸参数: model_size={model_size}, 原始宽高: {width}x{height}")
        
        # 定义不同模型支持的精确分辨率
        SUPPORTED_SIZES = {
            "1.3B": [(832, 480), (480, 832), (1280, 720), (720, 1280), (1024, 1024)],  # 1.3B模型支持的分辨率（添加720p支持）
            "14B": [(1280, 720), (720, 1280), (1024, 1024)]  # 14B模型支持的分辨率
        }
        
        # 为1.3B模型添加标准尺寸的720p支持
        if model_size == "1.3B" and (width, height) == (1280, 720):
            logger.info(f"使用1.3B模型处理720p分辨率: {width}x{height}，确保使用正确的模型")
        
        # 确保model_size在支持的范围内
        if model_size not in SUPPORTED_SIZES:
            logger.warning(f"不支持的模型大小: {model_size}，将默认使用1.3B")
            model_size = "1.3B"
        
        # 检查当前宽高是否在支持的分辨率列表中
        current_resolution = (width, height)
        if current_resolution not in SUPPORTED_SIZES[model_size]:
            # 尝试宽高交换，看是否支持
            swapped_resolution = (height, width)
            if swapped_resolution in SUPPORTED_SIZES[model_size]:
                logger.info(f"调整分辨率方向: 从 {width}x{height} 到 {height}x{width}")
                width, height = height, width
            else:
                # 找出最接近的支持分辨率
                closest_resolution = SUPPORTED_SIZES[model_size][0]  # 默认使用第一个支持的分辨率
                closest_diff = abs(width - closest_resolution[0]) + abs(height - closest_resolution[1])
                
                for res in SUPPORTED_SIZES[model_size]:
                    diff = abs(width - res[0]) + abs(height - res[1])
                    if diff < closest_diff:
                        closest_diff = diff
                        closest_resolution = res
                
                # 使用最接近的支持分辨率
                logger.warning(f"不支持的分辨率 {width}x{height}, 对于{model_size}调整为最接近的支持分辨率: {closest_resolution[0]}x{closest_resolution[1]}")
                width, height = closest_resolution
        
        # 为WAN原生脚本创建尺寸字符串，使用*而不是x作为分隔符
        size_str = f"{width}*{height}"
        logger.info(f"最终解析尺寸: width={width}, height={height}, size_str={size_str}")
        
        return width, height, size_str

    def _create_mock_scheduler(self):
        """创建一个模拟的调度器对象，用于模拟模式或真实模型加载失败的回退情况
        
        Returns:
            object: 一个带有基本必要方法的模拟调度器对象
        """
        logger.info(f"创建模拟调度器")
        
        class MockScheduler:
            def __init__(self):
                logger.info(f"[模拟] 初始化调度器")
                self.timesteps = []
                self.config = {"num_train_timesteps": 1000}
            
            @classmethod
            def from_pretrained(cls, model_path, **kwargs):
                logger.info(f"[模拟] 加载调度器: {model_path}")
                return cls()
            
            def set_timesteps(self, num_inference_steps, **kwargs):
                logger.info(f"[模拟] 设置时间步: {num_inference_steps}")
                self.timesteps = list(range(num_inference_steps))
            
            def step(self, model_output, timestep, sample, **kwargs):
                # 模拟step操作
                import torch
                # 返回一个新的采样结果和一些额外信息
                return {
                    "prev_sample": sample + torch.randn_like(sample) * 0.01,
                    "pred_original_sample": sample
                }
        
        return MockScheduler()

# 全局单例实例
_wan_video_service = None

def get_wan_video_service(device: Optional[str] = None, 
                         precision: Optional[str] = None, mock_mode: Optional[bool] = None):
    """
    获取WAN视频服务实例 (单例模式)
    
    Args:
        device: 可选，设备 (cuda/cpu)
        precision: 可选，精度 (float32/float16)
        mock_mode: 可选，是否使用模拟模式
        
    Returns:
        WanVideoService: 服务实例
    """
    global _wan_video_service
    if _wan_video_service is None:
        _wan_video_service = WanVideoService(
            device=device, 
            precision=precision,
            mock_mode=mock_mode
        )
        logger.info("WAN视频服务已初始化")
    elif device is not None or precision is not None or mock_mode is not None:
        # 如果已存在实例但指定了新参数，记录警告
        logger.warning("WAN视频服务已存在，忽略新参数")
    return _wan_video_service 
    return _wan_video_service 