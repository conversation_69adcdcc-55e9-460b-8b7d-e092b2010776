import { ref, onUnmounted } from 'vue';

/**
 * 防抖函数钩子
 * @param {Function} fn - 需要防抖的函数
 * @param {Object} options - 配置选项
 * @param {number} options.wait - 防抖等待时间(ms)
 * @param {boolean} options.leading - 是否在延迟开始前调用函数
 * @param {boolean} options.trailing - 是否在延迟结束后调用函数
 * @param {number} options.maxWait - 最大等待时间(ms)，超过该时间会强制执行
 * @returns {Object} 防抖函数控制对象
 */
export default function useDebounceFn(fn, options = {}) {
  if (typeof fn !== 'function') {
    throw new Error('第一个参数必须是函数');
  }
  
  const {
    wait = 300,
    leading = false,
    trailing = true,
    maxWait = 0
  } = options;
  
  // 上次执行时间
  let lastCallTime = 0;
  // 上次执行的结果
  let lastResult;
  // 定时器
  let timerId = null;
  // 最大等待定时器
  let maxWaitTimer = null;
  // 待执行的参数
  let lastArgs = null;
  // 待执行的上下文
  let lastThis = null;
  // 是否已经执行过
  let hasInvokedLeading = false;
  
  // 防抖后的函数是否正在等待执行
  const isPending = ref(false);
  
  // 获取当前时间
  const now = () => Date.now();
  
  // 执行函数
  const invokeFunc = () => {
    const args = lastArgs;
    const thisArg = lastThis;
    
    // 清除状态
    lastArgs = null;
    lastThis = null;
    hasInvokedLeading = false;
    
    // 执行函数
    isPending.value = true;
    lastResult = fn.apply(thisArg, args);
    isPending.value = false;
    
    return lastResult;
  };
  
  // 处理延迟
  const handleTrailing = () => {
    timerId = null;
    
    // 如果允许尾部调用且有参数，执行函数
    if (trailing && lastArgs) {
      return invokeFunc();
    }
    
    // 清除状态
    lastArgs = null;
    lastThis = null;
    isPending.value = false;
    
    return lastResult;
  };
  
  // 清除最大等待定时器
  const clearMaxWaitTimer = () => {
    if (maxWaitTimer) {
      clearTimeout(maxWaitTimer);
      maxWaitTimer = null;
    }
  };
  
  // 清除定时器
  const clearTimer = () => {
    if (timerId) {
      clearTimeout(timerId);
      timerId = null;
    }
  };
  
  // 取消防抖
  const cancel = () => {
    clearTimer();
    clearMaxWaitTimer();
    
    lastCallTime = 0;
    lastArgs = null;
    lastThis = null;
    timerId = null;
    maxWaitTimer = null;
    isPending.value = false;
  };
  
  // 立即执行
  const flush = () => {
    if (!timerId) return lastResult;
    
    // 执行并清除定时器
    clearTimer();
    clearMaxWaitTimer();
    return handleTrailing();
  };
  
  // 防抖函数
  const debounced = function(...args) {
    const currentTime = now();
    const context = this;
    
    // 保存参数和上下文
    lastArgs = args;
    lastThis = context;
    isPending.value = true;
    
    // 判断是否是首次调用或者超过等待时间
    const isInvoking = lastCallTime === 0;
    
    // 更新调用时间
    lastCallTime = currentTime;
    
    // 如果是首次调用且设置了leading，立即执行
    if (isInvoking && leading && !hasInvokedLeading) {
      hasInvokedLeading = true;
      return invokeFunc();
    }
    
    // 清除现有定时器
    clearTimer();
    
    // 设置新的定时器
    timerId = setTimeout(handleTrailing, wait);
    
    // 如果设置了最大等待时间，创建强制执行的定时器
    if (maxWait > 0 && !maxWaitTimer) {
      maxWaitTimer = setTimeout(() => {
        const shouldInvoke = (now() - lastCallTime) >= maxWait;
        
        if (shouldInvoke) {
          if (timerId) {
            clearTimer();
          }
          lastCallTime = now();
          const result = invokeFunc();
          
          // 清除最大等待定时器
          clearMaxWaitTimer();
          
          return result;
        }
      }, maxWait);
    }
    
    return lastResult;
  };
  
  // 组件卸载时清除定时器
  onUnmounted(() => {
    cancel();
  });
  
  return {
    run: debounced,
    cancel,
    flush,
    isPending
  };
} 