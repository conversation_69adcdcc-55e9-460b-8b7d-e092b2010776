import { ref, reactive, computed, watch, nextTick } from 'vue';

/**
 * 表单钩子
 * @param {Object} options - 配置选项
 * @param {Object} options.defaultValues - 默认表单值
 * @param {Object} options.rules - 表单验证规则
 * @param {Function} options.onSubmit - 提交回调
 * @param {Function} options.onReset - 重置回调
 * @param {Function} options.onChange - 表单值变化回调
 * @param {boolean} options.validateOnChange - 是否在值变化时验证
 * @returns {Object} 表单控制对象
 */
export default function useForm(options = {}) {
  const {
    defaultValues = {},
    rules = {},
    onSubmit,
    onReset,
    onChange,
    validateOnChange = false
  } = options;
  
  // 表单引用
  const formRef = ref(null);
  
  // 表单数据
  const formData = reactive({...defaultValues});
  
  // 表单错误信息
  const formErrors = reactive({});
  
  // 表单已修改状态
  const isDirty = ref(false);
  
  // 表单提交状态
  const isSubmitting = ref(false);
  
  // 表单是否已验证通过
  const isValid = computed(() => Object.keys(formErrors).length === 0);
  
  // 监听表单数据变化
  watch(formData, (newValues, oldValues) => {
    // 标记表单已修改
    if (!isDirty.value && JSON.stringify(newValues) !== JSON.stringify(defaultValues)) {
      isDirty.value = true;
    }
    
    // 值变化时验证
    if (validateOnChange) {
      validateForm();
    }
    
    // 值变化回调
    if (typeof onChange === 'function') {
      onChange(newValues, oldValues);
    }
  }, { deep: true });
  
  // 重置表单
  const resetForm = () => {
    // 重置表单数据
    Object.keys(formData).forEach(key => {
      formData[key] = defaultValues[key] !== undefined ? defaultValues[key] : '';
    });
    
    // 清除错误
    Object.keys(formErrors).forEach(key => {
      delete formErrors[key];
    });
    
    // 重置状态
    isDirty.value = false;
    
    // 重置表单组件
    nextTick(() => {
      if (formRef.value && formRef.value.resetFields) {
        formRef.value.resetFields();
      }
    });
    
    // 执行重置回调
    if (typeof onReset === 'function') {
      onReset();
    }
  };
  
  // 设置表单值
  const setFormValues = (values) => {
    if (!values || typeof values !== 'object') return;
    
    Object.keys(values).forEach(key => {
      if (key in formData) {
        formData[key] = values[key];
      }
    });
  };
  
  // 获取表单值
  const getFormValues = () => {
    return {...formData};
  };
  
  // 设置单个表单值
  const setFieldValue = (field, value) => {
    if (field in formData) {
      formData[field] = value;
    }
  };
  
  // 获取单个表单值
  const getFieldValue = (field) => {
    return formData[field];
  };
  
  // 设置表单错误
  const setFormErrors = (errors) => {
    if (!errors || typeof errors !== 'object') return;
    
    // 清除现有错误
    Object.keys(formErrors).forEach(key => {
      delete formErrors[key];
    });
    
    // 设置新错误
    Object.keys(errors).forEach(key => {
      formErrors[key] = errors[key];
    });
  };
  
  // 设置单个字段错误
  const setFieldError = (field, error) => {
    formErrors[field] = error;
  };
  
  // 清除单个字段错误
  const clearFieldError = (field) => {
    if (field in formErrors) {
      delete formErrors[field];
    }
  };
  
  // 清除所有错误
  const clearErrors = () => {
    Object.keys(formErrors).forEach(key => {
      delete formErrors[key];
    });
  };
  
  // 验证单个字段
  const validateField = (field) => {
    if (!rules || !rules[field]) {
      clearFieldError(field);
      return true;
    }
    
    const fieldRules = Array.isArray(rules[field]) ? rules[field] : [rules[field]];
    const value = formData[field];
    
    for (const rule of fieldRules) {
      // 必填规则
      if (rule.required && (value === undefined || value === null || value === '')) {
        setFieldError(field, rule.message || `${field} 是必填项`);
        return false;
      }
      
      // 正则表达式验证
      if (rule.pattern && !rule.pattern.test(value)) {
        setFieldError(field, rule.message || `${field} 格式不正确`);
        return false;
      }
      
      // 最小长度验证
      if (rule.min !== undefined && value.length < rule.min) {
        setFieldError(field, rule.message || `${field} 长度不能小于 ${rule.min}`);
        return false;
      }
      
      // 最大长度验证
      if (rule.max !== undefined && value.length > rule.max) {
        setFieldError(field, rule.message || `${field} 长度不能大于 ${rule.max}`);
        return false;
      }
      
      // 自定义验证器
      if (typeof rule.validator === 'function') {
        try {
          const result = rule.validator(value, formData);
          if (result === false) {
            setFieldError(field, rule.message || `${field} 验证失败`);
            return false;
          } else if (result instanceof Promise) {
            // 异步验证需要单独处理
            return result.then(valid => {
              if (valid === false) {
                setFieldError(field, rule.message || `${field} 验证失败`);
                return false;
              }
              clearFieldError(field);
              return true;
            });
          }
        } catch (error) {
          setFieldError(field, error.message || `${field} 验证出错`);
          return false;
        }
      }
    }
    
    clearFieldError(field);
    return true;
  };
  
  // 验证整个表单
  const validateForm = async () => {
    if (!rules) {
      clearErrors();
      return true;
    }
    
    // 收集所有字段的验证结果
    const validationPromises = Object.keys(rules).map(async field => {
      const result = validateField(field);
      if (result instanceof Promise) {
        return await result;
      }
      return result;
    });
    
    // 等待所有验证完成
    const results = await Promise.all(validationPromises);
    return results.every(result => result === true);
  };
  
  // 提交表单
  const submitForm = async () => {
    if (isSubmitting.value) return;
    
    isSubmitting.value = true;
    
    try {
      // 验证表单
      const isValid = await validateForm();
      
      if (!isValid) {
        return false;
      }
      
      // 执行提交回调
      if (typeof onSubmit === 'function') {
        return await onSubmit(formData);
      }
      
      return true;
    } catch (error) {
      console.error('表单提交出错:', error);
      return false;
    } finally {
      isSubmitting.value = false;
    }
  };
  
  return {
    formRef,
    formData,
    formErrors,
    isDirty,
    isSubmitting,
    isValid,
    resetForm,
    submitForm,
    validateForm,
    validateField,
    setFormValues,
    getFormValues,
    setFieldValue,
    getFieldValue,
    setFormErrors,
    setFieldError,
    clearFieldError,
    clearErrors
  };
} 