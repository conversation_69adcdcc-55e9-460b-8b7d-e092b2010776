#!/usr/bin/env python3
"""
语音识别服务
支持实时语音识别和音频文件转录
"""

import asyncio
import logging
from typing import Optional, Dict, Any, Callable
import tempfile
import os
from pathlib import Path

logger = logging.getLogger(__name__)

class SpeechRecognitionService:
    """语音识别服务类"""
    
    def __init__(self):
        self.whisper_model = None
        self.is_initialized = False
        
    async def initialize(self):
        """初始化语音识别模型"""
        try:
            # 尝试加载Whisper模型
            import whisper
            
            # 选择合适的模型大小
            model_size = "base"  # base模型平衡了速度和准确性
            
            logger.info(f"🎤 加载Whisper模型: {model_size}")
            self.whisper_model = whisper.load_model(model_size)
            
            self.is_initialized = True
            logger.info("✅ 语音识别服务初始化成功")
            return True
            
        except ImportError:
            logger.warning("⚠️  Whisper未安装，语音识别功能不可用")
            return False
        except Exception as e:
            logger.error(f"❌ 语音识别服务初始化失败: {e}")
            return False
    
    async def transcribe_audio_file(self, audio_path: Path, language: str = "zh") -> Dict[str, Any]:
        """
        转录音频文件
        
        Args:
            audio_path: 音频文件路径
            language: 语言代码 (zh, en, etc.)
        
        Returns:
            转录结果字典
        """
        try:
            if not self.is_initialized:
                await self.initialize()
            
            if not self.whisper_model:
                return {
                    "success": False,
                    "error": "语音识别模型未初始化"
                }
            
            if not audio_path.exists():
                return {
                    "success": False,
                    "error": f"音频文件不存在: {audio_path}"
                }
            
            logger.info(f"🎤 开始转录音频: {audio_path}")
            
            # 使用Whisper转录
            result = await asyncio.to_thread(
                self.whisper_model.transcribe,
                str(audio_path),
                language=language,
                task="transcribe"
            )
            
            text = result.get("text", "").strip()
            
            if text:
                logger.info(f"✅ 转录成功: {text[:50]}...")
                return {
                    "success": True,
                    "text": text,
                    "language": result.get("language", language),
                    "segments": result.get("segments", []),
                    "duration": audio_path.stat().st_size / 1024  # 简化的时长估算
                }
            else:
                return {
                    "success": False,
                    "error": "未检测到语音内容"
                }
                
        except Exception as e:
            logger.error(f"❌ 音频转录失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def transcribe_audio_data(self, audio_data: bytes, format: str = "wav", language: str = "zh") -> Dict[str, Any]:
        """
        转录音频数据
        
        Args:
            audio_data: 音频数据
            format: 音频格式
            language: 语言代码
        
        Returns:
            转录结果字典
        """
        try:
            # 创建临时文件
            with tempfile.NamedTemporaryFile(suffix=f".{format}", delete=False) as temp_file:
                temp_file.write(audio_data)
                temp_path = Path(temp_file.name)
            
            try:
                # 转录临时文件
                result = await self.transcribe_audio_file(temp_path, language)
                return result
            finally:
                # 清理临时文件
                if temp_path.exists():
                    temp_path.unlink()
                    
        except Exception as e:
            logger.error(f"❌ 音频数据转录失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def detect_language(self, audio_path: Path) -> str:
        """
        检测音频语言
        
        Args:
            audio_path: 音频文件路径
        
        Returns:
            语言代码
        """
        try:
            if not self.is_initialized:
                await self.initialize()
            
            if not self.whisper_model:
                return "zh"  # 默认中文
            
            # 使用Whisper检测语言
            audio = whisper.load_audio(str(audio_path))
            audio = whisper.pad_or_trim(audio)
            
            mel = whisper.log_mel_spectrogram(audio).to(self.whisper_model.device)
            _, probs = self.whisper_model.detect_language(mel)
            
            detected_language = max(probs, key=probs.get)
            logger.info(f"🌐 检测到语言: {detected_language}")
            
            return detected_language
            
        except Exception as e:
            logger.error(f"❌ 语言检测失败: {e}")
            return "zh"  # 默认中文

class WebRTCVoiceRecognition:
    """WebRTC实时语音识别"""
    
    def __init__(self, speech_service: SpeechRecognitionService):
        self.speech_service = speech_service
        self.is_recording = False
        self.audio_buffer = []
        self.sample_rate = 16000
        self.chunk_duration = 2.0  # 2秒一个块
        
    async def start_recording(self, on_result: Callable[[str], None] = None):
        """开始录音识别"""
        try:
            self.is_recording = True
            self.audio_buffer = []
            
            logger.info("🎤 开始实时语音识别")
            
            # 这里可以集成WebRTC或其他实时音频捕获
            # 目前使用模拟实现
            await self._simulate_voice_input(on_result)
            
        except Exception as e:
            logger.error(f"❌ 开始录音失败: {e}")
            self.is_recording = False
    
    async def stop_recording(self):
        """停止录音"""
        self.is_recording = False
        logger.info("🛑 停止语音识别")
    
    async def _simulate_voice_input(self, on_result: Callable[[str], None] = None):
        """模拟语音输入（用于测试）"""
        test_phrases = [
            "你好，我想要学习英语",
            "请帮我翻译这段文字",
            "我需要一些编程方面的帮助",
            "能否推荐一些旅游景点",
            "我想了解一些健康知识"
        ]
        
        for phrase in test_phrases:
            if not self.is_recording:
                break
            
            await asyncio.sleep(3)  # 模拟说话间隔
            
            if on_result:
                on_result(phrase)
            
            logger.info(f"🎤 模拟语音识别: {phrase}")

class VoiceCommandProcessor:
    """语音命令处理器"""
    
    def __init__(self):
        self.commands = {
            "开始录音": "start_recording",
            "停止录音": "stop_recording",
            "发送消息": "send_message",
            "清空对话": "clear_conversation",
            "切换智能体": "switch_agent",
            "打开设置": "open_settings",
            "关闭设置": "close_settings"
        }
    
    def process_command(self, text: str) -> Dict[str, Any]:
        """
        处理语音命令
        
        Args:
            text: 识别的文本
        
        Returns:
            命令处理结果
        """
        text = text.strip()
        
        # 检查是否是命令
        for command_text, command_action in self.commands.items():
            if command_text in text:
                return {
                    "is_command": True,
                    "action": command_action,
                    "original_text": text,
                    "command_text": command_text
                }
        
        # 不是命令，返回普通文本
        return {
            "is_command": False,
            "text": text
        }

# 全局实例
speech_recognition_service = SpeechRecognitionService()
voice_command_processor = VoiceCommandProcessor()
