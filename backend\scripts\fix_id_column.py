#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
修复digital_humans表中的id列类型
将id列从INTEGER类型修改为VARCHAR类型
"""

import os
import sys
import logging
import traceback
from sqlalchemy import create_engine, text, inspect

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 获取数据库连接URL
DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://postgres:langpro8@localhost:5432/ai_platform")

def fix_id_column():
    """修复digital_humans表中id列的类型，从INTEGER改为VARCHAR"""
    logger.info("开始修复digital_humans表id列类型...")
    
    try:
        # 创建数据库引擎
        engine = create_engine(DATABASE_URL)
        
        # 检查表结构
        inspector = inspect(engine)
        columns = inspector.get_columns('digital_humans')
        
        id_column = None
        for col in columns:
            if col['name'] == 'id':
                id_column = col
                break
        
        if not id_column:
            logger.error("未找到id列")
            return False
        
        # 检查id列的类型
        id_type = str(id_column['type']).lower()
        logger.info(f"当前id列类型: {id_type}")
        
        # 如果已经是字符串类型，无需修改
        if 'varchar' in id_type or 'char' in id_type or 'text' in id_type or 'string' in id_type:
            logger.info("id列已经是字符串类型，无需修改")
            return True
        
        # 如果是整数类型，需要修改
        if 'int' in id_type:
            try:
                logger.info("正在修改id列类型为VARCHAR...")
                
                # 创建连接
                conn = engine.connect()
                
                # 开始事务
                trans = conn.begin()
                
                try:
                    # 检查是否有主键约束
                    pk_constraint = conn.execute(text("""
                        SELECT constraint_name FROM information_schema.table_constraints
                        WHERE table_name = 'digital_humans' AND constraint_type = 'PRIMARY KEY'
                    """)).fetchone()
                    
                    pk_name = pk_constraint[0] if pk_constraint else None
                    
                    # 如果有主键约束，先删除
                    if pk_name:
                        logger.info(f"删除主键约束: {pk_name}")
                        conn.execute(text(f"ALTER TABLE digital_humans DROP CONSTRAINT {pk_name}"))
                    
                    # 添加临时列
                    logger.info("添加临时id列(VARCHAR类型)")
                    conn.execute(text("ALTER TABLE digital_humans ADD COLUMN id_temp VARCHAR(64)"))
                    
                    # 更新临时列的值，将整数转为字符串
                    logger.info("将整数ID转换为字符串")
                    conn.execute(text("UPDATE digital_humans SET id_temp = id::VARCHAR"))
                    
                    # 删除原id列
                    logger.info("删除原id列")
                    conn.execute(text("ALTER TABLE digital_humans DROP COLUMN id"))
                    
                    # 重命名临时列
                    logger.info("重命名临时列为id")
                    conn.execute(text("ALTER TABLE digital_humans RENAME COLUMN id_temp TO id"))
                    
                    # 重新添加主键约束
                    logger.info("添加主键约束")
                    conn.execute(text("ALTER TABLE digital_humans ADD PRIMARY KEY (id)"))
                    
                    # 重新添加索引
                    logger.info("添加索引")
                    conn.execute(text("CREATE INDEX IF NOT EXISTS idx_dh_id ON digital_humans (id)"))
                    
                    # 提交事务
                    trans.commit()
                    logger.info("成功修改id列类型为VARCHAR(64)")
                    return True
                    
                except Exception as e:
                    # 回滚事务
                    trans.rollback()
                    logger.error(f"修改id列类型失败: {e}")
                    traceback.print_exc()
                    return False
                finally:
                    # 关闭连接
                    conn.close()
            except Exception as e:
                logger.error(f"连接数据库失败: {e}")
                traceback.print_exc()
                return False
        else:
            logger.warning(f"id列类型({id_type})不是整数也不是字符串，需要手动检查")
            return False
    except Exception as e:
        logger.error(f"修复过程中出错: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = fix_id_column()
    if success:
        logger.info("✅ 修复digital_humans表id列类型成功")
    else:
        logger.error("❌ 修复digital_humans表id列类型失败") 