"""
视频生成任务
使用现有的数字人模型生成会说话的视频
"""

from app.core.celery_app import celery_app, DatabaseTask
from app.services.model_manager import model_manager
import logging
import os
import sys
import subprocess
import tempfile
from datetime import datetime
import json
import shutil

logger = logging.getLogger(__name__)

@celery_app.task(bind=True, base=DatabaseTask, name="generate_talking_video")
async def generate_talking_video(self, results, digital_human_id: int, task_id: str, input_data: dict):
    """
    生成会说话的视频
    整合图像和语音结果，使用数字人模型生成最终视频
    """
    try:
        logger.info(f"开始生成会说话的视频: ID={digital_human_id}")
        
        # 更新进度
        self.update_task_progress(task_id, 55, "正在生成会说话的视频...")
        
        # 解析输入结果
        image_result = results[0] if len(results) > 0 else {}
        voice_result = results[1] if len(results) > 1 else {}
        
        # 验证输入
        if not image_result.get("processed_image_path"):
            raise ValueError("缺少处理后的图像")
        if not voice_result.get("voice_sample_path"):
            raise ValueError("缺少语音样本")
        
        # 选择最佳的生成模型
        model_choice = model_manager.get_recommended_model({"quality": input_data.get("quality", "standard")})
        logger.info(f"选择的模型: {model_choice}")

        if not model_choice:
            raise ValueError("没有可用的数字人生成模型")

        # 准备输出目录
        output_dir = f"storage/temp/video_{digital_human_id}_{task_id}"
        os.makedirs(output_dir, exist_ok=True)

        # 根据选择的模型生成视频
        if model_choice == "musetalk":
            video_result = await model_manager.generate_with_musetalk(
                image_result["processed_image_path"],
                voice_result["voice_sample_path"],
                output_dir,
                {"fps": 25, "batch_size": 8}
            )
        elif model_choice == "sadtalker":
            video_result = await model_manager.generate_with_sadtalker(
                image_result["processed_image_path"],
                voice_result["voice_sample_path"],
                output_dir,
                {"preprocess": "full", "enhancer": "gfpgan"}
            )
        elif model_choice == "liveportrait":
            video_result = await model_manager.generate_with_liveportrait(
                image_result["processed_image_path"],
                voice_result["voice_sample_path"],
                output_dir
            )
        elif model_choice == "wav2lip":
            output_path = os.path.join(output_dir, "result.mp4")
            video_result = await model_manager.generate_with_wav2lip(
                image_result["processed_image_path"],
                voice_result["voice_sample_path"],
                output_path,
                {"static": True}
            )
        else:
            raise ValueError(f"不支持的模型: {model_choice}")
        
        # 后处理视频
        self.update_task_progress(task_id, 85, "正在后处理视频...")
        final_video_path = post_process_video(video_result["video_path"], digital_human_id)
        
        result = {
            "success": True,
            "digital_human_id": digital_human_id,
            "task_id": task_id,
            "video_url": f"/static/generated/{os.path.basename(final_video_path)}",
            "video_path": final_video_path,
            "model_used": model_choice,
            "generation_info": video_result,
            "input_image": image_result["processed_image_path"],
            "input_audio": voice_result["voice_sample_path"],
            "processing_steps": [
                f"使用{model_choice}模型生成视频",
                "视频后处理完成",
                "视频保存完成"
            ]
        }
        
        logger.info(f"会说话的视频生成完成: {result}")
        return result
        
    except Exception as e:
        logger.error(f"视频生成失败: {str(e)}")
        raise

# 旧的生成函数已移至model_manager中，这里只保留后处理函数

def post_process_video(video_path: str, digital_human_id: int) -> str:
    """后处理视频"""
    try:
        # 确保输出目录存在
        output_dir = "storage/generated"
        os.makedirs(output_dir, exist_ok=True)
        
        # 生成最终文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        final_filename = f"digital_human_{digital_human_id}_{timestamp}.mp4"
        final_path = os.path.join(output_dir, final_filename)
        
        # 使用ffmpeg进行视频优化
        cmd = [
            "ffmpeg",
            "-i", video_path,
            "-c:v", "libx264",
            "-preset", "medium",
            "-crf", "23",
            "-c:a", "aac",
            "-b:a", "128k",
            "-movflags", "+faststart",
            "-y",  # 覆盖输出文件
            final_path
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode != 0:
            logger.warning(f"ffmpeg优化失败，使用原始视频: {result.stderr}")
            # 如果ffmpeg失败，直接复制原始视频
            shutil.copy2(video_path, final_path)
        
        return final_path
        
    except Exception as e:
        logger.error(f"视频后处理失败: {str(e)}")
        # 如果后处理失败，直接复制原始视频
        output_dir = "storage/generated"
        os.makedirs(output_dir, exist_ok=True)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        final_filename = f"digital_human_{digital_human_id}_{timestamp}.mp4"
        final_path = os.path.join(output_dir, final_filename)
        shutil.copy2(video_path, final_path)
        return final_path
