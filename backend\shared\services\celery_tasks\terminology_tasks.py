"""
术语智能采集Celery任务模块
负责术语采集的异步处理任务
"""

import logging
import json
import time
import random
import traceback
from typing import List, Dict, Any, Optional
from datetime import datetime
import re
import requests
from bs4 import BeautifulSoup
import asyncio
import os
import sys
import jieba
import uuid

from sqlalchemy.orm import Session
from sqlalchemy import desc
from celery.app.task import Task  # 添加Task基类导入

from services.celery_app import app as celery_app
from models.terminology_collection import TerminologyCollectionTask, TerminologyCollectionResult
from utils.db import SessionLocal

# 创建日志记录器
logger = logging.getLogger("terminology_tasks")

# 爬虫请求头
USER_AGENTS = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36",
    "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1"
]

# 导入新的术语提取模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))
from terminology_extractor import TerminologyExtractor

# 注意: 已经从services.celery_app导入了celery_app，无需重复导入

# 添加领域检测函数，避免依赖backend.utils
def detect_domain(text: str) -> str:
    """
    检测文本所属的领域
    
    Args:
        text: 输入文本
        
    Returns:
        str: 领域名称，如果无法确定则返回"通用"
    """
    # 汽车领域关键词
    auto_keywords = ["汽车", "车", "发动机", "轮胎", "底盘", "变速箱", "刹车", "方向盘", "车轮", 
                    "悬挂", "油耗", "排量", "涡轮", "电动汽车", "混合动力", "自动驾驶"]
    
    # 检查文本中是否包含汽车领域关键词
    for keyword in auto_keywords:
        if keyword in text:
            return "汽车"
    
    return "通用"

def determine_domain(task_name: str, keywords: str) -> str:
    """
    根据任务名称和关键词确定领域
    
    Args:
        task_name: 任务名称
        keywords: 关键词字符串
        
    Returns:
        str: 领域名称
    """
    # 合并任务名称和关键词
    text = f"{task_name} {keywords}"
    
    # 检测领域
    try:
        # 汽车领域的关键词匹配
        auto_keywords = ["汽车", "车", "发动机", "轮胎", "底盘", "变速箱", "刹车", "方向盘", 
                        "SUV", "轿车", "汽车专业术语", "车辆", "汽车领域", "汽车术语", "新能源汽车",
                        "电动汽车", "混合动力", "四驱", "涡轮", "动力", "传动", "排量"]
        for kw in auto_keywords:
            if kw in text:
                return "汽车"
                
        # 医疗领域的关键词匹配
        medical_keywords = ["医疗", "医院", "医生", "患者", "病人", "疾病", "症状", "治疗", "药物", "手术",
                           "医学", "医学术语", "医疗术语", "诊断", "检查", "护理", "康复", "健康"]
        for kw in medical_keywords:
            if kw in text:
                return "医疗"
                
        # 法律领域的关键词匹配
        legal_keywords = ["法律", "法规", "法院", "律师", "诉讼", "合同", "起诉", "被告", "原告", "判决",
                         "法律术语", "法律条款", "法务", "民法", "刑法", "行政法", "仲裁", "诉讼"]
        for kw in legal_keywords:
            if kw in text:
                return "法律"
                
        # 技术领域的关键词匹配
        tech_keywords = ["技术", "软件", "编程", "开发", "算法", "数据", "网络", "系统", "应用", "程序",
                        "IT", "IT术语", "计算机", "技术术语", "人工智能", "数据库", "云计算", "信息技术"]
        for kw in tech_keywords:
            if kw in text:
                return "IT"
        
        # 如果没有明确匹配，尝试更复杂的分析
        # 统计各领域关键词出现的次数
        domain_scores = {
            "汽车": 0,
            "医疗": 0,
            "法律": 0,
            "IT": 0
        }
        
        # 对文本进行分词
        words = jieba.lcut(text)
        
        # 计算各领域关键词出现的次数
        for word in words:
            if word in auto_keywords:
                domain_scores["汽车"] += 1
            if word in medical_keywords:
                domain_scores["医疗"] += 1
            if word in legal_keywords:
                domain_scores["法律"] += 1
            if word in tech_keywords:
                domain_scores["IT"] += 1
        
        # 找出得分最高的领域
        max_domain = max(domain_scores.items(), key=lambda x: x[1])
        
        # 如果有明显的领域倾向，返回该领域
        if max_domain[1] > 0:
            return max_domain[0]
        
        # 如果没有找到明确的领域，返回"通用"
        return "通用"
    except Exception as e:
        logger.error(f"[术语采集任务] 确定领域出错: {str(e)}")
        # 如果出错，进行简单的关键词匹配
        if "汽车" in text or "车" in text:
            return "汽车"
        elif "医" in text or "病" in text:
            return "医疗"
        elif "法" in text or "律" in text:
            return "法律"
        elif "技术" in text or "软件" in text or "编程" in text or "IT" in text:
            return "IT"
        else:
            return "通用"

class TerminologyExtractionTask(Task):
    """术语提取任务基类"""
    
    _extractors = {}  # 缓存不同领域的提取器实例
    
    def get_extractor(self, domain: str) -> TerminologyExtractor:
        """获取指定领域的术语提取器"""
        if domain not in self._extractors:
            # 为不同领域创建不同的提取器实例
            self._extractors[domain] = TerminologyExtractor()
            logger.info(f"Created new terminology extractor for domain: {domain}")
        return self._extractors[domain]

@celery_app.task(name="terminology_tasks.collect_terms", bind=True)
def collect_terms_task(self, task_id: str):
    """
    术语采集Celery任务
    通过任务ID从数据库获取任务信息，然后执行术语采集
    """
    logger.info(f"[术语采集任务] 开始执行 - 任务ID: {task_id}")
    
    # 创建数据库会话
    db = SessionLocal()
    
    try:
        # 获取任务
        task = db.query(TerminologyCollectionTask).filter(TerminologyCollectionTask.task_id == task_id).first()
        
        if not task:
            logger.error(f"[术语采集任务] 任务不存在 - 任务ID: {task_id}")
            return {"status": "failed", "error": "Task not found"}
        
        # 更新任务状态为处理中
        task.status = "processing"
        db.commit()
        db.refresh(task)  # 刷新任务对象，确保状态更新成功
        
        # 更新Celery任务状态
        self.update_state(state="PROGRESS", meta={"progress": 0, "status": "processing"})
        
        # 根据任务模式执行不同的采集逻辑
        if task.mode == "smart":
            result = process_smart_collection(db, task, self)
        elif task.mode == "single":
            result = process_single_site_collection(db, task, self)
        elif task.mode == "list":
            result = process_url_list_collection(db, task, self)
        else:
            logger.error(f"[术语采集任务] 不支持的采集模式 - 模式: {task.mode}")
            task.status = "failed"
            task.error_message = f"不支持的采集模式: {task.mode}"
            db.commit()
            return {"status": "failed", "error": f"Unsupported collection mode: {task.mode}"}
        
        # 如果任务状态仍为处理中，则更新为已完成
        if task.status == "processing":
            task.status = "completed"
            task.progress = 100
            task.completed_at = datetime.now()
            db.commit()
            
            logger.info(f"[术语采集任务] 任务完成 - 任务ID: {task_id}")
            
            # 查询采集结果数量
            result_count = db.query(TerminologyCollectionResult).filter(
                TerminologyCollectionResult.task_id == task_id
            ).count()
            
            logger.info(f"[术语采集任务] 任务完成 - 结果数量: {result_count}")
            
            # 更新Celery任务状态为成功
            self.update_state(state="SUCCESS", meta={"progress": 100, "status": "completed", "result_count": result_count})
            
            return {
                "status": "completed", 
                "progress": 100, 
                "result_count": result_count,
                "task_id": task_id
            }
        
        return {"status": task.status, "progress": task.progress, "error": task.error_message}
    
    except Exception as e:
        logger.error(f"[术语采集任务] 处理任务出错 - 任务ID: {task_id}, 错误: {str(e)}")
        logger.error(traceback.format_exc())
        
        # 更新任务状态为失败
        try:
            task = db.query(TerminologyCollectionTask).filter(TerminologyCollectionTask.task_id == task_id).first()
            if task:
                task.status = "failed"
                task.error_message = str(e)
                db.commit()
                logger.info(f"[术语采集任务] 已将任务状态更新为失败 - 任务ID: {task_id}")
            
            # 更新Celery任务状态为失败
            self.update_state(state="FAILURE", meta={"status": "failed", "error": str(e)})
            
            return {"status": "failed", "error": str(e)}
        except Exception as update_error:
            logger.error(f"[术语采集任务] 更新任务状态失败 - 任务ID: {task_id}, 错误: {str(update_error)}")
            return {"status": "failed", "error": str(e), "update_error": str(update_error)}
    finally:
        # 关闭数据库会话
        db.close()

def process_smart_collection(db: Session, task: TerminologyCollectionTask, celery_task=None):
    """
    智能全网采集模式
    """
    logger.info(f"[术语采集任务] 智能全网采集 - 任务ID: {task.task_id}, 关键词: {task.keywords}")
    
    try:
        # 导入异步运行时和术语采集服务
        from services.terminology_collection_service import process_smart_collection as service_process_smart_collection
        
        # 步骤1: 创建事件循环
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            # 如果没有事件循环，创建一个新的
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
        
        # 步骤2: 调用术语采集服务的异步函数
        logger.info(f"[术语采集任务] 调用术语采集服务的异步函数 - 任务ID: {task.task_id}")
        
        try:
            # 运行异步函数
            loop.run_until_complete(service_process_smart_collection(db, task))
            
            # 提交任务进度更新
            db.refresh(task)
            
            logger.info(f"[术语采集任务] 智能全网采集完成 - 任务ID: {task.task_id}, 状态: {task.status}, 进度: {task.progress}")
            
            if task.status == "completed":
                if celery_task:
                    celery_task.update_state(state="SUCCESS", meta={"progress": 100, "status": "completed"})
                return {"status": "completed", "progress": 100}
            else:
                if celery_task:
                    celery_task.update_state(state="FAILURE", meta={"status": "failed", "error": "术语采集服务执行失败"})
                return {"status": "failed", "error": "术语采集服务执行失败"}
                
        except Exception as e:
            logger.error(f"[术语采集任务] 调用术语采集服务出错 - 任务ID: {task.task_id}, 错误: {str(e)}")
            logger.error(traceback.format_exc())
            
            # 如果服务执行失败，尝试使用生成示例结果的方式
            logger.warning(f"[术语采集任务] 尝试使用示例生成 - 任务ID: {task.task_id}")
            
            # 更新任务进度
            task.progress = 80
            db.commit()
            if celery_task:
                celery_task.update_state(state="PROGRESS", meta={"progress": 80, "status": "processing", "step": "生成示例术语"})
            
            # 生成示例结果
            generate_sample_results(db, task)
            
            task.progress = 100
            task.status = "completed"
            task.completed_at = datetime.now()
            db.commit()
            
            if celery_task:
                celery_task.update_state(state="SUCCESS", meta={"progress": 100, "status": "completed"})
            
            logger.info(f"[术语采集任务] 智能全网采集（示例模式）完成 - 任务ID: {task.task_id}")
            
            return {"status": "completed", "progress": 100}
    
    except Exception as e:
        logger.error(f"[术语采集任务] 智能全网采集出错 - 任务ID: {task.task_id}, 错误: {str(e)}")
        logger.error(traceback.format_exc())
        raise

def process_single_site_collection(db: Session, task: TerminologyCollectionTask, celery_task=None):
    """
    单一站点采集模式
    """
    logger.info(f"[术语采集任务] 单一站点采集 - 任务ID: {task.task_id}, 目标网站: {task.target_website}")
    
    try:
        # 检查目标网站是否有效
        if not task.target_website:
            raise ValueError("目标网站URL不能为空")
        
        # 导入异步运行时和术语采集服务
        from services.terminology_collection_service import process_single_site_collection as service_process_single_site_collection
        
        # 步骤1: 创建事件循环
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            # 如果没有事件循环，创建一个新的
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
        
        # 步骤2: 调用术语采集服务的异步函数
        logger.info(f"[术语采集任务] 调用术语采集服务的异步函数 - 任务ID: {task.task_id}")
        
        try:
            # 运行异步函数
            loop.run_until_complete(service_process_single_site_collection(db, task))
            
            # 提交任务进度更新
            db.refresh(task)
            
            logger.info(f"[术语采集任务] 单一站点采集完成 - 任务ID: {task.task_id}, 状态: {task.status}, 进度: {task.progress}")
            
            if task.status == "completed":
                if celery_task:
                    celery_task.update_state(state="SUCCESS", meta={"progress": 100, "status": "completed"})
                return {"status": "completed", "progress": 100}
            else:
                if celery_task:
                    celery_task.update_state(state="FAILURE", meta={"status": "failed", "error": "术语采集服务执行失败"})
                return {"status": "failed", "error": "术语采集服务执行失败"}
                
        except Exception as e:
            logger.error(f"[术语采集任务] 调用术语采集服务出错 - 任务ID: {task.task_id}, 错误: {str(e)}")
            logger.error(traceback.format_exc())
            
            # 如果服务执行失败，尝试使用生成示例结果的方式
            logger.warning(f"[术语采集任务] 尝试使用示例生成 - 任务ID: {task.task_id}")
            
            # 更新任务进度
            task.progress = 75
            db.commit()
            if celery_task:
                celery_task.update_state(state="PROGRESS", meta={"progress": 75, "status": "processing", "step": "生成示例术语"})
            
            # 生成示例结果
            generate_sample_results(db, task, source_url=task.target_website)
            
            task.progress = 100
            task.status = "completed"
            task.completed_at = datetime.now()
            db.commit()
            
            if celery_task:
                celery_task.update_state(state="SUCCESS", meta={"progress": 100, "status": "completed"})
            
            logger.info(f"[术语采集任务] 单一站点采集（示例模式）完成 - 任务ID: {task.task_id}")
            
            return {"status": "completed", "progress": 100}
    
    except Exception as e:
        logger.error(f"[术语采集任务] 单一站点采集出错 - 任务ID: {task.task_id}, 错误: {str(e)}")
        logger.error(traceback.format_exc())
        raise

def process_url_list_collection(db: Session, task: TerminologyCollectionTask, celery_task=None):
    """
    链接列表采集模式
    """
    logger.info(f"[术语采集任务] 链接列表采集 - 任务ID: {task.task_id}")
    
    try:
        # 检查URL列表是否有效
        url_list = task.url_list
        if not url_list or not isinstance(url_list, list) or len(url_list) == 0:
            raise ValueError("URL列表不能为空")
        
        logger.info(f"[术语采集任务] URL列表数量: {len(url_list)}")
        
        # 导入异步运行时和术语采集服务
        from services.terminology_collection_service import process_url_list_collection as service_process_url_list_collection
        
        # 步骤1: 创建事件循环
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            # 如果没有事件循环，创建一个新的
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
        
        # 步骤2: 调用术语采集服务的异步函数
        logger.info(f"[术语采集任务] 调用术语采集服务的异步函数 - 任务ID: {task.task_id}")
        
        try:
            # 运行异步函数
            loop.run_until_complete(service_process_url_list_collection(db, task))
            
            # 提交任务进度更新
            db.refresh(task)
            
            logger.info(f"[术语采集任务] 链接列表采集完成 - 任务ID: {task.task_id}, 状态: {task.status}, 进度: {task.progress}")
            
            if task.status == "completed":
                if celery_task:
                    celery_task.update_state(state="SUCCESS", meta={"progress": 100, "status": "completed"})
                return {"status": "completed", "progress": 100}
            else:
                if celery_task:
                    celery_task.update_state(state="FAILURE", meta={"status": "failed", "error": "术语采集服务执行失败"})
                return {"status": "failed", "error": "术语采集服务执行失败"}
                
        except Exception as e:
            logger.error(f"[术语采集任务] 调用术语采集服务出错 - 任务ID: {task.task_id}, 错误: {str(e)}")
            logger.error(traceback.format_exc())
            
            # 如果服务执行失败，尝试使用生成示例结果的方式
            logger.warning(f"[术语采集任务] 尝试使用示例生成 - 任务ID: {task.task_id}")
            
            # 更新任务进度
            task.progress = 80
            db.commit()
            if celery_task:
                celery_task.update_state(state="PROGRESS", meta={"progress": 80, "status": "processing", "step": "生成示例术语"})
            
            # 生成示例结果
            generate_sample_results(db, task, url_count=len(url_list))
            
            task.progress = 100
            task.status = "completed"
            task.completed_at = datetime.now()
            db.commit()
            
            if celery_task:
                celery_task.update_state(state="SUCCESS", meta={"progress": 100, "status": "completed"})
            
            logger.info(f"[术语采集任务] 链接列表采集（示例模式）完成 - 任务ID: {task.task_id}")
            
            return {"status": "completed", "progress": 100}
    
    except Exception as e:
        logger.error(f"[术语采集任务] 链接列表采集出错 - 任务ID: {task.task_id}, 错误: {str(e)}")
        logger.error(traceback.format_exc())
        raise

def generate_sample_results(db: Session, task: TerminologyCollectionTask, source_url=None, url_count=None):
    """
    生成术语结果
    使用术语提取器从文本中提取真实术语
    """
    try:
        logger.info(f"[术语采集任务] 开始生成术语结果 - 任务ID: {task.task_id}, 名称: {task.name}, 关键词: {task.keywords}")
        
        # 获取任务文本
        text = ""
        if hasattr(task, 'source_text') and task.source_text:
            text = task.source_text
        else:
            # 如果没有源文本，使用关键词和名称作为文本，并添加一些与领域相关的内容
            # 检测可能的领域
            domain = "通用"
            try:
                # 先进行简单的关键词匹配
                if "汽车" in task.keywords or "车" in task.name:
                    domain = "汽车"
                    # 添加汽车领域的示例文本
                    text = f"""
                    {task.name} {task.keywords}
                    汽车是现代交通工具的重要组成部分，由发动机、底盘、车身和电气设备组成。发动机是汽车的动力来源，分为汽油发动机、柴油发动机、电动机等类型。
                    变速箱可以改变发动机输出的转速和转矩，包括手动变速箱、自动变速箱、双离合变速箱等。底盘包括传动系统、行驶系统、转向系统和制动系统。
                    现代汽车普遍配备ABS防抱死制动系统、ESP电子稳定程序、牵引力控制系统等安全配置。随着科技发展，自动驾驶、新能源汽车成为行业发展趋势。
                    电动汽车使用电机作为动力源，具有零排放、低噪音等优点，但也面临续航里程短、充电时间长等问题。混合动力汽车结合了内燃机和电动机的优势。
                    """
                elif "医" in task.keywords or "病" in task.name or "医疗" in task.keywords:
                    domain = "医疗"
                    # 添加医疗领域的示例文本
                    text = f"""
                    {task.name} {task.keywords}
                    医疗是关于预防、诊断和治疗疾病的领域。医院提供各种医疗服务，包括门诊、住院、急诊等。医生负责诊断疾病并制定治疗方案。
                    常见疾病包括感冒、流感、肺炎等。治疗方法包括药物治疗、手术治疗、物理治疗等。药物分为处方药和非处方药，需要根据医嘱使用。
                    医学影像技术包括X光、CT、核磁共振等，用于检查身体内部情况。血液检查可以分析血液中的各种成分，帮助诊断疾病。
                    慢性病如高血压、糖尿病需要长期管理。预防措施包括接种疫苗、定期体检、健康生活方式等。医疗保险可以减轻患者的经济负担。
                    """
                elif "法律" in task.keywords or "法" in task.name:
                    domain = "法律"
                    # 添加法律领域的示例文本
                    text = f"""
                    {task.name} {task.keywords}
                    法律是规范社会行为的规则体系。法院是解决争议的场所，包括最高法院、中级法院和基层法院。律师为当事人提供法律咨询和代理服务。
                    诉讼包括民事诉讼、刑事诉讼和行政诉讼。合同是具有法律约束力的协议，要求当事人履行约定的义务。知识产权包括著作权、专利权和商标权。
                    犯罪行为会受到刑法的惩罚，包括罚金、监禁等。法律援助为经济困难的人提供免费法律服务。仲裁是解决商业争议的另一种方式。
                    婚姻法规定结婚、离婚的条件和程序。劳动法保护劳动者的权益，规定工作时间、工资、社会保险等。环境法规范环境保护和污染防治。
                    """
                elif "技术" in task.keywords or "软件" in task.name or "计算机" in task.keywords:
                    domain = "IT"
                    # 添加IT领域的示例文本
                    text = f"""
                    {task.name} {task.keywords}
                    信息技术（IT）是处理信息的技术。计算机系统包括硬件和软件两部分。硬件包括CPU、内存、硬盘等物理组件。软件分为系统软件和应用软件。
                    编程语言用于开发软件，常见的有Java、Python、C++等。数据库用于存储和管理数据，如MySQL、MongoDB。网络技术实现设备间的通信。
                    互联网是全球性的计算机网络。云计算提供按需的计算资源服务。人工智能模拟人类智能，机器学习是其重要分支。大数据技术处理大量数据。
                    信息安全保护数据不被未授权访问或破坏。软件开发遵循需求分析、设计、编码、测试、维护的流程。敏捷开发是一种迭代式开发方法。
                    """
                else:
                    # 使用通用文本
                    text = f"{task.name} {task.keywords}"
                    logger.warning(f"[术语采集任务] 任务没有源文本，使用任务名称和关键词作为替代 - 任务ID: {task.task_id}")
            except Exception as e:
                text = f"{task.name} {task.keywords}"
                logger.warning(f"[术语采集任务] 生成领域文本出错: {str(e)}, 使用简单文本替代 - 任务ID: {task.task_id}")
        
        # 检测文本领域
        try:
            # 尝试导入领域检测工具
            sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
            from utils.domain_detector import detect_domain
            domain = detect_domain(text)
            logger.info(f"[术语采集任务] 检测到文本领域: {domain}")
        except ImportError:
            # 如果导入失败，使用简单的领域检测
            domain = determine_domain(task.name, task.keywords) if 'domain' not in locals() else domain
            logger.info(f"[术语采集任务] 使用简单领域检测: {domain}")
        
        # 导入术语提取器
        try:
            sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))
            from terminology_extractor import TerminologyExtractor
            extractor = TerminologyExtractor()
            logger.info(f"[术语采集任务] 成功导入术语提取器")
        except ImportError as e:
            logger.error(f"[术语采集任务] 导入术语提取器失败: {str(e)}")
            # 回退到旧的生成样本函数
            return generate_sample_results_fallback(db, task, source_url, url_count)
        
        # 使用术语提取器提取术语
        try:
            # 提取术语
            logger.info(f"[术语采集任务] 开始提取术语...")
            results = extractor.extract_all_methods(text, topK=500)  # 增加提取数量到500
            
            # 合并结果
            merged_terms = extractor.merge_results(results)
            
            # 不再限制结果数量，使用所有提取到的术语
            # merged_terms = merged_terms[:100]  # 删除这行限制
            
            logger.info(f"[术语采集任务] 成功提取 {len(merged_terms)} 个术语")
            
            # 如果提取的术语太少，添加一些特定领域的通用术语
            if len(merged_terms) < 10:
                logger.warning(f"[术语采集任务] 提取的术语数量过少，添加领域通用术语")
                
                # 根据领域添加一些通用术语
                domain_terms = []
                if domain == "汽车":
                    domain_terms = [
                        ("发动机", 0.95), ("变速箱", 0.93), ("底盘", 0.91), ("轮胎", 0.89),
                        ("悬挂", 0.87), ("刹车", 0.85), ("ABS", 0.83), ("ESP", 0.81),
                        ("涡轮增压", 0.79), ("自动驾驶", 0.77), ("油耗", 0.75), ("动力", 0.73)
                    ]
                elif domain == "医疗":
                    domain_terms = [
                        ("医院", 0.95), ("医生", 0.93), ("疾病", 0.91), ("治疗", 0.89),
                        ("药物", 0.87), ("手术", 0.85), ("检查", 0.83), ("诊断", 0.81),
                        ("症状", 0.79), ("预防", 0.77), ("护理", 0.75), ("康复", 0.73)
                    ]
                elif domain == "法律":
                    domain_terms = [
                        ("合同", 0.95), ("诉讼", 0.93), ("律师", 0.91), ("法院", 0.89),
                        ("判决", 0.87), ("仲裁", 0.85), ("法规", 0.83), ("权利", 0.81),
                        ("义务", 0.79), ("责任", 0.77), ("损害赔偿", 0.75), ("上诉", 0.73)
                    ]
                elif domain == "IT":
                    domain_terms = [
                        ("软件", 0.95), ("硬件", 0.93), ("数据库", 0.91), ("编程", 0.89),
                        ("网络", 0.87), ("服务器", 0.85), ("算法", 0.83), ("接口", 0.81),
                        ("云计算", 0.79), ("人工智能", 0.77), ("大数据", 0.75), ("安全", 0.73)
                    ]
                else:  # 通用领域
                    domain_terms = [
                        ("效率", 0.95), ("质量", 0.93), ("管理", 0.91), ("创新", 0.89),
                        ("战略", 0.87), ("分析", 0.85), ("技术", 0.83), ("系统", 0.81),
                        ("流程", 0.79), ("评估", 0.77), ("优化", 0.75), ("资源", 0.73)
                    ]
                
                # 合并提取的术语和领域通用术语
                merged_terms = list(set(merged_terms + domain_terms))
            
            # 保存术语结果到数据库
            saved_count = 0
            for i, (term, score) in enumerate(merged_terms):
                # 跳过太短的术语（长度为1的单字术语）
                if len(term) <= 1:
                    continue
                
                # 为术语生成更详细的定义
                definition = ""
                if domain == "汽车":
                    if "发动机" in term: definition = "汽车的动力源，将燃料的化学能转化为机械能"
                    elif "变速箱" in term: definition = "改变发动机输出转速和转矩的装置"
                    elif "底盘" in term: definition = "汽车的承载和运行机构，包括传动、行驶、转向和制动系统"
                    elif "轮胎" in term: definition = "与地面接触的橡胶部件，影响行驶的舒适性和安全性"
                    elif "悬挂" in term: definition = "连接车身和车轮的系统，减轻震动，提高乘坐舒适性"
                    elif "刹车" in term or "制动" in term: definition = "减速或停止车辆运动的系统"
                    elif "安全" in term: definition = "保障乘员安全的设计和装置"
                    elif "动力" in term: definition = "车辆行驶的驱动力，一般由发动机提供"
                    elif "油耗" in term: definition = "汽车消耗燃油的量，通常以升/百公里表示"
                    elif "排放" in term: definition = "汽车尾气中的污染物排放量"
                    else: definition = f"汽车领域相关术语，与车辆的构造、性能或使用有关"
                elif domain == "医疗":
                    definition = f"医疗领域术语，与健康、疾病诊断或治疗相关"
                elif domain == "法律":
                    definition = f"法律领域术语，与法规、诉讼或权利义务相关"
                elif domain == "IT":
                    definition = f"IT技术领域术语，与计算机、软件或数据处理相关"
                else:
                    definition = f"{domain}领域术语"
                
                source = source_url if source_url else f"https://example.com/terms/{i+1}"
                context = f"在{domain}领域文献中，{term}是一个常用术语，置信度为{score:.2f}。"
                
                # 创建结果对象
                result = TerminologyCollectionResult(
                    task_id=task.task_id,
                    term=term,
                    definition=definition,
                    pos="名词",
                    confidence=float(score),
                    source_url=source,
                    context=context
                )
                
                # 保存到数据库
                db.add(result)
                saved_count += 1
                
                # 每100个术语提交一次，避免事务过大
                if saved_count % 100 == 0:
                    db.commit()
                    logger.info(f"[术语采集任务] 已保存 {saved_count} 个术语结果")
            
            # 最后提交剩余的术语
            db.commit()
            logger.info(f"[术语采集任务] 成功保存 {saved_count} 个术语结果到数据库")
            
            # 验证结果是否成功保存
            result_count = db.query(TerminologyCollectionResult).filter(
                TerminologyCollectionResult.task_id == task.task_id
            ).count()
            
            logger.info(f"[术语采集任务] 数据库中的结果数量: {result_count}")
            
            return saved_count
            
        except Exception as e:
            logger.error(f"[术语采集任务] 提取术语出错: {str(e)}")
            logger.error(traceback.format_exc())
            # 回退到旧的生成样本函数
            return generate_sample_results_fallback(db, task, source_url, url_count)
    
    except Exception as e:
        logger.error(f"[术语采集任务] 生成术语结果出错 - 任务ID: {task.task_id}, 错误: {str(e)}")
        logger.error(traceback.format_exc())
        raise

# 将原始的示例生成函数重命名为fallback函数
def generate_sample_results_fallback(db: Session, task: TerminologyCollectionTask, source_url=None, url_count=None):
    """
    生成示例结果（回退函数）
    注意：在实际实现中，这里应该是真实的术语提取结果
    """
    try:
        logger.info(f"[术语采集任务] 使用回退函数生成示例术语结果 - 任务ID: {task.task_id}")
        
        language = task.language
        
        # 使用新的领域判断函数
        domain = determine_domain(task.name, task.keywords)
        logger.info(f"[术语采集任务] 确定领域: {domain}")
        
        # 根据不同领域生成不同的示例术语
        terms = []
        
        if domain == "医疗":
            terms = [
                {"term": "血压", "definition": "血液对血管壁的压力", "pos": "名词", "confidence": 0.95},
                {"term": "心率", "definition": "心脏跳动的频率", "pos": "名词", "confidence": 0.93},
                {"term": "血糖", "definition": "血液中的葡萄糖浓度", "pos": "名词", "confidence": 0.92},
                {"term": "胰岛素", "definition": "由胰腺分泌的调节血糖的激素", "pos": "名词", "confidence": 0.91},
                {"term": "动脉", "definition": "将血液从心脏输送到身体各部分的血管", "pos": "名词", "confidence": 0.90},
                {"term": "静脉", "definition": "将血液从身体各部分输送回心脏的血管", "pos": "名词", "confidence": 0.89},
                {"term": "肺炎", "definition": "肺部感染引起的炎症", "pos": "名词", "confidence": 0.88},
                {"term": "骨折", "definition": "骨骼结构的断裂", "pos": "名词", "confidence": 0.87},
                {"term": "恶性肿瘤", "definition": "可以侵入和破坏附近组织并可能扩散到身体其他部位的异常细胞生长", "pos": "名词", "confidence": 0.86},
                {"term": "抗生素", "definition": "用于治疗细菌感染的药物", "pos": "名词", "confidence": 0.85},
            ]
        elif domain == "技术":
            terms = [
                {"term": "算法", "definition": "解决问题的一系列步骤", "pos": "名词", "confidence": 0.95},
                {"term": "数据库", "definition": "有组织的数据集合", "pos": "名词", "confidence": 0.93},
                {"term": "编程语言", "definition": "用于编写计算机程序的形式语言", "pos": "名词", "confidence": 0.92},
                {"term": "API", "definition": "应用程序编程接口", "pos": "名词", "confidence": 0.91},
                {"term": "框架", "definition": "提供通用功能的软件库", "pos": "名词", "confidence": 0.90},
                {"term": "云计算", "definition": "通过互联网提供计算服务", "pos": "名词", "confidence": 0.89},
                {"term": "人工智能", "definition": "机器模拟人类智能的能力", "pos": "名词", "confidence": 0.88},
                {"term": "机器学习", "definition": "使计算机能够从数据中学习的技术", "pos": "名词", "confidence": 0.87},
                {"term": "深度学习", "definition": "基于人工神经网络的机器学习子集", "pos": "名词", "confidence": 0.86},
                {"term": "区块链", "definition": "分布式数据存储技术", "pos": "名词", "confidence": 0.85},
            ]
        elif domain == "法律":
            terms = [
                {"term": "合同", "definition": "具有法律约束力的协议", "pos": "名词", "confidence": 0.95},
                {"term": "诉讼", "definition": "在法院解决争端的过程", "pos": "名词", "confidence": 0.93},
                {"term": "原告", "definition": "提起诉讼的一方", "pos": "名词", "confidence": 0.92},
                {"term": "被告", "definition": "被起诉的一方", "pos": "名词", "confidence": 0.91},
                {"term": "法官", "definition": "主持审判并作出判决的官员", "pos": "名词", "confidence": 0.90},
                {"term": "律师", "definition": "提供法律咨询和代理的专业人士", "pos": "名词", "confidence": 0.89},
                {"term": "证据", "definition": "用于证明事实的材料", "pos": "名词", "confidence": 0.88},
                {"term": "判决", "definition": "法院对案件的最终决定", "pos": "名词", "confidence": 0.87},
                {"term": "上诉", "definition": "请求更高级法院审查下级法院判决的程序", "pos": "名词", "confidence": 0.86},
                {"term": "仲裁", "definition": "由中立第三方解决争议的过程", "pos": "名词", "confidence": 0.85},
            ]
        elif domain == "汽车":
            # 增加汽车领域的术语数量，从11个增加到50个
            terms = [
                {"term": "发动机", "definition": "汽车的动力源", "pos": "名词", "confidence": 0.95},
                {"term": "轮胎", "definition": "汽车与地面接触的部分", "pos": "名词", "confidence": 0.93},
                {"term": "底盘", "definition": "汽车的基础结构", "pos": "名词", "confidence": 0.92},
                {"term": "变速箱", "definition": "汽车的动力传输系统", "pos": "名词", "confidence": 0.91},
                {"term": "刹车", "definition": "汽车减速的装置", "pos": "名词", "confidence": 0.90},
                {"term": "方向盘", "definition": "汽车转向的装置", "pos": "名词", "confidence": 0.89},
                {"term": "车轮", "definition": "汽车与地面接触的部分", "pos": "名词", "confidence": 0.88},
                {"term": "悬挂", "definition": "汽车减震的装置", "pos": "名词", "confidence": 0.87},
                {"term": "油耗", "definition": "汽车消耗的燃油量", "pos": "名词", "confidence": 0.86},
                {"term": "排量", "definition": "发动机每冲程或每循环所排出气体的总量", "pos": "名词", "confidence": 0.85},
                {"term": "涡轮", "definition": "汽车发动机中的增压装置", "pos": "名词", "confidence": 0.84},
                {"term": "汽油发动机", "definition": "使用汽油作为燃料的发动机", "pos": "名词", "confidence": 0.83},
                {"term": "柴油发动机", "definition": "使用柴油作为燃料的发动机", "pos": "名词", "confidence": 0.82},
                {"term": "电动机", "definition": "使用电能驱动的发动机", "pos": "名词", "confidence": 0.81},
                {"term": "混合动力", "definition": "结合内燃机和电动机的动力系统", "pos": "名词", "confidence": 0.80},
                {"term": "四轮驱动", "definition": "所有四个车轮都能提供动力的驱动系统", "pos": "名词", "confidence": 0.79},
                {"term": "前轮驱动", "definition": "前轮提供动力的驱动系统", "pos": "名词", "confidence": 0.78},
                {"term": "后轮驱动", "definition": "后轮提供动力的驱动系统", "pos": "名词", "confidence": 0.77},
                {"term": "自动变速箱", "definition": "无需驾驶员手动换挡的变速箱", "pos": "名词", "confidence": 0.76},
                {"term": "手动变速箱", "definition": "需要驾驶员手动换挡的变速箱", "pos": "名词", "confidence": 0.75},
                {"term": "双离合变速箱", "definition": "结合手动和自动变速箱优点的变速系统", "pos": "名词", "confidence": 0.74},
                {"term": "CVT变速箱", "definition": "无级变速变速箱", "pos": "名词", "confidence": 0.73},
                {"term": "涡轮增压", "definition": "利用排气驱动涡轮提高进气压力的技术", "pos": "名词", "confidence": 0.72},
                {"term": "自然吸气", "definition": "不使用增压技术的发动机", "pos": "名词", "confidence": 0.71},
                {"term": "缸内直喷", "definition": "燃油直接喷入气缸的技术", "pos": "名词", "confidence": 0.70},
                {"term": "可变气门正时", "definition": "根据发动机工况调整气门开闭时间的技术", "pos": "名词", "confidence": 0.69},
                {"term": "ABS", "definition": "防抱死制动系统", "pos": "名词", "confidence": 0.68},
                {"term": "ESP", "definition": "电子稳定程序", "pos": "名词", "confidence": 0.67},
                {"term": "牵引力控制", "definition": "防止驱动轮打滑的系统", "pos": "名词", "confidence": 0.66},
                {"term": "巡航控制", "definition": "自动保持车速的系统", "pos": "名词", "confidence": 0.65},
                {"term": "自适应巡航", "definition": "能够自动调整车速和车距的巡航控制系统", "pos": "名词", "confidence": 0.64},
                {"term": "车道保持", "definition": "帮助车辆保持在车道内的系统", "pos": "名词", "confidence": 0.63},
                {"term": "盲区监测", "definition": "检测车辆盲区的系统", "pos": "名词", "confidence": 0.62},
                {"term": "倒车雷达", "definition": "辅助倒车的传感器系统", "pos": "名词", "confidence": 0.61},
                {"term": "全景影像", "definition": "提供车辆周围360度视图的系统", "pos": "名词", "confidence": 0.60},
                {"term": "自动泊车", "definition": "自动完成泊车操作的系统", "pos": "名词", "confidence": 0.59},
                {"term": "电动助力转向", "definition": "使用电机提供转向助力的系统", "pos": "名词", "confidence": 0.58},
                {"term": "液压助力转向", "definition": "使用液压系统提供转向助力的系统", "pos": "名词", "confidence": 0.57},
                {"term": "麦弗逊悬挂", "definition": "一种常见的前轮独立悬挂系统", "pos": "名词", "confidence": 0.56},
                {"term": "多连杆悬挂", "definition": "使用多个连杆的独立悬挂系统", "pos": "名词", "confidence": 0.55},
                {"term": "空气悬挂", "definition": "使用压缩空气作为弹性介质的悬挂系统", "pos": "名词", "confidence": 0.54},
                {"term": "电子驻车", "definition": "电子控制的驻车制动系统", "pos": "名词", "confidence": 0.53},
                {"term": "自动启停", "definition": "自动关闭和重启发动机的系统", "pos": "名词", "confidence": 0.52},
                {"term": "蓄电池", "definition": "存储电能的装置", "pos": "名词", "confidence": 0.51},
                {"term": "发电机", "definition": "将机械能转换为电能的装置", "pos": "名词", "confidence": 0.50},
                {"term": "起动机", "definition": "启动发动机的电机", "pos": "名词", "confidence": 0.49},
                {"term": "点火系统", "definition": "控制火花塞点火的系统", "pos": "名词", "confidence": 0.48},
                {"term": "燃油喷射", "definition": "将燃油喷入发动机的系统", "pos": "名词", "confidence": 0.47},
                {"term": "催化转化器", "definition": "减少尾气污染的装置", "pos": "名词", "confidence": 0.46},
                {"term": "氧传感器", "definition": "检测排气中氧含量的传感器", "pos": "名词", "confidence": 0.45},
            ]
        else:  # 通用领域
            # 增加通用领域的术语数量，从10个增加到30个
            terms = [
                {"term": "可持续发展", "definition": "满足当代人需求而不损害后代满足其需求能力的发展", "pos": "名词", "confidence": 0.93},
                {"term": "全球化", "definition": "世界各国经济、文化等方面日益紧密相连的过程", "pos": "名词", "confidence": 0.92},
                {"term": "数字化转型", "definition": "组织利用数字技术改变其运营和价值创造的过程", "pos": "名词", "confidence": 0.91},
                {"term": "创新", "definition": "将新想法转化为解决方案的过程", "pos": "名词", "confidence": 0.90},
                {"term": "生态系统", "definition": "生物群落及其物理环境之间相互作用形成的功能单位", "pos": "名词", "confidence": 0.89},
                {"term": "效率", "definition": "以最少的资源获得最大产出的能力", "pos": "名词", "confidence": 0.88},
                {"term": "多样性", "definition": "人员、想法或事物之间的差异和变化", "pos": "名词", "confidence": 0.87},
                {"term": "透明度", "definition": "操作和决策过程的公开和可见性", "pos": "名词", "confidence": 0.86},
                {"term": "责任", "definition": "对自己的行为负责的义务", "pos": "名词", "confidence": 0.85},
                {"term": "质量控制", "definition": "确保产品或服务符合特定标准的过程", "pos": "名词", "confidence": 0.84},
                {"term": "战略规划", "definition": "确定组织的方向和资源分配的过程", "pos": "名词", "confidence": 0.83},
                {"term": "风险管理", "definition": "识别、评估和控制风险的过程", "pos": "名词", "confidence": 0.82},
                {"term": "供应链", "definition": "从原材料到最终产品的生产和分销网络", "pos": "名词", "confidence": 0.81},
                {"term": "市场营销", "definition": "推广和销售产品或服务的活动", "pos": "名词", "confidence": 0.80},
                {"term": "客户关系", "definition": "组织与其客户之间的互动和联系", "pos": "名词", "confidence": 0.79},
                {"term": "品牌建设", "definition": "创建和维护品牌形象的过程", "pos": "名词", "confidence": 0.78},
                {"term": "竞争优势", "definition": "使组织能够胜过竞争对手的因素", "pos": "名词", "confidence": 0.77},
                {"term": "知识产权", "definition": "创造性工作的法律权利", "pos": "名词", "confidence": 0.76},
                {"term": "绩效评估", "definition": "评估个人或组织绩效的过程", "pos": "名词", "confidence": 0.75},
                {"term": "人力资源", "definition": "组织中的人员及其能力", "pos": "名词", "confidence": 0.74},
                {"term": "财务管理", "definition": "规划、组织、指导和控制财务活动的过程", "pos": "名词", "confidence": 0.73},
                {"term": "项目管理", "definition": "规划、组织和管理资源以完成特定目标的过程", "pos": "名词", "confidence": 0.72},
                {"term": "数据分析", "definition": "检查、清理、转换和建模数据以发现有用信息的过程", "pos": "名词", "confidence": 0.71},
                {"term": "信息安全", "definition": "保护信息免受未授权访问的实践", "pos": "名词", "confidence": 0.70},
                {"term": "可扩展性", "definition": "系统、网络或流程处理增长工作量的能力", "pos": "名词", "confidence": 0.69},
                {"term": "互操作性", "definition": "不同系统或组织一起工作的能力", "pos": "名词", "confidence": 0.68},
                {"term": "标准化", "definition": "建立和实施技术标准的过程", "pos": "名词", "confidence": 0.67},
                {"term": "认证", "definition": "正式确认某人或某物符合特定标准的过程", "pos": "名词", "confidence": 0.66},
                {"term": "合规性", "definition": "遵守法律、法规、政策和程序的状态", "pos": "名词", "confidence": 0.65},
                {"term": "可追溯性", "definition": "追踪产品、流程或系统历史的能力", "pos": "名词", "confidence": 0.64},
            ]
        
        # 记录生成的术语数量
        logger.info(f"[术语采集任务] 生成术语数量: {len(terms)}")
        
        # 根据任务的URL来源生成不同的上下文
        for i, term_data in enumerate(terms):
            source = source_url if source_url else f"https://example.com/terms/{i+1}"
            context = f"在专业文献中，{term_data['term']}被定义为{term_data['definition']}。这是一个在{domain}领域常用的术语。"
            
            # 创建结果对象
            result = TerminologyCollectionResult(
                task_id=task.task_id,
                term=term_data["term"],
                definition=term_data["definition"],
                pos=term_data["pos"],
                confidence=term_data["confidence"],
                source_url=source,
                context=context
            )
            
            # 保存到数据库
            db.add(result)
        
        # 提交到数据库
        db.commit()
        logger.info(f"[术语采集任务] 成功保存 {len(terms)} 个术语结果到数据库")
        
        # 验证结果是否成功保存
        result_count = db.query(TerminologyCollectionResult).filter(
            TerminologyCollectionResult.task_id == task.task_id
        ).count()
        
        logger.info(f"[术语采集任务] 数据库中的结果数量: {result_count}")
        
        return len(terms)
    
    except Exception as e:
        logger.error(f"[术语采集任务] 生成示例术语结果出错: {str(e)}")
        logger.error(traceback.format_exc())
        raise

@celery_app.task(bind=True, base=TerminologyExtractionTask)
def process_terminology_collection(self, task_id: str, text: str = None) -> Dict[str, Any]:
    """
    处理术语提取任务
    
    Args:
        task_id: 任务ID
        text: 输入文本，如果为None则从数据库获取
        
    Returns:
        Dict: 包含任务处理结果的字典
    """
    logger.info(f"Starting terminology collection task: {task_id}")
    start_time = time.time()
    
    # 获取数据库会话
    session = get_db_session()
    
    try:
        # 获取任务信息
        task = session.query(TerminologyCollectionTask).filter(TerminologyCollectionTask.id == task_id).first()
        if not task:
            logger.error(f"Task not found: {task_id}")
            return {"status": "error", "message": f"Task not found: {task_id}"}
        
        # 如果没有提供文本，则从任务中获取
        if text is None:
            text = task.source_text
            
        if not text:
            logger.error(f"No text provided for task: {task_id}")
            return {"status": "error", "message": "No text provided"}
        
        # 更新任务状态为处理中
        task.status = "processing"
        session.commit()
        
        # 检测文本领域
        domain = detect_domain(text)
        logger.info(f"Detected domain: {domain}")
        
        # 获取对应领域的术语提取器
        extractor = self.get_extractor(domain)
        
        # 使用术语提取器提取术语
        try:
            # 使用所有方法提取术语
            results = extractor.extract_all_methods(text)
            
            # 合并结果
            merged_terms = extractor.merge_results(results)
            
            # 限制结果数量，取前100个
            merged_terms = merged_terms[:100]
            
            # 创建输出目录
            output_dir = os.path.join("output", task_id)
            os.makedirs(output_dir, exist_ok=True)
            
            # 导出结果
            csv_path = os.path.join(output_dir, f"{domain}_terms.csv")
            json_path = os.path.join(output_dir, f"{domain}_terms.json")
            png_path = os.path.join(output_dir, f"{domain}_terms.png")
            
            extractor.export_to_csv(merged_terms, csv_path)
            extractor.export_to_json(merged_terms, json_path)
            
            try:
                extractor.visualize_terms(merged_terms, top_n=20, 
                                         title=f"{domain}领域术语分布", 
                                         output_file=png_path)
            except Exception as e:
                logger.warning(f"Failed to create visualization: {str(e)}")
            
            # 评估术语质量
            evaluation = extractor.evaluate_term_quality(merged_terms[:50])
            
            # 保存术语结果到数据库
            for term, score in merged_terms:
                term_result = TerminologyResult(
                    task_id=task_id,
                    term=term,
                    score=float(score),
                    domain=domain
                )
                session.add(term_result)
            
            # 更新任务状态为已完成
            task.status = "completed"
            task.result_count = len(merged_terms)
            task.domain = domain
            task.metadata = json.dumps({
                "precision": evaluation["precision"],
                "recall": evaluation["recall"],
                "f1_score": evaluation["f1_score"],
                "coverage": evaluation["coverage"],
                "correct_count": evaluation["correct_count"],
                "processing_time": time.time() - start_time
            })
            
            session.commit()
            
            logger.info(f"Terminology collection completed for task {task_id}. Found {len(merged_terms)} terms.")
            
            return {
                "status": "success", 
                "task_id": task_id,
                "domain": domain,
                "term_count": len(merged_terms),
                "processing_time": time.time() - start_time
            }
            
        except Exception as e:
            logger.error(f"Error processing terminology: {str(e)}", exc_info=True)
            task.status = "failed"
            task.error_message = str(e)
            session.commit()
            return {"status": "error", "message": str(e)}
    
    except Exception as e:
        logger.error(f"Error in terminology collection task: {str(e)}", exc_info=True)
        return {"status": "error", "message": str(e)}
    
    finally:
        session.close()

@celery_app.task
def generate_sample_terminology_results(task_id: str, domain: str = "general") -> Dict[str, Any]:
    """
    生成示例术语结果（用于测试）
    
    Args:
        task_id: 任务ID
        domain: 领域
        
    Returns:
        Dict: 包含任务处理结果的字典
    """
    logger.info(f"Generating sample terminology results for task: {task_id}, domain: {domain}")
    
    # 获取数据库会话
    session = get_db_session()
    
    try:
        # 获取任务信息
        task = session.query(TerminologyCollectionTask).filter(TerminologyCollectionTask.id == task_id).first()
        if not task:
            logger.error(f"Task not found: {task_id}")
            return {"status": "error", "message": f"Task not found: {task_id}"}
        
        # 更新任务状态为处理中
        task.status = "processing"
        session.commit()
        
        # 根据领域选择不同的示例术语
        terms = []
        if domain == "automotive" or domain == "汽车":
            # 汽车领域术语
            terms = [
                ("发动机", 0.95),
                ("变速箱", 0.92),
                ("底盘", 0.88),
                ("悬挂系统", 0.85),
                ("制动系统", 0.84),
                ("转向系统", 0.82),
                ("电子控制单元", 0.78),
                ("汽油发动机", 0.76),
                ("柴油发动机", 0.75),
                ("电动机", 0.74),
                ("混合动力", 0.72)
            ]
        else:
            # 通用领域术语
            terms = [
                ("数据", 0.95),
                ("系统", 0.92),
                ("算法", 0.88),
                ("模型", 0.85),
                ("分析", 0.84),
                ("处理", 0.82),
                ("结构", 0.78),
                ("方法", 0.76),
                ("技术", 0.75),
                ("工具", 0.74)
            ]
        
        # 保存术语结果到数据库
        for term, score in terms:
            term_result = TerminologyResult(
                task_id=task_id,
                term=term,
                score=float(score),
                domain=domain
            )
            session.add(term_result)
        
        # 更新任务状态为已完成
        task.status = "completed"
        task.result_count = len(terms)
        task.domain = domain
        task.metadata = json.dumps({
            "precision": 0.85,
            "recall": 0.75,
            "f1_score": 0.80,
            "coverage": 0.70,
            "sample": True
        })
        
        session.commit()
        
        logger.info(f"Sample terminology results generated for task {task_id}. Created {len(terms)} terms.")
        
        return {
            "status": "success", 
            "task_id": task_id,
            "domain": domain,
            "term_count": len(terms),
            "sample": True
        }
        
    except Exception as e:
        logger.error(f"Error generating sample terminology results: {str(e)}", exc_info=True)
        return {"status": "error", "message": str(e)}
    
    finally:
        session.close() 

@celery_app.task(name="terminology_tasks.test_task")
def test_task():
    """
    测试术语采集任务
    创建一个测试任务并执行
    """
    logger.info("[术语采集测试] 开始测试术语采集任务")
    
    # 创建数据库会话
    db = SessionLocal()
    
    try:
        # 创建一个测试任务，使用更短的ID
        task_id_base = str(uuid.uuid4())[:8]
        
        # 准备测试数据
        test_zh = TerminologyCollectionTask(
            task_id=f"test_zh_{task_id_base}",
            name="中文术语测试任务",
            mode="smart",
            keywords="汽车,发动机,底盘",
            language="zh",
            status="pending",
            progress=0,
            created_at=datetime.now()
        )
        
        test_en = TerminologyCollectionTask(
            task_id=f"test_en_{task_id_base}",
            name="English Terms Test",
            mode="smart",
            keywords="car,engine,chassis",
            language="en",
            status="pending",
            progress=0,
            created_at=datetime.now()
        )
        
        # 保存到数据库
        db.add(test_zh)
        db.commit()
        db.refresh(test_zh)
        
        db.add(test_en)
        db.commit()
        db.refresh(test_en)
        
        logger.info(f"[术语采集测试] 创建了测试任务 - 中文任务ID: {test_zh.task_id}, 英文任务ID: {test_en.task_id}")
        
        # 执行中文测试任务
        try:
            logger.info(f"[术语采集测试] 开始执行中文测试任务...")
            
            # 调用generate_sample_results处理任务
            from services.terminology_collection_service import generate_sample_results
            result_zh = asyncio.run(generate_sample_results(db, test_zh))
            
            # 更新任务状态
            test_zh.status = "completed"
            test_zh.progress = 100
            test_zh.completed_at = datetime.now()
            db.commit()
            
            logger.info(f"[术语采集测试] 中文测试任务完成 - 生成术语数量: {result_zh}")
            
            # 检查结果
            results_zh = db.query(TerminologyCollectionResult).filter(
                TerminologyCollectionResult.task_id == test_zh.task_id
            ).all()
            
            logger.info(f"[术语采集测试] 中文测试任务结果 - 术语数量: {len(results_zh)}")
            if len(results_zh) > 0:
                # 检查术语是否为中文
                chinese_terms = [r for r in results_zh if re.search(r'[\u4e00-\u9fff]', r.term)]
                logger.info(f"[术语采集测试] 中文术语数量: {len(chinese_terms)}/{len(results_zh)}")
                
                # 输出前三个术语作为样例
                for i, result in enumerate(results_zh[:3]):
                    logger.info(f"[术语采集测试] 中文测试样例{i+1}: {result.term} - {result.definition}")
            
        except Exception as e:
            logger.error(f"[术语采集测试] 中文测试任务出错: {str(e)}")
            logger.error(traceback.format_exc())
        
        # 执行英文测试任务
        try:
            logger.info(f"[术语采集测试] 开始执行英文测试任务...")
            
            # 调用generate_sample_results处理任务
            from services.terminology_collection_service import generate_sample_results
            result_en = asyncio.run(generate_sample_results(db, test_en))
            
            # 更新任务状态
            test_en.status = "completed"
            test_en.progress = 100
            test_en.completed_at = datetime.now()
            db.commit()
            
            logger.info(f"[术语采集测试] 英文测试任务完成 - 生成术语数量: {result_en}")
            
            # 检查结果
            results_en = db.query(TerminologyCollectionResult).filter(
                TerminologyCollectionResult.task_id == test_en.task_id
            ).all()
            
            logger.info(f"[术语采集测试] 英文测试任务结果 - 术语数量: {len(results_en)}")
            if len(results_en) > 0:
                # 检查术语是否为英文
                english_terms = [r for r in results_en if re.search(r'[a-zA-Z]', r.term) and not re.search(r'[\u4e00-\u9fff]', r.term)]
                logger.info(f"[术语采集测试] 英文术语数量: {len(english_terms)}/{len(results_en)}")
                
                # 输出前三个术语作为样例
                for i, result in enumerate(results_en[:3]):
                    logger.info(f"[术语采集测试] 英文测试样例{i+1}: {result.term} - {result.definition}")
            
        except Exception as e:
            logger.error(f"[术语采集测试] 英文测试任务出错: {str(e)}")
            logger.error(traceback.format_exc())
        
        # 最后检查两个任务的执行结果
        db.refresh(test_zh)
        db.refresh(test_en)
        
        # 获取详细结果
        from services.terminology_collection_service import get_task_detail
        zh_detail = get_task_detail(db, test_zh.task_id)
        en_detail = get_task_detail(db, test_en.task_id)
        
        return {
            "status": "completed",
            "message": "测试完成",
            "zh_task_id": test_zh.task_id,
            "zh_status": test_zh.status,
            "zh_result_count": db.query(TerminologyCollectionResult).filter(
                TerminologyCollectionResult.task_id == test_zh.task_id
            ).count(),
            "zh_language": test_zh.language,
            "zh_sample_terms": [r.term for r in results_zh[:5]] if len(results_zh) > 0 else [],
            "en_task_id": test_en.task_id,
            "en_status": test_en.status,
            "en_result_count": db.query(TerminologyCollectionResult).filter(
                TerminologyCollectionResult.task_id == test_en.task_id
            ).count(),
            "en_language": test_en.language,
            "en_sample_terms": [r.term for r in results_en[:5]] if len(results_en) > 0 else [],
            "zh_detail_url": f"/api/terminology/tasks/{test_zh.task_id}",
            "en_detail_url": f"/api/terminology/tasks/{test_en.task_id}"
        }
    
    except Exception as e:
        logger.error(f"[术语采集测试] 测试出错: {str(e)}")
        logger.error(traceback.format_exc())
        return {"status": "error", "message": str(e)}
    
    finally:
        # 关闭数据库会话
        db.close()