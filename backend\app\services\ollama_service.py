"""
Ollama本地模型服务
"""

import requests
import json
import logging
from typing import Dict, Any, List, Optional
from app.core.config import settings

logger = logging.getLogger(__name__)


class OllamaService:
    """Ollama本地模型服务"""
    
    def __init__(self, base_url: str = "http://localhost:11434"):
        self.base_url = base_url
        self.available = self._check_availability()
        
    def _check_availability(self) -> bool:
        """检查Ollama服务是否可用"""
        try:
            response = requests.get(f"{self.base_url}/api/tags", timeout=5)
            return response.status_code == 200
        except Exception as e:
            logger.warning(f"Ollama服务不可用: {e}")
            return False
    
    def list_models(self) -> List[Dict[str, Any]]:
        """获取可用模型列表"""
        if not self.available:
            return []
            
        try:
            response = requests.get(f"{self.base_url}/api/tags")
            if response.status_code == 200:
                data = response.json()
                return data.get('models', [])
        except Exception as e:
            logger.error(f"获取模型列表失败: {e}")
        return []
    
    def generate_text(self, 
                     model: str, 
                     prompt: str, 
                     system_prompt: Optional[str] = None,
                     temperature: float = 0.7,
                     max_tokens: int = 2000) -> Optional[str]:
        """生成文本"""
        if not self.available:
            logger.warning("Ollama服务不可用，无法生成文本")
            return None
            
        try:
            payload = {
                "model": model,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": temperature,
                    "num_predict": max_tokens
                }
            }
            
            if system_prompt:
                payload["system"] = system_prompt
            
            response = requests.post(
                f"{self.base_url}/api/generate",
                json=payload,
                timeout=120
            )
            
            if response.status_code == 200:
                data = response.json()
                return data.get('response', '')
            else:
                logger.error(f"Ollama生成失败: {response.status_code} - {response.text}")
                
        except Exception as e:
            logger.error(f"Ollama生成文本异常: {e}")
        
        return None
    
    def chat_completion(self, 
                       model: str, 
                       messages: List[Dict[str, str]],
                       temperature: float = 0.7,
                       max_tokens: int = 2000) -> Optional[str]:
        """聊天完成"""
        if not self.available:
            logger.warning("Ollama服务不可用，无法进行聊天")
            return None
            
        try:
            payload = {
                "model": model,
                "messages": messages,
                "stream": False,
                "options": {
                    "temperature": temperature,
                    "num_predict": max_tokens
                }
            }
            
            response = requests.post(
                f"{self.base_url}/api/chat",
                json=payload,
                timeout=120
            )
            
            if response.status_code == 200:
                data = response.json()
                return data.get('message', {}).get('content', '')
            else:
                logger.error(f"Ollama聊天失败: {response.status_code} - {response.text}")
                
        except Exception as e:
            logger.error(f"Ollama聊天异常: {e}")
        
        return None
    
    def analyze_opinion(self, 
                       text: str, 
                       model: str = "llama3.1:8b") -> Dict[str, Any]:
        """舆情分析"""
        if not self.available:
            return {
                "sentiment": "neutral",
                "keywords": [],
                "summary": "Ollama服务不可用，无法进行分析",
                "confidence": 0.0
            }
        
        system_prompt = """你是一个专业的舆情分析专家。请分析给定的文本内容，并提供以下信息：
1. 情感倾向（positive/negative/neutral）
2. 关键词提取（最多10个）
3. 内容摘要（不超过200字）
4. 分析置信度（0-1之间的数值）

请以JSON格式返回结果。"""
        
        user_prompt = f"请分析以下文本的舆情信息：\n\n{text}"
        
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]
        
        response = self.chat_completion(model, messages)
        
        if response:
            try:
                # 尝试解析JSON响应
                result = json.loads(response)
                return result
            except json.JSONDecodeError:
                # 如果不是JSON格式，返回基本分析
                return {
                    "sentiment": "neutral",
                    "keywords": [],
                    "summary": response[:200] if response else "分析失败",
                    "confidence": 0.5
                }
        
        return {
            "sentiment": "neutral",
            "keywords": [],
            "summary": "分析失败",
            "confidence": 0.0
        }
    
    def generate_report(self, 
                       data: List[Dict[str, Any]], 
                       template_type: str = "standard",
                       model: str = "llama3.1:8b") -> str:
        """生成舆情简报"""
        if not self.available:
            return "Ollama服务不可用，无法生成简报"
        
        # 构建系统提示
        system_prompt = f"""你是一个专业的舆情分析师。请根据提供的舆情数据生成一份{template_type}类型的舆情简报。

简报要求：
1. 结构清晰，包含摘要、热点事件、趋势分析、建议等部分
2. 语言专业，客观中性
3. 突出重点信息和关键趋势
4. 字数控制在1000-2000字之间

请生成专业的舆情简报。"""
        
        # 构建数据摘要
        data_summary = "舆情数据摘要：\n"
        for i, item in enumerate(data[:10], 1):  # 只取前10条数据
            title = item.get('title', '无标题')
            content = item.get('content', item.get('description', ''))[:100]
            data_summary += f"{i}. {title}\n   {content}...\n\n"
        
        user_prompt = f"请基于以下舆情数据生成简报：\n\n{data_summary}"
        
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]
        
        response = self.chat_completion(model, messages, max_tokens=3000)
        
        return response or "简报生成失败"


# 全局实例
ollama_service = OllamaService()
