"""
基础任务类，为所有Celery任务提供通用的状态追踪和错误处理
"""
import logging
import time
import traceback
from typing import Dict, Any, Optional, List, Union, Callable
import asyncio
from functools import wraps

from celery import Task
from sqlalchemy.ext.asyncio import AsyncSession

from utils.db import get_db, async_db_context
from utils.task_persistence import TaskPersistenceManager

logger = logging.getLogger(__name__)

class BaseTask(Task):
    """
    基础任务类，所有Celery任务都应继承此类
    自动处理任务状态更新和异常处理
    """
    # 抽象类
    abstract = True
    
    # 最大重试次数
    max_retries = 3
    
    # 每次重试之间的延迟（秒）
    default_retry_delay = 5

    def __call__(self, *args, **kwargs):
        """
        重写__call__方法，添加任务跟踪
        """
        # 从kwargs或args中获取task_id
        task_id = kwargs.get('task_id')
        
        # 如果kwargs中没有task_id，尝试从args中获取
        if not task_id and args and len(args) > 0:
            task_id = args[0]
        
        # 确保task_id是字符串类型
        if task_id and not isinstance(task_id, str):
            logger.warning(f"任务ID类型错误: {type(task_id)}，尝试转换为字符串")
            try:
                task_id = str(task_id)
            except Exception as e:
                logger.error(f"转换任务ID失败: {str(e)}")
        
        # 如果没有提供task_id，记录警告
        if not task_id:
            logger.warning(f"任务未提供task_id，无法跟踪任务状态: {self.name}[{self.request.id}]")
            return super().__call__(*args, **kwargs)
        
        # 记录开始执行任务的日志
        logger.info(f"开始执行任务: {self.name}[{self.request.id}], 任务ID: {task_id}")
        
        try:
            # 更新任务状态为运行中
            self._update_task_status(
                task_id=task_id,
                status="running",
                progress=0,
                message=f"开始执行任务: {self.name}"
            )
            
            # 执行任务
            result = super().__call__(*args, **kwargs)
            
            # 更新任务状态为成功
            self._update_task_status(
                task_id=task_id,
                status="success",
                progress=100,
                message=f"任务执行成功: {self.name}"
            )
            
            # 尝试保存任务结果，但不让异常影响任务完成
            try:
                # 如果结果是字典并且包含result字段，保存结果数据
                if isinstance(result, dict):
                    if 'result' in result:
                        # 保存任务结果
                        TaskPersistenceManager.update_task_result(task_id, result['result'])
                    elif 'data' in result:
                        # 有些任务可能使用data字段
                        TaskPersistenceManager.update_task_result(task_id, result['data'])
            except Exception as result_error:
                # 记录错误但不影响任务完成
                logger.error(f"保存任务结果时出错，但任务已成功完成: {str(result_error)}", exc_info=True)
            
            return result
        except Exception as e:
            # 更新任务状态为失败
            error_message = str(e)
            logger.exception(f"任务执行失败: {self.name}[{self.request.id}], 任务ID: {task_id}, 错误: {error_message}")
            
            self._update_task_status(
                task_id=task_id,
                status="failed",
                progress=0,
                message=f"任务执行失败: {error_message}",
                error=error_message
            )
            
            # 重新抛出异常
            raise
    
    def on_retry(self, exc, task_id, args, kwargs, einfo):
        """
        任务重试回调，更新任务状态
        """
        # 获取任务ID
        task_id = kwargs.get('task_id')
        
        if not task_id:
            return super().on_retry(exc, task_id, args, kwargs, einfo)
        
        # 更新任务状态
        message = f"任务重试 ({self.request.retries}/{self.max_retries}): {str(exc)}"
        self._update_task_status(task_id, "retrying", 0, message)
        
        return super().on_retry(exc, task_id, args, kwargs, einfo)
    
    def _update_task_status(
        self, 
        task_id: str, 
        status: str, 
        progress: int = None, 
        message: str = None,
        result_url: str = None,
        error: str = None
    ) -> None:
        """
        更新任务状态
        
        参数:
            task_id: 任务ID
            status: 状态
            progress: 进度 (0-100)
            message: 消息
            result_url: 结果URL
            error: 错误信息
        """
        # 确保task_id是字符串类型
        if not isinstance(task_id, str):
            logger.warning(f"任务ID类型错误: {type(task_id)}，尝试转换为字符串")
            try:
                task_id = str(task_id)
            except Exception as e:
                logger.error(f"转换任务ID失败: {str(e)}")
                return
        
        try:
            # 如果任务完成或成功，确保清除错误信息
            if status in ["completed", "success"] and (progress is None or progress == 100):
                error = None
            
            # 更新任务状态
            TaskPersistenceManager.update_task_status(
                task_id=task_id,
                status=status,
                progress=progress,
                error=error,
                result_url=result_url
            )
            
            # 添加任务日志
            if message:
                TaskPersistenceManager.add_task_log(
                    task_id=task_id,
                    message=message,
                    level="info" if status != "failed" else "error"
                )
            
            logger.debug(f"已更新任务状态: {task_id}, 状态: {status}, 进度: {progress}, 消息: {message}")
        except Exception as e:
            logger.error(f"更新任务状态失败: {str(e)}", exc_info=True)

    def update_progress(self, task_id: str, progress: int, message: str = None, result_url: str = None, output_data: dict = None) -> None:
        """
        更新任务进度
        
        参数:
            task_id: 任务ID
            progress: 进度 (0-100)
            message: 消息
            result_url: 结果URL
            output_data: 输出数据
        """
        # 确保task_id是字符串类型
        if not isinstance(task_id, str):
            logger.warning(f"任务ID类型错误: {type(task_id)}，尝试转换为字符串")
            try:
                task_id = str(task_id)
            except Exception as e:
                logger.error(f"转换任务ID失败: {str(e)}")
                return
        
        try:
            # 更新任务状态
            TaskPersistenceManager.update_task_status(
                task_id=task_id,
                status="running" if progress < 100 else "completed",
                progress=progress,
                result_url=result_url
            )
            
            # 如果有输出数据，更新任务结果
            if output_data:
                TaskPersistenceManager.update_task_result(task_id, output_data)
            
            # 添加任务日志
            if message:
                TaskPersistenceManager.add_task_log(
                    task_id=task_id,
                    message=message,
                    level="info"
                )
            
            logger.debug(f"已更新任务进度: {task_id}, 进度: {progress}, 消息: {message}")
        except Exception as e:
            logger.error(f"更新任务进度失败: {str(e)}", exc_info=True)

# 装饰器，用于在函数级别跟踪任务进度
def task_progress(description: str):
    """
    跟踪任务进度的装饰器
    
    Args:
        description: 步骤描述
        
    例：
    @task_progress("处理视频")
    def process_video(self, task_id, video_path):
        # 处理逻辑
        return result
    """
    def decorator(func):
        @wraps(func)
        def wrapper(self, *args, **kwargs):
            # 获取任务ID
            task_id = kwargs.get('task_id')
            
            if not task_id:
                # 尝试从位置参数中获取task_id（通常是第一个参数）
                if len(args) > 0:
                    task_id = args[0]
            
            if not isinstance(self, BaseTask) or not task_id:
                # 如果不是BaseTask实例或没有任务ID，直接执行函数
                return func(self, *args, **kwargs)
            
            # 更新任务进度，表示开始执行子步骤
            self.update_progress(task_id, self.progress, f"开始{description}")
            
            try:
                # 执行原始函数
                result = func(self, *args, **kwargs)
                
                # 更新任务进度，表示子步骤完成
                self.update_progress(task_id, self.progress, f"{description}完成")
                
                return result
            except Exception as e:
                # 记录错误，子步骤失败
                logger.error(f"{description}失败: {str(e)}")
                
                # 更新任务进度，表示子步骤失败
                self.update_progress(task_id, self.progress, f"{description}失败: {str(e)}")
                
                # 重新抛出异常，让BaseTask处理
                raise e
                
        return wrapper
    return decorator 