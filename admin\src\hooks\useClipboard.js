import { ref } from 'vue';
import { message } from 'ant-design-vue';

/**
 * 剪贴板操作钩子
 * @param {Object} options - 配置选项
 * @param {Function} options.onSuccess - 复制成功回调
 * @param {Function} options.onError - 复制失败回调
 * @param {string} options.successMessage - 成功提示信息
 * @param {string} options.errorMessage - 失败提示信息
 * @param {number} options.timeout - 复制状态保持时间(ms)
 * @returns {Object} 剪贴板操作对象
 */
export default function useClipboard(options = {}) {
  const {
    onSuccess,
    onError,
    successMessage = '复制成功',
    errorMessage = '复制失败',
    timeout = 1500
  } = options;
  
  // 复制状态
  const copied = ref(false);
  // 复制内容
  const copiedText = ref('');
  // 定时器
  let timer = null;
  
  // 重置复制状态
  const resetCopied = () => {
    if (timer) {
      clearTimeout(timer);
      timer = null;
    }
    
    if (copied.value) {
      timer = setTimeout(() => {
        copied.value = false;
      }, timeout);
    }
  };
  
  // 复制文本到剪贴板
  const copy = async (text) => {
    if (!text) {
      return false;
    }
    
    try {
      // 使用 Clipboard API
      if (navigator.clipboard && navigator.clipboard.writeText) {
        await navigator.clipboard.writeText(text);
      } else {
        // 兼容性方案
        const element = document.createElement('textarea');
        element.value = text;
        element.setAttribute('readonly', '');
        element.style.position = 'absolute';
        element.style.left = '-9999px';
        document.body.appendChild(element);
        element.select();
        document.execCommand('copy');
        document.body.removeChild(element);
      }
      
      // 更新状态
      copied.value = true;
      copiedText.value = text;
      resetCopied();
      
      // 调用成功回调
      if (typeof onSuccess === 'function') {
        onSuccess(text, successMessage);
      }
      
      return true;
    } catch (error) {
      console.error('复制到剪贴板失败:', error);
      
      // 调用失败回调
      if (typeof onError === 'function') {
        onError(error, errorMessage);
      }
      
      return false;
    }
  };
  
  // 复制HTML到剪贴板
  const copyHtml = async (html, plainText) => {
    if (!html) {
      return false;
    }
    
    try {
      // 尝试使用更高级的API复制HTML
      if (navigator.clipboard && typeof navigator.clipboard.write === 'function') {
        const blob = new Blob([html], { type: 'text/html' });
        const plainBlob = new Blob([plainText || html.replace(/<[^>]*>/g, '')], { type: 'text/plain' });
        
        const data = [
          new ClipboardItem({
            'text/html': blob,
            'text/plain': plainBlob
          })
        ];
        
        await navigator.clipboard.write(data);
      } else {
        // 回退到只复制纯文本
        const element = document.createElement('div');
        element.innerHTML = html;
        const text = plainText || element.textContent || element.innerText;
        return copy(text);
      }
      
      // 更新状态
      copied.value = true;
      copiedText.value = html;
      resetCopied();
      
      // 调用成功回调
      if (typeof onSuccess === 'function') {
        onSuccess(html, successMessage);
      }
      
      return true;
    } catch (error) {
      console.error('复制HTML到剪贴板失败:', error);
      
      // 尝试回退到复制纯文本
      try {
        const text = plainText || html.replace(/<[^>]*>/g, '');
        return copy(text);
      } catch (fallbackError) {
        // 调用失败回调
        if (typeof onError === 'function') {
          onError(error, errorMessage);
        }
        
        return false;
      }
    }
  };
  
  // 从剪贴板读取文本
  const readText = async () => {
    try {
      // 检查API支持
      if (!navigator.clipboard || !navigator.clipboard.readText) {
        throw new Error('浏览器不支持从剪贴板读取内容');
      }
      
      // 读取剪贴板内容
      const text = await navigator.clipboard.readText();
      return text;
    } catch (error) {
      console.error('从剪贴板读取失败:', error);
      
      // 调用失败回调
      if (typeof onError === 'function') {
        onError(error, '读取剪贴板失败');
      }
      
      return null;
    }
  };
  
  return {
    copied,
    copiedText,
    copy,
    copyHtml,
    readText
  };
} 