from typing import Dict, Any, Optional
import logging
import asyncio
from datetime import datetime

logger = logging.getLogger(__name__)

class TaskStateManager:
    """任务状态管理器"""
    
    def __init__(self):
        self._tasks: Dict[str, Dict[str, Any]] = {}
        self._lock = asyncio.Lock()
        
    async def create_task(self, task_id: str, initial_state: Dict[str, Any]) -> None:
        """创建新任务"""
        async with self._lock:
            self._tasks[task_id] = {
                "state": initial_state,
                "created_at": datetime.now(),
                "updated_at": datetime.now(),
                "logs": []
            }
            logger.info(f"Task {task_id}: 创建新任务")
            
    async def update_task(self, task_id: str, updates: Dict[str, Any]) -> None:
        """更新任务状态"""
        async with self._lock:
            if task_id not in self._tasks:
                raise ValueError(f"Task {task_id} not found")
                
            self._tasks[task_id]["state"].update(updates)
            self._tasks[task_id]["updated_at"] = datetime.now()
            logger.info(f"Task {task_id}: 更新任务状态")
            
    async def add_log(self, task_id: str, level: str, message: str) -> None:
        """添加任务日志"""
        async with self._lock:
            if task_id not in self._tasks:
                raise ValueError(f"Task {task_id} not found")
                
            self._tasks[task_id]["logs"].append({
                "timestamp": datetime.now(),
                "level": level,
                "message": message
            })
            logger.info(f"Task {task_id}: 添加日志 - {level}: {message}")
            
    async def get_task(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务信息"""
        async with self._lock:
            return self._tasks.get(task_id)
            
    async def delete_task(self, task_id: str) -> None:
        """删除任务"""
        async with self._lock:
            if task_id in self._tasks:
                del self._tasks[task_id]
                logger.info(f"Task {task_id}: 删除任务")
                
    def get_all_tasks(self) -> Dict[str, Dict[str, Any]]:
        """获取所有任务"""
        return self._tasks.copy() 