"""
知识库管理器
负责知识库的创建、管理和查询
"""

import json
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime

from sqlalchemy.orm import Session
from models.true_agent import AgentKnowledgeBase, KnowledgeDocument

logger = logging.getLogger(__name__)

class KnowledgeManager:
    """知识库管理器"""
    
    def __init__(self):
        pass
    
    async def create_knowledge_base(
        self,
        agent_id: str,
        name: str,
        kb_type: str,
        description: str = "",
        config: Dict[str, Any] = None,
        db: Session = None
    ) -> AgentKnowledgeBase:
        """创建知识库"""

        if not db:
            logger.error("数据库会话为None，无法创建知识库")
            raise ValueError("数据库会话不能为None")

        try:
            knowledge_base = AgentKnowledgeBase(
                agent_id=agent_id,
                name=name,
                type=kb_type,
                description=description,
                config=config or {}
            )

            db.add(knowledge_base)
            db.commit()
            db.refresh(knowledge_base)
            
            logger.info(f"创建知识库成功: {name} ({kb_type})")
            return knowledge_base
            
        except Exception as e:
            logger.error(f"创建知识库失败: {e}")
            raise
    
    async def add_document(
        self,
        knowledge_base_id: str,
        title: str,
        content: str,
        content_type: str = "text",
        metadata: Dict[str, Any] = None,
        db: Session = None
    ) -> KnowledgeDocument:
        """添加文档到知识库"""
        
        try:
            # 获取知识库
            kb = db.query(AgentKnowledgeBase).filter(
                AgentKnowledgeBase.id == knowledge_base_id
            ).first()
            
            if not kb:
                raise ValueError(f"知识库不存在: {knowledge_base_id}")
            
            # 创建文档
            document = KnowledgeDocument(
                knowledge_base_id=knowledge_base_id,
                title=title,
                content=content,
                content_type=content_type,
                metadata=metadata or {}
            )
            
            # 处理文档内容（分块、向量化等）
            chunks = await self._process_document_content(content, kb.chunk_size, kb.chunk_overlap)
            document.chunks = chunks
            
            # 生成向量嵌入（模拟）
            embeddings = await self._generate_embeddings(chunks)
            document.embeddings = embeddings
            
            db.add(document)
            
            # 更新知识库统计
            kb.document_count += 1
            kb.total_chunks += len(chunks)
            
            db.commit()
            db.refresh(document)
            
            logger.info(f"添加文档成功: {title} ({len(chunks)} 个分块)")
            return document
            
        except Exception as e:
            logger.error(f"添加文档失败: {e}")
            raise
    
    async def query_knowledge(
        self,
        agent_id: str,
        query: str,
        max_results: int = 5,
        db: Session = None
    ) -> List[Dict[str, Any]]:
        """查询知识库"""

        if not db:
            logger.error("数据库会话为None，无法查询知识库")
            raise ValueError("数据库会话不能为None")

        try:
            # 获取智能体的所有知识库
            knowledge_bases = db.query(AgentKnowledgeBase).filter(
                AgentKnowledgeBase.agent_id == agent_id
            ).all()
            
            if not knowledge_bases:
                return []
            
            results = []
            
            for kb in knowledge_bases:
                # 查询每个知识库
                kb_results = await self._query_knowledge_base(kb, query, max_results, db)
                results.extend(kb_results)
            
            # 按相关性排序
            results.sort(key=lambda x: x.get("relevance", 0), reverse=True)
            
            return results[:max_results]
            
        except Exception as e:
            logger.error(f"查询知识库失败: {e}")
            return []
    
    async def _query_knowledge_base(
        self,
        knowledge_base: AgentKnowledgeBase,
        query: str,
        max_results: int,
        db: Session
    ) -> List[Dict[str, Any]]:
        """查询单个知识库"""
        
        try:
            # 获取知识库中的文档
            documents = db.query(KnowledgeDocument).filter(
                KnowledgeDocument.knowledge_base_id == knowledge_base.id
            ).all()
            
            if not documents:
                return []
            
            results = []
            query_lower = query.lower()
            
            for doc in documents:
                # 简单的文本匹配（实际应该使用向量相似度）
                relevance = 0.0
                
                # 标题匹配
                if query_lower in doc.title.lower():
                    relevance += 0.5
                
                # 内容匹配
                content_lower = doc.content.lower()
                if query_lower in content_lower:
                    relevance += 0.3
                    
                    # 计算匹配度
                    query_words = query_lower.split()
                    content_words = content_lower.split()
                    
                    matches = sum(1 for word in query_words if word in content_words)
                    if len(query_words) > 0:
                        relevance += (matches / len(query_words)) * 0.2
                
                if relevance > 0:
                    # 提取相关片段
                    snippet = self._extract_snippet(doc.content, query, 200)
                    
                    results.append({
                        "source": knowledge_base.name,
                        "document_title": doc.title,
                        "content": snippet,
                        "relevance": relevance,
                        "metadata": {
                            "document_id": doc.id,
                            "knowledge_base_id": knowledge_base.id,
                            "content_type": doc.content_type
                        }
                    })
            
            # 按相关性排序
            results.sort(key=lambda x: x["relevance"], reverse=True)
            
            return results[:max_results]
            
        except Exception as e:
            logger.error(f"查询知识库失败 {knowledge_base.name}: {e}")
            return []
    
    def _extract_snippet(self, content: str, query: str, max_length: int = 200) -> str:
        """提取相关片段"""
        
        content_lower = content.lower()
        query_lower = query.lower()
        
        # 找到查询词在内容中的位置
        index = content_lower.find(query_lower)
        
        if index == -1:
            # 如果没有找到，返回开头部分
            return content[:max_length] + "..." if len(content) > max_length else content
        
        # 计算片段的开始和结束位置
        start = max(0, index - max_length // 2)
        end = min(len(content), start + max_length)
        
        snippet = content[start:end]
        
        # 添加省略号
        if start > 0:
            snippet = "..." + snippet
        if end < len(content):
            snippet = snippet + "..."
        
        return snippet
    
    async def _process_document_content(
        self, 
        content: str, 
        chunk_size: int = 1000, 
        chunk_overlap: int = 200
    ) -> List[Dict[str, Any]]:
        """处理文档内容，分块"""
        
        chunks = []
        
        # 简单的分块策略
        if len(content) <= chunk_size:
            chunks.append({
                "index": 0,
                "content": content,
                "start": 0,
                "end": len(content)
            })
        else:
            start = 0
            index = 0
            
            while start < len(content):
                end = min(start + chunk_size, len(content))
                
                # 尝试在句号处分割
                if end < len(content):
                    last_period = content.rfind("。", start, end)
                    if last_period > start + chunk_size // 2:
                        end = last_period + 1
                
                chunk_content = content[start:end]
                chunks.append({
                    "index": index,
                    "content": chunk_content,
                    "start": start,
                    "end": end
                })
                
                # 下一个分块的开始位置（考虑重叠）
                start = max(start + 1, end - chunk_overlap)
                index += 1
        
        return chunks
    
    async def _generate_embeddings(self, chunks: List[Dict[str, Any]]) -> List[List[float]]:
        """生成向量嵌入（模拟）"""
        
        embeddings = []
        
        for chunk in chunks:
            # 模拟向量嵌入（实际应该调用嵌入模型）
            content = chunk["content"]
            
            # 简单的特征提取
            embedding = []
            for i in range(384):  # 模拟384维向量
                # 基于内容长度和字符的简单计算
                value = (len(content) + ord(content[i % len(content)])) / 1000.0
                embedding.append(value % 1.0)
            
            embeddings.append(embedding)
        
        return embeddings
    
    async def get_knowledge_base_stats(
        self, 
        agent_id: str, 
        db: Session
    ) -> Dict[str, Any]:
        """获取知识库统计信息"""
        
        try:
            knowledge_bases = db.query(AgentKnowledgeBase).filter(
                AgentKnowledgeBase.agent_id == agent_id
            ).all()
            
            total_documents = sum(kb.document_count for kb in knowledge_bases)
            total_chunks = sum(kb.total_chunks for kb in knowledge_bases)
            
            kb_by_type = {}
            for kb in knowledge_bases:
                if kb.type not in kb_by_type:
                    kb_by_type[kb.type] = 0
                kb_by_type[kb.type] += 1
            
            return {
                "total_knowledge_bases": len(knowledge_bases),
                "total_documents": total_documents,
                "total_chunks": total_chunks,
                "knowledge_bases_by_type": kb_by_type,
                "knowledge_bases": [kb.to_dict() for kb in knowledge_bases]
            }
            
        except Exception as e:
            logger.error(f"获取知识库统计失败: {e}")
            return {
                "total_knowledge_bases": 0,
                "total_documents": 0,
                "total_chunks": 0,
                "knowledge_bases_by_type": {},
                "knowledge_bases": []
            }
    
    async def delete_knowledge_base(
        self, 
        knowledge_base_id: str, 
        db: Session
    ) -> bool:
        """删除知识库"""
        
        try:
            # 删除知识库中的所有文档
            db.query(KnowledgeDocument).filter(
                KnowledgeDocument.knowledge_base_id == knowledge_base_id
            ).delete()
            
            # 删除知识库
            kb = db.query(AgentKnowledgeBase).filter(
                AgentKnowledgeBase.id == knowledge_base_id
            ).first()
            
            if kb:
                db.delete(kb)
                db.commit()
                logger.info(f"删除知识库成功: {kb.name}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"删除知识库失败: {e}")
            return False
    
    async def update_document(
        self,
        document_id: str,
        title: str = None,
        content: str = None,
        metadata: Dict[str, Any] = None,
        db: Session = None
    ) -> bool:
        """更新文档"""
        
        try:
            document = db.query(KnowledgeDocument).filter(
                KnowledgeDocument.id == document_id
            ).first()
            
            if not document:
                return False
            
            if title is not None:
                document.title = title
            
            if content is not None:
                document.content = content
                # 重新处理内容
                kb = db.query(AgentKnowledgeBase).filter(
                    AgentKnowledgeBase.id == document.knowledge_base_id
                ).first()
                
                if kb:
                    chunks = await self._process_document_content(
                        content, kb.chunk_size, kb.chunk_overlap
                    )
                    document.chunks = chunks
                    
                    embeddings = await self._generate_embeddings(chunks)
                    document.embeddings = embeddings
            
            if metadata is not None:
                document.metadata = metadata
            
            db.commit()
            logger.info(f"更新文档成功: {document.title}")
            return True
            
        except Exception as e:
            logger.error(f"更新文档失败: {e}")
            return False
