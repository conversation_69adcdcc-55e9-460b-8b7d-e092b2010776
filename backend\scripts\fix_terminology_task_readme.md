# 术语采集任务修复工具

这个脚本用于修复术语采集任务的结果，特别是针对汽车领域的任务。

## 背景

术语采集系统中存在一个问题：有些任务（例如ID为e4084286-a0ff-4ee7-92cd-c40badde4b09的汽车术语任务）显示100%完成，但只有少量术语数据。这是因为数据库中的表结构与代码中的模型定义不匹配，导致术语提取过程无法正确执行。

## 主要修复内容

1. 更新了模型定义，使其与数据库表结构匹配，特别是`terminology_collection_tasks`表中的`id`和`task_id`字段
2. 修改了查询逻辑，使用`task_id`而不是`id`来查找任务
3. 调整了术语提取和保存逻辑，确保结果正确保存到数据库
4. 添加了术语质量评估功能
5. 增强了可视化功能

## 使用方法

### 命令行参数

```
python fix_terminology_task.py [task_id] [选项]
```

#### 基本选项

- `task_id`: 任务ID，例如`e4084286-a0ff-4ee7-92cd-c40badde4b09`
- `--force`: 强制重新生成结果，即使任务状态为已完成
- `--init-db`: 初始化数据库（通常不需要，因为脚本会使用现有的数据库表）
- `--create-sample`: 创建示例任务
- `--test`: 测试术语提取功能
- `--view`: 查看术语结果

### 示例用法

1. 创建示例任务：

```bash
python fix_terminology_task.py --create-sample e4084286-a0ff-4ee7-92cd-c40badde4b09
```

2. 修复术语采集任务：

```bash
python fix_terminology_task.py e4084286-a0ff-4ee7-92cd-c40badde4b09 --force
```

3. 查看术语结果：

```bash
python fix_terminology_task.py --view e4084286-a0ff-4ee7-92cd-c40badde4b09
```

4. 测试术语提取功能：

```bash
python fix_terminology_task.py --test e4084286-a0ff-4ee7-92cd-c40badde4b09
```

## 输出内容

脚本会生成以下输出：

1. 数据库中的术语结果
2. CSV格式的术语列表（保存在`output/task_id/domain_terms.csv`）
3. JSON格式的术语列表（保存在`output/task_id/domain_terms.json`）
4. 术语分布的可视化图表（保存在`output/task_id/domain_terms.png`）
5. 日志信息（保存在`fix_terminology_task.log`）

## 术语提取方法

脚本使用以下方法提取术语：

1. TF-IDF：基于词频-逆文档频率计算术语重要性
2. TextRank：基于图排序算法提取关键词
3. N-gram：提取常见的多词组合
4. 领域匹配：基于领域词表进行匹配
5. 模式匹配：使用正则表达式匹配特定格式的术语

这些方法的结果会被合并，并按权重排序，最终得到最终的术语列表。

## 术语质量评估

脚本包含一个简单的术语质量评估功能，计算以下指标：

1. 精确度：提取的术语中有多少是领域术语
2. 召回率：领域术语中有多少被成功提取
3. F1得分：精确度和召回率的调和平均
4. 覆盖率：提取的术语数量与理想数量的比值

## 注意事项

1. 该脚本依赖于`terminology_extractor.py`模块，请确保该模块在Python路径中可用
2. 脚本需要访问PostgreSQL数据库，请确保数据库连接信息正确
3. 如果遇到"任务文本为空"的错误，请使用`--create-sample`选项重新创建任务
4. 可视化功能依赖于matplotlib库，如果显示中文字符出现问题，可能需要配置中文字体

## 修复效果

修复后的系统现在能够真实有效地执行术语采集任务，不再生成假数据，而是从文本中提取真实术语。例如，对于汽车领域的任务，系统能够正确提取"制动"、"发动机"、"变速箱"等术语，并按权重排序。 