@echo off
echo ===== WAN模型目录结构修复工具 =====
echo.
echo 该工具将修复WAN模型目录结构问题，创建缺失的子目录和配置文件。
echo 主要解决错误: "模型目录结构不完整，缺少以下子目录: scheduler, unet"
echo.

setlocal
cd %~dp0..\..

echo 设置Python环境...
set PYTHONPATH=%cd%

echo 运行修复脚本...
python -m backend.scripts.fix_wan_model_dirs %*

if %errorlevel% neq 0 (
    echo.
    echo 修复脚本执行失败，错误代码: %errorlevel%
    echo 请查看日志获取详细信息。
    echo.
    pause
    exit /b %errorlevel%
)

echo.
echo 修复完成！请重新启动应用程序以应用修复。
echo.
pause 