"""
AI服务模块
提供与AI模型交互的接口
"""

import logging
import os
import time
import random
import requests
import json
from typing import Dict, List, Any, Optional, Union

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AIService:
    """AI服务类"""
    
    def __init__(self):
        """初始化AI服务"""
        logger.info("初始化AI服务")
        # 在完整实现中，这里会初始化各种AI模型
        self._initialized = True
        self._available = True
        
        # Ollama配置
        self.ollama_base_url = os.environ.get("OLLAMA_API_BASE", "http://localhost:5003")
        self.ollama_model = os.environ.get("OLLAMA_MODEL", "llama3")
        logger.info(f"Ollama配置: URL={self.ollama_base_url}, 模型={self.ollama_model}")
        
    def get_text_generation(self, prompt: str, max_tokens: int = 500, 
                           temperature: float = 0.7) -> Dict[str, Any]:
        """
        生成文本
        
        Args:
            prompt: 提示词
            max_tokens: 最大生成token数
            temperature: 温度参数
            
        Returns:
            生成的文本内容
        """
        logger.info(f"生成文本，提示词长度：{len(prompt)}字符")
        
        try:
            # 尝试使用Ollama生成
            result = self._generate_with_ollama(prompt, max_tokens, temperature)
            if result:
                return result
        except Exception as e:
            logger.error(f"使用Ollama生成文本失败: {e}")
        
        # 模拟生成内容作为备选方案
        result = {
            "text": f"这是一个模拟的AI生成内容，基于提示词：{prompt[:20]}...",
            "status": "success",
            "usage": {
                "prompt_tokens": len(prompt) // 4,
                "completion_tokens": max_tokens // 2,
                "total_tokens": (len(prompt) // 4) + (max_tokens // 2)
            }
        }
        
        return result
    
    def _generate_with_ollama(self, prompt: str, max_tokens: int = 500, 
                             temperature: float = 0.7) -> Dict[str, Any]:
        """
        使用Ollama生成文本
        
        Args:
            prompt: 提示词
            max_tokens: 最大生成token数
            temperature: 温度参数
            
        Returns:
            生成的文本内容
        """
        try:
            api_url = f"{self.ollama_base_url}/api/generate"
            
            payload = {
                "model": self.ollama_model,
                "prompt": prompt,
                "options": {
                    "temperature": temperature,
                    "num_predict": max_tokens
                },
                "stream": False
            }
            
            logger.info(f"请求Ollama生成文本，模型: {self.ollama_model}")
            
            response = requests.post(api_url, json=payload, timeout=60)
            
            if response.status_code == 200:
                data = response.json()
                return {
                    "text": data.get("response", ""),
                    "status": "success",
                    "usage": {
                        "prompt_tokens": len(prompt) // 4,  # 近似值
                        "completion_tokens": len(data.get("response", "")) // 4,  # 近似值
                        "total_tokens": (len(prompt) + len(data.get("response", ""))) // 4  # 近似值
                    }
                }
            else:
                logger.error(f"Ollama API返回错误: {response.status_code}, {response.text}")
                return None
                
        except Exception as e:
            logger.error(f"调用Ollama API失败: {e}")
            return None
    
    def generate_ppt_content(self, title: str, theme: str, outline: str) -> Dict[str, Any]:
        """
        生成PPT内容
        
        Args:
            title: PPT标题
            theme: PPT主题
            outline: 内容大纲
            
        Returns:
            生成的PPT内容结构
        """
        # 构建提示词
        prompt = f"""请根据以下信息生成一个详细的PPT内容结构：
标题: {title}
主题: {theme}
大纲: 
{outline}

请提供以下内容:
1. 每个章节的详细内容，包括要点和解释
2. 适合每个章节的图表建议（如果适用）
3. 总结内容

输出格式应为JSON，包含以下结构:
{{
  "sections": [
    {{
      "title": "章节标题",
      "content": ["要点1", "要点2"...],
      "details": "章节详细内容...",
      "chart_suggestion": "图表建议（如有）" 
    }}
  ],
  "summary": "总结内容"
}}
"""
        
        # 使用Ollama生成内容
        result = self.get_text_generation(prompt, max_tokens=2000, temperature=0.7)
        
        # 解析生成的内容
        try:
            # 尝试直接解析JSON
            content_text = result.get("text", "")
            # 提取JSON部分
            json_text = self._extract_json(content_text)
            content_structure = json.loads(json_text)
            return content_structure
        except json.JSONDecodeError as e:
            logger.error(f"解析AI生成的PPT内容结构失败: {e}")
            # 返回基本结构
            return {
                "sections": [
                    {
                        "title": "引言",
                        "content": [f"{title}概述", f"{theme}的重要性"],
                        "details": f"这是关于{title}的介绍部分...",
                        "chart_suggestion": None
                    }
                ],
                "summary": f"总结了{title}的主要内容"
            }
    
    def _extract_json(self, text: str) -> str:
        """
        从文本中提取JSON部分
        
        Args:
            text: 包含JSON的文本
            
        Returns:
            提取的JSON文本
        """
        # 尝试查找JSON开始的位置
        start_pos = text.find('{')
        if start_pos == -1:
            return "{}"
            
        # 尝试平衡括号来提取完整JSON
        bracket_count = 0
        in_quotes = False
        escape_next = False
        
        for i in range(start_pos, len(text)):
            char = text[i]
            
            if char == '"' and not escape_next:
                in_quotes = not in_quotes
            elif char == '\\' and not escape_next:
                escape_next = True
                continue
            
            if not in_quotes:
                if char == '{':
                    bracket_count += 1
                elif char == '}':
                    bracket_count -= 1
                    if bracket_count == 0:
                        return text[start_pos:i+1]
            
            escape_next = False
            
        return "{}"  # 如果无法提取，返回空对象
    
    def generate_content_ideas(self, topic: str, count: int = 5) -> List[str]:
        """
        生成内容创意
        
        Args:
            topic: 主题
            count: 创意数量
            
        Returns:
            创意列表
        """
        ideas = [
            f"{topic}的基本概念和定义",
            f"{topic}的历史发展",
            f"{topic}的应用场景",
            f"{topic}的核心技术",
            f"{topic}的未来趋势",
            f"{topic}的挑战与机遇",
            f"{topic}的实际案例分析"
        ]
        
        # 返回指定数量的创意
        return ideas[:count]
    
    def generate_slide_content(self, title: str, keywords: List[str]) -> Dict[str, Any]:
        """
        生成幻灯片内容
        
        Args:
            title: 标题
            keywords: 关键词列表
            
        Returns:
            幻灯片内容
        """
        # 模拟生成幻灯片内容
        content = {
            "title": title,
            "paragraphs": [
                f"这是关于{title}的第一个要点。",
                f"这是关于{title}的第二个要点。",
                f"这是关于{title}的第三个要点，包含关键词：{', '.join(keywords[:2]) if keywords else '未指定'}。"
            ],
            "bullet_points": [
                "要点一：内容概述",
                "要点二：核心分析",
                "要点三：结论与建议"
            ]
        }
        
        return content
    
    def is_available(self) -> bool:
        """
        检查AI服务是否可用
        
        Returns:
            服务可用状态
        """
        return self._available and self._initialized
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取服务状态
        
        Returns:
            服务状态信息
        """
        # 检查Ollama是否可用
        ollama_available = False
        try:
            response = requests.get(f"{self.ollama_base_url}", timeout=5)
            ollama_available = response.status_code == 200
        except:
            pass
            
        return {
            "status": "operational" if self._available else "unavailable",
            "initialized": self._initialized,
            "models_loaded": True,
            "api_status": "connected",
            "ollama_available": ollama_available,
            "ollama_model": self.ollama_model
        }

    def chat_completion(self, messages: List[Dict[str, str]], 
                        max_tokens: int = 1000, 
                        temperature: float = 0.7) -> Dict[str, Any]:
        """
        使用Ollama进行聊天完成，支持对话历史
        
        Args:
            messages: 消息历史，格式为[{"role": "system|user|assistant", "content": "消息内容"}, ...]
            max_tokens: 最大生成令牌数
            temperature: 生成温度，控制随机性
            
        Returns:
            Dict: 包含响应消息的字典
        """
        try:
            # Ollama API endpoint
            url = "http://localhost:11434/api/chat"
            
            # 准备请求数据
            payload = {
                "model": "deepseek-r1:8b",  # 使用性能最佳的DeepSeek R1 8B模型
                "messages": messages,
                "options": {
                    "temperature": temperature,
                    "num_predict": min(max_tokens, 300),  # 进一步限制输出长度
                    "top_p": 0.9,
                    "top_k": 40,
                    "repeat_penalty": 1.1,
                    "num_ctx": 2048,  # 限制上下文长度
                    "num_thread": 8   # 使用多线程加速
                },
                "stream": False
            }

            # 发送请求（增加超时时间）
            response = requests.post(url, json=payload, timeout=30)
            
            # 解析响应
            if response.status_code == 200:
                result = response.json()
                
                return {
                    "message": {
                        "role": "assistant",
                        "content": result.get("message", {}).get("content", "")
                    },
                    "model": result.get("model", "unknown"),
                    "created_at": result.get("created_at", ""),
                    "done": True
                }
            else:
                # 如果请求失败，尝试解析错误消息
                error_message = f"请求失败: {response.status_code}"
                try:
                    error_data = response.json()
                    error_message = error_data.get("error", error_message)
                except:
                    pass
                    
                # 使用备用方法
                return self._fallback_chat_completion(messages, max_tokens, temperature)
        except Exception as e:
            print(f"使用Ollama处理聊天请求时出错: {str(e)}")
            
            # 使用备用方法
            return self._fallback_chat_completion(messages, max_tokens, temperature)

    def _fallback_chat_completion(self, messages: List[Dict[str, str]], 
                            max_tokens: int = 1000,
                            temperature: float = 0.7) -> Dict[str, Any]:
        """
        当Ollama不可用时的备用聊天完成方法
        
        Args:
            messages: 消息历史
            max_tokens: 最大生成令牌数
            temperature: 生成温度
            
        Returns:
            Dict: 包含响应消息的字典
        """
        try:
            # 提取最后一条用户消息作为提示
            last_user_msg = ""
            system_msg = ""
            
            for msg in messages:
                if msg["role"] == "user":
                    last_user_msg = msg["content"]
                elif msg["role"] == "system" and not system_msg:
                    system_msg = msg["content"]
            
            # 如果没有找到用户消息，使用最后一条消息
            if not last_user_msg and messages:
                last_user_msg = messages[-1]["content"]
            
            # 创建完整提示
            prompt = ""
            if system_msg:
                prompt += f"系统指令: {system_msg}\n\n"
                
            # 添加对话历史上下文
            context = []
            for i, msg in enumerate(messages):
                if i >= len(messages) - 5:  # 只使用最近的5条消息作为上下文
                    role_name = "助手" if msg["role"] == "assistant" else ("系统" if msg["role"] == "system" else "用户")
                    context.append(f"{role_name}: {msg['content']}")
            
            if context:
                prompt += "对话历史:\n" + "\n".join(context) + "\n\n"
                
            prompt += f"请回答: {last_user_msg}"
            
            # 使用基本的文本生成作为备用
            response_text = self.get_text_generation(prompt, max_tokens, temperature).get("text", "")
            
            # 清理响应文本
            if response_text.startswith("助手:"):
                response_text = response_text[3:].strip()
                
            return {
                "message": {
                    "role": "assistant",
                    "content": response_text
                },
                "model": "fallback_model",
                "done": True
            }
        except Exception as e:
            print(f"备用聊天完成方法出错: {str(e)}")
            
            # 返回一个友好的错误消息
            return {
                "message": {
                    "role": "assistant",
                    "content": "抱歉，我现在无法处理您的请求。请稍后再试。"
                },
                "model": "error_fallback",
                "done": True
            }

# 单例实例
_ai_service = None

def get_ai_service() -> AIService:
    """
    获取AI服务实例
    
    Returns:
        AI服务实例
    """
    global _ai_service
    if _ai_service is None:
        _ai_service = AIService()
    return _ai_service 