"""
音频和语言处理服务模块
"""

# 从相对路径导入服务
from .asr_service import get_asr_service
from .task_scheduler import TaskScheduler, get_task_scheduler
from .transcription_service import get_transcription_service
from .tts_service import get_tts_service
from .chat_service import chat_service
from .audio_service import get_audio_service
from .mt_service import MachineTranslationService

# 公开API
__all__ = [
    'get_asr_service',
    'get_task_scheduler',
    'get_transcription_service',
    'get_tts_service',
    'chat_service',
    'get_audio_service',
    'MachineTranslationService'
]

# 服务模块初始化
import os
import sys
import logging
import importlib
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def initialize_services():
    """初始化所有服务"""
    # 初始化存储目录
    try:
        from .init_storage import initialize_storage
        initialize_storage()
    except ImportError:
        logger.warning("未找到存储初始化模块，跳过存储目录初始化")
    except Exception as e:
        logger.error(f"初始化存储目录时出错: {e}")

    # 确保适配器目录存在
    adapters_dir = Path(__file__).parent / "adapters"
    if not adapters_dir.exists():
        adapters_dir.mkdir(exist_ok=True)
        # 创建空的__init__.py文件
        init_file = adapters_dir / "__init__.py"
        if not init_file.exists():
            with open(init_file, 'w') as f:
                f.write('"""适配器包"""')

    # 动态加载所有适配器
    load_adapters()
        
    logger.info("服务模块初始化完成")

def load_adapters():
    """动态加载所有适配器"""
    try:
        # 获取适配器目录
        adapters_dir = Path(__file__).parent / "adapters"
        
        # 如果目录不存在，直接返回
        if not adapters_dir.exists():
            logger.warning("适配器目录不存在，跳过加载")
            return
        
        # 查找所有适配器模块
        adapter_modules = []
        for file in adapters_dir.glob("*.py"):
            if file.name == "__init__.py":
                continue
            
            module_name = file.stem
            adapter_modules.append(module_name)
        
        # 加载适配器模块
        for module_name in adapter_modules:
            try:
                full_module_name = f"services.adapters.{module_name}"
                importlib.import_module(full_module_name)
                logger.info(f"已加载适配器模块: {full_module_name}")
            except Exception as e:
                logger.error(f"加载适配器模块 {module_name} 失败: {e}")
        
        logger.info(f"共加载了 {len(adapter_modules)} 个适配器模块")
    except Exception as e:
        logger.error(f"加载适配器时出错: {e}")

# 在导入时初始化
try:
    initialize_services()
except Exception as e:
    logger.error(f"初始化服务时出错: {e}")

# 由于可能存在循环导入问题，我们不在这里导入其他模块
# 在需要时动态导入 