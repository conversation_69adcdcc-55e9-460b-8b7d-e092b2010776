import { ref, watch } from 'vue';

/**
 * 本地存储钩子
 * @param {string} key - 存储键名
 * @param {any} initialValue - 初始值
 * @param {Object} options - 配置选项
 * @param {boolean} options.serializer - 自定义序列化函数
 * @param {boolean} options.deserializer - 自定义反序列化函数
 * @param {string} options.storage - 存储类型，可选 'local' 或 'session'
 * @returns {Array} [值, 设置函数, 其他方法]
 */
export default function useLocalStorage(key, initialValue = null, options = {}) {
  if (!key) {
    throw new Error('useLocalStorage: key is required');
  }
  
  const {
    serializer = JSON.stringify,
    deserializer = JSON.parse,
    storage = 'local'
  } = options;
  
  // 获取存储对象
  const storageObject = storage === 'local' ? localStorage : sessionStorage;
  
  // 从存储中获取值
  const getStoredValue = () => {
    try {
      const item = storageObject.getItem(key);
      // 检查是否存在存储值
      if (item !== null) {
        return deserializer(item);
      }
      
      // 如果初始值是函数，执行它获取结果
      const result = typeof initialValue === 'function' ? initialValue() : initialValue;
      
      // 如果有初始值，存储它
      if (result !== null) {
        storageObject.setItem(key, serializer(result));
      }
      
      return result;
    } catch (error) {
      console.error(`useLocalStorage: Error reading ${key} from ${storage}Storage`, error);
      return initialValue;
    }
  };
  
  // 存储值的ref
  const storedValue = ref(getStoredValue());
  
  // 更新存储的值
  const setValue = (value) => {
    try {
      // 允许值是一个函数
      const valueToStore = value instanceof Function ? value(storedValue.value) : value;
      
      // 更新ref
      storedValue.value = valueToStore;
      
      // 如果值为null，则移除该项
      if (valueToStore === null) {
        storageObject.removeItem(key);
      } else {
        // 否则存储序列化后的值
        storageObject.setItem(key, serializer(valueToStore));
      }
    } catch (error) {
      console.error(`useLocalStorage: Error storing ${key} to ${storage}Storage`, error);
    }
  };
  
  // 移除存储项
  const removeItem = () => {
    try {
      storageObject.removeItem(key);
      storedValue.value = null;
    } catch (error) {
      console.error(`useLocalStorage: Error removing ${key} from ${storage}Storage`, error);
    }
  };
  
  // 监听窗口存储事件，实现跨标签页同步
  const handleStorageChange = (event) => {
    if (event.key === key && event.storageArea === storageObject) {
      try {
        // 如果新值为null，则该项被删除
        if (event.newValue === null) {
          storedValue.value = null;
        } else {
          // 否则更新为新值
          storedValue.value = deserializer(event.newValue);
        }
      } catch (error) {
        console.error(`useLocalStorage: Error syncing ${key} from ${storage}Storage event`, error);
      }
    }
  };
  
  // 添加存储事件监听
  if (typeof window !== 'undefined') {
    window.addEventListener('storage', handleStorageChange);
  }
  
  // 监听ref值的变化，同步到存储
  watch(storedValue, (newValue) => {
    if (newValue === null) {
      storageObject.removeItem(key);
    } else {
      storageObject.setItem(key, serializer(newValue));
    }
  }, { deep: true });
  
  return [storedValue, setValue, { removeItem }];
} 