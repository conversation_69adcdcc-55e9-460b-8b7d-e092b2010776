import logging
import os
from typing import Dict, Any, Optional
import requests
from pydantic import BaseModel

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EmotionConfig(BaseModel):
    """情感分析配置"""
    api_key: str
    api_url: str
    language: str = "zh"
    timeout: int = 30

class EmotionService:
    """情感分析服务"""
    
    def __init__(self, config: EmotionConfig):
        self.config = config
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {config.api_key}"
        }
        
    async def analyze_text(self, text: str) -> Optional[Dict[str, Any]]:
        """分析文本情感"""
        try:
            response = requests.post(
                self.config.api_url,
                headers=self.headers,
                json={
                    "text": text,
                    "language": self.config.language
                },
                timeout=self.config.timeout
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"Emotion analysis failed: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            logger.error(f"Error in emotion analysis: {str(e)}")
            return None
            
    async def analyze_speech(self, audio_file: str) -> Optional[Dict[str, Any]]:
        """分析语音情感"""
        try:
            with open(audio_file, "rb") as f:
                files = {"audio": f}
                response = requests.post(
                    f"{self.config.api_url}/audio",
                    headers=self.headers,
                    files=files,
                    timeout=self.config.timeout
                )
                
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"Speech emotion analysis failed: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            logger.error(f"Error in speech emotion analysis: {str(e)}")
            return None

def get_emotion_service():
    """获取情感分析服务实例"""
    config = EmotionConfig(
        api_key=os.getenv("EMOTION_API_KEY"),
        api_url=os.getenv("EMOTION_API_URL", "https://api.emotion-analysis.com/v1"),
        language=os.getenv("EMOTION_LANGUAGE", "zh")
    )
    return EmotionService(config) 