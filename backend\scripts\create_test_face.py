#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
创建一个带有脸部标记的测试图像用于SadTalker测试
"""

import os
import sys
import cv2
import numpy as np
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    # 输出文件路径
    script_dir = os.path.dirname(os.path.abspath(__file__))
    output_path = os.path.join(script_dir, "test_image.jpg")
    
    # 创建一个512x512的图像
    img_size = 512
    img = np.ones((img_size, img_size, 3), dtype=np.uint8) * 220  # 浅灰背景
    
    # 创建一个更真实的人脸图像
    try:
        # 绘制椭圆形脸型 - 肤色
        face_center = (img_size // 2, img_size // 2)
        face_axes = (160, 200)
        cv2.ellipse(img, face_center, face_axes, 0, 0, 360, (203, 174, 159), -1)
        
        # 添加一些肤色渐变
        overlay = img.copy()
        cv2.ellipse(overlay, face_center, (145, 180), 0, 0, 360, (210, 180, 165), -1)
        cv2.addWeighted(overlay, 0.5, img, 0.5, 0, img)
        
        # 绘制发际线/头发
        hair_color = (80, 60, 40)  # 棕色
        hair_points = np.array([
            [156, 150], [116, 180], [106, 230], [110, 280],  # 左侧
            [400, 150], [430, 180], [435, 230], [430, 280],  # 右侧
            [430, 280], [400, 320], [350, 340], [300, 350], [250, 350], [200, 340], [150, 320], [110, 280]  # 底部
        ], np.int32)
        cv2.fillPoly(img, [hair_points], hair_color)
        
        # 绘制眼睛区域
        eye_white_color = (240, 240, 240)
        eye_color = (70, 40, 20)  # 棕色
        pupil_color = (20, 20, 20)  # 近黑色
        
        # 左眼
        left_eye_center = (img_size // 2 - 60, img_size // 2 - 30)
        cv2.ellipse(img, left_eye_center, (30, 15), 0, 0, 360, eye_white_color, -1)
        cv2.ellipse(img, left_eye_center, (15, 15), 0, 0, 360, eye_color, -1)
        cv2.circle(img, left_eye_center, 7, pupil_color, -1)
        cv2.circle(img, (left_eye_center[0] + 5, left_eye_center[1] - 5), 3, (255, 255, 255), -1)  # 高光
        
        # 右眼
        right_eye_center = (img_size // 2 + 60, img_size // 2 - 30)
        cv2.ellipse(img, right_eye_center, (30, 15), 0, 0, 360, eye_white_color, -1)
        cv2.ellipse(img, right_eye_center, (15, 15), 0, 0, 360, eye_color, -1)
        cv2.circle(img, right_eye_center, 7, pupil_color, -1)
        cv2.circle(img, (right_eye_center[0] + 5, right_eye_center[1] - 5), 3, (255, 255, 255), -1)  # 高光
        
        # 眉毛
        eyebrow_color = (60, 40, 20)
        # 左眉毛
        cv2.ellipse(img, (left_eye_center[0], left_eye_center[1] - 20), (35, 10), 0, -180, 0, eyebrow_color, 5)
        # 右眉毛
        cv2.ellipse(img, (right_eye_center[0], right_eye_center[1] - 20), (35, 10), 0, -180, 0, eyebrow_color, 5)
        
        # 鼻子
        nose_tip = (img_size // 2, img_size // 2 + 30)
        nose_bridge_start = (img_size // 2, img_size // 2 - 40)
        nose_bridge_end = (img_size // 2, img_size // 2 + 20)
        
        nose_color = (190, 160, 145)
        cv2.line(img, nose_bridge_start, nose_bridge_end, nose_color, 4)
        cv2.ellipse(img, nose_tip, (23, 15), 0, 0, 180, nose_color, -1)
        
        # 嘴巴
        mouth_center = (img_size // 2, img_size // 2 + 80)
        mouth_color = (140, 90, 100)
        
        # 嘴唇
        cv2.ellipse(img, mouth_center, (60, 20), 0, 0, 180, mouth_color, -1)
        
        # 添加一些细节
        # 上唇
        upper_lip_center = (mouth_center[0], mouth_center[1] - 5)
        cv2.ellipse(img, upper_lip_center, (55, 12), 0, 0, 180, (150, 100, 110), -1)
        
        # 法令纹/面部阴影
        shadow_color = (185, 155, 140)
        cv2.line(img, (img_size // 2 - 50, img_size // 2 + 30), (img_size // 2 - 60, img_size // 2 + 90), shadow_color, 3)
        cv2.line(img, (img_size // 2 + 50, img_size // 2 + 30), (img_size // 2 + 60, img_size // 2 + 90), shadow_color, 3)
        
        # 下巴轮廓增强
        chin_center = (img_size // 2, img_size // 2 + 150)
        cv2.ellipse(img, chin_center, (100, 80), 0, -45, 45, (190, 160, 145), 4)
        
        # 保存图像
        cv2.imwrite(output_path, img)
        logger.info(f"已创建人脸测试图像: {output_path}")
        
        # 也可以显示图像
        # cv2.imshow("Test Face", img)
        # cv2.waitKey(0)
        # cv2.destroyAllWindows()
        
    except Exception as e:
        logger.error(f"创建人脸测试图像失败: {e}")
        sys.exit(1)

 