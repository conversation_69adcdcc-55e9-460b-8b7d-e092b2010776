import re
from typing import Dict, Any, Optional

def replace_placeholders(text: str, user: Optional[Any] = None) -> str:
    """
    替换文本中的占位符
    
    Args:
        text: 输入文本，可能包含占位符如 {username}
        user: 用户对象，用于提取用户相关信息
        
    Returns:
        str: 替换占位符后的文本
    """
    if not text:
        return ""
    
    # 初始化替换字典
    replacements = {}
    
    # 如果提供了用户对象，提取用户相关信息
    if user:
        # 用户基本信息
        replacements.update({
            "username": getattr(user, "username", "用户"),
            "user_id": str(getattr(user, "id", "")),
            "email": getattr(user, "email", ""),
            "fullname": getattr(user, "full_name", getattr(user, "username", "用户"))
        })
    
    # 系统信息
    import datetime
    now = datetime.datetime.now()
    replacements.update({
        "date": now.strftime("%Y-%m-%d"),
        "time": now.strftime("%H:%M:%S"),
        "datetime": now.strftime("%Y-%m-%d %H:%M:%S"),
        "year": now.strftime("%Y"),
        "month": now.strftime("%m"),
        "day": now.strftime("%d")
    })
    
    # 替换所有占位符
    for key, value in replacements.items():
        pattern = r'\{' + key + r'\}'
        text = re.sub(pattern, str(value), text)
    
    return text 