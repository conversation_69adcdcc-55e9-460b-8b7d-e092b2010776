"""
适配器模块
为SQLAlchemy和Pydantic之间的交互提供适配器
用于处理类型转换和编码问题
"""
import datetime
from typing import Any, Dict, List, Optional, Type, TypeVar, Union, cast
from sqlalchemy.sql.sqltypes import DateTime
import logging

logger = logging.getLogger(__name__)

T = TypeVar('T')

class DateTimeAdapter:
    """处理不同类型的日期时间之间的转换"""
    
    @staticmethod
    def to_python(value: Any) -> Optional[datetime.datetime]:
        """将SQLAlchemy日期时间转换为Python日期时间"""
        if value is None:
            return None
            
        try:
            if isinstance(value, datetime.datetime):
                return value
            elif isinstance(value, str):
                return datetime.datetime.fromisoformat(value.replace('Z', '+00:00'))
            else:
                logger.warning(f"不支持的日期时间类型: {type(value)}")
                return None
        except Exception as e:
            logger.error(f"将日期时间转换为Python对象时出错: {e}")
            return None
    
    @staticmethod
    def to_string(value: Any) -> Optional[str]:
        """将日期时间转换为ISO格式字符串"""
        if value is None:
            return None
            
        try:
            if isinstance(value, datetime.datetime):
                return value.isoformat()
            elif isinstance(value, str):
                return value
            else:
                logger.warning(f"不支持的日期时间类型: {type(value)}")
                return None
        except Exception as e:
            logger.error(f"将日期时间转换为字符串时出错: {e}")
            return None

class StringAdapter:
    """字符串适配器，用于处理不同编码的字符串"""
    
    @staticmethod
    def to_utf8(text: str) -> str:
        """将字符串转换为UTF-8编码"""
        try:
            if isinstance(text, str):
                return text
            elif isinstance(text, bytes):
                return text.decode('utf-8')
            else:
                return str(text)
        except Exception as e:
            logger.error(f"字符串转换失败: {str(e)}")
            raise ValueError("无法将字符串转换为UTF-8编码")
    
    @staticmethod
    def sanitize(value: Any) -> Optional[str]:
        """清理字符串中的不可见字符"""
        if value is None:
            return None
            
        try:
            if isinstance(value, str):
                # 移除控制字符，保留基本可见字符
                return ''.join(c for c in value if ord(c) >= 32 or c in '\n\r\t')
            else:
                return str(value)
        except Exception as e:
            logger.error(f"清理字符串时出错: {e}")
            return str(value)

class ModelAdapter:
    """在ORM模型和Pydantic模型之间进行转换"""
    
    @staticmethod
    def sqlalchemy_to_pydantic(orm_instance: Any, pydantic_model: Type[T]) -> T:
        """从SQLAlchemy ORM实例创建Pydantic模型"""
        try:
            # 获取ORM模型的所有属性
            orm_data = {}
            for column in orm_instance.__table__.columns:
                column_name = column.name
                value = getattr(orm_instance, column_name)
                
                # 处理不同类型
                if isinstance(column.type, DateTime) and value is not None:
                    value = DateTimeAdapter.to_python(value)
                elif isinstance(value, str):
                    value = StringAdapter.to_utf8(value)
                
                orm_data[column_name] = value
            
            # 创建Pydantic模型
            return pydantic_model(**orm_data)
        except Exception as e:
            logger.error(f"从SQLAlchemy转换到Pydantic模型时出错: {e}")
            # 尝试使用基本转换
            return pydantic_model.model_validate(orm_instance)
    
    @staticmethod
    def dict_to_pydantic(data: Dict[str, Any], pydantic_model: Type[T]) -> T:
        """从字典创建Pydantic模型，处理编码问题"""
        try:
            # 处理字典中的所有字符串值
            processed_data = {}
            for key, value in data.items():
                if isinstance(value, str):
                    processed_data[key] = StringAdapter.to_utf8(value)
                elif isinstance(value, datetime.datetime):
                    processed_data[key] = DateTimeAdapter.to_python(value)
                elif isinstance(value, dict):
                    # 递归处理嵌套字典
                    processed_data[key] = ModelAdapter.process_dict(value)
                elif isinstance(value, list):
                    # 处理列表
                    processed_data[key] = [
                        ModelAdapter.process_dict(item) if isinstance(item, dict)
                        else StringAdapter.to_utf8(item) if isinstance(item, str)
                        else item
                        for item in value
                    ]
                else:
                    processed_data[key] = value
            
            # 创建Pydantic模型
            return pydantic_model(**processed_data)
        except Exception as e:
            logger.error(f"从字典转换到Pydantic模型时出错: {e}")
            # 尝试使用基本转换
            return pydantic_model.model_validate(data)
    
    @staticmethod
    def process_dict(data: Dict[str, Any]) -> Dict[str, Any]:
        """处理字典中的所有字符串值"""
        result = {}
        for key, value in data.items():
            if isinstance(value, str):
                result[key] = StringAdapter.to_utf8(value)
            elif isinstance(value, datetime.datetime):
                result[key] = DateTimeAdapter.to_python(value)
            elif isinstance(value, dict):
                # 递归处理嵌套字典
                result[key] = ModelAdapter.process_dict(value)
            elif isinstance(value, list):
                # 处理列表
                result[key] = [
                    ModelAdapter.process_dict(item) if isinstance(item, dict)
                    else StringAdapter.to_utf8(item) if isinstance(item, str)
                    else item
                    for item in value
                ]
            else:
                result[key] = value
        return result 