"""
聊天消息模型

用于存储聊天会话中的单条消息
"""

from sqlalchemy import Column, String, Integer, DateTime, ForeignKey, Text, JSON, Float, Boolean
from sqlalchemy.orm import relationship
from datetime import datetime

from utils.db import Base

class ChatMessage(Base):
    """聊天消息模型"""
    
    __tablename__ = "chat_messages"
    __table_args__ = {'extend_existing': True}  # 允许表被多次定义，解决SQLAlchemy注册冲突
    
    id = Column(Integer, primary_key=True, index=True)
    session_id = Column(String(64), ForeignKey("chat_sessions.session_id", ondelete="CASCADE"), nullable=False)
    content = Column(Text, nullable=False)  # 消息内容
    message_type = Column(String(20), default="text")  # 消息类型：text, audio, image
    sender = Column(String(20), nullable=False)  # 发送者：user, digital_human
    timestamp = Column(DateTime, default=datetime.now)  # 消息时间戳
    emotion = Column(String(20), nullable=True)  # 情感标签
    emotion_score = Column(Float, nullable=True)  # 情感得分
    audio_url = Column(String(255), nullable=True)  # 音频URL
    media_url = Column(String(255), nullable=True)  # 媒体URL
    visemes = Column(JSON, nullable=True)  # 口型数据
    processing_time = Column(Float, nullable=True)  # 处理时间（毫秒）
    context_id = Column(String(64), nullable=True)  # 上下文ID
    is_read = Column(Boolean, default=False)  # 是否已读
    feedback = Column(Integer, nullable=True)  # 用户反馈（点赞/踩）
    message_metadata = Column(JSON, nullable=True)  # 额外元数据，改名避免与SQLAlchemy保留字冲突
    
    # 关系
    session = relationship("ChatSession", back_populates="messages")
    
    def __init__(self, **kwargs):
        """初始化聊天消息"""
        super().__init__(**kwargs)
        
        # 初始化元数据为空字典
        if self.message_metadata is None:
            self.message_metadata = {}
            
        # 初始化口型数据为空列表
        if self.visemes is None:
            self.visemes = []
            
    def mark_as_read(self):
        """标记为已读"""
        self.is_read = True
        
    def add_feedback(self, value):
        """添加反馈"""
        if value in [-1, 0, 1]:
            self.feedback = value
            
    def add_metadata(self, key, value):
        """添加元数据"""
        if self.message_metadata is None:
            self.message_metadata = {}
        self.message_metadata[key] = value
        
    def get_metadata(self, key, default=None):
        """获取元数据"""
        if not self.message_metadata:
            return default
        return self.message_metadata.get(key, default)
        
    def add_emotion_data(self, emotion, score=None):
        """添加情感数据"""
        self.emotion = emotion
        if score is not None:
            self.emotion_score = score
            
    def set_processing_time(self, time_ms):
        """设置处理时间"""
        self.processing_time = time_ms 