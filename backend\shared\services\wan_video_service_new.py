import os
import torch
import logging
import tempfile
import uuid
import time
import numpy as np
import asyncio
import sys
import platform
import shutil
import subprocess
from typing import Optional, Dict, Any, List, Tuple
from PIL import Image
import cv2
from dotenv import load_dotenv
from services.progress_tracker import get_progress_tracker
from services.progress_updater import ProgressUpdater
import traceback
from config.models import WAN_CONFIG  # 导入WAN配置
import json
from skimage.transform import resize

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 获取标准的应用数据目录
def get_app_data_dir():
    """获取跨平台的应用数据目录"""
    app_name = "WanAI"
    if platform.system() == "Windows":
        # Windows: AppData/Local
        return os.path.join(os.environ.get("LOCALAPPDATA", os.path.expanduser("~/AppData/Local")), app_name)
    elif platform.system() == "Darwin":
        # macOS: ~/Library/Application Support
        return os.path.join(os.path.expanduser("~/Library/Application Support"), app_name)
    else:
        # Linux/Unix: ~/.local/share
        return os.path.join(os.path.expanduser("~/.local/share"), app_name)

def check_directory_permissions(dir_path: str) -> Tuple[bool, bool, bool, str]:
    """
    检查目录的权限状态
    
    Args:
        dir_path: 要检查的目录路径
        
    Returns:
        Tuple:
            - 目录是否存在
            - 是否有读权限
            - 是否有写权限
            - 诊断信息
    """
    exists = os.path.exists(dir_path)
    readable = False
    writable = False
    msg = ""
    
    if not exists:
        msg = f"目录不存在: {dir_path}"
        return exists, readable, writable, msg
    
    if not os.path.isdir(dir_path):
        msg = f"路径不是目录: {dir_path}"
        return exists, readable, writable, msg
    
    readable = os.access(dir_path, os.R_OK)
    writable = os.access(dir_path, os.W_OK)
    
    if readable and writable:
        msg = f"目录存在且拥有读写权限: {dir_path}"
    elif readable and not writable:
        msg = f"目录存在但只读，无写入权限: {dir_path}"
    elif not readable and writable:
        msg = f"目录存在但只写，无读取权限: {dir_path}"
    else:
        msg = f"目录存在但无读写权限: {dir_path}"
    
    return exists, readable, writable, msg

# 自动修复模型目录结构
def auto_fix_model_structure(model_dir: str) -> bool:
    """
    自动修复模型目录结构
    
    Args:
        model_dir: 模型目录路径
        
    Returns:
        bool: 是否成功修复
    """
    logger.info(f"尝试自动修复模型目录结构: {model_dir}")
    
    try:
        # 尝试导入修复脚本
        try:
            from scripts import fix_wan_model_structure
            
            # 执行修复
            if fix_wan_model_structure.fix_model_structure(model_dir):
                logger.info(f"模型目录结构自动修复成功: {model_dir}")
                return True
            else:
                logger.warning(f"模型目录结构自动修复失败: {model_dir}")
                return False
                
        except ImportError:
            logger.warning(f"未找到修复脚本，无法自动修复模型目录结构")
            # 尝试使用一键修复工具
            script_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 
                                     "scripts", "fix_wan_issue.py")
            
            if os.path.exists(script_path):
                logger.info(f"找到一键修复工具，尝试执行: {script_path}")
                
                # 执行修复脚本
                import subprocess
                result = subprocess.run([sys.executable, script_path], 
                                        capture_output=True, text=True)
                
                if result.returncode == 0:
                    logger.info(f"一键修复工具执行成功")
                    logger.info(result.stdout)
                    return True
                else:
                    logger.error(f"一键修复工具执行失败: {result.stderr}")
                    return False
            else:
                logger.warning(f"未找到一键修复工具，无法自动修复模型目录结构")
                return False
    
    except Exception as e:
        logger.error(f"尝试自动修复模型目录结构时出错: {e}")
        logger.error(traceback.format_exc())
        return False

# 查找最佳的模型缓存目录
def find_best_model_cache_dir() -> str:
    """
    查找最佳的模型缓存目录，以简化逻辑方式处理：
    1. 首先尝试使用WAN_CONFIG中定义的缓存目录
    2. 然后检查环境变量WAN_MODEL_CACHE_DIR指定的目录
    3. 检查当前目录下的models_cache目录
    4. 检查脚本所在目录下的models_cache目录
    5. 使用标准应用数据目录
    
    简化查找逻辑并提供详细的诊断信息。
    
    Returns:
        str: 最佳的模型缓存目录路径
    """
    logger.info("正在查找最佳模型缓存目录...")
    
    # 强制使用真实模式
    mock_mode = False
    
    logger.info(f"系统平台: {platform.system()} {platform.release()}")
    logger.info(f"Python版本: {sys.version}")
    logger.info(f"当前工作目录: {os.getcwd()}")
    
    # 记录搜索目录的状态，以便全面了解系统环境
    all_searched_paths = []
    
    # 0. 首先尝试使用配置中定义的路径(优先级最高)
    if hasattr(WAN_CONFIG, 'model_cache_dir') and WAN_CONFIG['model_cache_dir']:
        config_cache_dir = WAN_CONFIG['model_cache_dir']
        logger.info(f"检查配置中设置的缓存目录: {config_cache_dir}")
        
        exists, readable, writable, msg = check_directory_permissions(config_cache_dir)
        logger.info(f"检查配置设置的缓存目录 - {msg}")
        all_searched_paths.append((config_cache_dir, exists, readable, writable, "CONFIG中的model_cache_dir"))
        
        if exists and readable and writable:
            logger.info(f"✓ 使用配置指定的模型缓存目录: {config_cache_dir}")
            return config_cache_dir
        else:
            logger.warning(f"⚠ 配置指定的目录有问题: {msg}")
    
    # 1. 检查环境变量并记录情况
    env_var_value = os.environ.get("WAN_MODEL_CACHE_DIR", "")
    if env_var_value:
        logger.info(f"发现环境变量 WAN_MODEL_CACHE_DIR = {env_var_value}")
    else:
        logger.info("未设置环境变量 WAN_MODEL_CACHE_DIR")
    
    # 使用环境变量指定的目录
    if env_var_value:
        cache_dir = env_var_value
        exists, readable, writable, msg = check_directory_permissions(cache_dir)
        logger.info(f"检查环境变量设置的缓存目录 - {msg}")
        all_searched_paths.append((cache_dir, exists, readable, writable, "环境变量WAN_MODEL_CACHE_DIR"))
        
        if exists and readable and writable:
            logger.info(f"✓ 使用环境变量指定的模型缓存目录: {cache_dir}")
            return cache_dir
        else:
            logger.warning(f"⚠ 环境变量指定的目录有问题: {msg}")
    
    # 2. 检查当前目录下的models_cache目录
    current_dir_cache = os.path.join(os.getcwd(), "models_cache")
    exists, readable, writable, msg = check_directory_permissions(current_dir_cache)
    logger.info(f"检查当前目录下的models_cache - {msg}")
    all_searched_paths.append((current_dir_cache, exists, readable, writable, "当前目录下的models_cache"))
    
    if exists and readable and writable:
        logger.info(f"✓ 使用当前目录下的models_cache目录: {current_dir_cache}")
        return current_dir_cache
    
    # 3. 检查脚本所在目录下的models_cache目录
    script_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    script_dir_cache = os.path.join(script_dir, "models_cache")
    exists, readable, writable, msg = check_directory_permissions(script_dir_cache)
    logger.info(f"检查脚本目录下的models_cache - {msg}")
    all_searched_paths.append((script_dir_cache, exists, readable, writable, "脚本目录下的models_cache"))
    
    if exists and readable and writable:
        logger.info(f"✓ 使用脚本目录下的models_cache目录: {script_dir_cache}")
        return script_dir_cache
    
    # 4. 使用标准应用数据目录
    app_data_dir = os.path.join(get_app_data_dir(), "models_cache")
    exists, readable, writable, msg = check_directory_permissions(app_data_dir)
    logger.info(f"检查标准应用数据目录 - {msg}")
    all_searched_paths.append((app_data_dir, exists, readable, writable, "标准应用数据目录"))
    
    # 如果目录不存在，尝试创建
    if not exists:
        try:
            os.makedirs(app_data_dir, exist_ok=True)
            logger.info(f"已创建标准应用数据目录: {app_data_dir}")
            exists = True
            readable = os.access(app_data_dir, os.R_OK)
            writable = os.access(app_data_dir, os.W_OK)
        except Exception as e:
            logger.error(f"创建标准应用数据目录失败: {e}")
    
    if exists and readable and writable:
        logger.info(f"✓ 使用标准应用数据目录: {app_data_dir}")
        return app_data_dir
    
    # 5. 如果所有尝试都失败，使用临时目录
    temp_dir = os.path.join(tempfile.gettempdir(), "wan_models_cache")
    try:
        os.makedirs(temp_dir, exist_ok=True)
        logger.warning(f"⚠ 使用临时目录作为最后的选择: {temp_dir}")
        return temp_dir
    except Exception as e:
        logger.error(f"创建临时目录失败: {e}")
        logger.error(f"无法找到可用的模型缓存目录，返回当前目录")
        return os.getcwd()

# 设置全局模型缓存目录
WAN_MODEL_CACHE_DIR = find_best_model_cache_dir()

# 设置模型路径
WAN_T2V_14B_MODEL = os.path.join(WAN_MODEL_CACHE_DIR, os.environ.get("WAN_T2V_14B_MODEL", "Wan2.1-T2V-14B"))
WAN_T2V_1_3B_MODEL = os.path.join(WAN_MODEL_CACHE_DIR, os.environ.get("WAN_T2V_1_3B_MODEL", "Wan2.1-T2V-1.3B"))
WAN_I2V_720P_MODEL = os.path.join(WAN_MODEL_CACHE_DIR, os.environ.get("WAN_I2V_720P_MODEL", "Wan2.1-I2V-720p"))
WAN_I2V_480P_MODEL = os.path.join(WAN_MODEL_CACHE_DIR, os.environ.get("WAN_I2V_480P_MODEL", "Wan2.1-I2V-480p"))

# 日志记录目录变更
logger.info(f"使用模型缓存目录: {WAN_MODEL_CACHE_DIR}")
if os.getenv("WAN_MODEL_CACHE_DIR") is None:
    logger.info(f"提示: 使用默认缓存目录。如需更改，请设置环境变量 WAN_MODEL_CACHE_DIR")

# 检查需要的模型目录结构
for model_dir in [WAN_T2V_14B_MODEL, WAN_T2V_1_3B_MODEL, WAN_I2V_720P_MODEL, WAN_I2V_480P_MODEL]:
    exists, readable, writable, msg = check_directory_permissions(model_dir)
    logger.info(f"模型目录检查: {msg}")

class WanVideoService:
    """WAN 2.1 视频生成服务"""
    
    def __init__(self, model_cache_dir: Optional[str] = None, device: Optional[str] = None, 
                 precision: Optional[str] = None, mock_mode: Optional[bool] = None):
        """
        初始化WAN 2.1视频生成服务
        
        Args:
            model_cache_dir: 可选，直接指定模型缓存目录，覆盖默认查找逻辑
            device: 可选，运行设备 (cpu/cuda)
            precision: 可选，模型精度 (float32/float16)
            mock_mode: 可选，是否使用模拟模式生成视频，而非实际AI模型
        """
        try:
            # 获取环境变量，或使用默认值
            self.max_retry_attempts = int(os.environ.get("WAN_MAX_RETRY_ATTEMPTS", "3"))
            
            # 设置模拟模式 - 允许通过参数或环境变量控制
            if mock_mode is None:
                # 如果未指定，则使用环境变量
                env_mock_mode = os.environ.get("WAN_MOCK_MODE", "false").lower()
                self.mock_mode = env_mock_mode == "true"
            else:
                # 如果明确指定，则使用指定值
                self.mock_mode = mock_mode
                
            # 强制使用真实模式 - 如果设置了此项，则忽略模拟模式设置
            force_real_mode = os.environ.get("WAN_FORCE_REAL_MODE", "false").lower() == "true"
            if force_real_mode:
                logger.info("已启用强制真实模式，将忽略模拟模式设置")
                self.mock_mode = False
                
            logger.info(f"模拟模式设置: {self.mock_mode}")
            
            # 设置设备
            if device is None:
                self.device = os.environ.get("WAN_DEVICE", "cuda")
            else:
                self.device = device
                
            logger.info(f"设备设置: {self.device}")
            
            # 设置模型精度
            if precision is None:
                self.precision = os.environ.get("WAN_MODEL_PRECISION", "bfloat16")
            else:
                self.precision = precision
                
            logger.info(f"精度设置: {self.precision}")
            
            # 设置模型缓存目录
            if model_cache_dir is None:
                self.model_cache_dir = find_best_model_cache_dir()
            else:
                self.model_cache_dir = model_cache_dir
                
            logger.info(f"模型缓存目录: {self.model_cache_dir}")
            
            # 初始化模型状态跟踪
            self.is_initialized = True
            self.models = {'t2v': {}, 'i2v': {}}
            self._init_model_status()
                
        except Exception as e:
            logger.error(f"初始化WAN视频服务时出错: {e}")
            logger.error(traceback.format_exc())
            
            # 在初始化失败时，如果不是强制真实模式，则使用模拟模式
            force_real_mode = os.environ.get("WAN_FORCE_REAL_MODE", "false").lower() == "true"
            if force_real_mode:
                logger.error("强制真实模式下初始化失败，服务将无法正常工作")
                self.mock_mode = False
            else:
                logger.warning("由于初始化错误，已启用模拟模式")
                self.mock_mode = True
                os.environ["WAN_MOCK_MODE"] = "true"
            
            # 设置最小必要的属性
            self.model_cache_dir = tempfile.gettempdir()
            self.device = "cpu"
            self.precision = "float32"
            self.max_retry_attempts = 3
            self.is_initialized = False
            self.models = {'t2v': {}, 'i2v': {}}
    
    def _ensure_video_dimensions_compatible(self, width: int, height: int) -> Tuple[int, int]:
        """
        确保视频尺寸与标准宏块大小兼容，以避免编码器警告或错误
        
        视频编码器通常要求尺寸是特定值的倍数（如2、4、8或16），
        这个方法确保宽度和高度是这些值的倍数，避免FFmpeg警告。
        
        Args:
            width: 原始宽度
            height: 原始高度
            
        Returns:
            Tuple[int, int]: 调整后的宽度和高度
        """
        # 大多数编码器要求尺寸是16的倍数，但保守起见我们使用2
        # 对于较高级的编码器，可能需要是4或8的倍数
        macroblock_size = 16
        
        # 计算调整后的尺寸，确保是宏块大小的倍数
        adjusted_width = (width + macroblock_size - 1) // macroblock_size * macroblock_size
        adjusted_height = (height + macroblock_size - 1) // macroblock_size * macroblock_size
        
        # 记录尺寸调整
        if width != adjusted_width or height != adjusted_height:
            logger.info(f"视频尺寸调整: {width}x{height} -> {adjusted_width}x{adjusted_height} (宏块大小: {macroblock_size})")
        
        return adjusted_width, adjusted_height 

    def _init_model_status(self):
        """初始化模型状态跟踪"""
        # T2V模型状态初始化
        t2v_model_14b = os.environ.get("WAN_T2V_14B_MODEL", "Wan2.1-T2V-14B")
        t2v_model_1_3b = os.environ.get("WAN_T2V_1_3B_MODEL", "Wan2.1-T2V-1.3B")
        
        # 解析支持的分辨率
        resolutions = ["720p", "1080p"] # 默认支持的分辨率
        
        self.model_status = {
            "t2v": {
                "14B": {res: {"loaded": False, "loading": False, "error": None} for res in resolutions},
                "1.3B": {res: {"loaded": False, "loading": False, "error": None} for res in resolutions}
            },
            "i2v": {res: {"loaded": False, "loading": False, "error": None} for res in resolutions}
        }
        
        # 日志记录模型状态
        logger.info(f"初始化模型状态跟踪:")
        logger.info(f"  文本到视频 (14B): {', '.join(resolutions)}")
        logger.info(f"  文本到视频 (1.3B): {', '.join(resolutions)}")
        logger.info(f"  图像到视频: {', '.join(resolutions)}")
        
        # 检查模型目录是否存在
        t2v_14b_path = os.path.join(self.model_cache_dir, t2v_model_14b)
        t2v_1_3b_path = os.path.join(self.model_cache_dir, t2v_model_1_3b)
        
        # 记录模型路径信息
        logger.info(f"模型路径:")
        logger.info(f"  T2V 14B: {t2v_14b_path}")
        logger.info(f"  T2V 1.3B: {t2v_1_3b_path}")
        
        # 检查模型文件是否存在
        t2v_14b_exists = os.path.exists(t2v_14b_path) and os.path.isdir(t2v_14b_path)
        t2v_1_3b_exists = os.path.exists(t2v_1_3b_path) and os.path.isdir(t2v_1_3b_path)
        
        # 日志记录模型文件状态
        logger.info(f"模型目录状态:")
        logger.info(f"  T2V 14B: {'存在' if t2v_14b_exists else '不存在'}")
        logger.info(f"  T2V 1.3B: {'存在' if t2v_1_3b_exists else '不存在'}")
    
    def _get_torch_dtype(self, precision: str):
        """获取torch数据类型"""
        if precision == "float16":
            return torch.float16
        elif precision == "bfloat16" and hasattr(torch, "bfloat16"):
            return torch.bfloat16
        else:
            return torch.float32 

    async def check_model_status(self, model_type: str, model_size: str = "14B", resolution: str = "720p") -> Dict[str, Any]:
        """
        检查模型状态，检查本地模型文件是否存在和完整。
        
        Args:
            model_type: 模型类型，'t2v'或'i2v'
            model_size: 模型大小，仅用于t2v
            resolution: 分辨率，用于i2v或t2v的分辨率参数
            
        Returns:
            Dict: 模型状态信息
        """
        logger.info(f"检查模型状态: {model_type}, 规格: {model_size}, 分辨率: {resolution}")
        
        # 更新检查时间
        if model_type == "t2v" and model_size in self.model_status["t2v"]:
            if resolution in self.model_status["t2v"][model_size]:
                self.model_status["t2v"][model_size][resolution]["last_check"] = time.time()
        elif model_type == "i2v" and resolution in self.model_status["i2v"]:
            self.model_status["i2v"][resolution]["last_check"] = time.time()
        
        # 检查模型是否已加载到内存中
        if model_type == "t2v":
            if model_type in self.models and model_size in self.models[model_type] and resolution in self.models[model_type][model_size]:
                logger.info(f"模型已加载到内存中: {model_type} {model_size} {resolution}")
                if model_size in self.model_status["t2v"] and resolution in self.model_status["t2v"][model_size]:
                    self.model_status["t2v"][model_size][resolution]["loaded"] = True
                return {
                    "type": model_type,
                    "size": model_size,
                    "resolution": resolution,
                    "loaded": True,
                    "loading": False,
                    "available": True,
                    "error": None,
                    "mock_mode": self.mock_mode
                }
        elif model_type == "i2v":
            if model_type in self.models and resolution in self.models[model_type]:
                logger.info(f"模型已加载到内存中: {model_type} {resolution}")
                if resolution in self.model_status["i2v"]:
                    self.model_status["i2v"][resolution]["loaded"] = True
                return {
                    "type": model_type,
                    "resolution": resolution,
                    "loaded": True,
                    "loading": False,
                    "available": True,
                    "error": None,
                    "mock_mode": self.mock_mode
                }
        
        # 如果使用模拟模式，则不需要检查模型文件
        if self.mock_mode:
            logger.info(f"模拟模式启用，跳过模型文件检查")
            if model_type == "t2v" and model_size in self.model_status["t2v"]:
                if resolution in self.model_status["t2v"][model_size]:
                    self.model_status["t2v"][model_size][resolution]["available"] = True
                    self.model_status["t2v"][model_size][resolution]["error"] = None
            elif model_type == "i2v" and resolution in self.model_status["i2v"]:
                self.model_status["i2v"][resolution]["available"] = True
                self.model_status["i2v"][resolution]["error"] = None
            
            return {
                "type": model_type,
                "size": model_size if model_type == "t2v" else None,
                "resolution": resolution,
                "loaded": False,
                "loading": False,
                "available": True,
                "error": None,
                "mock_mode": True
            }
        
        # 对于非模拟模式，检查模型文件
        try:
            # 检查完整性
            integrity_result = await self.check_model_integrity(model_type, model_size, resolution)
            
            # 更新模型状态
            if model_type == "t2v" and model_size in self.model_status["t2v"]:
                if resolution in self.model_status["t2v"][model_size]:
                    self.model_status["t2v"][model_size][resolution]["available"] = integrity_result["available"]
                    self.model_status["t2v"][model_size][resolution]["error"] = integrity_result["error"]
            elif model_type == "i2v" and resolution in self.model_status["i2v"]:
                self.model_status["i2v"][resolution]["available"] = integrity_result["available"]
                self.model_status["i2v"][resolution]["error"] = integrity_result["error"]
            
            return {
                "type": model_type,
                "size": model_size if model_type == "t2v" else None,
                "resolution": resolution,
                "loaded": False,
                "loading": False,
                "available": integrity_result["available"],
                "error": integrity_result["error"],
                "mock_mode": False,
                "model_path": integrity_result.get("model_path", ""),
                "files_status": integrity_result.get("files_status", {})
            }
            
        except Exception as e:
            logger.error(f"检查模型状态时出错: {str(e)}")
            logger.error(traceback.format_exc())
            
            error_msg = f"检查模型状态出错: {str(e)}"
            
            if model_type == "t2v" and model_size in self.model_status["t2v"]:
                if resolution in self.model_status["t2v"][model_size]:
                    self.model_status["t2v"][model_size][resolution]["available"] = False
                    self.model_status["t2v"][model_size][resolution]["error"] = error_msg
            elif model_type == "i2v" and resolution in self.model_status["i2v"]:
                self.model_status["i2v"][resolution]["available"] = False
                self.model_status["i2v"][resolution]["error"] = error_msg
            
            return {
                "type": model_type,
                "size": model_size if model_type == "t2v" else None,
                "resolution": resolution,
                "loaded": False,
                "loading": False,
                "available": False,
                "error": error_msg,
                "mock_mode": False
            }
    
    async def check_model_integrity(self, model_type: str, model_size: str = "14B", resolution: str = "720p") -> Dict[str, Any]:
        """
        检查模型文件完整性
        
        Args:
            model_type: 模型类型，'t2v'或'i2v'
            model_size: 模型大小，仅用于t2v
            resolution: 分辨率，用于i2v或t2v的分辨率参数
            
        Returns:
            Dict: 模型完整性信息
        """
        logger.info(f"检查模型完整性: {model_type}, 规格: {model_size}, 分辨率: {resolution}")
        
        # 获取模型ID
        model_id = self._get_model_id(model_type, model_size, resolution)
        model_path = os.path.join(self.model_cache_dir, model_id)
        
        # 检查模型目录是否存在
        if not os.path.exists(model_path):
            logger.warning(f"模型目录不存在: {model_path}")
            return {
                "available": False,
                "error": f"模型目录不存在: {model_path}",
                "model_path": model_path,
                "files_status": {"directory_exists": False}
            }
        
        if not os.path.isdir(model_path):
            logger.warning(f"路径不是目录: {model_path}")
            return {
                "available": False,
                "error": f"路径不是目录: {model_path}",
                "model_path": model_path,
                "files_status": {"is_directory": False}
            }
        
        # 检查权限
        if not os.access(model_path, os.R_OK):
            logger.warning(f"模型目录无读取权限: {model_path}")
            return {
                "available": False,
                "error": f"模型目录无读取权限: {model_path}",
                "model_path": model_path,
                "files_status": {"readable": False}
            }
        
        # 基本文件检查
        expected_files = []
        
        # 通用检查的文件
        expected_files.extend(["model_index.json", "config.json"])
        
        # 检查文件
        files_status = {}
        missing_files = []
        
        for file_name in expected_files:
            file_path = os.path.join(model_path, file_name)
            file_exists = os.path.isfile(file_path)
            files_status[file_name] = file_exists
            
            if not file_exists:
                missing_files.append(file_name)
        
        # 总结结果
        available = len(missing_files) == 0
        
        if not available:
            error_msg = f"模型文件不完整，缺少以下文件: {', '.join(missing_files)}"
            logger.warning(error_msg)
            return {
                "available": False,
                "error": error_msg,
                "model_path": model_path,
                "files_status": files_status,
                "missing_files": missing_files
            }
        
        # 所有检查都通过
        logger.info(f"模型文件完整性检查通过: {model_path}")
        return {
            "available": True,
            "error": None,
            "model_path": model_path,
            "files_status": files_status
        } 

    def _get_model_id(self, model_type: str, model_size: str = "14B", resolution: str = "720p") -> str:
        """
        获取模型ID，用于构建模型路径
        
        Args:
            model_type: 模型类型，'t2v'或'i2v'
            model_size: 模型大小，仅用于t2v
            resolution: 分辨率，用于i2v或t2v的分辨率参数
            
        Returns:
            str: 模型ID
        """
        if model_type == "t2v":
            # T2V模型ID格式：Wan2.1-T2V-{size}
            model_id = os.environ.get(f"WAN_T2V_{model_size}_MODEL", f"Wan2.1-T2V-{model_size}")
        elif model_type == "i2v":
            # I2V模型ID格式：Wan2.1-I2V-{resolution}
            model_id = os.environ.get(f"WAN_I2V_{resolution.upper()}_MODEL", f"Wan2.1-I2V-{resolution}")
        else:
            raise ValueError(f"不支持的模型类型: {model_type}")
        
        return model_id
    
    async def initialize_t2v(self, task_id: str, progress_updater: ProgressUpdater, model_size: str = "14B", resolution: str = "720p"):
        """
        初始化文本到视频模型
        
        Args:
            task_id: 任务ID
            progress_updater: 进度更新器
            model_size: 模型大小
            resolution: 分辨率
        
        Returns:
            bool: 是否成功初始化
        """
        logger.info(f"初始化文本到视频模型: 规格={model_size}, 分辨率={resolution}")
        
        # 检查是否已经加载
        if "t2v" in self.models and model_size in self.models["t2v"] and resolution in self.models["t2v"][model_size]:
            logger.info(f"文本到视频模型已加载: {model_size} {resolution}")
            return True
        
        # 更新模型状态
        if model_size in self.model_status["t2v"] and resolution in self.model_status["t2v"][model_size]:
            self.model_status["t2v"][model_size][resolution]["loading"] = True
        
        # 更新进度
        if progress_updater:
            await progress_updater.update(
                progress=10,
                logs=[f"初始化文本到视频模型 ({model_size}, {resolution})..."]
            )
        
        try:
            # 使用模拟模式
            if self.mock_mode:
                logger.info(f"使用模拟模式，创建模拟文本到视频模型: {model_size} {resolution}")
                
                # 更新进度
                if progress_updater:
                    await progress_updater.update(
                        progress=20,
                        logs=[f"使用模拟模式 - 创建模拟文本到视频模型..."]
                    )
                
                # 创建模拟模型
                mock_success = await self._create_mock_model(model_size, resolution, progress_updater)
                
                if mock_success:
                    logger.info(f"模拟文本到视频模型创建成功: {model_size} {resolution}")
                    
                    # 更新模型状态
                    if model_size in self.model_status["t2v"] and resolution in self.model_status["t2v"][model_size]:
                        self.model_status["t2v"][model_size][resolution]["loaded"] = True
                        self.model_status["t2v"][model_size][resolution]["loading"] = False
                        self.model_status["t2v"][model_size][resolution]["error"] = None
                    
                    return True
                else:
                    logger.error(f"模拟文本到视频模型创建失败: {model_size} {resolution}")
                    
                    # 更新模型状态
                    if model_size in self.model_status["t2v"] and resolution in self.model_status["t2v"][model_size]:
                        self.model_status["t2v"][model_size][resolution]["loaded"] = False
                        self.model_status["t2v"][model_size][resolution]["loading"] = False
                        self.model_status["t2v"][model_size][resolution]["error"] = "模拟模型创建失败"
                    
                    return False
            
            # 使用真实模型
            # 获取模型ID
            model_id = self._get_model_id("t2v", model_size, resolution)
            model_path = os.path.join(self.model_cache_dir, model_id)
            
            # 检查模型目录是否存在
            if not os.path.exists(model_path) or not os.path.isdir(model_path):
                error_msg = f"模型目录不存在: {model_path}"
                logger.error(error_msg)
                
                # 更新模型状态
                if model_size in self.model_status["t2v"] and resolution in self.model_status["t2v"][model_size]:
                    self.model_status["t2v"][model_size][resolution]["loaded"] = False
                    self.model_status["t2v"][model_size][resolution]["loading"] = False
                    self.model_status["t2v"][model_size][resolution]["error"] = error_msg
                
                # 尝试自动修复模型结构
                if auto_fix_model_structure(model_path):
                    logger.info(f"模型目录结构已自动修复: {model_path}")
                else:
                    # 更新进度
                    if progress_updater:
                        await progress_updater.update(
                            progress=15,
                            logs=[f"模型目录不存在: {model_path}",
                                 f"请使用模型下载工具下载模型，或设置正确的模型路径"]
                        )
                    
                    # 提供手动下载指南
                    manual_guide = self._get_manual_download_guide("t2v", model_size, resolution)
                    
                    # 更新进度
                    if progress_updater:
                        await progress_updater.update(
                            progress=16,
                            logs=[manual_guide]
                        )
                    
                    return False
            
            # 加载模型之前，先检查其他依赖项
            # 例如检查pytorch是否可用
            try:
                import torch
                logger.info(f"PyTorch版本: {torch.__version__}")
                
                # 检查CUDA是否可用
                if self.device.lower().startswith("cuda"):
                    cuda_available = torch.cuda.is_available()
                    logger.info(f"CUDA可用性: {cuda_available}")
                    
                    if not cuda_available:
                        logger.warning(f"CUDA不可用，回退到CPU模式")
                        self.device = "cpu"
                
                # 更新进度
                if progress_updater:
                    await progress_updater.update(
                        progress=20,
                        logs=[f"PyTorch环境检查完成，继续加载模型..."]
                    )
                
            except ImportError:
                error_msg = "未找到PyTorch，无法加载模型"
                logger.error(error_msg)
                
                # 更新模型状态
                if model_size in self.model_status["t2v"] and resolution in self.model_status["t2v"][model_size]:
                    self.model_status["t2v"][model_size][resolution]["loaded"] = False
                    self.model_status["t2v"][model_size][resolution]["loading"] = False
                    self.model_status["t2v"][model_size][resolution]["error"] = error_msg
                
                # 更新进度
                if progress_updater:
                    await progress_updater.update(
                        progress=18,
                        logs=[error_msg, "请安装PyTorch: pip install torch"]
                    )
                
                return False
            
            # 加载autoencoder模型
            # 更新进度
            if progress_updater:
                await progress_updater.update(
                    progress=30,
                    logs=[f"加载自编码器模型..."]
                )
            
            try:
                # 如果在生产代码中，这里应该包含实际的模型加载逻辑
                # 以下是占位代码，应该替换为实际的模型加载
                logger.info(f"正在加载自编码器模型: {model_path}")
                
                # 这里是模拟的加载代码，真实情况下应替换
                from diffusers import AutoencoderKL
                try:
                    vae = AutoencoderKL.from_pretrained(
                        model_path, 
                        subfolder="vae", 
                        torch_dtype=self._get_torch_dtype(self.precision)
                    )
                    vae = vae.to(self.device)
                    
                    # 更新进度
                    if progress_updater:
                        await progress_updater.update(
                            progress=40,
                            logs=[f"自编码器模型加载完成，加载调度器..."]
                        )
                    
                    # 加载调度器
                    from diffusers import DDIMScheduler
                    scheduler = DDIMScheduler.from_pretrained(
                        model_path, 
                        subfolder="scheduler"
                    )
                    
                    # 更新进度
                    if progress_updater:
                        await progress_updater.update(
                            progress=50,
                            logs=[f"调度器加载完成，加载扩散模型..."]
                        )
                    
                    # 加载完整的pipeline
                    # 根据实际情况调整
                    from diffusers import TextToVideoSDPipeline
                    pipe = TextToVideoSDPipeline.from_pretrained(
                        model_path,
                        scheduler=scheduler,
                        vae=vae,
                        torch_dtype=self._get_torch_dtype(self.precision)
                    )
                    pipe = pipe.to(self.device)
                    
                    # 更新进度
                    if progress_updater:
                        await progress_updater.update(
                            progress=70,
                            logs=[f"扩散模型加载完成，完成初始化..."]
                        )
                    
                    # 初始化成功，存储模型
                    if "t2v" not in self.models:
                        self.models["t2v"] = {}
                    if model_size not in self.models["t2v"]:
                        self.models["t2v"][model_size] = {}
                    
                    self.models["t2v"][model_size][resolution] = pipe
                    
                    # 更新模型状态
                    if model_size in self.model_status["t2v"] and resolution in self.model_status["t2v"][model_size]:
                        self.model_status["t2v"][model_size][resolution]["loaded"] = True
                        self.model_status["t2v"][model_size][resolution]["loading"] = False
                        self.model_status["t2v"][model_size][resolution]["error"] = None
                    
                    # 更新进度
                    if progress_updater:
                        await progress_updater.update(
                            progress=100,
                            logs=[f"文本到视频模型初始化完成"]
                        )
                    
                    logger.info(f"文本到视频模型初始化成功: {model_size} {resolution}")
                    return True
                    
                except Exception as e:
                    error_msg = f"加载模型时出错: {str(e)}"
                    logger.error(error_msg)
                    logger.error(traceback.format_exc())
                    
                    # 更新模型状态
                    if model_size in self.model_status["t2v"] and resolution in self.model_status["t2v"][model_size]:
                        self.model_status["t2v"][model_size][resolution]["loaded"] = False
                        self.model_status["t2v"][model_size][resolution]["loading"] = False
                        self.model_status["t2v"][model_size][resolution]["error"] = error_msg
                    
                    # 更新进度
                    if progress_updater:
                        await progress_updater.update(
                            progress=35,
                            logs=[error_msg, "模型加载失败，请检查模型文件是否完整"]
                        )
                    
                    return False
                
            except ImportError as e:
                error_msg = f"加载模型依赖库时出错: {str(e)}"
                logger.error(error_msg)
                
                # 更新模型状态
                if model_size in self.model_status["t2v"] and resolution in self.model_status["t2v"][model_size]:
                    self.model_status["t2v"][model_size][resolution]["loaded"] = False
                    self.model_status["t2v"][model_size][resolution]["loading"] = False
                    self.model_status["t2v"][model_size][resolution]["error"] = error_msg
                
                # 更新进度
                if progress_updater:
                    await progress_updater.update(
                        progress=35,
                        logs=[error_msg, "请安装所需依赖: pip install diffusers transformers"]
                    )
                
                return False
            
        except Exception as e:
            error_msg = f"初始化文本到视频模型时出错: {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            
            # 更新模型状态
            if model_size in self.model_status["t2v"] and resolution in self.model_status["t2v"][model_size]:
                self.model_status["t2v"][model_size][resolution]["loaded"] = False
                self.model_status["t2v"][model_size][resolution]["loading"] = False
                self.model_status["t2v"][model_size][resolution]["error"] = error_msg
            
            # 更新进度
            if progress_updater:
                await progress_updater.update(
                    progress=20,
                    logs=[error_msg]
                )
            
            return False 

    async def _create_mock_model(self, model_size: str, resolution: str, progress_updater: ProgressUpdater) -> bool:
        """
        创建模拟模型，用于模拟模式
        
        Args:
            model_size: 模型大小
            resolution: 分辨率
            progress_updater: 进度更新器
            
        Returns:
            bool: 是否成功创建
        """
        try:
            logger.info(f"创建模拟文本到视频模型: {model_size} {resolution}")
            
            # 更新进度
            if progress_updater:
                await progress_updater.update(
                    progress=30,
                    logs=[f"创建模拟文本到视频模型..."]
                )
            
            # 创建模拟的autoencoder
            class AutoencoderKLWan:
                @classmethod
                def from_pretrained(cls, model_path, **kwargs):
                    logger.info(f"[模拟] 加载自编码器模型: {model_path}")
                    return cls()
                
                def to(self, device):
                    logger.info(f"[模拟] 将自编码器模型加载到设备: {device}")
                    return self
            
            # 创建模拟的pipeline
            class WanPipeline:
                def __init__(self, **kwargs):
                    logger.info(f"[模拟] 初始化Pipeline")
                    self.model_size = model_size
                    self.resolution = resolution
                
                @classmethod
                def from_pretrained(cls, model_path, **kwargs):
                    logger.info(f"[模拟] 加载Pipeline: {model_path}")
                    return cls(**kwargs)
                
                def to(self, device):
                    logger.info(f"[模拟] 将Pipeline加载到设备: {device}")
                    return self
                
                def __call__(self, prompt, negative_prompt="", height=720, width=1280, 
                             num_frames=16, guidance_scale=7.5, generator=None):
                    logger.info(f"[模拟] 生成视频: {prompt}")
                    
                    # 生成模拟的随机视频帧
                    import torch
                    import numpy as np
                    
                    # 如果提供了生成器，使用它的种子
                    seed = None
                    if generator is not None:
                        if hasattr(generator, "seed"):
                            seed = generator.seed()
                        elif isinstance(generator, int):
                            seed = generator
                    
                    # 如果没有提供种子，生成一个随机种子
                    if seed is None:
                        seed = np.random.randint(0, 2**32 - 1)
                    
                    # 设置随机种子
                    np.random.seed(seed)
                    
                    # 模拟视频帧 - 使用彩色渐变和波
                    frames = []
                    for i in range(num_frames):
                        frame = np.zeros((height, width, 3), dtype=np.uint8)
                        for y in range(height):
                            for x in range(width):
                                # 创建时间和空间变化的渐变
                                r = int((np.sin(x * 0.01 + i * 0.1) * 0.5 + 0.5) * 255)
                                g = int((np.sin(y * 0.01 + i * 0.1) * 0.5 + 0.5) * 255)
                                b = int((np.sin((x+y) * 0.01 + i * 0.1) * 0.5 + 0.5) * 255)
                                frame[y, x] = [r, g, b]
                        frames.append(frame)
                    
                    # 将帧转换为torch张量
                    video = torch.from_numpy(np.stack(frames)).permute(0, 3, 1, 2).float() / 255.0
                    
                    # 在模拟模式下返回一个字典
                    return {"videos": video, "seed": seed}
            
            # 创建模拟的调度器
            class MockScheduler:
                def __init__(self, **kwargs):
                    logger.info(f"[模拟] 初始化调度器")
                
                @classmethod
                def from_pretrained(cls, model_path, **kwargs):
                    logger.info(f"[模拟] 加载调度器: {model_path}")
                    return cls(**kwargs)
            
            # 更新进度
            if progress_updater:
                await progress_updater.update(
                    progress=70,
                    logs=[f"模拟模型创建中..."]
                )
            
            # 创建并存储模拟模型
            if "t2v" not in self.models:
                self.models["t2v"] = {}
            if model_size not in self.models["t2v"]:
                self.models["t2v"][model_size] = {}
            
            # 实例化模拟pipeline
            mock_pipeline = WanPipeline()
            self.models["t2v"][model_size][resolution] = mock_pipeline
            
            # 更新进度
            if progress_updater:
                await progress_updater.update(
                    progress=100,
                    logs=[f"模拟文本到视频模型创建完成"]
                )
            
            logger.info(f"模拟文本到视频模型创建成功: {model_size} {resolution}")
            return True
            
        except Exception as e:
            error_msg = f"创建模拟模型时出错: {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            
            # 更新进度
            if progress_updater:
                await progress_updater.update(
                    progress=30,
                    logs=[error_msg]
                )
            
            return False
    
    def _get_manual_download_guide(self, model_type: str, model_size: str = "14B", resolution: str = "720p") -> str:
        """
        获取手动下载指南
        
        Args:
            model_type: 模型类型，'t2v'或'i2v'
            model_size: 模型大小，仅用于t2v
            resolution: 分辨率，用于i2v或t2v的分辨率参数
            
        Returns:
            str: 手动下载指南
        """
        model_id = self._get_model_id(model_type, model_size, resolution)
        model_path = os.path.join(self.model_cache_dir, model_id)
        
        guide = f"模型文件不存在: {model_path}\n\n"
        guide += "手动下载指南:\n"
        guide += f"1. 请在以下地址下载模型: https://huggingface.co/models?search={model_id}\n"
        guide += f"2. 下载后解压到目录: {self.model_cache_dir}\n"
        guide += f"3. 确保目录结构为: {model_path}/\n"
        guide += f"   - model_index.json\n"
        guide += f"   - config.json\n"
        guide += f"   - 其他模型文件...\n\n"
        guide += f"或者使用环境变量设置自定义模型路径:\n"
        
        if model_type == "t2v":
            guide += f"WAN_T2V_{model_size}_MODEL=你的模型目录名\n"
        elif model_type == "i2v":
            guide += f"WAN_I2V_{resolution.upper()}_MODEL=你的模型目录名\n"
        
        guide += f"\n也可以使用全局缓存目录环境变量:\n"
        guide += f"WAN_MODEL_CACHE_DIR=你的模型缓存根目录"
        
        return guide 

    def _generate_thumbnail(self, video_path: str) -> str:
        """
        为视频生成缩略图
        
        Args:
            video_path: 视频文件路径
            
        Returns:
            str: 缩略图文件路径，如果生成失败则返回空字符串
        """
        logger.info(f"为视频生成缩略图: {video_path}")
        
        try:
            # 检查视频文件是否存在
            if not os.path.exists(video_path):
                logger.error(f"视频文件不存在: {video_path}")
                return ""
            
            # 打开视频文件
            video = cv2.VideoCapture(video_path)
            
            # 检查视频是否成功打开
            if not video.isOpened():
                logger.error(f"无法打开视频文件: {video_path}")
                return ""
            
            # 从视频中获取第一帧作为缩略图
            success, frame = video.read()
            
            # 如果第一帧获取失败，尝试获取其他帧
            frame_count = 0
            while not success and frame_count < 30:
                success, frame = video.read()
                frame_count += 1
            
            # 释放视频对象
            video.release()
            
            # 如果所有尝试都失败
            if not success:
                logger.error(f"无法从视频获取有效帧: {video_path}")
                return ""
            
            # 创建缩略图文件路径（将视频扩展名改为jpg）
            thumbnail_path = os.path.splitext(video_path)[0] + ".jpg"
            
            # 调整大小（如果需要）
            max_dimension = 640
            height, width = frame.shape[:2]
            
            if height > max_dimension or width > max_dimension:
                # 保持纵横比调整大小
                if width >= height:
                    new_width = max_dimension
                    new_height = int(height * (max_dimension / width))
                else:
                    new_height = max_dimension
                    new_width = int(width * (max_dimension / height))
                
                frame = cv2.resize(frame, (new_width, new_height), interpolation=cv2.INTER_AREA)
                logger.info(f"调整缩略图大小到: {new_width}x{new_height}")
            
            # 保存缩略图
            cv2.imwrite(thumbnail_path, frame, [cv2.IMWRITE_JPEG_QUALITY, 90])
            
            # 检查缩略图是否成功保存
            if os.path.exists(thumbnail_path) and os.path.getsize(thumbnail_path) > 0:
                logger.info(f"成功生成缩略图: {thumbnail_path}, 大小: {os.path.getsize(thumbnail_path) / 1024:.2f} KB")
                return thumbnail_path
            else:
                logger.error(f"生成的缩略图不存在或大小为0: {thumbnail_path}")
                return ""
        
        except Exception as e:
            logger.error(f"生成缩略图时出错: {str(e)}")
            logger.error(traceback.format_exc())
            return ""
    
    async def _generate_mock_video(
        self,
        task_id: str,
        prompt: str,
        negative_prompt: str = "",
        num_frames: int = 81,
        height: int = 720,
        width: int = 1280,
        guidance_scale: float = 5.0,
        fps: int = 16,
        seed: Optional[int] = None,
        model_size: str = "14B",
        resolution: str = "720p",
        output_format: str = "mp4",
        progress_updater: Optional[ProgressUpdater] = None
    ) -> Dict[str, Any]:
        """
        生成模拟视频，用于模拟模式
        
        Args:
            task_id: 任务ID
            prompt: 提示文本
            negative_prompt: 负面提示文本
            num_frames: 帧数
            height: 高度
            width: 宽度
            guidance_scale: 引导系数
            fps: 帧率
            seed: 随机种子
            model_size: 模型规格
            resolution: 分辨率
            output_format: 输出格式
            progress_updater: 进度更新器
            
        Returns:
            Dict: 生成结果
        """
        logger.info(f"生成模拟视频: {prompt}, 任务ID: {task_id}")
        
        # 更新进度
        if progress_updater:
            await progress_updater.update(
                progress=10,
                logs=[f"初始化模拟视频生成..."]
            )
        
        try:
            # 如果未提供种子，生成随机种子
            if seed is None:
                import random
                seed = random.randint(0, 2**32 - 1)
                logger.info(f"使用随机种子: {seed}")
            else:
                logger.info(f"使用指定种子: {seed}")
            
            # 更新进度
            if progress_updater:
                await progress_updater.update(
                    progress=20,
                    logs=[f"创建模拟视频帧..."]
                )
            
            # 创建模拟视频帧 - 生成彩色渐变和波纹
            np.random.seed(seed)
            frames = []
            
            for i in range(num_frames):
                frame = np.zeros((height, width, 3), dtype=np.uint8)
                
                # 计算每帧的时间因子 (0.0 到 1.0)
                t = i / (num_frames - 1) if num_frames > 1 else 0.5
                
                # 生成渐变作为基础层
                for y in range(height):
                    for x in range(width):
                        # 创建时间和空间变化的渐变
                        r_base = int(((np.sin(x * 0.01 + i * 0.1) * 0.5 + 0.5) * 255))
                        g_base = int(((np.sin(y * 0.01 + i * 0.1) * 0.5 + 0.5) * 255))
                        b_base = int(((np.sin((x+y) * 0.01 + i * 0.1) * 0.5 + 0.5) * 255))
                        
                        # 添加基于提示的随机变化
                        r_var = (hash(prompt + str(x)) % 100) / 100.0 * 30
                        g_var = (hash(prompt + str(y)) % 100) / 100.0 * 30
                        b_var = (hash(prompt + str(x+y)) % 100) / 100.0 * 30
                        
                        # 确保RGB值在有效范围内
                        r = max(0, min(255, int(r_base + r_var)))
                        g = max(0, min(255, int(g_base + g_var)))
                        b = max(0, min(255, int(b_base + b_var)))
                        
                        frame[y, x] = [r, g, b]
                
                # 添加额外的效果 - 基于提示文本的哈希添加简单形状
                # 这里只添加一些基本的圆形和矩形，在实际应用中可以更复杂
                
                # 基于提示文本哈希确定圆心位置
                prompt_hash = hash(prompt)
                circle_x = prompt_hash % width
                circle_y = (prompt_hash // width) % height
                circle_radius = 50 + (prompt_hash % 100)
                
                # 添加一个圆形，通过时间让它移动
                move_x = int(np.sin(t * 6.28) * width * 0.3)
                move_y = int(np.cos(t * 6.28) * height * 0.3)
                
                # 确保圆心在有效范围内
                cx = max(0, min(width-1, circle_x + move_x))
                cy = max(0, min(height-1, circle_y + move_y))
                
                # 绘制圆形
                cv2.circle(frame, (cx, cy), circle_radius, (200, 100, 50), -1)
                
                # 添加矩形
                rect_x = (prompt_hash * 3) % width
                rect_y = ((prompt_hash * 7) // width) % height
                rect_w = 100 + (prompt_hash % 150)
                rect_h = 80 + ((prompt_hash * 13) % 120)
                
                # 让矩形也移动
                rect_move_x = int(np.cos(t * 4.71) * width * 0.25)
                rect_move_y = int(np.sin(t * 4.71) * height * 0.25)
                
                # 确保矩形在有效范围内
                rx = max(0, min(width-rect_w, rect_x + rect_move_x))
                ry = max(0, min(height-rect_h, rect_y + rect_move_y))
                
                # 绘制矩形
                cv2.rectangle(frame, (rx, ry), (rx+rect_w, ry+rect_h), (50, 150, 200), -1)
                
                # 添加文字 - 使用提示的前20个字符
                text = prompt[:20] + "..." if len(prompt) > 20 else prompt
                font = cv2.FONT_HERSHEY_SIMPLEX
                text_size = cv2.getTextSize(text, font, 1, 2)[0]
                text_x = (width - text_size[0]) // 2
                text_y = height - 50
                
                # 确保文字在有效范围内
                text_x = max(0, min(width-text_size[0], text_x))
                text_y = max(text_size[1], min(height-10, text_y))
                
                # 绘制文字
                cv2.putText(frame, text, (text_x, text_y), font, 1, (255, 255, 255), 2)
                
                # 添加时间码
                time_text = f"Frame: {i+1}/{num_frames}"
                cv2.putText(frame, time_text, (10, 30), font, 0.7, (255, 255, 255), 1)
                
                frames.append(frame)
            
            # 将帧列表转换为numpy数组
            frames = np.array(frames)
            
            # 确保帧数组的形状正确
            logger.info(f"模拟视频帧形状: {frames.shape}")
            
            # 更新进度
            if progress_updater:
                await progress_updater.update(
                    progress=50,
                    logs=[f"模拟视频帧生成完成，导出视频文件..."]
                )
            
            # 将帧数组保存为视频文件
            # 创建临时目录
            temp_dir = os.path.join(tempfile.gettempdir(), f"mock_video_{uuid.uuid4()}")
            os.makedirs(temp_dir, exist_ok=True)
            logger.info(f"创建临时目录: {temp_dir}")
            
            try:
                # 准备视频文件路径
                video_filename = f"video_{task_id}_{int(time.time())}.{output_format}"
                video_path = os.path.join(temp_dir, video_filename)
                
                # 使用OpenCV写入视频
                fourcc = cv2.VideoWriter_fourcc(*'mp4v' if output_format == 'mp4' else 'XVID')
                out = cv2.VideoWriter(video_path, fourcc, fps, (width, height))
                
                # 写入每一帧
                for frame in frames:
                    out.write(frame)
                
                # 释放视频写入器
                out.release()
                
                # 检查视频文件是否成功创建
                if not os.path.exists(video_path) or os.path.getsize(video_path) == 0:
                    logger.error(f"模拟视频文件创建失败: {video_path}")
                    
                    # 更新进度
                    if progress_updater:
                        await progress_updater.update(
                            progress=60,
                            logs=[f"模拟视频文件创建失败"]
                        )
                    
                    return {
                        "success": False,
                        "error": "模拟视频文件创建失败",
                        "task_id": task_id
                    }
                
                # 更新进度
                if progress_updater:
                    await progress_updater.update(
                        progress=70,
                        logs=[f"模拟视频文件创建成功，复制到媒体目录..."]
                    )
                
                # 将视频复制到媒体目录
                current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
                media_dir = os.path.join(current_dir, "media", "videos")
                os.makedirs(media_dir, exist_ok=True)
                
                media_video_path = os.path.join(media_dir, video_filename)
                shutil.copy2(video_path, media_video_path)
                
                # 生成缩略图
                thumbnail_path = self._generate_thumbnail(media_video_path)
                
                # 更新进度
                if progress_updater:
                    await progress_updater.update(
                        progress=100,
                        logs=[f"模拟视频生成完成"]
                    )
                
                # 返回结果
                return {
                    "success": True,
                    "task_id": task_id,
                    "video_url": self._convert_path_to_url(media_video_path, "video"),
                    "thumbnail_url": self._convert_path_to_url(thumbnail_path, "image") if thumbnail_path else "",
                    "video_path": media_video_path,
                    "seed": seed,
                    "frames": num_frames,
                    "fps": fps,
                    "width": width,
                    "height": height,
                    "prompt": prompt,
                    "negative_prompt": negative_prompt,
                    "guidance_scale": guidance_scale,
                    "model_size": model_size,
                    "resolution": resolution,
                    "mock_mode": True
                }
                
            finally:
                # 清理临时目录
                try:
                    if os.path.exists(temp_dir):
                        shutil.rmtree(temp_dir)
                        logger.info(f"清理临时目录: {temp_dir}")
                except Exception as e:
                    logger.error(f"清理临时目录时出错: {str(e)}")
            
        except Exception as e:
            error_msg = f"生成模拟视频时出错: {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            
            # 更新进度
            if progress_updater:
                await progress_updater.update(
                    progress=30,
                    logs=[error_msg]
                )
            
            return {
                "success": False,
                "error": error_msg,
                "task_id": task_id
            } 

# 全局单例实例
_wan_video_service = None

def get_wan_video_service(model_cache_dir: Optional[str] = None, device: Optional[str] = None, 
                         precision: Optional[str] = None, mock_mode: Optional[bool] = None):
    """
    获取WAN视频服务实例 (单例模式)
    
    Args:
        model_cache_dir: 可选，模型缓存目录
        device: 可选，设备 (cuda/cpu)
        precision: 可选，精度 (float32/float16)
        mock_mode: 可选，是否使用模拟模式
        
    Returns:
        WanVideoService: 服务实例
    """
    global _wan_video_service
    if _wan_video_service is None:
        _wan_video_service = WanVideoService(
            model_cache_dir=model_cache_dir, 
            device=device, 
            precision=precision,
            mock_mode=mock_mode
        )
        logger.info("WAN视频服务已初始化")
    elif model_cache_dir is not None or device is not None or precision is not None or mock_mode is not None:
        # 如果已存在实例但指定了新参数，记录警告
        logger.warning("WAN视频服务已存在，忽略新参数")
    return _wan_video_service 