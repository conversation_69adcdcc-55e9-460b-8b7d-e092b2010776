import axios from 'axios';
import { message, Modal } from 'ant-design-vue';
import { getToken, getRefreshToken, setToken, clearAuth } from '@/utils/auth';
import store from '@/store';
import router from '@/router';

// 刷新token请求实例 - 避免循环调用
const refreshTokenRequest = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/admin-api',
  timeout: 10000
});

// 是否正在刷新token
let isRefreshing = false;
// 重试队列
let retryQueue = [];

// 创建axios实例
const service = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/admin-api',
  timeout: 15000,
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true // 跨域请求时发送Cookie
});

// 请求拦截器
service.interceptors.request.use(
  config => {
    // 从store获取token
    const token = store.getters.token;
    
    // 如果有token，添加到请求头
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    
    // 添加时间戳防止缓存
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        _t: new Date().getTime()
      };
    }
    
    return config;
  },
  error => {
    console.error('请求错误:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
service.interceptors.response.use(
  response => {
    const res = response.data;
    
    // 根据后端API约定进行处理
    // 假设后端返回格式为 { code: 0, data: {}, message: '' }
    if (res.code !== 0) {
      message.error(res.message || '请求失败');
      
      // 401: 未登录或token过期
      if (res.code === 401) {
        // 清除用户token信息，跳转到登录页
        store.dispatch('user/logout').then(() => {
          location.reload();
        });
      }
      
      return Promise.reject(new Error(res.message || '请求失败'));
    } else {
      return res.data;
    }
  },
  error => {
    // 取消请求不需要提示
    if (axios.isCancel(error)) {
      return Promise.reject(error);
    }
    
    // 处理HTTP错误状态码
    const { response } = error;
    let errorMsg = '连接服务器失败';
    
    if (response) {
      const { status, data } = response;
      
      // 根据HTTP状态码设置错误消息
      switch (status) {
        case 400:
          errorMsg = data.message || '请求参数错误';
          break;
        case 401:
          errorMsg = '未登录或登录已过期';
          // 清除用户token信息，跳转到登录页
          store.dispatch('user/logout').then(() => {
            location.reload();
          });
          break;
        case 403:
          errorMsg = '没有权限进行此操作';
          break;
        case 404:
          errorMsg = '请求的资源不存在';
          break;
        case 500:
          errorMsg = '服务器内部错误';
          break;
        default:
          errorMsg = `请求失败(${status})`;
      }
    } else if (error.message && error.message.includes('timeout')) {
      errorMsg = '请求超时，请稍后重试';
    }
    
    message.error(errorMsg);
    console.error('响应错误:', error);
    
    return Promise.reject(new Error(errorMsg));
  }
);

/**
 * 处理特定错误码
 * @param {number} code - 错误码
 * @param {string} errorMsg - 错误消息
 */
function handleErrorCode(code, errorMsg) {
  switch (code) {
    case 401:
      // 未授权，可能是Token无效或过期
      message.error(errorMsg || '未授权，请重新登录');
      break;
    case 403:
      message.error(errorMsg || '权限不足，无法访问');
      break;
    case 10001:
      // 假设10001是业务错误码
      message.warning(errorMsg || '操作失败');
      break;
    default:
      if (errorMsg) {
        message.error(errorMsg);
      }
  }
}

/**
 * 处理HTTP错误
 * @param {Error} error - 错误对象
 */
function handleHttpError(error) {
  const { status, data } = error.response;
  const errorMsg = data?.message || '请求失败';
  
  switch (status) {
    case 400:
      message.error(`请求参数错误: ${errorMsg}`);
      break;
    case 403:
      message.error(`权限不足: ${errorMsg}`);
      break;
    case 404:
      message.error(`请求的资源不存在: ${errorMsg}`);
      break;
    case 500:
      message.error(`服务器错误: ${errorMsg}`);
      break;
    default:
      message.error(`请求失败 (${status}): ${errorMsg}`);
  }
}

/**
 * 处理网络错误
 * @param {Error} error - 错误对象
 */
function handleNetworkError(error) {
  if (axios.isCancel(error)) {
    console.log('请求已取消');
    return;
  }
  
  if (error.message.includes('timeout')) {
    message.error('请求超时，请检查网络连接');
  } else if (error.message.includes('Network Error')) {
    message.error('网络异常，请检查网络连接');
  } else {
    message.error(error.message || '请求失败');
  }
}

/**
 * 刷新Token
 * @returns {Promise<string>} 刷新后的Token
 */
async function refreshToken() {
  // 如果已经在刷新Token，则将请求加入队列
  if (isRefreshing) {
    return new Promise((resolve, reject) => {
      retryQueue.push({ resolve, reject });
    });
  }
  
  isRefreshing = true;
  
  try {
    const refreshToken = getRefreshToken();
    if (!refreshToken) {
      throw new Error('无刷新令牌');
    }
    
    const response = await refreshTokenRequest.post('/user/refresh_token', {
      refreshToken
    });
    
    const { token } = response.data.data;
    setToken(token);
    
    // 执行队列中的请求
    retryQueue.forEach(request => {
      request.resolve(token);
    });
    
    return token;
  } catch (error) {
    // 刷新Token失败，执行队列中的请求（失败）
    retryQueue.forEach(request => {
      request.reject(error);
    });
    
    throw error;
  } finally {
    isRefreshing = false;
    retryQueue = [];
  }
}

/**
 * 重定向到登录页
 */
function redirectToLogin() {
  clearAuth();
  
  Modal.warning({
    title: '登录已过期',
    content: '您的登录状态已过期，请重新登录',
    okText: '确定',
    onOk: () => {
      // 保存当前路径，登录成功后跳回
      const currentPath = router.currentRoute.value.fullPath;
      router.push(`/auth/login?redirect=${encodeURIComponent(currentPath)}`);
    }
  });
}

/**
 * 创建取消令牌
 * @returns {Object} 取消令牌源
 */
export function createCancelToken() {
  return axios.CancelToken.source();
}

/**
 * 取消请求
 * @param {Object} tokenSource - 取消令牌源
 * @param {string} msg - 取消消息
 */
export function cancelRequest(tokenSource, msg = '请求已取消') {
  if (tokenSource) {
    tokenSource.cancel(msg);
  }
}

/**
 * GET请求
 * @param {string} url - 请求地址
 * @param {Object} params - 请求参数
 * @param {Object} config - 额外配置
 * @returns {Promise} 请求Promise
 */
export function get(url, params = {}, config = {}) {
  return service.get(url, { params, ...config });
}

/**
 * POST请求
 * @param {string} url - 请求地址
 * @param {Object} data - 请求数据
 * @param {Object} config - 额外配置
 * @returns {Promise} 请求Promise
 */
export function post(url, data = {}, config = {}) {
  return service.post(url, data, config);
}

/**
 * PUT请求
 * @param {string} url - 请求地址
 * @param {Object} data - 请求数据
 * @param {Object} config - 额外配置
 * @returns {Promise} 请求Promise
 */
export function put(url, data = {}, config = {}) {
  return service.put(url, data, config);
}

/**
 * DELETE请求
 * @param {string} url - 请求地址
 * @param {Object} params - 请求参数
 * @param {Object} config - 额外配置
 * @returns {Promise} 请求Promise
 */
export function del(url, params = {}, config = {}) {
  return service.delete(url, { params, ...config });
}

/**
 * 下载文件
 * @param {string} url - 请求地址
 * @param {Object} params - 请求参数
 * @param {string} filename - 文件名
 * @param {string} method - 请求方法
 * @returns {Promise} 请求Promise
 */
export function download(url, params = {}, filename, method = 'get') {
  const requestConfig = {
    responseType: 'blob',
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/octet-stream'
    }
  };
  
  let request;
  if (method.toLowerCase() === 'post') {
    request = service.post(url, params, requestConfig);
  } else {
    request = service.get(url, { params, ...requestConfig });
  }
  
  return request.then(response => {
    // 创建Blob对象
    const blob = new Blob([response]);
    
    // 创建下载链接
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    
    // 设置下载文件名
    const contentDisposition = response.headers['content-disposition'];
    let fname = filename;
    
    if (!fname && contentDisposition) {
      const match = contentDisposition.match(/filename="?([^"]+)"?/);
      if (match) {
        fname = match[1];
      }
    }
    
    link.download = fname || 'download';
    link.style.display = 'none';
    
    // 添加到DOM并触发点击
    document.body.appendChild(link);
    link.click();
    
    // 清理
    URL.revokeObjectURL(link.href);
    document.body.removeChild(link);
    
    return response;
  });
}

export default service; 