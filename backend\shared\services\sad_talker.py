import logging
import torch
import os
import datetime
import librosa
import cv2
import numpy as np
import yaml
from PIL import Image
from torchvision import transforms
from pydub import AudioSegment
import tempfile
import uuid
import time
import asyncio
import sys
import subprocess
import traceback
import json

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 尝试导入TTS服务
try:
    from services.tts_service import get_tts_service
    USE_TTS_SERVICE = True
except ImportError:
    USE_TTS_SERVICE = False
    logger.warning("TTS服务导入失败，将使用模拟TTS")

# 单例实例
_sad_talker_instance = None

def get_sad_talker():
    """获取SadTalker实例（单例模式）"""
    global _sad_talker_instance
    if _sad_talker_instance is None:
        logger.info("初始化新的SadTalker实例")
        _sad_talker_instance = SadTalker()
        logger.info("SadTalker实例初始化完成")
    return _sad_talker_instance

# 模拟SadTalker模型 (仅作为备用)
class SadTalkerModel(torch.nn.Module):
    """SadTalker模型的模拟实现"""
    
    def __init__(self, config=None):
        super().__init__()
        self.config = config
        self.conv = torch.nn.Conv2d(3, 64, kernel_size=3, padding=1)
        self.relu = torch.nn.ReLU()
        self.pool = torch.nn.MaxPool2d(2)
        
    def forward(self, image, audio):
        """模拟前向传播"""
        # 返回随机视频数据
        batch_size = 1
        frames = 90  # 3秒，30fps
        height = 256
        width = 256
        channels = 3
        
        # 创建随机张量
        video_data = torch.rand(batch_size, frames, channels, height, width)
        return video_data
        
    @staticmethod
    def load_state_dict(weights):
        """模拟加载权重"""
        return True

class SadTalker:
    """SadTalker服务，用于生成数字人视频"""
    
    def __init__(self):
        self.model = None
        self.is_initialized = False
        self.use_real_model = False  # 标记是否使用真实模型
        self.real_sadtalker_imported = False  # 标记是否导入了真实的SadTalker
        
        # 确保logger正确初始化
        global logger
        if not logger:
            logger = logging.getLogger(__name__)
            logger.setLevel(logging.INFO)
        
        # 设置输出目录
        self.output_dir = os.path.join(os.getcwd(), "static", "videos")
        os.makedirs(self.output_dir, exist_ok=True)
        logger.info(f"SadTalker输出目录: {self.output_dir}")
        
        # 设置音频目录
        self.audio_dir = os.path.join(os.getcwd(), "static", "audio")
        os.makedirs(self.audio_dir, exist_ok=True)
        logger.info(f"SadTalker音频目录: {self.audio_dir}")
        
        # 设置SadTalker路径
        self.sadtalker_path = self._find_sadtalker_path()
        logger.info(f"找到SadTalker路径: {self.sadtalker_path}")
        
        try:
            from services.tts_service import get_tts_service
            self.tts_service = get_tts_service()
            logger.info("TTS服务加载成功")
        except ImportError:
            logger.warning("TTS服务导入失败，将使用模拟TTS")
            self.tts_service = None
        except Exception as e:
            logger.error(f"加载TTS服务时出错: {str(e)}")
            logger.exception("TTS服务加载详细错误")
            self.tts_service = None
        
        self._load_model()
    
    def _find_sadtalker_path(self):
        """查找SadTalker的路径"""
        # 尝试多个可能的路径，优先本地模型目录
        current_dir = os.getcwd()
        backend_dir = os.path.join(current_dir, "backend") if not current_dir.endswith("backend") else current_dir
        
        possible_paths = [
            os.path.join(backend_dir, "local_models", "SadTalker"),  # 优先本地模型目录
            os.path.join(backend_dir, "scripts", "third_party", "SadTalker"),
            os.path.join(backend_dir, "scripts/third_party/SadTalker"),
            os.path.join(backend_dir, "scripts\\third_party\\SadTalker"),
            os.path.join(current_dir, "scripts", "third_party", "SadTalker"),
            os.path.join(current_dir, "third_party", "SadTalker"),
        ]
        
        # 显示所有尝试的路径
        logger.info(f"尝试查找SadTalker路径，当前工作目录: {current_dir}")
        for i, path in enumerate(possible_paths):
            exists = os.path.exists(path)
            logger.info(f"  路径 {i+1}: {path} - {'存在' if exists else '不存在'}")
            if exists:
                return path
        
        # 找不到任何可用路径
        logger.warning(f"无法找到SadTalker路径，将使用模拟模式")
        return None
        
    def _load_model(self):
        """加载SadTalker模型"""
        try:
            # 如果找不到SadTalker路径，使用模拟模型
            if self.sadtalker_path is None:
                logger.warning("SadTalker路径未设置，无法加载真实模型")
                self.model = SadTalkerModel(None)
                self.is_initialized = True
                self.use_real_model = False
                return
            
            # 检查模型文件是否存在
            checkpoints_dir = os.path.join(self.sadtalker_path, "checkpoints")
            logger.info(f"检查SadTalker检查点目录: {checkpoints_dir}")
            
            if not os.path.exists(checkpoints_dir):
                logger.warning(f"SadTalker检查点目录不存在: {checkpoints_dir}")
                self.model = SadTalkerModel(None)
                self.is_initialized = True
                self.use_real_model = False
                return
            
            # 检查必要的模型文件
            required_models = [
                "mapping_00109-model.pth.tar", 
                "mapping_00229-model.pth.tar",
                "SadTalker_V0.0.2_256.safetensors"
            ]
            
            missing_models = []
            for model_file in required_models:
                full_path = os.path.join(checkpoints_dir, model_file)
                if not os.path.exists(full_path):
                    missing_models.append(model_file)
                    logger.warning(f"模型文件不存在: {full_path}")
                else:
                    logger.info(f"找到模型文件: {full_path}")
            
            if missing_models:
                logger.warning(f"缺少以下SadTalker模型文件: {missing_models}")
                logger.warning("将使用模拟模型代替")
                self.model = SadTalkerModel(None)
                self.is_initialized = True
                self.use_real_model = False
                return
            
            # 加载真实的SadTalker模型
            # 首先将SadTalker路径添加到sys.path
            if self.sadtalker_path not in sys.path:
                sys.path.append(self.sadtalker_path)
                logger.info(f"已将SadTalker路径添加到sys.path: {self.sadtalker_path}")
            
            # 尝试导入SadTalker的关键模块
            try:
                # 导入需要的模块，但不直接使用
                # 真正的调用将在generate_video时通过subprocess完成
                from src.utils.preprocess import CropAndExtract
                from src.test_audio2coeff import Audio2Coeff  
                from src.facerender.animate import AnimateFromCoeff
                logger.info("成功导入SadTalker模块")
                self.real_sadtalker_imported = True
                
                # 在这里不实际加载模型，而是设置标志位和配置
                # 调用SadTalker会在generate_video中通过命令行完成
                self.use_real_model = True
                self.is_initialized = True
                logger.info("设置为使用真实SadTalker模型")
                
            except ImportError as e:
                logger.error(f"导入SadTalker模块失败: {str(e)}")
                logger.exception("SadTalker导入详细错误")
                self.model = SadTalkerModel(None)
                self.is_initialized = True
                self.use_real_model = False
                logger.warning("将使用模拟模型代替")
            
        except Exception as e:
            logger.error(f"SadTalker模型加载失败: {str(e)}")
            logger.exception("SadTalker模型加载详细错误")
            # 创建一个默认模型而不是失败
            self.model = SadTalkerModel(None)
            logger.info("使用默认SadTalker模型代替")
            self.is_initialized = True
            self.use_real_model = False
    
    async def generate_speech(self, text: str, voice_id: str = None) -> str:
        """
        生成语音
        
        Args:
            text: 要转换的文本
            voice_id: 语音ID，如果为None则使用默认语音
            
        Returns:
            str: 生成的音频文件路径
        """
        try:
            # 详细日志记录
            logger.info(f"开始生成语音，文本: '{text[:50]}...' (长度: {len(text)}), 语音ID: {voice_id}")
            
            # 验证voice_id
            original_voice_id = voice_id  # 保存原始值用于日志
            if voice_id in [None, 'system', '']:
                voice_id = self.tts_service.default_voice_id if self.tts_service else "zh-female1"
                logger.info(f"使用默认语音ID: {voice_id} (原值: {original_voice_id})")
            
            # 生成音频文件路径
            audio_path = os.path.join(self.audio_dir, f"{uuid.uuid4()}.wav")
            logger.info(f"目标音频文件路径: {audio_path}")
            
            # 检查目录是否存在并可写
            if not os.path.exists(self.audio_dir):
                logger.warning(f"音频目录不存在，尝试创建: {self.audio_dir}")
                os.makedirs(self.audio_dir, exist_ok=True)
            
            if not os.access(self.audio_dir, os.W_OK):
                logger.error(f"音频目录没有写入权限: {self.audio_dir}")
                raise PermissionError(f"没有权限写入目录: {self.audio_dir}")
            
            # 如果TTS服务可用，使用TTS服务生成语音
            if self.tts_service:
                logger.info(f"TTS服务已加载，将调用TTS服务")
                try:
                    logger.info(f"调用TTS服务generate_speech，voice_id={voice_id}, output_path={audio_path}")
                    result = await self.tts_service.generate_speech(
                        text=text,
                        voice_id=voice_id,
                        output_path=audio_path
                    )
                    
                    # 详细记录TTS结果
                    logger.info(f"TTS服务返回结果类型: {type(result).__name__}")
                    if isinstance(result, dict):
                        logger.info(f"TTS返回字典: {result}")
                        if "status" in result and result["status"] == "success" and "path" in result:
                            logger.info(f"使用TTS服务生成语音成功: {result['path']}")
                            # 验证生成的文件
                            if os.path.exists(result['path']):
                                file_size = os.path.getsize(result['path'])
                                logger.info(f"音频文件已生成，大小: {file_size} 字节")
                                if file_size < 100:  # 小于100字节的文件可能是无效的
                                    logger.warning(f"生成的音频文件异常小: {file_size} 字节")
                            else:
                                logger.error(f"TTS服务返回的路径不存在: {result['path']}")
                                raise FileNotFoundError(f"TTS服务返回的路径不存在: {result['path']}")
                            return result["path"]
                        elif "path" in result:
                            logger.info(f"使用TTS服务生成语音成功: {result['path']}")
                            # 验证生成的文件
                            if os.path.exists(result['path']):
                                file_size = os.path.getsize(result['path'])
                                logger.info(f"音频文件已生成，大小: {file_size} 字节")
                                if file_size < 100:  # 小于100字节的文件可能是无效的
                                    logger.warning(f"生成的音频文件异常小: {file_size} 字节")
                            else:
                                logger.error(f"TTS服务返回的路径不存在: {result['path']}")
                                raise FileNotFoundError(f"TTS服务返回的路径不存在: {result['path']}")
                            return result["path"]
                        else:
                            logger.warning(f"TTS服务返回的结果缺少路径信息: {result}")
                            logger.info(f"创建模拟音频文件作为备选: {audio_path}")
                            self._create_mock_audio(audio_path)
                            return audio_path
                    else:
                        # 结果是字符串路径
                        logger.info(f"使用TTS服务生成语音成功，返回字符串路径: {result}")
                        # 验证生成的文件
                        if result and os.path.exists(result):
                            file_size = os.path.getsize(result)
                            logger.info(f"音频文件已生成，大小: {file_size} 字节")
                            if file_size < 100:  # 小于100字节的文件可能是无效的
                                logger.warning(f"生成的音频文件异常小: {file_size} 字节")
                            return result
                        else:
                            logger.error(f"TTS服务返回的路径为空或不存在: {result}")
                            logger.info(f"创建模拟音频文件作为备选: {audio_path}")
                            self._create_mock_audio(audio_path)
                            return audio_path
                        
                except Exception as e:
                    logger.error(f"TTS服务生成语音失败: {str(e)}")
                    logger.exception("TTS服务生成语音详细错误")
                    # 如果TTS服务失败，创建模拟音频文件
                    logger.info(f"创建模拟音频文件替代TTS生成失败: {audio_path}")
                    self._create_mock_audio(audio_path)
                    return audio_path
            else:
                # 如果TTS服务不可用，创建模拟音频文件
                logger.warning("TTS服务不可用，创建模拟音频文件")
                self._create_mock_audio(audio_path)
                logger.info(f"创建的模拟音频文件: {audio_path}")
                return audio_path
            
        except Exception as e:
            logger.error(f"生成语音过程中发生未预期的异常: {str(e)}")
            logger.exception("生成语音详细错误信息")
            # 尝试创建紧急模拟音频
            try:
                emergency_audio_path = os.path.join(self.audio_dir, f"emergency_{uuid.uuid4()}.wav")
                logger.info(f"尝试创建紧急模拟音频文件: {emergency_audio_path}")
                self._create_mock_audio(emergency_audio_path)
                logger.info(f"已创建紧急模拟音频文件: {emergency_audio_path}")
                return emergency_audio_path
            except Exception as e2:
                logger.critical(f"创建紧急模拟音频文件也失败: {str(e2)}")
                raise RuntimeError(f"完全无法生成音频: {str(e)}, 紧急创建也失败: {str(e2)}")
    
    async def process_audio(self, audio_path: str) -> dict:
        """
        处理音频文件
        
        Args:
            audio_path: 音频文件路径
            
        Returns:
            dict: 处理后的音频信息
        """
        logger.info(f"开始处理音频: {audio_path}")
        
        try:
            # 检查文件是否存在
            if not os.path.exists(audio_path):
                logger.error(f"音频文件不存在: {audio_path}")
                raise FileNotFoundError(f"音频文件不存在: {audio_path}")
            
            # 获取文件名和扩展名
            audio_name = os.path.basename(audio_path)
            
            # 构建输出路径
            output_filename = f"processed_{audio_name}"
            output_path = os.path.join(self.audio_dir, output_filename)
            
            # 这里可以添加实际的音频处理代码
            # 模拟处理音频
            logger.info(f"复制音频文件到: {output_path}")
            import shutil
            shutil.copy(audio_path, output_path)
            
            # 创建服务可访问的URL路径
            url_path = f"/static/audio/{output_filename}"
            
            logger.info(f"音频处理完成，路径: {output_path}, URL: {url_path}")
            
            return {
                "path": output_path,
                "url": url_path,
                "duration": 5.0  # 模拟音频时长
            }
            
        except Exception as e:
            logger.error(f"处理音频失败: {str(e)}")
            logger.exception("处理音频详细错误")
            raise
    
    def _create_mock_audio(self, audio_path: str) -> None:
        """
        创建模拟音频文件
        
        Args:
            audio_path: 音频文件路径
        """
        logger.info(f"开始创建模拟音频文件: {audio_path}")
        
        # 确保输出目录存在
        audio_dir = os.path.dirname(audio_path)
        if not os.path.exists(audio_dir):
            logger.info(f"创建音频输出目录: {audio_dir}")
            os.makedirs(audio_dir, exist_ok=True)
        
        # 检查目录权限
        if not os.access(audio_dir, os.W_OK):
            logger.error(f"音频目录没有写入权限: {audio_dir}")
            raise PermissionError(f"没有权限写入目录: {audio_dir}")
        
        try:
            # 首选方法：使用pydub创建有效的空白音频文件
            logger.info("尝试使用pydub创建模拟音频文件...")
            try:
                from pydub import AudioSegment
                from pydub.generators import Sine
                
                # 创建一个2秒的静音
                duration_ms = 2000
                sample_rate = 44100
                
                # 创建一个空的音频段（静音）
                silent_segment = AudioSegment.silent(duration=duration_ms, frame_rate=sample_rate)
                
                # 添加一个非常轻微的音调，确保文件有实际内容
                # 生成440Hz的音调，音量非常低
                tone = Sine(440).to_audio_segment(duration=duration_ms, volume=-50)
                result_segment = silent_segment.overlay(tone)
                
                # 导出为wav格式
                logger.info(f"使用pydub将音频导出到: {audio_path}")
                result_segment.export(audio_path, format="wav")
                
                # 验证创建的文件
                if os.path.exists(audio_path):
                    file_size = os.path.getsize(audio_path)
                    logger.info(f"使用pydub创建的音频文件大小: {file_size} 字节")
                    if file_size < 100:
                        logger.warning(f"pydub生成的音频文件异常小: {file_size} 字节")
                else:
                    logger.error(f"pydub导出后文件不存在: {audio_path}")
                    raise FileNotFoundError(f"pydub导出后文件不存在: {audio_path}")
                
                logger.info(f"使用pydub创建模拟音频文件成功: {audio_path}")
                return
            except ImportError as ie:
                logger.warning(f"pydub未安装或导入失败: {str(ie)}，尝试备用方法")
            except Exception as pydub_err:
                logger.error(f"使用pydub创建音频失败: {str(pydub_err)}")
                logger.exception("pydub创建音频详细错误")
            
            # 备用方法1：使用二进制模式写入一个空白的WAV文件头
            logger.info("尝试使用WAV文件头方式创建模拟音频文件...")
            try:
                with open(audio_path, "wb") as f:
                    # WAV文件头 - 44字节
                    # RIFF header
                    f.write(b'RIFF')  # ChunkID
                    f.write((36).to_bytes(4, 'little'))  # ChunkSize: 4 + (8 + SubChunk1Size) + (8 + SubChunk2Size)
                    f.write(b'WAVE')  # Format
                    
                    # fmt subchunk
                    f.write(b'fmt ')  # Subchunk1ID
                    f.write((16).to_bytes(4, 'little'))  # Subchunk1Size: 16 for PCM
                    f.write((1).to_bytes(2, 'little'))  # AudioFormat: 1 for PCM
                    f.write((1).to_bytes(2, 'little'))  # NumChannels: 1 for mono
                    f.write((8000).to_bytes(4, 'little'))  # SampleRate: 8000 Hz
                    f.write((8000).to_bytes(4, 'little'))  # ByteRate: SampleRate * NumChannels * BitsPerSample/8
                    f.write((1).to_bytes(2, 'little'))  # BlockAlign: NumChannels * BitsPerSample/8
                    f.write((8).to_bytes(2, 'little'))  # BitsPerSample: 8 bits
                    
                    # data subchunk
                    f.write(b'data')  # Subchunk2ID
                    f.write((0).to_bytes(4, 'little'))  # Subchunk2Size: 0 for empty audio
                
                # 添加少量实际数据，使其成为有效的WAV文件
                with open(audio_path, "ab") as f:
                    # 添加500字节无声数据
                    f.write(b'\x80' * 500)  # 中点值（无声）
                    # 更新数据大小
                    f.seek(40, 0)  # 移动到data size位置
                    f.write((500).to_bytes(4, 'little'))  # 写入数据大小
                    # 更新文件大小
                    f.seek(4, 0)  # 移动到文件大小位置
                    f.write((36 + 500).to_bytes(4, 'little'))  # 写入文件大小
                
                # 验证文件
                if os.path.exists(audio_path):
                    file_size = os.path.getsize(audio_path)
                    logger.info(f"使用WAV头方式创建的音频文件大小: {file_size} 字节")
                    if file_size < 500:
                        logger.warning(f"WAV文件头方式创建的音频文件异常小: {file_size} 字节")
                else:
                    logger.error(f"WAV文件头方式创建后文件不存在: {audio_path}")
                
                logger.info(f"使用WAV文件头方式创建模拟音频文件成功: {audio_path}")
                return
            except Exception as e:
                logger.error(f"使用WAV文件头方式创建模拟音频文件失败: {str(e)}")
                logger.exception("WAV文件头方式创建详细错误")
            
            # 备用方法2：使用硬编码的WAV文件头
            logger.info("尝试使用硬编码WAV头创建模拟音频...")
            try:
                with open(audio_path, "wb") as f:
                    # 这是一个最小的有效WAV文件头 + 一些数据
                    f.write(b"RIFF\x24\x00\x00\x00WAVEfmt \x10\x00\x00\x00\x01\x00\x01\x00\x00\x1F\x00\x00\x00\x1F\x00\x00\x01\x00\x08\x00data\x00\x00\x00\x00")
                    # 添加一些实际数据
                    f.write(b'\x80' * 1000)  # 中点值（无声）
                    
                # 验证文件
                if os.path.exists(audio_path):
                    file_size = os.path.getsize(audio_path)
                    logger.info(f"使用硬编码WAV头创建的音频文件大小: {file_size} 字节")
                
                logger.info(f"使用硬编码WAV头创建模拟音频文件成功: {audio_path}")
                return
            except Exception as e2:
                logger.error(f"使用硬编码WAV头创建模拟音频文件也失败: {str(e2)}")
                logger.exception("硬编码WAV头创建详细错误")
                
                # 最终尝试：创建一个空文件
                try:
                    logger.info("尝试创建简单的空文件...")
                    with open(audio_path, "wb") as f:
                        f.write(b"MOCK AUDIO FILE - This is not a valid audio file but a placeholder")
                    logger.warning(f"已创建简单的假音频文件: {audio_path}，注意这不是有效的音频文件")
                except Exception as e3:
                    logger.critical(f"创建简单假音频文件也失败: {str(e3)}")
                    logger.exception("简单假音频文件创建详细错误")
                    raise RuntimeError(f"无法创建任何形式的音频文件: {str(e3)}")
        except Exception as e:
            logger.critical(f"创建模拟音频文件过程中发生未捕获异常: {str(e)}")
            logger.exception("未捕获音频创建异常详细信息")
            raise

    async def generate_video(self, source_image: str, audio_path: str, gender: str) -> dict:
        """
        生成视频
        
        Args:
            source_image: 源图像路径
            audio_path: 音频文件路径
            gender: 性别参数
            
        Returns:
            dict: 生成的视频信息
        """
        logger.info(f"开始生成视频，源图像: {source_image}, 音频: {audio_path}")
        
        try:
            # 检查文件是否存在
            if not os.path.exists(source_image):
                logger.error(f"源图像不存在: {source_image}")
                raise FileNotFoundError(f"源图像不存在: {source_image}")
                
            if not os.path.exists(audio_path):
                logger.error(f"音频文件不存在: {audio_path}")
                raise FileNotFoundError(f"音频文件不存在: {audio_path}")
            
            # 记录输入文件信息
            source_image_size = os.path.getsize(source_image)
            audio_size = os.path.getsize(audio_path)
            logger.info(f"源图像大小: {source_image_size} 字节, 音频文件大小: {audio_size} 字节")
            
            # 检查文件是否为空或太小
            if source_image_size < 100:
                logger.warning(f"源图像文件异常小: {source_image_size} 字节")
            
            if audio_size < 100:
                logger.warning(f"音频文件异常小: {audio_size} 字节")
            
            # 确保输出目录存在并可写
            if not os.path.exists(self.output_dir):
                logger.info(f"输出目录不存在，尝试创建: {self.output_dir}")
                os.makedirs(self.output_dir, exist_ok=True)
            
            if not os.access(self.output_dir, os.W_OK):
                logger.error(f"输出目录没有写入权限: {self.output_dir}")
                raise PermissionError(f"没有权限写入目录: {self.output_dir}")
            
            # 创建唯一文件名
            video_filename = f"{uuid.uuid4()}.mp4"
            video_path = os.path.join(self.output_dir, video_filename)
            logger.info(f"生成的视频将保存为: {video_path}")
            
            # 如果使用真实SadTalker模型
            if self.use_real_model and self.sadtalker_path:
                logger.info("使用真实SadTalker模型生成视频")
                
                # 验证SadTalker模型文件
                if not self._verify_sadtalker_files():
                    logger.warning("SadTalker模型文件验证失败，将使用模拟方法生成视频")
                else:
                    # 创建临时输出目录
                    temp_dir = tempfile.gettempdir()  # 使用tempfile模块获取临时目录
                    temp_output_dir = os.path.join(temp_dir, f"sadtalker_output_{uuid.uuid4()}")
                    os.makedirs(temp_output_dir, exist_ok=True)
                    logger.info(f"创建临时输出目录: {temp_output_dir}")
                    
                    # 构建调用SadTalker的命令
                    inference_script = os.path.join(self.sadtalker_path, "inference.py")
                    
                    # 确保使用绝对路径
                    abs_source_image = os.path.abspath(source_image)
                    abs_audio_path = os.path.abspath(audio_path)
                    abs_output_dir = os.path.abspath(temp_output_dir)
                    
                    # SadTalker命令参数
                    cmd = [
                        sys.executable,  # 当前Python解释器
                        os.path.basename(inference_script),  # 仅使用脚本名称，因为我们会切换工作目录
                        "--source_image", abs_source_image,
                        "--driven_audio", abs_audio_path,
                        "--result_dir", abs_output_dir,
                        "--pose_style", "0",  # 使用默认姿势
                        "--batch_size", "2",  # 批处理大小
                        "--size", "256",      # 大小
                        "--preprocess", "full"  # 预处理方式
                    ]
                    
                    # 如果有GPU，添加设备参数
                    if torch.cuda.is_available():
                        logger.info("检测到CUDA可用，将使用GPU运行SadTalker")
                    else:
                        logger.info("未检测到CUDA，将使用CPU运行SadTalker")
                        cmd.append("--cpu")  # 添加CPU标志
                    
                    # 记录原始工作目录，以便之后恢复
                    original_cwd = os.getcwd()
                    logger.info(f"当前工作目录: {original_cwd}")
                    logger.info(f"切换工作目录到: {self.sadtalker_path}")
                    
                    # 执行命令
                    logger.info(f"执行SadTalker命令: {' '.join(cmd)}")
                    
                    try:
                        # 切换到SadTalker目录
                        os.chdir(self.sadtalker_path)
                        logger.info(f"已切换工作目录到: {os.getcwd()}")
                        
                        # 使用asyncio.create_subprocess_exec异步执行命令
                        process = await asyncio.create_subprocess_exec(
                            *cmd,
                            stdout=asyncio.subprocess.PIPE,
                            stderr=asyncio.subprocess.PIPE
                        )
                        
                        # 等待命令执行完成
                        stdout, stderr = await process.communicate()
                        
                        # 恢复原始工作目录
                        os.chdir(original_cwd)
                        logger.info(f"已恢复工作目录到: {os.getcwd()}")
                        
                        # 检查命令执行结果
                        if process.returncode != 0:
                            stderr_content = stderr.decode('utf-8', errors='replace')
                            stdout_content = stdout.decode('utf-8', errors='replace')
                            logger.error(f"SadTalker命令执行失败，返回代码: {process.returncode}")
                            logger.error(f"标准错误输出: {stderr_content}")
                            logger.error(f"标准输出: {stdout_content}")
                            
                            # 检查常见错误模式
                            if "No such file or directory" in stderr_content:
                                logger.error("检测到文件未找到错误，请确保所有模型文件都已下载")
                            elif "CUDA out of memory" in stderr_content:
                                logger.error("检测到CUDA内存不足，尝试使用--cpu选项或减小批处理大小")
                            elif "ModuleNotFoundError" in stderr_content:
                                logger.error("检测到模块缺失，请确保已安装SadTalker的所有依赖")
                            
                            raise RuntimeError(f"SadTalker命令执行失败，返回代码: {process.returncode}")
                        
                        # 记录命令输出
                        logger.info(f"SadTalker命令执行成功")
                        logger.debug(f"标准输出: {stdout.decode('utf-8', errors='replace')}")
                        
                        # 找到SadTalker生成的视频文件
                        output_mp4_files = []
                        for root, _, files in os.walk(temp_output_dir):
                            for file in files:
                                if file.endswith(".mp4"):
                                    output_mp4_files.append(os.path.join(root, file))
                        
                        # 检查是否找到视频文件
                        if not output_mp4_files:
                            logger.error(f"SadTalker命令执行成功，但未找到生成的视频文件")
                            raise FileNotFoundError(f"未找到SadTalker生成的视频文件")
                        
                        # 使用最新的视频文件
                        output_mp4_files.sort(key=os.path.getmtime, reverse=True)
                        sadtalker_output_video = output_mp4_files[0]
                        logger.info(f"找到SadTalker生成的视频: {sadtalker_output_video}")
                        
                        # 导入必要的模块
                        import shutil
                        import subprocess
                        
                        # 创建一个临时文件用于转码
                        temp_video_path = os.path.join(temp_dir, f"temp_transcoded_{uuid.uuid4()}.mp4")
                        logger.info(f"开始对SadTalker生成的视频进行转码，确保浏览器兼容性")

                        # 构建ffmpeg转码命令
                        try:
                            transcode_cmd = [
                                "ffmpeg",
                                "-y",  # 覆盖输出文件
                                "-i", sadtalker_output_video,  # 输入SadTalker生成的视频
                                "-c:v", "libx264",  # 视频编码
                                "-profile:v", "baseline",  # 使用基准配置，兼容性最好
                                "-level", "3.0",  # 设置较低的编码级别增加兼容性
                                "-preset", "medium",  # 平衡编码速度和质量
                                "-movflags", "+faststart",  # 优化网络流媒体播放
                                "-pix_fmt", "yuv420p",  # 像素格式，确保兼容性
                                "-c:a", "aac",  # 音频编码
                                "-b:a", "192k",  # 音频比特率
                                temp_video_path  # 输出路径
                            ]
                            
                            # 执行ffmpeg转码命令
                            transcode_cmd_str = ' '.join(transcode_cmd)
                            logger.info(f"执行ffmpeg转码命令: {transcode_cmd_str}")
                            
                            transcode_process = subprocess.run(
                                transcode_cmd,
                                stdout=subprocess.PIPE,
                                stderr=subprocess.PIPE
                            )
                            
                            # 检查转码是否成功
                            if transcode_process.returncode == 0:
                                logger.info(f"转码成功，生成浏览器兼容的视频: {temp_video_path}")
                                # 使用转码后的视频作为最终视频
                                shutil.copy2(temp_video_path, video_path)
                                logger.info(f"已将转码后的视频复制到最终位置: {video_path}")
                            else:
                                # 转码失败，使用原始SadTalker生成的视频
                                error_output = transcode_process.stderr.decode()
                                logger.warning(f"转码失败，将使用原始SadTalker视频: {error_output}")
                                # 复制到最终输出路径 (使用原始SadTalker视频)
                                shutil.copy2(sadtalker_output_video, video_path)
                                logger.info(f"已将原始SadTalker视频复制到: {video_path}")
                        except Exception as transcode_err:
                            # 转码出错，使用原始SadTalker生成的视频
                            logger.warning(f"转码过程出错: {str(transcode_err)}")
                            # 复制到最终输出路径 (使用原始SadTalker视频)
                            shutil.copy2(sadtalker_output_video, video_path)
                            logger.info(f"已将原始SadTalker视频复制到: {video_path}")

                        # 验证文件
                        if os.path.exists(video_path):
                            video_size = os.path.getsize(video_path)
                            logger.info(f"最终视频文件大小: {video_size} 字节")
                            
                            # 创建服务可访问的URL路径
                            url_path = f"/static/videos/{video_filename}"
                            
                            # 清理临时文件
                            try:
                                # 删除临时转码文件
                                if os.path.exists(temp_video_path):
                                    os.remove(temp_video_path)
                                    logger.info(f"已删除临时转码文件: {temp_video_path}")
                                
                                # 删除临时输出目录
                                shutil.rmtree(temp_output_dir)
                                logger.info(f"已清理临时输出目录: {temp_output_dir}")
                            except Exception as cleanup_err:
                                logger.warning(f"清理临时文件失败: {cleanup_err}")
                            
                            # 返回结果
                            return {
                                "path": video_path,
                                "url": url_path,
                                "duration": 5.0,  # 假设时长
                                "size": video_size,
                                "model": "SadTalker_Real",  # 标记为真实SadTalker
                                "generation_type": "real",
                                "metadata": {
                                    "generator": "SadTalker",
                                    "version": self._get_sadtalker_version() or "Unknown",
                                    "timestamp": datetime.datetime.now().isoformat(),
                                    "source_image": os.path.basename(source_image),
                                    "audio": os.path.basename(audio_path),
                                    "transcoded": True,  # 标记视频已经过转码处理
                                    "gender": gender  # 记录性别参数
                                }
                            }
                        else:
                            logger.error(f"复制SadTalker视频后，最终文件不存在: {video_path}")
                            # 降级到模拟生成
                            logger.warning("将使用模拟方法生成视频")
                            
                    except Exception as sad_talker_err:
                        # 确保工作目录被恢复
                        if os.getcwd() != original_cwd:
                            os.chdir(original_cwd)
                            logger.info(f"异常处理中恢复工作目录到: {os.getcwd()}")
                        
                        logger.error(f"执行SadTalker命令时出错: {str(sad_talker_err)}")
                        logger.exception("SadTalker执行详细错误")
                        logger.warning("将使用模拟方法生成视频")
            else:
                logger.info("使用模拟方法生成视频（未启用真实SadTalker模型）")
            
            # 模拟处理时间
            await asyncio.sleep(1)
            
            # 创建模拟视频文件 - 使用ffmpeg从静态图像和音频生成
            try:
                # 检查ffmpeg是否可用
                try:
                    import subprocess
                    result = subprocess.run(["ffmpeg", "-version"], stdout=subprocess.PIPE, stderr=subprocess.PIPE, timeout=5)
                    if result.returncode != 0:
                        logger.error(f"ffmpeg不可用，版本检查返回非零状态: {result.returncode}")
                        logger.error(f"ffmpeg错误输出: {result.stderr.decode()}")
                        raise RuntimeError("ffmpeg不可用")
                    logger.info(f"ffmpeg版本检查通过: {result.stdout.decode().splitlines()[0]}")
                except FileNotFoundError:
                    logger.error("ffmpeg命令未找到，请确保已安装ffmpeg并添加到PATH中")
                    raise RuntimeError("ffmpeg命令未找到")
                except Exception as e:
                    logger.error(f"检查ffmpeg可用性失败: {str(e)}")
                    logger.exception("ffmpeg可用性检查详细错误")
                    raise RuntimeError(f"无法确认ffmpeg可用性: {str(e)}")
                
                import subprocess  # 局部导入，避免名称冲突
                import cv2
                import numpy as np
                
                # 创建一个简单的图像，避免破坏原图
                logger.info("创建用于生成视频的模拟图像")
                img_height, img_width = 640, 480
                color_image = np.zeros((img_height, img_width, 3), dtype=np.uint8)
                # 设置背景色为浅灰色
                color_image[:, :] = [200, 200, 200]
                
                # 画一个矩形表示脸部
                face_left, face_top = img_width // 4, img_height // 4
                face_right, face_bottom = face_left + img_width // 2, face_top + img_height // 2
                cv2.rectangle(color_image, (face_left, face_top), (face_right, face_bottom), (245, 230, 200), -1)
                
                # 画眼睛
                eye_size = 30
                left_eye_center = (face_left + img_width // 8, face_top + img_height // 8)
                right_eye_center = (face_right - img_width // 8, face_top + img_height // 8)
                cv2.circle(color_image, left_eye_center, eye_size, (255, 255, 255), -1)
                cv2.circle(color_image, right_eye_center, eye_size, (255, 255, 255), -1)
                cv2.circle(color_image, left_eye_center, eye_size // 2, (0, 0, 0), -1)
                cv2.circle(color_image, right_eye_center, eye_size // 2, (0, 0, 0), -1)
                
                # 画嘴巴
                mouth_width, mouth_height = img_width // 4, img_height // 8
                mouth_left = face_left + img_width // 4
                mouth_top = face_top + img_height // 2
                cv2.ellipse(color_image, 
                           (mouth_left + mouth_width // 2, mouth_top),
                           (mouth_width // 2, mouth_height // 2),
                           0, 0, 180, (150, 75, 75), -1)
                
                # 保存为临时文件
                temp_dir = tempfile.gettempdir()  # 重新获取临时目录变量
                temp_img_path = os.path.join(temp_dir, f"temp_face_{uuid.uuid4()}.jpg")
                logger.info(f"保存临时图像到: {temp_img_path}")
                cv2.imwrite(temp_img_path, color_image)
                
                if not os.path.exists(temp_img_path):
                    logger.error(f"临时图像创建失败，文件不存在: {temp_img_path}")
                    raise FileNotFoundError(f"临时图像创建失败: {temp_img_path}")
                
                temp_img_size = os.path.getsize(temp_img_path)
                logger.info(f"临时图像已创建，大小: {temp_img_size} 字节")
                
                # 使用ffmpeg生成视频
                # 如果有音频文件，将其复制到视频中
                ffmpeg_cmd = [
                    "ffmpeg",
                    "-y",  # 覆盖输出文件
                    "-loop", "1",  # 循环输入图像
                    "-framerate", "30",  # 帧率
                    "-i", temp_img_path,  # 输入图像
                    "-i", audio_path,  # 输入音频
                    "-c:v", "libx264",  # 视频编码
                    "-profile:v", "baseline",  # 使用基准配置，兼容性最好
                    "-level", "3.0",  # 设置较低的编码级别增加兼容性
                    "-preset", "medium",  # 平衡编码速度和质量
                    "-tune", "stillimage",  # 针对静态图像优化
                    "-movflags", "+faststart",  # 优化网络流媒体播放
                    "-c:a", "aac",  # 音频编码
                    "-b:a", "192k",  # 音频比特率
                    "-pix_fmt", "yuv420p",  # 像素格式
                    "-shortest",  # 使用最短输入长度
                    video_path  # 输出路径
                ]
                
                # 执行ffmpeg命令
                cmd_str = ' '.join(ffmpeg_cmd)
                logger.info(f"执行ffmpeg命令: {cmd_str}")
                
                process = subprocess.run(
                    ffmpeg_cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE
                )
                
                # 检查是否成功
                if process.returncode == 0:
                    logger.info(f"ffmpeg执行成功，生成视频: {video_path}")
                    # 验证输出文件
                    if os.path.exists(video_path):
                        video_size = os.path.getsize(video_path)
                        logger.info(f"生成的视频文件大小: {video_size} 字节")
                        if video_size < 1000:
                            logger.warning(f"生成的视频文件异常小: {video_size} 字节")
                    else:
                        logger.error(f"ffmpeg执行成功但视频文件不存在: {video_path}")
                        raise FileNotFoundError(f"生成的视频文件不存在: {video_path}")
                else:
                    error_output = process.stderr.decode()
                    logger.error(f"ffmpeg执行失败，返回代码: {process.returncode}")
                    logger.error(f"ffmpeg错误输出: {error_output}")
                    raise Exception(f"ffmpeg执行失败: {error_output}")
                
                # 清理临时文件
                if os.path.exists(temp_img_path):
                    logger.info(f"清理临时图像文件: {temp_img_path}")
                    os.remove(temp_img_path)
                    logger.info("临时图像文件已清理")
                
            except Exception as e:
                logger.error(f"使用ffmpeg创建模拟视频失败: {str(e)}")
                logger.exception("创建模拟视频详细错误")
                
                # 备用方法：使用仅包含头部的MP4文件
                logger.info("尝试创建简单的MP4头文件作为备用...")
                try:
                    with open(video_path, "wb") as f:
                        # MP4文件头 + 一些模拟数据
                        f.write(bytes.fromhex('00 00 00 18 66 74 79 70 6D 70 34 32 00 00 00 00 6D 70 34 32 69 73 6F 6D 00 00 00 08 6D 6F 6F 76'))
                        # 添加一些额外数据，使其更像有效文件
                        f.write(b'\x00' * 1024)
                    
                    # 验证文件
                    if os.path.exists(video_path):
                        video_size = os.path.getsize(video_path)
                        logger.info(f"使用MP4头数据创建的模拟视频大小: {video_size} 字节")
                    
                    logger.info(f"使用MP4头数据创建模拟视频成功: {video_path}")
                except Exception as e2:
                    logger.error(f"创建模拟MP4文件也失败: {str(e2)}")
                    logger.exception("创建模拟MP4详细错误")
                    
                    # 最终备用方法
                    logger.info("尝试创建简单的模拟文件作为最终备用...")
                    try:
                        with open(video_path, "wb") as f:
                            f.write(b"MOCK VIDEO FILE - This is not a valid video file but a placeholder")
                        logger.warning(f"创建简单模拟文件成功: {video_path}，但这不是有效的视频文件")
                    except Exception as e3:
                        logger.critical(f"创建简单模拟文件也失败: {str(e3)}")
                        logger.exception("简单模拟文件创建详细错误")
                        raise RuntimeError(f"无法创建任何形式的视频文件: {str(e3)}")
            
            # 创建服务可访问的URL路径
            url_path = f"/static/videos/{video_filename}"
            logger.info(f"视频URL路径: {url_path}")
            
            # 最终验证
            if not os.path.exists(video_path):
                logger.critical(f"最终视频文件不存在: {video_path}")
                raise FileNotFoundError(f"视频生成失败，文件不存在: {video_path}")
            
            video_size = os.path.getsize(video_path)
            logger.info(f"最终视频文件大小: {video_size} 字节")
            
            if video_size < 1000:
                logger.warning(f"最终视频文件异常小: {video_size} 字节，可能无法正常播放")
            
            logger.info(f"视频生成完成，路径: {video_path}, URL: {url_path}")
            
            return {
                "path": video_path,
                "url": url_path,
                "duration": 5.0,  # 模拟视频时长
                "size": video_size,  # 添加文件大小信息
                "model": "SadTalker_Mock",  # 标记为模拟SadTalker
                "generation_type": "mock",
                "metadata": {
                    "generator": "ffmpeg_fallback",
                    "timestamp": datetime.datetime.now().isoformat(),
                    "source_image": os.path.basename(source_image),
                    "audio": os.path.basename(audio_path),
                    "transcoded": True,  # 标记视频已经过转码处理
                    "gender": gender  # 记录性别参数
                }
            }
            
        except Exception as e:
            logger.error(f"生成视频失败: {str(e)}")
            logger.exception("生成视频详细错误")
            raise 

    def get_status(self) -> dict:
        """获取SadTalker状态信息"""
        status = {
            "initialized": self.is_initialized,
            "use_real_model": self.use_real_model,
            "sadtalker_path": self.sadtalker_path,
            "output_dir": self.output_dir,
            "audio_dir": self.audio_dir,
            "has_tts_service": self.tts_service is not None,
            "device": "cuda" if torch.cuda.is_available() else "cpu"
        }
        
        # 如果有SadTalker路径，添加模型文件信息
        if self.sadtalker_path:
            checkpoints_dir = os.path.join(self.sadtalker_path, "checkpoints")
            if os.path.exists(checkpoints_dir):
                model_files = []
                for f in os.listdir(checkpoints_dir):
                    if os.path.isfile(os.path.join(checkpoints_dir, f)) and f not in ["__init__.py", "__pycache__"]:
                        file_path = os.path.join(checkpoints_dir, f)
                        model_files.append({
                            "name": f,
                            "size": os.path.getsize(file_path),
                            "path": file_path
                        })
                status["model_files"] = model_files
            
            # 添加SadTalker版本信息
            try:
                version_file = os.path.join(self.sadtalker_path, "VERSION")
                if os.path.exists(version_file):
                    with open(version_file, "r") as f:
                        status["version"] = f.read().strip()
                else:
                    # 从README或其他文件推断版本
                    readme_file = os.path.join(self.sadtalker_path, "README.md")
                    if os.path.exists(readme_file):
                        with open(readme_file, "r", encoding="utf-8", errors="ignore") as f:
                            content = f.read()
                            # 尝试从README中查找版本信息
                            import re
                            version_match = re.search(r"v(\d+\.\d+\.\d+)", content)
                            if version_match:
                                status["version"] = version_match.group(0)
                            else:
                                status["version"] = "Unknown"
                    else:
                        status["version"] = "Unknown"
            except Exception as e:
                status["version_error"] = str(e)
                status["version"] = "Error"
                
        return status 

    def _verify_video_file(self, video_path: str) -> bool:
        """
        验证视频文件是否有效
        
        Args:
            video_path: 视频文件路径
            
        Returns:
            bool: 文件是否有效
        """
        if not os.path.exists(video_path):
            logger.error(f"要验证的视频文件不存在: {video_path}")
            return False
            
        try:
            # 使用ffprobe检查视频文件
            ffprobe_cmd = [
                "ffprobe",
                "-v", "error",
                "-show_format",
                "-show_streams",
                "-print_format", "json",
                video_path
            ]
            
            logger.info(f"执行ffprobe验证视频: {' '.join(ffprobe_cmd)}")
            
            process = subprocess.run(
                ffprobe_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            if process.returncode == 0:
                # 解析JSON输出
                try:
                    result = json.loads(process.stdout)
                    if "format" in result and "streams" in result:
                        logger.info(f"视频文件有效，格式: {result.get('format', {}).get('format_name', '未知')}")
                        return True
                    else:
                        logger.warning(f"视频文件结构可能不完整: {process.stdout}")
                        return False
                except json.JSONDecodeError:
                    logger.warning(f"无法解析ffprobe输出: {process.stdout}")
                    return False
            else:
                logger.error(f"ffprobe验证失败: {process.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"验证视频过程中出错: {str(e)}")
            return False 

    def _verify_sadtalker_files(self) -> bool:
        """
        验证SadTalker所需的模型文件是否存在
        
        Returns:
            bool: 文件是否验证通过
        """
        if not self.sadtalker_path:
            logger.error("SadTalker路径未设置，无法验证模型文件")
            return False
            
        # 检查核心文件
        inference_script = os.path.join(self.sadtalker_path, "inference.py")
        if not os.path.exists(inference_script):
            logger.error(f"SadTalker核心文件不存在: {inference_script}")
            return False
            
        # 检查checkpoints目录
        checkpoints_dir = os.path.join(self.sadtalker_path, "checkpoints")
        if not os.path.exists(checkpoints_dir):
            logger.warning(f"SadTalker检查点目录不存在: {checkpoints_dir}，尝试创建...")
            try:
                os.makedirs(checkpoints_dir, exist_ok=True)
                logger.info(f"成功创建SadTalker检查点目录: {checkpoints_dir}")
            except Exception as e:
                logger.error(f"创建检查点目录失败: {str(e)}")
                return False
            
        # 检查必要的模型文件
        model_files_info = {
            "epoch_20.pth": {
                "required": True,
                "url": "https://github.com/OpenTalker/SadTalker/releases/download/v0.0.2/epoch_20.pth",
                "desc": "人脸重建模型"
            },
            "shape_predictor_68_face_landmarks.dat": {
                "required": True,
                "url": "https://github.com/davisking/dlib-models/raw/master/shape_predictor_68_face_landmarks.dat.bz2",
                "desc": "人脸特征点检测模型"
            },
            "mapping_00109-model.pth.tar": {
                "required": True,
                "url": "https://github.com/OpenTalker/SadTalker/releases/download/v0.0.2/mapping_00109-model.pth.tar",
                "desc": "映射模型1"
            }, 
            "mapping_00229-model.pth.tar": {
                "required": True,
                "url": "https://github.com/OpenTalker/SadTalker/releases/download/v0.0.2/mapping_00229-model.pth.tar",
                "desc": "映射模型2"
            },
            "SadTalker_V0.0.2_256.safetensors": {
                "required": True,
                "url": "https://github.com/OpenTalker/SadTalker/releases/download/v0.0.2/SadTalker_V0.0.2_256.safetensors",
                "desc": "主SadTalker模型"
            }
        }
        
        missing_models = []
        for model_file, info in model_files_info.items():
            model_path = os.path.join(checkpoints_dir, model_file)
            if not os.path.exists(model_path):
                if info["required"]:
                    missing_models.append({
                        "name": model_file,
                        "path": model_path,
                        "url": info["url"],
                        "desc": info["desc"]
                    })
                    logger.warning(f"缺少必要的SadTalker模型文件: {model_file} ({info['desc']})")
                else:
                    logger.info(f"可选的SadTalker模型文件不存在: {model_file} ({info['desc']})")
                
        if missing_models:
            logger.error(f"缺少 {len(missing_models)} 个必要的SadTalker模型文件")
            logger.error("请手动下载以下文件并放置在checkpoints目录中，或使用download_sadtalker_models方法下载：")
            for model in missing_models:
                logger.error(f"- {model['name']}: {model['url']} -> {model['path']}")
            
            # 尝试自动下载缺失的模型文件
            should_download = False
            try:
                # 在生产环境中，这里可能需要进一步的用户确认或配置选项
                should_download = os.environ.get("AUTO_DOWNLOAD_SADTALKER_MODELS", "0") == "1"
            except:
                pass
                
            if should_download:
                logger.info("尝试自动下载缺失的SadTalker模型文件...")
                success = self._download_sadtalker_models(missing_models)
                if success:
                    logger.info("所有模型文件下载成功")
                    return True
                else:
                    logger.error("自动下载模型文件失败")
                    return False
            else:
                logger.info("未启用自动下载，跳过模型文件下载")
                return False
                
        logger.info("SadTalker模型文件验证通过")
        return True

    def _download_sadtalker_models(self, missing_models: list) -> bool:
        """
        下载缺失的SadTalker模型文件
        
        Args:
            missing_models: 缺失的模型文件列表，每个元素为包含name, path, url的字典
            
        Returns:
            bool: 是否所有文件都下载成功
        """
        try:
            import requests
            from tqdm import tqdm
        except ImportError:
            logger.error("下载模型文件需要requests和tqdm库，请先安装: pip install requests tqdm")
            return False
            
        success_count = 0
        
        for model in missing_models:
            model_name = model["name"]
            model_path = model["path"]
            model_url = model["url"]
            
            try:
                logger.info(f"开始下载 {model_name} 从 {model_url}")
                
                # 处理特殊情况：.dat.bz2文件需要解压
                is_bz2 = model_url.endswith(".bz2")
                download_path = model_path if not is_bz2 else model_path + ".bz2"
                
                # 创建目录
                os.makedirs(os.path.dirname(download_path), exist_ok=True)
                
                # 发送HEAD请求获取文件大小
                response = requests.head(model_url, allow_redirects=True)
                file_size = int(response.headers.get('content-length', 0))
                
                # 下载文件
                response = requests.get(model_url, stream=True)
                response.raise_for_status()
                
                # 使用tqdm显示进度条
                with open(download_path, 'wb') as f, tqdm(
                    desc=model_name,
                    total=file_size,
                    unit='B',
                    unit_scale=True,
                    unit_divisor=1024,
                ) as bar:
                    for data in response.iter_content(chunk_size=1024):
                        size = f.write(data)
                        bar.update(size)
                
                # 如果是.bz2文件，需要解压
                if is_bz2:
                    logger.info(f"解压 {download_path} 到 {model_path}")
                    import bz2
                    with open(model_path, 'wb') as new_file, bz2.BZ2File(download_path, 'rb') as file:
                        for data in iter(lambda: file.read(100 * 1024), b''):
                            new_file.write(data)
                    # 删除原始压缩文件
                    os.remove(download_path)
                
                # 验证文件
                if os.path.exists(model_path):
                    file_size = os.path.getsize(model_path)
                    if file_size > 0:
                        logger.info(f"成功下载 {model_name}，文件大小: {file_size} 字节")
                        success_count += 1
                    else:
                        logger.error(f"下载的文件 {model_name} 大小为零")
                else:
                    logger.error(f"下载的文件 {model_name} 不存在")
                    
            except Exception as e:
                logger.error(f"下载 {model_name} 时出错: {str(e)}")
                logger.exception(f"下载模型详细错误")
                
        # 返回是否所有文件都下载成功
        return success_count == len(missing_models) 

    def _get_sadtalker_version(self) -> str:
        """
        获取SadTalker版本
        
        Returns:
            str: SadTalker版本号，如果无法获取则返回None
        """
        if not self.sadtalker_path:
            return None
            
        # 尝试从VERSION文件获取
        version_file = os.path.join(self.sadtalker_path, "VERSION")
        if os.path.exists(version_file):
            try:
                with open(version_file, "r") as f:
                    return f.read().strip()
            except:
                pass
                
        # 尝试从README获取
        readme_file = os.path.join(self.sadtalker_path, "README.md")
        if os.path.exists(readme_file):
            try:
                with open(readme_file, "r", encoding="utf-8", errors="ignore") as f:
                    content = f.read()
                    import re
                    version_match = re.search(r"v(\d+\.\d+\.\d+)", content)
                    if version_match:
                        return version_match.group(0)
            except:
                pass
                
        return None

    def download_models(self) -> bool:
        """
        下载所有缺失的SadTalker模型文件
        
        Returns:
            bool: 是否成功下载所有缺失的模型文件
        """
        if not self.sadtalker_path:
            logger.error("SadTalker路径未设置，无法下载模型文件")
            return False
            
        # 检查checkpoints目录
        checkpoints_dir = os.path.join(self.sadtalker_path, "checkpoints")
        if not os.path.exists(checkpoints_dir):
            logger.info(f"创建SadTalker检查点目录: {checkpoints_dir}")
            try:
                os.makedirs(checkpoints_dir, exist_ok=True)
            except Exception as e:
                logger.error(f"创建检查点目录失败: {str(e)}")
                return False
                
        # 获取模型文件信息
        model_files_info = {
            "epoch_20.pth": {
                "required": True,
                "url": "https://github.com/OpenTalker/SadTalker/releases/download/v0.0.2/epoch_20.pth",
                "desc": "人脸重建模型"
            },
            "shape_predictor_68_face_landmarks.dat": {
                "required": True,
                "url": "https://github.com/davisking/dlib-models/raw/master/shape_predictor_68_face_landmarks.dat.bz2",
                "desc": "人脸特征点检测模型"
            },
            "mapping_00109-model.pth.tar": {
                "required": True,
                "url": "https://github.com/OpenTalker/SadTalker/releases/download/v0.0.2/mapping_00109-model.pth.tar",
                "desc": "映射模型1"
            }, 
            "mapping_00229-model.pth.tar": {
                "required": True,
                "url": "https://github.com/OpenTalker/SadTalker/releases/download/v0.0.2/mapping_00229-model.pth.tar",
                "desc": "映射模型2"
            },
            "SadTalker_V0.0.2_256.safetensors": {
                "required": True,
                "url": "https://github.com/OpenTalker/SadTalker/releases/download/v0.0.2/SadTalker_V0.0.2_256.safetensors",
                "desc": "主SadTalker模型"
            }
        }
        
        # 检查缺失的模型文件
        missing_models = []
        for model_file, info in model_files_info.items():
            model_path = os.path.join(checkpoints_dir, model_file)
            if not os.path.exists(model_path):
                missing_models.append({
                    "name": model_file,
                    "path": model_path,
                    "url": info["url"],
                    "desc": info["desc"]
                })
                
        if not missing_models:
            logger.info("所有SadTalker模型文件都已存在")
            return True
            
        logger.info(f"发现 {len(missing_models)} 个缺失的SadTalker模型文件")
        for model in missing_models:
            logger.info(f"将下载: {model['name']} ({model['desc']})")
            
        # 下载缺失的模型文件
        return self._download_sadtalker_models(missing_models) 