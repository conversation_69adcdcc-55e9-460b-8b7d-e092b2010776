from sqlalchemy import <PERSON><PERSON><PERSON>, Column, Integer, String, DateTime, ForeignKey
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from utils.db import Base

class Team(Base):
    """Team database model"""
    __tablename__ = "teams"
    __table_args__ = {'extend_existing': True}  # 允许表被多次定义，解决SQLAlchemy映射冲突

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, comment="团队名称")
    description = Column(String, nullable=True, comment="团队描述")
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 关联关系
    corpora = relationship("Corpus", back_populates="team") 