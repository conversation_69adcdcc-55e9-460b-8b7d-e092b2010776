#!/usr/bin/env python3
"""
直接测试 Celery 任务
"""
import os
import sys
import uuid
from pathlib import Path
from dotenv import load_dotenv

# 添加后端路径
backend_dir = Path(__file__).parent.parent
sys.path.append(str(backend_dir))

# 加载环境变量
env_path = backend_dir / ".env"
load_dotenv(env_path)

def test_celery_task_import():
    """测试 Celery 任务导入"""
    print("🔍 测试 Celery 任务导入...")
    
    try:
        from app.tasks.wanx_video_tasks import wanx_text_to_video
        print("✅ Wanx 任务导入成功")
        return wanx_text_to_video
    except Exception as e:
        print(f"❌ Wanx 任务导入失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_direct_task_call(task_func):
    """直接测试任务调用"""
    print("\n🧪 直接测试任务调用...")
    
    try:
        task_id = str(uuid.uuid4())
        
        print(f"📝 任务ID: {task_id}")
        print("⏳ 启动 Celery 任务...")
        
        # 直接调用任务
        result = task_func.delay(
            task_id=task_id,
            prompt="测试：一只可爱的小猫",
            model="t2v-1.3B",
            duration=3,
            resolution="512x512",
            fps=24,
            guidance_scale=7.5,
            num_inference_steps=20,
            user_id="test-user"
        )
        
        print(f"✅ 任务已提交")
        print(f"📊 Celery 任务ID: {result.id}")
        print(f"📊 任务状态: {result.status}")
        
        return task_id, result
        
    except Exception as e:
        print(f"❌ 任务调用失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None

def check_task_in_db(task_id):
    """检查任务是否在数据库中"""
    print(f"\n🔍 检查任务是否在数据库中: {task_id}")
    
    try:
        import psycopg2
        import psycopg2.extras
        
        conn = psycopg2.connect(os.getenv("DATABASE_URL"))
        cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
        
        cursor.execute("SELECT * FROM wanx_video_tasks WHERE task_id = %s", [task_id])
        result = cursor.fetchone()
        
        if result:
            print("✅ 任务在数据库中找到")
            print(f"   状态: {result['status']}")
            print(f"   进度: {result['progress']}%")
            print(f"   消息: {result['message']}")
        else:
            print("❌ 任务在数据库中未找到")
        
        cursor.close()
        conn.close()
        
        return result is not None
        
    except Exception as e:
        print(f"❌ 数据库查询失败: {e}")
        return False

def main():
    """主函数"""
    print("🧪 Celery 任务直接测试")
    print("=" * 50)
    
    # 测试任务导入
    task_func = test_celery_task_import()
    if not task_func:
        return
    
    # 测试任务调用
    task_id, celery_result = test_direct_task_call(task_func)
    if not task_id:
        return
    
    # 等待一下让任务有时间执行
    print("\n⏳ 等待 5 秒让任务执行...")
    import time
    time.sleep(5)
    
    # 检查数据库
    found_in_db = check_task_in_db(task_id)
    
    # 总结
    print(f"\n📊 测试总结:")
    print(f"✅ 任务导入: 成功")
    print(f"✅ 任务提交: 成功")
    print(f"{'✅' if found_in_db else '❌'} 数据库记录: {'找到' if found_in_db else '未找到'}")
    
    if not found_in_db:
        print(f"\n💡 可能的问题:")
        print(f"   1. Celery Worker 没有处理任务")
        print(f"   2. 任务执行时出现错误")
        print(f"   3. 数据库连接问题")
        print(f"   4. 任务代码中的错误")

if __name__ == "__main__":
    main()
