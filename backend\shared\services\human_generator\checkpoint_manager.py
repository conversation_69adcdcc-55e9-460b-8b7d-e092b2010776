from typing import Dict, Any, Optional
import json
import os
import logging
from datetime import datetime
import asyncio

logger = logging.getLogger(__name__)

class CheckpointManager:
    """检查点管理器"""
    
    def __init__(self, checkpoint_dir: str = "data/checkpoints"):
        self.checkpoint_dir = checkpoint_dir
        self._checkpoints: Dict[str, Dict[str, Any]] = {}
        self._lock = asyncio.Lock()
        os.makedirs(checkpoint_dir, exist_ok=True)
        
    async def create_checkpoint(self, task_id: str, data: Dict[str, Any]) -> None:
        """创建检查点"""
        async with self._lock:
            checkpoint = {
                "task_id": task_id,
                "data": data,
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat()
            }
            
            self._checkpoints[task_id] = checkpoint
            await self._save_checkpoint(task_id, checkpoint)
            logger.info(f"Checkpoint created for task {task_id}")
            
    async def update_checkpoint(self, task_id: str, updates: Dict[str, Any]) -> None:
        """更新检查点"""
        async with self._lock:
            if task_id not in self._checkpoints:
                raise ValueError(f"Checkpoint for task {task_id} not found")
                
            checkpoint = self._checkpoints[task_id]
            checkpoint["data"].update(updates)
            checkpoint["updated_at"] = datetime.now().isoformat()
            
            await self._save_checkpoint(task_id, checkpoint)
            logger.info(f"Checkpoint updated for task {task_id}")
            
    async def get_checkpoint(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取检查点"""
        async with self._lock:
            if task_id in self._checkpoints:
                return self._checkpoints[task_id]
                
            # 尝试从文件加载
            checkpoint = await self._load_checkpoint(task_id)
            if checkpoint:
                self._checkpoints[task_id] = checkpoint
                return checkpoint
            return None
            
    async def delete_checkpoint(self, task_id: str) -> None:
        """删除检查点"""
        async with self._lock:
            if task_id in self._checkpoints:
                del self._checkpoints[task_id]
                
            checkpoint_file = os.path.join(self.checkpoint_dir, f"{task_id}.json")
            if os.path.exists(checkpoint_file):
                os.remove(checkpoint_file)
                logger.info(f"Checkpoint file deleted for task {task_id}")
                
    async def _save_checkpoint(self, task_id: str, checkpoint: Dict[str, Any]) -> None:
        """保存检查点到文件"""
        checkpoint_file = os.path.join(self.checkpoint_dir, f"{task_id}.json")
        try:
            with open(checkpoint_file, "w", encoding="utf-8") as f:
                json.dump(checkpoint, f, ensure_ascii=False, indent=2)
            logger.debug(f"Checkpoint saved to file for task {task_id}")
        except Exception as e:
            logger.error(f"Error saving checkpoint for task {task_id}: {str(e)}")
            raise
            
    async def _load_checkpoint(self, task_id: str) -> Optional[Dict[str, Any]]:
        """从文件加载检查点"""
        checkpoint_file = os.path.join(self.checkpoint_dir, f"{task_id}.json")
        if not os.path.exists(checkpoint_file):
            return None
            
        try:
            with open(checkpoint_file, "r", encoding="utf-8") as f:
                checkpoint = json.load(f)
            logger.debug(f"Checkpoint loaded from file for task {task_id}")
            return checkpoint
        except Exception as e:
            logger.error(f"Error loading checkpoint for task {task_id}: {str(e)}")
            return None
            
    def get_all_checkpoints(self) -> Dict[str, Dict[str, Any]]:
        """获取所有检查点"""
        return self._checkpoints.copy() 