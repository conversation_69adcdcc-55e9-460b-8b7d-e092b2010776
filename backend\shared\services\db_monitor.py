"""
数据库连接监控服务
"""
import logging
import threading
import time
from typing import Dict, Any, List, Optional
from datetime import datetime
import asyncio
import traceback

# 导入SQLAlchemy相关模块
from sqlalchemy.engine import Engine
from sqlalchemy import event, select
from sqlalchemy.sql import text

# 配置日志
logger = logging.getLogger(__name__)

class DBMonitor:
    """数据库连接监控类"""
    
    def __init__(self, engine=None):
        """
        初始化数据库监控
        
        参数:
            engine: SQLAlchemy引擎
        """
        self.engine = engine
        self.monitoring = False
        self.monitor_thread = None
        self.lock = threading.RLock()
        
        # 监控统计数据
        self.stats = {
            "start_time": None,
            "total_connections": 0,
            "active_connections": 0,
            "idle_connections": 0,
            "connection_timeouts": 0,
            "connection_errors": 0,
            "long_running_queries": 0,
            "query_count": 0,
            "slow_queries": 0,
            "error_queries": 0,
            "last_error": None,
            "last_error_time": None,
            "connection_history": [],
            "connection_leak_suspects": []
        }
        
        # 最近检测到的连接泄漏
        self.leak_suspects = []
        
        # 活跃连接跟踪
        self.active_connections = {}
        
        # 注册事件监听器（如果引擎可用）
        if engine:
            self.register_engine_events(engine)
    
    def register_engine_events(self, engine):
        """
        注册引擎事件监听器
        
        参数:
            engine: SQLAlchemy引擎
        """
        self.engine = engine
        
        @event.listens_for(engine, "checkout")
        def receive_checkout(dbapi_connection, connection_record, connection_proxy):
            """连接检出事件"""
            with self.lock:
                connection_id = id(dbapi_connection)
                self.active_connections[connection_id] = {
                    "checkout_time": time.time(),
                    "thread_id": id(threading.current_thread()),
                    "thread_name": threading.current_thread().name,
                    "stack_trace": traceback.format_stack(),
                    "query_count": 0,
                    "last_activity": time.time()
                }
                self.stats["active_connections"] += 1
                self.stats["total_connections"] += 1
        
        @event.listens_for(engine, "checkin")
        def receive_checkin(dbapi_connection, connection_record):
            """连接检入事件"""
            with self.lock:
                connection_id = id(dbapi_connection)
                if connection_id in self.active_connections:
                    # 检查是否长时间持有连接
                    connection_data = self.active_connections[connection_id]
                    checkout_time = connection_data.get("checkout_time", 0)
                    hold_time = time.time() - checkout_time
                    
                    # 如果持有时间超过30秒且查询次数少，可能是连接泄漏
                    if hold_time > 30 and connection_data.get("query_count", 0) < 3:
                        leak_info = {
                            "connection_id": connection_id,
                            "thread_id": connection_data.get("thread_id"),
                            "thread_name": connection_data.get("thread_name"),
                            "checkout_time": datetime.fromtimestamp(checkout_time).isoformat(),
                            "hold_time": hold_time,
                            "query_count": connection_data.get("query_count", 0),
                            "stack_trace": connection_data.get("stack_trace", [])
                        }
                        self.stats["connection_leak_suspects"].append(leak_info)
                        
                        # 保持最近100条记录
                        if len(self.stats["connection_leak_suspects"]) > 100:
                            self.stats["connection_leak_suspects"] = self.stats["connection_leak_suspects"][-100:]
                    
                    # 删除连接记录
                    del self.active_connections[connection_id]
                
                self.stats["active_connections"] -= 1
                self.stats["idle_connections"] += 1
        
        @event.listens_for(engine, "before_cursor_execute")
        def before_cursor_execute(conn, cursor, statement, parameters, context, executemany):
            """执行查询前事件"""
            conn.info.setdefault('query_start_time', time.time())
            
            # 更新连接活动时间和查询计数
            with self.lock:
                connection_id = id(conn.connection.connection)
                if connection_id in self.active_connections:
                    self.active_connections[connection_id]["last_activity"] = time.time()
                    self.active_connections[connection_id]["query_count"] += 1
        
        @event.listens_for(engine, "after_cursor_execute")
        def after_cursor_execute(conn, cursor, statement, parameters, context, executemany):
            """执行查询后事件"""
            total_time = time.time() - conn.info.get('query_start_time', time.time())
            
            with self.lock:
                self.stats["query_count"] += 1
                
                # 记录慢查询
                if total_time > 1.0:  # 慢查询阈值为1秒
                    self.stats["slow_queries"] += 1
                
                # 更新连接活动时间
                connection_id = id(conn.connection.connection)
                if connection_id in self.active_connections:
                    self.active_connections[connection_id]["last_activity"] = time.time()
        
        logger.info("已注册数据库引擎事件监听器")
    
    def start_monitoring(self, interval=60):
        """
        启动监控线程
        
        参数:
            interval: 监控间隔（秒）
        """
        if self.monitoring:
            logger.warning("监控已经在运行中")
            return
        
        with self.lock:
            self.monitoring = True
            self.stats["start_time"] = time.time()
            
        def monitor_func():
            logger.info(f"数据库连接监控线程已启动，间隔: {interval}秒")
            
            while self.monitoring:
                try:
                    self.check_connections()
                    time.sleep(interval)
                except Exception as e:
                    logger.exception(f"监控线程异常: {e}")
                    time.sleep(interval)
        
        self.monitor_thread = threading.Thread(target=monitor_func, daemon=True)
        self.monitor_thread.start()
        logger.info("数据库连接监控已启动")
    
    def stop_monitoring(self):
        """停止监控线程"""
        with self.lock:
            self.monitoring = False
        
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
            self.monitor_thread = None
            
        logger.info("数据库连接监控已停止")
    
    def check_connections(self):
        """检查连接状态"""
        current_time = time.time()
        
        with self.lock:
            # 检查长时间未活动的连接
            for conn_id, conn_data in list(self.active_connections.items()):
                last_activity = conn_data.get("last_activity", 0)
                inactive_time = current_time - last_activity
                
                # 如果连接超过5分钟未活动，记录为可能的泄漏
                if inactive_time > 300:  # 5分钟
                    leak_info = {
                        "connection_id": conn_id,
                        "thread_id": conn_data.get("thread_id"),
                        "thread_name": conn_data.get("thread_name"),
                        "checkout_time": datetime.fromtimestamp(conn_data.get("checkout_time", 0)).isoformat(),
                        "last_activity": datetime.fromtimestamp(last_activity).isoformat(),
                        "inactive_time": inactive_time,
                        "query_count": conn_data.get("query_count", 0),
                        "stack_trace": conn_data.get("stack_trace", [])
                    }
                    
                    self.leak_suspects.append(leak_info)
                    logger.warning(f"检测到可能的连接泄漏: 连接ID {conn_id}, 线程 {conn_data.get('thread_name')}, 不活动时间 {inactive_time:.1f}秒")
            
            # 保持最近50条泄漏记录
            if len(self.leak_suspects) > 50:
                self.leak_suspects = self.leak_suspects[-50:]
    
    def get_stats(self):
        """获取监控统计数据"""
        with self.lock:
            # 计算运行时间
            if self.stats["start_time"]:
                runtime = time.time() - self.stats["start_time"]
            else:
                runtime = 0
                
            # 复制统计数据
            stats_copy = {**self.stats}
            stats_copy["runtime"] = runtime
            stats_copy["active_connection_count"] = len(self.active_connections)
            stats_copy["leak_suspect_count"] = len(self.leak_suspects)
            stats_copy["recent_leak_suspects"] = self.leak_suspects[-5:] if self.leak_suspects else []
            
            # 添加当前活跃连接信息
            active_connections_info = []
            for conn_id, conn_data in self.active_connections.items():
                checkout_time = conn_data.get("checkout_time", 0)
                hold_time = time.time() - checkout_time
                
                conn_info = {
                    "connection_id": conn_id,
                    "thread_name": conn_data.get("thread_name"),
                    "hold_time": hold_time,
                    "query_count": conn_data.get("query_count", 0),
                    "last_activity": time.time() - conn_data.get("last_activity", time.time())
                }
                active_connections_info.append(conn_info)
            
            stats_copy["active_connections_info"] = active_connections_info
            
            return stats_copy
    
    def get_connection_leaks(self):
        """获取连接泄漏信息"""
        with self.lock:
            return list(self.leak_suspects)
    
    def reset_stats(self):
        """重置统计数据"""
        with self.lock:
            self.stats = {
                "start_time": time.time() if self.monitoring else None,
                "total_connections": 0,
                "active_connections": len(self.active_connections),
                "idle_connections": 0,
                "connection_timeouts": 0,
                "connection_errors": 0,
                "long_running_queries": 0,
                "query_count": 0,
                "slow_queries": 0,
                "error_queries": 0,
                "last_error": None,
                "last_error_time": None,
                "connection_history": [],
                "connection_leak_suspects": []
            }
            self.leak_suspects = []
            logger.info("监控统计数据已重置")
    
    def detect_and_fix_leaks(self):
        """检测并修复连接泄漏"""
        # 注意：这个方法仅记录泄漏，实际修复需要应用层面的改进
        leak_count = 0
        
        with self.lock:
            current_time = time.time()
            
            for conn_id, conn_data in list(self.active_connections.items()):
                checkout_time = conn_data.get("checkout_time", 0)
                hold_time = current_time - checkout_time
                last_activity = conn_data.get("last_activity", 0)
                inactive_time = current_time - last_activity
                
                # 检测长时间持有但不活动的连接
                if hold_time > 600 and inactive_time > 300:  # 10分钟持有，5分钟不活动
                    leak_count += 1
                    logger.warning(
                        f"严重连接泄漏: 连接ID {conn_id}, "
                        f"线程 {conn_data.get('thread_name')}, "
                        f"持有时间 {hold_time:.1f}秒, "
                        f"不活动时间 {inactive_time:.1f}秒"
                    )
        
        return {
            "detected_leaks": leak_count,
            "timestamp": datetime.now().isoformat()
        }

# 单例实例
_db_monitor = None

def get_db_monitor():
    """获取数据库监控实例"""
    global _db_monitor
    if _db_monitor is None:
        # 导入引擎
        from utils.db import engine
        _db_monitor = DBMonitor(engine)
    return _db_monitor

def start_db_monitoring(interval=60):
    """启动数据库监控"""
    monitor = get_db_monitor()
    monitor.start_monitoring(interval)

def get_db_stats():
    """获取数据库统计信息"""
    monitor = get_db_monitor()
    return monitor.get_stats() 