"""
模型管理API路由
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks
from typing import List, Optional, Dict, Any
import os
import json
import asyncio
from pathlib import Path
from datetime import datetime
import subprocess
import sys

router = APIRouter()

# 模型存储路径
MODEL_STORAGE_PATH = Path("storage/models")
MODEL_STORAGE_PATH.mkdir(parents=True, exist_ok=True)

def load_migrated_models():
    """加载已迁移的模型信息"""
    migrated_models = {}
    digital_human_path = MODEL_STORAGE_PATH / "digital_human"

    if digital_human_path.exists():
        for model_dir in digital_human_path.iterdir():
            if model_dir.is_dir():
                info_file = model_dir / "model_info.json"
                if info_file.exists():
                    try:
                        with open(info_file, 'r', encoding='utf-8') as f:
                            model_info = json.load(f)
                            model_id = model_dir.name
                            migrated_models[model_id] = {
                                "id": model_id,
                                "name": model_info["name"],
                                "type": model_info["type"],
                                "category": model_info["type"],
                                "version": model_info["version"],
                                "description": model_info["description"],
                                "size": model_info["size"],
                                "gpu_memory_required": model_info["gpu_memory_required"],
                                "dependencies": model_info["dependencies"],
                                "status": "downloaded",  # 已下载状态
                                "required": False,
                                "local_path": str(model_dir),
                                "main_script": model_info.get("main_script"),
                                "inference_script": model_info.get("inference_script")
                            }
                    except Exception as e:
                        print(f"加载模型信息失败 {model_dir}: {e}")

    return migrated_models

# 加载已迁移的模型
MIGRATED_MODELS = load_migrated_models()

# AI模型配置
AI_MODELS = {
    "edge-tts": {
        "id": "edge-tts",
        "name": "Edge TTS",
        "type": "tts",
        "category": "tts",
        "version": "1.0.0",
        "description": "Microsoft Edge 文本转语音模型",
        "size": 50 * 1024 * 1024,
        "gpu_memory_required": 0,
        "dependencies": ["edge-tts"],
        "install_command": "pip install edge-tts",
        "status": "available",
        "required": True
    },
    "elevenlabs": {
        "id": "elevenlabs",
        "name": "ElevenLabs TTS",
        "type": "tts", 
        "category": "tts",
        "version": "2.1.0",
        "description": "高质量AI语音合成模型",
        "size": 200 * 1024 * 1024,
        "gpu_memory_required": 2,
        "dependencies": ["elevenlabs"],
        "install_command": "pip install elevenlabs",
        "status": "available",
        "required": False
    },
    "musetalk": {
        "id": "musetalk",
        "name": "MuseTalk",
        "type": "digital-human",
        "category": "digital-human", 
        "version": "1.5.0",
        "description": "腾讯开源的实时唇同步数字人模型",
        "size": 1.2 * 1024 * 1024 * 1024,
        "gpu_memory_required": 6,
        "dependencies": ["torch", "torchaudio", "librosa", "opencv-python"],
        "download_url": "https://github.com/TMElyralab/MuseTalk",
        "status": "available",
        "required": True
    },
    "sadtalker": {
        "id": "sadtalker", 
        "name": "SadTalker",
        "type": "digital-human",
        "category": "digital-human",
        "version": "1.0.0",
        "description": "数字人面部动画生成模型",
        "size": 800 * 1024 * 1024,
        "gpu_memory_required": 8,
        "dependencies": ["torch", "torchvision", "opencv-python"],
        "download_url": "https://github.com/OpenTalker/SadTalker",
        "status": "available",
        "required": False
    },
    "liveportrait": {
        "id": "liveportrait",
        "name": "LivePortrait", 
        "type": "digital-human",
        "category": "digital-human",
        "version": "1.0.0",
        "description": "快手开源的高质量数字人生成模型",
        "size": 500 * 1024 * 1024,
        "gpu_memory_required": 8,
        "dependencies": ["torch", "torchvision", "opencv-python"],
        "download_url": "https://github.com/KwaiVGI/LivePortrait",
        "status": "available",
        "required": False
    },
    "gpt-3.5": {
        "id": "gpt-3.5",
        "name": "GPT-3.5 Turbo",
        "type": "ai",
        "category": "ai",
        "version": "1.0.0", 
        "description": "OpenAI GPT-3.5 对话模型",
        "size": 0,  # API模型，无需下载
        "gpu_memory_required": 0,
        "dependencies": ["openai"],
        "install_command": "pip install openai",
        "status": "configured",
        "required": False
    }
}

# 合并已迁移的模型
AI_MODELS.update(MIGRATED_MODELS)

@router.get("/status")
async def get_models_status():
    """获取模型状态统计"""
    try:
        total_models = len(AI_MODELS)
        active_models = 0
        error_models = 0
        total_size = 0
        
        for model_id, config in AI_MODELS.items():
            # 检查模型状态
            model_status = await check_model_status(model_id)
            config["status"] = model_status
            
            if model_status == "active":
                active_models += 1
            elif model_status == "error":
                error_models += 1
                
            total_size += config["size"]
        
        return {
            "success": True,
            "data": {
                "statistics": {
                    "total_models": total_models,
                    "active_models": active_models,
                    "error_models": error_models,
                    "total_size": total_size
                },
                "models": AI_MODELS
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取模型状态失败: {str(e)}")

@router.get("/list")
async def get_models_list(
    category: Optional[str] = None,
    status: Optional[str] = None,
    search: Optional[str] = None
):
    """获取模型列表"""
    try:
        models = []
        
        for model_id, config in AI_MODELS.items():
            # 检查模型状态
            model_status = await check_model_status(model_id)
            
            # 应用筛选
            if category and config.get("category") != category:
                continue
                
            if status and model_status != status:
                continue
                
            if search and search.lower() not in config["name"].lower():
                continue
            
            model_info = {
                **config,
                "status": model_status,
                "last_used": datetime.now().isoformat(),
                "installed_at": datetime.now().isoformat(),
                "usage_count": 0
            }
            models.append(model_info)
        
        return {
            "success": True,
            "data": models
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取模型列表失败: {str(e)}")

@router.post("/download/{model_id}")
async def download_model(model_id: str, background_tasks: BackgroundTasks):
    """下载指定模型"""
    if model_id not in AI_MODELS:
        raise HTTPException(status_code=404, detail="模型不存在")
    
    try:
        config = AI_MODELS[model_id]
        
        # 检查模型是否已安装
        model_status = await check_model_status(model_id)
        if model_status == "active":
            return {
                "success": True,
                "message": "模型已安装",
                "model_id": model_id
            }
        
        # 添加后台下载任务
        background_tasks.add_task(download_model_task, model_id, config)
        
        return {
            "success": True,
            "message": "开始下载模型",
            "model_id": model_id
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"下载模型失败: {str(e)}")

@router.post("/install/{model_id}")
async def install_model(model_id: str, background_tasks: BackgroundTasks):
    """安装指定模型"""
    if model_id not in AI_MODELS:
        raise HTTPException(status_code=404, detail="模型不存在")
    
    try:
        config = AI_MODELS[model_id]
        
        # 添加后台安装任务
        background_tasks.add_task(install_model_task, model_id, config)
        
        return {
            "success": True,
            "message": "开始安装模型",
            "model_id": model_id
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"安装模型失败: {str(e)}")

@router.post("/load/{model_id}")
async def load_model(model_id: str):
    """加载模型"""
    if model_id not in AI_MODELS:
        raise HTTPException(status_code=404, detail="模型不存在")
    
    try:
        # 模拟加载过程
        await asyncio.sleep(2)
        
        return {
            "success": True,
            "message": f"模型 {model_id} 加载成功"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"加载模型失败: {str(e)}")

@router.post("/unload/{model_id}")
async def unload_model(model_id: str):
    """卸载模型"""
    if model_id not in AI_MODELS:
        raise HTTPException(status_code=404, detail="模型不存在")
    
    try:
        # 模拟卸载过程
        await asyncio.sleep(1)
        
        return {
            "success": True,
            "message": f"模型 {model_id} 卸载成功"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"卸载模型失败: {str(e)}")

@router.delete("/{model_id}")
async def delete_model(model_id: str):
    """删除模型"""
    if model_id not in AI_MODELS:
        raise HTTPException(status_code=404, detail="模型不存在")
    
    try:
        config = AI_MODELS[model_id]
        model_path = MODEL_STORAGE_PATH / model_id
        
        if model_path.exists():
            import shutil
            shutil.rmtree(model_path)
        
        return {
            "success": True,
            "message": f"模型 {model_id} 删除成功"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除模型失败: {str(e)}")

@router.get("/categories")
async def get_model_categories():
    """获取模型分类"""
    categories = set()
    for config in AI_MODELS.values():
        categories.add(config.get("category", "other"))
    
    return {
        "success": True,
        "data": list(categories)
    }

async def check_model_status(model_id: str) -> str:
    """检查模型状态"""
    try:
        config = AI_MODELS[model_id]
        
        # 检查依赖是否安装
        if "dependencies" in config:
            for dep in config["dependencies"]:
                try:
                    __import__(dep.replace("-", "_"))
                except ImportError:
                    return "inactive"
        
        # 检查模型文件是否存在
        model_path = MODEL_STORAGE_PATH / model_id
        if config["type"] in ["digital-human"] and not model_path.exists():
            return "inactive"
        
        return "active"
    except Exception:
        return "error"

async def download_model_task(model_id: str, config: dict):
    """后台下载模型任务"""
    try:
        print(f"开始下载模型 {model_id}...")
        
        # 模拟下载过程
        await asyncio.sleep(5)
        
        # 创建模型目录
        model_path = MODEL_STORAGE_PATH / model_id
        model_path.mkdir(exist_ok=True)
        
        # 创建模型文件标记
        (model_path / "model.bin").touch()
        
        print(f"模型 {model_id} 下载完成")
    except Exception as e:
        print(f"下载模型 {model_id} 失败: {e}")

async def install_model_task(model_id: str, config: dict):
    """后台安装模型任务"""
    try:
        print(f"开始安装模型 {model_id}...")
        
        # 安装依赖
        if "install_command" in config:
            process = await asyncio.create_subprocess_shell(
                config["install_command"],
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            await process.communicate()
        
        print(f"模型 {model_id} 安装完成")
    except Exception as e:
        print(f"安装模型 {model_id} 失败: {e}")
