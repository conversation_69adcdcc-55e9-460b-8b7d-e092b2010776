import os
import torch
import logging
import tempfile
import uuid
import time
import numpy as np
import asyncio
import sys
import platform
from typing import Optional, Dict, Any, List, Tuple
from PIL import Image
import cv2
from dotenv import load_dotenv
from services.progress_tracker import get_progress_tracker
from services.progress_updater import ProgressUpdater
import traceback
from config.models import WAN_CONFIG  # 导入WAN配置
import json

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 获取标准的应用数据目录
def get_app_data_dir():
    """获取跨平台的应用数据目录"""
    app_name = "WanAI"
    if platform.system() == "Windows":
        # Windows: AppData/Local
        return os.path.join(os.environ.get("LOCALAPPDATA", os.path.expanduser("~/AppData/Local")), app_name)
    elif platform.system() == "Darwin":
        # macOS: ~/Library/Application Support
        return os.path.join(os.path.expanduser("~/Library/Application Support"), app_name)
    else:
        # Linux/Unix: ~/.local/share
        return os.path.join(os.path.expanduser("~/.local/share"), app_name)

def check_directory_permissions(dir_path: str) -> Tuple[bool, bool, bool, str]:
    """
    检查目录的权限状态
    
    Args:
        dir_path: 要检查的目录路径
        
    Returns:
        Tuple:
            - 目录是否存在
            - 是否有读权限
            - 是否有写权限
            - 诊断信息
    """
    exists = os.path.exists(dir_path)
    readable = False
    writable = False
    msg = ""
    
    if not exists:
        msg = f"目录不存在: {dir_path}"
        return exists, readable, writable, msg
    
    if not os.path.isdir(dir_path):
        msg = f"路径不是目录: {dir_path}"
        return exists, readable, writable, msg
    
    readable = os.access(dir_path, os.R_OK)
    writable = os.access(dir_path, os.W_OK)
    
    if readable and writable:
        msg = f"目录存在且拥有读写权限: {dir_path}"
    elif readable and not writable:
        msg = f"目录存在但只读，无写入权限: {dir_path}"
    elif not readable and writable:
        msg = f"目录存在但只写，无读取权限: {dir_path}"
    else:
        msg = f"目录存在但无读写权限: {dir_path}"
    
    return exists, readable, writable, msg

# 自动修复模型目录结构
def auto_fix_model_structure(model_dir: str) -> bool:
    """
    自动修复模型目录结构
    
    Args:
        model_dir: 模型目录路径
        
    Returns:
        bool: 是否成功修复
    """
    logger.info(f"尝试自动修复模型目录结构: {model_dir}")
    
    try:
        # 尝试导入修复脚本
        try:
            from scripts import fix_wan_model_structure
            
            # 执行修复
            if fix_wan_model_structure.fix_model_structure(model_dir):
                logger.info(f"模型目录结构自动修复成功: {model_dir}")
                return True
            else:
                logger.warning(f"模型目录结构自动修复失败: {model_dir}")
                return False
                
        except ImportError:
            logger.warning(f"未找到修复脚本，无法自动修复模型目录结构")
            # 尝试使用一键修复工具
            script_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 
                                     "scripts", "fix_wan_issue.py")
            
            if os.path.exists(script_path):
                logger.info(f"找到一键修复工具，尝试执行: {script_path}")
                
                # 执行修复脚本
                import subprocess
                result = subprocess.run([sys.executable, script_path], 
                                        capture_output=True, text=True)
                
                if result.returncode == 0:
                    logger.info(f"一键修复工具执行成功")
                    logger.info(result.stdout)
                    return True
                else:
                    logger.error(f"一键修复工具执行失败: {result.stderr}")
                    return False
            else:
                logger.warning(f"未找到一键修复工具，无法自动修复模型目录结构")
                return False
    
    except Exception as e:
        logger.error(f"尝试自动修复模型目录结构时出错: {e}")
        logger.error(traceback.format_exc())
        return False

# 查找最佳的模型缓存目录
def find_best_model_cache_dir() -> str:
    """
    查找最佳的模型缓存目录，以简化逻辑方式处理：
    1. 首先尝试使用WAN_CONFIG中定义的缓存目录
    2. 然后检查环境变量WAN_MODEL_CACHE_DIR指定的目录
    3. 检查当前目录下的models_cache目录
    4. 检查脚本所在目录下的models_cache目录
    5. 使用标准应用数据目录
    
    简化查找逻辑并提供详细的诊断信息。
    
    Returns:
        str: 最佳的模型缓存目录路径
    """
    logger.info("正在查找最佳模型缓存目录...")
    
    # 强制使用真实模式
    mock_mode = False
    
    logger.info(f"系统平台: {platform.system()} {platform.release()}")
    logger.info(f"Python版本: {sys.version}")
    logger.info(f"当前工作目录: {os.getcwd()}")
    
    # 记录搜索目录的状态，以便全面了解系统环境
    all_searched_paths = []
    
    # 0. 首先尝试使用配置中定义的路径(优先级最高)
    if hasattr(WAN_CONFIG, 'model_cache_dir') and WAN_CONFIG['model_cache_dir']:
        config_cache_dir = WAN_CONFIG['model_cache_dir']
        logger.info(f"检查配置中设置的缓存目录: {config_cache_dir}")
        
        exists, readable, writable, msg = check_directory_permissions(config_cache_dir)
        logger.info(f"检查配置设置的缓存目录 - {msg}")
        all_searched_paths.append((config_cache_dir, exists, readable, writable, "CONFIG中的model_cache_dir"))
        
        if exists and readable and writable:
            logger.info(f"✓ 使用配置指定的模型缓存目录: {config_cache_dir}")
            return config_cache_dir
        else:
            logger.warning(f"⚠ 配置指定的目录有问题: {msg}")
    
    # 1. 检查环境变量并记录情况
    env_var_value = os.environ.get("WAN_MODEL_CACHE_DIR", "")
    if env_var_value:
        logger.info(f"发现环境变量 WAN_MODEL_CACHE_DIR = {env_var_value}")
    else:
        logger.info("未设置环境变量 WAN_MODEL_CACHE_DIR")
    
    # 使用环境变量指定的目录
    if env_var_value:
        cache_dir = env_var_value
        exists, readable, writable, msg = check_directory_permissions(cache_dir)
        logger.info(f"检查环境变量设置的缓存目录 - {msg}")
        all_searched_paths.append((cache_dir, exists, readable, writable, "环境变量WAN_MODEL_CACHE_DIR"))
        
        if exists and readable and writable:
            logger.info(f"✓ 使用环境变量指定的模型缓存目录: {cache_dir}")
            return cache_dir
        else:
            logger.warning(f"⚠ 环境变量指定的目录有问题: {msg}")
    
    # 2. 检查当前目录下的models_cache
    cwd_cache = os.path.join(os.getcwd(), "models_cache")
    exists, readable, writable, msg = check_directory_permissions(cwd_cache)
    logger.info(f"检查当前工作目录下的缓存目录 - {msg}")
    all_searched_paths.append((cwd_cache, exists, readable, writable, "当前工作目录下的models_cache"))
    
    if exists and readable and writable:
        logger.info(f"✓ 使用当前工作目录下的模型缓存: {cwd_cache}")
        return cwd_cache
    
    # 3. 检查脚本所在目录下的models_cache
    script_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    script_cache = os.path.join(script_dir, "models_cache")
    exists, readable, writable, msg = check_directory_permissions(script_cache)
    logger.info(f"检查脚本目录下的缓存目录 - {msg}")
    all_searched_paths.append((script_cache, exists, readable, writable, "脚本目录下的models_cache"))
    
    if exists and readable and writable:
        logger.info(f"✓ 使用脚本目录下的模型缓存: {script_cache}")
        return script_cache
    
    # 4. 使用标准应用数据目录
    app_data = get_app_data_dir()
    exists, readable, writable, msg = check_directory_permissions(app_data)
    logger.info(f"检查标准应用数据目录 - {msg}")
    all_searched_paths.append((app_data, exists, readable, writable, "标准应用数据目录"))
    
    # 如果目录不存在，尝试创建
    if not exists:
        try:
            os.makedirs(app_data, exist_ok=True)
            logger.info(f"✓ 已创建模型缓存目录: {app_data}")
            exists, readable, writable, new_msg = check_directory_permissions(app_data)
            logger.info(f"新创建目录权限检查 - {new_msg}")
            
            if not (readable and writable):
                logger.error(f"⚠ 创建的缓存目录权限不足: {app_data}")
            else:
                return app_data
        except Exception as e:
            logger.error(f"⚠ 创建模型缓存目录失败: {str(e)}")
            logger.error(f"详细错误: {type(e).__name__}: {e}")
    
    # 如果标准应用数据目录存在且有权限，使用它
    if exists and readable and writable:
        logger.info(f"✓ 使用标准应用数据目录: {app_data}")
        return app_data
    
    # 5. 尝试临时目录作为最后的备选方案
    temp_dir = tempfile.gettempdir()
    exists, readable, writable, msg = check_directory_permissions(temp_dir)
    logger.info(f"检查系统临时目录 - {msg}")
    all_searched_paths.append((temp_dir, exists, readable, writable, "系统临时目录"))
    
    # 输出所有尝试过的路径汇总
    logger.warning("⚠ 所有预定义路径均不可用，使用临时目录作为最后手段")
    logger.info("所有尝试的目录摘要:")
    
    for path, exists, readable, writable, desc in all_searched_paths:
        status = "✓" if (exists and readable and writable) else "✗"
        permission_info = ""
        if exists:
            permission_info = f"读:{readable} 写:{writable}"
        else:
            permission_info = "不存在"
        
        logger.info(f"{status} {desc}: {path} - {permission_info}")
    
    # 如果临时目录可用，使用它
    if exists and readable and writable:
        logger.info(f"✓ 使用系统临时目录作为后备: {temp_dir}")
        return temp_dir
    
    # 6. 所有选项都失败，使用当前目录并记录警告
    logger.error("❌ 无法找到可用的模型缓存目录!")
    logger.error("将使用当前工作目录，但可能会出现权限问题")
    
    return os.getcwd()

# 全局模型路径变量
WAN_MODEL_CACHE_DIR = find_best_model_cache_dir()

# 检测并输出是否与config/models.py中的配置一致
config_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
models_py_path = os.path.join(config_dir, "config", "models.py")
if os.path.exists(models_py_path):
    logger.info(f"检测到config/models.py配置文件存在，请确保其中的model_cache_dir与当前缓存目录一致")
else:
    logger.info(f"未检测到config/models.py配置文件")

WAN_T2V_14B_MODEL = os.path.normpath(os.getenv("WAN_T2V_14B_MODEL", os.path.join(WAN_MODEL_CACHE_DIR, "Wan2.1-T2V-14B")))
WAN_T2V_1_3B_MODEL = os.path.normpath(os.getenv("WAN_T2V_1_3B_MODEL", os.path.join(WAN_MODEL_CACHE_DIR, "Wan2.1-T2V-1.3B")))
WAN_I2V_720P_MODEL = os.path.normpath(os.getenv("WAN_I2V_720P_MODEL", os.path.join(WAN_MODEL_CACHE_DIR, "Wan2.1-I2V-720P")))
WAN_I2V_480P_MODEL = os.path.normpath(os.getenv("WAN_I2V_480P_MODEL", os.path.join(WAN_MODEL_CACHE_DIR, "Wan2.1-I2V-480P")))
WAN_DEVICE = os.getenv("WAN_DEVICE", "cuda" if torch.cuda.is_available() else "cpu")
WAN_MODEL_PRECISION = os.getenv("WAN_MODEL_PRECISION", "bfloat16") # bfloat16, float16, float32
MAX_RETRY_ATTEMPTS = int(os.getenv("WAN_MAX_RETRY_ATTEMPTS", "3"))
RETRY_DELAY_BASE = float(os.getenv("WAN_RETRY_DELAY_BASE", "2.0"))

# 日志记录目录变更
logger.info(f"使用模型缓存目录: {WAN_MODEL_CACHE_DIR}")
if os.getenv("WAN_MODEL_CACHE_DIR") is None:
    logger.info(f"提示: 使用默认缓存目录。如需更改，请设置环境变量 WAN_MODEL_CACHE_DIR")

# 检查需要的模型目录结构
for model_dir in [WAN_T2V_14B_MODEL, WAN_T2V_1_3B_MODEL, WAN_I2V_720P_MODEL, WAN_I2V_480P_MODEL]:
    exists, readable, writable, msg = check_directory_permissions(model_dir)
    logger.info(f"模型目录检查: {msg}")

class WanVideoService:
    """WAN 2.1 视频生成服务"""
    
    def __init__(self, model_cache_dir: Optional[str] = None, device: Optional[str] = None, 
                 precision: Optional[str] = None, mock_mode: Optional[bool] = None):
        """
        初始化WAN 2.1视频生成服务
        
        Args:
            model_cache_dir: 可选，直接指定模型缓存目录，覆盖默认查找逻辑
            device: 可选，运行设备 (cpu/cuda)
            precision: 可选，模型精度 (float32/float16)
            mock_mode: 可选，是否使用模拟模式生成视频，而非实际AI模型
        """
        try:
            # 获取环境变量，或使用默认值
            self.max_retry_attempts = int(os.environ.get("WAN_MAX_RETRY_ATTEMPTS", "3"))
            
            # 设置模拟模式 - 允许通过参数或环境变量控制
            if mock_mode is None:
                # 如果未指定，则使用环境变量
                env_mock_mode = os.environ.get("WAN_MOCK_MODE", "false").lower()
                self.mock_mode = env_mock_mode == "true"
            else:
                # 如果明确指定，则使用指定值
                self.mock_mode = mock_mode
                
            # 强制使用真实模式 - 如果设置了此项，则忽略模拟模式设置
            force_real_mode = os.environ.get("WAN_FORCE_REAL_MODE", "false").lower() == "true"
            if force_real_mode:
                logger.info("已启用强制真实模式，将忽略模拟模式设置")
                self.mock_mode = False
                
            logger.info(f"模拟模式设置: {self.mock_mode}")
            
            # 设置设备
            if device is None:
                self.device = os.environ.get("WAN_DEVICE", "cuda")
            else:
                self.device = device
                
            logger.info(f"设备设置: {self.device}")
            
            # 设置模型精度
            if precision is None:
                self.precision = os.environ.get("WAN_MODEL_PRECISION", "bfloat16")
            else:
                self.precision = precision
                
            logger.info(f"精度设置: {self.precision}")
            
            # 设置模型缓存目录
            if model_cache_dir is None:
                self.model_cache_dir = find_best_model_cache_dir()
            else:
                self.model_cache_dir = model_cache_dir
                
            logger.info(f"模型缓存目录: {self.model_cache_dir}")
            
            # 初始化模型状态跟踪
            self.is_initialized = True
            self.models = {'t2v': {}, 'i2v': {}}
            self._init_model_status()
                
        except Exception as e:
            logger.error(f"初始化WAN视频服务时出错: {e}")
            logger.error(traceback.format_exc())
            
            # 在初始化失败时，如果不是强制真实模式，则使用模拟模式
            force_real_mode = os.environ.get("WAN_FORCE_REAL_MODE", "false").lower() == "true"
            if force_real_mode:
                logger.error("强制真实模式下初始化失败，服务将无法正常工作")
                self.mock_mode = False
            else:
                logger.warning("由于初始化错误，已启用模拟模式")
                self.mock_mode = True
                os.environ["WAN_MOCK_MODE"] = "true"
            
            # 设置最小必要的属性
            self.model_cache_dir = tempfile.gettempdir()
            self.device = "cpu"
            self.precision = "float32"
            self.max_retry_attempts = 3
            self.is_initialized = False
            self.models = {'t2v': {}, 'i2v': {}}
    
    def _ensure_video_dimensions_compatible(self, width: int, height: int) -> Tuple[int, int]:
        """
        确保视频尺寸与标准宏块大小兼容，以避免编码器警告或错误
        
        视频编码器通常要求尺寸是特定值的倍数（如2、4、8或16），
        这个方法确保宽度和高度是这些值的倍数，避免FFmpeg警告。
        
        Args:
            width: 原始宽度
            height: 原始高度
            
        Returns:
            Tuple[int, int]: 调整后的宽度和高度
        """
        # 大多数编码器要求尺寸是16的倍数，但保守起见我们使用2
        # 对于较高级的编码器，可能需要是4或8的倍数
        macroblock_size = 16
        
        # 计算调整后的尺寸，确保是宏块大小的倍数
        adjusted_width = (width + macroblock_size - 1) // macroblock_size * macroblock_size
        adjusted_height = (height + macroblock_size - 1) // macroblock_size * macroblock_size
        
        # 记录尺寸调整
        if width != adjusted_width or height != adjusted_height:
            logger.info(f"视频尺寸调整: {width}x{height} -> {adjusted_width}x{adjusted_height} (宏块大小: {macroblock_size})")
        
        return adjusted_width, adjusted_height
    
    def _init_model_status(self):
        """初始化模型状态跟踪"""
        # T2V模型状态初始化
        t2v_model_14b = os.environ.get("WAN_T2V_14B_MODEL", "Wan2.1-T2V-14B")
        t2v_model_1_3b = os.environ.get("WAN_T2V_1_3B_MODEL", "Wan2.1-T2V-1.3B")
        
        # 解析支持的分辨率
        resolutions = ["720p", "1080p"] # 默认支持的分辨率
        
        self.model_status = {
            "t2v": {
                "14B": {res: {"loaded": False, "loading": False, "error": None} for res in resolutions},
                "1.3B": {res: {"loaded": False, "loading": False, "error": None} for res in resolutions}
            },
            "i2v": {res: {"loaded": False, "loading": False, "error": None} for res in resolutions}
        }
        
        # 日志记录模型状态
        logger.info(f"初始化模型状态跟踪:")
        logger.info(f"  文本到视频 (14B): {', '.join(resolutions)}")
        logger.info(f"  文本到视频 (1.3B): {', '.join(resolutions)}")
        logger.info(f"  图像到视频: {', '.join(resolutions)}")
        
        # 检查模型目录是否存在
        t2v_14b_path = os.path.join(self.model_cache_dir, t2v_model_14b)
        t2v_1_3b_path = os.path.join(self.model_cache_dir, t2v_model_1_3b)
        
        # 记录模型路径信息
        logger.info(f"模型路径:")
        logger.info(f"  T2V 14B: {t2v_14b_path}")
        logger.info(f"  T2V 1.3B: {t2v_1_3b_path}")
        
        # 检查模型文件是否存在
        t2v_14b_exists = os.path.exists(t2v_14b_path) and os.path.isdir(t2v_14b_path)
        t2v_1_3b_exists = os.path.exists(t2v_1_3b_path) and os.path.isdir(t2v_1_3b_path)
        
        # 日志记录模型文件状态
        logger.info(f"模型目录状态:")
        logger.info(f"  T2V 14B: {'存在' if t2v_14b_exists else '不存在'}")
        logger.info(f"  T2V 1.3B: {'存在' if t2v_1_3b_exists else '不存在'}")
    
    def _get_torch_dtype(self, precision: str):
        """获取torch数据类型"""
        if precision == "float16":
            return torch.float16
        elif precision == "bfloat16" and hasattr(torch, "bfloat16"):
            return torch.bfloat16
        else:
            return torch.float32
    
    async def check_model_status(self, model_type: str, model_size: str = "14B", resolution: str = "720p") -> Dict[str, Any]:
        """
        检查模型状态，检查本地模型文件是否存在和完整。
        
        Args:
            model_type: 模型类型，'t2v'或'i2v'
            model_size: 模型大小，仅用于t2v
            resolution: 分辨率，用于i2v或t2v的分辨率参数
            
        Returns:
            Dict: 模型状态信息
        """
        logger.info(f"检查模型状态: {model_type}, 规格: {model_size}, 分辨率: {resolution}")
        
        # 更新检查时间
        if model_type == "t2v" and model_size in self.model_status["t2v"]:
            if resolution in self.model_status["t2v"][model_size]:
                self.model_status["t2v"][model_size][resolution]["last_check"] = time.time()
        elif model_type == "i2v" and resolution in self.model_status["i2v"]:
            self.model_status["i2v"][resolution]["last_check"] = time.time()
        
        # 检查模型是否已加载到内存中
        if model_type == "t2v":
            if model_type in self.models and model_size in self.models[model_type] and resolution in self.models[model_type][model_size]:
                logger.info(f"模型已加载到内存中: {model_type} {model_size} {resolution}")
                if model_size in self.model_status["t2v"] and resolution in self.model_status["t2v"][model_size]:
                    self.model_status["t2v"][model_size][resolution]["loaded"] = True
                return {"status": "loaded", "error": None}
        elif model_type == "i2v":
            if model_type in self.models and resolution in self.models[model_type]:
                logger.info(f"模型已加载到内存中: {model_type} {resolution}")
                if resolution in self.model_status["i2v"]:
                    self.model_status["i2v"][resolution]["loaded"] = True
                return {"status": "loaded", "error": None}
        
        # 选择正确的模型ID
        model_id = self._get_model_id(model_type, model_size, resolution)
        model_path = os.path.join(self.model_cache_dir, model_id)
        
        logger.info(f"检查本地模型目录: {model_path}")
        
        # 检查本地模型目录和文件
        if not os.path.exists(model_path):
            status = "not_found"
            error = f"模型目录不存在: {model_path}"
            logger.warning(error)
            
            if model_type == "t2v" and model_size in self.model_status["t2v"] and resolution in self.model_status["t2v"][model_size]:
                self.model_status["t2v"][model_size][resolution]["loaded"] = False
                self.model_status["t2v"][model_size][resolution]["error"] = error
            elif model_type == "i2v" and resolution in self.model_status["i2v"]:
                self.model_status["i2v"][resolution]["loaded"] = False
                self.model_status["i2v"][resolution]["error"] = error
                
            return {"status": status, "error": error}
        
        # 检查必要的子目录
        required_subdirs = ["vae", "scheduler", "unet"]
        missing_subdirs = []
        
        for subdir in required_subdirs:
            subdir_path = os.path.join(model_path, subdir)
            if not os.path.exists(subdir_path) or not os.path.isdir(subdir_path):
                missing_subdirs.append(subdir)
        
        if missing_subdirs:
            status = "incomplete"
            error = f"模型目录结构不完整，缺少以下子目录: {', '.join(missing_subdirs)}"
            logger.warning(error)
            
            # 尝试自动修复
            logger.info(f"尝试自动修复模型目录结构...")
            if auto_fix_model_structure(model_path):
                logger.info(f"模型目录结构自动修复成功，重新检查...")
                
                # 重新检查子目录是否存在
                missing_subdirs = []
                for subdir in required_subdirs:
                    subdir_path = os.path.join(model_path, subdir)
                    if not os.path.exists(subdir_path) or not os.path.isdir(subdir_path):
                        missing_subdirs.append(subdir)
                
                if not missing_subdirs:
                    logger.info(f"修复成功，模型目录结构现在完整")
                    return {"status": "ready", "error": None}
                else:
                    # 修复失败，更新错误信息
                    error = f"自动修复后仍缺少子目录: {', '.join(missing_subdirs)}"
                    logger.error(error)
            else:
                logger.error(f"自动修复失败")
            
            if model_type == "t2v" and model_size in self.model_status["t2v"] and resolution in self.model_status["t2v"][model_size]:
                self.model_status["t2v"][model_size][resolution]["loaded"] = False
                self.model_status["t2v"][model_size][resolution]["error"] = error
            elif model_type == "i2v" and resolution in self.model_status["i2v"]:
                self.model_status["i2v"][resolution]["loaded"] = False
                self.model_status["i2v"][resolution]["error"] = error
                
            return {"status": status, "error": error}
        
        # 检查必要的文件
        files_to_check = {
            "vae/config.json": "VAE配置",
            "scheduler/scheduler_config.json": "调度器配置",
            "unet/config.json": "UNet配置"
        }
        
        missing_files = []
        
        for file_path, desc in files_to_check.items():
            full_path = os.path.join(model_path, file_path)
            if not os.path.exists(full_path):
                missing_files.append(f"{desc} ({file_path})")
        
        if missing_files:
            status = "incomplete"
            error = f"模型文件不完整，缺少以下文件: {', '.join(missing_files)}"
            logger.warning(error)
            
            # 尝试自动修复
            logger.info(f"尝试自动修复缺失的配置文件...")
            if auto_fix_model_structure(model_path):
                logger.info(f"配置文件自动修复成功，重新检查...")
                
                # 重新检查文件是否存在
                missing_files = []
                for file_path, desc in files_to_check.items():
                    full_path = os.path.join(model_path, file_path)
                    if not os.path.exists(full_path):
                        missing_files.append(f"{desc} ({file_path})")
                
                if not missing_files:
                    logger.info(f"修复成功，模型文件现在完整")
                    return {"status": "ready", "error": None}
                else:
                    # 修复失败，更新错误信息
                    error = f"自动修复后仍缺少文件: {', '.join(missing_files)}"
                    logger.error(error)
            else:
                logger.error(f"自动修复失败")
            
            if model_type == "t2v" and model_size in self.model_status["t2v"] and resolution in self.model_status["t2v"][model_size]:
                self.model_status["t2v"][model_size][resolution]["loaded"] = False
                self.model_status["t2v"][model_size][resolution]["error"] = error
            elif model_type == "i2v" and resolution in self.model_status["i2v"]:
                self.model_status["i2v"][resolution]["loaded"] = False
                self.model_status["i2v"][resolution]["error"] = error
                
            return {"status": status, "error": error}
        
        # 所有检查通过，模型文件存在但未加载
        status = "ready"
        logger.info(f"模型文件完整，准备加载: {model_path}")
        
        if model_type == "t2v" and model_size in self.model_status["t2v"] and resolution in self.model_status["t2v"][model_size]:
            self.model_status["t2v"][model_size][resolution]["loaded"] = False
            self.model_status["t2v"][model_size][resolution]["error"] = None
        elif model_type == "i2v" and resolution in self.model_status["i2v"]:
            self.model_status["i2v"][resolution]["loaded"] = False
            self.model_status["i2v"][resolution]["error"] = None
        
        return {"status": status, "error": None}
    

    async def check_model_integrity(self, model_type: str, model_size: str = "14B", resolution: str = "720p") -> Dict[str, Any]:
        """
        检查模型文件的完整性
        
        Args:
            model_type: 模型类型，'t2v'或'i2v'
            model_size: 模型大小，仅用于t2v
            resolution: 分辨率，用于i2v或t2v的分辨率参数
            
        Returns:
            Dict: 包含success和message字段的字典
        """
        logger.info(f"检查模型完整性: {model_type}, 规格: {model_size}, 分辨率: {resolution}")
        
        # 检查模型状态
        status_result = await self.check_model_status(model_type, model_size, resolution)
        
        # 如果模型已加载或准备就绪，返回成功
        if status_result["status"] == "loaded" or status_result["status"] == "ready":
            return {"success": True, "message": "模型文件完整"}
        
        # 否则返回失败
        return {"success": False, "message": status_result.get("error", "未知错误")}

    def _get_model_id(self, model_type: str, model_size: str = "14B", resolution: str = "720p") -> str:
        """
        获取模型ID
        
        Args:
            model_type: 模型类型 (t2v 或 i2v)
            model_size: 模型大小 (14B 或 1.3B)
            resolution: 分辨率 (720p 或 1080p)
            
        Returns:
            str: 模型ID
        """
        if model_type == "t2v":
            # 文本到视频模型ID
            if model_size == "14B":
                return os.environ.get("WAN_T2V_14B_MODEL", "Wan2.1-T2V-14B")
            elif model_size == "1.3B":
                return os.environ.get("WAN_T2V_1_3B_MODEL", "Wan2.1-T2V-1.3B")
            else:
                logger.warning(f"未知的T2V模型大小: {model_size}，使用默认14B模型")
                return os.environ.get("WAN_T2V_14B_MODEL", "Wan2.1-T2V-14B")
        elif model_type == "i2v":
            # 图像到视频模型ID
            return "Wan2.1-I2V"
        else:
            logger.warning(f"未知的模型类型: {model_type}，使用默认T2V模型")
            return os.environ.get("WAN_T2V_14B_MODEL", "Wan2.1-T2V-14B")
    
    async def initialize_t2v(self, task_id: str, progress_updater: ProgressUpdater, model_size: str = "14B", resolution: str = "720p"):
        """
        初始化文本到视频模型
        
        Args:
            task_id: 任务ID
            progress_updater: 进度更新器
            model_size: 模型规格 (14B 或 1.3B)
            resolution: 分辨率 (720p 或 1080p)
        """
        logger.info(f"初始化T2V模型: 规格 {model_size}, 分辨率 {resolution}")
        
        # 检查模型是否已加载
        if 't2v' in self.models and model_size in self.models['t2v'] and resolution in self.models['t2v'][model_size]:
            logger.info(f"T2V模型 ({model_size}, {resolution}) 已加载，跳过初始化")
            return True
        
        # 检查是否已在加载中
        if self.model_status["t2v"][model_size][resolution]["loading"]:
            logger.info(f"T2V模型 ({model_size}, {resolution}) 正在加载中，请等待")
            # 定期检查模型是否已加载完成
            retry_count = 0
            while retry_count < self.max_retry_attempts:
                if self.model_status["t2v"][model_size][resolution]["loaded"]:
                    logger.info(f"T2V模型 ({model_size}, {resolution}) 已加载完成")
                    return True
                await asyncio.sleep(2)  # 等待2秒再检查
                retry_count += 1
            logger.warning(f"等待T2V模型 ({model_size}, {resolution}) 加载超时")
        
        # 设置为加载中状态
        self.model_status["t2v"][model_size][resolution]["loading"] = True
        self.model_status["t2v"][model_size][resolution]["error"] = None
        
        # 更新进度
        await progress_updater.update(
            status="loading",
            progress=5,
            logs=[f"初始化文本到视频模型 ({model_size}, {resolution})..."]
        )
        
        try:
            # 记录开始时间
            start_time = time.time()
            
            # 如果已经是模拟模式，直接创建模拟模型
            if self.mock_mode:
                logger.info(f"已处于模拟模式，跳过模型完整性检查，直接创建模拟模型")
                return await self._create_mock_model(model_size, resolution, progress_updater)
            
            # 获取模型ID和路径
            model_id = self._get_model_id("t2v", model_size, resolution)
            
            # 优先检查本地模型目录
            local_model_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 
                                        "local_models", "wan", "Wan2.1", model_id)
            if os.path.exists(local_model_dir):
                logger.info(f"使用本地模型目录: {local_model_dir}")
                model_path = local_model_dir
            else:
                model_path = os.path.join(self.model_cache_dir, model_id)
            
            logger.info(f"模型路径: {model_path}")
            
            # 检查模型完整性
            integrity_result = await self.check_model_integrity(model_type="t2v", model_size=model_size, resolution=resolution)
            if not integrity_result["success"]:
                error_msg = f"模型文件不完整: {integrity_result['message']}"
                logger.error(error_msg)
                
                # 更新进度，提供详细的错误信息
                await progress_updater.update(
                    status="failed",
                    error=error_msg,
                    logs=[
                        f"❌ 模型文件不完整: {integrity_result['message']}",
                        "⚠️ 系统将切换到模拟模式，但这将生成非真实的AI视频"
                    ]
                )
                
                # 如果用户明确要求不使用模拟模式，则返回失败
                force_real_mode = os.environ.get("WAN_FORCE_REAL_MODE", "false").lower() == "true"
                if force_real_mode:
                    logger.error("用户要求使用真实模式，但模型文件不完整，任务失败")
                    self.model_status["t2v"][model_size][resolution]["loading"] = False
                    self.model_status["t2v"][model_size][resolution]["error"] = error_msg
                    
                    await progress_updater.update(
                        status="failed",
                        error="模型文件不完整，且系统设置为强制使用真实模式",
                        logs=[
                            "❌ 模型文件不完整，无法生成视频",
                            "⚠️ 系统设置为强制使用真实模式，不允许使用模拟模式"
                        ]
                    )
                    return False
                
                # 切换到模拟模式
                logger.warning("模型文件不完整，切换到模拟模式")
                self.mock_mode = True
                os.environ["WAN_MOCK_MODE"] = "true"
                
                # 创建模拟模型并返回成功
                return await self._create_mock_model(model_size, resolution, progress_updater)
            
            # 模型文件完整，继续加载
            logger.info(f"模型文件完整性检查通过，开始加载模型")
            
            # 更新进度
            await progress_updater.update(
                progress=10,
                logs=[f"模型文件完整性检查通过，开始加载模型..."]
            )
            
            # 如果启用了模拟模式，则创建模拟模型
            if self.mock_mode:
                logger.info("模拟模式已启用，创建模拟模型")
                return await self._create_mock_model(model_size, resolution, progress_updater)
            
            # 导入必要的库
            try:
                # 尝试导入WAN模型
                try:
                    # 添加本地模型路径到系统路径
                    wan_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 
                                         "local_models", "wan", "Wan2.1")
                    if wan_path not in sys.path:
                        sys.path.append(wan_path)
                        logger.info(f"添加WAN模型路径到系统路径: {wan_path}")
                    
                    # 尝试导入WAN模块
                    try:
                        import wan
                        from wan.configs import WAN_CONFIGS
                        from wan.text2video import WanT2V
                        logger.info("成功导入WAN模块")
                        
                        # 使用WAN模型
                        config = WAN_CONFIGS[f"t2v-{model_size}"]
                        logger.info(f"使用WAN配置: {config}")
                        
                        # 初始化WAN模型
                        model = WanT2V(
                            config=config,
                            checkpoint_dir=model_path,
                            device_id=0 if self.device == "cuda" else -1,
                            t5_cpu=self.device == "cpu"
                        )
                        logger.info(f"WAN模型初始化成功")
                        
                        # 保存模型到内存
                        if 't2v' not in self.models:
                            self.models['t2v'] = {}
                        if model_size not in self.models['t2v']:
                            self.models['t2v'][model_size] = {}
                        
                        self.models['t2v'][model_size][resolution] = model
                        
                        # 更新状态
                        self.model_status["t2v"][model_size][resolution]["loaded"] = True
                        self.model_status["t2v"][model_size][resolution]["loading"] = False
                        self.is_initialized = True
                        
                        # 记录加载时间
                        load_time = time.time() - start_time
                        logger.info(f"T2V模型 ({model_size}, {resolution}) 加载完成，耗时: {load_time:.2f}秒")
                        
                        # 更新进度
                        await progress_updater.update(
                            progress=20,
                            logs=[f"模型加载成功，准备生成视频"]
                        )
                        
                        return True
                    except ImportError as e:
                        logger.error(f"导入WAN模块失败: {e}")
                        raise e
                    except Exception as e:
                        logger.error(f"初始化WAN模型失败: {e}")
                        logger.error(traceback.format_exc())
                        raise e
                except Exception as e:
                    logger.error(f"加载WAN模型失败: {e}")
                    logger.error(traceback.format_exc())
                    
                    # 尝试使用diffusers
                    logger.info("尝试使用diffusers加载模型")
                    import torch
                    from diffusers import DiffusionPipeline, DPMSolverMultistepScheduler
                    logger.info("成功导入diffusers库")
                    
                    # 更新进度
                    await progress_updater.update(
                        progress=15,
                        logs=[f"使用diffusers加载模型..."]
                    )
                    
                    # 这里应该添加使用diffusers加载模型的代码
                    # 但由于我们优先使用WAN模型，这部分代码暂时留空
                    # 如果需要，可以在后续实现
                    
                    # 由于diffusers加载失败，切换到模拟模式
                    logger.warning("使用diffusers加载模型失败，切换到模拟模式")
                    self.mock_mode = True
                    os.environ["WAN_MOCK_MODE"] = "true"
                    
                    # 创建模拟模型并返回成功
                    return await self._create_mock_model(model_size, resolution, progress_updater)
            except ImportError as e:
                error_msg = f"导入必要的库失败: {str(e)}"
                logger.error(error_msg)
                self.model_status["t2v"][model_size][resolution]["error"] = error_msg
                self.model_status["t2v"][model_size][resolution]["loading"] = False
                await progress_updater.update(
                    status="failed",
                    error=error_msg
                )
                
                # 如果用户明确要求不使用模拟模式，则返回失败
                if os.environ.get("WAN_FORCE_REAL_MODE", "false").lower() == "true":
                    logger.error("用户要求使用真实模式，但导入库失败，任务失败")
                    return False
                
                # 启用模拟模式继续
                logger.info("导入库失败，将使用模拟模式继续")
                self.mock_mode = True
                os.environ["WAN_MOCK_MODE"] = "true"
                
                # 创建模拟模型并返回成功
                return await self._create_mock_model(model_size, resolution, progress_updater)
            
        except Exception as e:
            error_msg = f"初始化T2V模型时出错: {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            
            # 更新状态
            self.model_status["t2v"][model_size][resolution]["loading"] = False
            self.model_status["t2v"][model_size][resolution]["error"] = error_msg
            
            # 更新进度
            await progress_updater.update(
                status="failed",
                error=error_msg,
                logs=[f"❌ 初始化模型失败: {error_msg}"]
            )
            
            # 如果用户明确要求不使用模拟模式，则返回失败
            if os.environ.get("WAN_FORCE_REAL_MODE", "false").lower() == "true":
                logger.error("用户要求使用真实模式，但初始化失败，任务失败")
                return False
            
            # 否则切换到模拟模式
            logger.warning("初始化模型失败，将使用模拟模式继续")
            self.mock_mode = True
            os.environ["WAN_MOCK_MODE"] = "true"
            
            # 创建模拟模型并返回成功
            return await self._create_mock_model(model_size, resolution, progress_updater)
    
    async def _create_mock_model(self, model_size: str, resolution: str, progress_updater: ProgressUpdater) -> bool:
        """创建模拟模型并更新状态"""
        try:
            start_time = time.time()
            
            # 自定义AutoencoderKL类，用于加载VAE
            class AutoencoderKLWan:
                @classmethod
                def from_pretrained(cls, model_path, **kwargs):
                    logger.info(f"[模拟] 加载VAE: {model_path}")
                    return cls()
                    
            # 自定义Pipeline类，用于加载模型
            class WanPipeline:
                def __init__(self, **kwargs):
                    logger.info("[模拟] 初始化WAN Pipeline")
                    self.device = "cpu"
                    self.vae = kwargs.get("vae")
                    self.scheduler = kwargs.get("scheduler")
                    self.mock_mode = True  # 标记这是模拟模式
                
                @classmethod
                def from_pretrained(cls, model_path, **kwargs):
                    logger.info(f"[模拟] 从预训练模型加载: {model_path}")
                    return cls(**kwargs)
                    
                def to(self, device):
                    logger.info(f"[模拟] 将模型移至设备: {device}")
                    self.device = device
                    return self
                
                # 添加模拟的生成方法
                def __call__(self, prompt, negative_prompt="", height=720, width=1280, 
                             num_frames=16, guidance_scale=7.5, generator=None):
                    logger.info(f"[模拟] 生成视频: prompt='{prompt}', 大小={width}x{height}, 帧数={num_frames}")
                    
                    # 创建更有结构的模拟视频
                    import numpy as np
                    import cv2
                    from PIL import Image, ImageDraw, ImageFont
                    
                    # 创建一个帧数组
                    frames = np.zeros((1, num_frames, height, width, 3), dtype=np.uint8)
                    
                    # 为每一帧添加一些内容
                    for i in range(num_frames):
                        # 创建PIL图像以便添加文字
                        img = Image.new('RGB', (width, height), color=(0, 0, 0))
                        draw = ImageDraw.Draw(img)
                        
                        # 添加渐变背景
                        for y in range(height):
                            color_value = int(y / height * 255)
                            for x in range(width):
                                r = (color_value + i * 5) % 255
                                g = (color_value + i * 3) % 255
                                b = (color_value + i * 7) % 255
                                img.putpixel((x, y), (r, g, b))
                        
                        # 添加提示词和模拟模式提示
                        try:
                            # 尝试使用系统字体
                            font_size = 40
                            try:
                                font = ImageFont.truetype("arial.ttf", font_size)
                            except:
                                # 退回到默认字体
                                font = ImageFont.load_default()
                            
                            # 添加提示词
                            prompt_text = f"提示词: {prompt}"
                            draw.text((width//2 - 300, height//2 - 150), prompt_text, 
                                     fill=(255, 255, 255), font=font)
                            
                            # 添加模拟模式提示
                            mock_text = "⚠️ 模拟模式 - 非真实AI生成视频 ⚠️"
                            draw.text((width//2 - 300, height//2 - 50), mock_text, 
                                     fill=(255, 255, 0), font=font)
                            
                            # 添加原因说明
                            reason_text = "原因: 模型文件不完整或无法加载"
                            draw.text((width//2 - 300, height//2 + 50), reason_text, 
                                     fill=(255, 255, 255), font=font)
                            
                            # 添加帧计数
                            frame_text = f"帧 {i+1}/{num_frames}"
                            draw.text((width//2 - 300, height//2 + 150), frame_text, 
                                     fill=(255, 255, 255), font=font)
                        except Exception as e:
                            # 如果添加文字失败，记录错误但继续生成视频
                            logger.error(f"在模拟视频中添加文字时出错: {e}")
                        
                        # 转换为numpy数组并存储
                        frames[0, i] = np.array(img)
                    
                    return type('obj', (object,), {'frames': frames})
            
            # 创建简单的调度器
            class MockScheduler:
                def __init__(self, **kwargs):
                    logger.info("[模拟] 创建调度器")
                    for key, value in kwargs.items():
                        setattr(self, key, value)
            
            # 创建模拟调度器和VAE
            scheduler = MockScheduler(beta_start=0.00085, beta_end=0.012, beta_schedule="linear")
            vae = AutoencoderKLWan()
            
            # 创建管道
            pipeline = WanPipeline(scheduler=scheduler, vae=vae)
            pipeline = pipeline.to(self.device)
            
            # 记录模型加载完成
            load_time = time.time() - start_time
            logger.info(f"[模拟] T2V模型 ({model_size}, {resolution}) 加载完成，耗时: {load_time:.2f}秒")
            
            # 保存模型到内存
            if 't2v' not in self.models:
                self.models['t2v'] = {}
            if model_size not in self.models['t2v']:
                self.models['t2v'][model_size] = {}
            
            self.models['t2v'][model_size][resolution] = pipeline
            
            # 更新状态
            self.model_status["t2v"][model_size][resolution]["loaded"] = True
            self.model_status["t2v"][model_size][resolution]["loading"] = False
            self.is_initialized = True
            
            # 更新进度
            await progress_updater.update(
                status="running",
                progress=20,
                logs=[f"[模拟] T2V模型 ({model_size}, {resolution}) 加载完成，准备生成视频"]
            )
            
            return True
        except Exception as e:
            logger.error(f"创建模拟模型失败: {e}")
            return False
    
    def _get_manual_download_guide(self, model_type: str, model_size: str = "14B", resolution: str = "720p") -> str:
        """获取手动下载指南"""
        model_id = self._get_model_id(model_type, model_size, resolution)
        cache_dir = self.model_cache_dir
        
        # 是否是本地路径
        local_path = os.path.normpath(os.path.join(cache_dir, model_id))
        guide = f"""
手动设置指南:
1. 权限问题：无法访问或写入目录 {local_path}
2. 建议解决方案:
   a. 以管理员权限运行应用程序
   b. 修改目录权限，确保当前用户有读写权限
   c. 在环境变量中设置WAN_MODEL_CACHE_DIR为具有写入权限的目录

3. 目录结构要求:
   {local_path}/
      ├── vae/          # VAE模型目录
      │   └── config.json  # 需要创建
      ├── scheduler/    # 调度器配置
      │   └── scheduler_config.json  # 需要创建
      └── unet/         # UNet模型目录
          └── config.json  # 需要创建
"""
        return guide
    
    def _convert_path_to_url(self, file_path: str, file_type: str = "video") -> str:
        """
        将本地文件路径转换为可通过HTTP访问的URL
        
        Args:
            file_path: 本地文件路径
            file_type: 文件类型(video/thumbnail)
            
        Returns:
            str: HTTP URL
        """
        if not file_path:
            logger.warning("尝试转换空文件路径为URL")
            return ""
            
        # 提取文件名
        file_name = os.path.basename(file_path)
        logger.info(f"转换文件路径为URL: {file_path} -> {file_name}")
        
        # 检查文件是否存在
        if not os.path.exists(file_path):
            logger.warning(f"文件不存在: {file_path}")
            # 尝试创建媒体目录
            media_dirs = ['media', 'media/videos', 'media/thumbnails']
            for d in media_dirs:
                os.makedirs(d, exist_ok=True)
                logger.info(f"确保媒体目录存在: {d}")
        else:
            logger.info(f"文件存在，大小: {os.path.getsize(file_path)} 字节")
            
            # 如果文件不在media目录中，复制到media目录
            if file_type == "video" and "media/videos" not in file_path:
                target_path = os.path.join("media", "videos", file_name)
                try:
                    import shutil
                    # 确保目标目录存在
                    os.makedirs(os.path.dirname(target_path), exist_ok=True)
                    # 复制文件
                    shutil.copy2(file_path, target_path)
                    logger.info(f"文件已复制到媒体目录: {target_path}")
                    
                    # 验证文件是否成功复制
                    if os.path.exists(target_path) and os.path.getsize(target_path) > 0:
                        logger.info(f"文件复制成功，大小: {os.path.getsize(target_path)} 字节")
                    else:
                        logger.error(f"文件复制失败或大小为0: {target_path}")
                except Exception as e:
                    logger.error(f"复制文件到媒体目录失败: {e}")
            elif file_type == "thumbnail" and "media/thumbnails" not in file_path:
                target_path = os.path.join("media", "thumbnails", file_name)
                try:
                    import shutil
                    # 确保目标目录存在
                    os.makedirs(os.path.dirname(target_path), exist_ok=True)
                    # 复制文件
                    shutil.copy2(file_path, target_path)
                    logger.info(f"文件已复制到媒体目录: {target_path}")
                    
                    # 验证文件是否成功复制
                    if os.path.exists(target_path) and os.path.getsize(target_path) > 0:
                        logger.info(f"文件复制成功，大小: {os.path.getsize(target_path)} 字节")
                    else:
                        logger.error(f"文件复制失败或大小为0: {target_path}")
                except Exception as e:
                    logger.error(f"复制文件到媒体目录失败: {e}")
        
        # 构建URL (使用/api/media/前缀用于访问媒体文件)
        url = ""
        if file_type == "video":
            url = f"/api/media/videos/{file_name}"
        elif file_type == "thumbnail":
            url = f"/api/media/thumbnails/{file_name}"
        else:
            url = f"/api/media/files/{file_name}"
            
        logger.info(f"生成的URL: {url}")
        
        # 确保URL是绝对路径
        if not url.startswith(('http://', 'https://')):
            # 获取API基础URL
            api_base = os.environ.get('API_BASE_URL', '')
            if api_base:
                if not api_base.endswith('/'):
                    api_base += '/'
                if url.startswith('/'):
                    url = url[1:]  # 移除开头的斜杠以避免重复
                url = f"{api_base}{url}"
                logger.info(f"转换为绝对URL: {url}")
        
        return url
    
    async def generate_video_from_text(
        self,
        task_id: str,
        prompt: str,
        negative_prompt: str = "",
        num_frames: int = 81,  # 约5秒视频(16fps)
        height: int = 720,
        width: int = 1280,
        guidance_scale: float = 5.0,
        fps: int = 16,
        seed: Optional[int] = None,
        model_size: str = "14B",
        resolution: str = "720p", 
        output_format: str = "mp4"
    ) -> Dict[str, Any]:
        """
        从文本生成视频
        
        Args:
            task_id: 任务ID
            prompt: 提示词
            negative_prompt: 负面提示词
            num_frames: 帧数
            height: 高度
            width: 宽度
            guidance_scale: 引导比例
            fps: 帧率
            seed: 随机种子
            model_size: 模型大小 (14B/1.3B)
            resolution: 分辨率 (720p/1080p)
            output_format: 输出格式 (mp4/gif)
            
        Returns:
            Dict: 包含生成结果的字典
        """
        # 创建进度跟踪器
        progress_tracker = get_progress_tracker()
        progress_updater = ProgressUpdater(task_id)
        
        # 记录参数
        logger.info(f"从文本生成视频: task_id={task_id}, prompt='{prompt}', model_size={model_size}")
        logger.info(f"参数: frames={num_frames}, size={width}x{height}, fps={fps}, guidance={guidance_scale}")
        
        # 检查是否强制使用真实模式
        force_real_mode = os.environ.get("WAN_FORCE_REAL_MODE", "false").lower() == "true"
        
        if self.mock_mode:
            if force_real_mode:
                error_msg = "系统设置为强制使用真实模式，但模型文件不完整或无法加载"
                logger.error(error_msg)
                await progress_updater.update(
                    status="failed",
                    error=error_msg,
                    logs=[
                        "❌ 无法生成视频: 系统设置为强制使用真实模式，但模型文件不完整",
                        "⚠️ 请确保正确安装WAN 2.1模型文件，或禁用强制真实模式设置"
                    ]
                )
                return {
                    "success": False,
                    "error": error_msg,
                    "task_id": task_id
                }
            else:
                logger.warning(f"使用模拟模式生成视频: {prompt}")
        
        try:
            # 初始化模型
            await progress_updater.update(
                status="running",
                progress=0,
                logs=[f"初始化T2V模型 ({model_size}, {resolution})..."]
            )
            
            # 确保模型初始化
            model_initialized = await self.initialize_t2v(task_id, progress_updater, model_size, resolution)
            if not model_initialized:
                error_msg = f"模型初始化失败: {self.model_status['t2v'][model_size][resolution]['error']}"
                logger.error(error_msg)
                await progress_updater.update(
                    status="failed",
                    error=error_msg,
                    logs=[f"❌ {error_msg}"]
                )
                return {
                    "success": False,
                    "error": error_msg,
                    "task_id": task_id
                }
            
            # 更新进度
            await progress_updater.update(
                progress=30,
                logs=[f"开始生成视频..."]
            )
            
            # 检查是否使用模拟模式
            if self.mock_mode:
                # 如果强制使用真实模式，则返回错误
                if force_real_mode:
                    error_msg = "系统设置为强制使用真实模式，但当前处于模拟模式"
                    logger.error(error_msg)
                    await progress_updater.update(
                        status="failed",
                        error=error_msg,
                        logs=[
                            "❌ 无法生成视频: 系统设置为强制使用真实模式，但当前处于模拟模式",
                            "⚠️ 请确保正确安装WAN 2.1模型文件，或禁用强制真实模式设置"
                        ]
                    )
                    return {
                        "success": False,
                        "error": error_msg,
                        "task_id": task_id
                    }
                
                logger.info("使用模拟模式生成视频")
                # 直接使用模拟模式生成视频，不进行模型完整性检查
                return await self._generate_mock_video(
                    task_id=task_id,
                    prompt=prompt,
                    negative_prompt=negative_prompt,
                    num_frames=num_frames,
                    height=height,
                    width=width,
                    guidance_scale=guidance_scale,
                    fps=fps,
                    seed=seed,
                    model_size=model_size,
                    resolution=resolution,
                    output_format=output_format,
                    progress_updater=progress_updater
                )
            
            # 使用真实模型生成视频
            try:
                # 获取模型
                model = self.models['t2v'][model_size][resolution]
                
                # 检查模型类型
                if hasattr(model, 'generate'):
                    # 使用WAN模型
                    logger.info("使用WAN模型生成视频")
                    
                    # 更新进度
                    await progress_updater.update(
                        progress=40,
                        logs=[f"使用WAN模型生成视频..."]
                    )
                    
                    # 设置随机种子
                    if seed is None:
                        import random
                        seed = random.randint(0, 2**32 - 1)
                    logger.info(f"使用随机种子: {seed}")
                    
                    # 生成视频
                    frames = model.generate(
                        input_prompt=prompt,
                        size=(width, height),
                        frame_num=num_frames,
                        shift=5.0,  # 默认值
                        sampling_steps=50,  # 默认值
                        guide_scale=guidance_scale,
                        n_prompt=negative_prompt,
                        seed=seed
                    )
                    
                    # 更新进度
                    await progress_updater.update(
                        progress=80,
                        logs=[f"视频生成完成，准备导出..."]
                    )
                    
                    # 确保frames是正确的格式
                    if hasattr(frames, 'frames'):
                        frames = frames.frames
                    
                    # 将PyTorch张量转换为NumPy数组
                    import numpy as np
                    
                    # 详细检查和记录frames的类型和形状
                    logger.info(f"生成的frames类型: {type(frames)}")
                    if hasattr(frames, 'shape'):
                        logger.info(f"frames形状: {frames.shape}")
                    elif isinstance(frames, list):
                        logger.info(f"frames是列表，长度: {len(frames)}")
                        if len(frames) > 0:
                            logger.info(f"第一个元素类型: {type(frames[0])}")
                            if hasattr(frames[0], 'shape'):
                                logger.info(f"第一个元素形状: {frames[0].shape}")
                    
                    # 处理不同类型的frames
                    if isinstance(frames, torch.Tensor):
                        frames_np = frames.cpu().numpy()
                        logger.info(f"PyTorch张量转换为NumPy数组: {frames_np.shape}")
                    elif isinstance(frames, np.ndarray):
                        frames_np = frames
                        logger.info(f"frames已经是NumPy数组: {frames_np.shape}")
                    elif isinstance(frames, list) and len(frames) > 0 and isinstance(frames[0], torch.Tensor):
                        # 尝试转换列表中的第一个张量
                        frames_np = frames[0].cpu().numpy()
                        logger.info(f"从列表中提取第一个张量并转换: {frames_np.shape}")
                    else:
                        # 无法处理的类型，创建一个简单的黑色帧
                        logger.warning(f"无法处理的frames类型，创建黑色帧")
                        frames_np = np.zeros((num_frames, height, width, 3), dtype=np.uint8)
                    
                    # 检查是否需要转置
                    if len(frames_np.shape) == 4 and frames_np.shape[0] == 3:
                        # 可能是(C, T, H, W)格式，转换为(T, H, W, C)
                        logger.info(f"检测到(C, T, H, W)格式，转换为(T, H, W, C)")
                        frames_np = np.transpose(frames_np, (1, 2, 3, 0))
                        logger.info(f"转换后形状: {frames_np.shape}")
                    
                    # 确保值范围合适
                    if frames_np.max() > 1.0 and frames_np.max() <= 255.0:
                        # 已经是适合的范围，转为uint8
                        frames_np = frames_np.astype(np.uint8)
                    elif frames_np.max() <= 1.0:
                        frames_np = (frames_np * 255).astype(np.uint8)
                        logger.info(f"归一化值转换为uint8: {frames_np.dtype}")
                    else:
                        frames_np = np.clip(frames_np, 0, 255).astype(np.uint8)
                        logger.info(f"裁剪值并转换为uint8: {frames_np.dtype}")
                    
                    # 保存视频
                    output_path = os.path.join(tempfile.gettempdir(), f"{task_id}.{output_format}")
                    logger.info(f"保存视频到: {output_path}")
                    
                    # 使用自定义导出函数
                    export_success = self._export_video_with_ffmpeg_params(
                        frames_np, output_path, fps=fps
                    )
                    
                    if not export_success:
                        logger.error("导出视频失败")
                        if progress_updater:
                            await progress_updater.update(
                                status="failed",
                                error="导出视频失败",
                                logs=[f"❌ 导出视频失败"]
                            )
                        return {
                            "success": False,
                            "error": "导出视频失败",
                            "task_id": task_id
                        }
                    
                    # 生成缩略图
                    thumbnail_path = self._generate_thumbnail(output_path)
                    
                    # 转换为URL
                    video_url = self._convert_path_to_url(output_path, "video")
                    thumbnail_url = self._convert_path_to_url(thumbnail_path, "thumbnail")
                    
                    if progress_updater:
                        await progress_updater.update(
                            status="completed",
                            progress=100,
                            logs=[f"视频生成完成"],
                            result={
                                "video_url": video_url,
                                "thumbnail_url": thumbnail_url,
                                "is_mock": True
                            }
                        )
                    
                    return {
                        "success": True,
                        "task_id": task_id,
                        "video_path": output_path,
                        "video_url": video_url,
                        "thumbnail_path": thumbnail_path,
                        "thumbnail_url": thumbnail_url,
                        "is_mock": True
                    }
            except Exception as e:
                logger.error(f"使用WAN模型生成视频时出错: {e}")
                logger.error(traceback.format_exc())
                if progress_updater:
                    await progress_updater.update(
                        status="failed",
                        error="使用WAN模型生成视频失败",
                        logs=[f"❌ 使用WAN模型生成视频失败"]
                    )
                return {
                    "success": False,
                    "error": "使用WAN模型生成视频失败",
                    "task_id": task_id
                }
        except Exception as e:
            # 捕获未被内部try-except捕获的异常
            error_msg = f"视频生成过程中发生未处理的错误: {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            
            # 更新进度
            if progress_updater:
                await progress_updater.update(
                    status="failed",
                    error=error_msg,
                    logs=[f"❌ 未处理的错误: {error_msg}"]
                )
            
            return {
                "success": False,
                "error": error_msg,
                "task_id": task_id
            }

    
    def _export_video_with_ffmpeg_params(self, frames, output_path, fps=16):
        """
        使用FFmpeg将视频帧序列导出为视频文件
        
        Args:
            frames: 视频帧NumPy数组，格式为(T, H, W, C)
            output_path: 输出视频文件路径
            fps: 帧率（默认16）
            
        Returns:
            bool: 操作是否成功
        """
        import numpy as np
        from skimage.transform import resize
        import tempfile
        import os
        import subprocess
        import shutil
        import uuid
        
        logger.info(f"开始导出视频: {output_path}, fps={fps}")
        
        # 检查frames数据
        if not isinstance(frames, np.ndarray):
            logger.error(f"视频帧类型错误: {type(frames)}，应为NumPy数组")
            return False
        
        # 记录形状信息便于调试
        logger.info(f"视频帧形状: {frames.shape}")
        
        # 检查维度
        if len(frames.shape) != 4:
            logger.warning(f"视频帧维度数量不是4: {len(frames.shape)}")
            if len(frames.shape) == 3:
                # 可能是单帧图像，尝试添加时间维度
                frames = np.expand_dims(frames, axis=0)
                logger.info(f"添加时间维度后形状: {frames.shape}")
        
        # 注意通道数量，有时是单通道或不同格式
        channels = frames.shape[-1] if len(frames.shape) >= 3 else 1
        if channels != 3 and channels != 1:
            logger.warning(f"视频帧通道数不是3: {channels}")
        
        # 确保数据类型正确
        if frames.dtype != np.uint8:
            logger.info(f"转换帧数据类型从 {frames.dtype} 到 uint8")
            if frames.max() <= 1.0:
                frames = (frames * 255).astype(np.uint8)
            else:
                frames = frames.astype(np.uint8)
        
        # 创建临时目录
        temp_dir = os.path.join(tempfile.gettempdir(), f"video_export_{uuid.uuid4()}")
        os.makedirs(temp_dir, exist_ok=True)
        logger.info(f"创建临时目录: {temp_dir}")
        
        try:
        # 确保帧的尺寸正确
        if len(frames.shape) == 4:
                # 记录原始形状以便分析
                original_shape = frames.shape
                logger.info(f"处理前帧形状: {original_shape}")
                
                # 检查是否存在特定错误模式
                # 以下逻辑用于检测和修正常见的维度混淆问题
                if original_shape[3] > 100:  # 通道数异常大，可能是宽度被误认为通道数
                    logger.warning(f"检测到维度错误: 通道数维度({original_shape[3]})异常大，可能是宽度和通道数交换")
                    # 交换宽度和通道数维度
                    frames = np.transpose(frames, (0, 1, 3, 2))
                    logger.info(f"修正后的帧形状: {frames.shape}")
                
                # 另一种错误模式：通道数被错误识别为高度，高度被错误识别为宽度，宽度被错误识别为通道数
                elif original_shape[1] <= 4 and original_shape[2] > 100 and original_shape[3] > 100:
                    logger.warning(f"检测到维度错误: 高度维度({original_shape[1]})异常小，可能是维度完全错位")
                    # 重新排列维度为正确顺序
                    frames = np.transpose(frames, (0, 3, 2, 1))
                    logger.info(f"修正后的帧形状: {frames.shape}")
                
                # 检查异常的高宽比
                elif original_shape[1] < 10 and original_shape[2] > 100:
                    logger.warning(f"检测到异常高宽比: 高度({original_shape[1]})远小于宽度({original_shape[2]})")
                    # 交换高度和宽度
                    frames = np.transpose(frames, (0, 2, 1, 3))
                    logger.info(f"交换高宽后的帧形状: {frames.shape}")
                
                # 最终提取形状
                num_frames, height, width, channels = frames.shape
                logger.info(f"最终处理的帧形状: 帧数={num_frames}, 高度={height}, 宽度={width}, 通道数={channels}")
            
            # 检查通道数
            if channels != 3:
                logger.warning(f"通道数不是3，尝试调整: {channels}")
                if channels == 1:
                    # 转换灰度到RGB
                    rgb_frames = np.zeros((num_frames, height, width, 3), dtype=frames.dtype)
                    for i in range(num_frames):
                        rgb_frames[i] = np.stack([frames[i, :, :, 0]] * 3, axis=-1)
                    frames = rgb_frames
                    channels = 3
                    logger.info(f"将单通道帧转换为RGB(3通道)")
                else:
                    # 裁剪或填充到3通道
                    rgb_frames = np.zeros((num_frames, height, width, 3), dtype=frames.dtype)
                    for i in range(num_frames):
                        rgb_frames[i, :, :, :3] = frames[i, :, :, :3]
                    frames = rgb_frames
                    channels = 3
                    logger.info(f"裁剪帧通道到RGB(3通道)")
            
            # 确保高度和宽度是2的倍数（H.264要求）
            if height % 2 != 0 or width % 2 != 0:
                logger.warning(f"帧尺寸不是2的倍数: {width}x{height}，调整中...")
                
                # 调整为2的倍数
                new_height = (height // 2) * 2
                new_width = (width // 2) * 2
                
                # 检查是否需要特殊处理
                if new_height < 2:
                    new_height = 2
                if new_width < 2:
                    new_width = 2
                    
                # 调整所有帧
                adjusted_frames = np.zeros((num_frames, new_height, new_width, channels), dtype=frames.dtype)
                for i in range(num_frames):
                    # 使用resize确保正确的尺寸
                    adjusted_frames[i] = resize(frames[i], (new_height, new_width, channels), 
                                               anti_aliasing=True, preserve_range=True).astype(frames.dtype)
                
                logger.info(f"已调整帧尺寸: {width}x{height} -> {new_width}x{new_height}")
                frames = adjusted_frames
        else:
            logger.error(f"意外的帧形状: {frames.shape}，需要4维(T, H, W, C)")
            return False
            
        
        # 确保帧的尺寸正确
            if len(frames.shape) == 4:
                # 记录原始形状以便分析
                original_shape = frames.shape
                logger.info(f"处理前帧形状: {original_shape}")
                
                # 检查是否存在特定错误模式
                # 以下逻辑用于检测和修正常见的维度混淆问题
                if original_shape[3] > 100:  # 通道数异常大，可能是宽度被误认为通道数
                    logger.warning(f"检测到维度错误: 通道数维度({original_shape[3]})异常大，可能是宽度和通道数交换")
                    # 交换宽度和通道数维度
                    frames = np.transpose(frames, (0, 1, 3, 2))
                    logger.info(f"修正后的帧形状: {frames.shape}")
                
                # 另一种错误模式：通道数被错误识别为高度，高度被错误识别为宽度，宽度被错误识别为通道数
                elif original_shape[1] <= 4 and original_shape[2] > 100 and original_shape[3] > 100:
                    logger.warning(f"检测到维度错误: 高度维度({original_shape[1]})异常小，可能是维度完全错位")
                    # 重新排列维度为正确顺序
                    frames = np.transpose(frames, (0, 3, 2, 1))
                    logger.info(f"修正后的帧形状: {frames.shape}")
                
                # 检查异常的高宽比
                elif original_shape[1] < 10 and original_shape[2] > 100:
                    logger.warning(f"检测到异常高宽比: 高度({original_shape[1]})远小于宽度({original_shape[2]})")
                    # 交换高度和宽度
                    frames = np.transpose(frames, (0, 2, 1, 3))
                    logger.info(f"交换高宽后的帧形状: {frames.shape}")
                
                # 最终提取形状
                num_frames, height, width, channels = frames.shape
                logger.info(f"最终处理的帧形状: 帧数={num_frames}, 高度={height}, 宽度={width}, 通道数={channels}")
            
            # 检查通道数
            if channels != 3:
                logger.warning(f"通道数不是3，尝试调整: {channels}")
                if channels == 1:
                    # 转换灰度到RGB
                    rgb_frames = np.zeros((num_frames, height, width, 3), dtype=frames.dtype)
                    for i in range(num_frames):
                        rgb_frames[i] = np.stack([frames[i, :, :, 0]] * 3, axis=-1)
                    frames = rgb_frames
                    channels = 3
                    logger.info(f"将单通道帧转换为RGB(3通道)")
                else:
                    # 裁剪或填充到3通道
                    rgb_frames = np.zeros((num_frames, height, width, 3), dtype=frames.dtype)
                    for i in range(num_frames):
                        rgb_frames[i, :, :, :3] = frames[i, :, :, :3]
                    frames = rgb_frames
                    channels = 3
                    logger.info(f"裁剪帧通道到RGB(3通道)")
            
            # 确保高度和宽度是2的倍数（H.264要求）
            if height % 2 != 0 or width % 2 != 0:
                
                logger.warning(f"帧尺寸不是2的倍数: 宽度={width}, 高度={height}，调整中...")
                # 调整为2的倍数
                new_height = (height // 2) * 2
                new_width = (width // 2) * 2
                
                # 检查是否需要特殊处理
                if new_height < 2:
                    new_height = 2
                if new_width < 2:
                    new_width = 2
                    
                # 调整所有帧
                adjusted_frames = np.zeros((num_frames, new_height, new_width, channels), dtype=frames.dtype)
                for i in range(num_frames):
                    # 使用resize确保正确的尺寸
                    adjusted_frames[i] = resize(frames[i], (new_height, new_width, channels), 
                                               anti_aliasing=True, preserve_range=True).astype(frames.dtype)
                
                logger.info(f"已调整帧尺寸: {width}x{height} -> {new_width}x{new_height}")
                frames = adjusted_frames

            
        
            # 保存帧为临时图像
            frame_paths = []
            for i, frame in enumerate(frames):
                frame_path = os.path.join(temp_dir, f"frame_{i:05d}.png")
                
                # 如果是灰度图像，需要转换为RGB
                if channels == 1 and len(frame.shape) == 2:
                    frame = np.stack([frame] * 3, axis=-1)
                
                # 使用OpenCV保存图像
                cv2.imwrite(frame_path, cv2.cvtColor(frame, cv2.COLOR_RGB2BGR))
                frame_paths.append(frame_path)
            
            logger.info(f"保存了 {len(frame_paths)} 帧图像")
            
            # 检查是否有足够的帧生成视频
            if len(frame_paths) < 1:
                logger.error("没有足够的帧来生成视频")
                return False
            
            # 首先尝试使用FFmpeg
            try:
                # 准备FFmpeg命令
                cmd = [
                    "ffmpeg",
                    "-y",  # 覆盖输出文件
                    "-framerate", str(fps),  # 帧率
                    "-i", os.path.join(temp_dir, "frame_%05d.png"),  # 输入图像序列
                    "-c:v", "libx264",  # 视频编码器
                    "-profile:v", "high",  # 编码配置文件
                    "-crf", "18",  # 视频质量(0-51)，18为高质量
                    "-pix_fmt", "yuv420p",  # 像素格式，确保兼容性
                    output_path  # 输出文件
                ]
                
                logger.info(f"执行FFmpeg命令: {' '.join(cmd)}")
                
                # 执行命令
                process = subprocess.run(
                    cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    timeout=180  # 3分钟超时
                )
                
                # 检查是否成功
                if process.returncode != 0:
                    logger.error(f"FFmpeg执行失败: {process.stderr.decode('utf-8', errors='ignore')}")
                    raise Exception("FFmpeg执行失败")
                
                # 验证输出文件
                if not os.path.exists(output_path) or os.path.getsize(output_path) == 0:
                    logger.error("FFmpeg生成的视频文件无效")
                    raise Exception("输出文件无效")
                
                logger.info(f"使用FFmpeg成功导出视频: {output_path}")
                return True
                
            except (subprocess.SubprocessError, Exception) as e:
                logger.warning(f"FFmpeg导出失败，尝试使用OpenCV: {str(e)}")
                
                # 使用OpenCV作为备选方案
                if len(frame_paths) > 0:
                    # 读取第一帧以获取尺寸
                    first_frame = cv2.imread(frame_paths[0])
                    height, width = first_frame.shape[:2]
                    
                    # 创建视频写入器
                    fourcc = cv2.VideoWriter_fourcc(*'mp4v')  # 或尝试 'avc1', 'H264' 等
                    out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
                    
                    # 写入所有帧
                    for frame_path in frame_paths:
                        frame = cv2.imread(frame_path)
                        if frame is not None:
                            out.write(frame)
                    
                    # 释放资源
                    out.release()
                    
                    # 验证输出
                    if os.path.exists(output_path) and os.path.getsize(output_path) > 0:
                        logger.info(f"使用OpenCV成功导出视频: {output_path}")
                        return True
                    else:
                        logger.error("OpenCV生成的视频文件无效")
                        return False
        
        except Exception as e:
            logger.error(f"视频导出过程中出错: {str(e)}")
            logger.error(traceback.format_exc())
            return False
            
        finally:
            # 清理临时文件
            try:
                shutil.rmtree(temp_dir)
                logger.info(f"清理临时目录: {temp_dir}")
            except Exception as e:
                logger.warning(f"清理临时目录失败: {str(e)}")
        
        return False
    
    def _generate_thumbnail(self, video_path: str) -> str:
        """
        从视频生成缩略图
        
        Args:
            video_path: 视频文件路径
            
        Returns:
            str: 缩略图文件路径，如果生成失败则返回空字符串
        """
        try:
            logger.info(f"从视频生成缩略图: {video_path}")
            import cv2
            
            # 检查视频文件是否存在
            if not os.path.exists(video_path):
                logger.error(f"视频文件不存在: {video_path}")
                return ""
            
            # 打开视频文件
            video_capture = cv2.VideoCapture(video_path)
            if not video_capture.isOpened():
                logger.error(f"无法打开视频文件: {video_path}")
                return ""
            
            # 获取帧总数
            total_frames = int(video_capture.get(cv2.CAP_PROP_FRAME_COUNT))
            logger.info(f"视频总帧数: {total_frames}")
            
            if total_frames == 0:
                logger.error(f"视频文件为空: {video_path}")
                return ""
            
            # 设置帧位置到1/3处
            target_frame = int(total_frames / 3)
            video_capture.set(cv2.CAP_PROP_POS_FRAMES, target_frame)
            
            # 读取帧
            success, frame = video_capture.read()
            if not success:
                logger.warning(f"无法读取指定帧，尝试读取第一帧")
                video_capture.set(cv2.CAP_PROP_POS_FRAMES, 0)
                success, frame = video_capture.read()
                
                if not success:
                    logger.error(f"无法读取视频帧: {video_path}")
                    video_capture.release()
                    return ""
            
            # 释放视频捕获对象
            video_capture.release()
            
            # 创建缩略图文件路径（将视频扩展名改为jpg）
            thumbnail_path = os.path.splitext(video_path)[0] + ".jpg"
            
            # 调整大小（如果需要）
            max_dimension = 640
            height, width = frame.shape[:2]
            
            if height > max_dimension or width > max_dimension:
                # 保持纵横比调整大小
                if width >= height:
                    new_width = max_dimension
                    new_height = int(height * (max_dimension / width))
                else:
                    new_height = max_dimension
                    new_width = int(width * (max_dimension / height))
                
                frame = cv2.resize(frame, (new_width, new_height), interpolation=cv2.INTER_AREA)
                logger.info(f"调整缩略图大小到: {new_width}x{new_height}")
            
            # 保存缩略图
            cv2.imwrite(thumbnail_path, frame, [cv2.IMWRITE_JPEG_QUALITY, 90])
            
            # 检查缩略图是否成功保存
            if os.path.exists(thumbnail_path) and os.path.getsize(thumbnail_path) > 0:
                logger.info(f"成功生成缩略图: {thumbnail_path}, 大小: {os.path.getsize(thumbnail_path) / 1024:.2f} KB")
                return thumbnail_path
            else:
                logger.error(f"生成的缩略图不存在或大小为0: {thumbnail_path}")
                return ""
                
        except Exception as e:
            logger.error(f"生成缩略图时出错: {str(e)}")
            logger.error(traceback.format_exc())
            return ""
    
    async def _generate_mock_video(
        self,
        task_id: str,
        prompt: str,
        negative_prompt: str = "",
        num_frames: int = 81,
        height: int = 720,
        width: int = 1280,
        guidance_scale: float = 5.0,
        fps: int = 16,
        seed: Optional[int] = None,
        model_size: str = "14B",
        resolution: str = "720p",
        output_format: str = "mp4",
        progress_updater: Optional[ProgressUpdater] = None
    ) -> Dict[str, Any]:
        """
        生成模拟视频，用于真实模型无法使用时
        
        Args:
            task_id: 任务ID
            prompt: 提示词
            negative_prompt: 负面提示词
            num_frames: 帧数
            height: 高度
            width: 宽度
            guidance_scale: 引导比例
            fps: 帧率
            seed: 随机种子
            model_size: 模型大小
            resolution: 分辨率
            output_format: 输出格式
            progress_updater: 进度更新器
        
        Returns:
            Dict: 包含生成结果的字典
        """
        try:
            logger.info(f"[模拟] 生成视频: {prompt}")
            import numpy as np
            import cv2
            from PIL import Image, ImageDraw, ImageFont
            
            # 设置随机种子
            if seed is None:
                import random
                seed = random.randint(0, 2**32 - 1)
            np.random.seed(seed)
            
            # 更新进度
            if progress_updater:
                await progress_updater.update(
                    progress=40,
                    logs=[f"[模拟] 开始生成视频..."]
                )
            
            # 创建帧数组
            frames = np.zeros((num_frames, height, width, 3), dtype=np.uint8)
            
            # 为每帧添加内容
            for i in range(num_frames):
                # 创建渐变背景
                frame = np.zeros((height, width, 3), dtype=np.uint8)
                for y in range(height):
                    color_value = int(y / height * 255)
                    for x in range(width):
                        r = (color_value + i * 5) % 255
                        g = (color_value + i * 3) % 255
                        b = (color_value + i * 7) % 255
                        frame[y, x] = [r, g, b]
                
                # 添加动画元素
                circle_x = int(width * (0.5 + 0.3 * np.sin(i * 2 * np.pi / num_frames)))
                circle_y = int(height * (0.5 + 0.3 * np.cos(i * 2 * np.pi / num_frames)))
                cv2.circle(frame, (circle_x, circle_y), 50, (255, 255, 255), -1)
                
                # 转为PIL图像添加文字
                img = Image.fromarray(frame)
                draw = ImageDraw.Draw(img)
                
                # 使用默认字体
                try:
                    font = ImageFont.load_default()
                    
                    # 添加文字
                    draw.text((width//2 - 150, height//2 - 100), f"提示词: {prompt}", fill=(255, 255, 255), font=font)
                    draw.text((width//2 - 150, height//2), "模拟模式 - 非真实AI生成", fill=(255, 255, 0), font=font)
                    draw.text((width//2 - 150, height//2 + 100), f"帧 {i+1}/{num_frames}", fill=(255, 255, 255), font=font)
                except Exception as e:
                    logger.error(f"添加文字失败: {e}")
                
                frames[i] = np.array(img)
                
                # 每20帧更新一次进度
                if i % 20 == 0 and progress_updater:
                    progress = 50 + int(30 * (i / num_frames))
                    await progress_updater.update(
                        progress=progress,
                        logs=[f"[模拟] 已生成 {i+1}/{num_frames} 帧"]
                    )
            
            # 更新进度
            if progress_updater:
                await progress_updater.update(
                    progress=80,
                    logs=[f"[模拟] 导出视频..."]
                )
            
            # 保存视频
            output_path = os.path.join(tempfile.gettempdir(), f"{task_id}.{output_format}")
            logger.info(f"[模拟] 保存视频到: {output_path}")
            
            # 导出视频
            export_success = self._export_video_with_ffmpeg_params(
                frames, output_path, fps=fps
            )
            
            if not export_success:
                logger.error("[模拟] 导出视频失败")
                if progress_updater:
                    await progress_updater.update(
                        status="failed",
                        error="导出视频失败",
                        logs=[f"❌ [模拟] 导出视频失败"]
                    )
                return {
                    "success": False,
                    "error": "导出视频失败",
                    "task_id": task_id
                }
            
            # 生成缩略图
            thumbnail_path = self._generate_thumbnail(output_path)
            
            # 转换为URL
            video_url = self._convert_path_to_url(output_path, "video")
            thumbnail_url = self._convert_path_to_url(thumbnail_path, "thumbnail")
            
            if progress_updater:
                await progress_updater.update(
                    status="completed",
                    progress=100,
                    logs=[f"[模拟] 视频生成完成"],
                    result={
                        "video_url": video_url,
                        "thumbnail_url": thumbnail_url,
                        "is_mock": True
                    }
                )
            
            return {
                "success": True,
                "task_id": task_id,
                "video_path": output_path,
                "video_url": video_url,
                "thumbnail_path": thumbnail_path,
                "thumbnail_url": thumbnail_url,
                "is_mock": True
            }
        except Exception as e:
            error_msg = f"[模拟] 生成视频时出错: {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            
            if progress_updater:
                await progress_updater.update(
                    status="failed",
                    error=error_msg,
                    logs=[f"❌ {error_msg}"]
                )
            
            return {
                "success": False,
                "error": error_msg,
                "task_id": task_id
            }
    
    def _init_model_status(self):
        """初始化模型状态跟踪"""
        # T2V模型状态初始化
        t2v_model_14b = os.environ.get("WAN_T2V_14B_MODEL", "Wan2.1-T2V-14B")
        t2v_model_1_3b = os.environ.get("WAN_T2V_1_3B_MODEL", "Wan2.1-T2V-1.3B")
        
        # 解析支持的分辨率
        resolutions = ["720p", "1080p"] # 默认支持的分辨率
        
        self.model_status = {
            "t2v": {
                "14B": {res: {"loaded": False, "loading": False, "error": None} for res in resolutions},
                "1.3B": {res: {"loaded": False, "loading": False, "error": None} for res in resolutions}
            },
            "i2v": {res: {"loaded": False, "loading": False, "error": None} for res in resolutions}
        }
        
        # 日志记录模型状态
        logger.info(f"初始化模型状态跟踪:")
        logger.info(f"  文本到视频 (14B): {', '.join(resolutions)}")
        logger.info(f"  文本到视频 (1.3B): {', '.join(resolutions)}")
        logger.info(f"  图像到视频: {', '.join(resolutions)}")
        
        # 检查模型目录是否存在
        t2v_14b_path = os.path.join(self.model_cache_dir, t2v_model_14b)
        t2v_1_3b_path = os.path.join(self.model_cache_dir, t2v_model_1_3b)
        
        # 记录模型路径信息
        logger.info(f"模型路径:")
        logger.info(f"  T2V 14B: {t2v_14b_path}")
        logger.info(f"  T2V 1.3B: {t2v_1_3b_path}")
        
        # 检查模型文件是否存在
        t2v_14b_exists = os.path.exists(t2v_14b_path) and os.path.isdir(t2v_14b_path)
        t2v_1_3b_exists = os.path.exists(t2v_1_3b_path) and os.path.isdir(t2v_1_3b_path)
        
        # 日志记录模型文件状态
        logger.info(f"模型目录状态:")
        logger.info(f"  T2V 14B: {'存在' if t2v_14b_exists else '不存在'}")
        logger.info(f"  T2V 1.3B: {'存在' if t2v_1_3b_exists else '不存在'}")
    
    def _get_torch_dtype(self, precision: str):
        """获取torch数据类型"""
        if precision == "float16":
            return torch.float16
        elif precision == "bfloat16" and hasattr(torch, "bfloat16"):
            return torch.bfloat16
        else:
            return torch.float32
    
    async def check_model_status(self, model_type: str, model_size: str = "14B", resolution: str = "720p") -> Dict[str, Any]:
        """
        检查模型状态，检查本地模型文件是否存在和完整。
        
        Args:
            model_type: 模型类型，'t2v'或'i2v'
            model_size: 模型大小，仅用于t2v
            resolution: 分辨率，用于i2v或t2v的分辨率参数
            
        Returns:
            Dict: 模型状态信息
        """
        logger.info(f"检查模型状态: {model_type}, 规格: {model_size}, 分辨率: {resolution}")
        
        # 更新检查时间
        if model_type == "t2v" and model_size in self.model_status["t2v"]:
            if resolution in self.model_status["t2v"][model_size]:
                self.model_status["t2v"][model_size][resolution]["last_check"] = time.time()
        elif model_type == "i2v" and resolution in self.model_status["i2v"]:
            self.model_status["i2v"][resolution]["last_check"] = time.time()
        
        # 检查模型是否已加载到内存中
        if model_type == "t2v":
            if model_type in self.models and model_size in self.models[model_type] and resolution in self.models[model_type][model_size]:
                logger.info(f"模型已加载到内存中: {model_type} {model_size} {resolution}")
                if model_size in self.model_status["t2v"] and resolution in self.model_status["t2v"][model_size]:
                    self.model_status["t2v"][model_size][resolution]["loaded"] = True
                return {"status": "loaded", "error": None}
        elif model_type == "i2v":
            if model_type in self.models and resolution in self.models[model_type]:
                logger.info(f"模型已加载到内存中: {model_type} {resolution}")
                if resolution in self.model_status["i2v"]:
                    self.model_status["i2v"][resolution]["loaded"] = True
                return {"status": "loaded", "error": None}
        
        # 选择正确的模型ID
        model_id = self._get_model_id(model_type, model_size, resolution)
        model_path = os.path.join(self.model_cache_dir, model_id)
        
        logger.info(f"检查本地模型目录: {model_path}")
        
        # 检查本地模型目录和文件
        if not os.path.exists(model_path):
            status = "not_found"
            error = f"模型目录不存在: {model_path}"
            logger.warning(error)
            
            if model_type == "t2v" and model_size in self.model_status["t2v"] and resolution in self.model_status["t2v"][model_size]:
                self.model_status["t2v"][model_size][resolution]["loaded"] = False
                self.model_status["t2v"][model_size][resolution]["error"] = error
            elif model_type == "i2v" and resolution in self.model_status["i2v"]:
                self.model_status["i2v"][resolution]["loaded"] = False
                self.model_status["i2v"][resolution]["error"] = error
                
            return {"status": status, "error": error}
        
        # 检查必要的子目录
        required_subdirs = ["vae", "scheduler", "unet"]
        missing_subdirs = []
        
        for subdir in required_subdirs:
            subdir_path = os.path.join(model_path, subdir)
            if not os.path.exists(subdir_path) or not os.path.isdir(subdir_path):
                missing_subdirs.append(subdir)
        
        if missing_subdirs:
            status = "incomplete"
            error = f"模型目录结构不完整，缺少以下子目录: {', '.join(missing_subdirs)}"
            logger.warning(error)
            
            # 尝试自动修复
            logger.info(f"尝试自动修复模型目录结构...")
            if auto_fix_model_structure(model_path):
                logger.info(f"模型目录结构自动修复成功，重新检查...")
                
                # 重新检查子目录是否存在
                missing_subdirs = []
                for subdir in required_subdirs:
                    subdir_path = os.path.join(model_path, subdir)
                    if not os.path.exists(subdir_path) or not os.path.isdir(subdir_path):
                        missing_subdirs.append(subdir)
                
                if not missing_subdirs:
                    logger.info(f"修复成功，模型目录结构现在完整")
                    return {"status": "ready", "error": None}
                else:
                    # 修复失败，更新错误信息
                    error = f"自动修复后仍缺少子目录: {', '.join(missing_subdirs)}"
                    logger.error(error)
            else:
                logger.error(f"自动修复失败")
            
            if model_type == "t2v" and model_size in self.model_status["t2v"] and resolution in self.model_status["t2v"][model_size]:
                self.model_status["t2v"][model_size][resolution]["loaded"] = False
                self.model_status["t2v"][model_size][resolution]["error"] = error
            elif model_type == "i2v" and resolution in self.model_status["i2v"]:
                self.model_status["i2v"][resolution]["loaded"] = False
                self.model_status["i2v"][resolution]["error"] = error
                
            return {"status": status, "error": error}
        
        # 检查必要的文件
        files_to_check = {
            "vae/config.json": "VAE配置",
            "scheduler/scheduler_config.json": "调度器配置",
            "unet/config.json": "UNet配置"
        }
        
        missing_files = []
        
        for file_path, desc in files_to_check.items():
            full_path = os.path.join(model_path, file_path)
            if not os.path.exists(full_path):
                missing_files.append(f"{desc} ({file_path})")
        
        if missing_files:
            status = "incomplete"
            error = f"模型文件不完整，缺少以下文件: {', '.join(missing_files)}"
            logger.warning(error)
            
            # 尝试自动修复
            logger.info(f"尝试自动修复缺失的配置文件...")
            if auto_fix_model_structure(model_path):
                logger.info(f"配置文件自动修复成功，重新检查...")
                
                # 重新检查文件是否存在
                missing_files = []
                for file_path, desc in files_to_check.items():
                    full_path = os.path.join(model_path, file_path)
                    if not os.path.exists(full_path):
                        missing_files.append(f"{desc} ({file_path})")
                
                if not missing_files:
                    logger.info(f"修复成功，模型文件现在完整")
                    return {"status": "ready", "error": None}
                else:
                    # 修复失败，更新错误信息
                    error = f"自动修复后仍缺少文件: {', '.join(missing_files)}"
                    logger.error(error)
            else:
                logger.error(f"自动修复失败")
            
            if model_type == "t2v" and model_size in self.model_status["t2v"] and resolution in self.model_status["t2v"][model_size]:
                self.model_status["t2v"][model_size][resolution]["loaded"] = False
                self.model_status["t2v"][model_size][resolution]["error"] = error
            elif model_type == "i2v" and resolution in self.model_status["i2v"]:
                self.model_status["i2v"][resolution]["loaded"] = False
                self.model_status["i2v"][resolution]["error"] = error
                
            return {"status": status, "error": error}
        
        # 所有检查通过，模型文件存在但未加载
        status = "ready"
        logger.info(f"模型文件完整，准备加载: {model_path}")
        
        if model_type == "t2v" and model_size in self.model_status["t2v"] and resolution in self.model_status["t2v"][model_size]:
            self.model_status["t2v"][model_size][resolution]["loaded"] = False
            self.model_status["t2v"][model_size][resolution]["error"] = None
        elif model_type == "i2v" and resolution in self.model_status["i2v"]:
            self.model_status["i2v"][resolution]["loaded"] = False
            self.model_status["i2v"][resolution]["error"] = None
        
        return {"status": status, "error": None}
    
    def _get_model_id(self, model_type: str, model_size: str = "14B", resolution: str = "720p") -> str:
        """
        获取模型ID
        
        Args:
            model_type: 模型类型 (t2v 或 i2v)
            model_size: 模型大小 (14B 或 1.3B)
            resolution: 分辨率 (720p 或 1080p)
            
        Returns:
            str: 模型ID
        """
        if model_type == "t2v":
            # 文本到视频模型ID
            if model_size == "14B":
                return os.environ.get("WAN_T2V_14B_MODEL", "Wan2.1-T2V-14B")
            elif model_size == "1.3B":
                return os.environ.get("WAN_T2V_1_3B_MODEL", "Wan2.1-T2V-1.3B")
            else:
                logger.warning(f"未知的T2V模型大小: {model_size}，使用默认14B模型")
                return os.environ.get("WAN_T2V_14B_MODEL", "Wan2.1-T2V-14B")
        elif model_type == "i2v":
            # 图像到视频模型ID
            return "Wan2.1-I2V"
        else:
            logger.warning(f"未知的模型类型: {model_type}，使用默认T2V模型")
            return os.environ.get("WAN_T2V_14B_MODEL", "Wan2.1-T2V-14B")
    
    async def initialize_t2v(self, task_id: str, progress_updater: ProgressUpdater, model_size: str = "14B", resolution: str = "720p"):
        """
        初始化文本到视频模型
        
        Args:
            task_id: 任务ID
            progress_updater: 进度更新器
            model_size: 模型规格 (14B 或 1.3B)
            resolution: 分辨率 (720p 或 1080p)
        """
        logger.info(f"初始化T2V模型: 规格 {model_size}, 分辨率 {resolution}")
        
        # 检查模型是否已加载
        if 't2v' in self.models and model_size in self.models['t2v'] and resolution in self.models['t2v'][model_size]:
            logger.info(f"T2V模型 ({model_size}, {resolution}) 已加载，跳过初始化")
            return True
        
        # 检查是否已在加载中
        if self.model_status["t2v"][model_size][resolution]["loading"]:
            logger.info(f"T2V模型 ({model_size}, {resolution}) 正在加载中，请等待")
            # 定期检查模型是否已加载完成
            retry_count = 0
            while retry_count < self.max_retry_attempts:
                if self.model_status["t2v"][model_size][resolution]["loaded"]:
                    logger.info(f"T2V模型 ({model_size}, {resolution}) 已加载完成")
                    return True
                await asyncio.sleep(2)  # 等待2秒再检查
                retry_count += 1
            logger.warning(f"等待T2V模型 ({model_size}, {resolution}) 加载超时")
        
        # 设置为加载中状态
        self.model_status["t2v"][model_size][resolution]["loading"] = True
        self.model_status["t2v"][model_size][resolution]["error"] = None
        
        # 更新进度
        await progress_updater.update(
            status="loading",
            progress=5,
            logs=[f"初始化文本到视频模型 ({model_size}, {resolution})..."]
        )
        
        try:
            # 记录开始时间
            start_time = time.time()
            
            # 如果已经是模拟模式，直接创建模拟模型
            if self.mock_mode:
                logger.info(f"已处于模拟模式，跳过模型完整性检查，直接创建模拟模型")
                return await self._create_mock_model(model_size, resolution, progress_updater)
            
            # 获取模型ID和路径
            model_id = self._get_model_id("t2v", model_size, resolution)
            
            # 优先检查本地模型目录
            local_model_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 
                                        "local_models", "wan", "Wan2.1", model_id)
            if os.path.exists(local_model_dir):
                logger.info(f"使用本地模型目录: {local_model_dir}")
                model_path = local_model_dir
            else:
                model_path = os.path.join(self.model_cache_dir, model_id)
            
            logger.info(f"模型路径: {model_path}")
            
            # 检查模型完整性
            integrity_result = await self.check_model_integrity(model_type="t2v", model_size=model_size, resolution=resolution)
            if not integrity_result["success"]:
                error_msg = f"模型文件不完整: {integrity_result['message']}"
                logger.error(error_msg)
                
                # 更新进度，提供详细的错误信息
                await progress_updater.update(
                    status="failed",
                    error=error_msg,
                    logs=[
                        f"❌ 模型文件不完整: {integrity_result['message']}",
                        "⚠️ 系统将切换到模拟模式，但这将生成非真实的AI视频"
                    ]
                )
                
                # 如果用户明确要求不使用模拟模式，则返回失败
                force_real_mode = os.environ.get("WAN_FORCE_REAL_MODE", "false").lower() == "true"
                if force_real_mode:
                    logger.error("用户要求使用真实模式，但模型文件不完整，任务失败")
                    self.model_status["t2v"][model_size][resolution]["loading"] = False
                    self.model_status["t2v"][model_size][resolution]["error"] = error_msg
                    
                    await progress_updater.update(
                        status="failed",
                        error="模型文件不完整，且系统设置为强制使用真实模式",
                        logs=[
                            "❌ 模型文件不完整，无法生成视频",
                            "⚠️ 系统设置为强制使用真实模式，不允许使用模拟模式"
                        ]
                    )
                    return False
                
                # 切换到模拟模式
                logger.warning("模型文件不完整，切换到模拟模式")
                self.mock_mode = True
                os.environ["WAN_MOCK_MODE"] = "true"
                
                # 创建模拟模型并返回成功
                return await self._create_mock_model(model_size, resolution, progress_updater)
            
            # 模型文件完整，继续加载
            logger.info(f"模型文件完整性检查通过，开始加载模型")
            
            # 更新进度
            await progress_updater.update(
                progress=10,
                logs=[f"模型文件完整性检查通过，开始加载模型..."]
            )
            
            # 如果启用了模拟模式，则创建模拟模型
            if self.mock_mode:
                logger.info("模拟模式已启用，创建模拟模型")
                return await self._create_mock_model(model_size, resolution, progress_updater)
            
            # 导入必要的库
            try:
                # 尝试导入WAN模型
                try:
                    # 添加本地模型路径到系统路径
                    wan_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 
                                         "local_models", "wan", "Wan2.1")
                    if wan_path not in sys.path:
                        sys.path.append(wan_path)
                        logger.info(f"添加WAN模型路径到系统路径: {wan_path}")
                    
                    # 尝试导入WAN模块
                    try:
                        import wan
                        from wan.configs import WAN_CONFIGS
                        from wan.text2video import WanT2V
                        logger.info("成功导入WAN模块")
                        
                        # 使用WAN模型
                        config = WAN_CONFIGS[f"t2v-{model_size}"]
                        logger.info(f"使用WAN配置: {config}")
                        
                        # 初始化WAN模型
                        model = WanT2V(
                            config=config,
                            checkpoint_dir=model_path,
                            device_id=0 if self.device == "cuda" else -1,
                            t5_cpu=self.device == "cpu"
                        )
                        logger.info(f"WAN模型初始化成功")
                        
                        # 保存模型到内存
                        if 't2v' not in self.models:
                            self.models['t2v'] = {}
                        if model_size not in self.models['t2v']:
                            self.models['t2v'][model_size] = {}
                        
                        self.models['t2v'][model_size][resolution] = model
                        
                        # 更新状态
                        self.model_status["t2v"][model_size][resolution]["loaded"] = True
                        self.model_status["t2v"][model_size][resolution]["loading"] = False
                        self.is_initialized = True
                        
                        # 记录加载时间
                        load_time = time.time() - start_time
                        logger.info(f"T2V模型 ({model_size}, {resolution}) 加载完成，耗时: {load_time:.2f}秒")
                        
                        # 更新进度
                        await progress_updater.update(
                            progress=20,
                            logs=[f"模型加载成功，准备生成视频"]
                        )
                        
                        return True
                    except ImportError as e:
                        logger.error(f"导入WAN模块失败: {e}")
                        raise e
                    except Exception as e:
                        logger.error(f"初始化WAN模型失败: {e}")
                        logger.error(traceback.format_exc())
                        raise e
                except Exception as e:
                    logger.error(f"加载WAN模型失败: {e}")
                    logger.error(traceback.format_exc())
                    
                    # 尝试使用diffusers
                    logger.info("尝试使用diffusers加载模型")
                    import torch
                    from diffusers import DiffusionPipeline, DPMSolverMultistepScheduler
                    logger.info("成功导入diffusers库")
                    
                    # 更新进度
                    await progress_updater.update(
                        progress=15,
                        logs=[f"使用diffusers加载模型..."]
                    )
                    
                    # 这里应该添加使用diffusers加载模型的代码
                    # 但由于我们优先使用WAN模型，这部分代码暂时留空
                    # 如果需要，可以在后续实现
                    
                    # 由于diffusers加载失败，切换到模拟模式
                    logger.warning("使用diffusers加载模型失败，切换到模拟模式")
                    self.mock_mode = True
                    os.environ["WAN_MOCK_MODE"] = "true"
                    
                    # 创建模拟模型并返回成功
                    return await self._create_mock_model(model_size, resolution, progress_updater)
            except ImportError as e:
                error_msg = f"导入必要的库失败: {str(e)}"
                logger.error(error_msg)
                self.model_status["t2v"][model_size][resolution]["error"] = error_msg
                self.model_status["t2v"][model_size][resolution]["loading"] = False
                await progress_updater.update(
                    status="failed",
                    error=error_msg
                )
                
                # 如果用户明确要求不使用模拟模式，则返回失败
                if os.environ.get("WAN_FORCE_REAL_MODE", "false").lower() == "true":
                    logger.error("用户要求使用真实模式，但导入库失败，任务失败")
                    return False
                
                # 启用模拟模式继续
                logger.info("导入库失败，将使用模拟模式继续")
                self.mock_mode = True
                os.environ["WAN_MOCK_MODE"] = "true"
                
                # 创建模拟模型并返回成功
                return await self._create_mock_model(model_size, resolution, progress_updater)
            
        except Exception as e:
            error_msg = f"初始化T2V模型时出错: {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            
            # 更新状态
            self.model_status["t2v"][model_size][resolution]["loading"] = False
            self.model_status["t2v"][model_size][resolution]["error"] = error_msg
            
            # 更新进度
            await progress_updater.update(
                status="failed",
                error=error_msg,
                logs=[f"❌ 初始化模型失败: {error_msg}"]
            )
            
            # 如果用户明确要求不使用模拟模式，则返回失败
            if os.environ.get("WAN_FORCE_REAL_MODE", "false").lower() == "true":
                logger.error("用户要求使用真实模式，但初始化失败，任务失败")
                return False
            
            # 否则切换到模拟模式
            logger.warning("初始化模型失败，将使用模拟模式继续")
            self.mock_mode = True
            os.environ["WAN_MOCK_MODE"] = "true"
            
            # 创建模拟模型并返回成功
            return await self._create_mock_model(model_size, resolution, progress_updater)
    
    async def _create_mock_model(self, model_size: str, resolution: str, progress_updater: ProgressUpdater) -> bool:
        """创建模拟模型并更新状态"""
        try:
            start_time = time.time()
            
            # 自定义AutoencoderKL类，用于加载VAE
            class AutoencoderKLWan:
                @classmethod
                def from_pretrained(cls, model_path, **kwargs):
                    logger.info(f"[模拟] 加载VAE: {model_path}")
                    return cls()
                    
            # 自定义Pipeline类，用于加载模型
            class WanPipeline:
                def __init__(self, **kwargs):
                    logger.info("[模拟] 初始化WAN Pipeline")
                    self.device = "cpu"
                    self.vae = kwargs.get("vae")
                    self.scheduler = kwargs.get("scheduler")
                    self.mock_mode = True  # 标记这是模拟模式
                
                @classmethod
                def from_pretrained(cls, model_path, **kwargs):
                    logger.info(f"[模拟] 从预训练模型加载: {model_path}")
                    return cls(**kwargs)
                    
                def to(self, device):
                    logger.info(f"[模拟] 将模型移至设备: {device}")
                    self.device = device
                    return self
                
                # 添加模拟的生成方法
                def __call__(self, prompt, negative_prompt="", height=720, width=1280, 
                             num_frames=16, guidance_scale=7.5, generator=None):
                    logger.info(f"[模拟] 生成视频: prompt='{prompt}', 大小={width}x{height}, 帧数={num_frames}")
                    
                    # 创建更有结构的模拟视频
                    import numpy as np
                    import cv2
                    from PIL import Image, ImageDraw, ImageFont
                    
                    # 创建一个帧数组
                    frames = np.zeros((1, num_frames, height, width, 3), dtype=np.uint8)
                    
                    # 为每一帧添加一些内容
                    for i in range(num_frames):
                        # 创建PIL图像以便添加文字
                        img = Image.new('RGB', (width, height), color=(0, 0, 0))
                        draw = ImageDraw.Draw(img)
                        
                        # 添加渐变背景
                        for y in range(height):
                            color_value = int(y / height * 255)
                            for x in range(width):
                                r = (color_value + i * 5) % 255
                                g = (color_value + i * 3) % 255
                                b = (color_value + i * 7) % 255
                                img.putpixel((x, y), (r, g, b))
                        
                        # 添加提示词和模拟模式提示
                        try:
                            # 尝试使用系统字体
                            font_size = 40
                            try:
                                font = ImageFont.truetype("arial.ttf", font_size)
                            except:
                                # 退回到默认字体
                                font = ImageFont.load_default()
                            
                            # 添加提示词
                            prompt_text = f"提示词: {prompt}"
                            draw.text((width//2 - 300, height//2 - 150), prompt_text, 
                                     fill=(255, 255, 255), font=font)
                            
                            # 添加模拟模式提示
                            mock_text = "⚠️ 模拟模式 - 非真实AI生成视频 ⚠️"
                            draw.text((width//2 - 300, height//2 - 50), mock_text, 
                                     fill=(255, 255, 0), font=font)
                            
                            # 添加原因说明
                            reason_text = "原因: 模型文件不完整或无法加载"
                            draw.text((width//2 - 300, height//2 + 50), reason_text, 
                                     fill=(255, 255, 255), font=font)
                            
                            # 添加帧计数
                            frame_text = f"帧 {i+1}/{num_frames}"
                            draw.text((width//2 - 300, height//2 + 150), frame_text, 
                                     fill=(255, 255, 255), font=font)
                        except Exception as e:
                            # 如果添加文字失败，记录错误但继续生成视频
                            logger.error(f"在模拟视频中添加文字时出错: {e}")
                        
                        # 转换为numpy数组并存储
                        frames[0, i] = np.array(img)
                    
                    return type('obj', (object,), {'frames': frames})
            
            # 创建简单的调度器
            class MockScheduler:
                def __init__(self, **kwargs):
                    logger.info("[模拟] 创建调度器")
                    for key, value in kwargs.items():
                        setattr(self, key, value)
            
            # 创建模拟调度器和VAE
            scheduler = MockScheduler(beta_start=0.00085, beta_end=0.012, beta_schedule="linear")
            vae = AutoencoderKLWan()
            
            # 创建管道
            pipeline = WanPipeline(scheduler=scheduler, vae=vae)
            pipeline = pipeline.to(self.device)
            
            # 记录模型加载完成
            load_time = time.time() - start_time
            logger.info(f"[模拟] T2V模型 ({model_size}, {resolution}) 加载完成，耗时: {load_time:.2f}秒")
            
            # 保存模型到内存
            if 't2v' not in self.models:
                self.models['t2v'] = {}
            if model_size not in self.models['t2v']:
                self.models['t2v'][model_size] = {}
            
            self.models['t2v'][model_size][resolution] = pipeline
            
            # 更新状态
            self.model_status["t2v"][model_size][resolution]["loaded"] = True
            self.model_status["t2v"][model_size][resolution]["loading"] = False
            self.is_initialized = True
            
            # 更新进度
            await progress_updater.update(
                status="running",
                progress=20,
                logs=[f"[模拟] T2V模型 ({model_size}, {resolution}) 加载完成，准备生成视频"]
            )
            
            return True
        except Exception as e:
            logger.error(f"创建模拟模型失败: {e}")
            return False
    
    def _get_manual_download_guide(self, model_type: str, model_size: str = "14B", resolution: str = "720p") -> str:
        """获取手动下载指南"""
        model_id = self._get_model_id(model_type, model_size, resolution)
        cache_dir = self.model_cache_dir
        
        # 是否是本地路径
        local_path = os.path.normpath(os.path.join(cache_dir, model_id))
        guide = f"""
手动设置指南:
1. 权限问题：无法访问或写入目录 {local_path}
2. 建议解决方案:
   a. 以管理员权限运行应用程序
   b. 修改目录权限，确保当前用户有读写权限
   c. 在环境变量中设置WAN_MODEL_CACHE_DIR为具有写入权限的目录

3. 目录结构要求:
   {local_path}/
      ├── vae/          # VAE模型目录
      │   └── config.json  # 需要创建
      ├── scheduler/    # 调度器配置
      │   └── scheduler_config.json  # 需要创建
      └── unet/         # UNet模型目录
          └── config.json  # 需要创建
"""
        return guide
    
    def _convert_path_to_url(self, file_path: str, file_type: str = "video") -> str:
        """
        将本地文件路径转换为可通过HTTP访问的URL
        
        Args:
            file_path: 本地文件路径
            file_type: 文件类型(video/thumbnail)
            
        Returns:
            str: HTTP URL
        """
        if not file_path:
            logger.warning("尝试转换空文件路径为URL")
            return ""
            
        # 提取文件名
        file_name = os.path.basename(file_path)
        logger.info(f"转换文件路径为URL: {file_path} -> {file_name}")
        
        # 检查文件是否存在
        if not os.path.exists(file_path):
            logger.warning(f"文件不存在: {file_path}")
            # 尝试创建媒体目录
            media_dirs = ['media', 'media/videos', 'media/thumbnails']
            for d in media_dirs:
                os.makedirs(d, exist_ok=True)
                logger.info(f"确保媒体目录存在: {d}")
        else:
            logger.info(f"文件存在，大小: {os.path.getsize(file_path)} 字节")
            
            # 如果文件不在media目录中，复制到media目录
            if file_type == "video" and "media/videos" not in file_path:
                target_path = os.path.join("media", "videos", file_name)
                try:
                    import shutil
                    # 确保目标目录存在
                    os.makedirs(os.path.dirname(target_path), exist_ok=True)
                    # 复制文件
                    shutil.copy2(file_path, target_path)
                    logger.info(f"文件已复制到媒体目录: {target_path}")
                    
                    # 验证文件是否成功复制
                    if os.path.exists(target_path) and os.path.getsize(target_path) > 0:
                        logger.info(f"文件复制成功，大小: {os.path.getsize(target_path)} 字节")
                    else:
                        logger.error(f"文件复制失败或大小为0: {target_path}")
                except Exception as e:
                    logger.error(f"复制文件到媒体目录失败: {e}")
            elif file_type == "thumbnail" and "media/thumbnails" not in file_path:
                target_path = os.path.join("media", "thumbnails", file_name)
                try:
                    import shutil
                    # 确保目标目录存在
                    os.makedirs(os.path.dirname(target_path), exist_ok=True)
                    # 复制文件
                    shutil.copy2(file_path, target_path)
                    logger.info(f"文件已复制到媒体目录: {target_path}")
                    
                    # 验证文件是否成功复制
                    if os.path.exists(target_path) and os.path.getsize(target_path) > 0:
                        logger.info(f"文件复制成功，大小: {os.path.getsize(target_path)} 字节")
                    else:
                        logger.error(f"文件复制失败或大小为0: {target_path}")
                except Exception as e:
                    logger.error(f"复制文件到媒体目录失败: {e}")
        
        # 构建URL (使用/api/media/前缀用于访问媒体文件)
        url = ""
        if file_type == "video":
            url = f"/api/media/videos/{file_name}"
        elif file_type == "thumbnail":
            url = f"/api/media/thumbnails/{file_name}"
        else:
            url = f"/api/media/files/{file_name}"
            
        logger.info(f"生成的URL: {url}")
        
        # 确保URL是绝对路径
        if not url.startswith(('http://', 'https://')):
            # 获取API基础URL
            api_base = os.environ.get('API_BASE_URL', '')
            if api_base:
                if not api_base.endswith('/'):
                    api_base += '/'
                if url.startswith('/'):
                    url = url[1:]  # 移除开头的斜杠以避免重复
                url = f"{api_base}{url}"
                logger.info(f"转换为绝对URL: {url}")
        
        return url
    
    async def generate_video_from_text(
        self,
        task_id: str,
        prompt: str,
        negative_prompt: str = "",
        num_frames: int = 81,  # 约5秒视频(16fps)
        height: int = 720,
        width: int = 1280,
        guidance_scale: float = 5.0,
        fps: int = 16,
        seed: Optional[int] = None,
        model_size: str = "14B",
        resolution: str = "720p", 
        output_format: str = "mp4"
    ) -> Dict[str, Any]:
        """
        从文本生成视频
        
        Args:
            task_id: 任务ID
            prompt: 提示词
            negative_prompt: 负面提示词
            num_frames: 帧数
            height: 高度
            width: 宽度
            guidance_scale: 引导比例
            fps: 帧率
            seed: 随机种子
            model_size: 模型大小 (14B/1.3B)
            resolution: 分辨率 (720p/1080p)
            output_format: 输出格式 (mp4/gif)
            
        Returns:
            Dict: 包含生成结果的字典
        """
        # 创建进度跟踪器
        progress_tracker = get_progress_tracker()
        progress_updater = ProgressUpdater(task_id)
        
        # 记录参数
        logger.info(f"从文本生成视频: task_id={task_id}, prompt='{prompt}', model_size={model_size}")
        logger.info(f"参数: frames={num_frames}, size={width}x{height}, fps={fps}, guidance={guidance_scale}")
        
        # 检查是否强制使用真实模式
        force_real_mode = os.environ.get("WAN_FORCE_REAL_MODE", "false").lower() == "true"
        
        if self.mock_mode:
            if force_real_mode:
                error_msg = "系统设置为强制使用真实模式，但模型文件不完整或无法加载"
                logger.error(error_msg)
                await progress_updater.update(
                    status="failed",
                    error=error_msg,
                    logs=[
                        "❌ 无法生成视频: 系统设置为强制使用真实模式，但模型文件不完整",
                        "⚠️ 请确保正确安装WAN 2.1模型文件，或禁用强制真实模式设置"
                    ]
                )
                return {
                    "success": False,
                    "error": error_msg,
                    "task_id": task_id
                }
            else:
                logger.warning(f"使用模拟模式生成视频: {prompt}")
        
        try:
            # 初始化模型
            await progress_updater.update(
                status="running",
                progress=0,
                logs=[f"初始化T2V模型 ({model_size}, {resolution})..."]
            )
            
            # 确保模型初始化
            model_initialized = await self.initialize_t2v(task_id, progress_updater, model_size, resolution)
            if not model_initialized:
                error_msg = f"模型初始化失败: {self.model_status['t2v'][model_size][resolution]['error']}"
                logger.error(error_msg)
                await progress_updater.update(
                    status="failed",
                    error=error_msg,
                    logs=[f"❌ {error_msg}"]
                )
                return {
                    "success": False,
                    "error": error_msg,
                    "task_id": task_id
                }
            
            # 更新进度
            await progress_updater.update(
                progress=30,
                logs=[f"开始生成视频..."]
            )
            
            # 检查是否使用模拟模式
            if self.mock_mode:
                # 如果强制使用真实模式，则返回错误
                if force_real_mode:
                    error_msg = "系统设置为强制使用真实模式，但当前处于模拟模式"
                    logger.error(error_msg)
                    await progress_updater.update(
                        status="failed",
                        error=error_msg,
                        logs=[
                            "❌ 无法生成视频: 系统设置为强制使用真实模式，但当前处于模拟模式",
                            "⚠️ 请确保正确安装WAN 2.1模型文件，或禁用强制真实模式设置"
                        ]
                    )
                    return {
                        "success": False,
                        "error": error_msg,
                        "task_id": task_id
                    }
                
                logger.info("使用模拟模式生成视频")
                # 直接使用模拟模式生成视频，不进行模型完整性检查
                return await self._generate_mock_video(
                    task_id=task_id,
                    prompt=prompt,
                    negative_prompt=negative_prompt,
                    num_frames=num_frames,
                    height=height,
                    width=width,
                    guidance_scale=guidance_scale,
                    fps=fps,
                    seed=seed,
                    model_size=model_size,
                    resolution=resolution,
                    output_format=output_format,
                    progress_updater=progress_updater
                )
            
            # 使用真实模型生成视频
            try:
                # 获取模型
                model = self.models['t2v'][model_size][resolution]
                
                # 检查模型类型
                if hasattr(model, 'generate'):
                    # 使用WAN模型
                    logger.info("使用WAN模型生成视频")
                    
                    # 更新进度
                    await progress_updater.update(
                        progress=40,
                        logs=[f"使用WAN模型生成视频..."]
                    )
                    
                    # 设置随机种子
                    if seed is None:
                        import random
                        seed = random.randint(0, 2**32 - 1)
                    logger.info(f"使用随机种子: {seed}")
                    
                    # 生成视频
                    frames = model.generate(
                        input_prompt=prompt,
                        size=(width, height),
                        frame_num=num_frames,
                        shift=5.0,  # 默认值
                        sampling_steps=50,  # 默认值
                        guide_scale=guidance_scale,
                        n_prompt=negative_prompt,
                        seed=seed
                    )
                    
                    # 更新进度
                    await progress_updater.update(
                        progress=80,
                        logs=[f"视频生成完成，准备导出..."]
                    )
                    
                    # 确保frames是正确的格式
                    if hasattr(frames, 'frames'):
                        frames = frames.frames
                    
                    # 将PyTorch张量转换为NumPy数组
                    import numpy as np
                    
                    # 详细检查和记录frames的类型和形状
                    logger.info(f"生成的frames类型: {type(frames)}")
                    if hasattr(frames, 'shape'):
                        logger.info(f"frames形状: {frames.shape}")
                    elif isinstance(frames, list):
                        logger.info(f"frames是列表，长度: {len(frames)}")
                        if len(frames) > 0:
                            logger.info(f"第一个元素类型: {type(frames[0])}")
                            if hasattr(frames[0], 'shape'):
                                logger.info(f"第一个元素形状: {frames[0].shape}")
                    
                    # 处理不同类型的frames
                    if isinstance(frames, torch.Tensor):
                        frames_np = frames.cpu().numpy()
                        logger.info(f"PyTorch张量转换为NumPy数组: {frames_np.shape}")
                    elif isinstance(frames, np.ndarray):
                        frames_np = frames
                        logger.info(f"frames已经是NumPy数组: {frames_np.shape}")
                    elif isinstance(frames, list) and len(frames) > 0 and isinstance(frames[0], torch.Tensor):
                        # 尝试转换列表中的第一个张量
                        frames_np = frames[0].cpu().numpy()
                        logger.info(f"从列表中提取第一个张量并转换: {frames_np.shape}")
                    else:
                        # 无法处理的类型，创建一个简单的黑色帧
                        logger.warning(f"无法处理的frames类型，创建黑色帧")
                        frames_np = np.zeros((num_frames, height, width, 3), dtype=np.uint8)
                    
                    # 检查是否需要转置
                    if len(frames_np.shape) == 4 and frames_np.shape[0] == 3:
                        # 可能是(C, T, H, W)格式，转换为(T, H, W, C)
                        logger.info(f"检测到(C, T, H, W)格式，转换为(T, H, W, C)")
                        frames_np = np.transpose(frames_np, (1, 2, 3, 0))
                        logger.info(f"转换后形状: {frames_np.shape}")
                    
                    # 确保值范围合适
                    if frames_np.max() > 1.0 and frames_np.max() <= 255.0:
                        # 已经是适合的范围，转为uint8
                        frames_np = frames_np.astype(np.uint8)
                    elif frames_np.max() <= 1.0:
                        frames_np = (frames_np * 255).astype(np.uint8)
                        logger.info(f"归一化值转换为uint8: {frames_np.dtype}")
                    else:
                        frames_np = np.clip(frames_np, 0, 255).astype(np.uint8)
                        logger.info(f"裁剪值并转换为uint8: {frames_np.dtype}")
                    
                    # 保存视频
                    output_path = os.path.join(tempfile.gettempdir(), f"{task_id}.{output_format}")
                    logger.info(f"保存视频到: {output_path}")
                    
                    # 使用自定义导出函数
                    export_success = self._export_video_with_ffmpeg_params(
                        frames_np, output_path, fps=fps
                    )
                    
                    if not export_success:
                        logger.error("导出视频失败")
                        if progress_updater:
                            await progress_updater.update(
                                status="failed",
                                error="导出视频失败",
                                logs=[f"❌ 导出视频失败"]
                            )
                        return {
                            "success": False,
                            "error": "导出视频失败",
                            "task_id": task_id
                        }
                    
                    # 生成缩略图
                    thumbnail_path = self._generate_thumbnail(output_path)
                    
                    # 转换为URL
                    video_url = self._convert_path_to_url(output_path, "video")
                    thumbnail_url = self._convert_path_to_url(thumbnail_path, "thumbnail")
                    
                    if progress_updater:
                        await progress_updater.update(
                            status="completed",
                            progress=100,
                            logs=[f"视频生成完成"],
                            result={
                                "video_url": video_url,
                                "thumbnail_url": thumbnail_url,
                                "is_mock": True
                            }
                        )
                    
                    return {
                        "success": True,
                        "task_id": task_id,
                        "video_path": output_path,
                        "video_url": video_url,
                        "thumbnail_path": thumbnail_path,
                        "thumbnail_url": thumbnail_url,
                        "is_mock": True
                    }
            except Exception as e:
                logger.error(f"使用WAN模型生成视频时出错: {e}")
                logger.error(traceback.format_exc())
                if progress_updater:
                    await progress_updater.update(
                        status="failed",
                        error="使用WAN模型生成视频失败",
                        logs=[f"❌ 使用WAN模型生成视频失败"]
                    )
                return {
                    "success": False,
                    "error": "使用WAN模型生成视频失败",
                    "task_id": task_id
                }
        except Exception as e:
            # 捕获未被内部try-except捕获的异常
            error_msg = f"视频生成过程中发生未处理的错误: {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            
            # 更新进度
            if progress_updater:
                await progress_updater.update(
                    status="failed",
                    error=error_msg,
                    logs=[f"❌ 未处理的错误: {error_msg}"]
                )
            
            return {
                "success": False,
                "error": error_msg,
                "task_id": task_id
            }

    def _generate_thumbnail(self, video_path: str) -> str:
        """
        从视频生成缩略图
        
        Args:
            video_path: 视频文件路径
            
        Returns:
            str: 缩略图文件路径，如果生成失败则返回空字符串
        """
        try:
            logger.info(f"从视频生成缩略图: {video_path}")
            import cv2
            
            # 检查视频文件是否存在
            if not os.path.exists(video_path):
                logger.error(f"视频文件不存在: {video_path}")
                return ""
            
            # 打开视频文件
            video_capture = cv2.VideoCapture(video_path)
            if not video_capture.isOpened():
                logger.error(f"无法打开视频文件: {video_path}")
                return ""
            
            # 获取帧总数
            total_frames = int(video_capture.get(cv2.CAP_PROP_FRAME_COUNT))
            logger.info(f"视频总帧数: {total_frames}")
            
            if total_frames == 0:
                logger.error(f"视频文件为空: {video_path}")
                return ""
            
            # 设置帧位置到1/3处
            target_frame = int(total_frames / 3)
            video_capture.set(cv2.CAP_PROP_POS_FRAMES, target_frame)
            
            # 读取帧
            success, frame = video_capture.read()
            if not success:
                logger.warning(f"无法读取指定帧，尝试读取第一帧")
                video_capture.set(cv2.CAP_PROP_POS_FRAMES, 0)
                success, frame = video_capture.read()
                
                if not success:
                    logger.error(f"无法读取视频帧: {video_path}")
                    video_capture.release()
                    return ""
            
            # 释放视频捕获对象
            video_capture.release()
            
            # 创建缩略图文件路径（将视频扩展名改为jpg）
            thumbnail_path = os.path.splitext(video_path)[0] + ".jpg"
            
            # 调整大小（如果需要）
            max_dimension = 640
            height, width = frame.shape[:2]
            
            if height > max_dimension or width > max_dimension:
                # 保持纵横比调整大小
                if width >= height:
                    new_width = max_dimension
                    new_height = int(height * (max_dimension / width))
                else:
                    new_height = max_dimension
                    new_width = int(width * (max_dimension / height))
                
                frame = cv2.resize(frame, (new_width, new_height), interpolation=cv2.INTER_AREA)
                logger.info(f"调整缩略图大小到: {new_width}x{new_height}")
            
            # 保存缩略图
            cv2.imwrite(thumbnail_path, frame, [cv2.IMWRITE_JPEG_QUALITY, 90])
            
            # 检查缩略图是否成功保存
            if os.path.exists(thumbnail_path) and os.path.getsize(thumbnail_path) > 0:
                logger.info(f"成功生成缩略图: {thumbnail_path}, 大小: {os.path.getsize(thumbnail_path) / 1024:.2f} KB")
                return thumbnail_path
            else:
                logger.error(f"生成的缩略图不存在或大小为0: {thumbnail_path}")
                return ""
                
        except Exception as e:
            logger.error(f"生成缩略图时出错: {str(e)}")
            logger.error(traceback.format_exc())
            return ""
    
    async def _generate_mock_video(
        self,
        task_id: str,
        prompt: str,
        negative_prompt: str = "",
        num_frames: int = 81,
        height: int = 720,
        width: int = 1280,
        guidance_scale: float = 5.0,
        fps: int = 16,
        seed: Optional[int] = None,
        model_size: str = "14B",
        resolution: str = "720p",
        output_format: str = "mp4",
        progress_updater: Optional[ProgressUpdater] = None
    ) -> Dict[str, Any]:
        """
        生成模拟视频，用于真实模型无法使用时
        
        Args:
            task_id: 任务ID
            prompt: 提示词
            negative_prompt: 负面提示词
            num_frames: 帧数
            height: 高度
            width: 宽度
            guidance_scale: 引导比例
            fps: 帧率
            seed: 随机种子
            model_size: 模型大小
            resolution: 分辨率
            output_format: 输出格式
            progress_updater: 进度更新器
        
        Returns:
            Dict: 包含生成结果的字典
        """
        try:
            logger.info(f"[模拟] 生成视频: {prompt}")
            import numpy as np
            import cv2
            from PIL import Image, ImageDraw, ImageFont
            
            # 设置随机种子
            if seed is None:
                import random
                seed = random.randint(0, 2**32 - 1)
            np.random.seed(seed)
            
            # 更新进度
            if progress_updater:
                await progress_updater.update(
                    progress=40,
                    logs=[f"[模拟] 开始生成视频..."]
                )
            
            # 创建帧数组
            frames = np.zeros((num_frames, height, width, 3), dtype=np.uint8)
            
            # 为每帧添加内容
            for i in range(num_frames):
                # 创建渐变背景
                frame = np.zeros((height, width, 3), dtype=np.uint8)
                for y in range(height):
                    color_value = int(y / height * 255)
                    for x in range(width):
                        r = (color_value + i * 5) % 255
                        g = (color_value + i * 3) % 255
                        b = (color_value + i * 7) % 255
                        frame[y, x] = [r, g, b]
                
                # 添加动画元素
                circle_x = int(width * (0.5 + 0.3 * np.sin(i * 2 * np.pi / num_frames)))
                circle_y = int(height * (0.5 + 0.3 * np.cos(i * 2 * np.pi / num_frames)))
                cv2.circle(frame, (circle_x, circle_y), 50, (255, 255, 255), -1)
                
                # 转为PIL图像添加文字
                img = Image.fromarray(frame)
                draw = ImageDraw.Draw(img)
                
                # 使用默认字体
                try:
                    font = ImageFont.load_default()
                    
                    # 添加文字
                    draw.text((width//2 - 150, height//2 - 100), f"提示词: {prompt}", fill=(255, 255, 255), font=font)
                    draw.text((width//2 - 150, height//2), "模拟模式 - 非真实AI生成", fill=(255, 255, 0), font=font)
                    draw.text((width//2 - 150, height//2 + 100), f"帧 {i+1}/{num_frames}", fill=(255, 255, 255), font=font)
                except Exception as e:
                    logger.error(f"添加文字失败: {e}")
                
                frames[i] = np.array(img)
                
                # 每20帧更新一次进度
                if i % 20 == 0 and progress_updater:
                    progress = 50 + int(30 * (i / num_frames))
                    await progress_updater.update(
                        progress=progress,
                        logs=[f"[模拟] 已生成 {i+1}/{num_frames} 帧"]
                    )
            
            # 更新进度
            if progress_updater:
                await progress_updater.update(
                    progress=80,
                    logs=[f"[模拟] 导出视频..."]
                )
            
            # 保存视频
            output_path = os.path.join(tempfile.gettempdir(), f"{task_id}.{output_format}")
            logger.info(f"[模拟] 保存视频到: {output_path}")
            
            # 导出视频
            export_success = self._export_video_with_ffmpeg_params(
                frames, output_path, fps=fps
            )
            
            if not export_success:
                logger.error("[模拟] 导出视频失败")
                if progress_updater:
                    await progress_updater.update(
                        status="failed",
                        error="导出视频失败",
                        logs=[f"❌ [模拟] 导出视频失败"]
                    )
                return {
                    "success": False,
                    "error": "导出视频失败",
                    "task_id": task_id
                }
            
            # 生成缩略图
            thumbnail_path = self._generate_thumbnail(output_path)
            
            # 转换为URL
            video_url = self._convert_path_to_url(output_path, "video")
            thumbnail_url = self._convert_path_to_url(thumbnail_path, "thumbnail")
            
            if progress_updater:
                await progress_updater.update(
                    status="completed",
                    progress=100,
                    logs=[f"[模拟] 视频生成完成"],
                    result={
                        "video_url": video_url,
                        "thumbnail_url": thumbnail_url,
                        "is_mock": True
                    }
                )
            
            return {
                "success": True,
                "task_id": task_id,
                "video_path": output_path,
                "video_url": video_url,
                "thumbnail_path": thumbnail_path,
                "thumbnail_url": thumbnail_url,
                "is_mock": True
            }
        except Exception as e:
            error_msg = f"[模拟] 生成视频时出错: {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            
            if progress_updater:
                await progress_updater.update(
                    status="failed",
                    error=error_msg,
                    logs=[f"❌ {error_msg}"]
                )
            
            return {
                "success": False,
                "error": error_msg,
                "task_id": task_id
            }
    
    def _init_model_status(self):
        """初始化模型状态跟踪"""
        # T2V模型状态初始化
        t2v_model_14b = os.environ.get("WAN_T2V_14B_MODEL", "Wan2.1-T2V-14B")
        t2v_model_1_3b = os.environ.get("WAN_T2V_1_3B_MODEL", "Wan2.1-T2V-1.3B")
        
        # 解析支持的分辨率
        resolutions = ["720p", "1080p"] # 默认支持的分辨率
        
        self.model_status = {
            "t2v": {
                "14B": {res: {"loaded": False, "loading": False, "error": None} for res in resolutions},
                "1.3B": {res: {"loaded": False, "loading": False, "error": None} for res in resolutions}
            },
            "i2v": {res: {"loaded": False, "loading": False, "error": None} for res in resolutions}
        }
        
        # 日志记录模型状态
        logger.info(f"初始化模型状态跟踪:")
        logger.info(f"  文本到视频 (14B): {', '.join(resolutions)}")
        logger.info(f"  文本到视频 (1.3B): {', '.join(resolutions)}")
        logger.info(f"  图像到视频: {', '.join(resolutions)}")
        
        # 检查模型目录是否存在
        t2v_14b_path = os.path.join(self.model_cache_dir, t2v_model_14b)
        t2v_1_3b_path = os.path.join(self.model_cache_dir, t2v_model_1_3b)
        
        # 记录模型路径信息
        logger.info(f"模型路径:")
        logger.info(f"  T2V 14B: {t2v_14b_path}")
        logger.info(f"  T2V 1.3B: {t2v_1_3b_path}")
        
        # 检查模型文件是否存在
        t2v_14b_exists = os.path.exists(t2v_14b_path) and os.path.isdir(t2v_14b_path)
        t2v_1_3b_exists = os.path.exists(t2v_1_3b_path) and os.path.isdir(t2v_1_3b_path)
        
        # 日志记录模型文件状态
        logger.info(f"模型目录状态:")
        logger.info(f"  T2V 14B: {'存在' if t2v_14b_exists else '不存在'}")
        logger.info(f"  T2V 1.3B: {'存在' if t2v_1_3b_exists else '不存在'}")
    
    def _get_torch_dtype(self, precision: str):
        """获取torch数据类型"""
        if precision == "float16":
            return torch.float16
        elif precision == "bfloat16" and hasattr(torch, "bfloat16"):
            return torch.bfloat16
        else:
            return torch.float32
    
    async def check_model_status(self, model_type: str, model_size: str = "14B", resolution: str = "720p") -> Dict[str, Any]:
        """
        检查模型状态，检查本地模型文件是否存在和完整。
        
        Args:
            model_type: 模型类型，'t2v'或'i2v'
            model_size: 模型大小，仅用于t2v
            resolution: 分辨率，用于i2v或t2v的分辨率参数
            
        Returns:
            Dict: 模型状态信息
        """
        logger.info(f"检查模型状态: {model_type}, 规格: {model_size}, 分辨率: {resolution}")
        
        # 更新检查时间
        if model_type == "t2v" and model_size in self.model_status["t2v"]:
            if resolution in self.model_status["t2v"][model_size]:
                self.model_status["t2v"][model_size][resolution]["last_check"] = time.time()
        elif model_type == "i2v" and resolution in self.model_status["i2v"]:
            self.model_status["i2v"][resolution]["last_check"] = time.time()
        
        # 检查模型是否已加载到内存中
        if model_type == "t2v":
            if model_type in self.models and model_size in self.models[model_type] and resolution in self.models[model_type][model_size]:
                logger.info(f"模型已加载到内存中: {model_type} {model_size} {resolution}")
                if model_size in self.model_status["t2v"] and resolution in self.model_status["t2v"][model_size]:
                    self.model_status["t2v"][model_size][resolution]["loaded"] = True
                return {"status": "loaded", "error": None}
        elif model_type == "i2v":
            if model_type in self.models and resolution in self.models[model_type]:
                logger.info(f"模型已加载到内存中: {model_type} {resolution}")
                if resolution in self.model_status["i2v"]:
                    self.model_status["i2v"][resolution]["loaded"] = True
                return {"status": "loaded", "error": None}
        
        # 选择正确的模型ID
        model_id = self._get_model_id(model_type, model_size, resolution)
        model_path = os.path.join(self.model_cache_dir, model_id)
        
        logger.info(f"检查本地模型目录: {model_path}")
        
        # 检查本地模型目录和文件
        if not os.path.exists(model_path):
            status = "not_found"
            error = f"模型目录不存在: {model_path}"
            logger.warning(error)
            
            if model_type == "t2v" and model_size in self.model_status["t2v"] and resolution in self.model_status["t2v"][model_size]:
                self.model_status["t2v"][model_size][resolution]["loaded"] = False
                self.model_status["t2v"][model_size][resolution]["error"] = error
            elif model_type == "i2v" and resolution in self.model_status["i2v"]:
                self.model_status["i2v"][resolution]["loaded"] = False
                self.model_status["i2v"][resolution]["error"] = error
                
            return {"status": status, "error": error}
        
        # 检查必要的子目录
        required_subdirs = ["vae", "scheduler", "unet"]
        missing_subdirs = []
        
        for subdir in required_subdirs:
            subdir_path = os.path.join(model_path, subdir)
            if not os.path.exists(subdir_path) or not os.path.isdir(subdir_path):
                missing_subdirs.append(subdir)
        
        if missing_subdirs:
            status = "incomplete"
            error = f"模型目录结构不完整，缺少以下子目录: {', '.join(missing_subdirs)}"
            logger.warning(error)
            
            # 尝试自动修复
            logger.info(f"尝试自动修复模型目录结构...")
            if auto_fix_model_structure(model_path):
                logger.info(f"模型目录结构自动修复成功，重新检查...")
                
                # 重新检查子目录是否存在
                missing_subdirs = []
                for subdir in required_subdirs:
                    subdir_path = os.path.join(model_path, subdir)
                    if not os.path.exists(subdir_path) or not os.path.isdir(subdir_path):
                        missing_subdirs.append(subdir)
                
                if not missing_subdirs:
                    logger.info(f"修复成功，模型目录结构现在完整")
                    return {"status": "ready", "error": None}
                else:
                    # 修复失败，更新错误信息
                    error = f"自动修复后仍缺少子目录: {', '.join(missing_subdirs)}"
                    logger.error(error)
            else:
                logger.error(f"自动修复失败")
            
            if model_type == "t2v" and model_size in self.model_status["t2v"] and resolution in self.model_status["t2v"][model_size]:
                self.model_status["t2v"][model_size][resolution]["loaded"] = False
                self.model_status["t2v"][model_size][resolution]["error"] = error
            elif model_type == "i2v" and resolution in self.model_status["i2v"]:
                self.model_status["i2v"][resolution]["loaded"] = False
                self.model_status["i2v"][resolution]["error"] = error
                
            return {"status": status, "error": error}
        
        # 检查必要的文件
        files_to_check = {
            "vae/config.json": "VAE配置",
            "scheduler/scheduler_config.json": "调度器配置",
            "unet/config.json": "UNet配置"
        }
        
        missing_files = []
        
        for file_path, desc in files_to_check.items():
            full_path = os.path.join(model_path, file_path)
            if not os.path.exists(full_path):
                missing_files.append(f"{desc} ({file_path})")
        
        if missing_files:
            status = "incomplete"
            error = f"模型文件不完整，缺少以下文件: {', '.join(missing_files)}"
            logger.warning(error)
            
            # 尝试自动修复
            logger.info(f"尝试自动修复缺失的配置文件...")
            if auto_fix_model_structure(model_path):
                logger.info(f"配置文件自动修复成功，重新检查...")
                
                # 重新检查文件是否存在
                missing_files = []
                for file_path, desc in files_to_check.items():
                    full_path = os.path.join(model_path, file_path)
                    if not os.path.exists(full_path):
                        missing_files.append(f"{desc} ({file_path})")
                
                if not missing_files:
                    logger.info(f"修复成功，模型文件现在完整")
                    return {"status": "ready", "error": None}
                else:
                    # 修复失败，更新错误信息
                    error = f"自动修复后仍缺少文件: {', '.join(missing_files)}"
                    logger.error(error)
            else:
                logger.error(f"自动修复失败")
            
            if model_type == "t2v" and model_size in self.model_status["t2v"] and resolution in self.model_status["t2v"][model_size]:
                self.model_status["t2v"][model_size][resolution]["loaded"] = False
                self.model_status["t2v"][model_size][resolution]["error"] = error
            elif model_type == "i2v" and resolution in self.model_status["i2v"]:
                self.model_status["i2v"][resolution]["loaded"] = False
                self.model_status["i2v"][resolution]["error"] = error
                
            return {"status": status, "error": error}
        
        # 所有检查通过，模型文件存在但未加载
        status = "ready"
        logger.info(f"模型文件完整，准备加载: {model_path}")
        
        if model_type == "t2v" and model_size in self.model_status["t2v"] and resolution in self.model_status["t2v"][model_size]:
            self.model_status["t2v"][model_size][resolution]["loaded"] = False
            self.model_status["t2v"][model_size][resolution]["error"] = None
        elif model_type == "i2v" and resolution in self.model_status["i2v"]:
            self.model_status["i2v"][resolution]["loaded"] = False
            self.model_status["i2v"][resolution]["error"] = None
        
        return {"status": status, "error": None}
    
    def _get_model_id(self, model_type: str, model_size: str = "14B", resolution: str = "720p") -> str:
        """
        获取模型ID
        
        Args:
            model_type: 模型类型 (t2v 或 i2v)
            model_size: 模型大小 (14B 或 1.3B)
            resolution: 分辨率 (720p 或 1080p)
            
        Returns:
            str: 模型ID
        """
        if model_type == "t2v":
            # 文本到视频模型ID
            if model_size == "14B":
                return os.environ.get("WAN_T2V_14B_MODEL", "Wan2.1-T2V-14B")
            elif model_size == "1.3B":
                return os.environ.get("WAN_T2V_1_3B_MODEL", "Wan2.1-T2V-1.3B")
            else:
                logger.warning(f"未知的T2V模型大小: {model_size}，使用默认14B模型")
                return os.environ.get("WAN_T2V_14B_MODEL", "Wan2.1-T2V-14B")
        elif model_type == "i2v":
            # 图像到视频模型ID
            return "Wan2.1-I2V"
        else:
            logger.warning(f"未知的模型类型: {model_type}，使用默认T2V模型")
            return os.environ.get("WAN_T2V_14B_MODEL", "Wan2.1-T2V-14B")
    
    async def initialize_t2v(self, task_id: str, progress_updater: ProgressUpdater, model_size: str = "14B", resolution: str = "720p"):
        """
        初始化文本到视频模型
        
        Args:
            task_id: 任务ID
            progress_updater: 进度更新器
            model_size: 模型规格 (14B 或 1.3B)
            resolution: 分辨率 (720p 或 1080p)
        """
        logger.info(f"初始化T2V模型: 规格 {model_size}, 分辨率 {resolution}")
        
        # 检查模型是否已加载
        if 't2v' in self.models and model_size in self.models['t2v'] and resolution in self.models['t2v'][model_size]:
            logger.info(f"T2V模型 ({model_size}, {resolution}) 已加载，跳过初始化")
            return True
        
        # 检查是否已在加载中
        if self.model_status["t2v"][model_size][resolution]["loading"]:
            logger.info(f"T2V模型 ({model_size}, {resolution}) 正在加载中，请等待")
            # 定期检查模型是否已加载完成
            retry_count = 0
            while retry_count < self.max_retry_attempts:
                if self.model_status["t2v"][model_size][resolution]["loaded"]:
                    logger.info(f"T2V模型 ({model_size}, {resolution}) 已加载完成")
                    return True
                await asyncio.sleep(2)  # 等待2秒再检查
                retry_count += 1
            logger.warning(f"等待T2V模型 ({model_size}, {resolution}) 加载超时")
        
        # 设置为加载中状态
        self.model_status["t2v"][model_size][resolution]["loading"] = True
        self.model_status["t2v"][model_size][resolution]["error"] = None
        
        # 更新进度
        await progress_updater.update(
            status="loading",
            progress=5,
            logs=[f"初始化文本到视频模型 ({model_size}, {resolution})..."]
        )
        
        try:
            # 记录开始时间
            start_time = time.time()
            
            # 如果已经是模拟模式，直接创建模拟模型
            if self.mock_mode:
                logger.info(f"已处于模拟模式，跳过模型完整性检查，直接创建模拟模型")
                return await self._create_mock_model(model_size, resolution, progress_updater)
            
            # 获取模型ID和路径
            model_id = self._get_model_id("t2v", model_size, resolution)
            
            # 优先检查本地模型目录
            local_model_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 
                                        "local_models", "wan", "Wan2.1", model_id)
            if os.path.exists(local_model_dir):
                logger.info(f"使用本地模型目录: {local_model_dir}")
                model_path = local_model_dir
            else:
                model_path = os.path.join(self.model_cache_dir, model_id)
            
            logger.info(f"模型路径: {model_path}")
            
            # 检查模型完整性
            integrity_result = await self.check_model_integrity(model_type="t2v", model_size=model_size, resolution=resolution)
            if not integrity_result["success"]:
                error_msg = f"模型文件不完整: {integrity_result['message']}"
                logger.error(error_msg)
                
                # 更新进度，提供详细的错误信息
                await progress_updater.update(
                    status="failed",
                    error=error_msg,
                    logs=[
                        f"❌ 模型文件不完整: {integrity_result['message']}",
                        "⚠️ 系统将切换到模拟模式，但这将生成非真实的AI视频"
                    ]
                )
                
                # 如果用户明确要求不使用模拟模式，则返回失败
                force_real_mode = os.environ.get("WAN_FORCE_REAL_MODE", "false").lower() == "true"
                if force_real_mode:
                    logger.error("用户要求使用真实模式，但模型文件不完整，任务失败")
                    self.model_status["t2v"][model_size][resolution]["loading"] = False
                    self.model_status["t2v"][model_size][resolution]["error"] = error_msg
                    
                    await progress_updater.update(
                        status="failed",
                        error="模型文件不完整，且系统设置为强制使用真实模式",
                        logs=[
                            "❌ 模型文件不完整，无法生成视频",
                            "⚠️ 系统设置为强制使用真实模式，不允许使用模拟模式"
                        ]
                    )
                    return False
                
                # 切换到模拟模式
                logger.warning("模型文件不完整，切换到模拟模式")
                self.mock_mode = True
                os.environ["WAN_MOCK_MODE"] = "true"
                
                # 创建模拟模型并返回成功
                return await self._create_mock_model(model_size, resolution, progress_updater)
            
            # 模型文件完整，继续加载
            logger.info(f"模型文件完整性检查通过，开始加载模型")
            
            # 更新进度
            await progress_updater.update(
                progress=10,
                logs=[f"模型文件完整性检查通过，开始加载模型..."]
            )
            
            # 如果启用了模拟模式，则创建模拟模型
            if self.mock_mode:
                logger.info("模拟模式已启用，创建模拟模型")
                return await self._create_mock_model(model_size, resolution, progress_updater)
            
            # 导入必要的库
            try:
                # 尝试导入WAN模型
                try:
                    # 添加本地模型路径到系统路径
                    wan_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 
                                         "local_models", "wan", "Wan2.1")
                    if wan_path not in sys.path:
                        sys.path.append(wan_path)
                        logger.info(f"添加WAN模型路径到系统路径: {wan_path}")
                    
                    # 尝试导入WAN模块
                    try:
                        import wan
                        from wan.configs import WAN_CONFIGS
                        from wan.text2video import WanT2V
                        logger.info("成功导入WAN模块")
                        
                        # 使用WAN模型
                        config = WAN_CONFIGS[f"t2v-{model_size}"]
                        logger.info(f"使用WAN配置: {config}")
                        
                        # 初始化WAN模型
                        model = WanT2V(
                            config=config,
                            checkpoint_dir=model_path,
                            device_id=0 if self.device == "cuda" else -1,
                            t5_cpu=self.device == "cpu"
                        )
                        logger.info(f"WAN模型初始化成功")
                        
                        # 保存模型到内存
                        if 't2v' not in self.models:
                            self.models['t2v'] = {}
                        if model_size not in self.models['t2v']:
                            self.models['t2v'][model_size] = {}
                        
                        self.models['t2v'][model_size][resolution] = model
                        
                        # 更新状态
                        self.model_status["t2v"][model_size][resolution]["loaded"] = True
                        self.model_status["t2v"][model_size][resolution]["loading"] = False
                        self.is_initialized = True
                        
                        # 记录加载时间
                        load_time = time.time() - start_time
                        logger.info(f"T2V模型 ({model_size}, {resolution}) 加载完成，耗时: {load_time:.2f}秒")
                        
                        # 更新进度
                        await progress_updater.update(
                            progress=20,
                            logs=[f"模型加载成功，准备生成视频"]
                        )
                        
                        return True
                    except ImportError as e:
                        logger.error(f"导入WAN模块失败: {e}")
                        raise e
                    except Exception as e:
                        logger.error(f"初始化WAN模型失败: {e}")
                        logger.error(traceback.format_exc())
                        raise e
                except Exception as e:
                    logger.error(f"加载WAN模型失败: {e}")
                    logger.error(traceback.format_exc())
                    
                    # 尝试使用diffusers
                    logger.info("尝试使用diffusers加载模型")
                    import torch
                    from diffusers import DiffusionPipeline, DPMSolverMultistepScheduler
                    logger.info("成功导入diffusers库")
                    
                    # 更新进度
                    await progress_updater.update(
                        progress=15,
                        logs=[f"使用diffusers加载模型..."]
                    )
                    
                    # 这里应该添加使用diffusers加载模型的代码
                    # 但由于我们优先使用WAN模型，这部分代码暂时留空
                    # 如果需要，可以在后续实现
                    
                    # 由于diffusers加载失败，切换到模拟模式
                    logger.warning("使用diffusers加载模型失败，切换到模拟模式")
                    self.mock_mode = True
                    os.environ["WAN_MOCK_MODE"] = "true"
                    
                    # 创建模拟模型并返回成功
                    return await self._create_mock_model(model_size, resolution, progress_updater)
            except ImportError as e:
                error_msg = f"导入必要的库失败: {str(e)}"
                logger.error(error_msg)
                self.model_status["t2v"][model_size][resolution]["error"] = error_msg
                self.model_status["t2v"][model_size][resolution]["loading"] = False
                await progress_updater.update(
                    status="failed",
                    error=error_msg
                )
                
                # 如果用户明确要求不使用模拟模式，则返回失败
                if os.environ.get("WAN_FORCE_REAL_MODE", "false").lower() == "true":
                    logger.error("用户要求使用真实模式，但导入库失败，任务失败")
                    return False
                
                # 启用模拟模式继续
                logger.info("导入库失败，将使用模拟模式继续")
                self.mock_mode = True
                os.environ["WAN_MOCK_MODE"] = "true"
                
                # 创建模拟模型并返回成功
                return await self._create_mock_model(model_size, resolution, progress_updater)
            
        except Exception as e:
            error_msg = f"初始化T2V模型时出错: {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            
            # 更新状态
            self.model_status["t2v"][model_size][resolution]["loading"] = False
            self.model_status["t2v"][model_size][resolution]["error"] = error_msg
            
            # 更新进度
            await progress_updater.update(
                status="failed",
                error=error_msg,
                logs=[f"❌ 初始化模型失败: {error_msg}"]
            )
            
            # 如果用户明确要求不使用模拟模式，则返回失败
            if os.environ.get("WAN_FORCE_REAL_MODE", "false").lower() == "true":
                logger.error("用户要求使用真实模式，但初始化失败，任务失败")
                return False
            
            # 否则切换到模拟模式
            logger.warning("初始化模型失败，将使用模拟模式继续")
            self.mock_mode = True
            os.environ["WAN_MOCK_MODE"] = "true"
            
            # 创建模拟模型并返回成功
            return await self._create_mock_model(model_size, resolution, progress_updater)
    
    async def _create_mock_model(self, model_size: str, resolution: str, progress_updater: ProgressUpdater) -> bool:
        """创建模拟模型并更新状态"""
        try:
            start_time = time.time()
            
            # 自定义AutoencoderKL类，用于加载VAE
            class AutoencoderKLWan:
                @classmethod
                def from_pretrained(cls, model_path, **kwargs):
                    logger.info(f"[模拟] 加载VAE: {model_path}")
                    return cls()
                    
            # 自定义Pipeline类，用于加载模型
            class WanPipeline:
                def __init__(self, **kwargs):
                    logger.info("[模拟] 初始化WAN Pipeline")
                    self.device = "cpu"
                    self.vae = kwargs.get("vae")
                    self.scheduler = kwargs.get("scheduler")
                    self.mock_mode = True  # 标记这是模拟模式
                
                @classmethod
                def from_pretrained(cls, model_path, **kwargs):
                    logger.info(f"[模拟] 从预训练模型加载: {model_path}")
                    return cls(**kwargs)
                    
                def to(self, device):
                    logger.info(f"[模拟] 将模型移至设备: {device}")
                    self.device = device
                    return self
                
                # 添加模拟的生成方法
                def __call__(self, prompt, negative_prompt="", height=720, width=1280, 
                             num_frames=16, guidance_scale=7.5, generator=None):
                    logger.info(f"[模拟] 生成视频: prompt='{prompt}', 大小={width}x{height}, 帧数={num_frames}")
                    
                    # 创建更有结构的模拟视频
                    import numpy as np
                    import cv2
                    from PIL import Image, ImageDraw, ImageFont
                    
                    # 创建一个帧数组
                    frames = np.zeros((1, num_frames, height, width, 3), dtype=np.uint8)
                    
                    # 为每一帧添加一些内容
                    for i in range(num_frames):
                        # 创建PIL图像以便添加文字
                        img = Image.new('RGB', (width, height), color=(0, 0, 0))
                        draw = ImageDraw.Draw(img)
                        
                        # 添加渐变背景
                        for y in range(height):
                            color_value = int(y / height * 255)
                            for x in range(width):
                                r = (color_value + i * 5) % 255
                                g = (color_value + i * 3) % 255
                                b = (color_value + i * 7) % 255
                                img.putpixel((x, y), (r, g, b))
                        
                        # 添加提示词和模拟模式提示
                        try:
                            # 尝试使用系统字体
                            font_size = 40
                            try:
                                font = ImageFont.truetype("arial.ttf", font_size)
                            except:
                                # 退回到默认字体
                                font = ImageFont.load_default()
                            
                            # 添加提示词
                            prompt_text = f"提示词: {prompt}"
                            draw.text((width//2 - 300, height//2 - 150), prompt_text, 
                                     fill=(255, 255, 255), font=font)
                            
                            # 添加模拟模式提示
                            mock_text = "⚠️ 模拟模式 - 非真实AI生成视频 ⚠️"
                            draw.text((width//2 - 300, height//2 - 50), mock_text, 
                                     fill=(255, 255, 0), font=font)
                            
                            # 添加原因说明
                            reason_text = "原因: 模型文件不完整或无法加载"
                            draw.text((width//2 - 300, height//2 + 50), reason_text, 
                                     fill=(255, 255, 255), font=font)
                            
                            # 添加帧计数
                            frame_text = f"帧 {i+1}/{num_frames}"
                            draw.text((width//2 - 300, height//2 + 150), frame_text, 
                                     fill=(255, 255, 255), font=font)
                        except Exception as e:
                            # 如果添加文字失败，记录错误但继续生成视频
                            logger.error(f"在模拟视频中添加文字时出错: {e}")
                        
                        # 转换为numpy数组并存储
                        frames[0, i] = np.array(img)
                    
                    return type('obj', (object,), {'frames': frames})
            
            # 创建简单的调度器
            class MockScheduler:
                def __init__(self, **kwargs):
                    logger.info("[模拟] 创建调度器")
                    for key, value in kwargs.items():
                        setattr(self, key, value)
            
            # 创建模拟调度器和VAE
            scheduler = MockScheduler(beta_start=0.00085, beta_end=0.012, beta_schedule="linear")
            vae = AutoencoderKLWan()
            
            # 创建管道
            pipeline = WanPipeline(scheduler=scheduler, vae=vae)
            pipeline = pipeline.to(self.device)
            
            # 记录模型加载完成
            load_time = time.time() - start_time
            logger.info(f"[模拟] T2V模型 ({model_size}, {resolution}) 加载完成，耗时: {load_time:.2f}秒")
            
            # 保存模型到内存
            if 't2v' not in self.models:
                self.models['t2v'] = {}
            if model_size not in self.models['t2v']:
                self.models['t2v'][model_size] = {}
            
            self.models['t2v'][model_size][resolution] = pipeline
            
            # 更新状态
            self.model_status["t2v"][model_size][resolution]["loaded"] = True
            self.model_status["t2v"][model_size][resolution]["loading"] = False
            self.is_initialized = True
            
            # 更新进度
            await progress_updater.update(
                status="running",
                progress=20,
                logs=[f"[模拟] T2V模型 ({model_size}, {resolution}) 加载完成，准备生成视频"]
            )
            
            return True
        except Exception as e:
            logger.error(f"创建模拟模型失败: {e}")
            return False
    
    def _get_manual_download_guide(self, model_type: str, model_size: str = "14B", resolution: str = "720p") -> str:
        """获取手动下载指南"""
        model_id = self._get_model_id(model_type, model_size, resolution)
        cache_dir = self.model_cache_dir
        
        # 是否是本地路径
        local_path = os.path.normpath(os.path.join(cache_dir, model_id))
        guide = f"""
手动设置指南:
1. 权限问题：无法访问或写入目录 {local_path}
2. 建议解决方案:
   a. 以管理员权限运行应用程序
   b. 修改目录权限，确保当前用户有读写权限
   c. 在环境变量中设置WAN_MODEL_CACHE_DIR为具有写入权限的目录

3. 目录结构要求:
   {local_path}/
      ├── vae/          # VAE模型目录
      │   └── config.json  # 需要创建
      ├── scheduler/    # 调度器配置
      │   └── scheduler_config.json  # 需要创建
      └── unet/         # UNet模型目录
          └── config.json  # 需要创建
"""
        return guide
    
    def _convert_path_to_url(self, file_path: str, file_type: str = "video") -> str:
        """
        将本地文件路径转换为可通过HTTP访问的URL
        
        Args:
            file_path: 本地文件路径
            file_type: 文件类型(video/thumbnail)
            
        Returns:
            str: HTTP URL
        """
        if not file_path:
            logger.warning("尝试转换空文件路径为URL")
            return ""
            
        # 提取文件名
        file_name = os.path.basename(file_path)
        logger.info(f"转换文件路径为URL: {file_path} -> {file_name}")
        
        # 检查文件是否存在
        if not os.path.exists(file_path):
            logger.warning(f"文件不存在: {file_path}")
            # 尝试创建媒体目录
            media_dirs = ['media', 'media/videos', 'media/thumbnails']
            for d in media_dirs:
                os.makedirs(d, exist_ok=True)
                logger.info(f"确保媒体目录存在: {d}")
        else:
            logger.info(f"文件存在，大小: {os.path.getsize(file_path)} 字节")
            
            # 如果文件不在media目录中，复制到media目录
            if file_type == "video" and "media/videos" not in file_path:
                target_path = os.path.join("media", "videos", file_name)
                try:
                    import shutil
                    # 确保目标目录存在
                    os.makedirs(os.path.dirname(target_path), exist_ok=True)
                    # 复制文件
                    shutil.copy2(file_path, target_path)
                    logger.info(f"文件已复制到媒体目录: {target_path}")
                    
                    # 验证文件是否成功复制
                    if os.path.exists(target_path) and os.path.getsize(target_path) > 0:
                        logger.info(f"文件复制成功，大小: {os.path.getsize(target_path)} 字节")
                    else:
                        logger.error(f"文件复制失败或大小为0: {target_path}")
                except Exception as e:
                    logger.error(f"复制文件到媒体目录失败: {e}")
            elif file_type == "thumbnail" and "media/thumbnails" not in file_path:
                target_path = os.path.join("media", "thumbnails", file_name)
                try:
                    import shutil
                    # 确保目标目录存在
                    os.makedirs(os.path.dirname(target_path), exist_ok=True)
                    # 复制文件
                    shutil.copy2(file_path, target_path)
                    logger.info(f"文件已复制到媒体目录: {target_path}")
                    
                    # 验证文件是否成功复制
                    if os.path.exists(target_path) and os.path.getsize(target_path) > 0:
                        logger.info(f"文件复制成功，大小: {os.path.getsize(target_path)} 字节")
                    else:
                        logger.error(f"文件复制失败或大小为0: {target_path}")
                except Exception as e:
                    logger.error(f"复制文件到媒体目录失败: {e}")
        
        # 构建URL (使用/api/media/前缀用于访问媒体文件)
        url = ""
        if file_type == "video":
            url = f"/api/media/videos/{file_name}"
        elif file_type == "thumbnail":
            url = f"/api/media/thumbnails/{file_name}"
        else:
            url = f"/api/media/files/{file_name}"
            
        logger.info(f"生成的URL: {url}")
        
        # 确保URL是绝对路径
        if not url.startswith(('http://', 'https://')):
            # 获取API基础URL
            api_base = os.environ.get('API_BASE_URL', '')
            if api_base:
                if not api_base.endswith('/'):
                    api_base += '/'
                if url.startswith('/'):
                    url = url[1:]  # 移除开头的斜杠以避免重复
                url = f"{api_base}{url}"
                logger.info(f"转换为绝对URL: {url}")
        
        return url
    
    async def generate_video_from_text(
        self,
        task_id: str,
        prompt: str,
        negative_prompt: str = "",
        num_frames: int = 81,  # 约5秒视频(16fps)
        height: int = 720,
        width: int = 1280,
        guidance_scale: float = 5.0,
        fps: int = 16,
        seed: Optional[int] = None,
        model_size: str = "14B",
        resolution: str = "720p", 
        output_format: str = "mp4"
    ) -> Dict[str, Any]:
        """
        从文本生成视频
        
        Args:
            task_id: 任务ID
            prompt: 提示词
            negative_prompt: 负面提示词
            num_frames: 帧数
            height: 高度
            width: 宽度
            guidance_scale: 引导比例
            fps: 帧率
            seed: 随机种子
            model_size: 模型大小 (14B/1.3B)
            resolution: 分辨率 (720p/1080p)
            output_format: 输出格式 (mp4/gif)
            
        Returns:
            Dict: 包含生成结果的字典
        """
        # 创建进度跟踪器
        progress_tracker = get_progress_tracker()
        progress_updater = ProgressUpdater(task_id)
        
        # 记录参数
        logger.info(f"从文本生成视频: task_id={task_id}, prompt='{prompt}', model_size={model_size}")
        logger.info(f"参数: frames={num_frames}, size={width}x{height}, fps={fps}, guidance={guidance_scale}")
        
        # 检查是否强制使用真实模式
        force_real_mode = os.environ.get("WAN_FORCE_REAL_MODE", "false").lower() == "true"
        
        if self.mock_mode:
            if force_real_mode:
                error_msg = "系统设置为强制使用真实模式，但模型文件不完整或无法加载"
                logger.error(error_msg)
                await progress_updater.update(
                    status="failed",
                    error=error_msg,
                    logs=[
                        "❌ 无法生成视频: 系统设置为强制使用真实模式，但模型文件不完整",
                        "⚠️ 请确保正确安装WAN 2.1模型文件，或禁用强制真实模式设置"
                    ]
                )
                return {
                    "success": False,
                    "error": error_msg,
                    "task_id": task_id
                }
            else:
                logger.warning(f"使用模拟模式生成视频: {prompt}")
        
        try:
            # 初始化模型
            await progress_updater.update(
                status="running",
                progress=0,
                logs=[f"初始化T2V模型 ({model_size}, {resolution})..."]
            )
            
            # 确保模型初始化
            model_initialized = await self.initialize_t2v(task_id, progress_updater, model_size, resolution)
            if not model_initialized:
                error_msg = f"模型初始化失败: {self.model_status['t2v'][model_size][resolution]['error']}"
                logger.error(error_msg)
                await progress_updater.update(
                    status="failed",
                    error=error_msg,
                    logs=[f"❌ {error_msg}"]
                )
                return {
                    "success": False,
                    "error": error_msg,
                    "task_id": task_id
                }
            
            # 更新进度
            await progress_updater.update(
                progress=30,
                logs=[f"开始生成视频..."]
            )
            
            # 检查是否使用模拟模式
            if self.mock_mode:
                # 如果强制使用真实模式，则返回错误
                if force_real_mode:
                    error_msg = "系统设置为强制使用真实模式，但当前处于模拟模式"
                    logger.error(error_msg)
                    await progress_updater.update(
                        status="failed",
                        error=error_msg,
                        logs=[
                            "❌ 无法生成视频: 系统设置为强制使用真实模式，但当前处于模拟模式",
                            "⚠️ 请确保正确安装WAN 2.1模型文件，或禁用强制真实模式设置"
                        ]
                    )
                    return {
                        "success": False,
                        "error": error_msg,
                        "task_id": task_id
                    }
                
                logger.info("使用模拟模式生成视频")
                # 直接使用模拟模式生成视频，不进行模型完整性检查
                return await self._generate_mock_video(
                    task_id=task_id,
                    prompt=prompt,
                    negative_prompt=negative_prompt,
                    num_frames=num_frames,
                    height=height,
                    width=width,
                    guidance_scale=guidance_scale,
                    fps=fps,
                    seed=seed,
                    model_size=model_size,
                    resolution=resolution,
                    output_format=output_format,
                    progress_updater=progress_updater
                )
            
            # 使用真实模型生成视频
            try:
                # 获取模型
                model = self.models['t2v'][model_size][resolution]
                
                # 检查模型类型
                if hasattr(model, 'generate'):
                    # 使用WAN模型
                    logger.info("使用WAN模型生成视频")
                    
                    # 更新进度
                    await progress_updater.update(
                        progress=40,
                        logs=[f"使用WAN模型生成视频..."]
                    )
                    
                    # 设置随机种子
                    if seed is None:
                        import random
                        seed = random.randint(0, 2**32 - 1)
                    logger.info(f"使用随机种子: {seed}")
                    
                    # 生成视频
                    frames = model.generate(
                        input_prompt=prompt,
                        size=(width, height),
                        frame_num=num_frames,
                        shift=5.0,  # 默认值
                        sampling_steps=50,  # 默认值
                        guide_scale=guidance_scale,
                        n_prompt=negative_prompt,
                        seed=seed
                    )
                    
                    # 更新进度
                    await progress_updater.update(
                        progress=80,
                        logs=[f"视频生成完成，准备导出..."]
                    )
                    
                    # 确保frames是正确的格式
                    if hasattr(frames, 'frames'):
                        frames = frames.frames
                    
                    # 将PyTorch张量转换为NumPy数组
                    import numpy as np
                    
                    # 详细检查和记录frames的类型和形状
                    logger.info(f"生成的frames类型: {type(frames)}")
                    if hasattr(frames, 'shape'):
                        logger.info(f"frames形状: {frames.shape}")
                    elif isinstance(frames, list):
                        logger.info(f"frames是列表，长度: {len(frames)}")
                        if len(frames) > 0:
                            logger.info(f"第一个元素类型: {type(frames[0])}")
                            if hasattr(frames[0], 'shape'):
                                logger.info(f"第一个元素形状: {frames[0].shape}")
                    
                    # 处理不同类型的frames
                    if isinstance(frames, torch.Tensor):
                        frames_np = frames.cpu().numpy()
                        logger.info(f"PyTorch张量转换为NumPy数组: {frames_np.shape}")
                    elif isinstance(frames, np.ndarray):
                        frames_np = frames
                        logger.info(f"frames已经是NumPy数组: {frames_np.shape}")
                    elif isinstance(frames, list) and len(frames) > 0 and isinstance(frames[0], torch.Tensor):
                        # 尝试转换列表中的第一个张量
                        frames_np = frames[0].cpu().numpy()
                        logger.info(f"从列表中提取第一个张量并转换: {frames_np.shape}")
                    else:
                        # 无法处理的类型，创建一个简单的黑色帧
                        logger.warning(f"无法处理的frames类型，创建黑色帧")
                        frames_np = np.zeros((num_frames, height, width, 3), dtype=np.uint8)
                    
                    # 检查是否需要转置
                    if len(frames_np.shape) == 4 and frames_np.shape[0] == 3:
                        # 可能是(C, T, H, W)格式，转换为(T, H, W, C)
                        logger.info(f"检测到(C, T, H, W)格式，转换为(T, H, W, C)")
                        frames_np = np.transpose(frames_np, (1, 2, 3, 0))
                        logger.info(f"转换后形状: {frames_np.shape}")
                    
                    # 确保值范围合适
                    if frames_np.max() > 1.0 and frames_np.max() <= 255.0:
                        # 已经是适合的范围，转为uint8
                        frames_np = frames_np.astype(np.uint8)
                    elif frames_np.max() <= 1.0:
                        frames_np = (frames_np * 255).astype(np.uint8)
                        logger.info(f"归一化值转换为uint8: {frames_np.dtype}")
                    else:
                        frames_np = np.clip(frames_np, 0, 255).astype(np.uint8)
                        logger.info(f"裁剪值并转换为uint8: {frames_np.dtype}")
                    
                    # 保存视频
                    output_path = os.path.join(tempfile.gettempdir(), f"{task_id}.{output_format}")
                    logger.info(f"保存视频到: {output_path}")
                    
                    # 使用自定义导出函数
                    export_success = self._export_video_with_ffmpeg_params(
                        frames_np, output_path, fps=fps
                    )
                    
                    if not export_success:
                        logger.error("导出视频失败")
                        if progress_updater:
                            await progress_updater.update(
                                status="failed",
                                error="导出视频失败",
                                logs=[f"❌ 导出视频失败"]
                            )
                        return {
                            "success": False,
                            "error": "导出视频失败",
                            "task_id": task_id
                        }
                    
                    # 生成缩略图
                    thumbnail_path = self._generate_thumbnail(output_path)
                    
                    # 转换为URL
                    video_url = self._convert_path_to_url(output_path, "video")
                    thumbnail_url = self._convert_path_to_url(thumbnail_path, "thumbnail")
                    
                    if progress_updater:
                        await progress_updater.update(
                            status="completed",
                            progress=100,
                            logs=[f"视频生成完成"],
                            result={
                                "video_url": video_url,
                                "thumbnail_url": thumbnail_url,
                                "is_mock": True
                            }
                        )
                    
                    return {
                        "success": True,
                        "task_id": task_id,
                        "video_path": output_path,
                        "video_url": video_url,
                        "thumbnail_path": thumbnail_path,
                        "thumbnail_url": thumbnail_url,
                        "is_mock": True
                    }
            except Exception as e:
                logger.error(f"使用WAN模型生成视频时出错: {e}")
                logger.error(traceback.format_exc())
                if progress_updater:
                    await progress_updater.update(
                        status="failed",
                        error="使用WAN模型生成视频失败",
                        logs=[f"❌ 使用WAN模型生成视频失败"]
                    )
                return {
                    "success": False,
                    "error": "使用WAN模型生成视频失败",
                    "task_id": task_id
                }
        except Exception as e:
            # 捕获未被内部try-except捕获的异常
            error_msg = f"视频生成过程中发生未处理的错误: {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            
            # 更新进度
            if progress_updater:
                await progress_updater.update(
                    status="failed",
                    error=error_msg,
                    logs=[f"❌ 未处理的错误: {error_msg}"]
                )
            
            return {
                "success": False,
                "error": error_msg,
                "task_id": task_id
            }

    def _generate_thumbnail(self, video_path: str) -> str:
        """
        从视频生成缩略图
        
        Args:
            video_path: 视频文件路径
            
        Returns:
            str: 缩略图文件路径，如果生成失败则返回空字符串
        """
        try:
            logger.info(f"从视频生成缩略图: {video_path}")
            import cv2
            
            # 检查视频文件是否存在
            if not os.path.exists(video_path):
                logger.error(f"视频文件不存在: {video_path}")
                return ""
            
            # 打开视频文件
            video_capture = cv2.VideoCapture(video_path)
            if not video_capture.isOpened():
                logger.error(f"无法打开视频文件: {video_path}")
                return ""
            
            # 获取帧总数
            total_frames = int(video_capture.get(cv2.CAP_PROP_FRAME_COUNT))
            logger.info(f"视频总帧数: {total_frames}")
            
            if total_frames == 0:
                logger.error(f"视频文件为空: {video_path}")
                return ""
            
            # 设置帧位置到1/3处
            target_frame = int(total_frames / 3)
            video_capture.set(cv2.CAP_PROP_POS_FRAMES, target_frame)
            
            # 读取帧
            success, frame = video_capture.read()
            if not success:
                logger.warning(f"无法读取指定帧，尝试读取第一帧")
                video_capture.set(cv2.CAP_PROP_POS_FRAMES, 0)
                success, frame = video_capture.read()
                
                if not success:
                    logger.error(f"无法读取视频帧: {video_path}")
                    video_capture.release()
                    return ""
            
            # 释放视频捕获对象
            video_capture.release()
            
            # 创建缩略图文件路径（将视频扩展名改为jpg）
            thumbnail_path = os.path.splitext(video_path)[0] + ".jpg"
            
            # 调整大小（如果需要）
            max_dimension = 640
            height, width = frame.shape[:2]
            
            if height > max_dimension or width > max_dimension:
                # 保持纵横比调整大小
                if width >= height:
                    new_width = max_dimension
                    new_height = int(height * (max_dimension / width))
                else:
                    new_height = max_dimension
                    new_width = int(width * (max_dimension / height))
                
                frame = cv2.resize(frame, (new_width, new_height), interpolation=cv2.INTER_AREA)
                logger.info(f"调整缩略图大小到: {new_width}x{new_height}")
            
            # 保存缩略图
            cv2.imwrite(thumbnail_path, frame, [cv2.IMWRITE_JPEG_QUALITY, 90])
            
            # 检查缩略图是否成功保存
            if os.path.exists(thumbnail_path) and os.path.getsize(thumbnail_path) > 0:
                logger.info(f"成功生成缩略图: {thumbnail_path}, 大小: {os.path.getsize(thumbnail_path) / 1024:.2f} KB")
                return thumbnail_path
            else:
                logger.error(f"生成的缩略图不存在或大小为0: {thumbnail_path}")
                return ""
                
        except Exception as e:
            logger.error(f"生成缩略图时出错: {str(e)}")
            logger.error(traceback.format_exc())
            return ""
    
    async def _generate_mock_video(
        self,
        task_id: str,
        prompt: str,
        negative_prompt: str = "",
        num_frames: int = 81,
        height: int = 720,
        width: int = 1280,
        guidance_scale: float = 5.0,
        fps: int = 16,
        seed: Optional[int] = None,
        model_size: str = "14B",
        resolution: str = "720p",
        output_format: str = "mp4",
        progress_updater: Optional[ProgressUpdater] = None
    ) -> Dict[str, Any]:
        """
        生成模拟视频，用于真实模型无法使用时
        
        Args:
            task_id: 任务ID
            prompt: 提示词
            negative_prompt: 负面提示词
            num_frames: 帧数
            height: 高度
            width: 宽度
            guidance_scale: 引导比例
            fps: 帧率
            seed: 随机种子
            model_size: 模型大小
            resolution: 分辨率
            output_format: 输出格式
            progress_updater: 进度更新器
        
        Returns:
            Dict: 包含生成结果的字典
        """
        try:
            logger.info(f"[模拟] 生成视频: {prompt}")
            import numpy as np
            import cv2
            from PIL import Image, ImageDraw, ImageFont
            
            # 设置随机种子
            if seed is None:
                import random
                seed = random.randint(0, 2**32 - 1)
            np.random.seed(seed)
            
            # 更新进度
            if progress_updater:
                await progress_updater.update(
                    progress=40,
                    logs=[f"[模拟] 开始生成视频..."]
                )
            
            # 创建帧数组
            frames = np.zeros((num_frames, height, width, 3), dtype=np.uint8)
            
            # 为每帧添加内容
            for i in range(num_frames):
                # 创建渐变背景
                frame = np.zeros((height, width, 3), dtype=np.uint8)
                for y in range(height):
                    color_value = int(y / height * 255)
                    for x in range(width):
                        r = (color_value + i * 5) % 255
                        g = (color_value + i * 3) % 255
                        b = (color_value + i * 7) % 255
                        frame[y, x] = [r, g, b]
                
                # 添加动画元素
                circle_x = int(width * (0.5 + 0.3 * np.sin(i * 2 * np.pi / num_frames)))
                circle_y = int(height * (0.5 + 0.3 * np.cos(i * 2 * np.pi / num_frames)))
                cv2.circle(frame, (circle_x, circle_y), 50, (255, 255, 255), -1)
                
                # 转为PIL图像添加文字
                img = Image.fromarray(frame)
                draw = ImageDraw.Draw(img)
                
                # 使用默认字体
                try:
                    font = ImageFont.load_default()
                    
                    # 添加文字
                    draw.text((width//2 - 150, height//2 - 100), f"提示词: {prompt}", fill=(255, 255, 255), font=font)
                    draw.text((width//2 - 150, height//2), "模拟模式 - 非真实AI生成", fill=(255, 255, 0), font=font)
                    draw.text((width//2 - 150, height//2 + 100), f"帧 {i+1}/{num_frames}", fill=(255, 255, 255), font=font)
                except Exception as e:
                    logger.error(f"添加文字失败: {e}")
                
                frames[i] = np.array(img)
                
                # 每20帧更新一次进度
                if i % 20 == 0 and progress_updater:
                    progress = 50 + int(30 * (i / num_frames))
                    await progress_updater.update(
                        progress=progress,
                        logs=[f"[模拟] 已生成 {i+1}/{num_frames} 帧"]
                    )
            
            # 更新进度
            if progress_updater:
                await progress_updater.update(
                    progress=80,
                    logs=[f"[模拟] 导出视频..."]
                )
            
            # 保存视频
            output_path = os.path.join(tempfile.gettempdir(), f"{task_id}.{output_format}")
            logger.info(f"[模拟] 保存视频到: {output_path}")
            
            # 导出视频
            export_success = self._export_video_with_ffmpeg_params(
                frames, output_path, fps=fps
            )
            
            if not export_success:
                logger.error("[模拟] 导出视频失败")
                if progress_updater:
                    await progress_updater.update(
                        status="failed",
                        error="导出视频失败",
                        logs=[f"❌ [模拟] 导出视频失败"]
                    )
                return {
                    "success": False,
                    "error": "导出视频失败",
                    "task_id": task_id
                }
            
            # 生成缩略图
            thumbnail_path = self._generate_thumbnail(output_path)
            
            # 转换为URL
            video_url = self._convert_path_to_url(output_path, "video")
            thumbnail_url = self._convert_path_to_url(thumbnail_path, "thumbnail")
            
            if progress_updater:
                await progress_updater.update(
                    status="completed",
                    progress=100,
                    logs=[f"[模拟] 视频生成完成"],
                    result={
                        "video_url": video_url,
                        "thumbnail_url": thumbnail_url,
                        "is_mock": True
                    }
                )
            
            return {
                "success": True,
                "task_id": task_id,
                "video_path": output_path,
                "video_url": video_url,
                "thumbnail_path": thumbnail_path,
                "thumbnail_url": thumbnail_url,
                "is_mock": True
            }
        except Exception as e:
            error_msg = f"[模拟] 生成视频时出错: {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            
            if progress_updater:
                await progress_updater.update(
                    status="failed",
                    error=error_msg,
                    logs=[f"❌ {error_msg}"]
                )
            
            return {
                "success": False,
                "error": error_msg,
                "task_id": task_id
            }
    
    def _init_model_status(self):
        """初始化模型状态跟踪"""
        # T2V模型状态初始化
        t2v_model_14b = os.environ.get("WAN_T2V_14B_MODEL", "Wan2.1-T2V-14B")
        t2v_model_1_3b = os.environ.get("WAN_T2V_1_3B_MODEL", "Wan2.1-T2V-1.3B")
        
        # 解析支持的分辨率
        resolutions = ["720p", "1080p"] # 默认支持的分辨率
        
        self.model_status = {
            "t2v": {
                "14B": {res: {"loaded": False, "loading": False, "error": None} for res in resolutions},
                "1.3B": {res: {"loaded": False, "loading": False, "error": None} for res in resolutions}
            },
            "i2v": {res: {"loaded": False, "loading": False, "error": None} for res in resolutions}
        }
        
        # 日志记录模型状态
        logger.info(f"初始化模型状态跟踪:")
        logger.info(f"  文本到视频 (14B): {', '.join(resolutions)}")
        logger.info(f"  文本到视频 (1.3B): {', '.join(resolutions)}")
        logger.info(f"  图像到视频: {', '.join(resolutions)}")
        
        # 检查模型目录是否存在
        t2v_14b_path = os.path.join(self.model_cache_dir, t2v_model_14b)
        t2v_1_3b_path = os.path.join(self.model_cache_dir, t2v_model_1_3b)
        
        # 记录模型路径信息
        logger.info(f"模型路径:")
        logger.info(f"  T2V 14B: {t2v_14b_path}")
        logger.info(f"  T2V 1.3B: {t2v_1_3b_path}")
        
        # 检查模型文件是否存在
        t2v_14b_exists = os.path.exists(t2v_14b_path) and os.path.isdir(t2v_14b_path)
        t2v_1_3b_exists = os.path.exists(t2v_1_3b_path) and os.path.isdir(t2v_1_3b_path)
        
        # 日志记录模型文件状态
        logger.info(f"模型目录状态:")
        logger.info(f"  T2V 14B: {'存在' if t2v_14b_exists else '不存在'}")
        logger.info(f"  T2V 1.3B: {'存在' if t2v_1_3b_exists else '不存在'}")
    
    def _get_torch_dtype(self, precision: str):
        """获取torch数据类型"""
        if precision == "float16":
            return torch.float16
        elif precision == "bfloat16" and hasattr(torch, "bfloat16"):
            return torch.bfloat16
        else:
            return torch.float32
    
    async def check_model_status(self, model_type: str, model_size: str = "14B", resolution: str = "720p") -> Dict[str, Any]:
        """
        检查模型状态，检查本地模型文件是否存在和完整。
        
        Args:
            model_type: 模型类型，'t2v'或'i2v'
            model_size: 模型大小，仅用于t2v
            resolution: 分辨率，用于i2v或t2v的分辨率参数
            
        Returns:
            Dict: 模型状态信息
        """
        logger.info(f"检查模型状态: {model_type}, 规格: {model_size}, 分辨率: {resolution}")
        
        # 更新检查时间
        if model_type == "t2v" and model_size in self.model_status["t2v"]:
            if resolution in self.model_status["t2v"][model_size]:
                self.model_status["t2v"][model_size][resolution]["last_check"] = time.time()
        elif model_type == "i2v" and resolution in self.model_status["i2v"]:
            self.model_status["i2v"][resolution]["last_check"] = time.time()
        
        # 检查模型是否已加载到内存中
        if model_type == "t2v":
            if model_type in self.models and model_size in self.models[model_type] and resolution in self.models[model_type][model_size]:
                logger.info(f"模型已加载到内存中: {model_type} {model_size} {resolution}")
                if model_size in self.model_status["t2v"] and resolution in self.model_status["t2v"][model_size]:
                    self.model_status["t2v"][model_size][resolution]["loaded"] = True
                return {"status": "loaded", "error": None}
        elif model_type == "i2v":
            if model_type in self.models and resolution in self.models[model_type]:
                logger.info(f"模型已加载到内存中: {model_type} {resolution}")
                if resolution in self.model_status["i2v"]:
                    self.model_status["i2v"][resolution]["loaded"] = True
                return {"status": "loaded", "error": None}
        
        # 选择正确的模型ID
        model_id = self._get_model_id(model_type, model_size, resolution)
        model_path = os.path.join(self.model_cache_dir, model_id)
        
        logger.info(f"检查本地模型目录: {model_path}")
        
        # 检查本地模型目录和文件
        if not os.path.exists(model_path):
            status = "not_found"
            error = f"模型目录不存在: {model_path}"
            logger.warning(error)
            
            if model_type == "t2v" and model_size in self.model_status["t2v"] and resolution in self.model_status["t2v"][model_size]:
                self.model_status["t2v"][model_size][resolution]["loaded"] = False
                self.model_status["t2v"][model_size][resolution]["error"] = error
            elif model_type == "i2v" and resolution in self.model_status["i2v"]:
                self.model_status["i2v"][resolution]["loaded"] = False
                self.model_status["i2v"][resolution]["error"] = error
                
            return {"status": status, "error": error}
        
        # 检查必要的子目录
        required_subdirs = ["vae", "scheduler", "unet"]
        missing_subdirs = []
        
        for subdir in required_subdirs:
            subdir_path = os.path.join(model_path, subdir)
            if not os.path.exists(subdir_path) or not os.path.isdir(subdir_path):
                missing_subdirs.append(subdir)
        
        if missing_subdirs:
            status = "incomplete"
            error = f"模型目录结构不完整，缺少以下子目录: {', '.join(missing_subdirs)}"
            logger.warning(error)
            
            # 尝试自动修复
            logger.info(f"尝试自动修复模型目录结构...")
            if auto_fix_model_structure(model_path):
                logger.info(f"模型目录结构自动修复成功，重新检查...")
                
                # 重新检查子目录是否存在
                missing_subdirs = []
                for subdir in required_subdirs:
                    subdir_path = os.path.join(model_path, subdir)
                    if not os.path.exists(subdir_path) or not os.path.isdir(subdir_path):
                        missing_subdirs.append(subdir)
                
                if not missing_subdirs:
                    logger.info(f"修复成功，模型目录结构现在完整")
                    return {"status": "ready", "error": None}
                else:
                    # 修复失败，更新错误信息
                    error = f"自动修复后仍缺少子目录: {', '.join(missing_subdirs)}"
                    logger.error(error)
            else:
                logger.error(f"自动修复失败")
            
            if model_type == "t2v" and model_size in self.model_status["t2v"] and resolution in self.model_status["t2v"][model_size]:
                self.model_status["t2v"][model_size][resolution]["loaded"] = False
                self.model_status["t2v"][model_size][resolution]["error"] = error
            elif model_type == "i2v" and resolution in self.model_status["i2v"]:
                self.model_status["i2v"][resolution]["loaded"] = False
                self.model_status["i2v"][resolution]["error"] = error
                
            return {"status": status, "error": error}
        
        # 检查必要的文件
        files_to_check = {
            "vae/config.json": "VAE配置",
            "scheduler/scheduler_config.json": "调度器配置",
            "unet/config.json": "UNet配置"
        }
        
        missing_files = []
        
        for file_path, desc in files_to_check.items():
            full_path = os.path.join(model_path, file_path)
            if not os.path.exists(full_path):
                missing_files.append(f"{desc} ({file_path})")
        
        if missing_files:
            status = "incomplete"
            error = f"模型文件不完整，缺少以下文件: {', '.join(missing_files)}"
            logger.warning(error)
            
            # 尝试自动修复
            logger.info(f"尝试自动修复缺失的配置文件...")
            if auto_fix_model_structure(model_path):
                logger.info(f"配置文件自动修复成功，重新检查...")
                
                # 重新检查文件是否存在
                missing_files = []
                for file_path, desc in files_to_check.items():
                    full_path = os.path.join(model_path, file_path)
                    if not os.path.exists(full_path):
                        missing_files.append(f"{desc} ({file_path})")
                
                if not missing_files:
                    logger.info(f"修复成功，模型文件现在完整")
                    return {"status": "ready", "error": None}
                else:
                    # 修复失败，更新错误信息
                    error = f"自动修复后仍缺少文件: {', '.join(missing_files)}"
                    logger.error(error)
            else:
                logger.error(f"自动修复失败")
            
            if model_type == "t2v" and model_size in self.model_status["t2v"] and resolution in self.model_status["t2v"][model_size]:
                self.model_status["t2v"][model_size][resolution]["loaded"] = False
                self.model_status["t2v"][model_size][resolution]["error"] = error
            elif model_type == "i2v" and resolution in self.model_status["i2v"]:
                self.model_status["i2v"][resolution]["loaded"] = False
                self.model_status["i2v"][resolution]["error"] = error
                
            return {"status": status, "error": error}
        
        # 所有检查通过，模型文件存在但未加载
        status = "ready"
        logger.info(f"模型文件完整，准备加载: {model_path}")
        
        if model_type == "t2v" and model_size in self.model_status["t2v"] and resolution in self.model_status["t2v"][model_size]:
            self.model_status["t2v"][model_size][resolution]["loaded"] = False
            self.model_status["t2v"][model_size][resolution]["error"] = None
        elif model_type == "i2v" and resolution in self.model_status["i2v"]:
            self.model_status["i2v"][resolution]["loaded"] = False
            self.model_status["i2v"][resolution]["error"] = None
        
        return {"status": status, "error": None}
    
    def _get_model_id(self, model_type: str, model_size: str = "14B", resolution: str = "720p") -> str:
        """
        获取模型ID
        
        Args:
            model_type: 模型类型 (t2v 或 i2v)
            model_size: 模型大小 (14B 或 1.3B)
            resolution: 分辨率 (720p 或 1080p)
            
        Returns:
            str: 模型ID
        """
        if model_type == "t2v":
            # 文本到视频模型ID
            if model_size == "14B":
                return os.environ.get("WAN_T2V_14B_MODEL", "Wan2.1-T2V-14B")
            elif model_size == "1.3B":
                return os.environ.get("WAN_T2V_1_3B_MODEL", "Wan2.1-T2V-1.3B")
            else:
                logger.warning(f"未知的T2V模型大小: {model_size}，使用默认14B模型")
                return os.environ.get("WAN_T2V_14B_MODEL", "Wan2.1-T2V-14B")
        elif model_type == "i2v":
            # 图像到视频模型ID
            return "Wan2.1-I2V"
        else:
            logger.warning(f"未知的模型类型: {model_type}，使用默认T2V模型")
            return os.environ.get("WAN_T2V_14B_MODEL", "Wan2.1-T2V-14B")
    
    async def initialize_t2v(self, task_id: str, progress_updater: ProgressUpdater, model_size: str = "14B", resolution: str = "720p"):
        """
        初始化文本到视频模型
        
        Args:
            task_id: 任务ID
            progress_updater: 进度更新器
            model_size: 模型规格 (14B 或 1.3B)
            resolution: 分辨率 (720p 或 1080p)
        """
        logger.info(f"初始化T2V模型: 规格 {model_size}, 分辨率 {resolution}")
        
        # 检查模型是否已加载
        if 't2v' in self.models and model_size in self.models['t2v'] and resolution in self.models['t2v'][model_size]:
            logger.info(f"T2V模型 ({model_size}, {resolution}) 已加载，跳过初始化")
            return True
        
        # 检查是否已在加载中
        if self.model_status["t2v"][model_size][resolution]["loading"]:
            logger.info(f"T2V模型 ({model_size}, {resolution}) 正在加载中，请等待")
            # 定期检查模型是否已加载完成
            retry_count = 0
            while retry_count < self.max_retry_attempts:
                if self.model_status["t2v"][model_size][resolution]["loaded"]:
                    logger.info(f"T2V模型 ({model_size}, {resolution}) 已加载完成")
                    return True
                await asyncio.sleep(2)  # 等待2秒再检查
                retry_count += 1
            logger.warning(f"等待T2V模型 ({model_size}, {resolution}) 加载超时")
        
        # 设置为加载中状态
        self.model_status["t2v"][model_size][resolution]["loading"] = True
        self.model_status["t2v"][model_size][resolution]["error"] = None
        
        # 更新进度
        await progress_updater.update(
            status="loading",
            progress=5,
            logs=[f"初始化文本到视频模型 ({model_size}, {resolution})..."]
        )
        
        try:
            # 记录开始时间
            start_time = time.time()
            
            # 如果已经是模拟模式，直接创建模拟模型
            if self.mock_mode:
                logger.info(f"已处于模拟模式，跳过模型完整性检查，直接创建模拟模型")
                return await self._create_mock_model(model_size, resolution, progress_updater)
            
            # 获取模型ID和路径
            model_id = self._get_model_id("t2v", model_size, resolution)
            
            # 优先检查本地模型目录
            local_model_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 
                                        "local_models", "wan", "Wan2.1", model_id)
            if os.path.exists(local_model_dir):
                logger.info(f"使用本地模型目录: {local_model_dir}")
                model_path = local_model_dir
            else:
                model_path = os.path.join(self.model_cache_dir, model_id)
            
            logger.info(f"模型路径: {model_path}")
            
            # 检查模型完整性
            integrity_result = await self.check_model_integrity(model_type="t2v", model_size=model_size, resolution=resolution)
            if not integrity_result["success"]:
                error_msg = f"模型文件不完整: {integrity_result['message']}"
                logger.error(error_msg)
                
                # 更新进度，提供详细的错误信息
                await progress_updater.update(
                    status="failed",
                    error=error_msg,
                    logs=[
                        f"❌ 模型文件不完整: {integrity_result['message']}",
                        "⚠️ 系统将切换到模拟模式，但这将生成非真实的AI视频"
                    ]
                )
                
                # 如果用户明确要求不使用模拟模式，则返回失败
                force_real_mode = os.environ.get("WAN_FORCE_REAL_MODE", "false").lower() == "true"
                if force_real_mode:
                    logger.error("用户要求使用真实模式，但模型文件不完整，任务失败")
                    self.model_status["t2v"][model_size][resolution]["loading"] = False
                    self.model_status["t2v"][model_size][resolution]["error"] = error_msg
                    
                    await progress_updater.update(
                        status="failed",
                        error="模型文件不完整，且系统设置为强制使用真实模式",
                        logs=[
                            "❌ 模型文件不完整，无法生成视频",
                            "⚠️ 系统设置为强制使用真实模式，不允许使用模拟模式"
                        ]
                    )
                    return False
                
                # 切换到模拟模式
                logger.warning("模型文件不完整，切换到模拟模式")
                self.mock_mode = True
                os.environ["WAN_MOCK_MODE"] = "true"
                
                # 创建模拟模型并返回成功
                return await self._create_mock_model(model_size, resolution, progress_updater)
            
            # 模型文件完整，继续加载
            logger.info(f"模型文件完整性检查通过，开始加载模型")
            
            # 更新进度
            await progress_updater.update(
                progress=10,
                logs=[f"模型文件完整性检查通过，开始加载模型..."]
            )
            
            # 如果启用了模拟模式，则创建模拟模型
            if self.mock_mode:
                logger.info("模拟模式已启用，创建模拟模型")
                return await self._create_mock_model(model_size, resolution, progress_updater)
            
            # 导入必要的库
            try:
                # 尝试导入WAN模型
                try:
                    # 添加本地模型路径到系统路径
                    wan_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 
                                         "local_models", "wan", "Wan2.1")
                    if wan_path not in sys.path:
                        sys.path.append(wan_path)
                        logger.info(f"添加WAN模型路径到系统路径: {wan_path}")
                    
                    # 尝试导入WAN模块
                    try:
                        import wan
                        from wan.configs import WAN_CONFIGS
                        from wan.text2video import WanT2V
                        logger.info("成功导入WAN模块")
                        
                        # 使用WAN模型
                        config = WAN_CONFIGS[f"t2v-{model_size}"]
                        logger.info(f"使用WAN配置: {config}")
                        
                        # 初始化WAN模型
                        model = WanT2V(
                            config=config,
                            checkpoint_dir=model_path,
                            device_id=0 if self.device == "cuda" else -1,
                            t5_cpu=self.device == "cpu"
                        )
                        logger.info(f"WAN模型初始化成功")
                        
                        # 保存模型到内存
                        if 't2v' not in self.models:
                            self.models['t2v'] = {}
                        if model_size not in self.models['t2v']:
                            self.models['t2v'][model_size] = {}
                        
                        self.models['t2v'][model_size][resolution] = model
                        
                        # 更新状态
                        self.model_status["t2v"][model_size][resolution]["loaded"] = True
                        self.model_status["t2v"][model_size][resolution]["loading"] = False
                        self.is_initialized = True
                        
                        # 记录加载时间
                        load_time = time.time() - start_time
                        logger.info(f"T2V模型 ({model_size}, {resolution}) 加载完成，耗时: {load_time:.2f}秒")
                        
                        # 更新进度
                        await progress_updater.update(
                            progress=20,
                            logs=[f"模型加载成功，准备生成视频"]
                        )
                        
                        return True
                    except ImportError as e:
                        logger.error(f"导入WAN模块失败: {e}")
                        raise e
                    except Exception as e:
                        logger.error(f"初始化WAN模型失败: {e}")
                        logger.error(traceback.format_exc())
                        raise e
                except Exception as e:
                    logger.error(f"加载WAN模型失败: {e}")
                    logger.error(traceback.format_exc())
                    
                    # 尝试使用diffusers
                    logger.info("尝试使用diffusers加载模型")
                    import torch
                    from diffusers import DiffusionPipeline, DPMSolverMultistepScheduler
                    logger.info("成功导入diffusers库")
                    
                    # 更新进度
                    await progress_updater.update(
                        progress=15,
                        logs=[f"使用diffusers加载模型..."]
                    )
                    
                    # 这里应该添加使用diffusers加载模型的代码
                    # 但由于我们优先使用WAN模型，这部分代码暂时留空
                    # 如果需要，可以在后续实现
                    
                    # 由于diffusers加载失败，切换到模拟模式
                    logger.warning("使用diffusers加载模型失败，切换到模拟模式")
                    self.mock_mode = True
                    os.environ["WAN_MOCK_MODE"] = "true"
                    
                    # 创建模拟模型并返回成功
                    return await self._create_mock_model(model_size, resolution, progress_updater)
            except ImportError as e:
                error_msg = f"导入必要的库失败: {str(e)}"
                logger.error(error_msg)
                self.model_status["t2v"][model_size][resolution]["error"] = error_msg
                self.model_status["t2v"][model_size][resolution]["loading"] = False
                await progress_updater.update(
                    status="failed",
                    error=error_msg
                )
                
                # 如果用户明确要求不使用模拟模式，则返回失败
                if os.environ.get("WAN_FORCE_REAL_MODE", "false").lower() == "true":
                    logger.error("用户要求使用真实模式，但导入库失败，任务失败")
                    return False
                
                # 启用模拟模式继续
                logger.info("导入库失败，将使用模拟模式继续")
                self.mock_mode = True
                os.environ["WAN_MOCK_MODE"] = "true"
                
                # 创建模拟模型并返回成功
                return await self._create_mock_model(model_size, resolution, progress_updater)
            
        except Exception as e:
            error_msg = f"初始化T2V模型时出错: {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            
            # 更新状态
            self.model_status["t2v"][model_size][resolution]["loading"] = False
            self.model_status["t2v"][model_size][resolution]["error"] = error_msg
            
            # 更新进度
            await progress_updater.update(
                status="failed",
                error=error_msg,
                logs=[f"❌ 初始化模型失败: {error_msg}"]
            )
            
            # 如果用户明确要求不使用模拟模式，则返回失败
            if os.environ.get("WAN_FORCE_REAL_MODE", "false").lower() == "true":
                logger.error("用户要求使用真实模式，但初始化失败，任务失败")
                return False
            
            # 否则切换到模拟模式
            logger.warning("初始化模型失败，将使用模拟模式继续")
            self.mock_mode = True
            os.environ["WAN_MOCK_MODE"] = "true"
            
            # 创建模拟模型并返回成功
            return await self._create_mock_model(model_size, resolution, progress_updater)
    
    async def _create_mock_model(self, model_size: str, resolution: str, progress_updater: ProgressUpdater) -> bool:
        """创建模拟模型并更新状态"""
        try:
            start_time = time.time()
            
            # 自定义AutoencoderKL类，用于加载VAE
            class AutoencoderKLWan:
                @classmethod
                def from_pretrained(cls, model_path, **kwargs):
                    logger.info(f"[模拟] 加载VAE: {model_path}")
                    return cls()
                    
            # 自定义Pipeline类，用于加载模型
            class WanPipeline:
                def __init__(self, **kwargs):
                    logger.info("[模拟] 初始化WAN Pipeline")
                    self.device = "cpu"
                    self.vae = kwargs.get("vae")
                    self.scheduler = kwargs.get("scheduler")
                    self.mock_mode = True  # 标记这是模拟模式
                
                @classmethod
                def from_pretrained(cls, model_path, **kwargs):
                    logger.info(f"[模拟] 从预训练模型加载: {model_path}")
                    return cls(**kwargs)
                    
                def to(self, device):
                    logger.info(f"[模拟] 将模型移至设备: {device}")
                    self.device = device
                    return self
                
                # 添加模拟的生成方法
                def __call__(self, prompt, negative_prompt="", height=720, width=1280, 
                             num_frames=16, guidance_scale=7.5, generator=None):
                    logger.info(f"[模拟] 生成视频: prompt='{prompt}', 大小={width}x{height}, 帧数={num_frames}")
                    
                    # 创建更有结构的模拟视频
                    import numpy as np
                    import cv2
                    from PIL import Image, ImageDraw, ImageFont
                    
                    # 创建一个帧数组
                    frames = np.zeros((1, num_frames, height, width, 3), dtype=np.uint8)
                    
                    # 为每一帧添加一些内容
                    for i in range(num_frames):
                        # 创建PIL图像以便添加文字
                        img = Image.new('RGB', (width, height), color=(0, 0, 0))
                        draw = ImageDraw.Draw(img)
                        
                        # 添加渐变背景
                        for y in range(height):
                            color_value = int(y / height * 255)
                            for x in range(width):
                                r = (color_value + i * 5) % 255
                                g = (color_value + i * 3) % 255
                                b = (color_value + i * 7) % 255
                                img.putpixel((x, y), (r, g, b))
                        
                        # 添加提示词和模拟模式提示
                        try:
                            # 尝试使用系统字体
                            font_size = 40
                            try:
                                font = ImageFont.truetype("arial.ttf", font_size)
                            except:
                                # 退回到默认字体
                                font = ImageFont.load_default()
                            
                            # 添加提示词
                            prompt_text = f"提示词: {prompt}"
                            draw.text((width//2 - 300, height//2 - 150), prompt_text, 
                                     fill=(255, 255, 255), font=font)
                            
                            # 添加模拟模式提示
                            mock_text = "⚠️ 模拟模式 - 非真实AI生成视频 ⚠️"
                            draw.text((width//2 - 300, height//2 - 50), mock_text, 
                                     fill=(255, 255, 0), font=font)
                            
                            # 添加原因说明
                            reason_text = "原因: 模型文件不完整或无法加载"
                            draw.text((width//2 - 300, height//2 + 50), reason_text, 
                                     fill=(255, 255, 255), font=font)
                            
                            # 添加帧计数
                            frame_text = f"帧 {i+1}/{num_frames}"
                            draw.text((width//2 - 300, height//2 + 150), frame_text, 
                                     fill=(255, 255, 255), font=font)
                        except Exception as e:
                            # 如果添加文字失败，记录错误但继续生成视频
                            logger.error(f"在模拟视频中添加文字时出错: {e}")
                        
                        # 转换为numpy数组并存储
                        frames[0, i] = np.array(img)
                    
                    return type('obj', (object,), {'frames': frames})
            
            # 创建简单的调度器
            class MockScheduler:
                def __init__(self, **kwargs):
                    logger.info("[模拟] 创建调度器")
                    for key, value in kwargs.items():
                        setattr(self, key, value)
            
            # 创建模拟调度器和VAE
            scheduler = MockScheduler(beta_start=0.00085, beta_end=0.012, beta_schedule="linear")
            vae = AutoencoderKLWan()
            
            # 创建管道
            pipeline = WanPipeline(scheduler=scheduler, vae=vae)
            pipeline = pipeline.to(self.device)
            
            # 记录模型加载完成
            load_time = time.time() - start_time
            logger.info(f"[模拟] T2V模型 ({model_size}, {resolution}) 加载完成，耗时: {load_time:.2f}秒")
            
            # 保存模型到内存
            if 't2v' not in self.models:
                self.models['t2v'] = {}
            if model_size not in self.models['t2v']:
                self.models['t2v'][model_size] = {}
            
            self.models['t2v'][model_size][resolution] = pipeline
            
            # 更新状态
            self.model_status["t2v"][model_size][resolution]["loaded"] = True
            self.model_status["t2v"][model_size][resolution]["loading"] = False
            self.is_initialized = True
            
            # 更新进度
            await progress_updater.update(
                status="running",
                progress=20,
                logs=[f"[模拟] T2V模型 ({model_size}, {resolution}) 加载完成，准备生成视频"]
            )
            
            return True
        except Exception as e:
            logger.error(f"创建模拟模型失败: {e}")
            return False
    
    def _get_manual_download_guide(self, model_type: str, model_size: str = "14B", resolution: str = "720p") -> str:
        """获取手动下载指南"""
        model_id = self._get_model_id(model_type, model_size, resolution)
        cache_dir = self.model_cache_dir
        
        # 是否是本地路径
        local_path = os.path.normpath(os.path.join(cache_dir, model_id))
        guide = f"""
手动设置指南:
1. 权限问题：无法访问或写入目录 {local_path}
2. 建议解决方案:
   a. 以管理员权限运行应用程序
   b. 修改目录权限，确保当前用户有读写权限
   c. 在环境变量中设置WAN_MODEL_CACHE_DIR为具有写入权限的目录

3. 目录结构要求:
   {local_path}/
      ├── vae/          # VAE模型目录
      │   └── config.json  # 需要创建
      ├── scheduler/    # 调度器配置
      │   └── scheduler_config.json  # 需要创建
      └── unet/         # UNet模型目录
          └── config.json  # 需要创建
"""
        return guide
    
    def _convert_path_to_url(self, file_path: str, file_type: str = "video") -> str:
        """
        将本地文件路径转换为可通过HTTP访问的URL
        
        Args:
            file_path: 本地文件路径
            file_type: 文件类型(video/thumbnail)
            
        Returns:
            str: HTTP URL
        """
        if not file_path:
            logger.warning("尝试转换空文件路径为URL")
            return ""
            
        # 提取文件名
        file_name = os.path.basename(file_path)
        logger.info(f"转换文件路径为URL: {file_path} -> {file_name}")
        
        # 检查文件是否存在
        if not os.path.exists(file_path):
            logger.warning(f"文件不存在: {file_path}")
            # 尝试创建媒体目录
            media_dirs = ['media', 'media/videos', 'media/thumbnails']
            for d in media_dirs:
                os.makedirs(d, exist_ok=True)
                logger.info(f"确保媒体目录存在: {d}")
        else:
            logger.info(f"文件存在，大小: {os.path.getsize(file_path)} 字节")
            
            # 如果文件不在media目录中，复制到media目录
            if file_type == "video" and "media/videos" not in file_path:
                target_path = os.path.join("media", "videos", file_name)
                try:
                    import shutil
                    # 确保目标目录存在
                    os.makedirs(os.path.dirname(target_path), exist_ok=True)
                    # 复制文件
                    shutil.copy2(file_path, target_path)
                    logger.info(f"文件已复制到媒体目录: {target_path}")
                    
                    # 验证文件是否成功复制
                    if os.path.exists(target_path) and os.path.getsize(target_path) > 0:
                        logger.info(f"文件复制成功，大小: {os.path.getsize(target_path)} 字节")
                    else:
                        logger.error(f"文件复制失败或大小为0: {target_path}")
                except Exception as e:
                    logger.error(f"复制文件到媒体目录失败: {e}")
            elif file_type == "thumbnail" and "media/thumbnails" not in file_path:
                target_path = os.path.join("media", "thumbnails", file_name)
                try:
                    import shutil
                    # 确保目标目录存在
                    os.makedirs(os.path.dirname(target_path), exist_ok=True)
                    # 复制文件
                    shutil.copy2(file_path, target_path)
                    logger.info(f"文件已复制到媒体目录: {target_path}")
                    
                    # 验证文件是否成功复制
                    if os.path.exists(target_path) and os.path.getsize(target_path) > 0:
                        logger.info(f"文件复制成功，大小: {os.path.getsize(target_path)} 字节")
                    else:
                        logger.error(f"文件复制失败或大小为0: {target_path}")
                except Exception as e:
                    logger.error(f"复制文件到媒体目录失败: {e}")
        
        # 构建URL (使用/api/media/前缀用于访问媒体文件)
        url = ""
        if file_type == "video":
            url = f"/api/media/videos/{file_name}"
        elif file_type == "thumbnail":
            url = f"/api/media/thumbnails/{file_name}"
        else:
            url = f"/api/media/files/{file_name}"
            
        logger.info(f"生成的URL: {url}")
        
        # 确保URL是绝对路径
        if not url.startswith(('http://', 'https://')):
            # 获取API基础URL
            api_base = os.environ.get('API_BASE_URL', '')
            if api_base:
                if not api_base.endswith('/'):
                    api_base += '/'
                if url.startswith('/'):
                    url = url[1:]  # 移除开头的斜杠以避免重复
                url = f"{api_base}{url}"
                logger.info(f"转换为绝对URL: {url}")
        
        return url
    
    async def generate_video_from_text(
        self,
        task_id: str,
        prompt: str,
        negative_prompt: str = "",
        num_frames: int = 81,  # 约5秒视频(16fps)
        height: int = 720,
        width: int = 1280,
        guidance_scale: float = 5.0,
        fps: int = 16,
        seed: Optional[int] = None,
        model_size: str = "14B",
        resolution: str = "720p", 
        output_format: str = "mp4"
    ) -> Dict[str, Any]:
        """
        从文本生成视频
        
        Args:
            task_id: 任务ID
            prompt: 提示词
            negative_prompt: 负面提示词
            num_frames: 帧数
            height: 高度
            width: 宽度
            guidance_scale: 引导比例
            fps: 帧率
            seed: 随机种子
            model_size: 模型大小 (14B/1.3B)
            resolution: 分辨率 (720p/1080p)
            output_format: 输出格式 (mp4/gif)
            
        Returns:
            Dict: 包含生成结果的字典
        """
        # 创建进度跟踪器
        progress_tracker = get_progress_tracker()
        progress_updater = ProgressUpdater(task_id)
        
        # 记录参数
        logger.info(f"从文本生成视频: task_id={task_id}, prompt='{prompt}', model_size={model_size}")
        logger.info(f"参数: frames={num_frames}, size={width}x{height}, fps={fps}, guidance={guidance_scale}")
        
        # 检查是否强制使用真实模式
        force_real_mode = os.environ.get("WAN_FORCE_REAL_MODE", "false").lower() == "true"
        
        if self.mock_mode:
            if force_real_mode:
                error_msg = "系统设置为强制使用真实模式，但模型文件不完整或无法加载"
                logger.error(error_msg)
                await progress_updater.update(
                    status="failed",
                    error=error_msg,
                    logs=[
                        "❌ 无法生成视频: 系统设置为强制使用真实模式，但模型文件不完整",
                        "⚠️ 请确保正确安装WAN 2.1模型文件，或禁用强制真实模式设置"
                    ]
                )
                return {
                    "success": False,
                    "error": error_msg,
                    "task_id": task_id
                }
            else:
                logger.warning(f"使用模拟模式生成视频: {prompt}")
        
        try:
            # 初始化模型
            await progress_updater.update(
                status="running",
                progress=0,
                logs=[f"初始化T2V模型 ({model_size}, {resolution})..."]
            )
            
            # 确保模型初始化
            model_initialized = await self.initialize_t2v(task_id, progress_updater, model_size, resolution)
            if not model_initialized:
                error_msg = f"模型初始化失败: {self.model_status['t2v'][model_size][resolution]['error']}"
                logger.error(error_msg)
                await progress_updater.update(
                    status="failed",
                    error=error_msg,
                    logs=[f"❌ {error_msg}"]
                )
                return {
                    "success": False,
                    "error": error_msg,
                    "task_id": task_id
                }
            
            # 更新进度
            await progress_updater.update(
                progress=30,
                logs=[f"开始生成视频..."]
            )
            
            # 检查是否使用模拟模式
            if self.mock_mode:
                # 如果强制使用真实模式，则返回错误
                if force_real_mode:
                    error_msg = "系统设置为强制使用真实模式，但当前处于模拟模式"
                    logger.error(error_msg)
                    await progress_updater.update(
                        status="failed",
                        error=error_msg,
                        logs=[
                            "❌ 无法生成视频: 系统设置为强制使用真实模式，但当前处于模拟模式",
                            "⚠️ 请确保正确安装WAN 2.1模型文件，或禁用强制真实模式设置"
                        ]
                    )
                    return {
                        "success": False,
                        "error": error_msg,
                        "task_id": task_id
                    }
                
                logger.info("使用模拟模式生成视频")
                # 直接使用模拟模式生成视频，不进行模型完整性检查
                return await self._generate_mock_video(
                    task_id=task_id,
                    prompt=prompt,
                    negative_prompt=negative_prompt,
                    num_frames=num_frames,
                    height=height,
                    width=width,
                    guidance_scale=guidance_scale,
                    fps=fps,
                    seed=seed,
                    model_size=model_size,
                    resolution=resolution,
                    output_format=output_format,
                    progress_updater=progress_updater
                )
            
            # 使用真实模型生成视频
            try:
                # 获取模型
                model = self.models['t2v'][model_size][resolution]
                
                # 检查模型类型
                if hasattr(model, 'generate'):
                    # 使用WAN模型
                    logger.info("使用WAN模型生成视频")
                    
                    # 更新进度
                    await progress_updater.update(
                        progress=40,
                        logs=[f"使用WAN模型生成视频..."]
                    )
                    
                    # 设置随机种子
                    if seed is None:
                        import random
                        seed = random.randint(0, 2**32 - 1)
                    logger.info(f"使用随机种子: {seed}")
                    
                    # 生成视频
                    frames = model.generate(
                        input_prompt=prompt,
                        size=(width, height),
                        frame_num=num_frames,
                        shift=5.0,  # 默认值
                        sampling_steps=50,  # 默认值
                        guide_scale=guidance_scale,
                        n_prompt=negative_prompt,
                        seed=seed
                    )
                    
                    # 更新进度
                    await progress_updater.update(
                        progress=80,
                        logs=[f"视频生成完成，准备导出..."]
                    )
                    
                    # 确保frames是正确的格式
                    if hasattr(frames, 'frames'):
                        frames = frames.frames
                    
                    # 将PyTorch张量转换为NumPy数组
                    import numpy as np
                    
                    # 详细检查和记录frames的类型和形状
                    logger.info(f"生成的frames类型: {type(frames)}")
                    if hasattr(frames, 'shape'):
                        logger.info(f"frames形状: {frames.shape}")
                    elif isinstance(frames, list):
                        logger.info(f"frames是列表，长度: {len(frames)}")
                        if len(frames) > 0:
                            logger.info(f"第一个元素类型: {type(frames[0])}")
                            if hasattr(frames[0], 'shape'):
                                logger.info(f"第一个元素形状: {frames[0].shape}")
                    
                    # 处理不同类型的frames
                    if isinstance(frames, torch.Tensor):
                        frames_np = frames.cpu().numpy()
                        logger.info(f"PyTorch张量转换为NumPy数组: {frames_np.shape}")
                    elif isinstance(frames, np.ndarray):
                        frames_np = frames
                        logger.info(f"frames已经是NumPy数组: {frames_np.shape}")
                    elif isinstance(frames, list) and len(frames) > 0 and isinstance(frames[0], torch.Tensor):
                        # 尝试转换列表中的第一个张量
                        frames_np = frames[0].cpu().numpy()
                        logger.info(f"从列表中提取第一个张量并转换: {frames_np.shape}")
                    else:
                        # 无法处理的类型，创建一个简单的黑色帧
                        logger.warning(f"无法处理的frames类型，创建黑色帧")
                        frames_np = np.zeros((num_frames, height, width, 3), dtype=np.uint8)
                    
                    # 检查是否需要转置
                    if len(frames_np.shape) == 4 and frames_np.shape[0] == 3:
                        # 可能是(C, T, H, W)格式，转换为(T, H, W, C)
                        logger.info(f"检测到(C, T, H, W)格式，转换为(T, H, W, C)")
                        frames_np = np.transpose(frames_np, (1, 2, 3, 0))
                        logger.info(f"转换后形状: {frames_np.shape}")
                    
                    # 确保值范围合适
                    if frames_np.max() > 1.0 and frames_np.max() <= 255.0:
                        # 已经是适合的范围，转为uint8
                        frames_np = frames_np.astype(np.uint8)
                    elif frames_np.max() <= 1.0:
                        frames_np = (frames_np * 255).astype(np.uint8)
                        logger.info(f"归一化值转换为uint8: {frames_np.dtype}")
                    else:
                        frames_np = np.clip(frames_np, 0, 255).astype(np.uint8)
                        logger.info(f"裁剪值并转换为uint8: {frames_np.dtype}")
                    
                    # 保存视频
                    output_path = os.path.join(tempfile.gettempdir(), f"{task_id}.{output_format}")
                    logger.info(f"保存视频到: {output_path}")
                    
                    # 使用自定义导出函数
                    export_success = self._export_video_with_ffmpeg_params(
                        frames_np, output_path, fps=fps
                    )
                    
                    if not export_success:
                        logger.error("导出视频失败")
                        if progress_updater:
                            await progress_updater.update(
                                status="failed",
                                error="导出视频失败",
                                logs=[f"❌ 导出视频失败"]
                            )
                        return {
                            "success": False,
                            "error": "导出视频失败",
                            "task_id": task_id
                        }
                    
                    # 生成缩略图
                    thumbnail_path = self._generate_thumbnail(output_path)
                    
                    # 转换为URL
                    video_url = self._convert_path_to_url(output_path, "video")
                    thumbnail_url = self._convert_path_to_url(thumbnail_path, "thumbnail")
                    
                    if progress_updater:
                        await progress_updater.update(
                            status="completed",
                            progress=100,
                            logs=[f"视频生成完成"],
                            result={
                                "video_url": video_url,
                                "thumbnail_url": thumbnail_url,
                                "is_mock": True
                            }
                        )
                    
                    return {
                        "success": True,
                        "task_id": task_id,
                        "video_path": output_path,
                        "video_url": video_url,
                        "thumbnail_path": thumbnail_path,
                        "thumbnail_url": thumbnail_url,
                        "is_mock": True
                    }
            except Exception as e:
                logger.error(f"使用WAN模型生成视频时出错: {e}")
                logger.error(traceback.format_exc())
                if progress_updater:
                    await progress_updater.update(
                        status="failed",
                        error="使用WAN模型生成视频失败",
                        logs=[f"❌ 使用WAN模型生成视频失败"]
                    )
                return {
                    "success": False,
                    "error": "使用WAN模型生成视频失败",
                    "task_id": task_id
                }
        except Exception as e:
            # 捕获未被内部try-except捕获的异常
            error_msg = f"视频生成过程中发生未处理的错误: {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            
            # 更新进度
            if progress_updater:
                await progress_updater.update(
                    status="failed",
                    error=error_msg,
                    logs=[f"❌ 未处理的错误: {error_msg}"]
                )
            
            return {
                "success": False,
                "error": error_msg,
                "task_id": task_id
            }

    def _generate_thumbnail(self, video_path: str) -> str:
        """
        从视频生成缩略图
        Args:
            video_path: 视频文件路径
        Returns:
            str: 缩略图文件路径，如果生成失败则返回空字符串
        """
        try:
            logger.info(f"从视频生成缩略图: {video_path}")
            import cv2
            
            # 检查视频文件是否存在
            if not os.path.exists(video_path):
                logger.error(f"视频文件不存在: {video_path}")
                return ""
            
            # 打开视频文件
            video_capture = cv2.VideoCapture(video_path)
            if not video_capture.isOpened():
                logger.error(f"无法打开视频文件: {video_path}")
                return ""
            
            # 获取帧总数
            total_frames = int(video_capture.get(cv2.CAP_PROP_FRAME_COUNT))
            logger.info(f"视频总帧数: {total_frames}")
            
            if total_frames == 0:
                logger.error(f"视频文件为空: {video_path}")
                return ""
            
            # 设置帧位置到1/3处
            target_frame = int(total_frames / 3)
            video_capture.set(cv2.CAP_PROP_POS_FRAMES, target_frame)
            
            # 读取帧
            success, frame = video_capture.read()
            if not success:
                logger.warning(f"无法读取指定帧，尝试读取第一帧")
                video_capture.set(cv2.CAP_PROP_POS_FRAMES, 0)
                success, frame = video_capture.read()
                
                if not success:
                    logger.error(f"无法读取视频帧: {video_path}")
                    video_capture.release()
                    return ""
            
            # 释放视频捕获对象
            video_capture.release()
            
            # 创建缩略图文件路径（将视频扩展名改为jpg）
            thumbnail_path = os.path.splitext(video_path)[0] + ".jpg"
            
            # 调整大小（如果需要）
            max_dimension = 640
            height, width = frame.shape[:2]
            
            if height > max_dimension or width > max_dimension:
                # 保持纵横比调整大小
                if width >= height:
                    new_width = max_dimension
                    new_height = int(height * (max_dimension / width))
                else:
                    new_height = max_dimension
                    new_width = int(width * (max_dimension / height))
                
                frame = cv2.resize(frame, (new_width, new_height), interpolation=cv2.INTER_AREA)
                logger.info(f"调整缩略图大小到: {new_width}x{new_height}")
            
            # 保存缩略图
            cv2.imwrite(thumbnail_path, frame, [cv2.IMWRITE_JPEG_QUALITY, 90])
            
            # 检查缩略图是否成功保存
            if os.path.exists(thumbnail_path) and os.path.getsize(thumbnail_path) > 0:
                logger.info(f"成功生成缩略图: {thumbnail_path}, 大小: {os.path.getsize(thumbnail_path) / 1024:.2f} KB")
                return thumbnail_path
            else:
                logger.error(f"生成的缩略图不存在或大小为0: {thumbnail_path}")
                return ""
        
        except Exception as e:
            logger.error(f"生成缩略图时出错: {str(e)}")
            logger.error(traceback.format_exc())
            return ""
    
# 全局单例实例
_wan_video_service = None

def get_wan_video_service(model_cache_dir: Optional[str] = None, device: Optional[str] = None, 
precision: Optional[str] = None, mock_mode: Optional[bool] = None):
    """
        获取WAN视频服务实例 (单例模式)
        
        Args:
        model_cache_dir: 可选，模型缓存目录
        device: 可选，设备 (cuda/cpu)
        precision: 可选，精度 (float32/float16)
        mock_mode: 可选，是否使用模拟模式
        
        Returns:
        WanVideoService: 服务实例
    """
    global _wan_video_service
    if _wan_video_service is None:
        _wan_video_service = WanVideoService(
            model_cache_dir=model_cache_dir, 
            device=device, 
            precision=precision,
            mock_mode=mock_mode
        )
        logger.info("WAN视频服务已初始化")
   

    elif model_cache_dir is not None or device is not None or precision is not None or mock_mode is not None:
        # 如果已存在实例但指定了新参数，记录警告
        logger.warning("WAN视频服务已存在，忽略新参数")
    return _wan_video_service
