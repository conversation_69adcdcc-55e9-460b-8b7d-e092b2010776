#!/usr/bin/env python3
"""
统一数字人服务 - 整合 MuseTalk 和 Wav2Lip
"""

import asyncio
import subprocess
import os
import tempfile
import time
import threading
import queue
import json
import logging
from pathlib import Path
from typing import Dict, Any, Optional, List
from enum import Enum

logger = logging.getLogger(__name__)

class DigitalHumanEngine(Enum):
    """数字人引擎类型"""
    MUSETALK = "musetalk"
    WAV2LIP = "wav2lip"
    AUTO = "auto"

class QualityLevel(Enum):
    """质量级别"""
    FAST = "fast"           # 快速模式 - 实时对话
    BALANCED = "balanced"   # 平衡模式 - 一般使用
    HIGH = "high"          # 高质量模式 - 专业应用
    ULTRA = "ultra"        # 超高质量 - 最佳效果

class UnifiedDigitalHumanService:
    """统一数字人服务"""
    
    def __init__(self, python_executable=r"D:\Python311\python.exe"):
        self.python_executable = python_executable
        self.project_root = Path(__file__).parent.parent
        
        # MuseTalk 配置
        self.musetalk_dir = self.project_root / "local_models" / "MuseTalk_official"
        self.musetalk_script = self.musetalk_dir / "scripts" / "inference.py"
        
        # Wav2Lip 配置
        self.wav2lip_script = self.project_root / "local_models" / "Wav2Lip" / "inference.py"
        self.wav2lip_checkpoint = self.project_root / "local_models" / "Wav2Lip" / "checkpoints" / "wav2lip_gan.pth"
        
        # 临时文件管理
        self.temp_dir = tempfile.mkdtemp(prefix="unified_dh_")
        
        logger.info("统一数字人服务初始化完成")
    
    async def generate_digital_human(
        self,
        image_path: str,
        audio_path: str,
        output_path: str,
        engine: DigitalHumanEngine = DigitalHumanEngine.AUTO,
        quality: QualityLevel = QualityLevel.BALANCED
    ) -> Dict[str, Any]:
        """生成数字人视频"""
        
        logger.info(f"开始生成数字人视频...")
        logger.info(f"引擎: {engine.value}, 质量: {quality.value}")
        
        # 检查输入文件
        if not os.path.exists(image_path):
            return {"success": False, "error": f"图像文件不存在: {image_path}"}
        
        if not os.path.exists(audio_path):
            return {"success": False, "error": f"音频文件不存在: {audio_path}"}
        
        # 自动选择引擎
        if engine == DigitalHumanEngine.AUTO:
            engine = self._auto_select_engine(quality)
        
        try:
            if engine == DigitalHumanEngine.MUSETALK:
                result = await self._generate_with_musetalk(image_path, audio_path, output_path, quality)
            else:
                result = await self._generate_with_wav2lip(image_path, audio_path, output_path, quality)
            
            return result
            
        except Exception as e:
            logger.error(f"数字人生成失败: {e}")
            return {"success": False, "error": str(e)}
    
    def _auto_select_engine(self, quality: QualityLevel) -> DigitalHumanEngine:
        """自动选择引擎"""
        if quality == QualityLevel.FAST:
            return DigitalHumanEngine.MUSETALK  # 实时场景优先 MuseTalk
        else:
            return DigitalHumanEngine.WAV2LIP   # 高质量场景优先 Wav2Lip
    
    async def _generate_with_musetalk(
        self, 
        image_path: str, 
        audio_path: str, 
        output_path: str, 
        quality: QualityLevel
    ) -> Dict[str, Any]:
        """使用 MuseTalk 生成"""
        
        logger.info("🎬 使用 MuseTalk 生成数字人...")
        
        try:
            # 创建配置文件
            config_file = await self._create_musetalk_config(image_path, audio_path)
            
            # 获取质量参数
            params = self._get_musetalk_params(quality)
            
            # 构建命令
            cmd = [
                self.python_executable,
                str(self.musetalk_script),
                "--inference_config", config_file,
                "--result_dir", str(Path(output_path).parent),
                "--fps", str(params["fps"]),
                "--batch_size", str(params["batch_size"])
            ]
            
            # 执行命令
            result = await self._run_command(cmd, str(self.musetalk_dir), timeout=300)
            
            if result["success"]:
                # 查找生成的文件
                output_files = list(Path(output_path).parent.glob("*musetalk*.mp4"))
                if output_files:
                    latest_file = max(output_files, key=lambda x: x.stat().st_mtime)
                    # 重命名到指定输出路径
                    if latest_file != Path(output_path):
                        latest_file.rename(output_path)
                    
                    file_size = os.path.getsize(output_path)
                    logger.info(f"✅ MuseTalk 生成成功: {file_size} bytes")
                    
                    return {
                        "success": True,
                        "output_path": output_path,
                        "engine": "musetalk",
                        "file_size": file_size,
                        "quality": quality.value
                    }
                else:
                    return {"success": False, "error": "MuseTalk 未生成输出文件"}
            else:
                return {"success": False, "error": f"MuseTalk 执行失败: {result['error']}"}
                
        except Exception as e:
            logger.error(f"MuseTalk 生成异常: {e}")
            return {"success": False, "error": str(e)}
    
    async def _generate_with_wav2lip(
        self, 
        image_path: str, 
        audio_path: str, 
        output_path: str, 
        quality: QualityLevel
    ) -> Dict[str, Any]:
        """使用 Wav2Lip 生成"""
        
        logger.info("🎬 使用 Wav2Lip 生成数字人...")
        
        try:
            # 获取质量参数
            params = self._get_wav2lip_params(quality)
            
            # 预处理图像（如果是高质量模式）
            processed_image = image_path
            if quality in [QualityLevel.HIGH, QualityLevel.ULTRA]:
                processed_image = await self._preprocess_image(image_path)
            
            # 构建命令
            cmd = [
                self.python_executable,
                str(self.wav2lip_script),
                "--checkpoint_path", str(self.wav2lip_checkpoint),
                "--face", processed_image,
                "--audio", audio_path,
                "--outfile", output_path,
                "--pads"] + params["pads"] + [
                "--resize_factor", str(params["resize_factor"]),
                "--fps", str(params["fps"]),
                "--wav2lip_batch_size", str(params["batch_size"]),
                "--face_det_batch_size", str(params["face_det_batch_size"])
            ]
            
            # 执行命令
            result = await self._run_command(cmd, timeout=300)
            
            # 后处理（如果是超高质量模式）
            if result["success"] and quality == QualityLevel.ULTRA:
                enhanced_output = await self._enhance_video(output_path)
                if enhanced_output:
                    output_path = enhanced_output
            
            if result["success"] and os.path.exists(output_path):
                file_size = os.path.getsize(output_path)
                logger.info(f"✅ Wav2Lip 生成成功: {file_size} bytes")
                
                return {
                    "success": True,
                    "output_path": output_path,
                    "engine": "wav2lip",
                    "file_size": file_size,
                    "quality": quality.value
                }
            else:
                return {"success": False, "error": f"Wav2Lip 执行失败: {result['error']}"}
                
        except Exception as e:
            logger.error(f"Wav2Lip 生成异常: {e}")
            return {"success": False, "error": str(e)}
    
    async def _create_musetalk_config(self, image_path: str, audio_path: str) -> str:
        """创建 MuseTalk 配置文件"""
        config_content = f"""task_0:
 video_path: "{image_path.replace(chr(92), '/')}"
 audio_path: "{audio_path.replace(chr(92), '/')}"
 bbox_shift: 0"""
        
        config_file = os.path.join(self.temp_dir, f"config_{int(time.time())}.yaml")
        with open(config_file, 'w') as f:
            f.write(config_content)
        
        return config_file
    
    def _get_musetalk_params(self, quality: QualityLevel) -> Dict[str, Any]:
        """获取 MuseTalk 参数"""
        params = {
            QualityLevel.FAST: {"fps": 20, "batch_size": 16},
            QualityLevel.BALANCED: {"fps": 25, "batch_size": 8},
            QualityLevel.HIGH: {"fps": 30, "batch_size": 4},
            QualityLevel.ULTRA: {"fps": 30, "batch_size": 2}
        }
        return params.get(quality, params[QualityLevel.BALANCED])
    
    def _get_wav2lip_params(self, quality: QualityLevel) -> Dict[str, Any]:
        """获取 Wav2Lip 参数"""
        params = {
            QualityLevel.FAST: {
                "pads": ["0", "10", "0", "0"],
                "resize_factor": 1,
                "fps": 25,
                "batch_size": 32,
                "face_det_batch_size": 8
            },
            QualityLevel.BALANCED: {
                "pads": ["5", "20", "5", "0"],
                "resize_factor": 1,
                "fps": 25,
                "batch_size": 16,
                "face_det_batch_size": 4
            },
            QualityLevel.HIGH: {
                "pads": ["10", "25", "10", "5"],
                "resize_factor": 1,
                "fps": 30,
                "batch_size": 8,
                "face_det_batch_size": 2
            },
            QualityLevel.ULTRA: {
                "pads": ["10", "25", "10", "5"],
                "resize_factor": 1,
                "fps": 30,
                "batch_size": 4,
                "face_det_batch_size": 1
            }
        }
        return params.get(quality, params[QualityLevel.BALANCED])
    
    async def _preprocess_image(self, image_path: str) -> str:
        """预处理图像"""
        # 这里可以添加图像预处理逻辑
        # 现在直接返回原图像
        return image_path
    
    async def _enhance_video(self, video_path: str) -> Optional[str]:
        """增强视频质量"""
        # 这里可以添加视频后处理逻辑
        # 现在直接返回原视频
        return video_path
    
    async def _run_command(self, cmd: List[str], cwd: str = None, timeout: int = 300) -> Dict[str, Any]:
        """运行命令"""
        try:
            result = subprocess.run(
                cmd,
                cwd=cwd,
                capture_output=True,
                text=True,
                timeout=timeout
            )
            
            if result.returncode == 0:
                return {"success": True, "stdout": result.stdout, "stderr": result.stderr}
            else:
                return {"success": False, "error": result.stderr, "stdout": result.stdout}
                
        except subprocess.TimeoutExpired:
            return {"success": False, "error": "命令执行超时"}
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def cleanup(self):
        """清理资源"""
        import shutil
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
        logger.info("资源清理完成")

# 测试函数
async def test_unified_service():
    """测试统一数字人服务"""
    
    print("🚀 测试统一数字人服务...")
    
    service = UnifiedDigitalHumanService()
    
    # 测试参数
    image_path = "backend/uploads/users/anonymous/media/1752645847_template_t001.png"
    audio_path = "backend/data/digital_humans/dh_51317237/dh_51317237_audio.wav"
    
    # 检查输入文件
    if not os.path.exists(image_path):
        print(f"❌ 图像文件不存在: {image_path}")
        return
    
    if not os.path.exists(audio_path):
        print(f"❌ 音频文件不存在: {audio_path}")
        return
    
    try:
        # 测试不同质量级别
        test_cases = [
            (DigitalHumanEngine.MUSETALK, QualityLevel.FAST, "test_unified_musetalk_fast.mp4"),
            (DigitalHumanEngine.WAV2LIP, QualityLevel.BALANCED, "test_unified_wav2lip_balanced.mp4"),
            (DigitalHumanEngine.AUTO, QualityLevel.HIGH, "test_unified_auto_high.mp4")
        ]
        
        for engine, quality, output_file in test_cases:
            print(f"\n🎬 测试 {engine.value} - {quality.value}...")
            
            result = await service.generate_digital_human(
                image_path=image_path,
                audio_path=audio_path,
                output_path=output_file,
                engine=engine,
                quality=quality
            )
            
            if result["success"]:
                print(f"✅ 成功! 引擎: {result['engine']}, 文件大小: {result['file_size']/1024:.1f} KB")
            else:
                print(f"❌ 失败: {result['error']}")
        
        print(f"\n🎉 统一数字人服务测试完成!")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        service.cleanup()

class RealtimeChatDigitalHuman:
    """实时对话数字人系统"""

    def __init__(self, avatar_image_path: str, ai_model: str = "gpt-3.5-turbo"):
        self.avatar_image_path = avatar_image_path
        self.ai_model = ai_model
        self.digital_human_service = UnifiedDigitalHumanService()

        # 处理队列
        self.audio_input_queue = queue.Queue()
        self.text_response_queue = queue.Queue()
        self.video_output_queue = queue.Queue()

        # 状态管理
        self.processing = False
        self.conversation_history = []

        logger.info("实时对话数字人系统初始化完成")

    async def start_chat_session(self):
        """启动对话会话"""
        self.processing = True

        # 启动处理线程
        threads = [
            threading.Thread(target=self._speech_recognition_worker, daemon=True),
            threading.Thread(target=self._ai_response_worker, daemon=True),
            threading.Thread(target=self._video_generation_worker, daemon=True)
        ]

        for thread in threads:
            thread.start()

        logger.info("🎬 实时对话会话已启动")

    def _speech_recognition_worker(self):
        """语音识别工作线程"""
        while self.processing:
            try:
                audio_data = self.audio_input_queue.get(timeout=1.0)
                if audio_data is None:
                    break

                # 简化的语音识别
                text = self._recognize_speech(audio_data)
                if text:
                    logger.info(f"🎤 识别到: {text}")
                    self.text_response_queue.put(("user", text))

            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"语音识别错误: {e}")

    def _ai_response_worker(self):
        """AI响应工作线程"""
        while self.processing:
            try:
                message_type, text = self.text_response_queue.get(timeout=1.0)
                if text is None:
                    break

                if message_type == "user":
                    self.conversation_history.append({"role": "user", "content": text})

                    # 生成AI响应
                    ai_response = self._generate_ai_response(text)
                    if ai_response:
                        logger.info(f"🤖 AI响应: {ai_response}")
                        self.conversation_history.append({"role": "assistant", "content": ai_response})
                        self.text_response_queue.put(("ai", ai_response))

                elif message_type == "ai":
                    # 生成TTS并触发视频生成
                    audio_file = self._text_to_speech(text)
                    if audio_file:
                        self.video_output_queue.put(("generate", audio_file, text))

            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"AI响应错误: {e}")

    def _video_generation_worker(self):
        """视频生成工作线程"""
        while self.processing:
            try:
                task_type, audio_file, text = self.video_output_queue.get(timeout=1.0)
                if task_type is None:
                    break

                if task_type == "generate":
                    # 使用统一服务生成数字人视频
                    output_file = f"realtime_response_{int(time.time())}.mp4"

                    # 使用快速模式进行实时生成
                    result = asyncio.run(
                        self.digital_human_service.generate_digital_human(
                            image_path=self.avatar_image_path,
                            audio_path=audio_file,
                            output_path=output_file,
                            engine=DigitalHumanEngine.MUSETALK,  # 实时场景使用 MuseTalk
                            quality=QualityLevel.FAST
                        )
                    )

                    if result["success"]:
                        self.video_output_queue.put(("ready", result["output_path"], text))
                        logger.info(f"✅ 实时视频生成完成: {result['output_path']}")
                    else:
                        logger.error(f"❌ 视频生成失败: {result['error']}")

                    # 清理临时音频文件
                    if os.path.exists(audio_file):
                        os.remove(audio_file)

            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"视频生成错误: {e}")

    def _recognize_speech(self, audio_data: bytes) -> Optional[str]:
        """语音识别（简化版本）"""
        # 这里可以集成真正的语音识别服务
        return "你好，我想和你聊天"  # 模拟识别结果

    def _generate_ai_response(self, user_input: str) -> str:
        """生成AI响应"""
        # 简化的AI响应逻辑
        responses = {
            "你好": "你好！很高兴见到你！我是你的AI数字人助手。",
            "聊天": "当然可以！我们可以聊任何你感兴趣的话题。",
            "天气": "今天天气很不错呢！适合出去走走。",
            "时间": f"现在是 {time.strftime('%H:%M')}，时间过得真快！",
            "再见": "再见！期待下次和你聊天！"
        }

        for keyword, response in responses.items():
            if keyword in user_input:
                return response

        return "我明白了，请继续说下去。"

    def _text_to_speech(self, text: str) -> Optional[str]:
        """文本转语音"""
        try:
            audio_file = f"temp_tts_{int(time.time())}.wav"

            # 使用 gTTS 生成语音
            cmd = [
                "python", "-c",
                f"""
from gtts import gTTS
tts = gTTS(text="{text}", lang='zh')
tts.save("{audio_file}")
print("TTS完成")
"""
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)

            if result.returncode == 0 and os.path.exists(audio_file):
                return audio_file
            else:
                logger.error(f"TTS失败: {result.stderr}")
                return None

        except Exception as e:
            logger.error(f"TTS异常: {e}")
            return None

    # API 接口方法
    def add_user_audio(self, audio_data: bytes):
        """添加用户语音输入"""
        if self.processing:
            self.audio_input_queue.put(audio_data)

    def get_response_video(self, timeout: float = 30.0) -> Optional[Dict[str, Any]]:
        """获取AI响应视频"""
        try:
            task_type, video_path, text = self.video_output_queue.get(timeout=timeout)
            if task_type == "ready":
                return {
                    "video_path": video_path,
                    "text": text,
                    "timestamp": time.time()
                }
            return None
        except queue.Empty:
            return None

    def stop_chat_session(self):
        """停止对话会话"""
        self.processing = False
        # 发送停止信号
        self.audio_input_queue.put(None)
        self.text_response_queue.put((None, None))
        self.video_output_queue.put((None, None, None))

    def cleanup(self):
        """清理资源"""
        self.stop_chat_session()
        self.digital_human_service.cleanup()

if __name__ == "__main__":
    import logging
    logging.basicConfig(level=logging.INFO)

    asyncio.run(test_unified_service())
