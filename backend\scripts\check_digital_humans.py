#!/usr/bin/env python3
"""
检查数字人表数据的脚本
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from dotenv import load_dotenv
load_dotenv()

from app.core.database import db_manager

def check_digital_humans():
    """检查数字人表数据"""
    
    print("=== 检查数字人表数据 ===")
    
    # 查询所有数字人
    digital_humans = db_manager.execute_query(
        "SELECT * FROM digital_humans ORDER BY created_at DESC"
    )
    
    print(f"✅ 数字人表查询成功")
    print(f"📊 数字人数量: {len(digital_humans)}")
    
    if digital_humans:
        print("\n📋 数字人列表:")
        for i, dh in enumerate(digital_humans, 1):
            print(f"  {i}. ID: {dh['id']}")
            print(f"     名称: {dh['name']}")
            print(f"     描述: {dh['description']}")
            print(f"     状态: {dh['status']}")
            print(f"     创建时间: {dh['created_at']}")
            print()
    else:
        print("\n❌ 数字人表为空！")
        print("\n🔧 创建测试数字人...")
        
        # 创建一些测试数字人
        test_digital_humans = [
            {
                'name': '智能助手小艾',
                'description': '专业的AI助手，可以回答各种问题',
                'voice_id': 'female1',
                'model_type': 'advanced',
                'status': 'ready'
            },
            {
                'name': '客服机器人',
                'description': '24小时在线客服，为您解答疑问',
                'voice_id': 'female2',
                'model_type': 'default',
                'status': 'ready'
            },
            {
                'name': '教育导师',
                'description': '专业的教育指导，帮助学习成长',
                'voice_id': 'male1',
                'model_type': 'advanced',
                'status': 'ready'
            }
        ]
        
        for dh in test_digital_humans:
            try:
                result = db_manager.execute_query(
                    """
                    INSERT INTO digital_humans (name, description, voice_id, model_type, status)
                    VALUES (%s, %s, %s, %s, %s)
                    """,
                    (dh['name'], dh['description'], dh['voice_id'], dh['model_type'], dh['status'])
                )
                print(f"✅ 创建数字人: {dh['name']}")
            except Exception as e:
                print(f"❌ 创建数字人失败: {dh['name']} - {e}")
        
        print("\n🔄 重新查询数字人列表...")
        digital_humans = db_manager.execute_query(
            "SELECT * FROM digital_humans ORDER BY created_at DESC"
        )
        print(f"📊 现在有 {len(digital_humans)} 个数字人")

if __name__ == "__main__":
    check_digital_humans()
