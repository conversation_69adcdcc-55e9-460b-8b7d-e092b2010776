<template>
  <a-form
    ref="formRef"
    :model="form"
    :rules="rules"
    :label-col="{ span: 4 }"
    :wrapper-col="{ span: 18 }"
    :disabled="disabled"
  >
    <a-form-item label="用户名" name="username">
      <a-input 
        v-model:value="form.username" 
        placeholder="请输入用户名" 
        :disabled="isEdit"
      />
    </a-form-item>

    <a-form-item 
      label="密码" 
      name="password"
      v-if="!isEdit"
    >
      <a-input-password v-model:value="form.password" placeholder="请输入密码" />
    </a-form-item>

    <a-form-item label="昵称" name="nickname">
      <a-input v-model:value="form.nickname" placeholder="请输入昵称" />
    </a-form-item>

    <a-form-item label="手机号" name="mobile">
      <a-input v-model:value="form.mobile" placeholder="请输入手机号" />
    </a-form-item>

    <a-form-item label="邮箱" name="email">
      <a-input v-model:value="form.email" placeholder="请输入邮箱" />
    </a-form-item>

    <a-form-item label="头像">
      <avatar-upload v-model:value="form.avatar" />
    </a-form-item>

    <a-form-item label="角色" name="roleIds">
      <a-select
        v-model:value="form.roleIds"
        mode="multiple"
        placeholder="请选择角色"
        style="width: 100%"
      >
        <a-select-option v-for="role in roles" :key="role.id" :value="role.id">
          {{ role.name }}
        </a-select-option>
      </a-select>
    </a-form-item>

    <a-form-item label="状态" name="status">
      <a-radio-group v-model:value="form.status">
        <a-radio value="1">正常</a-radio>
        <a-radio value="0">禁用</a-radio>
      </a-radio-group>
    </a-form-item>

    <a-form-item label="备注" name="remark">
      <a-textarea v-model:value="form.remark" placeholder="请输入备注" :rows="4" />
    </a-form-item>
  </a-form>
</template>

<script>
import { defineComponent, ref, reactive, watch, computed } from 'vue';
import AvatarUpload from '@/components/AvatarUpload.vue';

export default defineComponent({
  name: 'UserForm',
  components: {
    AvatarUpload
  },
  props: {
    // 表单数据
    modelValue: {
      type: Object,
      required: true
    },
    // 是否是编辑模式
    isEdit: {
      type: Boolean,
      default: false
    },
    // 是否禁用表单（用于查看模式）
    disabled: {
      type: Boolean,
      default: false
    },
    // 角色列表
    roles: {
      type: Array,
      default: () => []
    }
  },
  emits: ['update:modelValue', 'submit', 'cancel'],
  setup(props, { emit, expose }) {
    // 表单引用
    const formRef = ref(null);
    
    // 表单数据
    const form = reactive({
      id: '',
      username: '',
      password: '',
      nickname: '',
      mobile: '',
      email: '',
      avatar: '',
      roleIds: [],
      status: '1',
      remark: ''
    });
    
    // 表单验证规则
    const rules = reactive({
      username: [
        { required: true, message: '请输入用户名', trigger: 'blur' },
        { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
      ],
      password: [
        { required: true, message: '请输入密码', trigger: 'blur' },
        { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
      ],
      nickname: [
        { required: true, message: '请输入昵称', trigger: 'blur' }
      ],
      mobile: [
        { pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确', trigger: 'blur' }
      ],
      email: [
        { type: 'email', message: '邮箱格式不正确', trigger: 'blur' }
      ],
      roleIds: [
        { required: true, type: 'array', message: '请选择角色', trigger: 'change' }
      ]
    });
    
    // 监听父组件传入的表单数据变化
    watch(() => props.modelValue, (val) => {
      if (val) {
        Object.keys(form).forEach(key => {
          if (key in val) {
            form[key] = val[key];
          }
        });
      }
    }, { deep: true, immediate: true });
    
    // 监听表单数据变化，同步到父组件
    watch(form, (val) => {
      emit('update:modelValue', { ...val });
    }, { deep: true });
    
    // 验证表单
    const validate = () => {
      return formRef.value.validate();
    };
    
    // 重置表单
    const resetFields = () => {
      formRef.value.resetFields();
    };
    
    // 清除验证
    const clearValidate = (name) => {
      formRef.value.clearValidate(name);
    };
    
    // 提交表单
    const submitForm = () => {
      validate()
        .then(() => {
          emit('submit', { ...form });
        })
        .catch(error => {
          console.log('表单验证失败:', error);
        });
    };
    
    // 取消操作
    const cancelForm = () => {
      emit('cancel');
    };
    
    // 暴露方法给父组件
    expose({
      validate,
      resetFields,
      clearValidate,
      submitForm,
      cancelForm
    });
    
    return {
      formRef,
      form,
      rules
    };
  }
});
</script>

<style lang="less" scoped>
/* 可以添加自定义样式 */
</style> 