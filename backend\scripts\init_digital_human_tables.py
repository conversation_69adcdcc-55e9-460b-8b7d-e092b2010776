"""
初始化数字人相关数据库表
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.sqlalchemy_db import engine, Base
from app.models.digital_human import DigitalHuman, DigitalHumanGeneration

def init_tables():
    """初始化数字人相关表"""
    try:
        print("开始创建数字人相关表...")
        
        # 创建所有表
        Base.metadata.create_all(bind=engine)
        
        print("数字人相关表创建完成！")
        print("创建的表:")
        print("- digital_humans: 数字人基本信息表")
        print("- digital_human_generations: 数字人生成任务表")
        
    except Exception as e:
        print(f"创建表失败: {e}")
        raise

if __name__ == "__main__":
    init_tables()
