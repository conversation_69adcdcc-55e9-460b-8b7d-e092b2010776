"""
数据库配置和连接管理
"""

import os
import sqlite3
from typing import Optional
from contextlib import contextmanager
from .config import settings

try:
    import psycopg2
    import psycopg2.extras
    HAS_POSTGRES = True
except ImportError:
    HAS_POSTGRES = False


class DatabaseManager:
    """数据库管理器"""

    def __init__(self):
        # 检查是否配置了PostgreSQL
        if settings.DATABASE_URL and HAS_POSTGRES:
            self.use_postgres = True
            self.database_url = settings.DATABASE_URL
            print(f"[DB] 使用PostgreSQL数据库: {self.database_url}")
        else:
            self.use_postgres = False
            self.db_path = os.path.join(os.path.dirname(__file__), "../../database.db")
            print(f"[DB] 使用SQLite数据库: {self.db_path}")

        self.init_database()
    
    def init_database(self):
        """初始化数据库"""
        try:
            with self.get_connection() as conn:
                if conn is None:
                    return

                cursor = conn.cursor()

                # 创建用户表 - 适配PostgreSQL和SQLite
                if self.use_postgres:
                    cursor.execute("""
                        CREATE TABLE IF NOT EXISTS users (
                            id SERIAL PRIMARY KEY,
                            username VARCHAR(255) UNIQUE NOT NULL,
                            email VARCHAR(255) UNIQUE NOT NULL,
                            password_hash VARCHAR(255) NOT NULL,
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                        )
                    """)
                else:
                    cursor.execute("""
                        CREATE TABLE IF NOT EXISTS users (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            username TEXT UNIQUE NOT NULL,
                            email TEXT UNIQUE NOT NULL,
                            password_hash TEXT NOT NULL,
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                        )
                    """)

                # 创建数字人表
                if self.use_postgres:
                    cursor.execute("""
                        CREATE TABLE IF NOT EXISTS digital_humans (
                            id SERIAL PRIMARY KEY,
                            name VARCHAR(255) NOT NULL,
                            description TEXT,
                            avatar_path VARCHAR(500),
                            voice_id VARCHAR(100),
                            model_type VARCHAR(100),
                            status VARCHAR(50) DEFAULT 'creating',
                            user_id INTEGER REFERENCES users(id),
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                        )
                    """)
                else:
                    cursor.execute("""
                        CREATE TABLE IF NOT EXISTS digital_humans (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            name TEXT NOT NULL,
                            description TEXT,
                            avatar_path TEXT,
                            voice_id TEXT,
                            model_type TEXT,
                            status TEXT DEFAULT 'creating',
                            user_id INTEGER,
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            FOREIGN KEY (user_id) REFERENCES users (id)
                        )
                    """)

                # 创建翻译历史表
                if self.use_postgres:
                    cursor.execute("""
                        CREATE TABLE IF NOT EXISTS translation_history (
                            id SERIAL PRIMARY KEY,
                            original_text TEXT NOT NULL,
                            translated_text TEXT NOT NULL,
                            source_language VARCHAR(10) NOT NULL,
                            target_language VARCHAR(10) NOT NULL,
                            translation_type VARCHAR(50) DEFAULT 'text',
                            user_id INTEGER REFERENCES users(id),
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                        )
                    """)
                else:
                    cursor.execute("""
                        CREATE TABLE IF NOT EXISTS translation_history (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            original_text TEXT NOT NULL,
                            translated_text TEXT NOT NULL,
                            source_language TEXT NOT NULL,
                            target_language TEXT NOT NULL,
                            translation_type TEXT DEFAULT 'text',
                            user_id INTEGER,
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            FOREIGN KEY (user_id) REFERENCES users (id)
                        )
                    """)

                # 创建AI智能体表
                if self.use_postgres:
                    cursor.execute("""
                        CREATE TABLE IF NOT EXISTS ai_agents (
                            id SERIAL PRIMARY KEY,
                            name VARCHAR(255) NOT NULL,
                            description TEXT,
                            agent_type VARCHAR(100) NOT NULL,
                            config TEXT,
                            status VARCHAR(50) DEFAULT 'active',
                            user_id INTEGER REFERENCES users(id),
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                        )
                    """)
                else:
                    cursor.execute("""
                        CREATE TABLE IF NOT EXISTS ai_agents (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            name TEXT NOT NULL,
                            description TEXT,
                            agent_type TEXT NOT NULL,
                            config TEXT,
                            status TEXT DEFAULT 'active',
                            user_id INTEGER,
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            FOREIGN KEY (user_id) REFERENCES users (id)
                        )
                    """)

                conn.commit()
                print("数据库初始化完成")
                
        except Exception as e:
            if settings.DB_CONTINUE_ON_ERROR:
                print(f"数据库初始化警告: {e}")
            else:
                raise e
    
    @contextmanager
    def get_connection(self):
        """获取数据库连接"""
        conn = None
        try:
            if self.use_postgres:
                conn = psycopg2.connect(
                    self.database_url,
                    cursor_factory=psycopg2.extras.RealDictCursor
                )
            else:
                conn = sqlite3.connect(self.db_path)
                conn.row_factory = sqlite3.Row  # 使结果可以像字典一样访问
            yield conn
        except Exception as e:
            if conn:
                conn.rollback()
            if settings.DB_CONTINUE_ON_ERROR:
                print(f"数据库操作警告: {e}")
                # 返回一个空的连接对象，避免程序崩溃
                yield None
            else:
                raise e
        finally:
            if conn:
                conn.close()
    
    def execute_query(self, query: str, params: tuple = None):
        """执行查询"""
        with self.get_connection() as conn:
            if conn is None:
                return []

            if self.use_postgres:
                cursor = conn.cursor()
            else:
                cursor = conn.cursor()

            if params:
                if self.use_postgres:
                    # PostgreSQL使用%s占位符，psycopg2会自动处理
                    pg_query = query.replace('?', '%s')
                    print(f"[DB] PostgreSQL查询转换: {query} -> {pg_query}")
                    print(f"[DB] 参数: {params}")
                    cursor.execute(pg_query, params)
                else:
                    # SQLite使用?占位符
                    print(f"[DB] SQLite执行查询: {query}")
                    print(f"[DB] 使用参数: {params}")
                    cursor.execute(query, params)
            else:
                print(f"[DB] 执行查询(无参数): {query}")
                cursor.execute(query)

            if query.strip().upper().startswith('SELECT'):
                return cursor.fetchall()
            else:
                conn.commit()
                return cursor.rowcount


# 全局数据库管理器实例
db_manager = DatabaseManager()


def get_db_manager() -> DatabaseManager:
    """获取数据库管理器实例"""
    return db_manager
