#!/usr/bin/env python3
"""
数据库健康检查工具
用于诊断数据库连接和编码问题
"""
import os
import sys
import logging
import psycopg2
import sqlalchemy
from sqlalchemy import create_engine, text, inspect
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.declarative import declarative_base
import traceback
from dotenv import load_dotenv
import json
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session

# 添加父目录到路径，以便导入项目模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("db-health")

# 颜色代码
GREEN = '\033[92m'
YELLOW = '\033[93m'
RED = '\033[91m'
RESET = '\033[0m'
BOLD = '\033[1m'

def print_success(message):
    print(f"{GREEN}✓ {message}{RESET}")

def print_warning(message):
    print(f"{YELLOW}⚠ {message}{RESET}")

def print_error(message):
    print(f"{RED}✗ {message}{RESET}")

def print_header(title):
    print(f"\n{BOLD}{'=' * 10} {title} {'=' * 10}{RESET}")

def print_info(message):
    print(f"ℹ {message}")

def check_environment():
    """检查环境配置"""
    print_header("环境配置检查")
    
    # 检查DATABASE_URL是否存在
    database_url = os.getenv("DATABASE_URL")
    if database_url:
        masked_url = database_url
        # 隐藏密码
        if '@' in masked_url and ':' in masked_url.split('@')[0]:
            parts = masked_url.split('@')
            auth_parts = parts[0].split(':')
            masked_url = f"{auth_parts[0]}:****@{parts[1]}"
        print_success(f"找到DATABASE_URL: {masked_url}")
    else:
        print_error("未找到DATABASE_URL环境变量")
        return False
    
    # 检查数据库类型
    if database_url.startswith("postgresql"):
        print_success("数据库类型: PostgreSQL")
        
        # 检查client_encoding参数
        if "client_encoding=" in database_url:
            encoding = database_url.split("client_encoding=")[1].split("&")[0] if "&" in database_url else database_url.split("client_encoding=")[1]
            print_success(f"客户端编码设置: {encoding}")
        else:
            print_warning("未设置client_encoding参数")
            
    elif database_url.startswith("sqlite"):
        print_success("数据库类型: SQLite")
    else:
        print_warning(f"数据库类型: {database_url.split('://')[0]}")
    
    # 检查DB_CONTINUE_ON_ERROR设置
    if os.getenv("DB_CONTINUE_ON_ERROR", "").lower() in ["true", "1", "yes"]:
        print_success("DB_CONTINUE_ON_ERROR: True (将在数据库错误时继续运行)")
    else:
        print_warning("DB_CONTINUE_ON_ERROR: 未设置或为False (数据库错误时将停止应用)")
    
    # 检查自动初始化设置
    if os.getenv("DB_AUTO_INIT", "").lower() in ["false", "0", "no"]:
        print_warning("DB_AUTO_INIT: False (不会自动创建表)")
    else:
        print_success("DB_AUTO_INIT: True (将自动创建表)")
    
    return True

def check_psycopg2_connection():
    """使用psycopg2检查PostgreSQL连接"""
    print_header("PostgreSQL Direct Connection检查")
    try:
        # 加载环境变量
        load_dotenv()
        
        # 获取数据库URL
        db_url = os.getenv("DATABASE_URL", "")
        if not db_url.startswith("postgresql://"):
            print_warning("不是PostgreSQL连接字符串")
            return False
            
        # 解析连接URL
        url_parts = db_url.replace("postgresql://", "").split("/")
        auth_host = url_parts[0]
        
        if len(url_parts) < 2:
            print_error("数据库URL格式不正确")
            return False
            
        dbname = url_parts[1].split("?")[0]
        
        # 分离用户名密码和主机
        if "@" in auth_host:
            auth, host = auth_host.split("@")
            if ":" in auth:
                username, password = auth.split(":")
            else:
                username = auth
                password = ""
        else:
            host = auth_host
            username = "postgres"
            password = ""
            
        # 分离端口
        if ":" in host:
            hostname, port = host.split(":")
            port = int(port)
        else:
            hostname = host
            port = 5432
            
        print_info(f"尝试连接到: {hostname}:{port}/{dbname}")
        
        # 测试不同编码选项
        encodings = ["latin1", "utf8", "SQL_ASCII"]
        
        for encoding in encodings:
            try:
                print_info(f"\n尝试使用 {encoding} 编码连接...")
                
                # 连接到PostgreSQL
                conn = psycopg2.connect(
                    host=hostname,
                    port=port,
                    user=username,
                    password=password,
                    dbname=dbname,
                    client_encoding=encoding
                )
                
                # 检查连接
                with conn.cursor() as cursor:
                    # 获取服务器版本
                    cursor.execute("SELECT version()")
                    version = cursor.fetchone()[0]
                    print_success(f"✅ PostgreSQL版本: {version.split(',')[0]}")
                    
                    # 检查编码设置
                    cursor.execute("SHOW server_encoding")
                    server_encoding = cursor.fetchone()[0]
                    print_success(f"✅ 服务器编码: {server_encoding}")
                    
                    cursor.execute("SHOW client_encoding")
                    client_encoding = cursor.fetchone()[0]
                    print_success(f"✅ 客户端编码: {client_encoding}")
                    
                    # 检查排序规则
                    cursor.execute(
                        "SELECT datcollate FROM pg_database WHERE datname = %s",
                        (dbname,)
                    )
                    collate = cursor.fetchone()[0]
                    print_success(f"✅ 数据库排序规则: {collate}")
                    
                    # 检查表
                    cursor.execute(
                        "SELECT table_name FROM information_schema.tables "
                        "WHERE table_schema='public'"
                    )
                    tables = [row[0] for row in cursor.fetchall()]
                    print_success(f"✅ 数据库中的表: {', '.join(tables) if tables else '无'}")
                    
                    # 检查表结构
                    if tables:
                        for table in tables:
                            try:
                                cursor.execute(
                                    "SELECT column_name, data_type FROM information_schema.columns "
                                    "WHERE table_schema='public' AND table_name=%s",
                                    (table,)
                                )
                                columns = cursor.fetchall()
                                print_info(f"\n表 {table} 的结构:")
                                for column in columns:
                                    print_info(f"  - {column[0]}: {column[1]}")
                            except Exception as e:
                                print_error(f"❌ 检查表 {table} 结构时出错: {e}")
                
                conn.close()
                print_success(f"\n✅ 使用 {encoding} 编码连接成功!")
                return True
                
            except Exception as e:
                print_error(f"❌ 使用 {encoding} 编码连接失败: {e}")
        
        print_error("\n❌ 所有编码选项都连接失败")
        return False
        
    except Exception as e:
        print_error(f"❌ 检查连接时发生错误: {e}")
        traceback.print_exc()
        return False

def check_sqlalchemy_connection():
    """使用SQLAlchemy检查数据库连接"""
    print_header("SQLAlchemy Connection检查")
    try:
        # 加载环境变量
        load_dotenv()
        
        # 获取数据库URL
        db_url = os.getenv("DATABASE_URL", "")
        if not db_url:
            print_error("❌ 未设置DATABASE_URL环境变量")
            return False
            
        # 连接参数
        connect_args = {}
        
        # 根据数据库类型设置参数
        if db_url.startswith("postgresql://"):
            # 从URL中提取编码，或使用默认值
            if "client_encoding=" in db_url:
                encoding = db_url.split("client_encoding=")[1].split("&")[0]
            else:
                encoding = "latin1"
                
            connect_args = {
                "client_encoding": encoding,
                "connect_timeout": 10,
                "application_name": "db_health_check"
            }
            print_info(f"使用PostgreSQL连接参数: {connect_args}")
        elif db_url.startswith("sqlite://"):
            connect_args = {"check_same_thread": False}
            print_info("使用SQLite连接参数")
            
        # 创建引擎
        print_info("创建SQLAlchemy引擎...")
        engine = create_engine(
            db_url,
            echo=False,
            connect_args=connect_args
        )
        
        # 创建会话
        print_info("创建数据库会话...")
        Session = sessionmaker(bind=engine)
        session = Session()
        
        # 测试连接
        print_info("执行测试查询...")
        result = session.execute(text("SELECT 1"))
        print_success(f"✅ 测试查询结果: {result.fetchone()}")
        
        # 检查表
        print_info("检查数据库表...")
        inspector = inspect(engine)
        tables = inspector.get_table_names()
        
        if tables:
            print_success(f"✅ 数据库中的表: {', '.join(tables)}")
            
            # 检查每个表的结构
            for table in tables:
                print_info(f"\n表 {table} 的结构:")
                columns = inspector.get_columns(table)
                for column in columns:
                    print_info(f"  - {column['name']}: {column['type']}")
        else:
            print_warning("⚠️ 数据库中没有表")
            
        session.close()
        print_success("\n✅ SQLAlchemy连接测试成功!")
        return True
        
    except Exception as e:
        print_error(f"❌ SQLAlchemy连接测试失败: {e}")
        traceback.print_exc()
        return False

def check_import_models():
    """测试导入模型模块"""
    print_header("模型导入测试")
    try:
        print_info("尝试导入用户模型...")
        import models.user
        print_success("✅ 用户模型导入成功")
        
        print_info("尝试导入翻译模型...")
        import models.translation
        print_success("✅ 翻译模型导入成功")
        
        print_info("尝试导入数据库模块...")
        from utils.db import Base, init_db
        print_success("✅ 数据库模块导入成功")
        
        # 检查注册的表
        if Base.metadata.tables:
            tables = list(Base.metadata.tables.keys())
            print_success(f"✅ 注册的表: {', '.join(tables)}")
        else:
            print_warning("⚠️ 没有注册的表")
            
        return True
        
    except Exception as e:
        print_error(f"❌ 模型导入测试失败: {e}")
        traceback.print_exc()
        return False

def check_encoding():
    """测试特殊字符问题"""
    print_header("特殊字符编码测试")
    
    database_url = os.getenv("DATABASE_URL", "")
    if not database_url.startswith("postgresql"):
        print_warning("不是PostgreSQL数据库，跳过此测试")
        return True
    
    # 准备测试字符串
    test_strings = [
        "简体中文",
        "繁體中文",
        "日本語",
        "한국어",
        "Русский",
        "العربية",
        "Español",
        "ไทย",
        "çüğıöşÜĞİÖŞ",
        "😀👍🎉"
    ]
    
    try:
        # 解析连接字符串
        conn_str = database_url.split("://")[1]
        auth, rest = conn_str.split("@")
        
        if ":" in auth:
            username, password = auth.split(":")
        else:
            username = auth
            password = ""
        
        if "/" in rest:
            host_port, dbname_params = rest.split("/", 1)
        else:
            host_port = rest
            dbname_params = ""
        
        if ":" in host_port:
            host, port = host_port.split(":")
        else:
            host = host_port
            port = "5432"
        
        if "?" in dbname_params:
            dbname, params = dbname_params.split("?", 1)
        else:
            dbname = dbname_params
            params = ""
        
        # 测试拉丁编码
        print_info("测试latin1编码连接...")
        conn = psycopg2.connect(
            host=host,
            port=port,
            user=username,
            password=password,
            dbname=dbname,
            client_encoding='latin1'
        )
        
        with conn.cursor() as cur:
            # 创建临时表
            cur.execute("CREATE TEMP TABLE encoding_test (id SERIAL PRIMARY KEY, text_data TEXT, json_data JSONB)")
            
            # 测试插入字符串
            all_passed = True
            for i, test_str in enumerate(test_strings):
                try:
                    # 插入文本
                    cur.execute("INSERT INTO encoding_test (text_data) VALUES (%s) RETURNING id", (test_str,))
                    id = cur.fetchone()[0]
                    
                    # 读取插入的数据
                    cur.execute("SELECT text_data FROM encoding_test WHERE id = %s", (id,))
                    result = cur.fetchone()[0]
                    
                    if result == test_str:
                        print_success(f"字符串 '{test_str}' 编码/解码正确")
                    else:
                        print_error(f"字符串编码失败: 期望 '{test_str}', 得到 '{result}'")
                        all_passed = False
                except Exception as e:
                    print_error(f"插入字符串 '{test_str}' 时出错: {e}")
                    all_passed = False
            
            # 测试JSON字段
            try:
                # 创建测试JSON对象
                test_json = {
                    "strings": test_strings,
                    "nested": {
                        "text": "多语言测试",
                        "array": test_strings
                    }
                }
                
                # 插入JSON
                cur.execute("INSERT INTO encoding_test (json_data) VALUES (%s) RETURNING id", (json.dumps(test_json),))
                id = cur.fetchone()[0]
                
                # 读取JSON
                cur.execute("SELECT json_data FROM encoding_test WHERE id = %s", (id,))
                result = cur.fetchone()[0]
                
                print_success("JSON字段编码测试通过")
                
            except Exception as e:
                print_error(f"JSON编码测试失败: {e}")
                all_passed = False
        
        conn.close()
        
        if all_passed:
            print_success("所有编码测试通过")
        else:
            print_warning("部分编码测试失败")
        
        return all_passed
        
    except Exception as e:
        print_error(f"编码测试失败: {e}")
        return False

def run_diagnostics():
    """运行所有诊断测试"""
    print_header("数据库健康检查工具")
    
    results = {}
    
    # 检查环境
    results["环境检查"] = check_environment()
    
    # 检查模型导入
    results["模型导入"] = check_import_models()
    
    # 如果是PostgreSQL，检查直接连接
    if os.getenv("DATABASE_URL", "").startswith("postgresql://"):
        results["PostgreSQL连接"] = check_psycopg2_connection()
    
    # 检查SQLAlchemy连接
    results["SQLAlchemy连接"] = check_sqlalchemy_connection()
    
    # 编码测试
    results["编码测试"] = check_encoding()
    
    # 诊断摘要
    print_header("诊断摘要")
    all_passed = True
    for test, result in results.items():
        if result:
            print_success(f"{test}: 通过")
        else:
            print_error(f"{test}: 失败")
            all_passed = False
    
    if all_passed:
        print(f"\n{GREEN}{BOLD}所有检查通过! 数据库连接正常{RESET}")
    else:
        print(f"\n{YELLOW}{BOLD}部分检查失败，可能需要进一步调查{RESET}")
    
    print_header("健康检查完成")
    return all_passed

if __name__ == "__main__":
    # 加载环境变量
    load_dotenv()
    
    # 运行诊断
    success = run_diagnostics()
    
    # 设置退出代码
    sys.exit(0 if success else 1) 