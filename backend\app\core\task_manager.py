"""
统一任务管理器
"""
import os
import psycopg2
import psycopg2.extras
from datetime import datetime
from typing import Dict, Any, List, Optional
from app.models.unified_tasks import UnifiedTask, TaskTypes, TaskStatus
import json

class UnifiedTaskManager:
    """统一任务管理器"""
    
    def __init__(self):
        self.db_url = os.getenv("DATABASE_URL")
        self._ensure_table_exists()
    
    def _get_connection(self):
        """获取数据库连接"""
        return psycopg2.connect(self.db_url)
    
    def _ensure_table_exists(self):
        """确保统一任务表存在"""
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            
            # 创建统一任务表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS unified_tasks (
                    id SERIAL PRIMARY KEY,
                    task_id VARCHAR(255) UNIQUE NOT NULL,
                    celery_task_id VARCHAR(255),
                    task_type VARCHAR(50) NOT NULL,
                    task_subtype VARCHAR(50),
                    user_id VARCHAR(255) NOT NULL,
                    status VARCHAR(20) DEFAULT 'pending',
                    progress INTEGER DEFAULT 0,
                    title VARCHAR(500),
                    description TEXT,
                    message TEXT,
                    error_message TEXT,
                    input_params JSONB,
                    output_data JSONB,
                    input_files JSONB,
                    output_files JSONB,
                    estimated_duration INTEGER,
                    actual_duration INTEGER,
                    priority INTEGER DEFAULT 5,
                    queue_name VARCHAR(50),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    started_at TIMESTAMP,
                    completed_at TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    retry_count INTEGER DEFAULT 0,
                    max_retries INTEGER DEFAULT 3,
                    tags JSONB,
                    extra_data JSONB
                )
            """)
            
            # 创建索引
            indexes = [
                "CREATE INDEX IF NOT EXISTS idx_unified_tasks_task_id ON unified_tasks(task_id)",
                "CREATE INDEX IF NOT EXISTS idx_unified_tasks_celery_task_id ON unified_tasks(celery_task_id)",
                "CREATE INDEX IF NOT EXISTS idx_unified_tasks_type ON unified_tasks(task_type)",
                "CREATE INDEX IF NOT EXISTS idx_unified_tasks_status ON unified_tasks(status)",
                "CREATE INDEX IF NOT EXISTS idx_unified_tasks_user_id ON unified_tasks(user_id)",
                "CREATE INDEX IF NOT EXISTS idx_unified_tasks_created_at ON unified_tasks(created_at)",
            ]
            
            for index_sql in indexes:
                cursor.execute(index_sql)
            
            conn.commit()
            cursor.close()
            conn.close()
            
            print("✅ 统一任务表初始化完成")
            
        except Exception as e:
            print(f"❌ 统一任务表初始化失败: {e}")
    
    def create_task(self, task_type: str, task_subtype: str = None, user_id: str = "demo-user",
                   title: str = None, description: str = None, input_params: dict = None,
                   priority: int = 5, estimated_duration: int = None, tags: list = None) -> str:
        """创建新任务"""
        try:
            task = UnifiedTask.create_task(
                task_type=task_type,
                task_subtype=task_subtype,
                user_id=user_id,
                title=title,
                description=description,
                input_params=input_params,
                priority=priority,
                estimated_duration=estimated_duration,
                tags=tags
            )
            
            conn = self._get_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT INTO unified_tasks 
                (task_id, task_type, task_subtype, user_id, title, description, 
                 input_params, priority, estimated_duration, tags, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, [
                task.task_id, task.task_type, task.task_subtype, task.user_id,
                task.title, task.description, json.dumps(task.input_params) if task.input_params else None,
                task.priority, task.estimated_duration, json.dumps(task.tags) if task.tags else None,
                task.created_at, task.updated_at
            ])
            
            conn.commit()
            cursor.close()
            conn.close()
            
            print(f"✅ 任务创建成功: {task.task_id} ({task_type})")
            return task.task_id
            
        except Exception as e:
            print(f"❌ 任务创建失败: {e}")
            raise
    
    def update_task(self, task_id: str, **kwargs) -> bool:
        """更新任务"""
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            
            # 构建更新语句
            update_fields = []
            values = []
            
            allowed_fields = [
                'celery_task_id', 'status', 'progress', 'message', 'error_message',
                'output_data', 'output_files', 'actual_duration', 'started_at',
                'completed_at', 'retry_count', 'extra_data'
            ]
            
            for field, value in kwargs.items():
                if field in allowed_fields:
                    update_fields.append(f"{field} = %s")
                    if field in ['output_data', 'output_files', 'extra_data'] and value is not None:
                        values.append(json.dumps(value))
                    else:
                        values.append(value)
            
            if not update_fields:
                return False
            
            # 总是更新 updated_at
            update_fields.append("updated_at = %s")
            values.append(datetime.utcnow())
            values.append(task_id)
            
            sql = f"UPDATE unified_tasks SET {', '.join(update_fields)} WHERE task_id = %s"
            cursor.execute(sql, values)
            
            updated = cursor.rowcount > 0
            conn.commit()
            cursor.close()
            conn.close()
            
            if updated:
                print(f"✅ 任务更新成功: {task_id}")
            
            return updated
            
        except Exception as e:
            print(f"❌ 任务更新失败: {e}")
            return False
    
    def get_task(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务详情"""
        try:
            conn = self._get_connection()
            cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
            
            cursor.execute("SELECT * FROM unified_tasks WHERE task_id = %s", [task_id])
            result = cursor.fetchone()
            
            cursor.close()
            conn.close()
            
            if result:
                return dict(result)
            return None
            
        except Exception as e:
            print(f"❌ 获取任务失败: {e}")
            return None
    
    def get_user_tasks(self, user_id: str, task_type: str = None, limit: int = 50, offset: int = 0) -> List[Dict[str, Any]]:
        """获取用户任务列表"""
        try:
            conn = self._get_connection()
            cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
            
            if task_type:
                cursor.execute("""
                    SELECT * FROM unified_tasks 
                    WHERE user_id = %s AND task_type = %s 
                    ORDER BY created_at DESC 
                    LIMIT %s OFFSET %s
                """, [user_id, task_type, limit, offset])
            else:
                cursor.execute("""
                    SELECT * FROM unified_tasks 
                    WHERE user_id = %s 
                    ORDER BY created_at DESC 
                    LIMIT %s OFFSET %s
                """, [user_id, limit, offset])
            
            results = cursor.fetchall()
            cursor.close()
            conn.close()
            
            return [dict(row) for row in results]
            
        except Exception as e:
            print(f"❌ 获取用户任务失败: {e}")
            return []
    
    def get_task_stats(self, user_id: str = None) -> Dict[str, Any]:
        """获取任务统计"""
        try:
            conn = self._get_connection()
            cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
            
            if user_id:
                # 用户任务统计
                cursor.execute("""
                    SELECT 
                        task_type,
                        status,
                        COUNT(*) as count
                    FROM unified_tasks 
                    WHERE user_id = %s
                    GROUP BY task_type, status
                    ORDER BY task_type, status
                """, [user_id])
            else:
                # 全局任务统计
                cursor.execute("""
                    SELECT 
                        task_type,
                        status,
                        COUNT(*) as count
                    FROM unified_tasks 
                    GROUP BY task_type, status
                    ORDER BY task_type, status
                """)
            
            results = cursor.fetchall()
            cursor.close()
            conn.close()
            
            # 组织统计数据
            stats = {}
            for row in results:
                task_type = row['task_type']
                status = row['status']
                count = row['count']
                
                if task_type not in stats:
                    stats[task_type] = {}
                stats[task_type][status] = count
            
            return stats
            
        except Exception as e:
            print(f"❌ 获取任务统计失败: {e}")
            return {}

# 全局任务管理器实例
task_manager = UnifiedTaskManager()
