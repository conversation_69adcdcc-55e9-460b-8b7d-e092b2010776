#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WAN模型目录结构修复工具

该脚本用于将WAN模型文件重新组织到符合服务预期的目录结构中。
主要将模型根目录中的文件移动到正确的子目录下。
"""

import os
import sys
import json
import shutil
import logging
import platform
from pathlib import Path
from typing import List, Tuple, Dict, Any, Optional

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 获取项目根目录
PROJECT_ROOT = Path(__file__).parent.parent.parent.absolute()
logger.info(f"项目根目录: {PROJECT_ROOT}")

# 模型路径
MODELS_CACHE_DIR = os.path.join(PROJECT_ROOT, "backend", "models_cache")

# 需要处理的模型列表
MODEL_DIRS = [
    os.path.join(MODELS_CACHE_DIR, "Wan2.1-T2V-1.3B"),
    os.path.join(MODELS_CACHE_DIR, "Wan2.1-T2V-14B")
]

# 预期的目录结构
EXPECTED_SUBDIRS = ["scheduler", "unet", "vae"]

# 文件映射 - 根据文件名或特征将文件移动到对应的子目录
FILE_MAPPINGS = {
    "scheduler": ["scheduler", "scheduler_config.json"],
    "unet": ["unet", "diffusion_pytorch_model", "model_index.json"],
    "vae": ["vae", "Wan2.1_VAE.pth", "decoder"],
}

def create_subdirectories(model_dir):
    """在模型目录下创建所需的子目录"""
    for subdir in EXPECTED_SUBDIRS:
        subdir_path = os.path.join(model_dir, subdir)
        if not os.path.exists(subdir_path):
            os.makedirs(subdir_path, exist_ok=True)
            logger.info(f"创建子目录: {subdir_path}")

def is_file_matching(file_name, patterns):
    """检查文件名是否匹配模式"""
    file_name_lower = file_name.lower()
    for pattern in patterns:
        if pattern.lower() in file_name_lower:
            return True
    return False

def create_scheduler_config(scheduler_dir):
    """创建默认的scheduler配置文件"""
    config_path = os.path.join(scheduler_dir, "scheduler_config.json")
    if not os.path.exists(config_path):
        scheduler_config = {
            "beta_schedule": "linear",
            "beta_start": 0.00085,
            "beta_end": 0.012,
            "clip_sample": False,
            "num_train_timesteps": 1000,
            "prediction_type": "epsilon"
        }
        with open(config_path, 'w') as f:
            json.dump(scheduler_config, f, indent=2)
        logger.info(f"创建默认scheduler配置: {config_path}")

def create_unet_config(unet_dir):
    """创建默认的unet配置文件"""
    config_path = os.path.join(unet_dir, "config.json")
    if not os.path.exists(config_path):
        unet_config = {
            "in_channels": 4,
            "out_channels": 4,
            "model_type": "UNet3DConditionModel",
            "timestep_spacing": "uniform",
            "unified_pc": True,
            "is_wan2": True
        }
        with open(config_path, 'w') as f:
            json.dump(unet_config, f, indent=2)
        logger.info(f"创建默认unet配置: {config_path}")

def create_vae_config(vae_dir):
    """创建默认的vae配置文件"""
    config_path = os.path.join(vae_dir, "config.json")
    if not os.path.exists(config_path):
        vae_config = {
            "act_fn": "silu",
            "down_block_types": ["DownEncoderBlock2D", "DownEncoderBlock2D", "DownEncoderBlock2D", "DownEncoderBlock2D"],
            "latent_channels": 4,
            "layers_per_block": 2,
            "norm_num_groups": 32,
            "out_channels": 3,
            "sample_size": 512,
            "model_type": "AutoencoderKL"
        }
        with open(config_path, 'w') as f:
            json.dump(vae_config, f, indent=2)
        logger.info(f"创建默认vae配置: {config_path}")

def organize_files(model_dir):
    """将模型目录中的文件组织到正确的子目录中"""
    # 首先创建子目录
    create_subdirectories(model_dir)
    
    # 获取模型目录中的所有文件
    files = [f for f in os.listdir(model_dir) if os.path.isfile(os.path.join(model_dir, f))]
    
    # 移动文件到对应的子目录
    moved_files = []
    for file_name in files:
        file_path = os.path.join(model_dir, file_name)
        
        # 跳过一些不需要移动的文件
        if file_name.startswith('.') or file_name in ["README.md", "LICENSE.txt"]:
            continue
            
        # 确定文件应该被移动到哪个子目录
        target_subdir = None
        for subdir, patterns in FILE_MAPPINGS.items():
            if is_file_matching(file_name, patterns):
                target_subdir = subdir
                break
        
        # 如果找不到匹配的子目录，默认移动到unet
        if target_subdir is None:
            if "diffusion" in file_name.lower() or "model" in file_name.lower():
                target_subdir = "unet"
            else:
                logger.warning(f"无法确定文件 {file_name} 的目标子目录，跳过")
                continue
        
        # 移动文件
        target_dir = os.path.join(model_dir, target_subdir)
        target_path = os.path.join(target_dir, file_name)
        
        try:
            shutil.copy2(file_path, target_path)
            logger.info(f"复制文件: {file_path} -> {target_path}")
            moved_files.append((file_name, target_subdir))
        except Exception as e:
            logger.error(f"复制文件 {file_name} 失败: {e}")
    
    # 创建必要的配置文件
    create_scheduler_config(os.path.join(model_dir, "scheduler"))
    create_unet_config(os.path.join(model_dir, "unet"))
    create_vae_config(os.path.join(model_dir, "vae"))
    
    # 复制VAE文件
    vae_file = os.path.join(model_dir, "Wan2.1_VAE.pth")
    if os.path.exists(vae_file):
        target_vae = os.path.join(model_dir, "vae", "diffusion_pytorch_model.safetensors")
        if not os.path.exists(target_vae):
            try:
                shutil.copy2(vae_file, target_vae)
                logger.info(f"复制VAE模型: {vae_file} -> {target_vae}")
            except Exception as e:
                logger.error(f"复制VAE模型失败: {e}")
    
    # 复制Diffusion模型
    diffusion_file = os.path.join(model_dir, "diffusion_pytorch_model.safetensors")
    if os.path.exists(diffusion_file):
        target_unet = os.path.join(model_dir, "unet", "diffusion_pytorch_model.safetensors")
        if not os.path.exists(target_unet):
            try:
                shutil.copy2(diffusion_file, target_unet)
                logger.info(f"复制Diffusion模型: {diffusion_file} -> {target_unet}")
            except Exception as e:
                logger.error(f"复制Diffusion模型失败: {e}")
    
    # 创建模型索引文件
    model_index_path = os.path.join(model_dir, "model_index.json")
    if not os.path.exists(model_index_path):
        model_index = {
            "format": "wan2",
            "submodel_types": {
                "unet": "UNet3DConditionModel",
                "vae": "AutoencoderKL",
                "scheduler": "DDIMScheduler"
            },
            "_diffusers_version": "0.26.0",
            "wan_version": "2.1"
        }
        with open(model_index_path, 'w') as f:
            json.dump(model_index, f, indent=2)
        logger.info(f"创建模型索引: {model_index_path}")
    
    return moved_files

def fix_model_structure(model_dir):
    """修复单个模型的目录结构"""
    logger.info(f"开始修复模型目录结构: {model_dir}")
    
    # 检查模型目录是否存在
    if not os.path.exists(model_dir):
        logger.error(f"模型目录不存在: {model_dir}")
        return False
    
    # 组织文件
    moved_files = organize_files(model_dir)
    
    # 创建日志
    log_message = f"已处理 {len(moved_files)} 个文件:\n"
    for file_name, target_subdir in moved_files:
        log_message += f"  - {file_name} -> {target_subdir}\n"
    logger.info(log_message)
    
    # 检查目录结构是否完整
    missing_subdirs = []
    for subdir in EXPECTED_SUBDIRS:
        subdir_path = os.path.join(model_dir, subdir)
        if not os.path.exists(subdir_path) or not os.path.isdir(subdir_path) or not os.listdir(subdir_path):
            missing_subdirs.append(subdir)
    
    if missing_subdirs:
        logger.error(f"目录结构修复后仍不完整，缺少子目录或子目录为空: {', '.join(missing_subdirs)}")
        return False
    
    logger.info(f"模型目录结构修复完成: {model_dir}")
    return True

def fix_all_wan_models(models_cache_dir=None, quiet=False):
    """
    修复所有WAN模型的目录结构
    
    Args:
        models_cache_dir: 模型缓存目录，默认使用项目根目录下的backend/models_cache
        quiet: 是否抑制输出
    
    Returns:
        Tuple[int, int]: 成功和失败的数量
    """
    # 设置日志级别
    if quiet:
        logger.setLevel(logging.WARNING)
    
    # 设置模型缓存目录
    if models_cache_dir is None:
        models_cache_dir = os.path.join(PROJECT_ROOT, "backend", "models_cache")
    
    # 查找WAN模型目录
    model_dirs = []
    if os.path.exists(models_cache_dir):
        for item in os.listdir(models_cache_dir):
            if item.startswith("Wan2.1-T2V-"):
                model_dirs.append(os.path.join(models_cache_dir, item))
    
    # 修复所有模型的目录结构
    success_count = 0
    failed_count = 0
    
    for model_dir in model_dirs:
        if fix_model_structure(model_dir):
            success_count += 1
        else:
            failed_count += 1
    
    return success_count, failed_count

def main():
    print("\n===== WAN模型目录结构修复工具 =====\n")
    
    print(f"系统信息:")
    print(f"  操作系统: {platform.system()} {platform.release()}")
    print(f"  Python版本: {platform.python_version()}")
    print(f"  工作目录: {os.getcwd()}")
    print()
    
    # 获取模型缓存目录
    models_cache_dir = os.path.join(PROJECT_ROOT, "backend", "models_cache")
    
    # 查找WAN模型目录
    model_dirs = []
    if os.path.exists(models_cache_dir):
        for item in os.listdir(models_cache_dir):
            if item.startswith("Wan2.1-T2V-"):
                model_dirs.append(os.path.join(models_cache_dir, item))
    
    print(f"模型目录:")
    for model_dir in model_dirs:
        print(f"  - {model_dir}")
    print()
    
    # 修复所有模型的目录结构
    success_count = 0
    failed_count = 0
    
    for model_dir in model_dirs:
        print(f"\n处理模型: {os.path.basename(model_dir)}")
        if fix_model_structure(model_dir):
            success_count += 1
            print(f"✓ 目录结构修复成功: {os.path.basename(model_dir)}")
        else:
            failed_count += 1
            print(f"✗ 目录结构修复失败: {os.path.basename(model_dir)}")
    
    print("\n修复结果:")
    print(f"  成功: {success_count}")
    print(f"  失败: {failed_count}")
    print()
    
    if failed_count == 0:
        print("所有模型目录结构已成功修复！")
    else:
        print("部分模型目录结构修复失败，请检查日志。")
    
    print("\n请重新启动应用程序以应用修复。")
    print("\n===== 修复过程完成 =====\n")

if __name__ == "__main__":
    main() 