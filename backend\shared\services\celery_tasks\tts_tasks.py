"""
TTS（文本转语音）相关的Celery任务
"""
import os
import time
import asyncio
import logging
from typing import Dict, Any, Optional
from celery import shared_task
from celery.signals import task_prerun, task_postrun, task_failure

# 导入服务和工具
from services.celery_app import app
from services.tts_service import get_tts_service
from services.progress_tracker import get_progress_tracker
from services.progress_updater import ProgressUpdater
from services.minio_storage import save_audio_to_storage, get_audio_url

# 配置日志
logger = logging.getLogger(__name__)

@shared_task(name="generate_tts", bind=True, max_retries=2)
def generate_tts_task(
    self,
    task_id: str,
    text: str,
    voice_id: str,
    output_format: str = "wav",
    speed: float = 1.0,
    user_id: Optional[int] = None
) -> Dict[str, Any]:
    """
    文本转语音生成任务
    
    参数:
        self: Celery任务实例
        task_id: 任务ID
        text: 待转换文本
        voice_id: 声音ID
        output_format: 输出格式 (wav, mp3)
        speed: 语速
        user_id: 用户ID
        
    返回:
        包含任务结果的字典
    """
    start_time = time.time()
    success = False
    generation_time = 0
    error_message = None
    output_path = None
    progress_updater = ProgressUpdater(task_id)
    
    try:
        # 创建异步事件循环
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        # 更新初始状态
        loop.run_until_complete(progress_updater.update(
            status="initialization",
            progress=5,
            message="正在初始化..."
        ))
        
        # 获取TTS服务
        tts_service = get_tts_service()
        logger.info(f"开始处理TTS任务: {task_id}, 声音: {voice_id}")
        
        # 更新任务状态为模型加载中
        loop.run_until_complete(progress_updater.update(
            status="loading_model",
            progress=10,
            message="正在加载TTS模型..."
        ))
        
        # 更新任务状态为生成中
        loop.run_until_complete(progress_updater.update(
            status="generating",
            progress=30,
            message="正在生成语音..."
        ))
        
        # 生成语音文件
        output_path = loop.run_until_complete(tts_service.generate_speech(
            text=text,
            voice_id=voice_id,
            output_format=output_format,
            speed=speed
        ))
        
        # 检查输出路径是否有效
        if not output_path or not os.path.exists(output_path):
            error_message = "语音生成失败，未生成输出文件"
            logger.error(error_message)
            loop.run_until_complete(progress_updater.update(
                status="error",
                progress=0,
                message="语音生成失败",
                error=error_message
            ))
            
            return {
                "success": False,
                "error": error_message,
                "task_id": task_id
            }
        
        # 更新任务状态为保存中
        loop.run_until_complete(progress_updater.update(
            status="saving",
            progress=85,
            message="正在保存语音文件..."
        ))
        
        # 保存到存储服务
        audio_url = loop.run_until_complete(save_audio_to_storage(task_id, output_path))
        if not audio_url:
            error_message = "保存语音文件到存储服务失败"
            logger.error(error_message)
            loop.run_until_complete(progress_updater.update(
                status="error",
                progress=0,
                message="保存语音文件失败",
                error=error_message
            ))
            
            return {
                "success": False,
                "error": error_message,
                "task_id": task_id
            }
        
        # 生成成功，更新状态
        success = True
        generation_time = time.time() - start_time
        logger.info(f"语音生成成功: {task_id}, 用时: {generation_time:.2f}秒")
        
        loop.run_until_complete(progress_updater.update(
            status="completed",
            progress=100,
            message="语音生成完成",
            audio_url=audio_url
        ))
        
        return {
            "success": True,
            "task_id": task_id,
            "audio_url": audio_url,
            "generation_time": generation_time
        }
        
    except Exception as e:
        error_message = str(e)
        logger.exception(f"语音生成异常: {task_id}, 错误: {e}")
        
        try:
            # 确保异步事件循环可用
            if 'loop' not in locals() or loop is None:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
            loop.run_until_complete(progress_updater.update(
                status="error",
                progress=0,
                message="语音生成异常",
                error=error_message
            ))
        except Exception as update_error:
            logger.error(f"更新任务状态失败: {update_error}")
        
        # 尝试重试任务
        retry_count = self.request.retries
        if retry_count < self.max_retries:
            logger.info(f"尝试重试TTS任务: {task_id}, 当前重试次数: {retry_count + 1}")
            self.retry(exc=e)
        
        return {
            "success": False,
            "error": error_message,
            "task_id": task_id
        }
    finally:
        # 确保关闭事件循环
        try:
            if 'loop' in locals() and loop is not None:
                loop.close()
        except Exception as close_error:
            logger.error(f"关闭事件循环失败: {close_error}")
        
        # 清理临时文件
        try:
            if output_path and os.path.exists(output_path):
                os.remove(output_path)
                logger.debug(f"已清理临时文件: {output_path}")
        except Exception as file_error:
            logger.error(f"清理临时文件失败: {file_error}") 