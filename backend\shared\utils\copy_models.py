import os
import sys
import logging
import shutil
from pathlib import Path

# 添加项目根目录到Python路径
current_file = os.path.abspath(__file__)
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_file)))
sys.path.append(project_root)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def copy_models():
    """确保前端和后端的模型文件同步"""
    try:
        # 前端和后端模型目录
        frontend_model_dir = os.path.join(project_root, "frontend", "public", "assets", "models")
        backend_model_dir = os.path.join(project_root, "backend", "assets", "models")
        
        # 确保目录存在
        Path(frontend_model_dir).mkdir(parents=True, exist_ok=True)
        Path(backend_model_dir).mkdir(parents=True, exist_ok=True)
        
        # 同步前端到后端
        sync_models(frontend_model_dir, backend_model_dir)
        
        # 同步后端到前端
        sync_models(backend_model_dir, frontend_model_dir)
        
        logger.info("模型文件同步完成!")
    except Exception as e:
        logger.error(f"模型文件同步失败: {e}")

def sync_models(source_dir, target_dir):
    """将源目录中的模型文件同步到目标目录"""
    try:
        # 获取源目录中的所有GLB/GLTF文件
        model_files = []
        for file in os.listdir(source_dir):
            if file.lower().endswith((".glb", ".gltf")):
                model_files.append(file)
        
        # 同步到目标目录
        for file_name in model_files:
            source_path = os.path.join(source_dir, file_name)
            target_path = os.path.join(target_dir, file_name)
            
            # 如果目标文件不存在或源文件较新，则复制
            if not os.path.exists(target_path) or os.path.getmtime(source_path) > os.path.getmtime(target_path):
                shutil.copy2(source_path, target_path)
                logger.info(f"已复制模型 {file_name}: {source_path} -> {target_path}")
            else:
                logger.info(f"模型已存在且为最新: {target_path}")
    
    except Exception as e:
        logger.error(f"同步模型失败 '{source_dir}' -> '{target_dir}': {e}")


if __name__ == "__main__":
    logger.info("开始同步模型文件...")
    copy_models() 