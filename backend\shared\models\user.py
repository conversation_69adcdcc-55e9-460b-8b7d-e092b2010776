from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>umn, Integer, String, DateTime
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from pydantic import BaseModel, EmailStr, ConfigDict
from datetime import datetime
from typing import Optional, List
from passlib.context import CryptContext

from utils.db import Base

class User(Base):
    """User database model"""
    __tablename__ = "users"
    __table_args__ = {'extend_existing': True}  # 允许表被多次定义，解决SQLAlchemy映射冲突

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String, unique=True, index=True)
    email = Column(String, unique=True, index=True)
    hashed_password = Column(String)
    full_name = Column(String)
    is_active = Column(Boolean, default=True)
    is_admin = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 修正关系定义 - 使用back_populates而非primaryjoin，与TaskModel匹配
    tasks = relationship("TaskModel", back_populates="user", lazy="dynamic")
    
    # 与Agent相关的关系 - 使用字符串形式
    created_agents = relationship("Agent", back_populates="author")
    agent_favorites = relationship("UserFavorite", back_populates="user", cascade="all, delete-orphan")
    agent_reviews = relationship("AgentReview", back_populates="user", cascade="all, delete-orphan")

    # 添加与聊天会话的关系
    digital_humans = relationship("DigitalHuman", back_populates="user", lazy="dynamic")
    chat_sessions = relationship("ChatSession", back_populates="user")
    
    # 添加与术语管理相关的关系
    terminology = relationship("Terminology", back_populates="user")
    mono_corpora = relationship("MonoCorpus", back_populates="user")
    terms = relationship("Term", back_populates="user")
    # 添加与术语采集任务的关系
    collection_tasks = relationship("TerminologyCollectionTask", back_populates="user")
    # 添加与语料库的关系
    corpora = relationship("Corpus", back_populates="owner")

# Pydantic models for API
class UserBase(BaseModel):
    username: str
    email: EmailStr
    full_name: Optional[str] = None

class UserCreate(UserBase):
    password: str

class UserUpdate(BaseModel):
    email: Optional[EmailStr] = None
    full_name: Optional[str] = None
    password: Optional[str] = None
    is_active: Optional[bool] = None

class UserInDB(UserBase):
    id: int
    is_active: bool
    is_admin: bool
    created_at: datetime
    updated_at: Optional[datetime] = None

    model_config = ConfigDict(
        from_attributes=True,
        arbitrary_types_allowed=True
    )

class UserResponse(UserBase):
    id: int
    is_active: bool
    is_admin: bool

    model_config = ConfigDict(
        from_attributes=True,
        arbitrary_types_allowed=True
    )

class UserLogin(BaseModel):
    username: str
    password: str

    def verify_password(self, password: str) -> bool:
        """验证密码"""
        return pwd_context.verify(password, self.hashed_password) 