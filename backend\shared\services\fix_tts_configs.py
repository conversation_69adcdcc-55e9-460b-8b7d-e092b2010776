#!/usr/bin/env python
"""
TTS配置文件修复工具

此脚本用于修复TTS模型配置文件中的问题，确保TTS功能正常工作。
"""

import os
import json
import re
import logging
import sys
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger("tts-config-fix")

# 当前项目根目录
ROOT_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
# TTS模型目录
TTS_DIR = os.path.join(ROOT_DIR, "models", "tts")
# 正确的TTS模型目录
TTS_MODELS_DIR = os.path.join(TTS_DIR, "tts_models")
# 旧的模型目录
OLD_TTS_MODELS_DIR = os.path.join(TTS_DIR, "tts")

def clean_json_comments(json_str):
    """
    清理JSON字符串中的注释和其他非标准JSON元素
    """
    # 移除 // 注释
    json_str = re.sub(r'//.*?$', '', json_str, flags=re.MULTILINE)
    # 移除 /* ... */ 注释
    json_str = re.sub(r'/\*.*?\*/', '', json_str, flags=re.DOTALL)
    # 替换尾随逗号
    json_str = re.sub(r',(\s*[\]}])', r'\1', json_str)
    # 替换JavaScript中的Infinity为大整数
    json_str = re.sub(r':\s*Infinity', r': 1000000000', json_str)
    # 替换不可见字符
    for i in range(32):
        json_str = json_str.replace(chr(i), '')
    json_str = json_str.replace(chr(127), '')
    
    return json_str

def fix_config_file(config_path):
    """
    修复TTS模型配置文件中可能存在的问题
    
    Args:
        config_path: 配置文件路径
        
    Returns:
        bool: 是否成功修复
    """
    logger.info(f"修复配置文件: {config_path}")
    
    if not os.path.exists(config_path):
        logger.error(f"配置文件不存在: {config_path}")
        return False
    
    try:
        # 读取原始配置文件
        with open(config_path, 'r', encoding='utf-8', errors='ignore') as f:
            config_str = f.read()
        
        # 备份原始文件
        backup_path = f"{config_path}.bak"
        if not os.path.exists(backup_path):
            with open(backup_path, 'w', encoding='utf-8') as f:
                f.write(config_str)
            logger.info(f"已创建配置文件备份: {backup_path}")
        
        # 清理JSON
        clean_config_str = clean_json_comments(config_str)
        
        # 解析JSON
        try:
            config_data = json.loads(clean_config_str)
            
            # 修复特定字段
            
            # 1. 确保audio字段存在
            if "audio" not in config_data:
                logger.info("配置中添加缺失的audio字段")
                config_data["audio"] = {
                    "sample_rate": 22050,
                    "win_length": 1024,
                    "hop_length": 256,
                    "min_level_db": -100,
                    "ref_level_db": 20,
                    "mel_fmin": 0,
                    "mel_fmax": 8000,
                    "max_norm": 4.0,
                    "min_norm": -4.0
                }
            
            # 2. 处理无限值问题
            for field in ["max_audio_len", "max_text_len"]:
                if field in config_data and (config_data[field] == "Infinity" or str(config_data[field]).lower() == "infinity"):
                    logger.info(f"将{field}从Infinity修改为大整数")
                    config_data[field] = 1000000000
            
            # 3. 确保模型字段存在
            if "model" not in config_data:
                logger.info("配置中添加缺失的model字段")
                if "zh-CN" in config_path:
                    config_data["model"] = "tacotron2"
                    config_data["language"] = "zh-cn"
                elif "en" in config_path:
                    config_data["model"] = "tacotron2"
                    config_data["language"] = "en"
                else:
                    config_data["model"] = "tacotron2"
                    config_data["language"] = "en"
            
            # 4. 对于多语言模型，确保language_ids和speaker_ids存在
            if "vits" in config_data.get("model", "").lower() or "your_tts" in config_path.lower():
                if "language_ids" not in config_data:
                    logger.info("为多语言模型添加language_ids字段")
                    config_data["language_ids"] = {
                        "en": 0,
                        "fr": 1,
                        "de": 2,
                        "es": 3,
                        "it": 4,
                        "pt": 5,
                        "pt-br": 5,
                        "pl": 6,
                        "tr": 7,
                        "ru": 8,
                        "nl": 9,
                        "cs": 10,
                        "ar": 11,
                        "zh-cn": 12
                    }
                
                if "speaker_ids" not in config_data and "your_tts" in config_path.lower():
                    logger.info("为your_tts模型添加speaker_ids字段")
                    config_data["speaker_ids"] = {
                        "ED": 0,
                        "VCTK_p225": 1,
                        "VCTK_p234": 2,
                        "VCTK_p238": 3,
                        "VCTK_p245": 4,
                        "VCTK_p248": 5,
                        "VCTK_p260": 6,
                        "VCTK_p273": 7,
                        "VCTK_p294": 8,
                        "VCTK_p302": 9,
                        "VCTK_p326": 10,
                        "VCTK_p335": 11,
                        "VCTK_p347": 12,
                        "VCTK_p361": 13,
                        "VCTK_p362": 14
                    }
            
            # 保存修复后的配置
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=4, ensure_ascii=False)
            
            logger.info(f"成功修复配置文件: {config_path}")
            return True
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析错误: {e}")
            
            # 尝试使用更简单的替换方法
            fixed_str = config_str
            
            # 替换Infinity
            fixed_str = re.sub(r':\s*Infinity', r': 1000000000', fixed_str)
            
            # 尝试保存
            try:
                with open(config_path, 'w', encoding='utf-8') as f:
                    f.write(fixed_str)
                logger.info(f"使用简单替换成功修复配置文件: {config_path}")
                return True
            except Exception as e2:
                logger.error(f"简单替换也失败: {e2}")
                
            # 恢复备份
            try:
                if os.path.exists(backup_path):
                    shutil.copy2(backup_path, config_path)
                    logger.info(f"已恢复原始配置文件: {config_path}")
            except Exception:
                pass
            
            return False
    except Exception as e:
        logger.error(f"修复配置文件时发生错误: {e}")
        return False

def find_config_files():
    """
    查找所有TTS模型配置文件
    
    Returns:
        list: 配置文件路径列表
    """
    config_files = []
    
    # 在正确的模型目录中查找
    if os.path.exists(TTS_MODELS_DIR):
        for root, _, files in os.walk(TTS_MODELS_DIR):
            for file in files:
                if file == "config.json":
                    config_files.append(os.path.join(root, file))
    
    # 在旧的模型目录中查找
    if os.path.exists(OLD_TTS_MODELS_DIR):
        for root, _, files in os.walk(OLD_TTS_MODELS_DIR):
            for file in files:
                if file == "config.json":
                    config_files.append(os.path.join(root, file))
    
    return config_files

def main():
    """
    主函数
    """
    try:
        logger.info("开始修复TTS模型配置文件...")
        
        # 查找所有配置文件
        config_files = find_config_files()
        logger.info(f"找到 {len(config_files)} 个配置文件")
        
        # 修复每个配置文件
        success_count = 0
        failure_count = 0
        
        for config_path in config_files:
            if fix_config_file(config_path):
                success_count += 1
            else:
                failure_count += 1
        
        logger.info(f"修复完成: {success_count} 个成功, {failure_count} 个失败")
        
        return 0 if failure_count == 0 else 1
    except Exception as e:
        logger.error(f"执行过程中发生错误: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return 1

if __name__ == "__main__":
    sys.exit(main()) 