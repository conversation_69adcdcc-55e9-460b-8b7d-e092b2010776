from sqlalchemy import Column, Integer, String, Text, Boolean, ForeignKey, DateTime, Float
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from utils.db import Base

class Corpus(Base):
    """语料库模型"""
    __tablename__ = "corpus"
    __table_args__ = {'extend_existing': True}  # 允许表被多次定义，解决SQLAlchemy映射冲突

    corpus_id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    name = Column(String(100), nullable=False, comment="语料库名称")
    description = Column(Text, nullable=True, comment="语料库描述")
    type = Column(String(20), nullable=False, comment="语料库类型: 单语库, 多语库, 术语库")
    language = Column(String(10), nullable=False, comment="主要语言代码")
    is_public = Column(Boolean, default=False, comment="是否公开")
    owner_id = Column(Integer, ForeignKey("users.id"), nullable=False, comment="所有者ID")
    team_id = Column(Integer, ForeignKey("teams.id"), nullable=True, comment="团队ID")
    entry_count = Column(Integer, default=0, comment="条目数量")
    created_at = Column(DateTime, server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now(), comment="更新时间")

    # 关联关系
    owner = relationship("User", back_populates="corpora")
    team = relationship("Team", back_populates="corpora")
    entries = relationship("CorpusEntry", back_populates="corpus", cascade="all, delete-orphan")

    def to_dict(self):
        """转换为字典"""
        return {
            "corpus_id": self.corpus_id,
            "name": self.name,
            "description": self.description,
            "type": self.type,
            "language": self.language,
            "is_public": self.is_public,
            "owner_id": self.owner_id,
            "team_id": self.team_id,
            "entry_count": self.entry_count,
            "created_at": self.created_at.strftime("%Y-%m-%d %H:%M:%S") if self.created_at else None,
            "updated_at": self.updated_at.strftime("%Y-%m-%d %H:%M:%S") if self.updated_at else None
        }


class CorpusEntry(Base):
    """语料库条目模型"""
    __tablename__ = "corpus_entries"
    __table_args__ = {'extend_existing': True}  # 允许表被多次定义，解决SQLAlchemy映射冲突

    entry_id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    corpus_id = Column(Integer, ForeignKey("corpus.corpus_id"), nullable=False, comment="语料库ID")
    content = Column(Text, nullable=False, comment="内容文本")
    language = Column(String(10), nullable=False, comment="语言代码")
    source = Column(String(255), nullable=True, comment="来源")
    created_at = Column(DateTime, server_default=func.now(), comment="创建时间")

    # 多语条目的平行文本
    parallel_id = Column(Integer, nullable=True, comment="平行条目组ID")
    
    # 关联关系
    corpus = relationship("Corpus", back_populates="entries")

    def to_dict(self):
        """转换为字典"""
        return {
            "entry_id": self.entry_id,
            "corpus_id": self.corpus_id,
            "content": self.content,
            "language": self.language,
            "source": self.source,
            "parallel_id": self.parallel_id,
            "created_at": self.created_at.strftime("%Y-%m-%d %H:%M:%S") if self.created_at else None
        }


class CorpusSearchResult:
    """搜索结果模型(非数据库模型)"""
    def __init__(self, entry_id, match, left_context, right_context, language, source):
        self.entry_id = entry_id
        self.match = match
        self.left_context = left_context
        self.right_context = right_context
        self.language = language
        self.source = source

    def to_dict(self):
        return {
            "entry_id": self.entry_id,
            "match": self.match,
            "left_context": self.left_context,
            "right_context": self.right_context,
            "language": self.language,
            "source": self.source
        }


class CollocationResult:
    """搭配词结果模型(非数据库模型)"""
    def __init__(self, word, frequency, score, position):
        self.word = word
        self.frequency = frequency
        self.score = score
        self.position = position  # 'before' or 'after'

    def to_dict(self):
        return {
            "word": self.word,
            "frequency": self.frequency,
            "score": self.score,
            "position": self.position
        }


class WordFrequencyResult:
    """词频结果模型(非数据库模型)"""
    def __init__(self, word, frequency, percentage):
        self.word = word
        self.frequency = frequency
        self.percentage = percentage

    def to_dict(self):
        return {
            "word": self.word,
            "frequency": self.frequency,
            "percentage": self.percentage
        }