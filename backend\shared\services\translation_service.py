"""
翻译服务适配器，将 MachineTranslationService 适配为 Celery 任务所需的接口
"""
import logging
from typing import Optional, Dict, Any, Tuple
import asyncio
import re

# 导入 MachineTranslationService
from .mt_service import MachineTranslationService

# 获取logger
logger = logging.getLogger(__name__)

class TranslationService:
    """翻译服务适配器，提供统一的翻译接口"""
    
    def __init__(self):
        """初始化翻译服务"""
        # 初始化logger
        self.logger = logger
        
        # 初始化机器翻译服务
        self.mt_service = MachineTranslationService()
        self.logger.info("翻译服务已初始化")
    
    def _clean_special_characters(self, text: str) -> str:
        """
        清理文本中的特殊字符，包括零宽字符和变体选择器等
        
        参数:
            text: 要处理的文本
            
        返回:
            处理后的文本
        """
        if not text:
            return ""
            
        # 记录原始文本长度
        original_length = len(text)
        
        # 特殊字符映射
        special_char_map = {
            # 零宽字符
            '\u200b': '',  # 零宽空格
            '\u200c': '',  # 零宽非连接符
            '\u200d': '',  # 零宽连接符
            '\u200e': '',  # 从左到右标记
            '\u200f': '',  # 从右到左标记
            '\ufeff': '',  # 零宽不换行空格
            
            # 变体选择器
            '\ufe00': '',  # 变体选择器-1
            '\ufe01': '',  # 变体选择器-2
            '\ufe02': '',  # 变体选择器-3
            '\ufe03': '',  # 变体选择器-4
            '\ufe04': '',  # 变体选择器-5
            '\ufe05': '',  # 变体选择器-6
            '\ufe06': '',  # 变体选择器-7
            '\ufe07': '',  # 变体选择器-8
            '\ufe08': '',  # 变体选择器-9
            '\ufe09': '',  # 变体选择器-10
            '\ufe0a': '',  # 变体选择器-11
            '\ufe0b': '',  # 变体选择器-12
            '\ufe0c': '',  # 变体选择器-13
            '\ufe0d': '',  # 变体选择器-14
            '\ufe0e': '',  # 变体选择器-15
            '\ufe0f': '',  # 变体选择器-16
            
            # 控制字符 (ASCII 0-31)
            '\u0000': '', '\u0001': '', '\u0002': '', '\u0003': '', '\u0004': '',
            '\u0005': '', '\u0006': '', '\u0007': '', '\u0008': '', '\u0009': ' ',  # Tab转为空格
            '\u000a': '\n', '\u000b': '', '\u000c': '', '\u000d': '\n',  # 保留换行
            '\u000e': '', '\u000f': '', '\u0010': '', '\u0011': '', '\u0012': '',
            '\u0013': '', '\u0014': '', '\u0015': '', '\u0016': '', '\u0017': '',
            '\u0018': '', '\u0019': '', '\u001a': '', '\u001b': '', '\u001c': '',
            '\u001d': '', '\u001e': '', '\u001f': '',
            
            # 特殊字符替换
            '・': '·',  # 日文中点替换为中文中点
            '￥': '¥',  # 全角日元符号替换为半角
        }
        
        # 替换特殊字符
        processed_text = text
        replacement_count = 0
        
        for special, replacement in special_char_map.items():
            original_text = processed_text
            processed_text = processed_text.replace(special, replacement)
            
            # 计算替换次数
            if original_text != processed_text:
                current_replacements = original_text.count(special)
                replacement_count += current_replacements
                self.logger.debug(f"替换了 {special} -> {replacement}, 次数: {current_replacements}")
        
        # 处理连续的空格（保留最多两个空格）
        processed_text = re.sub(r' {3,}', '  ', processed_text)
        
        # 如果有替换，记录日志
        if replacement_count > 0:
            self.logger.info(f"处理了{replacement_count}个特殊字符: 原始长度={original_length}, 处理后长度={len(processed_text)}")
        
        return processed_text
    
    async def translate_text(
        self, 
        text: str, 
        source_lang: str, 
        target_lang: str, 
        terminology_id: Optional[str] = None
    ) -> str:
        """
        执行文本翻译
        
        参数:
            text: 待翻译文本
            source_lang: 源语言代码
            target_lang: 目标语言代码
            terminology_id: 术语表ID（可选）
            
        返回:
            翻译后的文本
        """
        try:
            self.logger.info(f"开始翻译: {source_lang} -> {target_lang}, 文本长度: {len(text)}")
            
            # 清理特殊字符
            cleaned_text = self._clean_special_characters(text)
            
            # 如果清理后文本为空，直接返回原文
            if not cleaned_text and text:
                self.logger.warning("清理特殊字符后文本为空，返回原文")
                return text
            
            # 使用 mt_service 执行翻译
            # 使用 mt_service 已有的 translate_text 方法
            if hasattr(self.mt_service, 'translate_text'):
                translated_text = await self.mt_service.translate_text(
                    text=cleaned_text or text,
                    source_language=source_lang,
                    target_language=target_lang
                )
            else:
                # 如果没有异步方法，使用同步方法
                translated_text, detected_lang = self.mt_service.translate_sync(
                    text=cleaned_text or text,
                    source_lang=source_lang,
                    target_lang=target_lang
                )
            
            self.logger.info(f"翻译完成: {source_lang} -> {target_lang}, 结果长度: {len(translated_text) if translated_text else 0}")
            return translated_text
            
        except Exception as e:
            self.logger.exception(f"翻译过程中出错: {e}")
            return f"[翻译错误: {str(e)}]"

    def translate_sync(
        self, 
        text: str, 
        source_lang: str, 
        target_lang: str, 
        domain: str = "general",
        style: str = "standard"
    ) -> str:
        """
        同步翻译文本
        
        参数:
            text: 要翻译的文本
            source_lang: 源语言
            target_lang: 目标语言
            domain: 领域
            style: 风格
            
        返回:
            翻译后的文本
        """
        self.logger.info(f"开始同步翻译: {source_lang} -> {target_lang}, 文本长度: {len(text)}")
        try:
            # 清理特殊字符
            cleaned_text = self._clean_special_characters(text)
            
            # 如果清理后文本为空，直接返回原文
            if not cleaned_text and text:
                self.logger.warning("清理特殊字符后文本为空，返回原文")
                return text
                
            # 检查是否有同步翻译方法
            if hasattr(self.mt_service, 'translate_sync'):
                # 直接调用同步方法
                # 注意：mt_service.translate_sync 现在只返回翻译结果，不再返回检测到的语言
                translated_text = self.mt_service.translate_sync(
                    cleaned_text or text, source_lang, target_lang
                )
                self.logger.info(f"同步翻译完成: {source_lang} -> {target_lang}, 结果长度: {len(translated_text)}")
                return translated_text
            else:
                # 如果没有同步方法，使用异步方法在同步上下文中运行
                self.logger.info("使用异步方法在同步上下文中运行翻译")
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    translated_text = loop.run_until_complete(
                        self.translate_text(cleaned_text or text, source_lang, target_lang)
                    )
                    return translated_text
                finally:
                    loop.close()
        except Exception as e:
            self.logger.error(f"同步翻译过程中出错: {str(e)}")
            return f"翻译错误: {str(e)}"

    def translate_text_sync(
        self, 
        text: str, 
        source_lang: str, 
        target_lang: str, 
        domain: str = "general",
        style: str = "standard",
        terminology_id: Optional[str] = None
    ) -> str:
        """
        执行同步文本翻译 (兼容旧版API)
        
        参数:
            text: 待翻译文本，可以是字符串或字符串列表
            source_lang: 源语言代码
            target_lang: 目标语言代码
            domain: 翻译领域
            style: 翻译风格
            terminology_id: 术语表ID（可选）
            
        返回:
            如果输入是字符串，返回翻译后的字符串；如果输入是列表，返回翻译后的列表
        """
        # 处理列表类型的输入
        if isinstance(text, list):
            self.logger.info(f"translate_text_sync: 处理列表输入，包含{len(text)}个文本项")
            
            # 如果列表为空，直接返回空列表
            if not text:
                self.logger.warning("translate_text_sync: 输入列表为空")
                return []
            
            # 过滤空文本
            filtered_texts = [item for item in text if item and isinstance(item, str) and item.strip()]
            if not filtered_texts:
                self.logger.warning("translate_text_sync: 过滤后的列表为空")
                return [""] * len(text)
            
            # 准备结果列表，保持与输入相同长度
            results = [""] * len(text)
            non_empty_indices = [i for i, item in enumerate(text) if item and isinstance(item, str) and item.strip()]
            
            try:
                # 检查mt_service是否有批量翻译方法
                if hasattr(self.mt_service, 'translate_batch'):
                    self.logger.info("translate_text_sync: 使用mt_service.translate_batch方法")
                    # 使用批量翻译方法
                    batch_results = self.mt_service.translate_batch(filtered_texts, source_lang, target_lang)
                    
                    # 将结果填入对应位置
                    result_index = 0
                    for i in non_empty_indices:
                        if result_index < len(batch_results):
                            results[i] = batch_results[result_index]
                            result_index += 1
                        else:
                            # 如果结果不足，单独翻译
                            self.logger.warning(f"translate_text_sync: 批量结果不足，单独翻译第{i}项")
                            results[i] = self.translate_sync(text[i], source_lang, target_lang, domain, style)
                else:
                    # 逐个翻译
                    self.logger.info("translate_text_sync: 逐个翻译列表项")
                    for i in non_empty_indices:
                        results[i] = self.translate_sync(text[i], source_lang, target_lang, domain, style)
                
                self.logger.info(f"translate_text_sync: 列表翻译完成，结果数量: {len(results)}")
                return results
            except Exception as e:
                self.logger.error(f"translate_text_sync: 列表翻译失败: {str(e)}", exc_info=True)
                # 失败时返回原文
                return text
        else:
            # 单个字符串的情况，调用translate_sync方法
            self.logger.info(f"translate_text_sync: 处理单个字符串输入，长度: {len(text) if text else 0}")
            return self.translate_sync(text, source_lang, target_lang, domain, style)

# 单例实例
_translation_service = None

def get_translation_service() -> TranslationService:
    """获取翻译服务实例"""
    global _translation_service
    if _translation_service is None:
        _translation_service = TranslationService()
    return _translation_service 