# TTS系统修复指南

## 问题说明

当前TTS（文本到语音）系统存在以下问题：

1. 缺少必要的TTS模型文件
2. 配置文件可能存在JSON格式问题
3. Windows路径处理问题导致非法字符（如`\x08`）
4. 音频文件保存路径可能不存在
5. TTS缓存与实际模型文件路径不匹配

## 修复方法

我们提供了几个修复工具来解决这些问题：

### 方法一：一键修复（推荐）

直接运行批处理文件 `fix_tts_all.bat`，该脚本将：

1. 修复YourTTS配置文件
2. 清除TTS缓存
3. 修复模型路径映射问题
4. 下载所需的TTS模型文件
5. 应用系统补丁解决路径问题
6. 创建必要的目录结构
7. 确保音频保存目录存在

### 方法二：仅下载TTS模型

如果只缺少模型文件，可以运行：

```
download_tts_models.bat
```

或者使用Python脚本下载特定模型：

```
python download_tts_models.py --model tts_models/zh-CN/baker/tacotron2-DDC-GST
```

### 方法三：仅修复配置文件

如果只需要修复YourTTS配置文件问题：

```
python fix_your_tts_config.py
```

### 方法四：修复TTS路径问题

如果TTS报告模型已下载但找不到文件，可以使用：

```
python fix_tts_model_paths.py
```

这将：
- 清除TTS下载缓存
- 查找系统中的模型文件
- 确保模型文件存在于TTS期望的位置

## 推荐的TTS模型

以下是我们为系统推荐的TTS模型：

1. **中文模型**：`tts_models/zh-CN/baker/tacotron2-DDC-GST`（中文女声）
2. **英文模型**：`tts_models/en/ljspeech/tacotron2-DDC`（英文女声）
3. **多语言模型**：`tts_models/multilingual/multi-dataset/your_tts`（支持多种语言）

## 常见问题

### Q: 运行修复脚本后仍然无法使用TTS功能？

A: 尝试重新启动应用程序，确保修复生效。如果问题仍然存在，请按顺序执行以下操作：
   1. 运行 `fix_tts_model_paths.py` 修复路径问题
   2. 运行 `download_tts_models.bat` 重新下载模型
   3. 重启应用程序

### Q: 模型下载太慢或失败？

A: 这可能是网络问题。您可以：
- 使用更稳定的网络连接
- 使用VPN服务改善连接（如果从中国访问国外资源）
- 手动从官方下载模型文件并放置到正确目录

### Q: TTS报告"模型已下载"但仍然找不到模型文件？

A: 这是TTS缓存与实际文件路径不匹配问题。运行 `fix_tts_model_paths.py` 解决，它将：
   - 清除TTS下载缓存
   - 查找系统中的模型文件
   - 确保模型文件存在于TTS期望的位置

### Q: 找不到Python或pip命令？

A: 确保已正确安装Python（推荐3.8+版本）并将其添加到系统PATH环境变量中。

## 技术支持

如果您遇到无法解决的问题，请提供以下信息寻求技术支持：

1. 系统日志文件
2. 操作系统版本
3. Python版本（运行`python --version`查看）
4. TTS库版本（运行`pip show TTS`查看）
5. 尝试过的修复步骤
6. 使用 `fix_tts_model_paths.py` 脚本生成的模型路径报告 