#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试SadTalker服务是否正常工作
"""

import os
import sys
import argparse
import logging
import dotenv

# 加载环境变量
try:
    # 查找并加载.env文件
    script_dir = os.path.dirname(os.path.abspath(__file__))
    backend_dir = os.path.dirname(script_dir)
    repo_root = os.path.dirname(backend_dir)
    
    env_files = [
        os.path.join(repo_root, '.env'),  # 项目根目录
        os.path.join(backend_dir, '.env'),  # 后端目录
        '.env'  # 当前目录
    ]
    
    for env_file in env_files:
        if os.path.exists(env_file):
            print(f"加载环境变量文件: {env_file}")
            dotenv.load_dotenv(env_file)
            break
except Exception as e:
    print(f"加载环境变量失败: {e}")

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

# 添加backend目录到系统路径
script_dir = os.path.dirname(os.path.abspath(__file__))
backend_dir = os.path.dirname(script_dir)
if backend_dir not in sys.path:
    sys.path.append(backend_dir)

# 导入SadTalker服务
try:
    from services.sadtalker_service import get_sadtalker_service
    logger.info("成功导入SadTalker服务")
except Exception as e:
    logger.error(f"导入SadTalker服务失败: {e}")
    sys.exit(1)

def test_sadtalker_path():
    """测试SadTalker路径设置"""
    # 获取SadTalker服务实例
    service = get_sadtalker_service()
    
    # 输出路径信息
    logger.info(f"SadTalker目录 (相对): {service.sadtalker_dir}")
    logger.info(f"SadTalker目录 (绝对): {os.path.abspath(service.sadtalker_dir)}")
    logger.info(f"检查点目录 (相对): {service.checkpoint_dir}")
    logger.info(f"检查点目录 (绝对): {os.path.abspath(service.checkpoint_dir)}")
    
    # 检查目录是否存在
    path_exists = os.path.exists(service.sadtalker_dir)
    logger.info(f"SadTalker目录存在: {path_exists}")
    
    # 如果目录不存在，尝试创建
    if not path_exists:
        try:
            logger.info(f"尝试创建SadTalker目录: {service.sadtalker_dir}")
            os.makedirs(service.sadtalker_dir, exist_ok=True)
            logger.info(f"SadTalker目录创建成功")
            path_exists = True
        except Exception as e:
            logger.error(f"创建SadTalker目录失败: {e}")
    
    checkpoint_exists = os.path.exists(service.checkpoint_dir)
    logger.info(f"检查点目录存在: {checkpoint_exists}")
    
    # 如果检查点目录不存在，尝试创建
    if not checkpoint_exists:
        try:
            logger.info(f"尝试创建检查点目录: {service.checkpoint_dir}")
            os.makedirs(service.checkpoint_dir, exist_ok=True)
            logger.info(f"检查点目录创建成功")
            checkpoint_exists = True
        except Exception as e:
            logger.error(f"创建检查点目录失败: {e}")
    
    inference_script = os.path.join(service.sadtalker_dir, "inference.py")
    script_exists = os.path.exists(inference_script)
    logger.info(f"inference.py脚本存在: {script_exists}")
    
    # 检查目录结构
    if path_exists:
        logger.info("SadTalker目录结构:")
        for root, dirs, files in os.walk(service.sadtalker_dir, topdown=True, onerror=None, followlinks=False):
            level = root.replace(service.sadtalker_dir, '').count(os.sep)
            indent = ' ' * 4 * level
            logger.info(f"{indent}{os.path.basename(root)}/")
            sub_indent = ' ' * 4 * (level + 1)
            for f in files:
                logger.info(f"{sub_indent}{f}")
            # 限制遍历深度，避免日志过长
            if level >= 1:
                dirs[:] = []  # 不再遍历子目录
    
    # 检查模型文件
    checkpoint_files = []
    if checkpoint_exists:
        checkpoint_files = os.listdir(service.checkpoint_dir)
    logger.info(f"检查点文件: {checkpoint_files}")
    
    # 输出基本系统信息
    logger.info(f"Python版本: {sys.version}")
    logger.info(f"工作目录: {os.getcwd()}")
    logger.info(f"脚本目录: {script_dir}")
    logger.info(f"后端目录: {backend_dir}")
    logger.info(f"CUDA可用: {service.device == 'cuda'}")
    
    # 检查环境变量
    sadtalker_path_env = os.getenv("SADTALKER_PATH")
    logger.info(f"环境变量SADTALKER_PATH: {sadtalker_path_env}")
    
    return path_exists

def test_initialize():
    """测试初始化SadTalker服务"""
    service = get_sadtalker_service()
    
    # 尝试初始化
    logger.info("尝试初始化SadTalker...")
    result = service.initialize()
    logger.info(f"初始化结果: {result}")
    
    return result

def generate_test_video(args):
    """生成测试视频"""
    service = get_sadtalker_service()
    
    # 默认测试文件
    if not args.image:
        # 使用SadTalker自带的示例图像
        sadtalker_examples_dir = os.path.join(service.sadtalker_dir, "examples", "source_image")
        if os.path.exists(sadtalker_examples_dir):
            # 查找示例图像
            example_images = [
                os.path.join(sadtalker_examples_dir, "full1.png"),  # 首选
                os.path.join(sadtalker_examples_dir, "full2.png"),
                os.path.join(sadtalker_examples_dir, "full3.png"),
                os.path.join(sadtalker_examples_dir, "crop1.png"),
                os.path.join(sadtalker_examples_dir, "crop2.png"),
                os.path.join(sadtalker_examples_dir, "crop3.png"),
                # 额外检查可能存在的其他示例
                os.path.join(sadtalker_examples_dir, "woman.png"),
                os.path.join(sadtalker_examples_dir, "woman1.png"),
                os.path.join(sadtalker_examples_dir, "man.png"),
                os.path.join(sadtalker_examples_dir, "art.png")
            ]
            
            # 使用找到的第一个有效图像
            for img_path in example_images:
                if os.path.exists(img_path):
                    args.image = img_path
                    logger.info(f"使用SadTalker自带的示例图像: {img_path}")
                    break
        
        # 如果找不到示例图像，则使用自绘图像
        if not args.image or not os.path.exists(args.image):
            args.image = os.path.join(script_dir, "test_image.jpg")
            logger.info(f"未找到SadTalker示例图像，使用自绘图像: {args.image}")
            
            # 如果测试图像不存在，创建一个简单的测试图像
            if not os.path.exists(args.image):
                try:
                    import cv2
                    import numpy as np
                    
                    logger.info("创建测试人脸图像...")
                    # 创建一个更真实的人脸图像
                    img = np.ones((512, 512, 3), dtype=np.uint8) * 220  # 浅灰背景
                    
                    # 绘制椭圆形脸型
                    cv2.ellipse(img, (256, 256), (180, 220), 0, 0, 360, (203, 174, 159), -1)
                    
                    # 绘制眼睛区域
                    cv2.ellipse(img, (196, 216), (30, 20), 0, 0, 360, (255, 255, 255), -1)  # 左眼白色部分
                    cv2.ellipse(img, (316, 216), (30, 20), 0, 0, 360, (255, 255, 255), -1)  # 右眼白色部分
                    cv2.circle(img, (196, 216), 12, (50, 50, 150), -1)  # 左眼棕色部分
                    cv2.circle(img, (316, 216), 12, (50, 50, 150), -1)  # 右眼棕色部分
                    cv2.circle(img, (196, 216), 6, (0, 0, 0), -1)  # 左眼瞳孔
                    cv2.circle(img, (316, 216), 6, (0, 0, 0), -1)  # 右眼瞳孔
                    
                    # 绘制眉毛
                    cv2.line(img, (166, 186), (226, 196), (30, 30, 30), 5)  # 左眉毛
                    cv2.line(img, (286, 196), (346, 186), (30, 30, 30), 5)  # 右眉毛
                    
                    # 绘制鼻子
                    cv2.ellipse(img, (256, 266), (20, 30), 0, 0, 180, (150, 120, 110), -1)
                    
                    # 绘制嘴巴
                    cv2.ellipse(img, (256, 336), (60, 25), 0, 0, 180, (150, 80, 80), -1)
                    
                    # 绘制嘴唇线条
                    cv2.ellipse(img, (256, 336), (60, 25), 0, 0, 180, (100, 40, 40), 2)
                    
                    # 保存图像
                    cv2.imwrite(args.image, img)
                    logger.info(f"已创建测试图像: {args.image}")
                except Exception as e:
                    logger.error(f"创建测试图像失败: {e}")
                    sys.exit(1)
    else:
        logger.info(f"使用指定图像: {args.image}")
    
    if not args.audio:
        # 尝试使用SadTalker自带的示例音频
        sadtalker_examples_dir = os.path.join(service.sadtalker_dir, "examples", "driven_audio")
        if os.path.exists(sadtalker_examples_dir):
            example_audios = [
                os.path.join(sadtalker_examples_dir, "bus_chinese.wav"),
                os.path.join(sadtalker_examples_dir, "chinese.wav"),
                os.path.join(sadtalker_examples_dir, "RD_Radio31_000.wav"),
                os.path.join(sadtalker_examples_dir, "aintgonna.wav"),
                os.path.join(sadtalker_examples_dir, "fakenews.wav"),
                os.path.join(sadtalker_examples_dir, "governor.wav")
            ]
            
            # 使用找到的第一个有效音频
            for audio_path in example_audios:
                if os.path.exists(audio_path):
                    args.audio = audio_path
                    logger.info(f"使用SadTalker自带的示例音频: {audio_path}")
                    break
        
        # 如果找不到示例音频，则使用生成的音频
        if not args.audio or not os.path.exists(args.audio):
            args.audio = os.path.join(script_dir, "test_audio.mp3")
            logger.info(f"未找到SadTalker示例音频，使用生成音频: {args.audio}")
            
            # 如果测试音频不存在，创建一个简单的测试音频
            if not os.path.exists(args.audio):
                try:
                    logger.info("创建测试音频...")
                    from gtts import gTTS
                    
                    # 创建一个简单的测试音频
                    tts = gTTS("你好，这是一段测试音频，用于验证SadTalker服务是否正常工作。", lang='zh-cn')
                    tts.save(args.audio)
                    logger.info(f"已创建测试音频: {args.audio}")
                except Exception as e:
                    logger.error(f"创建测试音频失败: {e}")
                    logger.info("尝试使用其他TTS服务...")
                    
                    try:
                        import pyttsx3
                        engine = pyttsx3.init()
                        engine.save_to_file("Hello, this is a test audio for SadTalker service.", args.audio)
                        engine.runAndWait()
                        logger.info(f"使用pyttsx3创建的测试音频: {args.audio}")
                    except Exception as e2:
                        logger.error(f"使用pyttsx3创建测试音频也失败: {e2}")
                        sys.exit(1)
    else:
        logger.info(f"使用指定音频: {args.audio}")
    
    if not args.output:
        args.output = os.path.join(script_dir, "test_output.mp4")
    
    # 确保输入文件存在
    if not os.path.exists(args.image):
        logger.error(f"图像文件不存在: {args.image}")
        sys.exit(1)
    
    if not os.path.exists(args.audio):
        logger.error(f"音频文件不存在: {args.audio}")
        sys.exit(1)
    
    # 确保输出目录存在
    os.makedirs(os.path.dirname(args.output), exist_ok=True)
    
    # 尝试生成视频
    logger.info(f"尝试生成测试视频，参数: 图像={args.image}, 音频={args.audio}, 输出={args.output}, 预处理={args.preprocess}")
    result = service.generate_talking_video(
        image_path=args.image,
        audio_path=args.audio,
        output_path=args.output,
        pose_style=args.pose_style,
        enhance=args.enhance,
        preprocess=args.preprocess
    )
    
    if result["success"]:
        logger.info(f"测试视频生成成功: {result['video_path']}")
        logger.info(f"缩略图: {result['thumbnail_path']}")
        logger.info(f"元数据: {result['metadata']}")
        
        # 检查生成的视频文件
        if os.path.exists(result['video_path']):
            file_size = os.path.getsize(result['video_path']) / (1024 * 1024)  # MB
            logger.info(f"视频文件大小: {file_size:.2f} MB")
        else:
            logger.warning(f"视频文件路径存在，但文件不存在: {result['video_path']}")
    else:
        logger.error(f"测试视频生成失败: {result['error']}")
    
    return result["success"]

def main():
    parser = argparse.ArgumentParser(description="测试SadTalker服务")
    parser.add_argument("--image", help="测试图像路径")
    parser.add_argument("--audio", help="测试音频路径")
    parser.add_argument("--output", help="输出视频路径")
    parser.add_argument("--pose-style", type=int, default=0, help="姿势风格")
    parser.add_argument("--enhance", action="store_true", help="是否增强面部质量")
    parser.add_argument("--path-only", action="store_true", help="仅测试路径设置")
    parser.add_argument("--init-only", action="store_true", help="仅测试初始化")
    parser.add_argument("--preprocess", choices=["full", "crop", "extcrop", "resize", "extfull"], 
                        default="full", help="预处理方式")
    args = parser.parse_args()
    
    # 测试路径设置
    path_test = test_sadtalker_path()
    if not path_test:
        logger.error("SadTalker路径测试失败，请检查安装路径")
        sys.exit(1)
        
    if args.path_only:
        sys.exit(0)
    
    # 测试初始化
    init_test = test_initialize()
    if not init_test:
        logger.error("SadTalker初始化测试失败，请检查安装")
        sys.exit(1)
        
    if args.init_only:
        sys.exit(0)
    
    # 生成测试视频
    video_test = generate_test_video(args)
    if not video_test:
        logger.error("SadTalker视频生成测试失败")
        sys.exit(1)
    
    logger.info("所有测试通过！")

if __name__ == "__main__":
    main() 