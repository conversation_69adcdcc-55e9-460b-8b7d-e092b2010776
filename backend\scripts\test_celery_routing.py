#!/usr/bin/env python3
"""
测试 Celery 任务路由
"""
import os
import sys
import uuid
from pathlib import Path
from dotenv import load_dotenv

# 添加后端路径
backend_dir = Path(__file__).parent.parent
sys.path.append(str(backend_dir))

# 加载环境变量
env_path = backend_dir / ".env"
load_dotenv(env_path)

def test_simple_task():
    """测试简单的测试任务"""
    print("🧪 测试简单的测试任务")
    print("=" * 50)
    
    try:
        from app.tasks.test_unified_task import test_unified_task
        from app.core.task_manager import task_manager
        from app.models.unified_tasks import TaskTypes
        
        # 创建任务记录
        task_id = task_manager.create_task(
            task_type="test",
            task_subtype="simple",
            user_id="test-user",
            title="简单测试任务",
            description="测试 Celery 路由",
            input_params={"test_message": "Hello World!", "duration": 3}
        )
        
        print(f"📝 任务ID: {task_id}")
        
        # 启动 Celery 任务
        result = test_unified_task.delay(
            task_id=task_id,
            test_message="Hello World!",
            duration=3
        )
        
        print(f"✅ Celery 任务已提交")
        print(f"📊 Celery 任务ID: {result.id}")
        print(f"📊 任务状态: {result.status}")
        
        return task_id, result
        
    except Exception as e:
        print(f"❌ 测试任务失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None

def test_wanx_task():
    """测试 Wanx 任务"""
    print(f"\n🎬 测试 Wanx 任务")
    print("=" * 50)
    
    try:
        from app.tasks.wanx_video_tasks import wanx_text_to_video
        from app.core.task_manager import task_manager
        from app.models.unified_tasks import TaskTypes
        
        # 创建任务记录
        task_id = task_manager.create_task(
            task_type=TaskTypes.VIDEO_GENERATION,
            task_subtype=TaskTypes.VideoGeneration.TEXT_TO_VIDEO,
            user_id="test-user",
            title="Wanx 路由测试",
            description="测试 Wanx 任务路由",
            input_params={
                "prompt": "路由测试视频",
                "model": "t2v-1.3B",
                "duration": 3
            }
        )
        
        print(f"📝 任务ID: {task_id}")
        
        # 启动 Celery 任务
        result = wanx_text_to_video.delay(
            task_id=task_id,
            prompt="路由测试视频",
            model="t2v-1.3B",
            duration=3,
            resolution="512x512",
            fps=24,
            guidance_scale=7.5,
            num_inference_steps=20,
            user_id="test-user"
        )
        
        print(f"✅ Wanx 任务已提交")
        print(f"📊 Celery 任务ID: {result.id}")
        print(f"📊 任务状态: {result.status}")
        
        return task_id, result
        
    except Exception as e:
        print(f"❌ Wanx 任务失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None

def check_redis_queues():
    """检查 Redis 队列"""
    print(f"\n🔍 检查 Redis 队列")
    print("=" * 50)
    
    try:
        import redis
        
        r = redis.Redis(host='localhost', port=6379, db=0)
        
        queues = ['wanx_generation', 'celery', 'test']
        
        print(f"📊 队列状态:")
        for queue in queues:
            length = r.llen(queue)
            print(f"   {queue}: {length} 个任务")
            
            if length > 0:
                # 查看队列中的任务
                tasks = r.lrange(queue, 0, 2)  # 获取前3个任务
                for i, task in enumerate(tasks):
                    print(f"     任务 {i+1}: {task.decode()[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Redis 检查失败: {e}")
        return False

def monitor_tasks(task_ids, max_wait=30):
    """监控任务执行"""
    print(f"\n⏳ 监控任务执行 (最多等待 {max_wait} 秒)")
    print("=" * 50)
    
    try:
        from app.core.task_manager import task_manager
        import time
        
        for i in range(max_wait):
            print(f"\n📊 检查轮次 {i+1}:")
            
            all_completed = True
            for task_id in task_ids:
                if task_id:
                    task_info = task_manager.get_task(task_id)
                    if task_info:
                        status = task_info['status']
                        progress = task_info['progress']
                        message = task_info.get('message', 'N/A')
                        
                        print(f"   {task_id[:8]}... - {status} ({progress}%) - {message}")
                        
                        if status not in ['completed', 'failed']:
                            all_completed = False
                    else:
                        print(f"   {task_id[:8]}... - 任务未找到")
                        all_completed = False
            
            if all_completed:
                print(f"\n🎉 所有任务已完成!")
                return True
            
            time.sleep(1)
        
        print(f"\n⏰ 监控超时")
        return False
        
    except Exception as e:
        print(f"❌ 监控失败: {e}")
        return False

def main():
    """主函数"""
    print("🧪 Celery 任务路由测试")
    print("=" * 60)
    
    # 检查初始队列状态
    check_redis_queues()
    
    # 测试简单任务
    simple_task_id, simple_result = test_simple_task()
    
    # 测试 Wanx 任务
    wanx_task_id, wanx_result = test_wanx_task()
    
    # 再次检查队列状态
    print(f"\n📊 任务提交后的队列状态:")
    check_redis_queues()
    
    # 监控任务执行
    task_ids = [simple_task_id, wanx_task_id]
    task_ids = [tid for tid in task_ids if tid]  # 过滤掉 None
    
    if task_ids:
        success = monitor_tasks(task_ids, max_wait=20)
        
        # 总结
        print(f"\n📊 测试总结:")
        print(f"{'✅' if simple_task_id else '❌'} 简单任务: {'提交成功' if simple_task_id else '提交失败'}")
        print(f"{'✅' if wanx_task_id else '❌'} Wanx 任务: {'提交成功' if wanx_task_id else '提交失败'}")
        print(f"{'✅' if success else '❌'} 任务执行: {'成功' if success else '失败/超时'}")
        
        if not success:
            print(f"\n💡 可能的问题:")
            print(f"   1. Celery Worker 没有正确加载任务代码")
            print(f"   2. 任务路由配置有问题")
            print(f"   3. 任务代码中有错误")
            print(f"   4. Worker 没有监听正确的队列")
    else:
        print(f"\n❌ 所有任务提交都失败了")

if __name__ == "__main__":
    main()
