"""
存储管理器 - 统一管理文件存储和检索
遵循指定的目录结构
"""
import os
import uuid
import shutil
import logging
from typing import Dict, Any, Optional, List, Union, Tuple
from datetime import datetime
import hashlib

# 配置日志
logger = logging.getLogger(__name__)

class StorageManager:
    """
    存储管理器，负责处理文件存储和检索
    """
    
    def __init__(self):
        """初始化存储管理器，创建必要的目录结构"""
        # 根目录
        self.storage_root = os.path.join("backend", "storage")
        
        # 上传目录
        self.uploads_dir = os.path.join(self.storage_root, "uploads")
        self.audio_uploads = os.path.join(self.uploads_dir, "audio")
        self.video_uploads = os.path.join(self.uploads_dir, "video")
        self.image_uploads = os.path.join(self.uploads_dir, "image")
        self.documents_uploads = os.path.join(self.uploads_dir, "documents")
        
        # 结果目录
        self.results_dir = os.path.join(self.storage_root, "results")
        self.audio_results = os.path.join(self.results_dir, "audio")
        self.video_results = os.path.join(self.results_dir, "video")
        self.transcriptions = os.path.join(self.results_dir, "transcriptions")
        self.translations = os.path.join(self.results_dir, "translations")
        
        # 临时目录
        self.temp_dir = os.path.join(self.storage_root, "temp")
        
        # 确保目录存在
        self._ensure_directories()
        
        logger.info("存储管理器已初始化")
    
    def _ensure_directories(self):
        """确保所有必要的目录存在"""
        directories = [
            self.uploads_dir, self.audio_uploads, self.video_uploads, 
            self.image_uploads, self.documents_uploads,
            self.results_dir, self.audio_results, self.video_results,
            self.transcriptions, self.translations,
            self.temp_dir
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
    
    def get_upload_path(self, file_type: str, filename: str, task_id: Optional[str] = None) -> str:
        """
        获取上传文件路径
        
        Args:
            file_type: 文件类型 (audio/video/image/document)
            filename: 原始文件名
            task_id: 任务ID（可选）
            
        Returns:
            完整的文件路径
        """
        # 选择合适的目录
        if file_type == "audio":
            directory = self.audio_uploads
        elif file_type == "video":
            directory = self.video_uploads
        elif file_type == "image":
            directory = self.image_uploads
        elif file_type == "document":
            directory = self.documents_uploads
        else:
            raise ValueError(f"不支持的文件类型: {file_type}")
        
        # 添加唯一标识符前缀
        prefix = f"{task_id}_" if task_id else ""
        unique_id = uuid.uuid4().hex[:8]
        unique_filename = f"{prefix}{unique_id}_{filename}"
        
        return os.path.join(directory, unique_filename)
    
    def get_result_path(self, result_type: str, filename: str, task_id: Optional[str] = None) -> str:
        """
        获取结果文件路径
        
        Args:
            result_type: 结果类型 (audio/video/transcription/translation)
            filename: 原始文件名
            task_id: 任务ID（可选）
            
        Returns:
            完整的文件路径
        """
        # 选择合适的目录
        if result_type == "audio":
            directory = self.audio_results
        elif result_type == "video":
            directory = self.video_results
        elif result_type == "transcription":
            directory = self.transcriptions
        elif result_type == "translation":
            directory = self.translations
        else:
            raise ValueError(f"不支持的结果类型: {result_type}")
        
        # 添加唯一标识符前缀
        prefix = f"{task_id}_" if task_id else ""
        unique_id = uuid.uuid4().hex[:8]
        unique_filename = f"{prefix}{unique_id}_{filename}"
        
        return os.path.join(directory, unique_filename)
    
    def get_temp_path(self, filename: str, task_id: Optional[str] = None) -> str:
        """
        获取临时文件路径
        
        Args:
            filename: 原始文件名
            task_id: 任务ID（可选）
            
        Returns:
            完整的临时文件路径
        """
        # 添加唯一标识符前缀
        prefix = f"{task_id}_" if task_id else ""
        unique_id = uuid.uuid4().hex[:8]
        unique_filename = f"{prefix}{unique_id}_{filename}"
        
        return os.path.join(self.temp_dir, unique_filename)
    
    def save_file(self, source_path: str, target_path: str) -> bool:
        """
        保存文件
        
        Args:
            source_path: 源文件路径
            target_path: 目标文件路径
            
        Returns:
            是否成功
        """
        try:
            # 确保目标目录存在
            os.makedirs(os.path.dirname(target_path), exist_ok=True)
            
            # 复制文件
            shutil.copy2(source_path, target_path)
            logger.info(f"文件已保存: {target_path}")
            return True
        except Exception as e:
            logger.error(f"保存文件失败: {e}")
            return False
    
    def delete_file(self, file_path: str) -> bool:
        """
        删除文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            是否成功
        """
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                logger.info(f"文件已删除: {file_path}")
                return True
            else:
                logger.warning(f"文件不存在，无法删除: {file_path}")
                return False
        except Exception as e:
            logger.error(f"删除文件失败: {e}")
            return False
    
    def clean_temp_files(self, task_id: Optional[str] = None, max_age_hours: int = 24) -> int:
        """
        清理临时文件
        
        Args:
            task_id: 指定任务ID的临时文件（可选）
            max_age_hours: 最大保留时间（小时）
            
        Returns:
            清理的文件数量
        """
        try:
            count = 0
            now = datetime.now()
            
            # 遍历临时目录
            for filename in os.listdir(self.temp_dir):
                file_path = os.path.join(self.temp_dir, filename)
                
                # 检查是否是指定任务的文件
                if task_id and not filename.startswith(f"{task_id}_"):
                    continue
                
                # 检查文件时间
                if not task_id:
                    file_mtime = datetime.fromtimestamp(os.path.getmtime(file_path))
                    age_hours = (now - file_mtime).total_seconds() / 3600
                    
                    if age_hours < max_age_hours:
                        continue
                
                # 删除文件
                if os.path.isfile(file_path):
                    os.remove(file_path)
                    count += 1
            
            logger.info(f"已清理 {count} 个临时文件")
            return count
        except Exception as e:
            logger.error(f"清理临时文件失败: {e}")
            return 0
    
    def get_file_info(self, file_path: str) -> Dict[str, Any]:
        """
        获取文件信息
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件信息字典
        """
        try:
            if not os.path.exists(file_path):
                return {"exists": False}
            
            # 获取基本信息
            stat = os.stat(file_path)
            size = stat.st_size
            modified = datetime.fromtimestamp(stat.st_mtime)
            
            # 计算文件hash
            md5_hash = hashlib.md5()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    md5_hash.update(chunk)
            
            return {
                "exists": True,
                "path": file_path,
                "size": size,
                "size_mb": round(size / (1024 * 1024), 2),
                "modified": modified.isoformat(),
                "md5": md5_hash.hexdigest()
            }
        except Exception as e:
            logger.error(f"获取文件信息失败: {e}")
            return {"exists": False, "error": str(e)}

# 单例模式
_storage_manager = None

def get_storage_manager() -> StorageManager:
    """
    获取存储管理器实例
    
    Returns:
        存储管理器实例
    """
    global _storage_manager
    if _storage_manager is None:
        _storage_manager = StorageManager()
    return _storage_manager 