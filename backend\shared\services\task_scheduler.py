import asyncio
import logging
import time
from datetime import datetime
from typing import Dict, Any, Callable, List, Optional, Union, Coroutine

logger = logging.getLogger(__name__)

class TaskScheduler:
    """定时任务调度器
    
    负责管理后台任务、定时任务的执行与调度
    """
    
    def __init__(self):
        """初始化任务调度器"""
        self.tasks: Dict[str, Dict[str, Any]] = {}
        self.running = False
        self.main_task = None
    
    async def start(self):
        """启动任务调度器"""
        if self.running:
            return
        
        self.running = True
        self.main_task = asyncio.create_task(self._scheduler_loop())
        logger.info("任务调度器已启动")
    
    async def stop(self):
        """停止任务调度器"""
        if not self.running:
            return
            
        self.running = False
        if self.main_task:
            self.main_task.cancel()
            try:
                await self.main_task
            except asyncio.CancelledError:
                pass
        logger.info("任务调度器已停止")
    
    async def _scheduler_loop(self):
        """调度器主循环"""
        while self.running:
            current_time = time.time()
            
            # 检查所有任务
            tasks_to_remove = []
            for task_id, task_info in self.tasks.items():
                if task_info["next_run"] <= current_time:
                    # 执行任务
                    try:
                        if asyncio.iscoroutinefunction(task_info["callback"]):
                            asyncio.create_task(self._run_task(task_id, task_info))
                        else:
                            # 非异步函数在线程池中执行
                            asyncio.create_task(self._run_sync_task(task_id, task_info))
                    except Exception as e:
                        logger.error(f"任务 {task_id} 执行失败: {str(e)}")
                    
                    # 更新下次运行时间
                    if task_info["interval"] > 0:
                        task_info["next_run"] = current_time + task_info["interval"]
                        task_info["run_count"] += 1
                    else:
                        # 一次性任务
                        tasks_to_remove.append(task_id)
            
            # 移除一次性任务
            for task_id in tasks_to_remove:
                self.tasks.pop(task_id, None)
                
            # 休眠一段时间
            await asyncio.sleep(0.1)
    
    async def _run_task(self, task_id: str, task_info: Dict[str, Any]):
        """运行异步任务"""
        start_time = time.time()
        try:
            if task_info["args"] and task_info["kwargs"]:
                await task_info["callback"](*task_info["args"], **task_info["kwargs"])
            elif task_info["args"]:
                await task_info["callback"](*task_info["args"])
            elif task_info["kwargs"]:
                await task_info["callback"](**task_info["kwargs"])
            else:
                await task_info["callback"]()
            
            # 更新任务状态
            task_info["last_run"] = start_time
            task_info["last_duration"] = time.time() - start_time
            task_info["last_status"] = "success"
            
            logger.debug(f"任务 {task_id} 执行成功，耗时: {task_info['last_duration']:.4f}秒")
        except Exception as e:
            task_info["last_run"] = start_time
            task_info["last_duration"] = time.time() - start_time
            task_info["last_status"] = "error"
            task_info["last_error"] = str(e)
            
            logger.error(f"任务 {task_id} 执行出错: {str(e)}")
    
    async def _run_sync_task(self, task_id: str, task_info: Dict[str, Any]):
        """在线程池中运行同步任务"""
        start_time = time.time()
        try:
            loop = asyncio.get_running_loop()
            if task_info["args"] and task_info["kwargs"]:
                await loop.run_in_executor(
                    None, 
                    lambda: task_info["callback"](*task_info["args"], **task_info["kwargs"])
                )
            elif task_info["args"]:
                await loop.run_in_executor(
                    None,
                    lambda: task_info["callback"](*task_info["args"])
                )
            elif task_info["kwargs"]:
                await loop.run_in_executor(
                    None,
                    lambda: task_info["callback"](**task_info["kwargs"])
                )
            else:
                await loop.run_in_executor(None, task_info["callback"])
            
            # 更新任务状态
            task_info["last_run"] = start_time
            task_info["last_duration"] = time.time() - start_time
            task_info["last_status"] = "success"
            
            logger.debug(f"同步任务 {task_id} 执行成功，耗时: {task_info['last_duration']:.4f}秒")
        except Exception as e:
            task_info["last_run"] = start_time
            task_info["last_duration"] = time.time() - start_time
            task_info["last_status"] = "error"
            task_info["last_error"] = str(e)
            
            logger.error(f"同步任务 {task_id} 执行出错: {str(e)}")
    
    def schedule_task(
        self,
        task_id: str,
        callback: Union[Callable, Coroutine],
        interval: float = 0,
        delay: float = 0,
        args: List = None,
        kwargs: Dict[str, Any] = None,
        description: str = ""
    ) -> bool:
        """调度一个新任务
        
        Args:
            task_id: 任务ID
            callback: 回调函数或协程
            interval: 任务间隔时间(秒)，0表示一次性任务
            delay: 首次运行延迟时间(秒)
            args: 位置参数
            kwargs: 关键字参数
            description: 任务描述
            
        Returns:
            bool: 是否成功添加任务
        """
        if task_id in self.tasks:
            logger.warning(f"任务 {task_id} 已存在，忽略调度请求")
            return False
        
        current_time = time.time()
        self.tasks[task_id] = {
            "callback": callback,
            "interval": interval,
            "next_run": current_time + delay,
            "last_run": None,
            "last_duration": None,
            "last_status": None,
            "last_error": None,
            "args": args or [],
            "kwargs": kwargs or {},
            "description": description,
            "created_at": current_time,
            "run_count": 0
        }
        
        logger.info(f"任务 {task_id} 已调度，间隔: {interval}秒，延迟: {delay}秒")
        return True
    
    def cancel_task(self, task_id: str) -> bool:
        """取消任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 是否成功取消任务
        """
        if task_id not in self.tasks:
            logger.warning(f"任务 {task_id} 不存在，无法取消")
            return False
            
        self.tasks.pop(task_id, None)
        logger.info(f"任务 {task_id} 已取消")
        return True
    
    def get_task_info(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务信息
        
        Args:
            task_id: 任务ID
            
        Returns:
            Dict[str, Any]: 任务信息，不存在则返回None
        """
        if task_id not in self.tasks:
            return None
            
        task_info = self.tasks[task_id].copy()
        # 移除不可序列化的对象
        task_info.pop("callback", None)
        
        # 添加状态信息
        if task_info["last_run"]:
            task_info["last_run_time"] = datetime.fromtimestamp(task_info["last_run"]).strftime("%Y-%m-%d %H:%M:%S")
        else:
            task_info["last_run_time"] = None
            
        if task_info["next_run"]:
            task_info["next_run_time"] = datetime.fromtimestamp(task_info["next_run"]).strftime("%Y-%m-%d %H:%M:%S")
            task_info["seconds_until_next_run"] = max(0, task_info["next_run"] - time.time())
        
        return task_info
    
    def get_all_tasks(self) -> Dict[str, Dict[str, Any]]:
        """获取所有任务信息
        
        Returns:
            Dict[str, Dict[str, Any]]: 所有任务信息
        """
        result = {}
        for task_id in self.tasks:
            result[task_id] = self.get_task_info(task_id)
        return result

# 全局任务调度器实例
_scheduler_instance = None

def get_task_scheduler() -> TaskScheduler:
    """获取任务调度器实例（单例模式）
    
    Returns:
        TaskScheduler: 任务调度器实例
    """
    global _scheduler_instance
    if _scheduler_instance is None:
        _scheduler_instance = TaskScheduler()
    return _scheduler_instance 