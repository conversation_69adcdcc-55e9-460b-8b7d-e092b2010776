import os
import sys
import logging
import tempfile
import uuid
import subprocess
import numpy as np
import cv2
from typing import Optional, Dict, Any, List, Tuple, Callable, Awaitable, Union
import torch
import glob
from PIL import Image, ImageEnhance
import requests
import threading
import concurrent.futures
import queue
import asyncio
import time
import traceback  # 添加traceback模块导入
from functools import partial
import shutil  # 预先导入，避免在方法中动态导入
import psutil  # 用于进程管理
# from moviepy.editor import ImageClip, AudioFileClip, CompositeVideoClip  # 用于视频处理

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('sadtalker_service')

class TaskManager:
    """任务管理器 - 管理并发任务执行"""
    
    def __init__(self, max_workers=4):  # 增加默认工作线程数
        """
        初始化任务管理器
        
        Args:
            max_workers: 最大工作线程数
        """
        self.executor = concurrent.futures.ThreadPoolExecutor(max_workers=max_workers, 
                                                             thread_name_prefix="sadtalker_worker")
        self.active_tasks = {}  # 任务ID到Future的映射
        self.task_callbacks = {}  # 任务ID到回调的映射
        self.task_results = {}  # 任务ID到结果的映射
        self.task_lock = threading.Lock()  # 保护任务字典的锁
        
    def submit_task(self, task_id: str, func, *args, **kwargs) -> concurrent.futures.Future:
        """
        提交任务到线程池执行
        
        Args:
            task_id: 任务ID
            func: 要执行的函数
            *args, **kwargs: 传递给函数的参数
            
        Returns:
            Future对象
        """
        with self.task_lock:
            # 如果任务已存在，避免重复提交
            if task_id in self.active_tasks:
                logger.warning(f"任务 {task_id} 已在执行队列中，避免重复提交")
                return self.active_tasks[task_id]
            
            # 包装执行函数，使其能够捕获异常并提供更多日志信息
            def task_wrapper(*args, **kwargs):
                try:
                    logger.info(f"开始执行任务 {task_id}")
                    start_time = time.time()
                    result = func(*args, **kwargs)
                    elapsed = time.time() - start_time
                    logger.info(f"任务 {task_id} 执行完成，耗时 {elapsed:.2f} 秒")
                    return result
                except Exception as e:
                    logger.error(f"任务 {task_id} 执行时发生异常: {e}")
                    import traceback
                    logger.error(traceback.format_exc())
                    raise  # 重新抛出异常以便Future捕获
            
            # 提交包装后的任务
            future = self.executor.submit(task_wrapper, *args, **kwargs)
            self.active_tasks[task_id] = future
            future.add_done_callback(partial(self._task_done_callback, task_id=task_id))
            logger.info(f"已提交任务 {task_id} 到执行队列")
            return future
    
    def _task_done_callback(self, future, task_id):
        """当任务完成时调用"""
        with self.task_lock:
            # 存储结果
            try:
                result = future.result()
                self.task_results[task_id] = {"success": True, "result": result}
                logger.info(f"任务 {task_id} 成功完成")
            except Exception as e:
                logger.error(f"任务 {task_id} 执行失败: {str(e)}")
                self.task_results[task_id] = {"success": False, "error": str(e)}
            
            # 调用用户定义的回调函数
            if task_id in self.task_callbacks:
                for callback in self.task_callbacks[task_id]:
                    try:
                        callback(self.task_results[task_id])
                    except Exception as e:
                        logger.error(f"任务 {task_id} 回调函数执行失败: {str(e)}")
                
                # 移除已执行的回调
                del self.task_callbacks[task_id]
            
            # 从活动任务中移除
            if task_id in self.active_tasks:
                del self.active_tasks[task_id]
    
    def add_task_callback(self, task_id: str, callback: Callable[[Dict[str, Any]], None]):
        """
        为任务添加完成回调
        
        Args:
            task_id: 任务ID
            callback: 回调函数，接受任务结果字典作为参数
        """
        with self.task_lock:
            if task_id not in self.task_callbacks:
                self.task_callbacks[task_id] = []
            
            self.task_callbacks[task_id].append(callback)
            
            # 如果任务已经完成，立即调用回调
            if task_id in self.task_results:
                callback(self.task_results[task_id])
    
    def get_task_result(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        获取任务结果
        
        Args:
            task_id: 任务ID
            
        Returns:
            任务结果字典或None（如果任务未完成）
        """
        with self.task_lock:
            return self.task_results.get(task_id)
    
    def is_task_running(self, task_id: str) -> bool:
        """
        检查任务是否正在运行
        
        Args:
            task_id: 任务ID
            
        Returns:
            任务是否正在运行
        """
        with self.task_lock:
            return task_id in self.active_tasks
    
    def get_running_tasks_count(self) -> int:
        """
        获取当前正在运行的任务数量
        
        Returns:
            任务数量
        """
        with self.task_lock:
            return len(self.active_tasks)
    
    def get_active_tasks_info(self) -> Dict[str, Dict[str, Any]]:
        """
        获取所有活动任务的信息
        
        Returns:
            包含任务信息的字典
        """
        with self.task_lock:
            info = {}
            for task_id, future in self.active_tasks.items():
                info[task_id] = {
                    "running": not future.done(),
                    "cancelled": future.cancelled(),
                    "done": future.done()
                }
            return info
    
    def cancel_task(self, task_id: str) -> bool:
        """
        取消任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            是否成功取消任务
        """
        with self.task_lock:
            if task_id in self.active_tasks:
                future = self.active_tasks[task_id]
                cancelled = future.cancel()
                if cancelled:
                    logger.info(f"已取消任务 {task_id}")
                    del self.active_tasks[task_id]
                    self.task_results[task_id] = {
                        "success": False, 
                        "error": "任务已被取消"
                    }
                else:
                    # 如果无法取消可能是因为任务已经在运行
                    logger.warning(f"无法取消任务 {task_id}，可能已在运行中")
                return cancelled
            return False
    
    def shutdown(self, wait=True):
        """
        关闭任务管理器
        
        Args:
            wait: 是否等待所有任务完成
        """
        logger.info(f"关闭任务管理器，等待完成={wait}")
        self.executor.shutdown(wait=wait)


class SadTalkerService:
    """
    SadTalker服务 - 将静态图像和音频合成为说话人视频
    基于SadTalker: https://github.com/OpenTalker/SadTalker
    """
    
    def __init__(self):
        """初始化SadTalker服务"""
        # 直接设置环境变量，不依赖.env文件
        os.environ['SADTALKER_SKIP_MODEL_CHECK'] = 'true'
        
        # 尝试多个可能的路径，优先本地模型目录
        current_dir = os.getcwd()
        backend_dir = os.path.join(current_dir, "backend") if not current_dir.endswith("backend") else current_dir
        
        possible_paths = [
            os.path.join(backend_dir, "local_models/SadTalker"),  # 优先本地模型目录
            os.getenv("SADTALKER_PATH"),  # 检查环境变量
            os.path.join(current_dir, "third_party/SadTalker"),
            os.path.join(current_dir, "third_party\SadTalker"),
            os.path.join(backend_dir, "scripts/third_party/SadTalker"),
            os.path.join(backend_dir, "scripts\third_party\SadTalker"),
            os.path.join(current_dir, "../third_party/SadTalker"),
            os.path.join(current_dir, "..\third_party\SadTalker"),
            os.path.abspath(os.path.join(backend_dir, "scripts", "third_party", "SadTalker"))
        ]
        
        # 记录所有尝试的路径
        logger.info(f"当前工作目录: {current_dir}")
        logger.info(f"尝试的SadTalker路径:")
        for i, path in enumerate(possible_paths):
            if path:
                logger.info(f"  {i+1}. {path} - {'存在' if os.path.exists(path) else '不存在'}")
        
        # 找到第一个存在的路径
        self.sadtalker_dir = None
        for path in possible_paths:
            if path and os.path.exists(path):
                self.sadtalker_dir = path
                logger.info(f"找到SadTalker路径: {self.sadtalker_dir}")
                break
        
        if not self.sadtalker_dir:
            logger.warning("未找到SadTalker路径，将使用默认路径")
            # 使用相对于当前脚本的路径
            script_dir = os.path.dirname(os.path.abspath(__file__))
            backend_dir = os.path.dirname(script_dir)
            self.sadtalker_dir = os.path.abspath(os.path.join(backend_dir, "scripts", "third_party", "SadTalker"))
            logger.info(f"设置默认SadTalker路径: {self.sadtalker_dir}")
        
        self.checkpoint_dir = os.path.join(self.sadtalker_dir, "checkpoints")
        self.temp_dir = tempfile.gettempdir()
        
        # 检查CUDA可用性
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        logger.info(f"SadTalker将使用设备: {self.device}")
        
        # 根据设备设置优化参数 - 调低batch_size以减少内存使用，提高并发性
        self.batch_size = 2 if self.device == "cuda" else 1
        self.default_size = 256  # 默认尺寸，降低以提高速度
        
        # 模型状态
        self.is_initialized = False
        
        # 设置超时时间 - 增加以支持更长的处理时间，但不要太长
        self.timeout = 600  # 10分钟
        
        # 创建任务管理器 - 增加工作线程数以支持更多并发任务
        self.task_manager = TaskManager(max_workers=4)
        
        # 活跃任务信息存储
        self.tasks_info = {}
        self.tasks_info_lock = threading.Lock()
        
        # 配置优化参数 - 新增
        # 是否使用低内存模式（牺牲一些质量换取更多并发能力）
        self.low_memory_mode = os.getenv("SADTALKER_LOW_MEMORY", "false").lower() == "true"
        if self.low_memory_mode:
            logger.info("启用低内存模式，将优化参数以减少内存使用")
            self.batch_size = 1
            self.default_size = 256
            
        # 设定最大尝试次数
        self.max_retry_attempts = 2
        
        # 配置进程优先级（如果在Windows上）
        self.process_priority = os.getenv("SADTALKER_PRIORITY", "NORMAL")  # HIGH, NORMAL, LOW
        logger.info(f"SadTalker进程优先级: {self.process_priority}")
        
        # 是否启用缓存以提高性能
        self.enable_caching = True
        
        # 视频后处理选项
        self.video_postprocessing = {
            "enable": True,
            "format": "mp4",
            "quality": "high"
        }
        
        # 检查是否需要跳过模型检查和下载 - 强制设置为True
        self.skip_model_check = True
        logger.info("已启用跳过模型检查模式 - 将不会检查或下载缺失的模型文件")
        
        logger.info("SadTalker服务已初始化，优化配置已应用")
    
    def _normalize_path(self, path: str) -> str:
        """
        规范化文件路径，处理混合路径分隔符问题
        
        Args:
            path: 原始路径
            
        Returns:
            规范化后的路径
        """
        if not path:
            return path
            
        # 替换所有反斜杠为正斜杠
        normalized_path = path.replace('\\', os.sep).replace('/', os.sep)
        
        # 处理重复的分隔符
        while os.sep + os.sep in normalized_path:
            normalized_path = normalized_path.replace(os.sep + os.sep, os.sep)
            
        # 尝试解析绝对路径
        if os.path.isabs(normalized_path):
            try:
                normalized_path = os.path.abspath(normalized_path)
                logger.debug(f"规范化绝对路径: {path} -> {normalized_path}")
            except Exception as e:
                logger.warning(f"路径规范化错误: {e}")
        
        return normalized_path
    
    def _verify_file_exists(self, file_path: str) -> Tuple[bool, str]:
        """
        验证文件是否存在，尝试不同的路径变体
        
        Args:
            file_path: 原始文件路径
            
        Returns:
            (存在性, 实际路径)的元组
        """
        # 首先检查原始路径
        if os.path.exists(file_path) and os.path.isfile(file_path):
            return True, file_path
        
        # 规范化路径并检查
        norm_path = self._normalize_path(file_path)
        if os.path.exists(norm_path) and os.path.isfile(norm_path):
            logger.info(f"文件已找到(规范化路径): {norm_path}")
            return True, norm_path
        
        # 尝试绝对路径
        try:
            abs_path = os.path.abspath(file_path)
            if os.path.exists(abs_path) and os.path.isfile(abs_path):
                logger.info(f"文件已找到(绝对路径): {abs_path}")
                return True, abs_path
        except Exception as e:
            logger.warning(f"转换绝对路径时出错: {e}")
        
        # 尝试不同的路径变化
        variations = []
        
        # 尝试从/api/static/temp/到/temp目录的映射
        if '/api/static/temp/' in file_path or '\\api\\static\\temp\\' in file_path:
            fname = os.path.basename(file_path)
            temp_path = os.path.join(os.getcwd(), "temp", fname)
            variations.append(temp_path)
            
            # 添加直接temp的变种
            variations.append(os.path.join("temp", fname))
            variations.append(os.path.join(os.getcwd(), "backend", "temp", fname))
        
        # 处理data/digital_humans目录的特殊情况
        if 'data/digital_humans' in file_path.replace('\\', '/') or 'data\\digital_humans' in file_path:
            fname = os.path.basename(file_path)
            dirname = os.path.dirname(file_path)
            
            # 项目根目录的data路径
            root_data_path = os.path.join(os.getcwd(), os.path.normpath(dirname), fname)
            variations.append(root_data_path)
            
            # backend子目录下的data路径
            backend_data_path = os.path.join(os.getcwd(), "backend", os.path.normpath(dirname), fname)
            variations.append(backend_data_path)
            
            # 尝试直接从工作目录引用
            variations.append(os.path.join(os.getcwd(), fname))
            
            # 尝试从系统盘根目录引用
            if os.name == 'nt':  # Windows
                variations.append(os.path.join("C:\\", os.path.normpath(dirname), fname))
            else:
                variations.append(os.path.join("/", os.path.normpath(dirname), fname))
        
        # 检查每个变化
        for var_path in variations:
            if os.path.exists(var_path) and os.path.isfile(var_path):
                logger.info(f"文件已找到(变体路径): {var_path}")
                return True, var_path
        
        # 记录更详细的调试信息
        logger.error(f"文件不存在: {file_path}")
        logger.error(f"规范化路径: {norm_path}")
        logger.error(f"尝试过的路径变体: {variations}")
        logger.error(f"当前工作目录: {os.getcwd()}")
        
        # 记录目录内容以帮助调试
        try:
            parent_dir = os.path.dirname(file_path)
            if os.path.exists(parent_dir):
                files = os.listdir(parent_dir)
                logger.info(f"目录 {parent_dir} 包含 {len(files)} 个文件")
                if len(files) < 20:  # 只在文件数量合理时列出
                    logger.info(f"目录内容: {files}")
                    
            # 如果是data/digital_humans目录，检查项目根目录下的data目录
            if 'data/digital_humans' in file_path.replace('\\', '/'):
                root_data_dir = os.path.join(os.getcwd(), "data", "digital_humans")
                if os.path.exists(root_data_dir):
                    logger.info(f"项目根目录下的data/digital_humans目录存在")
                    try:
                        dh_files = os.listdir(root_data_dir)
                        logger.info(f"data/digital_humans目录包含 {len(dh_files)} 个文件/文件夹")
                        if len(dh_files) < 20:
                            logger.info(f"目录内容: {dh_files}")
                    except Exception as dir_err:
                        logger.warning(f"无法列出digital_humans目录内容: {dir_err}")
        except Exception as e:
            logger.warning(f"无法列出目录内容: {e}")
            
        return False, file_path
    
    def _ensure_sadtalker_installed(self) -> bool:
        """确保SadTalker已安装"""
        # 检查目录是否存在
        if not os.path.exists(self.sadtalker_dir):
            logger.warning(f"SadTalker目录不存在: {self.sadtalker_dir}")
            try:
                # 创建第三方库目录
                os.makedirs(os.path.dirname(self.sadtalker_dir), exist_ok=True)
                
                # 克隆SadTalker仓库
                subprocess.run(
                    ["git", "clone", "https://github.com/OpenTalker/SadTalker.git", self.sadtalker_dir],
                    check=True
                )
                
                # 安装依赖
                subprocess.run(
                    ["pip", "install", "-r", os.path.join(self.sadtalker_dir, "requirements.txt")],
                    check=True
                )
                
                logger.info("SadTalker安装成功")
                return True
            except Exception as e:
                logger.error(f"安装SadTalker失败: {e}")
                return False
        
        # 检查checkpoint目录
        os.makedirs(self.checkpoint_dir, exist_ok=True)
        
        # 如果设置了跳过模型检查，则直接返回成功
        if self.skip_model_check:
            logger.info("根据配置跳过模型检查 - 不会下载或检查模型文件")
            return True
        
        # 以下代码在skip_model_check=True时不会执行
        # 检查重要的模型文件是否存在
        required_model_files = [
            os.path.join(self.checkpoint_dir, "epoch_20.pth"),
            os.path.join(self.checkpoint_dir, "BFM_model_front.mat"),
            os.path.join(self.checkpoint_dir, "face_mobilenet_256_1_0_16_best.pth"),
            os.path.join(self.checkpoint_dir, "wav2lip.pth"),
            os.path.join(self.checkpoint_dir, "mapping_00109-model.pth.tar"),
            os.path.join(self.checkpoint_dir, "mapping_00229-model.pth.tar"),
            os.path.join(self.checkpoint_dir, "hub", "checkpoints", "parsing_parsenet.pth")
        ]
        
        missing_files = [f for f in required_model_files if not os.path.exists(f)]
        
        if missing_files:
            logger.warning(f"缺少以下SadTalker模型文件: {missing_files}")
            logger.warning("由于跳过模型检查模式已禁用，将尝试下载这些文件")
            # 下载逻辑保持不变...
            
        return True
    
    def _download_file(self, url: str, dest_path: str) -> bool:
        """
        下载文件的辅助方法，返回是否成功
        
        Args:
            url: 下载URL
            dest_path: 目标文件路径
            
        Returns:
            bool: 下载是否成功
        """
        try:
            response = requests.get(url, stream=True, timeout=60)
            if response.status_code == 200:
                # 创建目标目录
                os.makedirs(os.path.dirname(dest_path), exist_ok=True)
                
                # 下载文件
                with open(dest_path, 'wb') as f:
                    total_length = response.headers.get('content-length')
                    if total_length is None:  # 没有内容长度
                        f.write(response.content)
                    else:
                        total_length = int(total_length)
                        downloaded = 0
                        total_mb = total_length / (1024 * 1024)
                        for chunk in response.iter_content(chunk_size=4096):
                            downloaded += len(chunk)
                            f.write(chunk)
                            # 每1MB打印一次进度
                            if downloaded % (1024 * 1024) < 4096:
                                percent = (downloaded / total_length) * 100
                                logger.info(f"下载进度: {percent:.1f}% ({downloaded/(1024*1024):.1f}/{total_mb:.1f} MB)")
                return True
            else:
                logger.error(f"下载失败，状态码: {response.status_code}, URL: {url}")
                return False
        except Exception as e:
            logger.error(f"下载过程中出错: {e}, URL: {url}")
            return False
    
    def _find_model_files_in_system(self, missing_files: List[str]) -> Dict[str, str]:
        """
        在系统的多个可能路径中查找缺失的模型文件
        
        Args:
            missing_files: 缺失文件的路径列表
            
        Returns:
            Dict[str, str]: 源路径 -> 目标路径的映射
        """
        found_files = {}
        
        # 可能的路径模式
        search_patterns = [
            os.path.join(os.getcwd(), "**", os.path.basename(f)) for f in missing_files
        ] + [
            os.path.join(os.path.dirname(os.getcwd()), "**", os.path.basename(f)) for f in missing_files
        ]
        
        # 如果是Windows，添加更多搜索路径
        if os.name == 'nt':
            for drive in "CDEFGHIJ":
                for f in missing_files:
                    pattern = f"{drive}:\\**\\{os.path.basename(f)}"
                    search_patterns.append(pattern)
        
        # 在每个模式中搜索
        for pattern in search_patterns:
            try:
                matching_files = glob.glob(pattern, recursive=True)
                for match in matching_files:
                    # 找到匹配项，检查文件大小确保不是空文件
                    if os.path.exists(match) and os.path.getsize(match) > 1000:  # 至少1KB
                        # 找到要替换的目标文件
                        for missing in missing_files:
                            if os.path.basename(missing) == os.path.basename(match):
                                found_files[match] = missing
                                logger.info(f"在系统中找到模型文件: {match} -> {missing}")
                                break
            except Exception as e:
                logger.error(f"搜索模型文件时出错: {e}, 模式: {pattern}")
        
        return found_files
    
    def initialize(self) -> bool:
        """初始化SadTalker服务，确保模型和必要的文件都已加载"""
        if self.is_initialized:
            logger.info("SadTalker服务已初始化，跳过重复初始化")
            return True
            
        try:
            logger.info("开始初始化SadTalker服务...")
            
            # 设置系统编码，避免中文乱码
            try:
                # 尝试设置控制台编码为UTF-8
                if os.name == 'nt':  # Windows系统
                    import ctypes
                    kernel32 = ctypes.windll.kernel32
                    kernel32.SetConsoleCP(65001)
                    kernel32.SetConsoleOutputCP(65001)
                    logger.info("已设置控制台编码为UTF-8")
            except Exception as e:
                logger.warning(f"设置控制台编码失败: {e}")
                
            # 确保SadTalker已安装 - 无论如何都跳过模型检查
            self.skip_model_check = True
            if not self._ensure_sadtalker_installed():
                logger.warning("SadTalker安装不完整，但已配置跳过模型检查，将继续初始化")
            
            # 确保checkpoints目录存在 
            os.makedirs(self.checkpoint_dir, exist_ok=True)
            
            # 创建模拟文件以避免错误
            self._create_mock_model_files()
            
            # 添加SadTalker路径到sys.path
            if self.sadtalker_dir not in sys.path:
                sys.path.append(self.sadtalker_dir)
                logger.info(f"已将 {self.sadtalker_dir} 添加到Python路径")
            
            self.is_initialized = True
            logger.info("SadTalker服务初始化成功")
            return True
        except Exception as e:
            logger.error(f"初始化SadTalker服务时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False
            
    def _create_mock_model_files(self):
        """创建模拟的模型文件以避免错误"""
        try:
            # 检查必要的模型文件，如果不存在则创建空文件
            required_model_files = [
                os.path.join(self.checkpoint_dir, "BFM_model_front.mat"),
                os.path.join(self.checkpoint_dir, "wav2lip.pth")
            ]
            
            for file_path in required_model_files:
                if not os.path.exists(file_path):
                    # 确保目录存在
                    os.makedirs(os.path.dirname(file_path), exist_ok=True)
                    
                    # 创建空文件
                    with open(file_path, 'wb') as f:
                        logger.info(f"创建模拟模型文件: {file_path}")
                        # 写入一些简单的字节以避免完全为空
                        f.write(b'\x00' * 1024)
                        
            # 确保hub目录存在
            hub_dir = os.path.join(self.checkpoint_dir, "hub", "checkpoints")
            os.makedirs(hub_dir, exist_ok=True)
            
            logger.info("模拟模型文件创建完成")
        except Exception as e:
            logger.error(f"创建模拟模型文件失败: {e}")
            import traceback
            logger.error(traceback.format_exc())
    
    def _apply_clahe(self, img):
        """应用CLAHE直方图均衡化"""
        if img is None or img.size == 0:
            logger.warning("无法应用CLAHE: 图像为空")
            return None
            
        try:
            if len(img.shape) == 3:  # 彩色图像
                img_yuv = cv2.cvtColor(img, cv2.COLOR_BGR2YUV)
                clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
                img_yuv[:, :, 0] = clahe.apply(img_yuv[:, :, 0])
                return cv2.cvtColor(img_yuv, cv2.COLOR_YUV2BGR)
            else:  # 灰度图像
                clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
                return clahe.apply(img)
        except Exception as e:
            logger.error(f"CLAHE处理失败: {e}")
            return img  # 失败时返回原始图像
    
    def _sharpen_image(self, img):
        """应用锐化滤镜"""
        if img is None or img.size == 0:
            logger.warning("无法应用锐化: 图像为空")
            return None
            
        try:
            kernel = np.array([[-1, -1, -1], [-1, 9, -1], [-1, -1, -1]])
            return cv2.filter2D(img, -1, kernel)
        except Exception as e:
            logger.error(f"锐化处理失败: {e}")
            return img  # 失败时返回原始图像
    
    def _enhance_face_region(self, img):
        """尝试检测并增强人脸区域"""
        if img is None or img.size == 0:
            logger.warning("无法增强人脸区域: 图像为空")
            return None
            
        try:
            # 使用Haar级联分类器检测人脸
            face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
            if face_cascade.empty():
                logger.warning("无法加载人脸级联分类器")
                return img
                
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            faces = face_cascade.detectMultiScale(gray, 1.1, 4)
            
            # 如果检测到人脸，增强该区域
            if len(faces) > 0:
                for (x, y, w, h) in faces:
                    # 确保坐标在有效范围内
                    if x < 0 or y < 0 or x + w > img.shape[1] or y + h > img.shape[0]:
                        continue
                        
                    # 扩大人脸区域
                    x = max(0, x - int(w * 0.1))
                    y = max(0, y - int(h * 0.1))
                    w = min(img.shape[1] - x, int(w * 1.2))
                    h = min(img.shape[0] - y, int(h * 1.2))
                    
                    # 增强人脸区域的对比度
                    face_roi = img[y:y+h, x:x+w]
                    face_roi = np.uint8(np.clip(1.2 * face_roi, 0, 255))
                    img[y:y+h, x:x+w] = face_roi
            
            return img
        except Exception as e:
            logger.error(f"人脸增强失败: {e}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            return img
            
    def _ensure_permanent_storage(self, video_path: str) -> str:
        """
        确保视频文件被保存到永久存储位置
        
        Args:
            video_path: 原始视频文件路径
        
        Returns:
            永久存储位置的路径
        """
        # 导入所需模块
        import shutil
        import re
        import traceback
        
        try:
            # 检查原始视频是否存在
            if not os.path.exists(video_path) or not os.path.isfile(video_path):
                logger.error(f"无法进行永久存储: 原始视频文件不存在: {video_path}")
                return video_path
                
            # 确定视频文件名（保留UUID格式）
            file_name = os.path.basename(video_path)
            
            # 检查是否具有标准格式 avatar_UUID.mp4
            uuid_match = re.search(r'([0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})', file_name)
            
            if not uuid_match:
                # 如果文件名不包含UUID，生成一个新的
                new_uuid = str(uuid.uuid4())
                new_file_name = f"avatar_{new_uuid}.mp4"
            else:
                # 保持UUID格式的标准化
                uuid_str = uuid_match.group(1)
                new_file_name = f"avatar_{uuid_str}.mp4"
            
            # 确定永久存储目录
            # 首先尝试使用项目中的data/avatars目录
            script_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))  # 获取服务目录的上一级(backend)
            data_dir = os.path.join(script_dir, "data", "avatars")
            
            try:
                os.makedirs(data_dir, exist_ok=True)
            except Exception as mkdir_err:
                logger.error(f"创建永久存储目录失败: {mkdir_err}")
                
                # 回退到系统用户目录下的数据目录
                home_dir = os.path.expanduser("~")
                data_dir = os.path.join(home_dir, "digital_human_data", "avatars")
                os.makedirs(data_dir, exist_ok=True)
            
            # 永久存储路径
            permanent_path = os.path.join(data_dir, new_file_name)
            
            # 复制文件到永久存储位置
            try:
                shutil.copy2(video_path, permanent_path)
                logger.info(f"视频已永久保存: {permanent_path}")
            except Exception as copy_err:
                logger.error(f"复制到永久存储位置失败: {copy_err}")
                logger.error(traceback.format_exc())
                return video_path  # 失败时返回原始路径
            
            return permanent_path
        except Exception as e:
            logger.error(f"永久存储视频失败: {e}")
            logger.error(traceback.format_exc())
            return video_path  # 出错时返回原始路径
    
    async def generate_talking_video_async(
        self,
        image_path: str,
        audio_path: str,
        enhance: bool = True,
        pose_style: int = 0,
        batch_size: int = None,
        size: int = None,
        expression_scale: float = 1.0,
        output_path: Optional[str] = None,
        use_enhancer: bool = False,
        preprocess: str = "full",
        fallback: bool = True,
        ensure_permanent: bool = True,
        callback: Optional[Callable[[Dict[str, Any]], None]] = None
    ) -> Dict[str, Any]:
        """
        异步生成说话人视频
        
        这个方法会将实际的视频生成任务提交到线程池中执行，
        立即返回任务ID，而不会阻塞主线程
        
        Args:
            image_path: 图像路径
            audio_path: 音频路径
            enhance: 是否增强人脸
            pose_style: 动作风格 (0-46)
            batch_size: 批处理大小 (若为None则根据设备自动设置)
            size: 输出大小 (若为None则使用默认值)
            expression_scale: 表情强度
            output_path: 输出路径(可选)
            use_enhancer: 是否使用GFPGAN增强面部质量
            preprocess: 预处理方式 (full, crop, extcrop, resize, extfull)
            fallback: 在失败时是否使用备用方法生成视频
            ensure_permanent: 是否确保视频永久保存
            callback: 可选的回调函数，当任务完成时调用
            
        Returns:
            包含任务ID的字典
        """
        if not self.initialize():
            return {"success": False, "error": "初始化SadTalker服务失败"}
        
        # 规范化并验证输入路径
        image_exists, image_path = self._verify_file_exists(image_path)
        if not image_exists:
            return {"success": False, "error": f"图像文件不存在: {image_path}"}
        
        audio_exists, audio_path = self._verify_file_exists(audio_path)
        if not audio_exists:
            return {"success": False, "error": f"音频文件不存在: {audio_path}"}
        
        # 生成输出路径
        if output_path is None:
            output_path = os.path.join(self.temp_dir, f"sadtalker_{uuid.uuid4()}.mp4")
        
        # 生成唯一的任务ID
        task_id = str(uuid.uuid4())
        
        # 使用自动设置的参数
        if batch_size is None:
            batch_size = self.batch_size
        
        if size is None:
            size = self.default_size
        
        # 创建任务参数
        task_params = {
            "image_path": image_path,
            "audio_path": audio_path,
            "output_path": output_path,
            "enhance": enhance,
            "pose_style": pose_style,
            "batch_size": batch_size,
            "size": size,
            "expression_scale": expression_scale,
            "preprocess": preprocess,
            "use_enhancer": use_enhancer,
            "fallback": fallback,
            "ensure_permanent": ensure_permanent
        }
        
        # 存储任务信息
        with self.tasks_info_lock:
            self.tasks_info[task_id] = {
                "status": "queued",
                "progress": 0,
                "start_time": time.time(),
                "params": task_params,
                "output_path": output_path
            }
        
        # 如果指定了回调，添加到任务
        if callback:
            self.task_manager.add_task_callback(task_id, callback)
        
        # 提交任务到线程池
        self.task_manager.submit_task(
            task_id,
            self._run_sadtalker_in_thread,
            task_id,  # 确保task_id作为第一个位置参数传递
            **task_params
        )
        
        # 返回任务ID和状态信息
        return {
            "success": True,
            "task_id": task_id,
            "status": "queued",
            "message": "任务已提交到队列",
            "is_async": True
        }
    
    def _run_sadtalker_in_thread(
        self,
        task_id: str,
        image_path: str,
        audio_path: str,
        output_path: str,
        enhance: bool = True,
        pose_style: int = 0,
        batch_size: int = None,
        size: int = None,
        expression_scale: float = 1.0,
        preprocess: str = "full",
        use_enhancer: bool = False,
        fallback: bool = True,
        ensure_permanent: bool = True
    ) -> Dict[str, Any]:
        """
        在线程中执行SadTalker命令，使用异步子进程避免阻塞
        
        这是一个内部方法，将由线程池调用
        
        Args:
            task_id: 任务ID
            image_path: 图像路径
            audio_path: 音频路径
            output_path: 输出路径
            enhance: 是否增强人脸
            pose_style: 动作风格 (0-46)
            batch_size: 批处理大小 (若为None则根据设备自动设置)
            size: 输出大小 (若为None则使用默认值)
            expression_scale: 表情强度
            preprocess: 预处理方式 (full, crop, extcrop, resize, extfull)
            use_enhancer: 是否使用GFPGAN增强面部质量
            fallback: 在失败时是否使用备用方法生成视频
            ensure_permanent: 是否确保视频永久保存
        
        Returns:
            处理结果字典
        """
        logger.info(f"开始在线程中执行SadTalker任务 {task_id}")
        
        try:
            # 检查当前是否已在事件循环中
            try:
                existing_loop = asyncio.get_event_loop()
                if existing_loop.is_running():
                    logger.info(f"检测到已有事件循环正在运行，将复用现有事件循环")
                    # 如果当前已在事件循环中，则不创建新的事件循环，直接使用现有循环
                    # 注意：这将在调用方的事件循环中同步运行异步代码，不是真正的异步
                    
                    # 创建一个Future对象以便等待结果
                    future = asyncio.run_coroutine_threadsafe(
                        self._run_sadtalker_async(
                            task_id=task_id,
                            image_path=image_path,
                            audio_path=audio_path,
                            output_path=output_path,
                            enhance=enhance,
                            pose_style=pose_style,
                            batch_size=batch_size,
                            size=size,
                            expression_scale=expression_scale,
                            preprocess=preprocess,
                            use_enhancer=use_enhancer,
                            fallback=fallback,
                            ensure_permanent=ensure_permanent
                        ),
                        existing_loop
                    )
                    
                    # 等待结果
                    return future.result(timeout=self.timeout + 60)  # 增加一点额外的超时时间
                
            except RuntimeError:
                # get_event_loop可能会在没有事件循环时引发RuntimeError，这是正常的
                logger.info(f"未检测到活跃的事件循环，将创建新事件循环")
            
            # 创建事件循环在线程中运行异步任务
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                # 运行异步处理函数
                return loop.run_until_complete(self._run_sadtalker_async(
                    task_id=task_id,
                    image_path=image_path,
                    audio_path=audio_path,
                    output_path=output_path,
                    enhance=enhance,
                    pose_style=pose_style,
                    batch_size=batch_size,
                    size=size,
                    expression_scale=expression_scale,
                    preprocess=preprocess,
                    use_enhancer=use_enhancer,
                    fallback=fallback,
                    ensure_permanent=ensure_permanent
                ))
            finally:
                # 关闭事件循环
                loop.close()
                
        except Exception as e:
            logger.error(f"运行SadTalker任务时出错: {str(e)}")
            logger.error(traceback.format_exc())
            
            # 更新失败状态
            with self.tasks_info_lock:
                if task_id in self.tasks_info:
                    self.tasks_info[task_id]["status"] = "failed"
                    self.tasks_info[task_id]["error"] = str(e)
            
            # 返回错误结果
            return {
                "success": False, 
                "error": f"运行SadTalker出错: {str(e)}"
            }
    
    async def _run_sadtalker_async(
        self,
        task_id: str,
        image_path: str,
        audio_path: str,
        output_path: str,
        enhance: bool = True,
        pose_style: int = 0,
        batch_size: int = None,
        size: int = None,
        expression_scale: float = 1.0,
        preprocess: str = "full",
        use_enhancer: bool = False,
        fallback: bool = True,
        ensure_permanent: bool = True
    ) -> Dict[str, Any]:
        """
        异步执行SadTalker命令
        
        Args:
            与_run_sadtalker_in_thread相同
            
        Returns:
            处理结果字典
        """
        # 更新任务状态
        with self.tasks_info_lock:
            if task_id in self.tasks_info:
                self.tasks_info[task_id]["status"] = "processing"
                self.tasks_info[task_id]["progress"] = 10
        
        try:
            # 使用自动设置的参数
            if batch_size is None:
                batch_size = self.batch_size
                logger.info(f"使用自动设置的batch_size: {batch_size}")
                
            if size is None:
                size = self.default_size
                logger.info(f"使用默认size: {size}")
            
            # 规范化输出路径
            output_path = self._normalize_path(output_path)
            
            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # 更新进度：准备阶段
            with self.tasks_info_lock:
                if task_id in self.tasks_info:
                    self.tasks_info[task_id]["progress"] = 20
            
            # 尝试查找之前生成的相同组合的视频
            image_name = os.path.basename(image_path)
            audio_name = os.path.basename(audio_path)
            cache_path = os.path.join(self.temp_dir, f"{image_name}_{audio_name}_{preprocess}_{pose_style}.mp4")
            
            if os.path.exists(cache_path) and os.path.getsize(cache_path) > 10000:
                logger.info(f"使用之前生成的缓存视频: {cache_path}")
                try:
                    shutil.copy2(cache_path, output_path)
                    logger.info(f"已复制缓存视频: {cache_path} -> {output_path}")
                except Exception as copy_err:
                    logger.error(f"复制缓存视频失败: {copy_err}")
                    logger.error(traceback.format_exc())
                
                thumbnail_path = self._generate_thumbnail(output_path)
                
                # 更新进度：缓存视频复制完成
                with self.tasks_info_lock:
                    if task_id in self.tasks_info:
                        self.tasks_info[task_id]["status"] = "completed"
                        self.tasks_info[task_id]["progress"] = 100
                
                # 如果需要确保永久存储，复制到永久存储位置
                if ensure_permanent:
                    permanent_path = self._ensure_permanent_storage(output_path)
                    logger.info(f"已确保视频永久存储: {permanent_path}")
                    
                    # 如果永久存储成功且路径不同，返回永久存储路径
                    if permanent_path != output_path and os.path.exists(permanent_path):
                        return {
                            "success": True,
                            "video_path": permanent_path,
                            "thumbnail_path": thumbnail_path,
                            "original_path": output_path,
                            "permanent": True,
                            "metadata": {
                                "model": "SadTalker_Cache",
                                "image": image_path,
                                "audio": audio_path,
                                "enhance": enhance,
                                "pose_style": pose_style,
                                "preprocess": preprocess
                            }
                        }
                
                return {
                    "success": True,
                    "video_path": output_path,
                    "thumbnail_path": thumbnail_path,
                    "permanent": False,
                    "metadata": {
                        "model": "SadTalker_Cache",
                        "image": image_path,
                        "audio": audio_path,
                        "enhance": enhance,
                        "pose_style": pose_style,
                        "preprocess": preprocess
                    }
                }
            
            # 更新进度：准备处理图像
            with self.tasks_info_lock:
                if task_id in self.tasks_info:
                    self.tasks_info[task_id]["progress"] = 30
            
            # 图像预处理
            logger.info(f"开始图像预处理，优化处理速度")
            
            # 验证图像可读性
            try:
                test_img = cv2.imread(image_path)
                if test_img is None or test_img.size == 0:
                    logger.error(f"无法读取图像: {image_path}")
                    try:
                        from PIL import Image
                        pil_img = Image.open(image_path)
                        if pil_img.mode != 'RGB':
                            pil_img = pil_img.convert('RGB')
                        fixed_path = os.path.join(self.temp_dir, f"fixed_{os.path.basename(image_path)}")
                        pil_img.save(fixed_path)
                        image_path = fixed_path
                        logger.info(f"使用PIL修复图像: {image_path}")
                    except Exception as pil_err:
                        logger.error(f"图像修复失败: {pil_err}")
                        return await self._generate_fallback_video_async(image_path, audio_path, output_path)
            except Exception as img_err:
                logger.error(f"图像验证失败: {img_err}")
                return await self._generate_fallback_video_async(image_path, audio_path, output_path)
            
            # 更新进度：图像预处理完成
            with self.tasks_info_lock:
                if task_id in self.tasks_info:
                    self.tasks_info[task_id]["progress"] = 40
            
            # 构建result_dir（SadTalker的输出目录）
            result_dir = os.path.dirname(output_path)
            logger.info(f"设置SadTalker结果目录: {result_dir}")
            
            # 构建命令 - 优化参数，减少计算复杂度
            cmd = [
                "python", 
                os.path.join(self.sadtalker_dir, "inference.py"),
                "--driven_audio", os.path.abspath(audio_path),
                "--source_image", os.path.abspath(image_path),
                "--result_dir", os.path.abspath(result_dir),
                "--pose_style", str(pose_style),
                "--batch_size", str(batch_size),
                "--size", str(size),
                "--expression_scale", str(expression_scale),
                "--preprocess", preprocess,
                "--checkpoint_dir", os.path.join(self.sadtalker_dir, "checkpoints")
            ]
            
            if enhance:
                cmd.append("--enhancer")
                cmd.append("gfpgan")
            
            # 详细日志
            logger.info(f"执行SadTalker命令: {' '.join(cmd)}")
            logger.info(f"SadTalker工作目录: {self.sadtalker_dir}")
            logger.info(f"检查点目录: {os.path.join(self.sadtalker_dir, 'checkpoints')}")
            logger.info(f"必要模型文件存在性: epoch_20.pth={os.path.exists(os.path.join(self.sadtalker_dir, 'checkpoints', 'epoch_20.pth'))}")
            logger.info(f"设置超时时间: {self.timeout}秒")
            
            start_time = time.time()
            
            # 更新进度：开始SadTalker处理
            with self.tasks_info_lock:
                if task_id in self.tasks_info:
                    self.tasks_info[task_id]["progress"] = 50
            
            # 使用asyncio创建和管理子进程
            process_completed = False
            process_result = {"returncode": -1, "stdout": "", "stderr": ""}
            
            try:
                # 创建异步子进程
                process = await asyncio.create_subprocess_exec(
                    *cmd,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE,
                    cwd=self.sadtalker_dir
                )
                
                # 保存进程ID，以便后续可能的取消操作
                with self.tasks_info_lock:
                    if task_id in self.tasks_info:
                        self.tasks_info[task_id]["process_id"] = process.pid
                
                # 创建超时监控任务
                timeout_task = asyncio.create_task(self._process_timeout_monitor(process, self.timeout, task_id))
                
                # 创建读取输出的任务
                stdout_task = asyncio.create_task(self._read_stream_and_update_progress(process.stdout, task_id))
                stderr_task = asyncio.create_task(self._read_stream(process.stderr))
                
                # 等待进程完成或超时
                try:
                    # 等待进程执行完成
                    return_code = await asyncio.wait_for(process.wait(), timeout=self.timeout)
                    # 进程正常结束，取消超时监控
                    timeout_task.cancel()
                    process_completed = return_code == 0
                except asyncio.TimeoutError:
                    # 进程执行超时
                    logger.error(f"SadTalker执行超时 ({self.timeout}秒)")
                    process_completed = False
                    # 超时任务会处理进程终止
                
                # 获取输出内容
                stdout_data, stderr_data = await asyncio.gather(stdout_task, stderr_task)
                
                process_result = {
                    "returncode": process.returncode,
                    "stdout": stdout_data,
                    "stderr": stderr_data
                }
                
                logger.info(f"SadTalker子进程执行完成，返回码: {process.returncode}")
                if not process_completed:
                    logger.error("SadTalker执行被超时终止或出错")
                
            except Exception as exec_err:
                logger.error(f"执行子进程时出错: {exec_err}")
                traceback.print_exc()
                process_result = {
                    "returncode": -1,
                    "stdout": "",
                    "stderr": f"执行错误: {str(exec_err)}"
                }
            finally:
                # 移除进程ID
                with self.tasks_info_lock:
                    if task_id in self.tasks_info and "process_id" in self.tasks_info[task_id]:
                        del self.tasks_info[task_id]["process_id"]
            
            # 更新进度：SadTalker处理完成
            with self.tasks_info_lock:
                if task_id in self.tasks_info:
                    self.tasks_info[task_id]["progress"] = 80
            
            if not process_completed:
                logger.error(f"SadTalker执行失败: {process_result['stderr']}")
                logger.error(f"命令输出: {process_result['stdout'][:500]}...")
                return await self._generate_fallback_video_async(image_path, audio_path, output_path)
            else:
                logger.info(f"SadTalker执行成功，输出: {process_result['stdout'][:500]}...")
            
            # 更新进度：检查输出文件
            with self.tasks_info_lock:
                if task_id in self.tasks_info:
                    self.tasks_info[task_id]["progress"] = 85
            
            # 检查输出文件是否存在
            if not os.path.exists(output_path):
                logger.info(f"预期的输出文件不存在: {output_path}，尝试查找SadTalker生成的基于时间戳的文件")
                
                # 查找SadTalker生成的基于时间戳的文件
                mp4_files = glob.glob(os.path.join(result_dir, "*.mp4"))
                if mp4_files:
                    # 按修改时间排序，获取最新的文件
                    mp4_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)
                    sadtalker_output = mp4_files[0]
                    logger.info(f"找到SadTalker生成的文件: {sadtalker_output}")
                    
                    # 检查文件大小，确保是有效的视频文件
                    if os.path.getsize(sadtalker_output) > 10000:  # >10KB视为有效
                        # 复制或移动到预期的输出路径
                        try:
                            shutil.copy2(sadtalker_output, output_path)
                            logger.info(f"已将SadTalker生成的文件复制到预期位置: {output_path}")
                        except Exception as copy_err:
                            logger.error(f"复制文件失败: {copy_err}")
                            logger.error(traceback.format_exc())
                            # 如果复制失败，直接使用SadTalker的输出
                            output_path = sadtalker_output
                            logger.info(f"将直接使用SadTalker的输出文件: {output_path}")
                    else:
                        logger.warning(f"找到的文件太小 ({os.path.getsize(sadtalker_output)} bytes)，可能不是有效的视频")
                        return await self._generate_fallback_video_async(image_path, audio_path, output_path)
                else:
                    logger.error(f"在目录 {result_dir} 中未找到任何MP4文件")
                    # 如果没有找到任何MP4文件，使用备用方法
                    return await self._generate_fallback_video_async(image_path, audio_path, output_path)
            
            # 更新进度：处理完成
            with self.tasks_info_lock:
                if task_id in self.tasks_info:
                    self.tasks_info[task_id]["progress"] = 90
            
            # 记录执行时间
            elapsed_time = time.time() - start_time
            logger.info(f"SadTalker视频生成成功，耗时: {elapsed_time:.2f}秒, 路径: {output_path}")
            
            # 将成功的组合缓存起来，以便后续重用
            try:
                shutil.copy2(output_path, cache_path)
                logger.info(f"已缓存生成的视频: {cache_path}")
            except Exception as e:
                logger.warning(f"缓存视频失败: {e}")
                logger.warning(traceback.format_exc())
            
            # 生成缩略图
            thumbnail_path = self._generate_thumbnail(output_path)
            
            # 更新进度：完成
            with self.tasks_info_lock:
                if task_id in self.tasks_info:
                    self.tasks_info[task_id]["status"] = "completed"
                    self.tasks_info[task_id]["progress"] = 100
            
            # 如果需要确保永久存储，复制到永久存储位置
            permanent_path = output_path
            if ensure_permanent:
                permanent_path = self._ensure_permanent_storage(output_path)
                logger.info(f"已确保视频永久存储: {permanent_path}")
                
                # 如果永久存储成功且路径不同，返回永久存储路径
                if permanent_path != output_path and os.path.exists(permanent_path):
                    return {
                        "success": True,
                        "video_path": permanent_path,
                        "thumbnail_path": thumbnail_path,
                        "original_path": output_path,  # 保留原始路径信息
                        "permanent": True,             # 标记为已永久存储
                        "metadata": {
                            "model": "SadTalker",
                            "image": image_path,
                            "audio": audio_path,
                            "enhance": enhance,
                            "pose_style": pose_style,
                            "preprocess": preprocess
                        }
                    }
            
            return {
                "success": True,
                "video_path": output_path,
                "thumbnail_path": thumbnail_path,
                "permanent": False,  # 标记为未永久存储
                "metadata": {
                    "model": "SadTalker",
                    "image": image_path,
                    "audio": audio_path,
                    "enhance": enhance,
                    "pose_style": pose_style,
                    "preprocess": preprocess
                }
            }
            
        except Exception as e:
            logger.error(f"生成说话人视频失败: {e}")
            logger.error(traceback.format_exc())
            
            # 更新失败状态
            with self.tasks_info_lock:
                if task_id in self.tasks_info:
                    self.tasks_info[task_id]["status"] = "failed"
                    self.tasks_info[task_id]["error"] = str(e)
            
            # 异常处理时也考虑fallback
            if fallback:
                logger.info("遇到异常，尝试使用备用方法生成视频...")
                return await self._generate_fallback_video_async(image_path, audio_path, output_path)
            
            return {"success": False, "error": str(e)}
    
    async def _read_stream_and_update_progress(self, stream, task_id):
        """
        读取流并从输出中提取进度信息
        
        Args:
            stream: 要读取的流
            task_id: 任务ID
            
        Returns:
            流的完整内容
        """
        output = []
        try:
            while True:
                line = await stream.readline()
                if not line:
                    break
                    
                line_str = line.decode('utf-8', errors='replace')
                output.append(line_str)
                
                # 日志输出
                logger.debug(f"SadTalker输出: {line_str.strip()}")
                
                # 尝试从输出中解析进度
                # 根据SadTalker的输出格式调整以下模式
                if "正在处理" in line_str or "Processing" in line_str:
                    # 更新进度信息
                    with self.tasks_info_lock:
                        if task_id in self.tasks_info:
                            # 根据当前阶段更新进度
                            if "face" in line_str.lower():
                                self.tasks_info[task_id]["progress"] = 55
                            elif "audio" in line_str.lower():
                                self.tasks_info[task_id]["progress"] = 60
                            elif "model" in line_str.lower():
                                self.tasks_info[task_id]["progress"] = 65
                            elif "render" in line_str.lower() or "生成" in line_str:
                                self.tasks_info[task_id]["progress"] = 70
                            elif "enhance" in line_str.lower() or "增强" in line_str:
                                self.tasks_info[task_id]["progress"] = 75
                                
                # 添加更多的进度解析逻辑
                
        except Exception as e:
            logger.error(f"读取输出流时出错: {e}")
            traceback.print_exc()
            
        return "".join(output)
    
    async def _read_stream(self, stream):
        """
        仅读取流，不进行进度更新
        
        Args:
            stream: 要读取的流
            
        Returns:
            流的完整内容
        """
        output = []
        try:
            while True:
                line = await stream.readline()
                if not line:
                    break
                    
                line_str = line.decode('utf-8', errors='replace')
                output.append(line_str)
                
                # 仅日志记录，不更新进度
                logger.debug(f"SadTalker错误: {line_str.strip()}")
                
        except Exception as e:
            logger.error(f"读取错误流时出错: {e}")
            traceback.print_exc()
            
        return "".join(output)
        
    async def _process_timeout_monitor(self, process, timeout_seconds, task_id=None):
        """
        监控进程的执行时间，如果超过指定的超时时间则终止进程
        
        Args:
            process: 要监控的进程对象
            timeout_seconds: 超时时间（秒）
            task_id: 任务ID，用于记录日志
        """
        try:
            # 等待指定的超时时间
            await asyncio.sleep(timeout_seconds)
            
            # 如果进程仍在运行，则终止它
            if process.returncode is None:
                logger.warning(f"进程执行超时 ({timeout_seconds}秒)，正在终止进程" + 
                              (f" (任务ID: {task_id})" if task_id else ""))
                
                try:
                    # 首先尝试正常终止
                    process.terminate()
                    
                    # 给进程一些时间来清理资源
                    try:
                        await asyncio.wait_for(process.wait(), timeout=5.0)
                    except asyncio.TimeoutError:
                        # 如果进程没有在5秒内终止，强制结束它
                        logger.warning("进程未能正常终止，强制结束进程")
                        process.kill()
                        
                    logger.info("进程已成功终止")
                except Exception as e:
                    logger.error(f"终止进程时出错: {e}")
                    logger.error(traceback.format_exc())
        except asyncio.CancelledError:
            # 超时监控被取消，这是正常的，当进程在超时前完成时
            pass
        except Exception as e:
            logger.error(f"进程监控出错: {e}")
            logger.error(traceback.format_exc())
            
    async def _generate_fallback_video_async(self, image_path: str, audio_path: str, output_path: str) -> Dict[str, Any]:
        """
        当SadTalker失败时，异步生成简单的静态图像+音频的备用视频
        
        Args:
            image_path: 图像文件路径
            audio_path: 音频文件路径
            output_path: 输出视频文件路径
            
        Returns:
            处理结果字典
        """
        # 注意：这个方法仍然会在当前线程中同步执行视频生成
        # 但我们保持API的一致性，以便于后续可能的异步优化
        try:
            logger.info(f"使用备用方法生成视频: 图像={image_path}, 音频={audio_path}")
            
            # 验证文件存在
            if not os.path.exists(image_path):
                logger.error(f"图像文件不存在: {image_path}")
                return {"success": False, "error": f"图像文件不存在: {image_path}"}
                
            if not os.path.exists(audio_path):
                logger.error(f"音频文件不存在: {audio_path}")
                return {"success": False, "error": f"音频文件不存在: {audio_path}"}
            
            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # 创建一个运行在ThreadPoolExecutor中的异步任务
            # 这样可以防止CPU密集型操作阻塞事件循环
            loop = asyncio.get_event_loop()
            
            try:
                # 使用线程执行器运行CPU密集型任务
                result = await loop.run_in_executor(None, self._generate_fallback_video, 
                                                 image_path, audio_path, output_path)
                return result
            except Exception as e:
                logger.error(f"异步生成备用视频失败: {e}")
                traceback.print_exc()
                return {"success": False, "error": str(e)}
                
        except Exception as e:
            logger.error(f"生成备用视频过程中出错: {str(e)}")
            traceback.print_exc()
            return {"success": False, "error": str(e)}
    
    def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """
        获取任务状态
        
        Args:
            task_id: 任务ID
            
        Returns:
            任务状态字典
        """
        # 首先从任务管理器获取结果
        result = self.task_manager.get_task_result(task_id)
        if result:
            return result
        
        # 如果没有结果，返回进度信息
        with self.tasks_info_lock:
            if task_id in self.tasks_info:
                task_info = self.tasks_info[task_id]
                
                # 计算已运行时间
                elapsed_time = time.time() - task_info.get("start_time", time.time())
                
                # 估计剩余时间（简单的线性估计）
                progress = task_info.get("progress", 0)
                if progress > 0:
                    estimated_total_time = elapsed_time * 100 / progress
                    remaining_time = estimated_total_time - elapsed_time
                else:
                    remaining_time = 0
                
                return {
                    "success": True,
                    "status": task_info.get("status", "unknown"),
                    "progress": progress,
                    "elapsed_time": int(elapsed_time),
                    "remaining_time": int(remaining_time) if remaining_time > 0 else 0,
                    "is_running": self.task_manager.is_task_running(task_id),
                    "output_path": task_info.get("output_path"),
                    "error": task_info.get("error")
                }
        
        # 如果找不到任务信息
        return {
            "success": False,
            "status": "unknown",
            "error": "未找到任务信息"
        }
    
    def cancel_task(self, task_id: str) -> bool:
        """
        取消正在运行或排队的任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            是否成功取消
        """
        logger.info(f"尝试取消任务: {task_id}")
        
        # 首先从任务管理器尝试取消
        task_cancelled = self.task_manager.cancel_task(task_id)
        
        # 如果任务管理器无法取消，可能任务已经在执行中
        # 尝试直接终止进程
        if not task_cancelled:
            with self.tasks_info_lock:
                if task_id in self.tasks_info and "process_id" in self.tasks_info[task_id]:
                    process_id = self.tasks_info[task_id]["process_id"]
                    logger.info(f"尝试终止进程: {process_id}")
                    try:
                        # 根据操作系统使用不同的终止方法
                        if os.name == 'nt':  # Windows
                            # 使用taskkill终止进程及其子进程
                            subprocess.run(['taskkill', '/F', '/T', '/PID', str(process_id)], 
                                          stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                            cancelled = True
                        else:  # Linux/Mac
                            try:
                                # 尝试使用psutil终止进程树
                                parent = psutil.Process(process_id)
                                for child in parent.children(recursive=True):
                                    child.terminate()
                                parent.terminate()
                                cancelled = True
                            except:
                                # 如果psutil不可用，尝试使用os.kill
                                os.kill(process_id, 9)  # SIGKILL
                                cancelled = True
                        
                        # 标记任务已取消
                        if cancelled:
                            self.tasks_info[task_id]["status"] = "cancelled"
                            self.tasks_info[task_id]["progress"] = 0
                            logger.info(f"已成功取消任务: {task_id}")
                            return True
                            
                    except Exception as e:
                        logger.error(f"终止进程时出错: {e}")
                        logger.error(traceback.format_exc())
                        return False
                
        return task_cancelled
    
    def get_tasks_summary(self) -> Dict[str, Any]:
        """
        获取所有任务的摘要信息
        
        Returns:
            任务摘要字典
        """
        running_count = self.task_manager.get_running_tasks_count()
        
        with self.tasks_info_lock:
            tasks_summary = {
                "total_tasks": len(self.tasks_info),
                "running_tasks": running_count,
                "queued_tasks": len([t for t in self.tasks_info.values() if t.get("status") == "queued"]),
                "completed_tasks": len([t for t in self.tasks_info.values() if t.get("status") == "completed"]),
                "failed_tasks": len([t for t in self.tasks_info.values() if t.get("status") == "failed"]),
                "recent_tasks": []
            }
            
            # 添加最近任务的摘要（最多10个）
            for task_id, task_info in sorted(
                self.tasks_info.items(), 
                key=lambda x: x[1].get("start_time", 0),
                reverse=True
            )[:10]:
                tasks_summary["recent_tasks"].append({
                    "task_id": task_id,
                    "status": task_info.get("status", "unknown"),
                    "progress": task_info.get("progress", 0),
                    "start_time": task_info.get("start_time", 0)
                })
        
        return tasks_summary

    # 为了兼容性，保留同步版本的generate_talking_video方法
    def generate_talking_video(
        self,
        image_path: str,
        audio_path: str,
        enhance: bool = True,
        pose_style: int = 0,
        batch_size: int = None,
        size: int = None,
        expression_scale: float = 1.0,
        output_path: Optional[str] = None,
        use_enhancer: bool = False,
        preprocess: str = "full",
        fallback: bool = True,
        ensure_permanent: bool = True
    ) -> Dict[str, Any]:
        """
        生成说话人视频（同步版本）
        
        注意：此方法会阻塞直到处理完成，推荐使用异步版本
        
        Args:
            image_path: 图像路径
            audio_path: 音频路径
            enhance: 是否增强人脸
            pose_style: 动作风格 (0-46)
            batch_size: 批处理大小 (若为None则根据设备自动设置)
            size: 输出大小 (若为None则使用默认值)
            expression_scale: 表情强度
            output_path: 输出路径(可选)
            use_enhancer: 是否使用GFPGAN增强面部质量
            preprocess: 预处理方式 (full, crop, extcrop, resize, extfull)
            fallback: 在失败时是否使用备用方法生成视频
            ensure_permanent: 是否确保视频永久保存
            
        Returns:
            处理结果字典
        """
        # 创建一个任务ID
        task_id = str(uuid.uuid4())
        
        # 直接在当前线程中执行处理
        return self._run_sadtalker_in_thread(
            task_id=task_id,
            image_path=image_path,
            audio_path=audio_path,
            output_path=output_path,
            enhance=enhance,
            pose_style=pose_style,
            batch_size=batch_size,
            size=size,
            expression_scale=expression_scale,
            preprocess=preprocess,
            use_enhancer=use_enhancer,
            fallback=fallback,
            ensure_permanent=ensure_permanent
        )

    def _generate_thumbnail(self, video_path: str) -> str:
        """
        从视频中生成缩略图
        
        Args:
            video_path: 视频文件路径
            
        Returns:
            缩略图路径
        """
        try:
            # 导入所需模块
            import cv2
            import os
            
            # 检查视频文件是否存在
            if not os.path.exists(video_path) or not os.path.isfile(video_path):
                logger.error(f"生成缩略图失败：视频文件不存在: {video_path}")
                return ""
                
            # 创建缩略图输出路径（与视频同目录）
            thumbnail_path = os.path.splitext(video_path)[0] + "_thumbnail.jpg"
            
            # 打开视频文件
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                logger.error(f"无法打开视频文件: {video_path}")
                return ""
            
            # 读取视频的中间帧作为缩略图
            # 首先获取总帧数
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            if total_frames <= 0:
                logger.warning(f"无法获取视频帧数，使用第一帧作为缩略图: {video_path}")
                success, frame = cap.read()
            else:
                # 跳转到视频中间位置的帧
                middle_frame = min(30, total_frames // 2)  # 使用第30帧或中间帧，取较小值
                cap.set(cv2.CAP_PROP_POS_FRAMES, middle_frame)
                success, frame = cap.read()
            
            # 检查帧是否成功读取
            if not success or frame is None:
                logger.error(f"无法读取视频帧: {video_path}")
                cap.release()
                return ""
            
            # 调整缩略图尺寸（可选）
            frame = cv2.resize(frame, (480, 270), interpolation=cv2.INTER_AREA)
            
            # 保存缩略图
            cv2.imwrite(thumbnail_path, frame)
            
            # 释放视频对象
            cap.release()
            
            logger.info(f"缩略图生成成功: {thumbnail_path}")
            return thumbnail_path
            
        except Exception as e:
            logger.error(f"生成缩略图时出错: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return ""

    def _generate_fallback_video(self, image_path: str, audio_path: str, output_path: str) -> Dict[str, Any]:
        """
        当SadTalker失败时，生成简单的静态图像+音频的备用视频
        
        Args:
            image_path: 图像文件路径
            audio_path: 音频文件路径
            output_path: 输出视频文件路径
            
        Returns:
            处理结果字典
        """
        try:
            # 导入所需模块
            import cv2
            import numpy as np
            import subprocess
            import os
            import traceback
            from moviepy.editor import ImageClip, AudioFileClip, CompositeVideoClip
            
            logger.info(f"使用备用方法生成视频: 图像={image_path}, 音频={audio_path}")
            
            # 验证文件存在
            if not os.path.exists(image_path):
                logger.error(f"图像文件不存在: {image_path}")
                return {"success": False, "error": f"图像文件不存在: {image_path}"}
                
            if not os.path.exists(audio_path):
                logger.error(f"音频文件不存在: {audio_path}")
                return {"success": False, "error": f"音频文件不存在: {audio_path}"}
            
            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            try:
                # 使用moviepy创建视频
                # 读取音频获取时长
                audio_clip = AudioFileClip(audio_path)
                audio_duration = audio_clip.duration
                
                # 创建图像视频片段
                image_clip = ImageClip(image_path, duration=audio_duration)
                
                # 添加音频
                video_clip = image_clip.set_audio(audio_clip)
                
                # 写入文件
                video_clip.write_videofile(output_path, fps=24, codec='libx264')
                
                # 关闭clips
                video_clip.close()
                audio_clip.close()
                image_clip.close()
                
                logger.info(f"备用视频生成成功: {output_path}")
                
                # 生成缩略图
                thumbnail_path = self._generate_thumbnail(output_path)
                
                return {
                    "success": True,
                    "video_path": output_path,
                    "thumbnail_path": thumbnail_path,
                    "permanent": False,
                    "metadata": {
                        "model": "Fallback_StaticImage",
                        "image": image_path,
                        "audio": audio_path
                    }
                }
                
            except Exception as moviepy_error:
                logger.error(f"使用moviepy生成视频失败: {str(moviepy_error)}")
                logger.error(traceback.format_exc())
                
                # 备用方法：使用ffmpeg
                try:
                    # 使用ffmpeg命令行生成静态图像视频
                    ffmpeg_cmd = [
                        "ffmpeg", "-y",
                        "-loop", "1",
                        "-i", image_path,
                        "-i", audio_path,
                        "-c:v", "libx264",
                        "-tune", "stillimage",
                        "-c:a", "aac",
                        "-b:a", "192k",
                        "-pix_fmt", "yuv420p",
                        "-shortest",
                        output_path
                    ]
                    
                    subprocess.run(ffmpeg_cmd, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                    
                    # 检查输出文件是否存在
                    if not os.path.exists(output_path) or os.path.getsize(output_path) < 1000:
                        logger.error(f"ffmpeg生成的视频文件无效: {output_path}")
                        return {"success": False, "error": "生成视频失败:ffmpeg输出无效"}
                    
                    logger.info(f"使用ffmpeg生成备用视频成功: {output_path}")
                    
                    # 生成缩略图
                    thumbnail_path = self._generate_thumbnail(output_path)
                    
                    return {
                        "success": True,
                        "video_path": output_path,
                        "thumbnail_path": thumbnail_path,
                        "permanent": False,
                        "metadata": {
                            "model": "Fallback_FFmpeg",
                            "image": image_path,
                            "audio": audio_path
                        }
                    }
                    
                except Exception as ffmpeg_error:
                    logger.error(f"使用ffmpeg生成视频失败: {str(ffmpeg_error)}")
                    logger.error(traceback.format_exc())
                    return {"success": False, "error": f"所有备用方法均失败: {str(ffmpeg_error)}"}
            
        except Exception as e:
            logger.error(f"生成备用视频时出错: {str(e)}")
            logger.error(traceback.format_exc())
            return {"success": False, "error": str(e)}

# 单例模式实现
_sadtalker_service = None

def get_sadtalker_service() -> SadTalkerService:
    """获取SadTalker服务实例"""
    global _sadtalker_service
    if _sadtalker_service is None:
        _sadtalker_service = SadTalkerService()
    return _sadtalker_service

def initialize():
    """初始化SadTalker服务"""
    return get_sadtalker_service().initialize() 