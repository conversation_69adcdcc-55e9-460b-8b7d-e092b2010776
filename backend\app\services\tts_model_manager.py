"""
TTS模型管理器
负责下载、安装和管理各种TTS模型
"""

import os
import sys
import logging
import subprocess
import tempfile
import asyncio
from typing import Dict, Any, Optional, List
from pathlib import Path
import requests
from tqdm import tqdm

logger = logging.getLogger(__name__)

class TTSModelManager:
    """TTS模型管理器"""
    
    def __init__(self):
        self.models_path = Path("storage/models/tts")
        self.models_path.mkdir(parents=True, exist_ok=True)
        self.available_models = {}
        self.initialize_models()
    
    def initialize_models(self):
        """初始化TTS模型"""
        logger.info("初始化TTS模型...")
        
        # 定义可用的TTS模型
        self.model_definitions = {
            "edge_tts": {
                "name": "Microsoft Edge TTS",
                "type": "api",
                "quality": "standard",
                "description": "微软Edge浏览器内置TTS，免费且质量较好",
                "languages": ["zh-CN", "en-US", "ja-JP", "ko-KR"],
                "install_command": "pip install edge-tts",
                "check_command": "python -c \"import edge_tts\"",
                "voices": [
                    {"id": "zh-CN-XiaoxiaoNeural", "name": "晓晓", "gender": "female", "language": "zh-CN"},
                    {"id": "zh-CN-YunxiNeural", "name": "云希", "gender": "male", "language": "zh-CN"},
                    {"id": "zh-CN-XiaoyiNeural", "name": "晓伊", "gender": "female", "language": "zh-CN"},
                    {"id": "zh-CN-YunjianNeural", "name": "云健", "gender": "male", "language": "zh-CN"},
                    {"id": "en-US-AriaNeural", "name": "Aria", "gender": "female", "language": "en-US"},
                    {"id": "en-US-GuyNeural", "name": "Guy", "gender": "male", "language": "en-US"}
                ]
            },
            "coqui_tts": {
                "name": "Coqui TTS",
                "type": "local",
                "quality": "high",
                "description": "高质量开源TTS引擎，支持多语言和语音克隆",
                "languages": ["zh-CN", "en-US", "ja-JP", "ko-KR", "es-ES", "fr-FR", "de-DE"],
                "install_command": "pip install TTS",
                "check_command": "python -c \"import TTS\"",
                "models": [
                    "tts_models/zh-CN/baker/tacotron2-DDC-GST",
                    "tts_models/en/ljspeech/tacotron2-DDC",
                    "tts_models/multilingual/multi-dataset/xtts_v2"
                ]
            },
            "piper_tts": {
                "name": "Piper TTS",
                "type": "local",
                "quality": "standard",
                "description": "轻量级、快速的本地TTS引擎",
                "languages": ["zh-CN", "en-US"],
                "download_url": "https://github.com/rhasspy/piper/releases/download/v1.2.0/piper_windows_amd64.tar.gz",
                "models": [
                    "zh_CN-huayan-medium",
                    "en_US-lessac-medium"
                ]
            },
            "bark": {
                "name": "Bark",
                "type": "local",
                "quality": "clone",
                "description": "Suno AI开发的AI语音克隆引擎",
                "languages": ["zh-CN", "en-US", "ja-JP", "ko-KR", "es-ES", "fr-FR", "de-DE"],
                "install_command": "pip install bark",
                "check_command": "python -c \"import bark\"",
                "gpu_required": True
            }
        }
        
        # 检查已安装的模型
        self.check_installed_models()
    
    def check_installed_models(self):
        """检查已安装的模型"""
        for model_id, model_info in self.model_definitions.items():
            try:
                if model_info["type"] == "api":
                    # 检查API类型的模型（如Edge TTS）
                    if "check_command" in model_info:
                        result = subprocess.run(
                            model_info["check_command"],
                            shell=True,
                            capture_output=True,
                            text=True,
                            timeout=10
                        )
                        available = result.returncode == 0
                        if not available:
                            logger.warning(f"TTS模型检查失败 {model_id}: {result.stderr}")
                    else:
                        available = True  # 如果没有检查命令，默认可用
                elif model_info["type"] == "local":
                    # 检查本地模型
                    if "check_command" in model_info:
                        result = subprocess.run(
                            model_info["check_command"],
                            shell=True,
                            capture_output=True,
                            text=True,
                            timeout=10
                        )
                        available = result.returncode == 0
                        if not available:
                            logger.warning(f"TTS模型检查失败 {model_id}: {result.stderr}")
                    else:
                        # 检查模型文件是否存在
                        model_path = self.models_path / model_id
                        available = model_path.exists()
                else:
                    available = False
                
                self.available_models[model_id] = {
                    **model_info,
                    "available": available,
                    "path": str(self.models_path / model_id)
                }
                
                logger.info(f"TTS模型 {model_info['name']}: {'可用' if available else '不可用'}")
                
            except Exception as e:
                logger.error(f"检查TTS模型失败 {model_id}: {str(e)}")
                self.available_models[model_id] = {
                    **model_info,
                    "available": False,
                    "error": str(e)
                }
    
    async def install_model(self, model_id: str) -> Dict[str, Any]:
        """安装TTS模型"""
        if model_id not in self.model_definitions:
            return {"success": False, "error": f"未知的模型: {model_id}"}
        
        model_info = self.model_definitions[model_id]
        
        try:
            logger.info(f"开始安装TTS模型: {model_info['name']}")
            
            if model_info["type"] == "api":
                # 安装API类型的模型
                return await self._install_api_model(model_id, model_info)
            elif model_info["type"] == "local":
                # 安装本地模型
                return await self._install_local_model(model_id, model_info)
            else:
                return {"success": False, "error": "不支持的模型类型"}
                
        except Exception as e:
            logger.error(f"安装TTS模型失败 {model_id}: {str(e)}")
            return {"success": False, "error": str(e)}
    
    async def _install_api_model(self, model_id: str, model_info: Dict[str, Any]) -> Dict[str, Any]:
        """安装API类型的模型"""
        try:
            if "install_command" in model_info:
                # 执行安装命令
                process = await asyncio.create_subprocess_shell(
                    model_info["install_command"],
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                
                stdout, stderr = await process.communicate()
                
                if process.returncode == 0:
                    # 重新检查模型可用性
                    self.check_installed_models()
                    return {
                        "success": True,
                        "message": f"{model_info['name']} 安装成功",
                        "log": stdout.decode('utf-8', errors='ignore')
                    }
                else:
                    return {
                        "success": False,
                        "error": f"安装失败: {stderr.decode('utf-8', errors='ignore')}"
                    }
            else:
                return {"success": False, "error": "该模型无需安装"}
                
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def _install_local_model(self, model_id: str, model_info: Dict[str, Any]) -> Dict[str, Any]:
        """安装本地模型"""
        try:
            model_path = self.models_path / model_id
            model_path.mkdir(parents=True, exist_ok=True)
            
            if "install_command" in model_info:
                # 先安装依赖
                process = await asyncio.create_subprocess_shell(
                    model_info["install_command"],
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                
                stdout, stderr = await process.communicate()
                
                if process.returncode != 0:
                    return {
                        "success": False,
                        "error": f"依赖安装失败: {stderr.decode('utf-8', errors='ignore')}"
                    }
            
            if "download_url" in model_info:
                # 下载模型文件
                download_result = await self._download_model(model_info["download_url"], model_path)
                if not download_result["success"]:
                    return download_result
            
            # 重新检查模型可用性
            self.check_installed_models()
            
            return {
                "success": True,
                "message": f"{model_info['name']} 安装成功",
                "path": str(model_path)
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def _download_model(self, url: str, target_path: Path) -> Dict[str, Any]:
        """下载模型文件"""
        try:
            logger.info(f"下载模型文件: {url}")
            
            response = requests.get(url, stream=True)
            response.raise_for_status()
            
            total_size = int(response.headers.get('content-length', 0))
            
            filename = url.split('/')[-1]
            file_path = target_path / filename
            
            with open(file_path, 'wb') as f:
                with tqdm(total=total_size, unit='B', unit_scale=True, desc=filename) as pbar:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
                            pbar.update(len(chunk))
            
            # 如果是压缩文件，解压
            if filename.endswith(('.tar.gz', '.zip')):
                await self._extract_archive(file_path, target_path)
                os.remove(file_path)  # 删除压缩文件
            
            return {"success": True, "message": "下载完成"}
            
        except Exception as e:
            logger.error(f"下载模型失败: {str(e)}")
            return {"success": False, "error": str(e)}
    
    async def _extract_archive(self, archive_path: Path, target_path: Path):
        """解压文件"""
        import tarfile
        import zipfile
        
        if archive_path.suffix == '.gz' and archive_path.stem.endswith('.tar'):
            with tarfile.open(archive_path, 'r:gz') as tar:
                tar.extractall(target_path)
        elif archive_path.suffix == '.zip':
            with zipfile.ZipFile(archive_path, 'r') as zip_ref:
                zip_ref.extractall(target_path)
    
    async def synthesize_speech(
        self, 
        text: str, 
        model_id: str, 
        voice_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """合成语音"""
        if model_id not in self.available_models:
            return {"success": False, "error": f"模型不存在: {model_id}"}
        
        if not self.available_models[model_id]["available"]:
            return {"success": False, "error": f"模型不可用: {model_id}"}
        
        try:
            if model_id == "edge_tts":
                return await self._synthesize_with_edge_tts(text, voice_config)
            elif model_id == "coqui_tts":
                return await self._synthesize_with_coqui_tts(text, voice_config)
            elif model_id == "bark":
                return await self._synthesize_with_bark(text, voice_config)
            elif model_id == "piper_tts":
                return await self._synthesize_with_piper_tts(text, voice_config)
            else:
                return {"success": False, "error": f"不支持的模型: {model_id}"}
                
        except Exception as e:
            logger.error(f"语音合成失败 {model_id}: {str(e)}")
            return {"success": False, "error": str(e)}
    
    async def _synthesize_with_edge_tts(self, text: str, voice_config: Dict[str, Any]) -> Dict[str, Any]:
        """使用Edge TTS合成语音"""
        try:
            import edge_tts
            
            voice = voice_config.get("voice_id", "zh-CN-XiaoxiaoNeural")
            rate = voice_config.get("speed", 1.0)
            
            # 生成临时文件
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
                output_path = temp_file.name
            
            # 合成语音
            communicate = edge_tts.Communicate(text, voice, rate=f"{rate:+.0%}")
            await communicate.save(output_path)
            
            return {
                "success": True,
                "audio_path": output_path,
                "model": "edge_tts",
                "duration": self._get_audio_duration(output_path)
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def _synthesize_with_coqui_tts(self, text: str, voice_config: Dict[str, Any]) -> Dict[str, Any]:
        """使用Coqui TTS合成语音"""
        try:
            from TTS.api import TTS
            
            # 选择模型
            language = voice_config.get("language", "zh-CN")
            if language == "zh-CN":
                model_name = "tts_models/zh-CN/baker/tacotron2-DDC-GST"
            else:
                model_name = "tts_models/en/ljspeech/tacotron2-DDC"
            
            tts = TTS(model_name=model_name)
            
            # 生成临时文件
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
                output_path = temp_file.name
            
            # 合成语音
            tts.tts_to_file(text=text, file_path=output_path)
            
            return {
                "success": True,
                "audio_path": output_path,
                "model": "coqui_tts",
                "duration": self._get_audio_duration(output_path)
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def _synthesize_with_bark(self, text: str, voice_config: Dict[str, Any]) -> Dict[str, Any]:
        """使用Bark合成语音"""
        try:
            from bark import SAMPLE_RATE, generate_audio, preload_models
            import scipy.io.wavfile as wavfile
            
            # 预加载模型
            preload_models()
            
            voice_preset = voice_config.get("voice_id", "v2/zh_speaker_0")
            
            # 生成音频
            audio_array = generate_audio(text, history_prompt=voice_preset)
            
            # 保存到临时文件
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
                output_path = temp_file.name
            
            wavfile.write(output_path, SAMPLE_RATE, audio_array)
            
            return {
                "success": True,
                "audio_path": output_path,
                "model": "bark",
                "duration": len(audio_array) / SAMPLE_RATE
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def _synthesize_with_piper_tts(self, text: str, voice_config: Dict[str, Any]) -> Dict[str, Any]:
        """使用Piper TTS合成语音"""
        try:
            model_path = self.models_path / "piper_tts"
            piper_exe = model_path / "piper.exe" if os.name == 'nt' else model_path / "piper"
            
            if not piper_exe.exists():
                return {"success": False, "error": "Piper可执行文件不存在"}
            
            voice_model = voice_config.get("voice_id", "zh_CN-huayan-medium")
            
            # 生成临时文件
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
                output_path = temp_file.name
            
            # 执行Piper命令
            cmd = [
                str(piper_exe),
                "--model", str(model_path / f"{voice_model}.onnx"),
                "--output_file", output_path
            ]
            
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdin=asyncio.subprocess.PIPE,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate(input=text.encode('utf-8'))
            
            if process.returncode == 0 and os.path.exists(output_path):
                return {
                    "success": True,
                    "audio_path": output_path,
                    "model": "piper_tts",
                    "duration": self._get_audio_duration(output_path)
                }
            else:
                return {
                    "success": False,
                    "error": f"Piper执行失败: {stderr.decode('utf-8', errors='ignore')}"
                }
                
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def _get_audio_duration(self, audio_path: str) -> float:
        """获取音频时长"""
        try:
            import librosa
            y, sr = librosa.load(audio_path)
            return len(y) / sr
        except:
            return 0.0
    
    def get_available_models(self) -> List[Dict[str, Any]]:
        """获取可用的TTS模型列表"""
        return [
            {
                "id": model_id,
                "name": model_info["name"],
                "type": model_info["type"],
                "quality": model_info["quality"],
                "description": model_info["description"],
                "languages": model_info["languages"],
                "available": model_info["available"],
                "voices": model_info.get("voices", [])
            }
            for model_id, model_info in self.available_models.items()
        ]
    
    def get_model_voices(self, model_id: str) -> List[Dict[str, Any]]:
        """获取指定模型的可用语音"""
        if model_id not in self.available_models:
            return []
        
        return self.available_models[model_id].get("voices", [])

# 全局TTS模型管理器实例
tts_model_manager = TTSModelManager()
