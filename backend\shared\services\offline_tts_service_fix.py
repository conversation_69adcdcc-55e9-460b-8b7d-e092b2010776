import os
import sys
import io
import logging
import subprocess
import shutil
import importlib
import json
import traceback
import torch
import glob
import torch.nn as nn
from pathlib import Path
import tempfile
import unicodedata
import re
import time

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger("tts_fix")

# 添加模块级变量以存储原始方法
original_embedding_init = None
original_tacotron_init = None
original_load_state_dict = None

# 添加修复状态跟踪
_FIX_STATUS = {
    "last_fix_time": 0,
    "fix_count": 0,
    "fixed_files": set(),
    "config_fix_complete": False,
    "all_fixes_applied": False
}

# 修复状态文件路径
def _get_status_file_path():
    """获取状态文件路径"""
    try:
        # 获取当前目录
        current_dir = os.path.abspath(os.getcwd())
        
        # 检查是否在backend目录下
        if os.path.basename(current_dir) == "backend":
            status_dir = os.path.join(current_dir, "local_models", "coqui_tts")
        else:
            status_dir = os.path.join(current_dir, "backend", "local_models", "coqui_tts")

        # 如果目录不存在，尝试备选路径
        if not os.path.exists(status_dir):
            alt_status_dir = os.path.join(current_dir, "local_models", "coqui_tts")
            if os.path.exists(alt_status_dir):
                status_dir = alt_status_dir
            else:
                # 尝试创建目录
                os.makedirs(status_dir, exist_ok=True)
        
        return os.path.join(status_dir, ".tts_fix_status.json")
    except Exception as e:
        logger.error(f"获取状态文件路径失败: {e}")
        # 回退到临时目录
        return os.path.join(tempfile.gettempdir(), ".tts_fix_status.json")

def _load_fix_status():
    """加载修复状态"""
    global _FIX_STATUS
    try:
        status_file = _get_status_file_path()
        if os.path.exists(status_file):
            with open(status_file, 'r', encoding='utf-8') as f:
                status = json.load(f)
                # 将fixed_files从列表转回集合
                status["fixed_files"] = set(status.get("fixed_files", []))
                _FIX_STATUS.update(status)
                logger.debug(f"已加载修复状态: {status_file}")
        return True
    except Exception as e:
        logger.warning(f"加载修复状态失败: {e}")
        return False

def _save_fix_status():
    """保存修复状态"""
    try:
        status_file = _get_status_file_path()
        # 转换集合为列表以便JSON序列化
        status_copy = _FIX_STATUS.copy()
        status_copy["fixed_files"] = list(status_copy["fixed_files"])
        
        with open(status_file, 'w', encoding='utf-8') as f:
            json.dump(status_copy, f, indent=2)
        logger.debug(f"已保存修复状态: {status_file}")
        return True
    except Exception as e:
        logger.warning(f"保存修复状态失败: {e}")
        return False

def reset_fix_status(force=False):
    """重置修复状态，强制下次运行执行完整修复"""
    global _FIX_STATUS
    try:
        if force:
            _FIX_STATUS = {
                "last_fix_time": 0,
                "fix_count": 0,
                "fixed_files": set(),
                "config_fix_complete": False,
                "all_fixes_applied": False
            }
            logger.info("已强制重置TTS修复状态")
            _save_fix_status()
            return True
        else:
            # 只重置完成标志，保留文件列表
            _FIX_STATUS["config_fix_complete"] = False
            _FIX_STATUS["all_fixes_applied"] = False
            logger.info("已重置TTS修复完成状态，但保留了已修复文件列表")
            _save_fix_status()
            return True
    except Exception as e:
        logger.error(f"重置修复状态失败: {e}")
        return False

def fix_json_config_files(force=False):
    """
    修复TTS模型配置文件中的JSON格式问题
    
    Args:
        force: 是否强制执行修复，忽略之前的修复状态
        
    返回:
        bool: 是否成功修复至少一个配置文件
    """
    global _FIX_STATUS
    
    # 首先加载状态
    _load_fix_status()
    
    # 检查是否需要修复
    if not force and _FIX_STATUS["config_fix_complete"]:
        logger.info("TTS配置文件修复已完成，跳过。使用force=True可强制修复")
        return True
    
    logger.info("开始修复TTS模型配置文件中的JSON格式问题...")
    
    try:
        # 尝试导入并使用外部修复工具
        try:
            from .json_repair_tool import repair_json_file
            logger.info("已找到JSON修复工具，将用于修复TTS配置文件")
            
            # 实现修复逻辑...
            result = _fix_configs_internal(force=force)
            
            if result:
                _FIX_STATUS["config_fix_complete"] = True
                _FIX_STATUS["last_fix_time"] = time.time()
                _save_fix_status()
                
            return result
        except ImportError:
            logger.warning("未找到JSON修复工具，将使用内置修复方法")
            result = _fix_configs_internal(force=force)
            
            if result:
                _FIX_STATUS["config_fix_complete"] = True
                _FIX_STATUS["last_fix_time"] = time.time()
                _save_fix_status()
                
            return result
    except Exception as e:
        logger.error(f"修复JSON配置文件失败: {e}")
        traceback.print_exc()
        return False

def _fix_json_file(file_path: str, force=False) -> bool:
    """
    修复单个JSON文件中的格式问题
    
    Args:
        file_path: JSON文件路径
        force: 是否强制修复，即使文件之前已被修复
        
    Returns:
        bool: 是否成功修复
    """
    global _FIX_STATUS
    
    try:
        if not os.path.exists(file_path):
            logger.error(f"文件不存在: {file_path}")
            return False
        
        # 检查该文件是否已经修复过
        if not force and file_path in _FIX_STATUS["fixed_files"]:
            logger.debug(f"文件已修复过，跳过: {file_path}")
            return True
            
        # 创建备份
        backup_path = f"{file_path}.bak"
        if not os.path.exists(backup_path):
            shutil.copy2(file_path, backup_path)
            logger.info(f"已创建配置文件备份: {backup_path}")
        
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8', errors='replace') as f:
            content = f.read()
        
        # 删除控制字符
        clean_content = _remove_control_chars(content)
        
        # 清理JSON注释
        clean_content = re.sub(r'//.*?(?=\n|$)', '', clean_content)
        clean_content = re.sub(r'/\*.*?\*/', '', clean_content, flags=re.DOTALL)
        
        # 修复尾随逗号
        clean_content = re.sub(r',(\s*[\]}])', r'\1', clean_content)
        
        # 替换Infinity
        clean_content = clean_content.replace('Infinity', '99999')
        clean_content = clean_content.replace('infinity', '99999')
        
        # 替换NaN
        clean_content = clean_content.replace('NaN', '0')
        clean_content = clean_content.replace('nan', '0')
        
        # 解析和重新格式化JSON
        try:
            json_obj = json.loads(clean_content)
            
            # 修复num_chars
            if 'characters' in json_obj:
                if 'characters' in json_obj['characters']:
                    chars = json_obj['characters']['characters']
                    json_obj['num_chars'] = len(chars) + 1  # +1 for padding
                    logger.info(f"已修复num_chars为: {json_obj['num_chars']}")
                elif json_obj.get('num_chars', 0) <= 10:
                    json_obj['num_chars'] = 149  # 设置默认值
            
            # 写回文件
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(json_obj, f, indent=4)
                
            logger.info(f"成功修复JSON文件: {file_path}")
            
            # 记录已修复的文件
            _FIX_STATUS["fixed_files"].add(file_path)
            _FIX_STATUS["fix_count"] += 1
            
            # 每10个文件保存一次状态
            if _FIX_STATUS["fix_count"] % 10 == 0:
                _save_fix_status()
                
            return True
            
        except json.JSONDecodeError as je:
            logger.error(f"JSON解析错误: {je}")
            
            # 再次尝试用更激进的方式清理
            try:
                # 更激进地清理非ASCII字符
                ascii_content = ''.join(c for c in clean_content if ord(c) < 128)
                json_obj = json.loads(ascii_content)
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(json_obj, f, indent=4)
                    
                logger.info(f"使用激进清理成功修复JSON文件: {file_path}")
                
                # 记录已修复的文件
                _FIX_STATUS["fixed_files"].add(file_path)
                _FIX_STATUS["fix_count"] += 1
                
                # 每10个文件保存一次状态
                if _FIX_STATUS["fix_count"] % 10 == 0:
                    _save_fix_status()
                    
                return True
            except:
                logger.error(f"无法修复严重损坏的JSON文件: {file_path}")
                return False
    except Exception as e:
        logger.error(f"修复JSON文件失败: {file_path}, 错误: {e}")
        traceback.print_exc()
        return False

def _remove_control_chars(s: str) -> str:
    """删除字符串中的控制字符"""
    return ''.join(ch for ch in s if unicodedata.category(ch)[0] != 'C' or ch in ('\n', '\t', '\r'))

def fix_model_config(config_path: str, model_path: str = None, force=False) -> bool:
    """
    修复模型配置文件，确保配置与模型权重匹配
    
    Args:
        config_path: 配置文件路径
        model_path: 模型文件路径
        force: 是否强制修复，即使文件之前已被修复
        
    Returns:
        bool: 是否成功修复
    """
    global _FIX_STATUS
    
    try:
        if not os.path.exists(config_path):
            logger.error(f"配置文件不存在: {config_path}")
            return False
        
        # 检查该文件是否已经修复过
        if not force and config_path in _FIX_STATUS["fixed_files"]:
            logger.debug(f"模型配置已修复过，跳过: {config_path}")
            return True
            
        # 首先修复JSON格式问题
        if not _fix_json_file(config_path, force=force):
            logger.warning(f"JSON格式修复失败: {config_path}")
            return False
            
        # 读取配置文件 - 这时应该已经是有效的JSON
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
            
        # 创建备份
        backup_path = f"{config_path}.bak"
        if not os.path.exists(backup_path):
            shutil.copy2(config_path, backup_path)
            logger.info(f"已创建配置文件备份: {backup_path}")
            
        # 修改配置，确保嵌入层大小正确
        if 'characters' in config:
            if 'characters' in config['characters']:
                chars = config['characters']['characters']
                # 确保num_chars与字符集大小匹配
                config['num_chars'] = len(chars) + 1  # +1 for padding
                logger.info(f"已修复配置文件中的num_chars为: {config['num_chars']}")
            elif config.get('num_chars', 0) <= 10:
                # 如果num_chars太小或不存在，设置为合理的默认值
                config['num_chars'] = 149  # 使用与模型匹配的默认值
                logger.info(f"设置num_chars默认值为: {config['num_chars']}")
                
        # 修复音频配置参数 - 确保mel参数正确
        if 'audio' in config:
            # 确保mel参数设置正确
            config['audio']['num_mels'] = 80  # 标准值
            
        # 将修改后的配置写回文件
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=4)
            
        logger.info(f"成功修复模型配置文件: {config_path}")
        
        # 记录已修复的文件
        _FIX_STATUS["fixed_files"].add(config_path)
        _FIX_STATUS["fix_count"] += 1
        
        # 每10个文件保存一次状态
        if _FIX_STATUS["fix_count"] % 10 == 0:
            _save_fix_status()
            
        return True
    except Exception as e:
        logger.error(f"修复模型配置文件失败: {e}")
        traceback.print_exc()
        return False

def fix_encoding_issues():
    """修复各种编码问题"""
    try:
        # 修复Windows控制台编码
        fix_windows_console_encoding()
        
        # 确保临时文件目录使用ASCII路径
        temp_dir = os.path.join(tempfile.gettempdir(), "tts_temp")
        os.makedirs(temp_dir, exist_ok=True)
        os.environ["TEMP"] = temp_dir
        os.environ["TMP"] = temp_dir
        
        # 修复Python默认编码
        if sys.getdefaultencoding() != 'utf-8':
            logger.warning(f"当前默认编码: {sys.getdefaultencoding()}, 建议使用UTF-8")
            # 无法动态修改Python默认编码，但可以确保文件操作使用UTF-8
            
        logger.info("编码问题修复完成")
        return True
    except Exception as e:
        logger.error(f"修复编码问题失败: {e}")
        return False

def patch_tts_tokenizer():
    """修补TTS的tokenizer.py文件以避免中文字符打印导致的编码错误"""
    try:
        # 尝试定位tokenizer.py文件
        tokenizer_paths = []
        for path in sys.path:
            potential_path = os.path.join(path, 'TTS', 'tts', 'utils', 'text', 'tokenizer.py')
            if os.path.exists(potential_path):
                tokenizer_paths.append(potential_path)
        
        if not tokenizer_paths:
            logger.warning("未找到tokenizer.py文件，跳过修补")
            return False
        
        # 修补找到的第一个tokenizer.py文件
        tokenizer_path = tokenizer_paths[0]
        logger.info(f"找到tokenizer.py: {tokenizer_path}")
        
        # 创建备份
        backup_path = tokenizer_path + '.bak'
        if not os.path.exists(backup_path):
            shutil.copy2(tokenizer_path, backup_path)
            logger.info(f"已创建备份: {backup_path}")
        
        # 读取文件内容
        with open(tokenizer_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 注释掉可能导致GBK编码错误的print语句
        modified_content = content.replace(
            'print(" > Available characters: {}".format(charset))',
            '# print(" > Available characters: {}".format(charset)) # Commented to avoid encoding issues'
        ).replace(
            'print(f" > {text}")',
            '# print(f" > {text}") # Commented to avoid encoding issues'
        )
        
        # 如果没有修改，说明文件可能已经被修补过
        if content == modified_content:
            logger.info("tokenizer.py已经被修补过，无需再次修补")
            return True
        
        # 写入修改后的内容
        with open(tokenizer_path, 'w', encoding='utf-8') as f:
            f.write(modified_content)
        
        logger.info("成功修补tokenizer.py")
        return True
    except Exception as e:
        logger.error(f"修补tokenizer.py失败: {e}")
        traceback.print_exc()
        return False

def patch_tacotron_model():
    """修补Tacotron2模型的初始化方法，解决nn.Embedding和num_chars=0问题"""
    try:
        # 导入torch.nn和TTS的Tacotron2模型
        import torch.nn as nn
        from TTS.tts.models.tacotron2 import Tacotron2
        
        # 使用全局变量
        global original_embedding_init, original_tacotron_init, original_load_state_dict
        
        # 保存原始的Embedding类初始化方法（如果尚未保存）
        if original_embedding_init is None:
            original_embedding_init = nn.Embedding.__init__
        
        # 定义安全的初始化方法，确保num_embeddings不为0
        def safe_embedding_init(self, num_embeddings, embedding_dim, padding_idx=None, 
                               max_norm=None, norm_type=2.0, scale_grad_by_freq=False, 
                               sparse=False, _weight=None, device=None, dtype=None):
            # 确保num_embeddings不为0，使用更合理的默认值
            if num_embeddings <= 0:
                logger.warning(f"尝试以num_embeddings={num_embeddings}初始化nn.Embedding，将其设置为默认值149")
                num_embeddings = 149  # 使用与模型匹配的默认值
            
            # 调用原始初始化方法
            original_embedding_init(
                self, num_embeddings, embedding_dim, padding_idx, 
                max_norm, norm_type, scale_grad_by_freq, 
                sparse, _weight, device, dtype
            )
        
        # 保存Tacotron2的原始初始化方法（如果尚未保存）
        if original_tacotron_init is None:
            original_tacotron_init = Tacotron2.__init__
        
        # 定义修补后的Tacotron2初始化方法 - 修正参数类型问题
        def patched_tacotron_init(self, config, ap=None, tokenizer=None, speaker_manager=None):
            # 检查配置是否已完成加载
            if hasattr(config, 'model') and config.model == "Tacotron2":
                logger.info(f"[TTS修复] 使用预加载的Tacotron2配置")
                return original_tacotron_init(self, config, ap, tokenizer, speaker_manager)
                
            # 确保num_chars不为0（处理config可能是对象或整数的情况）
            if hasattr(config, 'num_chars'):
                if not config.num_chars or config.num_chars == 0:
                    # 从字符集计算正确的值
                    if hasattr(config, 'characters') and hasattr(config.characters, 'characters'):
                        chars = config.characters.characters
                        if chars:
                            logger.info(f"[TTS修复] 将num_chars从0修正为字符集长度: {len(chars)}")
                            config.num_chars = len(chars)
                        else:
                            logger.info("[TTS修复] 字符集为空，设置num_chars为默认值149")
                            config.num_chars = 149  # 使用与模型匹配的默认值
                    else:
                        logger.info("[TTS修复] 未找到字符集，设置num_chars为默认值149")
                        config.num_chars = 149  # 使用与模型匹配的默认值
            
            # 检查并修复phonemes为空的情况
            if hasattr(config, 'characters'):
                # 检查所有字段
                for field in ['pad', 'eos', 'bos', 'characters', 'phonemes', 'punctuations']:
                    if not hasattr(config.characters, field) or getattr(config.characters, field) is None:
                        setattr(config.characters, field, "")
                        logger.info(f"[TTS修复] 已修复空{field}字段")
            
            # 调用原始初始化方法
            logger.info(f"[TTS修复] 准备初始化Tacotron2，num_chars={getattr(config, 'num_chars', 'unknown')}")
            return original_tacotron_init(self, config, ap, tokenizer, speaker_manager)
        
        # 保存原始的load_state_dict方法
        if original_load_state_dict is None:
            original_load_state_dict = nn.Module.load_state_dict
            
        # 定义修补后的load_state_dict方法，处理结构不匹配问题
        def patched_load_state_dict(self, state_dict, strict=True, **kwargs):
            # 接受并处理其他关键字参数，如'assign'（transformers库使用）
            # 提取常见参数
            assign = kwargs.get('assign', None)
            
            # 检查是否是Tacotron2模型
            if not isinstance(self, Tacotron2):
                # 如果不是Tacotron2模型，则直接传递所有参数到原始函数
                return original_load_state_dict(self, state_dict, strict, **kwargs)
                
            # 以下代码专门用于Tacotron2模型
            # 创建状态字典的副本以修改
            fixed_state_dict = {}
            
            # 处理coarse_decoder键和其他不匹配键
            for key, value in state_dict.items():
                # 跳过coarse_decoder键
                if key.startswith("coarse_decoder."):
                    logger.info(f"[TTS修复] 跳过不兼容的键: {key}")
                    continue
                    
                # 处理decoder.linear_projection.linear_layer.weight形状不匹配
                if key == "decoder.linear_projection.linear_layer.weight" and value.shape[0] != 80:
                    if value.shape[0] == 160 and self.decoder.linear_projection.linear_layer.weight.shape[0] == 80:
                        logger.info(f"[TTS修复] 调整形状不匹配: {key}, 从{value.shape}到{self.decoder.linear_projection.linear_layer.weight.shape}")
                        # 只保留前80个特征
                        value = value[:80, :]
                
                # 处理decoder.linear_projection.linear_layer.bias形状不匹配
                if key == "decoder.linear_projection.linear_layer.bias" and value.shape[0] != 80:
                    if value.shape[0] == 160 and self.decoder.linear_projection.linear_layer.bias.shape[0] == 80:
                        logger.info(f"[TTS修复] 调整形状不匹配: {key}, 从{value.shape}到{self.decoder.linear_projection.linear_layer.bias.shape}")
                        # 只保留前80个特征
                        value = value[:80]
                
                # 处理decoder.stopnet形状不匹配
                if key == "decoder.stopnet.1.linear_layer.weight" and value.shape[1] != self.decoder.stopnet[1].linear_layer.weight.shape[1]:
                    logger.info(f"[TTS修复] 跳过形状不匹配的stopnet权重: {key}")
                    continue
                
                fixed_state_dict[key] = value
            
            # 使用修复后的状态字典调用原始方法
            logger.info(f"[TTS修复] 使用修复后的状态字典加载模型")
            return original_load_state_dict(self, fixed_state_dict, False)  # 使用non-strict模式
        
        # 替换nn.Embedding的初始化方法
        nn.Embedding.__init__ = safe_embedding_init
        
        # 替换Tacotron2的初始化方法
        Tacotron2.__init__ = patched_tacotron_init
        
        # 替换load_state_dict方法
        nn.Module.load_state_dict = patched_load_state_dict
        
        logger.info("成功应用Tacotron2模型和nn.Embedding补丁")
        return True
    except ImportError as e:
        logger.warning(f"无法导入所需模块应用Tacotron2补丁，将在服务初始化时重试: {e}")
        return False
    except Exception as e:
        logger.error(f"应用Tacotron2补丁失败: {e}")
        traceback.print_exc()
        return False

def apply_fixes(force=False):
    """
    应用所有TTS修复
    
    Args:
        force: 是否强制应用修复，即使已经修复过
        
    Returns:
        bool: 是否成功应用修复
    """
    try:
        logger.info("应用TTS编码修复...")
        
        # 这里导入，避免循环引用
        try:
            from services.fix_your_tts_config import fix_your_tts_config
            # 修复YourTTS配置文件
            fix_your_tts_config()
        except ImportError:
            logger.warning("未找到YourTTS配置修复模块，将跳过此步骤")
            
        # 应用JSON配置文件修复
        success = fix_json_config_files(force)
        
        # 应用Tacotron初始化修复
        success = patch_tacotron_model() and success
        
        # 调试信息
        if success:
            logger.info("TTS修复成功应用")
        else:
            logger.warning("部分TTS修复未能成功应用")
            
        return success
        
    except Exception as e:
        logger.error(f"应用TTS修复失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        
        # 设置错误标志
        global _fix_error
        _fix_error = True
        
        return False

def fix_windows_console_encoding():
    """修复Windows控制台编码问题，确保中文显示正常"""
    try:
        # 检测是否在Windows环境
        if sys.platform.startswith('win'):
            # 尝试设置控制台编码为UTF-8
            import ctypes
            kernel32 = ctypes.windll.kernel32
            kernel32.SetConsoleCP(65001)
            kernel32.SetConsoleOutputCP(65001)
            logger.info("已设置Windows控制台为UTF-8编码")
    except Exception as e:
        logger.warning(f"设置Windows控制台编码失败: {e}")

def patch_torch_pickle_loader():
    """修补torch.load以处理pickle兼容性问题"""
    try:
        original_torch_load = torch.load
        
        def patched_torch_load(f, map_location=None, pickle_module=None, **kwargs):
            try:
                return original_torch_load(f, map_location, pickle_module, **kwargs)
            except Exception as e:
                logger.warning(f"使用原始torch.load失败，尝试使用兼容性加载方式: {e}")
                import pickle
                # 使用更宽松的unpickler
                class CustomUnpickler(pickle.Unpickler):
                    def find_class(self, module, name):
                        try:
                            return super().find_class(module, name)
                        except:
                            return super().find_class(module.replace('TTS', 'tts'), name)
                
                if hasattr(f, 'read'):
                    return CustomUnpickler(f).load()
                with open(f, 'rb') as fh:
                    return CustomUnpickler(fh).load()
        
        # 替换torch.load
        torch.load = patched_torch_load
        logger.info("已修补torch.load以提高兼容性")
        return True
    except Exception as e:
        logger.error(f"修补torch.load失败: {e}")
        return False

def _fix_configs_internal(force=False):
    """
    使用内置函数修复配置文件
    
    Args:
        force: 是否强制修复所有文件，包括之前已修复过的
    """
    try:
        # 获取模型目录 - 修正路径重复问题
        # 首先获取当前工作目录的绝对路径
        current_dir = os.path.abspath(os.getcwd())
        
        # 检查当前目录是否已经包含backend
        if os.path.basename(current_dir) == "backend":
            # 如果已经在backend目录下，直接使用相对路径
            base_models_dir = os.path.join(current_dir, "local_models", "coqui_tts")
        else:
            # 如果不在backend目录下，添加backend路径
            base_models_dir = os.path.join(current_dir, "backend", "local_models", "coqui_tts")
            
        logger.info(f"尝试内部修复TTS模型的基础路径: {base_models_dir}")
        
        # 检查路径是否存在
        if not os.path.exists(base_models_dir):
            # 尝试备选路径
            alt_models_dir = os.path.join(current_dir, "local_models", "coqui_tts")
            if os.path.exists(alt_models_dir):
                base_models_dir = alt_models_dir
                logger.info(f"使用备选模型目录: {base_models_dir}")
            else:
                logger.warning(f"模型目录不存在: {base_models_dir}")
                # 尝试在上级目录查找
                parent_dir = os.path.dirname(current_dir)
                parent_models_dir = os.path.join(parent_dir, "local_models", "coqui_tts")
                if os.path.exists(parent_models_dir):
                    base_models_dir = parent_models_dir
                    logger.info(f"使用上级目录中的模型目录: {base_models_dir}")
                else:
                    return False

        # 检查是否存在嵌套的tts子目录
        nested_tts_dir = os.path.join(base_models_dir, "tts")
        models_dir = nested_tts_dir if os.path.exists(nested_tts_dir) else base_models_dir
        logger.info(f"最终使用的模型目录: {models_dir}")
        
        # 查找所有config.json文件
        config_files = []
        for root, dirs, files in os.walk(models_dir):
            for file in files:
                if file.lower() == 'config.json':
                    config_files.append(os.path.join(root, file))
        
        total_files = len(config_files)
        fixed_count = 0
        skipped_count = 0
        failed_count = 0
        
        logger.info(f"找到 {total_files} 个配置文件")
        
        for file_path in config_files:
            # 检查是否已修复且不强制修复
            if not force and file_path in _FIX_STATUS["fixed_files"]:
                logger.debug(f"跳过已修复的文件: {file_path}")
                skipped_count += 1
                continue
                
            if _fix_json_file(file_path, force=force) and fix_model_config(file_path):
                fixed_count += 1
            else:
                failed_count += 1
        
        # 保存最终状态
        _save_fix_status()
        
        logger.info(f"修复完成: {fixed_count} 个成功, {skipped_count} 个跳过, {failed_count} 个失败")
        return fixed_count > 0 or skipped_count > 0
        
    except Exception as e:
        logger.error(f"内部修复配置文件时出错: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

# 如果直接运行此脚本，应用修复
if __name__ == "__main__":
    apply_fixes() 