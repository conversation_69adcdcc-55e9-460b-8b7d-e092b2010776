"""
TTS服务管理系统
支持多种开源TTS引擎和商业API
"""

import logging
import os
import tempfile
import subprocess
import asyncio
from typing import Dict, Any, Optional, List
from enum import Enum
import numpy as np

logger = logging.getLogger(__name__)

class TTSEngine(Enum):
    """TTS引擎类型"""
    # 开源引擎
    COQUI_TTS = "coqui_tts"           # Coqui TTS (Mozilla TTS继承者)
    ESPNET_TTS = "espnet_tts"         # ESPnet TTS
    PIPER_TTS = "piper_tts"           # Piper TTS (轻量级)
    BARK = "bark"                     # Bark (Suno AI)
    TORTOISE_TTS = "tortoise_tts"     # Tortoise TTS
    
    # 商业API
    AZURE_TTS = "azure_tts"           # Azure Speech Services
    GOOGLE_TTS = "google_tts"         # Google Cloud TTS
    AWS_POLLY = "aws_polly"           # Amazon Polly
    OPENAI_TTS = "openai_tts"         # OpenAI TTS
    
    # 本地引擎
    EDGE_TTS = "edge_tts"             # Microsoft Edge TTS (免费)
    FESTIVAL = "festival"             # Festival TTS

class TTSQuality(Enum):
    """TTS质量等级"""
    STANDARD = "standard"             # 标准质量
    HIGH = "high"                     # 高质量
    PREMIUM = "premium"               # 神级质量
    CLONE = "clone"                   # AI克隆语音

class TTSService:
    """TTS服务管理器"""
    
    def __init__(self):
        self.engines = {}
        self.initialize_engines()
    
    def initialize_engines(self):
        """初始化可用的TTS引擎"""
        # 检查Coqui TTS
        if self._check_coqui_tts():
            self.engines[TTSEngine.COQUI_TTS] = CoquiTTSEngine()
        
        # 检查ESPnet TTS
        if self._check_espnet_tts():
            self.engines[TTSEngine.ESPNET_TTS] = ESPnetTTSEngine()
        
        # 检查Piper TTS
        if self._check_piper_tts():
            self.engines[TTSEngine.PIPER_TTS] = PiperTTSEngine()
        
        # 检查Bark
        if self._check_bark():
            self.engines[TTSEngine.BARK] = BarkEngine()
        
        # 检查Edge TTS (通常可用)
        if self._check_edge_tts():
            self.engines[TTSEngine.EDGE_TTS] = EdgeTTSEngine()
        
        logger.info(f"初始化了 {len(self.engines)} 个TTS引擎: {list(self.engines.keys())}")
    
    def _check_coqui_tts(self) -> bool:
        """检查Coqui TTS是否可用"""
        try:
            import TTS
            return True
        except ImportError:
            return False
    
    def _check_espnet_tts(self) -> bool:
        """检查ESPnet TTS是否可用"""
        try:
            import espnet2
            return True
        except ImportError:
            return False
    
    def _check_piper_tts(self) -> bool:
        """检查Piper TTS是否可用"""
        return os.path.exists("models/piper/piper.exe") or os.path.exists("models/piper/piper")
    
    def _check_bark(self) -> bool:
        """检查Bark是否可用"""
        try:
            import bark
            return True
        except ImportError:
            return False
    
    def _check_edge_tts(self) -> bool:
        """检查Edge TTS是否可用"""
        try:
            import edge_tts
            return True
        except ImportError:
            return False
    
    def get_available_engines(self) -> List[Dict[str, Any]]:
        """获取可用的TTS引擎列表"""
        engines = []
        for engine_type, engine in self.engines.items():
            engines.append({
                "type": engine_type.value,
                "name": engine.get_name(),
                "description": engine.get_description(),
                "quality": engine.get_quality().value,
                "languages": engine.get_supported_languages(),
                "voices": engine.get_available_voices(),
                "features": engine.get_features()
            })
        return engines
    
    def get_recommended_engine(self, quality: TTSQuality, language: str = "zh-CN") -> Optional[TTSEngine]:
        """根据质量要求推荐TTS引擎"""
        if quality == TTSQuality.CLONE:
            # AI克隆语音优先使用Bark或Tortoise
            if TTSEngine.BARK in self.engines:
                return TTSEngine.BARK
            elif TTSEngine.TORTOISE_TTS in self.engines:
                return TTSEngine.TORTOISE_TTS
        
        elif quality == TTSQuality.PREMIUM:
            # 神级质量优先使用商业API
            if TTSEngine.AZURE_TTS in self.engines:
                return TTSEngine.AZURE_TTS
            elif TTSEngine.OPENAI_TTS in self.engines:
                return TTSEngine.OPENAI_TTS
            elif TTSEngine.COQUI_TTS in self.engines:
                return TTSEngine.COQUI_TTS
        
        elif quality == TTSQuality.HIGH:
            # 高质量使用Coqui TTS或ESPnet
            if TTSEngine.COQUI_TTS in self.engines:
                return TTSEngine.COQUI_TTS
            elif TTSEngine.ESPNET_TTS in self.engines:
                return TTSEngine.ESPNET_TTS
        
        else:  # STANDARD
            # 标准质量使用Edge TTS或Piper
            if TTSEngine.EDGE_TTS in self.engines:
                return TTSEngine.EDGE_TTS
            elif TTSEngine.PIPER_TTS in self.engines:
                return TTSEngine.PIPER_TTS
        
        # 降级到任何可用的引擎
        return next(iter(self.engines.keys())) if self.engines else None
    
    async def synthesize(
        self, 
        text: str, 
        engine_type: TTSEngine, 
        voice_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """合成语音"""
        if engine_type not in self.engines:
            raise ValueError(f"TTS引擎 {engine_type} 不可用")
        
        engine = self.engines[engine_type]
        return await engine.synthesize(text, voice_config)

class BaseTTSEngine:
    """TTS引擎基类"""
    
    def get_name(self) -> str:
        raise NotImplementedError
    
    def get_description(self) -> str:
        raise NotImplementedError
    
    def get_quality(self) -> TTSQuality:
        raise NotImplementedError
    
    def get_supported_languages(self) -> List[str]:
        raise NotImplementedError
    
    def get_available_voices(self) -> List[Dict[str, Any]]:
        raise NotImplementedError
    
    def get_features(self) -> List[str]:
        raise NotImplementedError
    
    async def synthesize(self, text: str, voice_config: Dict[str, Any]) -> Dict[str, Any]:
        raise NotImplementedError

class CoquiTTSEngine(BaseTTSEngine):
    """Coqui TTS引擎"""
    
    def get_name(self) -> str:
        return "Coqui TTS"
    
    def get_description(self) -> str:
        return "高质量开源TTS引擎，支持多语言和语音克隆"
    
    def get_quality(self) -> TTSQuality:
        return TTSQuality.HIGH
    
    def get_supported_languages(self) -> List[str]:
        return ["zh-CN", "en-US", "ja-JP", "ko-KR", "es-ES", "fr-FR", "de-DE"]
    
    def get_available_voices(self) -> List[Dict[str, Any]]:
        return [
            {"id": "zh-cn-female-1", "name": "中文女声1", "gender": "female", "language": "zh-CN"},
            {"id": "zh-cn-male-1", "name": "中文男声1", "gender": "male", "language": "zh-CN"},
            {"id": "en-us-female-1", "name": "English Female 1", "gender": "female", "language": "en-US"},
            {"id": "en-us-male-1", "name": "English Male 1", "gender": "male", "language": "en-US"}
        ]
    
    def get_features(self) -> List[str]:
        return ["多语言", "语音克隆", "情感控制", "语速调节"]
    
    async def synthesize(self, text: str, voice_config: Dict[str, Any]) -> Dict[str, Any]:
        try:
            from TTS.api import TTS
            
            # 选择模型
            language = voice_config.get("language", "zh-CN")
            if language == "zh-CN":
                model_name = "tts_models/zh-CN/baker/tacotron2-DDC-GST"
            else:
                model_name = "tts_models/en/ljspeech/tacotron2-DDC"
            
            tts = TTS(model_name=model_name)
            
            # 生成临时文件
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
                output_path = temp_file.name
            
            # 合成语音
            tts.tts_to_file(text=text, file_path=output_path)
            
            return {
                "success": True,
                "audio_path": output_path,
                "engine": "coqui_tts",
                "duration": self._get_audio_duration(output_path)
            }
            
        except Exception as e:
            logger.error(f"Coqui TTS合成失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _get_audio_duration(self, audio_path: str) -> float:
        """获取音频时长"""
        try:
            import librosa
            y, sr = librosa.load(audio_path)
            return len(y) / sr
        except:
            return 0.0

class EdgeTTSEngine(BaseTTSEngine):
    """Edge TTS引擎"""
    
    def get_name(self) -> str:
        return "Microsoft Edge TTS"
    
    def get_description(self) -> str:
        return "微软Edge浏览器内置TTS，免费且质量较好"
    
    def get_quality(self) -> TTSQuality:
        return TTSQuality.STANDARD
    
    def get_supported_languages(self) -> List[str]:
        return ["zh-CN", "en-US", "ja-JP", "ko-KR", "es-ES", "fr-FR", "de-DE"]
    
    def get_available_voices(self) -> List[Dict[str, Any]]:
        return [
            {"id": "zh-CN-XiaoxiaoNeural", "name": "晓晓", "gender": "female", "language": "zh-CN"},
            {"id": "zh-CN-YunxiNeural", "name": "云希", "gender": "male", "language": "zh-CN"},
            {"id": "zh-CN-XiaoyiNeural", "name": "晓伊", "gender": "female", "language": "zh-CN"},
            {"id": "zh-CN-YunjianNeural", "name": "云健", "gender": "male", "language": "zh-CN"},
            {"id": "en-US-AriaNeural", "name": "Aria", "gender": "female", "language": "en-US"},
            {"id": "en-US-GuyNeural", "name": "Guy", "gender": "male", "language": "en-US"}
        ]
    
    def get_features(self) -> List[str]:
        return ["免费使用", "多语言", "神经网络语音", "情感表达"]
    
    async def synthesize(self, text: str, voice_config: Dict[str, Any]) -> Dict[str, Any]:
        try:
            import edge_tts
            
            voice = voice_config.get("voice_id", "zh-CN-XiaoxiaoNeural")
            rate = voice_config.get("speed", 1.0)
            
            # 生成临时文件
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
                output_path = temp_file.name
            
            # 合成语音
            communicate = edge_tts.Communicate(text, voice, rate=f"{rate:+.0%}")
            await communicate.save(output_path)
            
            return {
                "success": True,
                "audio_path": output_path,
                "engine": "edge_tts",
                "duration": self._get_audio_duration(output_path)
            }
            
        except Exception as e:
            logger.error(f"Edge TTS合成失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _get_audio_duration(self, audio_path: str) -> float:
        """获取音频时长"""
        try:
            import librosa
            y, sr = librosa.load(audio_path)
            return len(y) / sr
        except:
            return 0.0

class ESPnetTTSEngine(BaseTTSEngine):
    """ESPnet TTS引擎"""
    
    def get_name(self) -> str:
        return "ESPnet TTS"
    
    def get_description(self) -> str:
        return "学术界广泛使用的开源TTS工具包"
    
    def get_quality(self) -> TTSQuality:
        return TTSQuality.HIGH
    
    def get_supported_languages(self) -> List[str]:
        return ["zh-CN", "en-US", "ja-JP"]
    
    def get_available_voices(self) -> List[Dict[str, Any]]:
        return [
            {"id": "espnet-zh-female", "name": "ESPnet中文女声", "gender": "female", "language": "zh-CN"},
            {"id": "espnet-en-female", "name": "ESPnet English Female", "gender": "female", "language": "en-US"}
        ]
    
    def get_features(self) -> List[str]:
        return ["学术级质量", "多语言", "可训练", "研究友好"]
    
    async def synthesize(self, text: str, voice_config: Dict[str, Any]) -> Dict[str, Any]:
        # ESPnet TTS实现
        return {
            "success": False,
            "error": "ESPnet TTS需要额外配置"
        }

class BarkEngine(BaseTTSEngine):
    """Bark AI语音克隆引擎"""
    
    def get_name(self) -> str:
        return "Bark"
    
    def get_description(self) -> str:
        return "Suno AI开发的AI语音克隆引擎，支持多语言和情感表达"
    
    def get_quality(self) -> TTSQuality:
        return TTSQuality.CLONE
    
    def get_supported_languages(self) -> List[str]:
        return ["zh-CN", "en-US", "ja-JP", "ko-KR", "es-ES", "fr-FR", "de-DE", "hi-IN"]
    
    def get_available_voices(self) -> List[Dict[str, Any]]:
        return [
            {"id": "v2/zh_speaker_0", "name": "中文说话者0", "gender": "female", "language": "zh-CN"},
            {"id": "v2/zh_speaker_1", "name": "中文说话者1", "gender": "male", "language": "zh-CN"},
            {"id": "v2/en_speaker_0", "name": "English Speaker 0", "gender": "female", "language": "en-US"},
            {"id": "v2/en_speaker_1", "name": "English Speaker 1", "gender": "male", "language": "en-US"}
        ]
    
    def get_features(self) -> List[str]:
        return ["AI语音克隆", "情感表达", "多语言", "零样本学习", "音效生成"]
    
    async def synthesize(self, text: str, voice_config: Dict[str, Any]) -> Dict[str, Any]:
        try:
            from bark import SAMPLE_RATE, generate_audio, preload_models
            
            # 预加载模型
            preload_models()
            
            voice_preset = voice_config.get("voice_id", "v2/zh_speaker_0")
            
            # 生成音频
            audio_array = generate_audio(text, history_prompt=voice_preset)
            
            # 保存到临时文件
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
                output_path = temp_file.name
            
            import scipy.io.wavfile as wavfile
            wavfile.write(output_path, SAMPLE_RATE, audio_array)
            
            return {
                "success": True,
                "audio_path": output_path,
                "engine": "bark",
                "duration": len(audio_array) / SAMPLE_RATE
            }
            
        except Exception as e:
            logger.error(f"Bark合成失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }

class PiperTTSEngine(BaseTTSEngine):
    """Piper TTS引擎（轻量级）"""
    
    def get_name(self) -> str:
        return "Piper TTS"
    
    def get_description(self) -> str:
        return "轻量级、快速的本地TTS引擎"
    
    def get_quality(self) -> TTSQuality:
        return TTSQuality.STANDARD
    
    def get_supported_languages(self) -> List[str]:
        return ["zh-CN", "en-US"]
    
    def get_available_voices(self) -> List[Dict[str, Any]]:
        return [
            {"id": "zh_CN-huayan-medium", "name": "华燕", "gender": "female", "language": "zh-CN"},
            {"id": "en_US-lessac-medium", "name": "Lessac", "gender": "male", "language": "en-US"}
        ]
    
    def get_features(self) -> List[str]:
        return ["轻量级", "快速合成", "本地运行", "低资源消耗"]
    
    async def synthesize(self, text: str, voice_config: Dict[str, Any]) -> Dict[str, Any]:
        # Piper TTS实现
        return {
            "success": False,
            "error": "Piper TTS需要额外配置"
        }

# 全局TTS服务实例
tts_service = TTSService()
