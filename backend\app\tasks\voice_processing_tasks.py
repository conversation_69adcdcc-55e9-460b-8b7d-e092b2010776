"""
语音处理任务
"""

from app.core.celery_app import celery_app, DatabaseTask
from app.services.tts_model_manager import tts_model_manager
import logging
import os
import numpy as np
from datetime import datetime
import uuid
import subprocess
import tempfile

# 简化版本，不依赖复杂的音频库
try:
    import librosa
    import soundfile as sf
    AUDIO_LIBS_AVAILABLE = True
except ImportError:
    AUDIO_LIBS_AVAILABLE = False

logger = logging.getLogger(__name__)

@celery_app.task(bind=True, base=DatabaseTask, name="process_voice_sample")
async def process_voice_sample(self, digital_human_id: int, task_id: str, input_data: dict):
    """
    处理语音样本
    包括TTS生成、语音克隆、音频处理等
    """
    try:
        logger.info(f"开始处理语音样本: ID={digital_human_id}")
        
        # 更新进度
        self.update_task_progress(task_id, 35, "正在处理语音样本...")
        
        # 获取语音配置
        voice_config = extract_voice_config(input_data)
        
        # 生成语音样本
        self.update_task_progress(task_id, 40, "正在生成语音...")

        # 选择最佳的TTS模型
        quality = voice_config.get("quality", "standard")
        available_models = tts_model_manager.get_available_models()

        # 根据质量选择模型
        selected_model = None
        for model in available_models:
            if model["available"] and model["quality"] == quality:
                selected_model = model["id"]
                break

        if not selected_model and available_models:
            # 降级到任何可用的模型
            for model in available_models:
                if model["available"]:
                    selected_model = model["id"]
                    break

        if not selected_model:
            raise ValueError("没有可用的TTS模型")

        # 使用TTS模型生成语音
        tts_result = await tts_model_manager.synthesize_speech(
            voice_config["text"],
            selected_model,
            voice_config
        )

        if not tts_result["success"]:
            raise ValueError(f"TTS生成失败: {tts_result['error']}")

        # 加载生成的音频
        if AUDIO_LIBS_AVAILABLE:
            audio_data, sample_rate = librosa.load(tts_result["audio_path"], sr=None)

            # 语音增强
            self.update_task_progress(task_id, 45, "正在增强语音质量...")
            enhanced_audio = enhance_audio(audio_data, sample_rate)

            # 保存语音文件
            self.update_task_progress(task_id, 50, "正在保存语音文件...")
            output_path = save_voice_sample(enhanced_audio, sample_rate, digital_human_id)

            # 提取语音特征（用于嘴型同步）
            voice_features = extract_voice_features(enhanced_audio, sample_rate)
        else:
            # 简化处理：直接使用生成的音频文件
            output_path = tts_result["audio_path"]
            voice_features = {
                "duration": tts_result.get("duration", 3.0),
                "sample_rate": 22050,
                "simplified": True
            }
        
        result = {
            "success": True,
            "digital_human_id": digital_human_id,
            "task_id": task_id,
            "voice_sample_url": f"/static/generated/{os.path.basename(output_path)}",
            "voice_sample_path": output_path,
            "voice_config": voice_config,
            "voice_features": voice_features,
            "audio_info": {
                "duration": len(enhanced_audio) / sample_rate,
                "sample_rate": sample_rate,
                "channels": 1,
                "format": "wav"
            },
            "processing_steps": [
                "语音配置解析完成",
                "语音生成完成",
                "语音增强完成",
                "语音特征提取完成"
            ]
        }
        
        logger.info(f"语音样本处理完成: {result}")
        return result
        
    except Exception as e:
        logger.error(f"语音样本处理失败: {str(e)}")
        raise

def extract_voice_config(input_data: dict) -> dict:
    """提取语音配置"""
    return {
        "text": input_data.get("welcome_text", "您好，我是您的数字人助手"),
        "voice_type": input_data.get("voice_type", "standard"),
        "selected_voice": input_data.get("selected_voice"),
        "custom_voice_url": input_data.get("custom_voice_url"),
        "voice_speed": input_data.get("voice_speed", 1.0),
        "voice_pitch": input_data.get("voice_pitch", 1.0),
        "gender": input_data.get("gender", "female"),
        "language": "zh-CN"
    }

def generate_voice_sample(voice_config: dict) -> tuple:
    """生成语音样本"""
    text = voice_config["text"]
    voice_type = voice_config["voice_type"]
    
    if voice_type == "custom" and voice_config.get("custom_voice_url"):
        # 使用自定义语音进行克隆
        return clone_voice_sample(text, voice_config["custom_voice_url"], voice_config)
    else:
        # 使用标准TTS
        return generate_tts_sample(text, voice_config)

def generate_tts_sample(text: str, voice_config: dict) -> tuple:
    """使用TTS生成语音样本"""
    try:
        if AUDIO_LIBS_AVAILABLE:
            # 尝试使用真实的TTS
            return generate_real_tts(text, voice_config)
        else:
            # 使用简化的静音生成
            logger.warning("音频库不可用，生成静音样本")
            duration = max(len(text) * 0.1, 3.0)  # 根据文本长度估算时长
            return generate_silence(duration, 22050)

    except Exception as e:
        logger.error(f"TTS生成失败: {str(e)}")
        # 使用备用方案
        return generate_silence(3.0, 22050)

def generate_real_tts(text: str, voice_config: dict) -> tuple:
    """生成真实的TTS（需要音频库）"""
    try:
        # 创建临时文件
        with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
            temp_path = temp_file.name

        # 构建TTS命令
        speed = voice_config.get("voice_speed", 1.0)
        pitch = voice_config.get("voice_pitch", 1.0)

        # 使用espnet-tts生成语音
        cmd = [
            "python", "-m", "espnet2.bin.tts_inference",
            "--text", text,
            "--output_dir", os.path.dirname(temp_path),
            "--output_name", os.path.basename(temp_path).replace(".wav", ""),
            "--vocoder_tag", "parallel_wavegan/ljspeech_parallel_wavegan.v1",
            "--speed_control_alpha", str(speed),
        ]

        try:
            subprocess.run(cmd, check=True, capture_output=True, text=True)

            # 加载生成的音频
            audio_data, sample_rate = librosa.load(temp_path, sr=None)

            # 应用音调调整
            if pitch != 1.0:
                audio_data = librosa.effects.pitch_shift(audio_data, sr=sample_rate, n_steps=pitch*12-12)

            return audio_data, sample_rate

        except subprocess.CalledProcessError:
            # 如果espnet-tts失败，使用备用方案
            logger.warning("espnet-tts失败，使用备用TTS方案")
            return generate_fallback_tts(text, voice_config)
        finally:
            # 清理临时文件
            if os.path.exists(temp_path):
                os.unlink(temp_path)

    except Exception as e:
        logger.error(f"真实TTS生成失败: {str(e)}")
        return generate_fallback_tts(text, voice_config)

def generate_fallback_tts(text: str, voice_config: dict) -> tuple:
    """备用TTS方案"""
    try:
        # 使用Mozilla TTS
        from TTS.api import TTS
        
        # 初始化TTS模型
        model_name = get_tts_model_name(voice_config["language"], voice_config["gender"])
        tts = TTS(model_name=model_name)
        
        # 生成语音
        with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
            temp_path = temp_file.name
        
        tts.tts_to_file(text=text, file_path=temp_path)
        
        # 加载音频
        audio_data, sample_rate = librosa.load(temp_path, sr=None)
        
        # 清理临时文件
        os.unlink(temp_path)
        
        return audio_data, sample_rate
        
    except Exception as e:
        logger.error(f"备用TTS也失败: {str(e)}")
        # 最后的备用方案：生成静音
        return generate_silence(3.0, 22050)

def generate_silence(duration: float, sample_rate: int) -> tuple:
    """生成静音作为最后的备用方案"""
    samples = int(duration * sample_rate)
    audio_data = np.zeros(samples, dtype=np.float32)
    return audio_data, sample_rate

def clone_voice_sample(text: str, reference_voice_url: str, voice_config: dict) -> tuple:
    """克隆语音样本"""
    try:
        # 下载参考语音
        reference_path = download_reference_voice(reference_voice_url)
        
        # 使用语音克隆技术（如Real-Time-Voice-Cloning）
        # 这里需要集成具体的语音克隆模型
        
        # 简化实现：使用参考语音的特征生成新语音
        ref_audio, ref_sr = librosa.load(reference_path, sr=None)
        
        # 提取参考语音的特征
        ref_features = extract_voice_features(ref_audio, ref_sr)
        
        # 生成基础TTS
        base_audio, base_sr = generate_tts_sample(text, voice_config)
        
        # 应用语音特征转换
        cloned_audio = apply_voice_conversion(base_audio, base_sr, ref_features)
        
        return cloned_audio, base_sr
        
    except Exception as e:
        logger.error(f"语音克隆失败: {str(e)}")
        # 降级到标准TTS
        return generate_tts_sample(text, voice_config)

def download_reference_voice(voice_url: str) -> str:
    """下载参考语音"""
    import requests
    
    response = requests.get(voice_url)
    response.raise_for_status()
    
    with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
        temp_file.write(response.content)
        return temp_file.name

def apply_voice_conversion(source_audio: np.ndarray, sample_rate: int, target_features: dict) -> np.ndarray:
    """应用语音转换"""
    # 这里应该实现真正的语音转换算法
    # 简化实现：只调整基本参数
    
    converted_audio = source_audio.copy()
    
    # 调整音调
    if "pitch" in target_features:
        pitch_shift = target_features["pitch"] - 1.0
        converted_audio = librosa.effects.pitch_shift(
            converted_audio, sr=sample_rate, n_steps=pitch_shift*12
        )
    
    # 调整语速
    if "tempo" in target_features:
        tempo_ratio = target_features["tempo"]
        converted_audio = librosa.effects.time_stretch(converted_audio, rate=tempo_ratio)
    
    return converted_audio

def enhance_audio(audio_data: np.ndarray, sample_rate: int) -> np.ndarray:
    """音频增强"""
    # 去噪
    enhanced_audio = remove_noise(audio_data, sample_rate)
    
    # 音量标准化
    enhanced_audio = normalize_volume(enhanced_audio)
    
    # 音频压缩
    enhanced_audio = apply_compression(enhanced_audio)
    
    return enhanced_audio

def remove_noise(audio_data: np.ndarray, sample_rate: int) -> np.ndarray:
    """去噪处理"""
    # 使用谱减法去噪
    # 简化实现
    return audio_data

def normalize_volume(audio_data: np.ndarray) -> np.ndarray:
    """音量标准化"""
    # 标准化到-20dB
    target_db = -20.0
    current_db = 20 * np.log10(np.sqrt(np.mean(audio_data**2)))
    gain_db = target_db - current_db
    gain_linear = 10**(gain_db/20)
    
    normalized_audio = audio_data * gain_linear
    
    # 防止削波
    max_val = np.max(np.abs(normalized_audio))
    if max_val > 0.95:
        normalized_audio = normalized_audio * (0.95 / max_val)
    
    return normalized_audio

def apply_compression(audio_data: np.ndarray) -> np.ndarray:
    """音频压缩"""
    # 简单的软限制器
    threshold = 0.8
    ratio = 4.0
    
    compressed_audio = audio_data.copy()
    mask = np.abs(compressed_audio) > threshold
    
    excess = np.abs(compressed_audio[mask]) - threshold
    compressed_excess = excess / ratio
    
    compressed_audio[mask] = np.sign(compressed_audio[mask]) * (threshold + compressed_excess)
    
    return compressed_audio

def extract_voice_features(audio_data: np.ndarray, sample_rate: int) -> dict:
    """提取语音特征（用于嘴型同步）"""
    # 提取MFCC特征
    mfcc = librosa.feature.mfcc(y=audio_data, sr=sample_rate, n_mfcc=13)
    
    # 提取基频
    f0, voiced_flag, voiced_probs = librosa.pyin(
        audio_data, fmin=librosa.note_to_hz('C2'), fmax=librosa.note_to_hz('C7')
    )
    
    # 提取能量
    energy = librosa.feature.rms(y=audio_data)[0]
    
    # 提取语音活动检测
    intervals = librosa.effects.split(audio_data, top_db=20)
    
    return {
        "mfcc": mfcc.tolist(),
        "f0": f0.tolist(),
        "energy": energy.tolist(),
        "voiced_segments": intervals.tolist(),
        "duration": len(audio_data) / sample_rate,
        "sample_rate": sample_rate
    }

def save_voice_sample(audio_data: np.ndarray, sample_rate: int, digital_human_id: int) -> str:
    """保存语音样本"""
    # 确保输出目录存在
    output_dir = "storage/generated"
    os.makedirs(output_dir, exist_ok=True)
    
    # 生成文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"voice_{digital_human_id}_{timestamp}.wav"
    output_path = os.path.join(output_dir, filename)
    
    # 保存音频文件
    sf.write(output_path, audio_data, sample_rate)
    
    return output_path

def get_voice_name(voice_config: dict) -> str:
    """获取语音名称"""
    gender = voice_config.get("gender", "female")
    language = voice_config.get("language", "zh-CN")
    
    voice_mapping = {
        ("zh-CN", "female"): "zh-CN-XiaoxiaoNeural",
        ("zh-CN", "male"): "zh-CN-YunxiNeural",
        ("en-US", "female"): "en-US-AriaNeural",
        ("en-US", "male"): "en-US-GuyNeural"
    }
    
    return voice_mapping.get((language, gender), "zh-CN-XiaoxiaoNeural")

def get_tts_model_name(language: str, gender: str) -> str:
    """获取TTS模型名称"""
    if language == "zh-CN":
        return "tts_models/zh-CN/baker/tacotron2-DDC-GST"
    else:
        return "tts_models/en/ljspeech/tacotron2-DDC"
