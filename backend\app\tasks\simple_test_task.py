"""
最简单的测试任务
"""
from app.core.celery_app import celery_app

@celery_app.task(name="simple_add")
def simple_add(x, y):
    """最简单的加法任务"""
    try:
        result = x + y
        print(f"执行加法: {x} + {y} = {result}")
        return result
    except Exception as e:
        print(f"加法任务失败: {e}")
        raise

@celery_app.task(name="simple_hello")
def simple_hello(name="World"):
    """最简单的问候任务"""
    try:
        message = f"Hello, {name}!"
        print(f"问候消息: {message}")
        return message
    except Exception as e:
        print(f"问候任务失败: {e}")
        raise

# 带绑定的任务版本（用于需要访问任务实例的场景）
@celery_app.task(bind=True, name="simple_add_with_bind")
def simple_add_with_bind(self, x, y):
    """带绑定的加法任务"""
    try:
        result = x + y
        print(f"[任务 {self.request.id}] 执行加法: {x} + {y} = {result}")

        # 更新任务状态
        self.update_state(
            state="PROGRESS",
            meta={"progress": 50, "message": "计算中..."}
        )

        # 模拟一些处理时间
        import time
        time.sleep(1)

        self.update_state(
            state="PROGRESS",
            meta={"progress": 100, "message": "计算完成"}
        )

        return result
    except Exception as e:
        print(f"加法任务失败: {e}")
        raise
