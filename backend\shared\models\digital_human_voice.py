from datetime import datetime
from typing import List, Dict, Optional, Any
from pydantic import BaseModel, validator

# 定义语音模型
class VoiceModel(BaseModel):
    """数字人语音模型"""
    voice_type: Optional[str] = None
    speed: Optional[float] = 1.0
    pitch: Optional[float] = 0.0
    emphasis: Optional[float] = 1.0

class DigitalHumanVoiceSettings(BaseModel):
    """数字人语音设置"""
    digital_human_id: str
    voice_id: Optional[str] = None  # 语音ID
    voice_type: Optional[str] = None  # 语音类型
    voice_speed: Optional[float] = 1.0  # 语速
    voice_pitch: Optional[float] = 0.0  # 音调
    welcome_text: Optional[str] = None  # 欢迎语
    custom_voice_url: Optional[str] = None  # 自定义语音URL
    
    # 扩展属性
    emotion_mapping: Optional[Dict[str, Dict[str, float]]] = None
    voice_settings: Optional[Dict[str, Any]] = None  # 其他语音设置 