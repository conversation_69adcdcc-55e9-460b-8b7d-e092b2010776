import { get, post, put, del } from './index';

/**
 * 获取内容列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.limit - 每页数量
 * @param {string} params.status - 内容状态
 * @param {string} params.type - 内容类型
 * @param {string} params.keyword - 搜索关键词
 * @returns {Promise}
 */
export function getContentList(params) {
  return get('/contents', params);
}

/**
 * 获取内容详情
 * @param {string} id - 内容ID
 * @returns {Promise}
 */
export function getContentDetail(id) {
  return get(`/contents/${id}`);
}

/**
 * 审核内容
 * @param {string} id - 内容ID
 * @param {Object} data - 审核数据
 * @param {string} data.status - 审核状态 (approved/rejected)
 * @param {string} data.reason - 审核原因
 * @returns {Promise}
 */
export function reviewContent(id, data) {
  return post(`/contents/${id}/review`, data);
}

/**
 * 批量审核内容
 * @param {Object} data - 批量审核数据
 * @param {Array} data.ids - 内容ID数组
 * @param {string} data.status - 审核状态
 * @param {string} data.reason - 审核原因
 * @returns {Promise}
 */
export function batchReviewContents(data) {
  return post('/contents/batch-review', data);
}

/**
 * 删除内容
 * @param {string} id - 内容ID
 * @returns {Promise}
 */
export function deleteContent(id) {
  return del(`/contents/${id}`);
}

/**
 * 批量删除内容
 * @param {Array} ids - 内容ID数组
 * @returns {Promise}
 */
export function batchDeleteContents(ids) {
  return post('/contents/batch-delete', { ids });
}

/**
 * 获取内容类型列表
 * @returns {Promise}
 */
export function getContentTypes() {
  return get('/content-types');
}

/**
 * 获取审核规则列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.limit - 每页数量
 * @returns {Promise}
 */
export function getContentRules(params) {
  return get('/content-rules', params);
}

/**
 * 获取审核规则详情
 * @param {string} id - 规则ID
 * @returns {Promise}
 */
export function getContentRuleDetail(id) {
  return get(`/content-rules/${id}`);
}

/**
 * 创建审核规则
 * @param {Object} data - 规则数据
 * @returns {Promise}
 */
export function createContentRule(data) {
  return post('/content-rules', data);
}

/**
 * 更新审核规则
 * @param {string} id - 规则ID
 * @param {Object} data - 规则数据
 * @returns {Promise}
 */
export function updateContentRule(id, data) {
  return put(`/content-rules/${id}`, data);
}

/**
 * 删除审核规则
 * @param {string} id - 规则ID
 * @returns {Promise}
 */
export function deleteContentRule(id) {
  return del(`/content-rules/${id}`);
}

/**
 * 启用/禁用审核规则
 * @param {string} id - 规则ID
 * @param {boolean} enabled - 是否启用
 * @returns {Promise}
 */
export function toggleContentRule(id, enabled) {
  return put(`/content-rules/${id}/toggle`, { enabled });
}

/**
 * 获取内容审核统计
 * @param {Object} params - 查询参数
 * @param {string} params.timeRange - 时间范围
 * @returns {Promise}
 */
export function getContentStats(params) {
  return get('/contents/stats', params);
}

/**
 * 获取敏感词列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.limit - 每页数量
 * @param {string} params.category - 分类
 * @returns {Promise}
 */
export function getSensitiveWords(params) {
  return get('/sensitive-words', params);
}

/**
 * 添加敏感词
 * @param {Object} data - 敏感词数据
 * @returns {Promise}
 */
export function addSensitiveWord(data) {
  return post('/sensitive-words', data);
}

/**
 * 删除敏感词
 * @param {string} id - 敏感词ID
 * @returns {Promise}
 */
export function deleteSensitiveWord(id) {
  return del(`/sensitive-words/${id}`);
}

/**
 * 批量导入敏感词
 * @param {FormData} formData - 包含文件的表单数据
 * @returns {Promise}
 */
export function importSensitiveWords(formData) {
  return post('/sensitive-words/import', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}

export default {
  getContentList,
  getContentDetail,
  reviewContent,
  batchReviewContents,
  deleteContent,
  batchDeleteContents,
  getContentTypes,
  getContentRules,
  getContentRuleDetail,
  createContentRule,
  updateContentRule,
  deleteContentRule,
  toggleContentRule,
  getContentStats,
  getSensitiveWords,
  addSensitiveWord,
  deleteSensitiveWord,
  importSensitiveWords
}; 