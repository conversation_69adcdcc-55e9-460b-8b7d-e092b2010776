#!/usr/bin/env python3
"""
修复后的 Celery 测试脚本
"""
import os
import sys
import time
from pathlib import Path
from dotenv import load_dotenv

# 添加后端路径
backend_dir = Path(__file__).parent.parent
sys.path.append(str(backend_dir))

# 加载环境变量
env_path = backend_dir / ".env"
load_dotenv(env_path)

def test_simple_tasks():
    """测试简单任务"""
    print("🧪 测试修复后的简单任务")
    print("=" * 50)
    
    try:
        from app.tasks.simple_test_task import simple_add, simple_hello, simple_add_with_bind
        
        # 1. 测试同步执行
        print("📤 测试同步执行...")
        result1 = simple_add(10, 20)
        print(f"   simple_add(10, 20) = {result1}")
        
        result2 = simple_hello("Celery Test")
        print(f"   simple_hello('Celery Test') = {result2}")
        
        # 2. 测试异步执行（无绑定）
        print("\n📤 测试异步执行（无绑定）...")
        task1 = simple_add.delay(15, 25)
        print(f"   任务 ID: {task1.id}")
        print(f"   任务状态: {task1.status}")
        
        try:
            result = task1.get(timeout=10)
            print(f"   ✅ 异步任务结果: {result}")
        except Exception as e:
            print(f"   ❌ 异步任务失败: {e}")
            return False
        
        # 3. 测试异步执行（带绑定）
        print("\n📤 测试异步执行（带绑定）...")
        task2 = simple_add_with_bind.delay(30, 40)
        print(f"   任务 ID: {task2.id}")
        
        # 监控任务进度
        while not task2.ready():
            if task2.state == 'PROGRESS':
                meta = task2.info
                print(f"   📊 进度: {meta.get('progress', 0)}% - {meta.get('message', '')}")
            time.sleep(0.5)
        
        try:
            result = task2.get(timeout=10)
            print(f"   ✅ 带绑定任务结果: {result}")
        except Exception as e:
            print(f"   ❌ 带绑定任务失败: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 简单任务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_task_manager():
    """测试任务管理器"""
    print(f"\n📊 测试修复后的任务管理器")
    print("=" * 50)
    
    try:
        from app.core.task_manager import task_manager
        
        # 创建测试任务
        test_task_id = f"test_task_{int(time.time())}"
        
        created_task = task_manager.create_task(
            task_type='test',
            task_subtype='celery_test',
            user_id='test_user',
            title='Celery 功能测试',
            description='测试任务管理器和 Celery 集成',
            input_params={'test_param': 'test_value'},
            estimated_duration=120
        )
        
        if created_task:
            print(f"✅ 创建测试任务成功: {created_task}")
            
            # 更新任务状态
            task_manager.update_task(created_task, status='processing', progress=25, message='开始处理')
            print("✅ 更新任务状态成功")
            
            # 获取任务信息
            task_info = task_manager.get_task(created_task)
            if task_info:
                print(f"✅ 获取任务信息成功:")
                print(f"   状态: {task_info.get('status')}")
                print(f"   进度: {task_info.get('progress')}%")
                print(f"   消息: {task_info.get('message')}")
            
            # 完成任务
            task_manager.update_task(
                created_task, 
                status='completed', 
                progress=100, 
                message='任务完成',
                output_data={'result': 'success'}
            )
            print("✅ 完成任务成功")
            
            return True
        else:
            print("❌ 创建测试任务失败")
            return False
            
    except Exception as e:
        print(f"❌ 任务管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_translation_task():
    """测试翻译任务"""
    print(f"\n🌐 测试翻译任务")
    print("=" * 50)
    
    try:
        from app.tasks.translation_tasks import translate_text_task
        
        # 测试翻译任务
        print("📤 发送翻译任务...")
        task = translate_text_task.delay(
            text="Hello, how are you today?",
            source_lang="en",
            target_lang="zh"
        )
        
        print(f"   任务 ID: {task.id}")
        print(f"   任务状态: {task.status}")
        
        try:
            result = task.get(timeout=30)
            print(f"   ✅ 翻译结果: {result}")
            return True
        except Exception as e:
            print(f"   ❌ 翻译任务失败: {e}")
            return False
            
    except Exception as e:
        print(f"❌ 翻译任务测试失败: {e}")
        return False

def test_worker_queues():
    """测试 Worker 队列"""
    print(f"\n🚛 测试 Worker 队列")
    print("=" * 50)
    
    try:
        from app.core.celery_app import celery_app
        
        # 检查队列状态
        inspect = celery_app.control.inspect()
        
        # 获取队列长度
        active_queues = inspect.active_queues()
        if active_queues:
            print("📊 活跃队列:")
            for worker, queues in active_queues.items():
                print(f"   Worker: {worker}")
                for queue in queues:
                    print(f"     - {queue['name']}: {queue.get('messages', 0)} 消息")
        
        # 发送任务到不同队列
        from app.tasks.simple_test_task import simple_add
        
        print("\n📤 发送任务到测试队列...")
        task = simple_add.delay(100, 200)
        print(f"   任务 ID: {task.id}")
        
        result = task.get(timeout=10)
        print(f"   ✅ 队列任务结果: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ 队列测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 Celery 修复后功能测试")
    print("=" * 60)
    
    results = {}
    
    # 1. 测试简单任务
    results['simple_tasks'] = test_simple_tasks()
    
    # 2. 测试任务管理器
    results['task_manager'] = test_task_manager()
    
    # 3. 测试翻译任务
    results['translation'] = test_translation_task()
    
    # 4. 测试队列
    results['queues'] = test_worker_queues()
    
    # 总结
    print(f"\n📊 测试结果总结:")
    print("=" * 60)
    
    for component, status in results.items():
        status_icon = "✅" if status else "❌"
        status_text = "通过" if status else "失败"
        print(f"{status_icon} {component.upper()}: {status_text}")
    
    # 总体状态
    all_passed = all(results.values())
    if all_passed:
        print(f"\n🎉 所有 Celery 功能测试通过！")
        print(f"💡 Celery 系统已修复并正常工作")
    else:
        failed_tests = [k for k, v in results.items() if not v]
        print(f"\n⚠️ 以下测试失败: {', '.join(failed_tests)}")
        print(f"💡 请检查相关配置和依赖")
    
    return all_passed

if __name__ == "__main__":
    main()
