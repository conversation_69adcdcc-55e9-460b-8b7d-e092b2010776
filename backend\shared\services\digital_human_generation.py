import os
import logging
import asyncio
import time
import uuid
import json
import aiohttp
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta

from services.human_generator import HumanGenerator, get_human_generator
from services.human_generator.task_queue import TaskPriority, TaskStatus
from services.progress_tracker import get_progress_tracker, ProgressTracker

# 更改为使用新的适配器
from services.progress_tracker import ProgressTracker
from services.progress_tracker_adapter import get_progress_tracker

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建一个临时类用于调试，实际应该从适当的模块导入
class HumanVideoGeneratorService:
    """服务生成视频的临时替代类，实际项目中应当从正确的模块导入"""
    async def generate_video(self, task_id: str, digital_human_id: str, image_path: str, 
                           voice_id: str, welcome_text: str, gender: str = None, **kwargs):
        """模拟视频生成过程"""
        self.logger.info(f"开始生成数字人视频: 任务ID={task_id}, 数字人ID={digital_human_id}")
        
        # 模拟处理过程
        await asyncio.sleep(1)
        
        # 返回成功响应
        return {
            "success": True,
            "message": "数字人生成成功",
            "video_url": f"https://example.com/videos/{digital_human_id}.mp4",
            "audio_url": f"https://example.com/audios/{digital_human_id}.mp3"
        }

# 全局环境配置
DEBUG_MODE = os.getenv("DIGITAL_HUMAN_DEBUG", "false").lower() in ["true", "1", "yes"]
MOCK_MODE = os.getenv("DIGITAL_HUMAN_MOCK", "false").lower() in ["true", "1", "yes"]

if DEBUG_MODE:
    logger.setLevel(logging.DEBUG)
    logger.info("数字人生成服务调试模式已启用")
    
if MOCK_MODE:
    logger.info("数字人生成服务模拟模式已启用 - 将返回模拟结果而不进行实际处理")


class DigitalHumanGenerationService:
    """数字人生成服务类，封装HumanGenerator提供给API层使用"""
    
    def __init__(self):
        """初始化数字人生成服务"""
        self.human_generator = get_human_generator()
        self.progress_tracker = get_progress_tracker()
        self._is_initialized = False
        self._lock = asyncio.Lock()
        logger.info("数字人生成服务已创建")
        
    async def initialize(self):
        """初始化服务（如果尚未初始化）"""
        async with self._lock:
            if not self._is_initialized:
                # 启动人脸生成器
                await self.human_generator.start()
                self._is_initialized = True
                logger.info("数字人生成服务已初始化")
                
    async def shutdown(self):
        """关闭服务"""
        async with self._lock:
            if self._is_initialized:
                await self.human_generator.stop()
                self._is_initialized = False
                logger.info("数字人生成服务已关闭")
                
    async def generate_digital_human(self, 
                                  task_id: str,
                                  digital_human_id: str,
                                  image_path: str, 
                                  model_type: str = "default", 
                                  output_path: Optional[str] = None,
                                  voice_id: Optional[str] = None,
                                  welcome_text: Optional[str] = None,
                                  gender: Optional[str] = None,
                                  progress_tracker: Optional[Any] = None,
                                  use_permanent_storage: Optional[bool] = None,
                                  name: Optional[str] = None,
                                  style: Optional[str] = None,
                                  tags: Optional[List[str]] = None,
                                  appearance: Optional[Dict[str, Any]] = None,
                                  voice_settings: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        从图像生成数字人视频
        
        Args:
            task_id: 任务ID
            digital_human_id: 数字人ID
            image_path: 输入图像路径
            model_type: 模型类型
            output_path: 输出路径，如果为None则自动生成
            voice_id: 语音ID
            welcome_text: 欢迎文本
            gender: 性别 (male/female/other)
            progress_tracker: 进度跟踪器
            use_permanent_storage: 是否使用永久存储，覆盖默认设置
            name: 数字人名称
            style: 数字人风格
            tags: 标签列表
            appearance: 外观设置
            voice_settings: 语音设置
            
        Returns:
            Dict[str, Any]: 生成结果
        """
        # 确保服务已初始化
        await self.initialize()
        
        # 记录所有接收到的参数
        logger.info(f"generate_digital_human接收到的参数: task_id={task_id}, digital_human_id={digital_human_id}, "
                   f"gender={gender}, name={name}, style={style}, 是否有tags={tags is not None}, "
                   f"是否有appearance={appearance is not None}, 是否有voice_settings={voice_settings is not None}")
        
        # 设置进度跟踪器，优先使用传入的，否则使用服务的默认跟踪器
        tracker = progress_tracker or self.progress_tracker
        
        # 处理模拟模式
        if MOCK_MODE:
            logger.info(f"使用模拟模式生成数字人 {digital_human_id}")
            return await self._mock_generate_digital_human(
                task_id=task_id,
                digital_human_id=digital_human_id,
                image_path=image_path,
                model_type=model_type,
                voice_id=voice_id,
                welcome_text=welcome_text,
                gender=gender,
                progress_tracker=tracker,
                name=name,
                style=style,
                tags=tags,
                appearance=appearance,
                voice_settings=voice_settings
            )
        
        try:
            # 验证文件路径
            if not os.path.exists(image_path):
                error_msg = f"媒体文件不存在: {image_path}"
                logger.error(error_msg)
                return {
                    "success": False,
                    "error": error_msg
                }
            
            # 更新进度 - 修复调用，移除message参数
            await tracker.update_task(
                task_id=task_id,
                status="processing",
                progress=10
            )
            
            # 准备语音数据
            audio_path = None
            if welcome_text:
                # 更新进度 - 修复调用，移除message参数
                await tracker.update_task(
                    task_id=task_id,
                    progress=20
                )
                
                # 调用生成语音的方法
                audio_result = await self.human_generator.sad_talker.generate_speech(
                    text=welcome_text,
                    voice_id=voice_id if voice_id else "default"
                )
                audio_path = audio_result if isinstance(audio_result, str) else audio_result.get("path")
                
                logger.info(f"生成语音成功: {audio_path}")
            
            # 更新进度 - 修复调用，移除message参数
            await tracker.update_task(
                task_id=task_id,
                progress=40
            )
            
            # 调用HumanGenerator生成视频
            if audio_path:
                # 有音频，生成视频
                video_result = await self.human_generator.sad_talker.generate_video(
                    source_image=image_path,
                    audio_path=audio_path,
                    gender=gender
                )
                
                video_path = video_result.get("path") if isinstance(video_result, dict) else video_result
                video_url = video_result.get("url") if isinstance(video_result, dict) else f"/static/videos/{os.path.basename(video_path)}"
                
                logger.info(f"生成视频成功: {video_path}")
            else:
                # 无音频，只处理图像
                logger.info(f"无欢迎文本，使用默认处理图像: {image_path}")
                # 调用底层的generate_digital_human方法
                video_path = self.human_generator.generate_digital_human(
                    image_path=image_path,
                    model_type=model_type,
                    output_path=output_path,
                    use_permanent_storage=use_permanent_storage
                )
                video_url = f"/static/videos/{os.path.basename(video_path)}" if video_path else None
            
            # 更新进度为完成 - 修复调用，移除message参数
            await tracker.update_task(
                task_id=task_id,
                status="completed",
                progress=100,
                result_url=video_url,
                audio_url=f"/static/audio/{os.path.basename(audio_path)}" if audio_path else None
            )
            
            logger.info(f"数字人生成成功: {digital_human_id}")
            
            # 创建或更新数据库记录
            try:
                # 导入需要的模块
                from sqlalchemy.orm import Session
                from utils.db import SessionLocal
                from models.digital_human import DigitalHuman
                # 使用别名避免与全局datetime冲突
                from datetime import datetime as dt
                
                # 创建数据库会话
                db = SessionLocal()
                try:
                    # 查询已有记录
                    digital_human = db.query(DigitalHuman).filter(
                        DigitalHuman.digital_human_id == digital_human_id
                    ).first()
                    
                    # 生成后端访问URL
                    video_path = video_result.get("path") if isinstance(video_result, dict) else video_result
                    
                    if digital_human:
                        # 更新现有记录
                        logger.info(f"更新数字人记录: {digital_human_id}")
                        digital_human.generation_status = "completed"
                        digital_human.updated_at = dt.now()
                        digital_human.video_url = video_url
                        
                        if voice_id:
                            digital_human.voice_id = voice_id
                        if welcome_text:
                            digital_human.welcome_text = welcome_text
                        
                        # 更新附加属性
                        if name and hasattr(digital_human, 'name'):
                            digital_human.name = name
                            logger.info(f"更新数字人名称: {name}")
                        
                        if style and hasattr(digital_human, 'style'):
                            digital_human.style = style
                            logger.info(f"更新数字人风格: {style}")
                            
                        if gender and hasattr(digital_human, 'gender'):
                            digital_human.gender = gender
                            logger.info(f"更新数字人性别: {gender}")
                            
                        if tags and hasattr(digital_human, 'tags'):
                            digital_human.tags = json.dumps(tags) if isinstance(tags, list) else tags
                            logger.info(f"更新数字人标签: {tags}")
                        
                        if appearance and hasattr(digital_human, 'appearance'):
                            digital_human.appearance = json.dumps(appearance) if isinstance(appearance, dict) else appearance
                            logger.info(f"更新外观设置: {appearance}")
                        
                        if voice_settings and hasattr(digital_human, 'voice_settings'):
                            digital_human.voice_settings = json.dumps(voice_settings) if isinstance(voice_settings, dict) else voice_settings
                            logger.info(f"更新语音设置: {voice_settings}")
                        
                    else:
                        # 创建新记录
                        logger.info(f"创建新数字人记录: {digital_human_id}")
                        new_digital_human = DigitalHuman(
                            digital_human_id=digital_human_id,
                            generation_status="completed",
                            created_at=dt.now(),
                            updated_at=dt.now(),
                            video_url=video_url
                        )
                        
                        # 设置基本属性
                        if voice_id:
                            new_digital_human.voice_id = voice_id
                        if welcome_text:
                            new_digital_human.welcome_text = welcome_text
                            
                        # 设置附加属性
                        if name and hasattr(DigitalHuman, 'name'):
                            new_digital_human.name = name
                            logger.info(f"设置数字人名称: {name}")
                        
                        if style and hasattr(DigitalHuman, 'style'):
                            new_digital_human.style = style
                            logger.info(f"设置数字人风格: {style}")
                            
                        if gender and hasattr(DigitalHuman, 'gender'):
                            new_digital_human.gender = gender
                            logger.info(f"设置数字人性别: {gender}")
                            
                        if tags and hasattr(DigitalHuman, 'tags'):
                            new_digital_human.tags = json.dumps(tags) if isinstance(tags, list) else tags
                            logger.info(f"设置数字人标签: {tags}")
                        
                        if appearance and hasattr(DigitalHuman, 'appearance'):
                            new_digital_human.appearance = json.dumps(appearance) if isinstance(appearance, dict) else appearance
                            logger.info(f"设置外观设置: {appearance}")
                        
                        if voice_settings and hasattr(DigitalHuman, 'voice_settings'):
                            new_digital_human.voice_settings = json.dumps(voice_settings) if isinstance(voice_settings, dict) else voice_settings
                            logger.info(f"设置语音设置: {voice_settings}")
                        
                        db.add(new_digital_human)
                    
                    # 提交更改
                    db.commit()
                    logger.info(f"数据库更新成功: {digital_human_id}")
                except Exception as db_error:
                    db.rollback()
                    logger.error(f"数据库更新失败: {str(db_error)}", exc_info=True)
                finally:
                    db.close()
            except Exception as outer_db_error:
                logger.error(f"数据库连接失败: {str(outer_db_error)}")
                logger.exception("数据库连接详细错误")
            
            return {
                "success": True,
                "task_id": task_id,
                "digital_human_id": digital_human_id,
                "video_path": video_path,
                "video_url": video_url,
                "audio_path": audio_path,
                "audio_url": f"/static/audio/{os.path.basename(audio_path)}" if audio_path else None,
                "model_type": model_type,
                "result_url": video_url
            }
            
        except Exception as e:
            error_msg = f"生成数字人失败: {str(e)}"
            logger.error(error_msg)
            logger.exception("详细错误信息")
            
            # 更新进度为失败 - 修复调用，移除message参数
            await tracker.update_task(
                task_id=task_id,
                status="failed",
                progress=0
            )
            
            return {
                "success": False,
                "task_id": task_id,
                "digital_human_id": digital_human_id,
                "error": error_msg
            }

    async def _mock_generate_digital_human(self,
                                        task_id: str,
                                        digital_human_id: str,
                                        image_path: str,
                                        model_type: str = "default",
                                        voice_id: Optional[str] = None,
                                        welcome_text: Optional[str] = None,
                                        gender: Optional[str] = None,
                                        progress_tracker: Optional[Any] = None,
                                        name: Optional[str] = None,
                                        style: Optional[str] = None,
                                        tags: Optional[List[str]] = None,
                                        appearance: Optional[Dict[str, Any]] = None,
                                        voice_settings: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        模拟生成数字人（用于测试）
        
        Args:
            task_id: 任务ID
            digital_human_id: 数字人ID
            image_path: 输入图像路径
            model_type: 模型类型
            voice_id: 语音ID
            welcome_text: 欢迎文本
            gender: 性别 (male/female/other)
            progress_tracker: 进度跟踪器
            name: 数字人名称
            style: 数字人风格
            tags: 标签列表
            appearance: 外观设置
            voice_settings: 语音设置
            
        Returns:
            Dict[str, Any]: 模拟生成结果
        """
        logger.info(f"开始模拟生成数字人: {digital_human_id}，性别: {gender or '未指定'}")
        
        # 设置进度跟踪器，优先使用传入的，否则使用服务的默认跟踪器
        tracker = progress_tracker or self.progress_tracker
        
        # 更新进度 - 修复调用，移除message参数
        await tracker.update_task(
            task_id=task_id,
            status="processing",
            progress=10
        )
        # 添加日志消息
        if hasattr(tracker, 'add_log') and callable(getattr(tracker, 'add_log')):
            await tracker.add_log(task_id, "info", "开始模拟生成数字人")
        
        # 模拟处理媒体文件
        await asyncio.sleep(1)
        # 更新进度 - 修复调用，移除message参数
        await tracker.update_task(
            task_id=task_id,
            progress=30
        )
        # 添加日志消息
        if hasattr(tracker, 'add_log') and callable(getattr(tracker, 'add_log')):
            await tracker.add_log(task_id, "info", "模拟处理媒体文件")
        
        # 模拟生成语音
        await asyncio.sleep(1.5)
        # 更新进度 - 修复调用，移除message参数
        await tracker.update_task(
            task_id=task_id,
            progress=50
        )
        # 添加日志消息
        if hasattr(tracker, 'add_log') and callable(getattr(tracker, 'add_log')):
            await tracker.add_log(task_id, "info", "模拟生成语音")
        
        # 模拟生成视频
        await asyncio.sleep(2)
        # 更新进度 - 修复调用，移除message参数
        await tracker.update_task(
            task_id=task_id,
            progress=80
        )
        # 添加日志消息
        if hasattr(tracker, 'add_log') and callable(getattr(tracker, 'add_log')):
            await tracker.add_log(task_id, "info", "模拟生成视频")
        
        # 模拟完成处理
        await asyncio.sleep(0.5)
        
        # 生成模拟结果路径
        mock_id = uuid.uuid4().hex[:8]
        video_path = f"mock_{mock_id}.mp4"
        video_url = f"/api/digital-human/media/video/mock_{mock_id}"
        audio_path = f"mock_{mock_id}.mp3"
        audio_url = f"/api/digital-human/media/audio/mock_{mock_id}"
        
        # 更新进度为完成 - 修复调用，移除message参数
        await tracker.update_task(
            task_id=task_id,
            status="completed",
            progress=100,
            result_url=video_url,
            audio_url=audio_url
        )
        # 添加日志消息
        if hasattr(tracker, 'add_log') and callable(getattr(tracker, 'add_log')):
            await tracker.add_log(task_id, "info", "模拟数字人生成完成")
        
        logger.info(f"模拟数字人生成完成: {digital_human_id}")
        
        return {
            "success": True,
            "task_id": task_id,
            "digital_human_id": digital_human_id,
            "video_path": video_path,
            "video_url": video_url,
            "audio_path": audio_path,
            "audio_url": audio_url,
            "model_type": model_type,
            "name": name,
            "style": style,
            "tags": tags,
            "appearance": appearance,
            "voice_settings": voice_settings
        }
        
    async def create_generation_task(self, 
                                  task_id: str, 
                                  digital_human_id: str,
                                  media_file_path: str,
                                  voice_id: Optional[str] = None,
                                  welcome_text: Optional[str] = None,
                                  priority: TaskPriority = TaskPriority.NORMAL) -> Dict[str, Any]:
        """
        创建数字人生成任务
        
        Args:
            task_id: 任务ID
            digital_human_id: 数字人ID
            media_file_path: 媒体文件路径
            voice_id: 语音ID（可选）
            welcome_text: 欢迎文本（可选）
            priority: 任务优先级
        
        Returns:
            Dict[str, Any]: 任务创建结果
        """
        # 确保服务已初始化
        await self.initialize()
        
        # 验证文件路径
        if not os.path.exists(media_file_path):
            error_msg = f"媒体文件不存在: {media_file_path}"
            logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg
            }
            
        # 创建任务数据
        task_data = {
            "task_id": task_id,
            "digital_human_id": digital_human_id,
            "media_file_path": media_file_path,
            "voice_id": voice_id,
            "welcome_text": welcome_text,
            "start_time": time.time()
        }
        
        try:
            # 初始化任务状态 - 修复调用，移除message参数
            await self.progress_tracker.update_task(
                task_id=task_id,
                status="queued",
                progress=0
            )
            
            # 添加到任务队列
            await self.human_generator.add_generation_task(task_id, task_data, priority)
            
            logger.info(f"创建数字人生成任务成功: {task_id}, 数字人ID: {digital_human_id}")
            
            return {
                "success": True,
                "task_id": task_id,
                "message": "任务已创建并加入队列"
            }
        except Exception as e:
            error_msg = f"创建数字人生成任务失败: {str(e)}"
            logger.error(error_msg)
            logger.exception("详细错误信息")
            
            # 更新任务状态为失败 - 修复调用，移除message参数
            await self.progress_tracker.update_task(
                task_id=task_id,
                status="failed",
                progress=0
            )
            
            return {
                "success": False,
                "error": error_msg
            }
            
    async def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """
        获取任务状态
        
        Args:
            task_id: 任务ID
        
        Returns:
            Dict[str, Any]: 任务状态信息
        """
        try:
            # 从进度跟踪器获取任务状态
            task_status = self.progress_tracker.get_task_status(task_id)
            
            if not task_status:
                # 尝试从HumanGenerator获取
                task_status = await self.human_generator.get_task_status(task_id)
                
            if task_status:
                # 估算剩余时间
                time_remaining = None
                if "progress" in task_status and task_status["progress"] > 0 and task_status["progress"] < 100:
                    time_remaining = self._estimate_remaining_time(task_status)
                
                return {
                    "success": True,
                    "task_id": task_id,
                    "status": task_status.get("status", "unknown"),
                    "progress": task_status.get("progress", 0),
                    "message": task_status.get("message", ""),
                    "result_url": task_status.get("result_url"),
                    "audio_url": task_status.get("audio_url"),
                    "time_remaining": time_remaining,
                    "updated_at": task_status.get("updated_at", datetime.now().isoformat())
                }
            else:
                return {
                    "success": False,
                    "error": f"任务 {task_id} 不存在或已过期"
                }
        except Exception as e:
            error_msg = f"获取任务状态失败: {str(e)}"
            logger.error(error_msg)
            logger.exception("详细错误信息")
            
            return {
                "success": False,
                "error": error_msg
            }
            
    async def cancel_task(self, task_id: str) -> Dict[str, Any]:
        """
        取消任务
        
        Args:
            task_id: 任务ID
        
        Returns:
            Dict[str, Any]: 取消结果
        """
        try:
            # 确保服务已初始化
            await self.initialize()
            
            # 取消任务
            await self.human_generator.cancel_task(task_id)
            
            # 更新任务状态 - 修复调用，移除message参数
            await self.progress_tracker.update_task(
                task_id=task_id,
                status="cancelled",
                progress=0
            )
            
            logger.info(f"任务 {task_id} 已取消")
            
            return {
                "success": True,
                "message": f"任务 {task_id} 已取消"
            }
        except Exception as e:
            error_msg = f"取消任务失败: {str(e)}"
            logger.error(error_msg)
            logger.exception("详细错误信息")
            
            return {
                "success": False,
                "error": error_msg
            }
            
    async def process_generation_direct(
        self,
        task_id: str,
        digital_human_id: str,
        image_path: str,
        voice_id: str,
        welcome_text: str,
        gender: str = None,
        progress_tracker = None,
        name: str = None,
        style: str = None,
        tags: List[str] = None,
        appearance: Dict[str, Any] = None,
        voice_settings: Dict[str, Any] = None
    ):
        """
        直接处理数字人生成任务

        Args:
            task_id (str): 任务ID
            digital_human_id (str): 数字人ID
            image_path (str): 图片路径
            voice_id (str): 语音ID
            welcome_text (str): 欢迎文本
            gender (str, optional): 性别
            progress_tracker (Any, optional): 进度跟踪器
            name (str, optional): 数字人名称
            style (str, optional): 风格
            tags (List[str], optional): 标签列表
            appearance (Dict[str, Any], optional): 外观设置
            voice_settings (Dict[str, Any], optional): 语音设置

        Returns:
            Dict: 包含处理结果的字典
        """
        # 初始化结果字典
        result = {
            "success": False,
            "message": "任务初始化",
            "video_url": None,
            "audio_url": None
        }
        
        # 如果未提供progress_tracker，则创建默认值
        if progress_tracker is None:
            try:
                progress_tracker = await get_progress_tracker()
            except Exception as e:
                self.logger.error(f"获取默认进度跟踪器失败: {str(e)}")
                result["message"] = f"无法初始化进度跟踪器: {str(e)}"
                return result
        
        # 创建进度适配器
        progress_adapter = ProgressTrackerAdapter(progress_tracker)
        
        try:
            # 更新任务状态为"处理中"
            await progress_adapter.update_task(
                task_id=task_id,
                status="processing",
                progress=10
            )
            
            # 记录附加参数
            additional_params = {}
            if name is not None:
                self.logger.info(f"附加参数 - 名称: {name}")
                additional_params["name"] = name
                
            if style is not None:
                self.logger.info(f"附加参数 - 风格: {style}")
                additional_params["style"] = style
                
            if tags is not None:
                self.logger.info(f"附加参数 - 标签: {tags}")
                additional_params["tags"] = tags
                
            if appearance is not None:
                self.logger.info(f"附加参数 - 外观: {json.dumps(appearance, ensure_ascii=False)}")
                additional_params["appearance"] = appearance
                
            if voice_settings is not None:
                self.logger.info(f"附加参数 - 语音设置: {json.dumps(voice_settings, ensure_ascii=False)}")
                additional_params["voice_settings"] = voice_settings

            # 更新任务状态并记录开始生成视频
            await progress_adapter.update_task(
                task_id=task_id,
                progress=20
            )
            
            await progress_adapter.update_task_status(
                task_id=task_id,
                status="processing",
                message="开始生成数字人视频..."
            )
            
            # 调用视频生成服务
            generator = HumanVideoGeneratorService()
            generation_result = await generator.generate_video(
                task_id=task_id,
                digital_human_id=digital_human_id,
                image_path=image_path,
                voice_id=voice_id,
                welcome_text=welcome_text,
                gender=gender,
                **additional_params
            )
            
            # 处理生成结果
            if generation_result.get("success", False):
                # 获取视频和音频URL
                video_url = generation_result.get("video_url")
                audio_url = generation_result.get("audio_url")
                
                # 更新结果
                result["success"] = True
                result["message"] = "数字人生成成功"
                result["video_url"] = video_url
                result["audio_url"] = audio_url
                
                # 更新任务状态
                await progress_adapter.update_task(
                    task_id=task_id,
                    status="completed",
                    progress=100,
                    result_url=video_url,
                    audio_url=audio_url
                )
                
                await progress_adapter.update_task_status(
                    task_id=task_id,
                    status="completed",
                    message="数字人生成完成"
                )
            else:
                # 生成失败
                error_message = generation_result.get("message", "生成数字人视频失败，无详细信息")
                self.logger.error(f"生成数字人视频失败: {error_message}")
                
                result["message"] = error_message
                
                # 更新任务状态
                await progress_adapter.update_task(
                    task_id=task_id,
                    status="failed",
                    progress=100
                )
                
                await progress_adapter.update_task_status(
                    task_id=task_id,
                    status="failed",
                    message=error_message
                )
                
        except Exception as e:
            # 捕获处理过程中的任何异常
            error_message = f"处理数字人生成任务时出错: {str(e)}"
            self.logger.error(error_message, exc_info=True)
            
            result["message"] = error_message
            
            # 更新任务状态为失败
            try:
                await progress_adapter.update_task(
                    task_id=task_id,
                    status="failed",
                    progress=100
                )
                
                await progress_adapter.update_task_status(
                    task_id=task_id, 
                    status="failed",
                    message=error_message
                )
            except Exception as update_error:
                self.logger.error(f"更新失败状态时出错: {str(update_error)}")
        
        # 返回结果
        return result

    def _estimate_remaining_time(self, task_status: Dict[str, Any]) -> int:
        """
        估计任务剩余时间（秒）
        
        Args:
            task_status: 任务状态
            
        Returns:
            int: 估计剩余时间（秒）
        """
        progress = task_status.get("progress", 0)
        if progress <= 0 or progress >= 100:
            return 0
            
        # 基于当前进度估算
        # 假设平均每个百分比进度需要1-3秒
        base_time_per_percent = 2  # 平均每个百分比进度需要2秒
        
        # 计算剩余百分比
        remaining_percent = 100 - progress
        
        # 估算剩余时间
        estimated_time = remaining_percent * base_time_per_percent
        
        # 再加上一些缓冲时间
        buffer_time = 5 if progress > 80 else 15
        
        return int(estimated_time + buffer_time)
        
    def get_performance_metrics(self) -> Dict[str, Any]:
        """
        获取性能指标
        
        Returns:
            Dict[str, Any]: 性能指标数据
        """
        return self.human_generator.get_performance_metrics()
        
    def get_queue_status(self) -> Dict[str, int]:
        """
        获取队列状态
        
        Returns:
            Dict[str, int]: 队列状态数据
        """
        return self.human_generator.get_queue_status()


# 单例模式
_digital_human_generation_service_instance = None

def get_digital_human_generation_service() -> DigitalHumanGenerationService:
    """
    获取数字人生成服务实例（单例模式）
    
    Returns:
        DigitalHumanGenerationService: 数字人生成服务实例
    """
    global _digital_human_generation_service_instance
    
    if _digital_human_generation_service_instance is None:
        _digital_human_generation_service_instance = DigitalHumanGenerationService()
        
    return _digital_human_generation_service_instance 