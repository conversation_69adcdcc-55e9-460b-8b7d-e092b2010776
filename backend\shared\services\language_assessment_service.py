"""
语言学习评估服务

提供语言学习相关的评估、纠正和反馈功能
"""

import os
import re
import json
import logging
from typing import Dict, List, Optional, Tuple, Any
import random

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class LanguageAssessmentService:
    """语言学习评估服务"""
    
    def __init__(self):
        """初始化语言评估服务"""
        self.scenarios_data = {}
        self.load_scenarios_data()
        logger.info("语言评估服务已初始化")
        
    def load_scenarios_data(self):
        """加载场景数据"""
        try:
            data_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "data", "language_learning")
            
            # 加载商务英语场景
            business_english_path = os.path.join(data_dir, "business_english_scenarios.json")
            if os.path.exists(business_english_path):
                with open(business_english_path, 'r', encoding='utf-8') as f:
                    self.scenarios_data["business_english"] = json.load(f)
                logger.info(f"已加载商务英语场景数据: {len(self.scenarios_data['business_english']['scenarios'])}个场景")
            else:
                logger.warning(f"商务英语场景文件不存在: {business_english_path}")
                
        except Exception as e:
            logger.error(f"加载场景数据失败: {str(e)}")
            
    def get_scenario_data(self, language: str, scenario_id: str) -> Optional[Dict[str, Any]]:
        """
        获取指定语言和场景的数据
        
        Args:
            language: 语言代码 (如 'english')
            scenario_id: 场景ID (如 'meeting')
            
        Returns:
            场景数据字典，如果不存在则返回None
        """
        if language == "english":
            data = self.scenarios_data.get("business_english")
            if data:
                for scenario in data.get("scenarios", []):
                    if scenario.get("id") == scenario_id:
                        return scenario
        return None
    
    def get_accent_info(self, language: str, accent: str) -> Optional[Dict[str, Any]]:
        """
        获取指定语言和口音的信息
        
        Args:
            language: 语言代码 (如 'english')
            accent: 口音代码 (如 'american')
            
        Returns:
            口音信息字典，如果不存在则返回None
        """
        if language == "english":
            data = self.scenarios_data.get("business_english")
            if data:
                return data.get("accents", {}).get(accent)
        return None
    
    def get_level_info(self, language: str, level: str) -> Optional[Dict[str, Any]]:
        """
        获取指定语言级别的信息
        
        Args:
            language: 语言代码 (如 'english')
            level: 级别代码 (如 'beginner')
            
        Returns:
            级别信息字典，如果不存在则返回None
        """
        if language == "english":
            data = self.scenarios_data.get("business_english")
            if data:
                return data.get("language_levels", {}).get(level)
        return None
    
    def get_vocabulary_for_scenario(self, language: str, scenario_id: str, level: str) -> List[str]:
        """
        获取特定场景和级别的词汇列表
        
        Args:
            language: 语言代码 (如 'english')
            scenario_id: 场景ID (如 'meeting')
            level: 级别代码 (如 'beginner')
            
        Returns:
            词汇列表
        """
        scenario = self.get_scenario_data(language, scenario_id)
        if scenario:
            return scenario.get("difficulty_levels", {}).get(level, {}).get("vocabulary", [])
        return []
    
    def get_phrases_for_scenario(self, language: str, scenario_id: str, level: str) -> List[str]:
        """
        获取特定场景和级别的短语列表
        
        Args:
            language: 语言代码 (如 'english')
            scenario_id: 场景ID (如 'meeting')
            level: 级别代码 (如 'beginner')
            
        Returns:
            短语列表
        """
        scenario = self.get_scenario_data(language, scenario_id)
        if scenario:
            return scenario.get("difficulty_levels", {}).get(level, {}).get("phrases", [])
        return []
    
    def extract_assessment_from_ai_response(self, response_text: str) -> Dict[str, Any]:
        """
        从AI回复中提取评估部分
        
        Args:
            response_text: AI生成的回复文本
        
        Returns:
            包含评估信息的字典
        """
        # 默认评估结果
        assessment = {
            "accuracy": 0,
            "fluency": 0,
            "grammar": 0,
            "suggestions": []
        }
        
        try:
            # 查找评估部分的标记
            evaluation_pattern = r"###\s*评估|###\s*Evaluation|评估[:：]"
            match = re.search(evaluation_pattern, response_text, re.IGNORECASE)
            
            if not match:
                logger.warning("回复中未找到评估部分")
                return assessment
                
            # 提取评估部分
            eval_text = response_text[match.start():]
            
            # 提取评分
            accuracy_match = re.search(r"准确度[:：]\s*(\d+)|accuracy[:：]?\s*(\d+)", eval_text, re.IGNORECASE)
            if accuracy_match:
                accuracy = accuracy_match.group(1) or accuracy_match.group(2)
                assessment["accuracy"] = int(accuracy)
                
            fluency_match = re.search(r"流利度[:：]\s*(\d+)|fluency[:：]?\s*(\d+)", eval_text, re.IGNORECASE)
            if fluency_match:
                fluency = fluency_match.group(1) or fluency_match.group(2)
                assessment["fluency"] = int(fluency)
                
            grammar_match = re.search(r"语法[:：]\s*(\d+)|grammar[:：]?\s*(\d+)", eval_text, re.IGNORECASE)
            if grammar_match:
                grammar = grammar_match.group(1) or grammar_match.group(2)
                assessment["grammar"] = int(grammar)
                
            # 提取建议
            suggestions_pattern = r"建议[:：](.*?)(?=\n\n|\Z)|suggestions[:：]?(.*?)(?=\n\n|\Z)"
            suggestions_match = re.search(suggestions_pattern, eval_text, re.IGNORECASE | re.DOTALL)
            
            if suggestions_match:
                suggestions_text = suggestions_match.group(1) or suggestions_match.group(2)
                
                # 尝试按项解析
                suggestions_items = re.findall(r"[-•*]\s*(.*?)(?=\n[-•*]|\n\n|\Z)", suggestions_text, re.DOTALL)
                
                if suggestions_items:
                    assessment["suggestions"] = [item.strip() for item in suggestions_items]
                else:
                    # 如果没有项标记，则整体作为一条建议
                    assessment["suggestions"] = [suggestions_text.strip()]
            
        except Exception as e:
            logger.error(f"提取评估信息时出错: {str(e)}")
            
        return assessment
    
    def split_response_and_assessment(self, response_text: str) -> Tuple[str, Dict[str, Any]]:
        """
        将AI回复分割为回复内容和评估两部分
        
        Args:
            response_text: AI生成的回复文本
            
        Returns:
            元组 (回复内容, 评估信息字典)
        """
        # 查找评估部分的标记
        evaluation_pattern = r"###\s*评估|###\s*Evaluation|评估[:：]"
        match = re.search(evaluation_pattern, response_text, re.IGNORECASE)
        
        if match:
            # 分割回复
            response_content = response_text[:match.start()].strip()
            assessment = self.extract_assessment_from_ai_response(response_text)
            return response_content, assessment
        
        # 如果没有找到评估部分，返回原始内容和空评估
        return response_text, {
            "accuracy": 0,
            "fluency": 0,
            "grammar": 0,
            "suggestions": []
        }
    
    def generate_practice_suggestions(self, language: str, scenario_id: str, level: str, accent: str) -> Dict[str, Any]:
        """
        生成练习建议
        
        Args:
            language: 语言代码
            scenario_id: 场景ID
            level: 级别代码
            accent: 口音代码
            
        Returns:
            包含练习建议的字典
        """
        # 获取相关数据
        vocabulary = self.get_vocabulary_for_scenario(language, scenario_id, level)
        phrases = self.get_phrases_for_scenario(language, scenario_id, level)
        
        # 选择随机词汇和短语作为建议
        suggested_vocabulary = random.sample(vocabulary, min(5, len(vocabulary)))
        suggested_phrases = random.sample(phrases, min(3, len(phrases)))
        
        # 获取场景信息
        scenario = self.get_scenario_data(language, scenario_id)
        scenario_title = scenario.get("title", "") if scenario else ""
        
        # 获取口音信息
        accent_info = self.get_accent_info(language, accent)
        accent_name = accent_info.get("name", "") if accent_info else ""
        
        return {
            "scenario": scenario_title,
            "level": level,
            "accent": accent_name,
            "vocabulary": suggested_vocabulary,
            "phrases": suggested_phrases,
            "tips": [
                f"尝试使用{accent_name}特有的表达方式和词汇",
                f"多听{accent_name}的录音来熟悉发音特点",
                "先练习单词发音，再练习整句表达",
                "尝试用学到的词汇造句并大声朗读"
            ]
        }

# 单例模式
_language_assessment_service = None

def get_language_assessment_service() -> LanguageAssessmentService:
    """获取语言评估服务实例（单例模式）"""
    global _language_assessment_service
    if _language_assessment_service is None:
        _language_assessment_service = LanguageAssessmentService()
    return _language_assessment_service 