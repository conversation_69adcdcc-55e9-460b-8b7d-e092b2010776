#!/usr/bin/env python3
"""
迁移 Wanx 2.1 模型到后端统一模型目录
"""
import os
import shutil
import sys
from pathlib import Path

def migrate_wan_models():
    """迁移 Wanx 2.1 模型"""
    
    # 源路径（根目录的模型）
    source_path = Path(__file__).parent.parent.parent / "data" / "models" / "wan"
    
    # 目标路径（后端模型目录）
    target_path = Path(__file__).parent.parent / "storage" / "models" / "video_generation" / "wan"
    
    print(f"源路径: {source_path}")
    print(f"目标路径: {target_path}")
    
    if not source_path.exists():
        print(f"❌ 源路径不存在: {source_path}")
        return False
    
    # 创建目标目录
    target_path.mkdir(parents=True, exist_ok=True)
    
    try:
        # 创建符号链接而不是复制（节省磁盘空间）
        if (source_path / "Wan2.1").exists():
            target_wan_path = target_path / "Wan2.1"
            if target_wan_path.exists():
                print("🔄 目标目录已存在，正在更新...")
                if target_wan_path.is_symlink():
                    target_wan_path.unlink()
                else:
                    shutil.rmtree(target_wan_path)

            print("🔗 正在创建 Wanx 2.1 模型符号链接...")
            target_wan_path.symlink_to(source_path / "Wan2.1", target_is_directory=True)
            print("✅ Wanx 2.1 模型符号链接创建完成")
        
        # 创建符号链接（如果支持）
        try:
            symlink_path = target_path / "current"
            if symlink_path.exists():
                symlink_path.unlink()
            symlink_path.symlink_to("Wan2.1")
            print("🔗 创建符号链接完成")
        except OSError:
            print("⚠️ 无法创建符号链接，跳过")
        
        # 创建配置文件
        config_content = f"""# Wanx 2.1 模型配置
MODEL_PATH = "{target_wan_path.absolute()}"
MODELS_AVAILABLE = ["t2v-1.3B", "t2v-14B", "i2v-14B"]
OUTPUT_DIR = "{target_path / 'outputs'}"
TEMP_DIR = "{target_path / 'temp'}"
"""
        
        with open(target_path / "config.py", "w", encoding="utf-8") as f:
            f.write(config_content)
        
        # 创建输出和临时目录
        (target_path / "outputs").mkdir(exist_ok=True)
        (target_path / "temp").mkdir(exist_ok=True)
        
        print("✅ 模型迁移完成！")
        print(f"📁 模型位置: {target_wan_path}")
        print(f"📁 输出目录: {target_path / 'outputs'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 迁移失败: {e}")
        return False

if __name__ == "__main__":
    success = migrate_wan_models()
    sys.exit(0 if success else 1)
