#!/usr/bin/env python3
"""
测试前端进度查询功能
"""
import requests
import json
import time

def test_task_creation():
    """创建一个测试任务"""
    print("🧪 创建测试任务")
    print("=" * 50)
    
    params = {
        "prompt": "测试前端进度查询",
        "model": "t2v-1.3B",
        "duration": 5,
        "resolution": "768x512",
        "fps": 24,
        "guidance_scale": 7.5,
        "num_inference_steps": 20
    }
    
    try:
        response = requests.post(
            "http://localhost:8000/api/v1/video/text-to-video",
            json=params,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            task_id = result.get("task_id")
            print(f"✅ 任务创建成功: {task_id}")
            return task_id
        else:
            print(f"❌ 任务创建失败: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return None

def test_status_query(task_id):
    """测试状态查询"""
    print(f"\n🔍 测试状态查询: {task_id}")
    print("=" * 50)
    
    try:
        response = requests.get(f"http://localhost:8000/api/v1/video/status/{task_id}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 状态查询成功")
            print(f"📊 状态: {result.get('status')}")
            print(f"📊 进度: {result.get('progress')}%")
            print(f"📊 消息: {result.get('message', 'N/A')}")
            print(f"📊 任务类型: {result.get('task_type')}/{result.get('task_subtype')}")
            return result
        else:
            print(f"❌ 状态查询失败: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 状态查询异常: {e}")
        return None

def simulate_task_progress(task_id):
    """模拟任务进度更新（用于测试前端轮询）"""
    print(f"\n🎭 模拟任务进度更新")
    print("=" * 50)
    
    try:
        from app.core.task_manager import task_manager
        
        # 模拟任务开始
        task_manager.update_task(
            task_id,
            status='processing',
            progress=10,
            message='正在初始化模型...'
        )
        print("📊 更新进度: 10% - 正在初始化模型...")
        
        time.sleep(2)
        
        # 模拟进度更新
        task_manager.update_task(
            task_id,
            progress=50,
            message='正在生成视频帧...'
        )
        print("📊 更新进度: 50% - 正在生成视频帧...")
        
        time.sleep(2)
        
        # 模拟任务完成
        task_manager.update_task(
            task_id,
            status='completed',
            progress=100,
            message='视频生成完成',
            output_data={'video_url': f'/api/v1/video/download/test_{task_id[:8]}.mp4'}
        )
        print("📊 更新进度: 100% - 视频生成完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 模拟进度更新失败: {e}")
        return False

def test_progress_polling(task_id):
    """测试进度轮询"""
    print(f"\n⏳ 测试进度轮询")
    print("=" * 50)
    
    max_polls = 10
    for i in range(max_polls):
        try:
            response = requests.get(f"http://localhost:8000/api/v1/video/status/{task_id}")
            
            if response.status_code == 200:
                result = response.json()
                status = result.get('status')
                progress = result.get('progress', 0)
                message = result.get('message', 'N/A')
                
                print(f"📊 轮询 {i+1}: {status} - {progress}% - {message}")
                
                if status == 'completed':
                    video_url = result.get('video_url')
                    print(f"🎉 任务完成! 视频URL: {video_url}")
                    return True
                elif status == 'failed':
                    error = result.get('error', 'N/A')
                    print(f"❌ 任务失败: {error}")
                    return False
                
            time.sleep(1)
            
        except Exception as e:
            print(f"❌ 轮询异常: {e}")
    
    print(f"⏰ 轮询超时")
    return False

def main():
    """主函数"""
    print("🧪 前端进度查询功能测试")
    print("=" * 60)
    
    # 创建测试任务
    task_id = test_task_creation()
    if not task_id:
        return
    
    # 测试初始状态查询
    initial_status = test_status_query(task_id)
    if not initial_status:
        return
    
    # 在后台模拟任务进度更新
    print(f"\n🚀 启动后台进度模拟...")
    import threading
    
    def background_progress():
        time.sleep(3)  # 等待3秒后开始更新
        simulate_task_progress(task_id)
    
    progress_thread = threading.Thread(target=background_progress)
    progress_thread.start()
    
    # 测试进度轮询
    success = test_progress_polling(task_id)
    
    # 等待后台线程完成
    progress_thread.join()
    
    # 总结
    print(f"\n📊 测试总结:")
    print(f"✅ 任务创建: 成功")
    print(f"✅ 状态查询: 成功")
    print(f"{'✅' if success else '❌'} 进度轮询: {'成功' if success else '失败'}")
    
    if success:
        print(f"\n🎉 前端进度查询功能测试通过！")
        print(f"💡 现在前端应该能正常轮询任务进度")
    else:
        print(f"\n⚠️ 进度轮询测试失败")

if __name__ == "__main__":
    import sys
    import os
    from pathlib import Path
    from dotenv import load_dotenv
    
    # 添加后端路径
    backend_dir = Path(__file__).parent.parent
    sys.path.append(str(backend_dir))
    
    # 加载环境变量
    env_path = backend_dir / ".env"
    load_dotenv(env_path)
    
    main()
