"""
Create terminology tables script
"""

import sys
import os
import logging
import psycopg2
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Database connection string - updated with correct URL
DATABASE_URL = "postgresql://postgres:langpro8@localhost:5432/ai_platform"

def run_migration():
    """Execute database migration to add terminology tables"""
    conn = None
    try:
        # Connect to database
        logger.info(f"Connecting to database: {DATABASE_URL}")
        conn = psycopg2.connect(DATABASE_URL)
        cursor = conn.cursor()
        
        # Set autocommit to False
        conn.autocommit = False
        
        try:
            # Create enum types
            logger.info("Creating enum types...")
            cursor.execute("""
                DO $$
                BEGIN
                    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'visibility_type') THEN
                        CREATE TYPE visibility_type AS ENUM ('team', 'private');
                    END IF;
                    
                    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'file_status') THEN
                        CREATE TYPE file_status AS ENUM ('pending', 'processing', 'completed', 'failed');
                    END IF;
                    
                    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'term_status') THEN
                        CREATE TYPE term_status AS ENUM ('draft', 'approved', 'rejected');
                    END IF;
                END
                $$;
            """)
            
            # Create mono corpus table
            logger.info("Creating mono corpus table...")
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS terminology_mono_corpus (
                    id SERIAL PRIMARY KEY,
                    name VARCHAR(255) NOT NULL,
                    language VARCHAR(10) NOT NULL,
                    description TEXT,
                    visibility visibility_type NOT NULL DEFAULT 'team',
                    user_id INTEGER REFERENCES users(id),
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                );
            """)
            
            # Create mono corpus files table
            logger.info("Creating mono corpus files table...")
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS terminology_mono_corpus_files (
                    id SERIAL PRIMARY KEY,
                    corpus_id INTEGER REFERENCES terminology_mono_corpus(id) ON DELETE CASCADE,
                    filename VARCHAR(255) NOT NULL,
                    original_filename VARCHAR(255) NOT NULL,
                    file_size INTEGER NOT NULL,
                    file_type VARCHAR(50) NOT NULL,
                    status file_status DEFAULT 'pending',
                    processed_entries INTEGER DEFAULT 0,
                    total_entries INTEGER DEFAULT 0,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                );
            """)
            
            # Create terms table
            logger.info("Creating terms table...")
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS terminology_terms (
                    id SERIAL PRIMARY KEY,
                    term_text VARCHAR(255) NOT NULL,
                    language VARCHAR(10) NOT NULL,
                    domain VARCHAR(100),
                    definition TEXT,
                    status term_status DEFAULT 'draft',
                    user_id INTEGER REFERENCES users(id),
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                );
            """)
            
            # Create mono corpus entries table
            logger.info("Creating mono corpus entries table...")
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS terminology_mono_corpus_entries (
                    id SERIAL PRIMARY KEY,
                    corpus_id INTEGER REFERENCES terminology_mono_corpus(id) ON DELETE CASCADE,
                    file_id INTEGER REFERENCES terminology_mono_corpus_files(id) ON DELETE CASCADE,
                    term_id INTEGER REFERENCES terminology_terms(id),
                    text TEXT NOT NULL,
                    context_before TEXT,
                    context_after TEXT,
                    line_number INTEGER,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                );
            """)
            
            # Create terminology data table
            logger.info("Creating terminology data table...")
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS terminology_data (
                    id SERIAL PRIMARY KEY,
                    term_id VARCHAR(36) UNIQUE,
                    user_id INTEGER REFERENCES users(id),
                    term VARCHAR(255) NOT NULL,
                    definition TEXT,
                    language VARCHAR(10) NOT NULL,
                    domain VARCHAR(100),
                    translations TEXT,
                    synonyms TEXT,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                );
            """)
            
            # Create indexes
            logger.info("Creating indexes...")
            cursor.execute("""
                CREATE INDEX IF NOT EXISTS idx_terminology_mono_corpus_language ON terminology_mono_corpus(language);
                CREATE INDEX IF NOT EXISTS idx_terminology_mono_corpus_user ON terminology_mono_corpus(user_id);
                CREATE INDEX IF NOT EXISTS idx_terminology_mono_corpus_files_corpus ON terminology_mono_corpus_files(corpus_id);
                CREATE INDEX IF NOT EXISTS idx_terminology_mono_corpus_files_status ON terminology_mono_corpus_files(status);
                CREATE INDEX IF NOT EXISTS idx_terminology_terms_language ON terminology_terms(language);
                CREATE INDEX IF NOT EXISTS idx_terminology_terms_domain ON terminology_terms(domain);
                CREATE INDEX IF NOT EXISTS idx_terminology_terms_status ON terminology_terms(status);
                CREATE INDEX IF NOT EXISTS idx_terminology_mono_corpus_entries_corpus ON terminology_mono_corpus_entries(corpus_id);
                CREATE INDEX IF NOT EXISTS idx_terminology_mono_corpus_entries_file ON terminology_mono_corpus_entries(file_id);
                CREATE INDEX IF NOT EXISTS idx_terminology_data_term ON terminology_data(term);
                CREATE INDEX IF NOT EXISTS idx_terminology_data_language ON terminology_data(language);
                CREATE INDEX IF NOT EXISTS idx_terminology_data_domain ON terminology_data(domain);
            """)
            
            # Commit transaction
            conn.commit()
            logger.info("Terminology tables created successfully")
            
        except Exception as e:
            # Rollback transaction
            conn.rollback()
            logger.error(f"Failed to create terminology tables: {str(e)}")
            raise
            
    except Exception as e:
        logger.error(f"Database connection failed: {str(e)}")
        raise
    finally:
        # Close connection
        if conn:
            conn.close()

if __name__ == "__main__":
    try:
        run_migration()
    except Exception as e:
        logger.error(f"Migration failed: {str(e)}")
        sys.exit(1) 