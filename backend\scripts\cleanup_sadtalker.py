"""
清理SadTalker相关代码和依赖的脚本
"""
import os
import shutil
import logging
from pathlib import Path

logger = logging.getLogger(__name__)

def cleanup_sadtalker():
    """清理SadTalker相关文件和代码"""
    try:
        base_dir = Path(__file__).parent.parent
        
        # 1. 删除SadTalker模型目录
        sadtalker_dir = base_dir / "local_models" / "SadTalker"
        if sadtalker_dir.exists():
            logger.info(f"删除SadTalker模型目录: {sadtalker_dir}")
            shutil.rmtree(sadtalker_dir)
            logger.info("SadTalker模型目录删除成功")
        
        # 2. 删除SadTalker服务文件
        sadtalker_service = base_dir / "services" / "sadtalker_service.py"
        if sadtalker_service.exists():
            logger.info(f"删除SadTalker服务文件: {sadtalker_service}")
            sadtalker_service.unlink()
            logger.info("SadTalker服务文件删除成功")
        
        # 3. 清理配置文件中的SadTalker引用
        config_files = [
            base_dir / "config" / "models.yaml",
            base_dir / "config" / "services.yaml",
            base_dir / "requirements.txt"
        ]
        
        for config_file in config_files:
            if config_file.exists():
                cleanup_config_file(config_file)
        
        # 4. 创建SadTalker替换说明文件
        create_replacement_notice(base_dir)
        
        logger.info("SadTalker清理完成")
        return True
        
    except Exception as e:
        logger.error(f"SadTalker清理失败: {e}")
        return False

def cleanup_config_file(config_file: Path):
    """清理配置文件中的SadTalker引用"""
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 移除SadTalker相关行
        lines = content.split('\n')
        cleaned_lines = []
        
        for line in lines:
            if 'sadtalker' not in line.lower() and 'librosa' not in line.lower():
                cleaned_lines.append(line)
            else:
                logger.info(f"从{config_file.name}中移除行: {line.strip()}")
        
        # 写回文件
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(cleaned_lines))
            
        logger.info(f"配置文件{config_file.name}清理完成")
        
    except Exception as e:
        logger.error(f"清理配置文件{config_file}失败: {e}")

def create_replacement_notice(base_dir: Path):
    """创建SadTalker替换说明文件"""
    notice_file = base_dir / "SADTALKER_REPLACEMENT.md"
    
    notice_content = """# SadTalker 替换说明

## 概述
SadTalker已被更先进的数字人生成技术替代。

## 新的数字人生成技术
- **LivePortrait**: 高质量的人像动画生成
- **MuseTalk**: 实时语音驱动的数字人
- **NextGenDigitalHumanService**: 集成多种先进模型的服务

## 迁移指南
1. 使用新的数字人API v2 (`/api/digital-human/generate`)
2. 所有数字人生成现在使用Celery异步任务
3. 支持更高质量的视频生成和更快的处理速度

## 技术优势
- 更高的视频质量
- 更快的生成速度
- 更好的唇形同步
- 支持更多的表情和动作
- 更稳定的服务架构

## 配置更新
所有SadTalker相关的配置已被移除，新的配置在：
- `config/next_gen_models.yaml`
- `services/next_gen_digital_human_service.py`

## 日期
清理时间: 2025-07-15
"""
    
    try:
        with open(notice_file, 'w', encoding='utf-8') as f:
            f.write(notice_content)
        logger.info(f"SadTalker替换说明文件创建成功: {notice_file}")
    except Exception as e:
        logger.error(f"创建替换说明文件失败: {e}")

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    cleanup_sadtalker()
