"""
文件处理工具模块
提供文件上传、保存和处理相关功能
"""

import os
import shutil
import logging
import aiofiles
from fastapi import UploadFile
from pathlib import Path
import re
import mimetypes
from typing import Union, Optional, List

logger = logging.getLogger(__name__)

async def save_upload_file(upload_file: UploadFile, destination_path: str) -> str:
    """
    保存上传的文件到指定路径
    
    Args:
        upload_file: FastAPI的UploadFile对象
        destination_path: 目标保存路径，包括文件名
        
    Returns:
        str: 保存的文件的完整路径
    """
    try:
        # 确保目标目录存在
        directory = os.path.dirname(destination_path)
        os.makedirs(directory, exist_ok=True)
        
        # 保存文件
        async with aiofiles.open(destination_path, 'wb') as out_file:
            content = await upload_file.read()
            await out_file.write(content)
        
        logger.info(f"文件已保存: {destination_path}")
        return destination_path
    except Exception as e:
        logger.error(f"保存上传文件时出错: {str(e)}")
        raise

async def ensure_directory(directory_path: str) -> str:
    """
    确保目录存在，如果不存在则创建
    
    Args:
        directory_path: 目录路径
        
    Returns:
        str: 确认的目录路径
    """
    try:
        os.makedirs(directory_path, exist_ok=True)
        return directory_path
    except Exception as e:
        logger.error(f"创建目录时出错: {str(e)}")
        raise

# 为兼容性添加ensure_dir作为ensure_directory的别名
async def ensure_dir(directory_path: str) -> str:
    """
    确保目录存在的别名函数，调用ensure_directory
    
    Args:
        directory_path: 目录路径
        
    Returns:
        str: 确认的目录路径
    """
    return await ensure_directory(directory_path)

def get_file_extension(filename: str) -> str:
    """
    获取文件扩展名
    
    Args:
        filename: 文件名
        
    Returns:
        str: 文件扩展名（带点，如 .mp3）
    """
    return os.path.splitext(filename)[1].lower()

def get_mime_type(file_path: Union[str, Path]) -> str:
    """获取文件的MIME类型"""
    mime_type, _ = mimetypes.guess_type(str(file_path))
    return mime_type or "application/octet-stream"

def is_valid_filename(filename: str) -> bool:
    """检查文件名是否有效"""
    return bool(re.match(r'^[a-zA-Z0-9_\-\. ]+$', filename))

def sanitize_filename(filename: str) -> str:
    """清理文件名，移除不安全的字符"""
    # 替换不安全的字符为下划线
    return re.sub(r'[^a-zA-Z0-9_\-\. ]', '_', filename)

def get_file_size(file_path: Union[str, Path]) -> int:
    """获取文件大小（字节）"""
    return os.path.getsize(file_path)

def get_file_size_formatted(file_path: Union[str, Path]) -> str:
    """获取格式化的文件大小"""
    size = get_file_size(file_path)
    for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
        if size < 1024 or unit == 'TB':
            return f"{size:.2f} {unit}"
        size /= 1024

def is_allowed_extension(filename: str, allowed_extensions: List[str]) -> bool:
    """检查文件扩展名是否在允许列表中"""
    return get_file_extension(filename) in allowed_extensions

def count_words(text: str) -> int:
    """计算文本中的单词数量
    
    对于中文，每个汉字算一个词
    对于英文，按空格分隔计算单词数
    """
    # 移除多余空白字符
    text = re.sub(r'\s+', ' ', text).strip()
    
    if not text:
        return 0
    
    # 计算英文单词（按空格分隔）
    english_words = len(re.findall(r'\b[a-zA-Z]+\b', text))
    
    # 计算中文字符
    chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', text))
    
    # 计算数字
    numbers = len(re.findall(r'\b\d+\b', text))
    
    return english_words + chinese_chars + numbers 