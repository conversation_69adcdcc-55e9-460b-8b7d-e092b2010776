import logging
import asyncio
import uuid
from typing import Dict, List, Set, Optional, Any, Union, Tuple
from datetime import datetime
import networkx as nx

from .progress_tracker import get_progress_tracker

# 配置日志器
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class WorkflowManager:
    """工作流管理器
    
    管理工作流的创建、执行和监控，支持任务依赖关系和DAG执行。
    """
    
    def __init__(self):
        """初始化工作流管理器"""
        self._workflow_templates: Dict[str, Dict[str, Any]] = {}
        self._workflow_instances: Dict[str, nx.DiGraph] = {}
        self._lock = asyncio.Lock()
        self._progress_tracker = get_progress_tracker()
        
        logger.info("工作流管理器初始化完成")
    
    async def create_workflow_template(
        self, 
        template_id: str, 
        name: str, 
        tasks: List[Dict[str, Any]], 
        dependencies: Dict[str, List[str]] = None,
        metadata: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """创建工作流模板
        
        Args:
            template_id: 模板唯一标识符
            name: 模板名称
            tasks: 任务定义列表，每个任务包含类型和参数
            dependencies: 任务ID到依赖任务ID列表的映射
            metadata: 模板元数据
            
        Returns:
            Dict: 创建的模板信息
        """
        async with self._lock:
            if template_id in self._workflow_templates:
                logger.warning(f"工作流模板ID已存在: {template_id}，返回现有模板")
                return self._workflow_templates[template_id]
            
            # 验证DAG
            if dependencies:
                try:
                    # 创建有向图
                    G = nx.DiGraph()
                    
                    # 添加所有任务节点
                    for task in tasks:
                        G.add_node(task["task_id"])
                    
                    # 添加依赖边
                    for task_id, deps in dependencies.items():
                        for dep_id in deps:
                            G.add_edge(dep_id, task_id)
                    
                    # 检查是否有环
                    if not nx.is_directed_acyclic_graph(G):
                        cycles = list(nx.simple_cycles(G))
                        raise ValueError(f"工作流存在循环依赖: {cycles}")
                except ImportError:
                    logger.warning("未安装networkx库，跳过DAG验证")
                except Exception as e:
                    logger.error(f"工作流依赖验证失败: {str(e)}")
                    raise ValueError(f"无效的工作流依赖: {str(e)}")
            
            # 创建工作流模板
            timestamp = datetime.now().isoformat()
            template = {
                "template_id": template_id,
                "name": name,
                "tasks": tasks,
                "dependencies": dependencies or {},
                "created_at": timestamp,
                "updated_at": timestamp,
                "metadata": metadata or {}
            }
            
            self._workflow_templates[template_id] = template
            logger.info(f"已创建工作流模板 {template_id}: {name}")
            return template
    
    async def get_workflow_template(self, template_id: str) -> Optional[Dict[str, Any]]:
        """获取工作流模板"""
        async with self._lock:
            if template_id not in self._workflow_templates:
                logger.warning(f"尝试获取不存在的工作流模板: {template_id}")
                return None
            
            return self._workflow_templates[template_id]
    
    async def list_workflow_templates(self) -> List[Dict[str, Any]]:
        """列出所有工作流模板"""
        async with self._lock:
            return list(self._workflow_templates.values())
    
    async def delete_workflow_template(self, template_id: str) -> bool:
        """删除工作流模板"""
        async with self._lock:
            if template_id not in self._workflow_templates:
                logger.warning(f"尝试删除不存在的工作流模板: {template_id}")
                return False
            
            del self._workflow_templates[template_id]
            logger.info(f"已删除工作流模板 {template_id}")
            return True
    
    async def instantiate_workflow(
        self, 
        template_id: str, 
        workflow_id: Optional[str] = None,
        params_override: Dict[str, Dict[str, Any]] = None
    ) -> Optional[Dict[str, Any]]:
        """从模板实例化工作流并开始执行
        
        Args:
            template_id: 模板ID
            workflow_id: 可选的工作流ID，如果不提供则自动生成
            params_override: 任务ID到参数覆盖的映射
            
        Returns:
            Dict: 创建的工作流信息，如果失败则返回None
        """
        async with self._lock:
            # 获取模板
            template = await self.get_workflow_template(template_id)
            if not template:
                logger.error(f"工作流模板不存在: {template_id}")
                return None
            
            # 生成工作流ID
            if not workflow_id:
                workflow_id = f"wf_{uuid.uuid4().hex}"
            
            # 创建工作流
            workflow = await self._progress_tracker.create_workflow(
                workflow_id=workflow_id,
                name=template["name"],
                description=f"从模板 {template_id} 创建",
                metadata={
                    "template_id": template_id,
                    "instantiated_at": datetime.now().isoformat()
                }
            )
            
            # 创建有向图表示任务依赖
            G = nx.DiGraph()
            
            # 记录已创建的任务
            created_tasks = {}
            
            # 创建任务
            for task_def in template["tasks"]:
                task_id = task_def["task_id"]
                task_type = task_def["type"]
                
                # 合并参数
                params = task_def.get("params", {}).copy()
                if params_override and task_id in params_override:
                    params.update(params_override[task_id])
                
                # 添加工作流ID到参数
                params["workflow_id"] = workflow_id
                
                # 添加节点到图
                G.add_node(task_id, task_type=task_type, params=params)
            
            # 添加依赖边
            if "dependencies" in template:
                for task_id, deps in template["dependencies"].items():
                    for dep_id in deps:
                        G.add_edge(dep_id, task_id)
            
            # 保存工作流图
            self._workflow_instances[workflow_id] = G
            
            # 获取没有依赖的任务（起始任务）
            start_tasks = [node for node in G.nodes() if G.in_degree(node) == 0]
            
            # 创建所有任务
            for task_id in G.nodes():
                node_data = G.nodes[task_id]
                task_type = node_data["task_type"]
                params = node_data["params"]
                
                # 获取任务依赖
                dependencies = list(G.predecessors(task_id)) if G.in_degree(task_id) > 0 else []
                
                # 创建任务
                task = await self._progress_tracker.create_task(
                    task_id=task_id,
                    task_type=task_type,
                    params=params,
                    dependencies=dependencies,
                    workflow_id=workflow_id
                )
                
                created_tasks[task_id] = task
            
            # 对于没有依赖的任务，设置状态为处理中以开始执行
            for task_id in start_tasks:
                await self._progress_tracker.update_task(task_id, status="processing")
            
            logger.info(f"已从模板 {template_id} 实例化工作流 {workflow_id}，创建了 {len(created_tasks)} 个任务")
            return workflow
    
    async def get_workflow_graph(self, workflow_id: str) -> Optional[Dict[str, Any]]:
        """获取工作流的图结构（节点和边）
        
        用于前端可视化工作流
        
        Args:
            workflow_id: 工作流ID
            
        Returns:
            Dict: 包含节点和边的图结构
        """
        async with self._lock:
            if workflow_id not in self._workflow_instances:
                logger.warning(f"尝试获取不存在的工作流图: {workflow_id}")
                
                # 尝试从进度跟踪器获取工作流信息
                workflow = await self._progress_tracker.get_workflow(workflow_id)
                if not workflow:
                    return None
                
                # 可能是从其他实例创建的工作流，尝试重建图
                G = nx.DiGraph()
                
                # 获取工作流中的所有任务
                tasks = await self._progress_tracker.list_tasks(workflow_id=workflow_id)
                
                # 添加节点
                for task in tasks:
                    G.add_node(
                        task["task_id"], 
                        task_type=task["type"], 
                        status=task["status"],
                        progress=task["progress"]
                    )
                
                # 尝试从任务依赖关系添加边
                for task in tasks:
                    task_id = task["task_id"]
                    if "dependencies" in task and task["dependencies"]:
                        for dep_id in task["dependencies"]:
                            if G.has_node(dep_id):
                                G.add_edge(dep_id, task_id)
                
                # 保存图以供将来使用
                self._workflow_instances[workflow_id] = G
            else:
                G = self._workflow_instances[workflow_id]
                
                # 更新节点状态和进度
                tasks = await self._progress_tracker.list_tasks(workflow_id=workflow_id)
                tasks_dict = {task["task_id"]: task for task in tasks}
                
                for node in G.nodes():
                    if node in tasks_dict:
                        task = tasks_dict[node]
                        G.nodes[node]["status"] = task["status"]
                        G.nodes[node]["progress"] = task["progress"]
            
            # 构建前端可用的图数据
            nodes = []
            for node, data in G.nodes(data=True):
                nodes.append({
                    "id": node,
                    "type": data.get("task_type", "unknown"),
                    "status": data.get("status", "unknown"),
                    "progress": data.get("progress", 0)
                })
            
            edges = []
            for u, v in G.edges():
                edges.append({
                    "source": u,
                    "target": v
                })
            
            return {
                "nodes": nodes,
                "edges": edges
            }
    
    async def cancel_workflow(self, workflow_id: str) -> bool:
        """取消工作流及其所有任务
        
        Args:
            workflow_id: 工作流ID
            
        Returns:
            bool: 操作是否成功
        """
        # 获取工作流
        workflow = await self._progress_tracker.get_workflow(workflow_id)
        if not workflow:
            logger.warning(f"尝试取消不存在的工作流: {workflow_id}")
            return False
        
        # 获取工作流中的所有任务
        tasks = await self._progress_tracker.list_tasks(workflow_id=workflow_id)
        
        # 取消所有未完成的任务
        for task in tasks:
            if task["status"] not in ["completed", "failed", "canceled"]:
                await self._progress_tracker.update_task(
                    task_id=task["task_id"],
                    status="canceled"
                )
        
        logger.info(f"已取消工作流 {workflow_id} 及其所有任务")
        return True
    
    async def retry_workflow(self, workflow_id: str, retry_failed_only: bool = True) -> bool:
        """重试工作流中的失败任务
        
        Args:
            workflow_id: 工作流ID
            retry_failed_only: 是否只重试失败的任务，如果为False则重试所有非完成任务
            
        Returns:
            bool: 操作是否成功
        """
        # 获取工作流
        workflow = await self._progress_tracker.get_workflow(workflow_id)
        if not workflow:
            logger.warning(f"尝试重试不存在的工作流: {workflow_id}")
            return False
        
        # 获取工作流图
        graph_data = await self.get_workflow_graph(workflow_id)
        if not graph_data:
            logger.error(f"无法获取工作流图: {workflow_id}")
            return False
        
        # 重建图
        G = nx.DiGraph()
        
        # 添加节点和边
        for node in graph_data["nodes"]:
            G.add_node(node["id"], status=node["status"], progress=node["progress"])
        
        for edge in graph_data["edges"]:
            G.add_edge(edge["source"], edge["target"])
        
        # 获取要重试的任务
        retry_tasks = []
        for node, data in G.nodes(data=True):
            # 根据策略确定要重试的任务
            if retry_failed_only and data["status"] == "failed":
                retry_tasks.append(node)
            elif not retry_failed_only and data["status"] in ["failed", "canceled"]:
                retry_tasks.append(node)
        
        if not retry_tasks:
            logger.info(f"工作流 {workflow_id} 没有需要重试的任务")
            return True
        
        # 获取重试任务的所有依赖任务的状态
        for task_id in retry_tasks:
            # 检查依赖
            deps = list(G.predecessors(task_id))
            all_deps_completed = True
            
            for dep_id in deps:
                dep_status = G.nodes[dep_id]["status"]
                if dep_status != "completed":
                    all_deps_completed = False
                    break
            
            # 重置任务状态
            status = "processing" if all_deps_completed else "waiting_dependencies"
            await self._progress_tracker.update_task(
                task_id=task_id,
                status=status,
                progress=0,
                error=None
            )
        
        logger.info(f"已重试工作流 {workflow_id} 中的 {len(retry_tasks)} 个任务")
        return True
    
    # 添加更多工作流管理方法...

# 全局工作流管理器单例
_workflow_manager = None

def get_workflow_manager() -> WorkflowManager:
    """获取工作流管理器单例"""
    global _workflow_manager
    if _workflow_manager is None:
        _workflow_manager = WorkflowManager()
    return _workflow_manager 