"""
后备TTS服务模块 - 当主TTS模型无法加载时提供基本功能
"""

import os
import logging
import tempfile
import hashlib
import shutil
import time
from typing import Dict, Any, Optional
import asyncio
from pathlib import Path
import uuid

# 尝试导入pyttsx3作为后备TTS引擎
try:
    import pyttsx3
    PYTTSX3_AVAILABLE = True
except ImportError:
    PYTTSX3_AVAILABLE = False

logger = logging.getLogger(__name__)

class FallbackTTSService:
    """
    后备TTS服务类，当主TTS服务无法加载模型时提供基本功能
    
    使用系统内置TTS功能或pyttsx3库提供基本的语音合成
    """
    
    def __init__(self, cache_dir=None):
        """
        初始化后备TTS服务
        
        Args:
            cache_dir: 缓存目录，默认为None使用临时目录
        """
        self.engine = None
        self.initialized = False
        
        # 设置缓存目录
        if cache_dir:
            self.cache_dir = cache_dir
        else:
            self.cache_dir = os.path.join(tempfile.gettempdir(), "fallback_tts_cache")
        
        # 确保缓存目录存在
        os.makedirs(self.cache_dir, exist_ok=True)
        
        # 语速设置
        self.rate_adjustment = 0.8  # 默认语速调整因子
        
        # 统计信息
        self.stats = {
            "total_requests": 0,
            "cache_hits": 0,
            "generation_time": 0,
            "start_time": time.time()
        }
    
    def initialize(self):
        """初始化TTS引擎"""
        if self.initialized:
            return True
            
        try:
            if PYTTSX3_AVAILABLE:
                logger.info("初始化pyttsx3后备TTS引擎")
                self.engine = pyttsx3.init()
                
                # 设置默认语速 (150是pyttsx3默认值，调整为稍慢)
                self.engine.setProperty('rate', 140)
                
                # 获取可用语音
                voices = self.engine.getProperty('voices')
                if voices:
                    # 寻找中文语音优先，否则使用第一个
                    chinese_voice = None
                    for voice in voices:
                        if "chinese" in voice.name.lower() or "zh" in voice.id.lower():
                            chinese_voice = voice
                            break
                    
                    if chinese_voice:
                        self.engine.setProperty('voice', chinese_voice.id)
                        logger.info(f"设置中文语音: {chinese_voice.name}")
                    else:
                        # 使用第一个语音
                        self.engine.setProperty('voice', voices[0].id)
                        logger.info(f"使用默认语音: {voices[0].name}")
                
                self.initialized = True
                logger.info("后备TTS引擎初始化成功")
                return True
            else:
                logger.warning("pyttsx3未安装，后备TTS服务将使用替代方法")
                self.initialized = True  # 即使没有pyttsx3，我们也标记为已初始化
                return True
                
        except Exception as e:
            logger.error(f"初始化后备TTS引擎失败: {e}")
            logger.info("将使用更基本的方法")
            self.initialized = True  # 即使失败，我们也标记为已初始化
            return False
    
    def get_cache_key(self, text: str, voice_id: str, speed: float) -> str:
        """生成缓存键"""
        # 创建唯一的缓存键
        key = f"{voice_id}_{speed}_{text}"
        return hashlib.md5(key.encode('utf-8')).hexdigest()
    
    def get_cached_audio(self, text: str, voice_id: str, speed: float) -> Optional[str]:
        """从缓存获取音频"""
        cache_key = self.get_cache_key(text, voice_id, speed)
        cache_path = os.path.join(self.cache_dir, f"{cache_key}.wav")
        
        if os.path.exists(cache_path):
            self.stats["cache_hits"] += 1
            return cache_path
        
        return None
    
    def _synthesize_with_pyttsx3(self, text: str, output_path: str, speed: float = 1.0):
        """使用pyttsx3生成语音"""
        if not self.engine:
            raise RuntimeError("pyttsx3引擎未初始化")
        
        # 调整语速
        current_rate = self.engine.getProperty('rate')
        adjusted_rate = int(current_rate / speed * self.rate_adjustment)
        self.engine.setProperty('rate', adjusted_rate)
        
        # 生成语音并保存
        self.engine.save_to_file(text, output_path)
        self.engine.runAndWait()
        
        # 恢复默认语速
        self.engine.setProperty('rate', current_rate)
        
        return os.path.exists(output_path)
    
    def _synthesize_with_google_tts(self, text: str, output_path: str, lang="zh"):
        """使用gTTS生成语音"""
        try:
            from gtts import gTTS
            # 设置gTTS超时和重试参数
            import urllib3
            # 增加连接和读取超时时间
            urllib3.util.timeout.Timeout(connect=15.0, read=60.0)
            
            # 设置gTTS代理（如果环境变量中有配置）
            proxies = {}
            if os.environ.get("HTTP_PROXY"):
                proxies["http"] = os.environ.get("HTTP_PROXY")
            if os.environ.get("HTTPS_PROXY"):
                proxies["https"] = os.environ.get("HTTPS_PROXY")
            
            # 创建临时文件以避免直接写入目标路径可能的权限问题
            temp_file = os.path.join(tempfile.gettempdir(), f"gtts_temp_{uuid.uuid4()}.mp3")
            
            # 使用gTTS生成语音
            tts = gTTS(text=text, lang=lang, slow=False)
            tts.save(temp_file)
            
            # 如果生成成功，复制到目标路径
            if os.path.exists(temp_file) and os.path.getsize(temp_file) > 0:
                shutil.copy2(temp_file, output_path)
                # 清理临时文件
                try:
                    os.remove(temp_file)
                except:
                    pass
                return True
            
            return False
        except Exception as e:
            logger.error(f"gTTS生成失败: {e}")
            return False
    
    def _create_silent_audio(self, output_path: str, duration=1.0):
        """创建静音音频（作为最后的后备方案）"""
        try:
            import numpy as np
            from scipy.io import wavfile
            
            # 创建1秒静音
            sample_rate = 22050
            samples = np.zeros(int(sample_rate * duration), dtype=np.int16)
            wavfile.write(output_path, sample_rate, samples)
            
            return os.path.exists(output_path)
        except Exception as e:
            logger.error(f"创建静音音频失败: {e}")
            
            # 最后的尝试：创建一个空文件
            try:
                with open(output_path, 'wb') as f:
                    # 写入一个最小的WAV文件头
                    f.write(b'RIFF\x24\x00\x00\x00WAVEfmt \x10\x00\x00\x00\x01\x00\x01\x00\x44\xac\x00\x00\x88\x58\x01\x00\x02\x00\x10\x00data\x00\x00\x00\x00')
                return True
            except:
                return False
    
    async def text_to_speech(self, 
                           text: str, 
                           voice_id: str = None, 
                           speed: float = 1.0,
                           output_path: str = None) -> Dict[str, Any]:
        """
        将文本转换为语音
        
        Args:
            text: 要转换的文本
            voice_id: 语音ID，例如 zh-female1 (后备服务会忽略此参数)
            speed: 语速
            output_path: 输出文件路径，如果为None则自动生成
            
        Returns:
            Dict: 结果字典，包含音频文件路径和元数据
        """
        self.stats["total_requests"] += 1
        
        # 确保引擎已初始化
        if not self.initialized:
            self.initialize()
        
        # 检查缓存
        cached_path = self.get_cached_audio(text, voice_id or "default", speed)
        if cached_path:
            # 使用缓存的音频
            if output_path:
                shutil.copy2(cached_path, output_path)
                audio_path = output_path
            else:
                audio_path = cached_path
                
            return {
                "success": True,
                "message": "使用缓存的音频",
                "audio_path": audio_path,
                "from_cache": True,
                "duration": 0  # 未计算实际持续时间
            }
        
        # 生成输出路径
        if not output_path:
            cache_key = self.get_cache_key(text, voice_id or "default", speed)
            output_path = os.path.join(self.cache_dir, f"{cache_key}.wav")
        
        start_time = time.time()
        success = False
        message = "未知错误"
        
        try:
            # 尝试使用pyttsx3
            if PYTTSX3_AVAILABLE and self.engine:
                try:
                    logger.info(f"使用pyttsx3生成语音: '{text[:30]}...'")
                    success = self._synthesize_with_pyttsx3(text, output_path, speed)
                    if success:
                        message = "使用pyttsx3生成语音成功"
                except Exception as e:
                    logger.error(f"pyttsx3生成失败: {e}")
            
            # 如果pyttsx3失败，尝试gTTS
            if not success:
                try:
                    # 判断语言
                    lang = "zh" if any('\u4e00' <= ch <= '\u9fff' for ch in text) else "en"
                    logger.info(f"使用gTTS生成语音: '{text[:30]}...' (语言: {lang})")
                    success = self._synthesize_with_google_tts(text, output_path, lang)
                    if success:
                        message = "使用gTTS生成语音成功"
                except Exception as e:
                    logger.error(f"gTTS生成失败: {e}")
            
            # 如果所有方法都失败，创建静音音频
            if not success:
                logger.warning(f"所有TTS方法失败，创建静音音频代替")
                success = self._create_silent_audio(output_path)
                if success:
                    message = "创建静音音频作为替代"
        
        except Exception as e:
            logger.error(f"语音生成过程中出错: {e}")
            success = False
            message = f"语音生成失败: {str(e)}"
        
        generation_time = time.time() - start_time
        self.stats["generation_time"] += generation_time
        
        return {
            "success": success, 
            "message": message,
            "audio_path": output_path if success else None,
            "from_cache": False,
            "duration": generation_time
        }
    
    async def generate_speech(self, 
                            text: str, 
                            voice_id: str = None, 
                            speed: float = 1.0,
                            output_path: str = None) -> Dict[str, Any]:
        """与text_to_speech相同，保持API一致性"""
        result = await self.text_to_speech(text, voice_id, speed, output_path)
        
        # 转换结果格式以匹配tts_service.py期望的格式
        converted_result = result.copy()
        
        # 添加status字段
        if "success" in result:
            # 将success布尔值转换为status字符串
            if result["success"]:
                converted_result["status"] = "success"
            else:
                converted_result["status"] = "error"
        
        # 确保路径字段格式统一
        if "audio_path" in result and result["audio_path"] and "path" not in converted_result:
            converted_result["path"] = result["audio_path"]
        
        return converted_result
    
    def get_available_voices(self) -> Dict:
        """获取可用的语音列表"""
        # 后备服务只提供默认语音
        voices = {
            "default": {
                "name": "后备服务默认语音",
                "language": "multi",
                "gender": "unknown",
                "description": "当主TTS服务不可用时的后备语音"
            }
        }
        
        # 如果有pyttsx3，添加系统语音
        if PYTTSX3_AVAILABLE and self.engine:
            try:
                system_voices = self.engine.getProperty('voices')
                for i, voice in enumerate(system_voices):
                    voice_id = f"system-{i+1}"
                    # 尝试检测语言
                    lang = "unknown"
                    if "chinese" in voice.name.lower() or "zh" in voice.id.lower():
                        lang = "zh"
                    elif "english" in voice.name.lower() or "en" in voice.id.lower():
                        lang = "en"
                    
                    voices[voice_id] = {
                        "name": voice.name,
                        "language": lang,
                        "gender": "unknown",
                        "description": f"系统语音: {voice.name}",
                        "id": voice.id
                    }
            except Exception as e:
                logger.error(f"获取系统语音时出错: {e}")
        
        return voices
    
    def get_performance_stats(self) -> Dict:
        """获取性能统计信息"""
        uptime = time.time() - self.stats["start_time"]
        
        return {
            "total_requests": self.stats["total_requests"],
            "cache_hits": self.stats["cache_hits"],
            "cache_hit_ratio": self.stats["cache_hits"] / max(1, self.stats["total_requests"]),
            "total_generation_time": self.stats["generation_time"],
            "average_generation_time": self.stats["generation_time"] / max(1, self.stats["total_requests"] - self.stats["cache_hits"]),
            "uptime_seconds": uptime,
            "engine_type": "pyttsx3" if (PYTTSX3_AVAILABLE and self.engine) else "fallback"
        }

# 单例实例
_fallback_tts_instance = None

def get_fallback_tts_service() -> FallbackTTSService:
    """获取后备TTS服务实例（单例模式）"""
    global _fallback_tts_instance
    if _fallback_tts_instance is None:
        _fallback_tts_instance = FallbackTTSService()
        _fallback_tts_instance.initialize()
    return _fallback_tts_instance 