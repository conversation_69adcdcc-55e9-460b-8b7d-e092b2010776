"""
初始化统一任务管理系统表
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.sqlalchemy_db import engine, Base
from app.models.task_system import UnifiedTask, TaskDependency, TaskLog, TaskMetrics, TaskQueue

def init_task_tables():
    """初始化任务管理系统表"""
    try:
        print("开始创建统一任务管理系统表...")
        
        # 创建所有表
        Base.metadata.create_all(bind=engine)
        
        print("统一任务管理系统表创建完成！")
        print("创建的表:")
        print("- unified_tasks: 统一任务表")
        print("- task_dependencies: 任务依赖关系表")
        print("- task_logs: 任务日志表")
        print("- task_metrics: 任务性能指标表")
        print("- task_queues: 任务队列配置表")
        
    except Exception as e:
        print(f"创建表失败: {e}")
        raise

if __name__ == "__main__":
    init_task_tables()
