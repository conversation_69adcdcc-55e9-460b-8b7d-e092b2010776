from sqlalchemy import Column, Integer, String, Text, ForeignKey, DateTime, Boolean, JSON, Float
from sqlalchemy.sql import func
from pydantic import BaseModel, ConfigDict
from typing import Optional, List, Dict, Any, Union
from datetime import datetime

from utils.db import Base

class DocumentTranslation(Base):
    """Document translation database model"""
    __tablename__ = "document_translations"

    id = Column(Integer, primary_key=True, index=True)
    task_id = Column(String, unique=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    file_name = Column(String)
    file_type = Column(String)
    file_size = Column(Integer)  # size in bytes
    source_language = Column(String)
    target_language = Column(String)
    source_file_path = Column(String)
    translated_file_path = Column(String, nullable=True)
    preview_path = Column(String, nullable=True)
    status = Column(String)  # pending, processing, completed, failed
    progress = Column(Float, default=0.0)
    error_message = Column(Text, nullable=True)
    word_count = Column(Integer, default=0)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    completed_at = Column(DateTime(timezone=True), nullable=True)
    domain = Column(String, nullable=True)  # 专业领域
    style = Column(String, nullable=True)  # 翻译风格
    advanced_options = Column(JSON, nullable=True)  # 高级选项
    glossary_id = Column(String, nullable=True)  # 术语库ID
    download_count = Column(Integer, default=0)  # 下载次数
    
# Pydantic models for API
class DocumentTranslationBase(BaseModel):
    source_language: str
    target_language: str
    domain: Optional[str] = "general"
    style: Optional[str] = "standard"
    advanced_options: Optional[List[str]] = []
    glossary_id: Optional[str] = None

class DocumentTranslationCreate(DocumentTranslationBase):
    pass

class DocumentTranslationUpdate(BaseModel):
    status: Optional[str] = None
    progress: Optional[float] = None
    error_message: Optional[str] = None
    translated_file_path: Optional[str] = None
    preview_path: Optional[str] = None
    word_count: Optional[int] = None
    completed_at: Optional[datetime] = None
    download_count: Optional[int] = None

class DocumentTranslationResponse(BaseModel):
    id: int
    task_id: str
    user_id: int
    file_name: str
    file_type: str
    file_size: int
    source_language: str
    target_language: str
    source_file_path: Optional[str] = None
    translated_file_path: Optional[str] = None
    preview_path: Optional[str] = None
    status: str
    progress: float
    error_message: Optional[str] = None
    word_count: int
    created_at: datetime
    completed_at: Optional[datetime] = None
    domain: Optional[str] = None
    style: Optional[str] = None
    advanced_options: Optional[List[str]] = None
    glossary_id: Optional[str] = None
    download_count: int

    model_config = ConfigDict(
        from_attributes=True,
        arbitrary_types_allowed=True,
        populate_by_name=True
    )

class DocumentTranslationPreview(BaseModel):
    source: str  # HTML格式的源文本预览
    translated: str  # HTML格式的译文预览
    
class DocumentFormatInfo(BaseModel):
    formats: List[Dict[str, Union[str, List[str]]]]
    maxSize: int
    sizeLimit: str
    
class DocumentTranslationHistory(BaseModel):
    list: List[DocumentTranslationResponse]
    pagination: Dict[str, int] 