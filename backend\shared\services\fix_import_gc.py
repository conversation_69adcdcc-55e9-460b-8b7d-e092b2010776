import re
import os

def fix_import_gc_issue():
    # Path to the file to fix
    file_path = os.path.join('services', 'wan_video_service.py')
    
    # Read the file
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Look for the problematic section
    pattern = r'try:\s+import torch\s+import gc'
    
    # Fix the issue by adding a proper indent before 'import gc'
    fixed_content = re.sub(pattern, 'try:\n                import torch\n                import gc', content)
    
    # Write the fixed content back
    with open(file_path + '.fixed', 'w', encoding='utf-8') as f:
        f.write(fixed_content)
    
    print(f"Fixed file created at {file_path}.fixed")
    print("To apply the fix, rename the fixed file to replace the original.")

if __name__ == "__main__":
    fix_import_gc_issue() 