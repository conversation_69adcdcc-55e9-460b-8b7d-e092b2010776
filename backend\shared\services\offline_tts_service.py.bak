import os
import logging
import tempfile
import uuid
import time
import hashlib
import warnings
import json
from typing import Dict, List, Optional, Union, Any
from pathlib import Path
import torch

# 导入修复脚本
from services.offline_tts_service_fix import apply_fixes, patch_tacotron_model
# 导入新的补丁脚本
from services.offline_tts_service_patch import apply_patches

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 过滤常见TTS警告
warnings.filterwarnings("ignore", message=".*weights_only=False.*")
warnings.filterwarnings("ignore", message=".*model_size.*")
warnings.filterwarnings("ignore", message=".*model_type.*")
warnings.filterwarnings("ignore", message=".*Valid config keys have changed in V2.*")

class TTSCache:
    """TTS语音缓存管理器"""
    
    def __init__(self, cache_dir=None, max_cache_size=1000, max_age_days=30):
        """
        初始化TTS缓存管理器
        
        Args:
            cache_dir: 缓存目录，为None时使用系统临时目录
            max_cache_size: 最大缓存条目数
            max_age_days: 缓存最大保存天数
        """
        if cache_dir is None:
            self.cache_dir = os.path.join(tempfile.gettempdir(), "tts_cache")
        else:
            self.cache_dir = cache_dir
            
        os.makedirs(self.cache_dir, exist_ok=True)
        self.max_cache_size = max_cache_size
        self.max_age_days = max_age_days
        self.cache_map = {}
        self._load_cache_index()
        
    def _load_cache_index(self):
        """加载缓存索引"""
        index_path = os.path.join(self.cache_dir, "cache_index.txt")
        if os.path.exists(index_path):
            try:
                with open(index_path, 'r', encoding='utf-8') as f:
                    for line in f:
                        parts = line.strip().split('|')
                        if len(parts) >= 3:
                            key, path, timestamp = parts[0], parts[1], float(parts[2])
                            if os.path.exists(path):
                                self.cache_map[key] = {"path": path, "timestamp": timestamp}
            except Exception as e:
                logger.warning(f"加载缓存索引失败: {e}")
                self.cache_map = {}
        
        # 清理过期缓存
        self._cleanup_old_cache()
        
    def _save_cache_index(self):
        """保存缓存索引"""
        index_path = os.path.join(self.cache_dir, "cache_index.txt")
        try:
            with open(index_path, 'w', encoding='utf-8') as f:
                for key, info in self.cache_map.items():
                    f.write(f"{key}|{info['path']}|{info['timestamp']}\n")
        except Exception as e:
            logger.warning(f"保存缓存索引失败: {e}")
    
    def _cleanup_old_cache(self):
        """清理过期缓存"""
        current_time = time.time()
        max_age_seconds = self.max_age_days * 24 * 60 * 60
        
        # 按时间戳排序
        sorted_items = sorted(self.cache_map.items(), key=lambda x: x[1]["timestamp"])
        
        # 删除过期缓存
        removed_count = 0
        for key, info in list(self.cache_map.items()):
            if current_time - info["timestamp"] > max_age_seconds:
                try:
                    if os.path.exists(info["path"]):
                        os.remove(info["path"])
                    del self.cache_map[key]
                    removed_count += 1
                except Exception as e:
                    logger.warning(f"删除过期缓存文件失败: {info['path']}, 错误: {e}")
        
        # 如果缓存数量超过最大值，删除最旧的
        if len(self.cache_map) > self.max_cache_size:
            to_remove = len(self.cache_map) - self.max_cache_size
            for key, _ in sorted_items[:to_remove]:
                info = self.cache_map[key]
                try:
                    if os.path.exists(info["path"]):
                        os.remove(info["path"])
                    del self.cache_map[key]
                    removed_count += 1
                except Exception as e:
                    logger.warning(f"删除多余缓存文件失败: {info['path']}, 错误: {e}")
        
        if removed_count > 0:
            logger.info(f"已清理 {removed_count} 个过期或多余缓存")
            self._save_cache_index()
    
    def get_cache_key(self, text: str, voice_id: str, speed: float) -> str:
        """生成缓存键"""
        cache_str = f"{text}|{voice_id}|{speed}"
        return hashlib.md5(cache_str.encode('utf-8')).hexdigest()
    
    def get_cached_audio(self, text: str, voice_id: str, speed: float) -> Optional[str]:
        """
        获取缓存的音频文件路径
        
        Args:
            text: 文本内容
            voice_id: 语音ID
            speed: 语速
            
        Returns:
            str: 缓存文件路径，如果不存在则返回None
        """
        cache_key = self.get_cache_key(text, voice_id, speed)
        if cache_key in self.cache_map:
            cache_info = self.cache_map[cache_key]
            if os.path.exists(cache_info["path"]):
                # 更新时间戳
                self.cache_map[cache_key]["timestamp"] = time.time()
                self._save_cache_index()
                return cache_info["path"]
            else:
                # 缓存文件不存在，删除记录
                del self.cache_map[cache_key]
                self._save_cache_index()
        
        return None
    
    def add_to_cache(self, text: str, voice_id: str, speed: float, audio_path: str) -> str:
        """
        添加音频文件到缓存
        
        Args:
            text: 文本内容
            voice_id: 语音ID
            speed: 语速
            audio_path: 原始音频文件路径
            
        Returns:
            str: 缓存文件路径
        """
        cache_key = self.get_cache_key(text, voice_id, speed)
        
        # 创建缓存文件路径
        ext = os.path.splitext(audio_path)[1]
        cache_filename = f"{cache_key}{ext}"
        cache_path = os.path.join(self.cache_dir, cache_filename)
        
        try:
            # 复制文件到缓存
            import shutil
            shutil.copy2(audio_path, cache_path)
            
            # 更新缓存索引
            self.cache_map[cache_key] = {
                "path": cache_path,
                "timestamp": time.time()
            }
            self._save_cache_index()
            
            return cache_path
        except Exception as e:
            logger.warning(f"添加文件到缓存失败: {e}")
            if os.path.exists(cache_path):
                try:
                    os.remove(cache_path)
                except:
                    pass
            return audio_path

class OfflineTTSService:
    """离线文本到语音转换服务 - 基于Coqui TTS"""
    
    def __init__(self, log_level='info'):
        # 可用语音字典 - 包含多种语言和风格
        self.available_voices = {
            "zh-female1": {"model": "tts_models/zh-CN/baker/tacotron2-DDC-GST", "gender": "female", "name": "中文女声"},
            "zh-male1": {"model": "tts_models/multilingual/multi-dataset/xtts_v1", "language": "zh-cn", "gender": "male", "name": "中文男声"},
            "zh-female2": {"model": "tts_models/multilingual/multi-dataset/xtts_v1", "language": "zh-cn", "gender": "female", "name": "中文女声2"},
            "en-female1": {"model": "tts_models/en/ljspeech/tacotron2-DDC", "gender": "female", "name": "英语女声"},
            "en-male1": {"model": "tts_models/multilingual/multi-dataset/xtts_v1", "language": "en", "gender": "male", "name": "英语男声"},
            "en-female2": {"model": "tts_models/multilingual/multi-dataset/xtts_v1", "language": "en", "gender": "female", "name": "英语女声2"},
            "ja-female1": {"model": "tts_models/ja/kokoro/tacotron2-DDC", "gender": "female", "name": "日语女声"},
            "ja-male1": {"model": "tts_models/multilingual/multi-dataset/xtts_v1", "language": "ja", "gender": "male", "name": "日语男声"},
            "de-female1": {"model": "tts_models/de/thorsten/tacotron2-DDC", "gender": "female", "name": "德语女声"},
            "fr-male1": {"model": "tts_models/fr/mai/tacotron2-DDC", "gender": "male", "name": "法语男声"},
            "es-male1": {"model": "tts_models/es/mai/tacotron2-DDC", "gender": "male", "name": "西班牙语男声"},
            "ru-female1": {"model": "tts_models/ru/ruslan/ruslan-v1", "gender": "female", "name": "俄语女声"},
        }
        
        # 默认语音设置
        self.default_voice_id = "zh-female1"
        
        # 模型缓存
        self.model_cache = {}
        
        # TTS设置
        self.use_cuda = torch.cuda.is_available()
        logger.info(f"CUDA可用: {self.use_cuda}")
        
        # 设置模型下载目录
        self.models_dir = os.path.join(os.getcwd(), "models", "tts")
        os.makedirs(self.models_dir, exist_ok=True)
        
        # 设置TTS模型下载路径环境变量
        os.environ["TTS_HOME"] = self.models_dir
        
        # 初始化标志
        self.is_initialized = False
        
        # 设置日志级别
        self.set_log_level(log_level)
        
        # 初始化TTS缓存
        self.tts_cache = TTSCache(
            cache_dir=os.path.join(self.models_dir, "tts_cache"),
            max_cache_size=500,
            max_age_days=7
        )
        
        # 性能统计
        self.perf_stats = {
            "total_requests": 0,
            "cache_hits": 0,
            "generation_times": []
        }
        
        logger.info("离线TTS服务已初始化")
    
    def set_log_level(self, level='info'):
        """
        设置日志级别
        
        Args:
            level: 日志级别，可选值: debug, info, warning, error, critical
        """
        level_dict = {
            'debug': logging.DEBUG,
            'info': logging.INFO,
            'warning': logging.WARNING,
            'error': logging.ERROR,
            'critical': logging.CRITICAL
        }
        
        log_level = level_dict.get(level.lower(), logging.INFO)
        logger.setLevel(log_level)
        
        # 设置第三方库的日志级别
        if log_level > logging.INFO:
            # 如果用户选择了更高级别的日志，则抑制第三方库的低级别日志
            third_party_loggers = [
                'TTS',
                'urllib3',
                'matplotlib',
                'torch',
                'whisper'
            ]
            
            for log_name in third_party_loggers:
                third_party_logger = logging.getLogger(log_name)
                third_party_logger.setLevel(logging.WARNING)
        
        logger.debug(f"日志级别已设置为: {level}")
        
        # 过滤常见警告
        self._filter_common_warnings()
    
    def _filter_common_warnings(self):
        """过滤常见警告"""
        # 这些警告来自第三方库，通常不影响核心功能
        common_warnings = [
            # PyTorch加载模型警告
            ".*weights_only=False.*",
            # Pydantic字段警告
            ".*model_size.*", 
            ".*model_type.*",
            ".*protected namespace.*",
            # 配置键更改警告
            ".*Valid config keys have changed.*",
            ".*'orm_mode' has been renamed.*"
        ]
        
        for warning_pattern in common_warnings:
            warnings.filterwarnings("ignore", message=warning_pattern)
            
        logger.debug("已过滤常见警告")
    
    def initialize(self) -> bool:
        """
        初始化TTS服务，加载必要的模型
        
        Returns:
            bool: 是否成功初始化
        """
        try:
            logger.info("应用TTS编码修复...")
            
            # 应用编码修复
            apply_fixes()
            
            # 应用补丁以处理espeak缺失的情况
            apply_patches()
            
            # 预检查模型配置文件
            self._precheck_model_configs()
            
            # 标记为已初始化
            self.is_initialized = True
            
            # 预加载默认模型
            default_voice = self.validate_voice_id(self.default_voice_id)
            voice_config = self.get_voice_config(default_voice)
            model_name = voice_config.get("model")
            
            if model_name:
                logger.info(f"预加载默认TTS模型: {model_name}")
                synthesizer = self._get_synthesizer(model_name)
                if synthesizer:
                    logger.info(f"默认TTS模型加载成功: {model_name}")
                else:
                    logger.warning(f"默认TTS模型加载失败: {model_name}，服务将在首次使用时尝试加载替代模型")
            
            logger.info("TTS服务初始化完成")
            return True
        except Exception as e:
            logger.error(f"初始化离线TTS服务失败: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False
            
    def _precheck_model_configs(self):
        """
        预检查所有语音模型的配置文件，并尝试修复损坏的配置
        """
        logger.info("开始预检查所有TTS模型配置文件...")
        fixed_count = 0
        failed_count = 0
        unavailable_voices = []
        
        for voice_id, voice_config in self.available_voices.items():
            model_name = voice_config.get("model", "")
            if not model_name:
                continue
                
            try:
                # 首先检查模型目录是否存在
                if "xtts_v1" in model_name and not self._check_model_dir_exists(model_name):
                    logger.warning(f"语音ID {voice_id} 的模型目录不存在: {model_name}")
                    unavailable_voices.append(voice_id)
                    failed_count += 1
                    continue
                
                # 尝试获取模型路径和验证配置
                try:
                    _, config_path, _ = self._get_model_paths(model_name)
                    if not self._validate_and_fix_config_file(config_path):
                        logger.warning(f"无法修复语音ID {voice_id} 的模型配置文件: {model_name}")
                        unavailable_voices.append(voice_id)
                        failed_count += 1
                    else:
                        fixed_count += 1
                except Exception as e:
                    logger.warning(f"获取语音ID {voice_id} 的模型路径时出错: {e}")
                    unavailable_voices.append(voice_id)
                    failed_count += 1
            except Exception as e:
                logger.warning(f"检查语音ID {voice_id} 的模型 {model_name} 时出错: {e}")
                unavailable_voices.append(voice_id)
                failed_count += 1
        
        logger.info(f"预检查完成: {fixed_count} 个模型配置正常或已修复, {failed_count} 个模型配置验证失败")
        
        # 处理不可用的语音
        if unavailable_voices:
            logger.warning(f"以下语音ID不可用，将从可用列表中移除: {', '.join(unavailable_voices)}")
            
            # 创建可用语音的副本
            available_voices_copy = self.available_voices.copy()
            
            # 确定哪些语言至少有一个可用语音
            available_langs = set()
            for voice_id in available_voices_copy:
                if voice_id not in unavailable_voices:
                    lang = voice_id.split('-')[0]
                    available_langs.add(lang)
            
            # 处理每个不可用的语音
            for voice_id in unavailable_voices:
                # 更新默认语音ID（如果需要）
                if voice_id == self.default_voice_id:
                    lang = voice_id.split('-')[0]
                    # 寻找同一语言的其他可用语音
                    for alt_id in available_voices_copy:
                        if alt_id.startswith(f"{lang}-") and alt_id not in unavailable_voices:
                            logger.info(f"更新默认语音ID从 {self.default_voice_id} 到 {alt_id}")
                            self.default_voice_id = alt_id
                            break
                    else:
                        # 如果找不到同一语言的可用语音，使用任何可用的语音
                        for alt_id in available_voices_copy:
                            if alt_id not in unavailable_voices:
                                logger.info(f"未找到同语言的替代语音，将默认语音ID从 {self.default_voice_id} 更新为 {alt_id}")
                                self.default_voice_id = alt_id
                                break
                
                # 首先检查是否需要保留此语音 - 如果它是该语言唯一的语音ID
                lang = voice_id.split('-')[0]
                if lang not in available_langs:
                    logger.info(f"保留语音ID {voice_id}，尽管模型不可用，因为它是唯一的{lang}语音")
                    continue
                
                # 否则从可用语音列表中删除
                if voice_id in self.available_voices:
                    del self.available_voices[voice_id]
        
        # 如果默认中文模型的配置有问题，更新默认语音ID
        if (self.default_voice_id.startswith("zh-") and 
            any(v.startswith("zh-") and 
                v != self.default_voice_id and 
                v not in unavailable_voices and
                self._is_model_available(self.available_voices[v].get("model", ""))
                for v in self.available_voices.keys())):
            
            # 寻找备选中文语音
            for alt_id in ["zh-male1", "zh-female2"]:
                if (alt_id in self.available_voices and 
                    alt_id not in unavailable_voices and
                    self._is_model_available(self.available_voices[alt_id].get("model", ""))):
                    logger.info(f"更新默认语音ID从 {self.default_voice_id} 到 {alt_id}")
                    self.default_voice_id = alt_id
                    break
    
    def _is_model_available(self, model_name: str) -> bool:
        """
        检查模型是否可用
        
        Args:
            model_name: 模型名称
            
        Returns:
            bool: 模型是否可用
        """
        if not model_name:
            return False
            
        try:
            # 检查模型目录是否存在
            if "xtts_v1" in model_name and not self._check_model_dir_exists(model_name):
                logger.warning(f"XTTS模型目录不存在: {model_name}")
                return False
                
            # 检查模型配置文件
            _, config_path, _ = self._get_model_paths(model_name)
            if not os.path.exists(config_path):
                logger.warning(f"模型配置文件不存在: {config_path}")
                return False
                
            return self._validate_and_fix_config_file(config_path)
        except Exception as e:
            logger.warning(f"检查模型 {model_name} 可用性时出错: {e}")
            return False
    
    def _check_model_dir_exists(self, model_name: str) -> bool:
        """
        检查模型目录是否存在
        
        Args:
            model_name: 模型名称，如 'tts_models/multilingual/multi-dataset/xtts_v1'
            
        Returns:
            bool: 目录是否存在
        """
        if not model_name:
            return False
        
        # 防止路径注入
        if '..' in model_name or model_name.startswith('/'):
            logger.warning(f"不安全的模型路径: {model_name}")
            return False
            
        # 尝试多种可能的路径格式
        possible_paths = []
        
        # 将模型路径转换为文件系统路径 - 方法1：直接替换
        model_path1 = model_name.replace('/', '--').replace('_', '-')
        possible_paths.append(os.path.join(self.models_dir, "tts", model_path1))
        
        # 方法2：分段替换
        parts = model_name.split('/')
        if len(parts) >= 2:
            # 构建像 "tts_models--zh-CN--baker--tacotron2-DDC-GST" 这样的路径
            if parts[0] in ["tts_models", "vocoder_models"]:
                # 主级别目录，如 tts_models
                main_dir = parts[0].replace('_', '-')
                # 语言，如 zh-CN
                lang = parts[1] if len(parts) > 1 else ""
                # 模型集合，如 baker
                dataset = parts[2] if len(parts) > 2 else ""
                # 模型名称，如 tacotron2-DDC-GST
                model = parts[3] if len(parts) > 3 else ""
                
                # 添加完整路径
                path = f"{main_dir}--{lang}--{dataset}--{model}"
                possible_paths.append(os.path.join(self.models_dir, "tts", path))
                
                # 添加部分路径 (没有模型名)
                if model:
                    path_without_model = f"{main_dir}--{lang}--{dataset}"
                    possible_paths.append(os.path.join(self.models_dir, "tts", path_without_model))
        
        # 方法3：递归构建路径
        model_dir = os.path.join(self.models_dir, "tts")
        build_path = ""
        
        for part in parts:
            if build_path:
                build_path = os.path.join(build_path, part.replace('-', '--'))
            else:
                build_path = part.replace('-', '--')
        
        if build_path:
            possible_paths.append(os.path.join(model_dir, build_path))
            
        # 替换通常的路径转换
        common_replacements = [
            (os.path.join('tts_models', ''), os.path.join('tts', 'tts_models--')),
            (os.path.join('vocoder_models', ''), os.path.join('tts', 'vocoder_models--')),
            ('/', '--')
        ]
        
        path_with_common = model_name
        for old, new in common_replacements:
            path_with_common = path_with_common.replace(old, new)
        
        possible_paths.append(os.path.join(self.models_dir, path_with_common))
            
        # 检查所有可能的路径
        for path in possible_paths:
            if os.path.exists(path):
                logger.debug(f"找到模型目录: {path}")
                return True
                
        # 尝试检查父目录，可能模型尚未下载但基础目录存在
        for path in possible_paths:
            parent_dir = os.path.dirname(path)
            if os.path.exists(parent_dir):
                # 仅当父目录包含有效的模型文件时才认为可能有效
                if any(f.endswith('.pth') for f in os.listdir(parent_dir) if os.path.isfile(os.path.join(parent_dir, f))):
                    logger.debug(f"找到可能的模型父目录: {parent_dir}")
                    return True
        
        logger.info(f"模型目录不存在，已尝试以下路径: {', '.join(possible_paths)}")
        return False
    
    def _clean_json_comments(self, json_str: str) -> str:
        """
        移除JSON字符串中的注释
        
        Args:
            json_str: 包含注释的JSON字符串
            
        Returns:
            str: 移除注释后的JSON字符串
        """
        import re
        
        # 移除单行注释 (// 注释)
        pattern = r'//.*?(?=\n|$)'
        clean_str = re.sub(pattern, '', json_str)
        
        # 移除多行注释 (/* 注释 */)
        pattern = r'/\*.*?\*/'
        clean_str = re.sub(pattern, '', clean_str, flags=re.DOTALL)
        
        # 处理末尾多余的逗号 (JSON不允许对象和数组的最后一个元素后有逗号)
        clean_str = re.sub(r',(\s*[\]}])', r'\1', clean_str)
        
        return clean_str
    
    def _validate_and_fix_config_file(self, config_path: str) -> bool:
        """验证并修复TTS模型配置文件"""
        try:
            # 检查文件是否存在
            if not os.path.exists(config_path):
                logger.error(f"配置文件不存在: {config_path}")
                return False
            
            # 读取配置文件
            with open(config_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 清理注释
            content_clean = self._clean_json_comments(content)
            
            # 解析JSON
            try:
                config = json.loads(content_clean)
            except json.JSONDecodeError as e:
                logger.error(f"配置文件JSON格式错误: {config_path}, {e}")
                return False
            
            # 检查是否为Tacotron2模型
            if 'model' in config:
                model_type = config['model'].lower()
                
                # 针对不同模型类型进行特定修复
                if model_type in ['tacotron2', 'tacotron']:
                    return self._fix_tacotron_config(config_path, config)
                else:
                    logger.info(f"不需要修复的模型类型: {model_type}")
                    return True
            else:
                logger.warning(f"配置文件缺少model字段: {config_path}")
                return False
            
        except Exception as e:
            logger.error(f"读取配置文件时出错: {e}")
            return False
    
    def _fix_tacotron_config(self, config_path: str, config: dict) -> bool:
        """修复Tacotron/Tacotron2模型的配置"""
        try:
            need_update = False
            
            # 检查并修复num_chars
            if 'num_chars' not in config or not config['num_chars'] or config['num_chars'] <= 0:
                # 尝试从字符集计算正确的值
                if 'characters' in config and 'characters' in config['characters']:
                    chars = config['characters']['characters']
                    if chars:
                        char_count = len(chars)
                        logger.info(f"设置num_chars为字符集长度: {char_count}")
                        config['num_chars'] = char_count
                        need_update = True
                    else:
                        logger.info("字符集为空，设置num_chars为默认值149")
                        config['num_chars'] = 149
                        need_update = True
                else:
                    logger.info("未找到字符集，设置num_chars为默认值149")
                    config['num_chars'] = 149
                    need_update = True
            
            # 检查并修复characters字段
            if 'characters' in config:
                for field in ['pad', 'eos', 'bos', 'characters', 'phonemes', 'punctuations']:
                    if field not in config['characters'] or config['characters'][field] is None:
                        config['characters'][field] = ""
                        logger.info(f"修复空{field}字段")
                        need_update = True
            
            # 如果是中文模型，确保其他参数也正确
            model_id = config.get('model_id', '').lower()
            if model_id and ('zh-cn' in model_id or 'zh_cn' in model_id or 'baker' in model_id or 'chinese' in model_id):
                # 修复中文特定配置
                if config.get('text_cleaner') != 'chinese_mandarin_cleaners':
                    config['text_cleaner'] = 'chinese_mandarin_cleaners'
                    logger.info("修复中文清理器设置")
                    need_update = True
                
                if config.get('phoneme_language') != 'zh-cn':
                    config['phoneme_language'] = 'zh-cn'
                    logger.info("修复中文音素语言设置")
                    need_update = True
                
                # 设置GST，因为中文模型通常使用GST
                if 'use_gst' not in config or not config['use_gst']:
                    config['use_gst'] = True
                    if 'gst' not in config:
                        config['gst'] = {
                            "gst_embedding_dim": 512,
                            "gst_num_heads": 4,
                            "gst_num_style_tokens": 10,
                            "gst_style_input_wav": None,
                            "gst_style_input_weights": None,
                            "gst_use_speaker_embedding": False
                        }
                    logger.info("添加GST配置")
                    need_update = True
            
            # 确保audio.num_mels与decoder匹配
            if 'audio' in config and 'num_mels' in config['audio']:
                # 如果存在与状态字典不匹配的情况，这里设置为80或160
                if config['audio']['num_mels'] not in [80, 160]:
                    logger.info(f"调整num_mels从{config['audio']['num_mels']}为80")
                    config['audio']['num_mels'] = 80  # 保守选择80，因为它更常见
                    need_update = True
                
                # 对于中文模型，特别检查
                if model_id and ('zh-cn' in model_id or 'zh_cn' in model_id or 'baker' in model_id):
                    # 检查是否需要切换为160
                    model_dir = os.path.dirname(config_path)
                    # 尝试查找模型文件
                    model_files = [f for f in os.listdir(model_dir) if f.endswith('.pth')]
                    if model_files:
                        model_path = os.path.join(model_dir, model_files[0])
                        try:
                            # 不加载整个模型，只检查状态字典的形状
                            checkpoint = torch.load(model_path, map_location='cpu')
                            if 'model' in checkpoint:
                                model_dict = checkpoint['model']
                                # 检查decoder.linear_projection的形状
                                if 'decoder.linear_projection.linear_layer.weight' in model_dict:
                                    weight = model_dict['decoder.linear_projection.linear_layer.weight']
                                    if weight.shape[0] == 160 and config['audio']['num_mels'] != 160:
                                        logger.info(f"根据模型状态字典，调整num_mels从{config['audio']['num_mels']}为160")
                                        config['audio']['num_mels'] = 160
                                        need_update = True
                                    elif weight.shape[0] == 80 and config['audio']['num_mels'] != 80:
                                        logger.info(f"根据模型状态字典，调整num_mels从{config['audio']['num_mels']}为80")
                                        config['audio']['num_mels'] = 80
                                        need_update = True
                        except Exception as e:
                            logger.warning(f"检查模型状态字典时出错: {e}")
            
            # 保存更新后的配置文件
            if need_update:
                try:
                    # 创建备份
                    backup_path = config_path + ".bak"
                    if not os.path.exists(backup_path):
                        import shutil
                        shutil.copy2(config_path, backup_path)
                        logger.info(f"已创建配置文件备份: {backup_path}")
                    
                    # 写入新配置
                    with open(config_path, 'w', encoding='utf-8') as f:
                        json.dump(config, f, indent=4)
                    
                    logger.info(f"已更新配置文件: {config_path}")
                    return True
                except Exception as e:
                    logger.error(f"保存更新后的配置文件时出错: {e}")
                    return False
            else:
                logger.info(f"配置文件无需更新: {config_path}")
                return True
            
        except Exception as e:
            logger.error(f"修复配置文件失败: {e}")
            import traceback
            logger.error(traceback.format_exc())
            
            return False

    def _get_synthesizer(self, model_name: str):
        """获取语音合成器 - 如果已缓存则返回缓存，否则加载新的"""
        if model_name in self.model_cache:
            return self.model_cache[model_name]["synthesizer"]
        
        try:
            from TTS.utils.synthesizer import Synthesizer
            from TTS.tts.models.tacotron2 import Tacotron2
            import torch.nn as nn
            
            # 应用TTS补丁修复 - 在获取合成器之前应用
            self._apply_tts_patch()
            
            logger.info(f"加载TTS模型: {model_name}")
            
            # 使用模型管理器获取模型信息
            model_path, config_path, model_item = self._get_model_paths(model_name)
            
            # 验证并修复配置文件
            if not self._validate_and_fix_config_file(config_path):
                logger.warning(f"配置文件验证或修复失败，模型可能无法正常工作: {model_name}")
                # 如果是中文模型并且配置文件有问题，尝试切换到其他可用模型
                if "zh-CN" in model_name:
                    alternate_model = "tts_models/multilingual/multi-dataset/xtts_v1"
                    logger.info(f"尝试使用替代模型: {alternate_model}")
                    return self._get_synthesizer(alternate_model)
            
            # 如果是多语言XTTS模型，需要特别处理
            if "xtts" in model_name.lower():
                try:
                    synthesizer = Synthesizer(
                        tts_checkpoint=model_path,
                        tts_config_path=config_path,
                        tts_speakers_file=None,
                        tts_languages_file=None,
                        vocoder_checkpoint="",
                        vocoder_config="",
                        encoder_checkpoint="",
                        encoder_config="",
                        use_cuda=self.use_cuda
                    )
                except Exception as e:
                    logger.error(f"加载XTTS模型失败: {model_name}, 错误: {e}")
                    logger.info(f"尝试加载XTTS模型: {model_name}")
                    logger.info(f"XTTS模型不可用: {model_name}, 尝试备选模型")
                    # 尝试其他备选模型
                    for alt_model in ["tts_models/multilingual/multi-dataset/your-tts", 
                                     "tts_models/multilingual/multi-dataset/bark",
                                     "tts_models/zh-CN/baker/tacotron2-DDC-GST",
                                     "tts_models/en/ljspeech/tacotron2-DDC"]:
                        try:
                            logger.info(f"尝试加载备选模型: {alt_model}")
                            return self._get_synthesizer(alt_model)
                        except Exception:
                            continue
                    raise RuntimeError("所有TTS模型加载失败，请确保至少一个TTS模型正确安装")
            else:
                # 获取vocoder信息
                vocoder_name = model_item.get("default_vocoder", "")
                vocoder_path, vocoder_config, _ = self._get_model_paths(vocoder_name) if vocoder_name else ("", "", None)
                
                try:
                    # 检查espeak是否缺失
                    espeak_error = False
                    
                    # 创建语音合成器
                    try:
                        synthesizer = Synthesizer(
                            tts_checkpoint=model_path,
                            tts_config_path=config_path,
                            tts_speakers_file=None,
                            tts_languages_file=None,
                            vocoder_checkpoint=vocoder_path,
                            vocoder_config=vocoder_config,
                            encoder_checkpoint="",
                            encoder_config="",
                            use_cuda=self.use_cuda
                        )
                    except Exception as e:
                        if "No espeak backend found" in str(e):
                            # 这是espeak缺失错误，启用我们的补丁
                            logger.warning("检测到espeak缺失，尝试使用补丁解决...")
                            from services.offline_tts_service_patch import apply_patches
                            apply_patches()  # 再次应用补丁
                            espeak_error = True
                            # 抛出异常，让下面的代码继续处理
                            raise
                        else:
                            # 其他错误，直接抛出
                            raise
                        
                except Exception as e:
                    # 捕获并记录具体错误
                    logger.error(f"加载TTS模型失败: {model_name}, 错误: {e}")
                    import traceback
                    logger.error(traceback.format_exc())
                    
                    # 如果是espeak错误，使用非严格模式尝试加载
                    if espeak_error or "No espeak backend found" in str(e) or "Phonemizer is not defined" in str(e):
                        logger.info(f"检测到espeak或phonemizer相关错误，尝试使用补丁方案解决")
                    
                    # 尝试使用更宽松的方式加载模型
                    try:
                        logger.info(f"尝试使用非严格模式加载模型: {model_name}")
                        # 导入模块
                        try:
                            from TTS.utils.generic_utils import setup_model
                        except ImportError:
                            # 如果导入失败，应用补丁后再试
                            from services.offline_tts_service_patch import patch_tts_setup_model
                            patch_tts_setup_model()
                            from TTS.utils.generic_utils import setup_model
                        
                        from TTS.utils.io import load_config
                        
                        # 加载配置
                        c = load_config(config_path)
                        
                        # 设置模型
                        model = setup_model(c)
                        
                        # 手动加载状态字典，使用非严格模式
                        checkpoint = torch.load(model_path, map_location=torch.device('cpu'))
                        # 我们的补丁会处理状态字典不匹配的问题
                        model.load_state_dict(checkpoint['model'])
                        
                        # 创建自定义合成器
                        synthesizer = Synthesizer(
                            tts_checkpoint="",  # 已经手动加载了模型
                            tts_config_path=config_path,
                            tts_speakers_file=None,
                            tts_languages_file=None,
                            vocoder_checkpoint=vocoder_path,
                            vocoder_config=vocoder_config,
                            encoder_checkpoint="",
                            encoder_config="",
                            use_cuda=self.use_cuda
                        )
                        
                        # 手动设置TTS模型
                        synthesizer.tts_model = model
                        synthesizer.tts_config = c
                        
                        logger.info(f"使用非严格模式成功加载模型: {model_name}")
                    except Exception as e2:
                        logger.error(f"非严格模式加载也失败: {e2}")
                        # 如果加载失败并且是中文模型，尝试切换到其他可用模型
                        if "zh-CN" in model_name:
                            try:
                                alternate_model = "tts_models/multilingual/multi-dataset/xtts_v1"
                                logger.info(f"主模型加载失败，尝试使用替代模型: {alternate_model}")
                                return self._get_synthesizer(alternate_model)
                            except Exception as alt_e:
                                logger.error(f"替代模型也加载失败: {alt_e}")
                        raise
            
            # 缓存合成器实例
            self.model_cache[model_name] = {
                "synthesizer": synthesizer,
                "last_used": time.time()
            }
            return synthesizer
            
        except Exception as e:
            logger.error(f"加载TTS模型失败: {model_name}, 错误: {e}")
            import traceback
            logger.error(traceback.format_exc())
            
            # 如果加载失败并且是中文模型，尝试切换到其他可用模型
            if "zh-CN" in model_name:
                try:
                    alternate_model = "tts_models/multilingual/multi-dataset/xtts_v1"
                    logger.info(f"主模型加载失败，尝试使用替代模型: {alternate_model}")
                    return self._get_synthesizer(alternate_model)
                except Exception as alt_e:
                    logger.error(f"替代模型也加载失败: {alt_e}")
            
            return None
    
    def _apply_tts_patch(self):
        """应用TTS库修复补丁，解决nn.Embedding和Tacotron2初始化问题"""
        # 使用外部修复模块应用补丁
        try:
            from services.offline_tts_service_fix import patch_tacotron_model
            from services.offline_tts_service_patch import apply_patches
            
            # 应用Tacotron2模型补丁
            patch_applied = patch_tacotron_model()
            if patch_applied:
                logger.info("[TTS修复] TTS模型初始化方法已修补")
            else:
                logger.warning("[TTS修复] TTS模型补丁应用失败，可能会影响服务稳定性")
                
            # 应用我们的espeak补丁和setup_model补丁
            patches_applied = apply_patches()
            if patches_applied:
                logger.info("[TTS修复] espeak和setup_model补丁已应用")
            else:
                logger.warning("[TTS修复] 无法应用espeak和setup_model补丁")
                
        except Exception as e:
            logger.error(f"[TTS修复] 应用补丁时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
    
    def _get_model_paths(self, model_name: str):
        """获取模型文件路径"""
        from TTS.utils.manage import ModelManager
        
        if not hasattr(self, 'model_manager'):
            # 使用正确的参数初始化ModelManager
            self.model_manager = ModelManager(
                models_file=None,
                output_prefix=self.models_dir,
                progress_bar=True,
                verbose=True
            )
        
        # 特殊处理XTTS模型，它可能在不同的路径
        if "xtts_v1" in model_name:
            # 使用info级别而不是warning，因为这是预期行为
            logger.info(f"尝试加载XTTS模型: {model_name}")
            
            # 定义备选多语言模型列表，优先级从高到低
            alternative_models = [
                "tts_models/multilingual/multi-dataset/your_tts",
                "tts_models/multilingual/multi-dataset/bark",
                # 如果有其他潜在的多语言模型，可以在这里添加
            ]
            
            # 首先尝试原始模型
            try:
                return self.model_manager.download_model(model_name)
            except Exception as e:
                # 降低日志级别，因为这是预期的异常
                logger.info(f"XTTS模型不可用: {model_name}, 尝试备选模型")
            
            # 尝试备选模型
            for alt_model in alternative_models:
                try:
                    # 首先检查模型目录是否存在
                    if self._check_model_dir_exists(alt_model):
                        logger.info(f"使用备选多语言模型: {alt_model}")
                        return self.model_manager.download_model(alt_model)
                except Exception as alt_e:
                    logger.info(f"备选模型 {alt_model} 也不可用")
            
            # 如果所有备选方案都失败，使用中文或英文标准模型作为最后的备选
            fallback_models = [
                "tts_models/zh-CN/baker/tacotron2-DDC-GST",  # 中文模型
                "tts_models/en/ljspeech/tacotron2-DDC"       # 英文模型
            ]
            
            for fallback_model in fallback_models:
                try:
                    if self._check_model_dir_exists(fallback_model):
                        logger.warning(f"所有多语言模型均不可用，使用标准模型替代: {fallback_model}")
                        return self.model_manager.download_model(fallback_model)
                except Exception as fb_e:
                    continue
            
            # 如果所有方案都失败，抛出异常，但使用更有用的错误信息
            raise RuntimeError("所有TTS模型加载失败，请确保至少一个TTS模型正确安装")
        else:
            # 普通模型处理
            try:
                return self.model_manager.download_model(model_name)
            except Exception as e:
                # 提供更详细的错误信息
                error_msg = str(e)
                if "not found" in error_msg.lower():
                    raise RuntimeError(f"模型 {model_name} 未找到，请检查模型是否已安装或下载") from e
                elif "config" in error_msg.lower():
                    raise RuntimeError(f"模型 {model_name} 配置文件出错，请检查配置文件格式") from e
                else:
                    raise RuntimeError(f"加载模型 {model_name} 时出错: {e}") from e
    
    def validate_voice_id(self, voice_id: str) -> str:
        """
        验证并返回有效的voice_id
        
        Args:
            voice_id: 输入的voice_id
            
        Returns:
            str: 有效的voice_id
        """
        # 如果voice_id为system、None或空字符串，使用默认voice_id
        if voice_id in [None, 'system', '']:
            return self.default_voice_id
            
        # 检查voice_id是否在可用列表中
        if voice_id in self.available_voices:
            # 验证对应的模型配置是否可用
            voice_config = self.available_voices[voice_id]
            model_name = voice_config.get("model", "")
            
            try:
                # 尝试获取模型路径以验证模型是否可加载
                if model_name:
                    _, config_path, _ = self._get_model_paths(model_name)
                    if not self._validate_and_fix_config_file(config_path):
                        logger.warning(f"语音ID {voice_id} 的配置文件验证失败，将尝试使用替代语音")
                        
                        # 如果是中文模型，尝试使用替代的中文语音
                        if voice_id.startswith("zh-"):
                            for alt_id in ["zh-male1", "zh-female2"]:
                                if alt_id in self.available_voices and alt_id != voice_id:
                                    logger.info(f"使用替代中文语音ID: {alt_id}")
                                    return alt_id
            except Exception as e:
                logger.warning(f"验证语音ID {voice_id} 时出错: {e}")
            
            # 如果没有验证失败，返回原始voice_id
            return voice_id
            
        # 根据language和gender匹配
        if "-" in voice_id:
            lang_code = voice_id.split("-")[0]
            for available_id, voice_info in self.available_voices.items():
                if available_id.startswith(lang_code):
                    logger.info(f"使用替代语音ID: {available_id}")
                    return available_id
        
        # 如果voice_id无效，返回默认voice_id
        logger.warning(f"未找到语音ID: {voice_id}，使用默认语音ID: {self.default_voice_id}")
        
        # 检查默认voice_id是否可用
        default_config = self.available_voices.get(self.default_voice_id, {})
        default_model = default_config.get("model", "")
        try:
            if default_model:
                _, config_path, _ = self._get_model_paths(default_model)
                if not self._validate_and_fix_config_file(config_path):
                    # 默认语音模型也有问题，尝试使用其他可用的语音模型
                    logger.warning(f"默认语音ID {self.default_voice_id} 的配置文件验证失败，将使用备用语音")
                    
                    # 尝试找到一个可用的语音ID
                    for backup_id in ["zh-male1", "zh-female2", "en-female1", "en-male1"]:
                        if backup_id in self.available_voices:
                            logger.info(f"使用备用语音ID: {backup_id}")
                            return backup_id
        except Exception as e:
            logger.warning(f"验证默认语音ID {self.default_voice_id} 时出错: {e}")
        
        return self.default_voice_id
    
    def get_voice_config(self, voice_id: str) -> Dict:
        """
        获取语音配置
        
        Args:
            voice_id: 语音ID
            
        Returns:
            Dict: 语音配置
        """
        voice_id = self.validate_voice_id(voice_id)
        return self.available_voices[voice_id]
    
    def _handle_tts_error(self, text, voice_id, error):
        """
        处理TTS错误，尤其是语言不匹配问题
        
        Args:
            text: 要转换的文本
            voice_id: 语音ID
            error: 错误信息
            
        Returns:
            bool: 是否可以尝试使用备选语音
        """
        error_str = str(error)
        
        # 检查是否是语言不匹配错误
        language_error = (
            "Kernel size can't be greater than actual input size" in error_str or
            "Character not found in the vocabulary" in error_str
        )
        
        # 如果是英文模型但文本包含中文
        if language_error and voice_id.startswith("en-") and any('\u4e00' <= ch <= '\u9fff' for ch in text):
            logger.warning(f"英文语音模型不支持中文字符，建议使用中文语音ID")
            return True
            
        # 如果是中文模型但文本只包含英文
        if language_error and voice_id.startswith("zh-") and not any('\u4e00' <= ch <= '\u9fff' for ch in text):
            logger.warning(f"文本不包含中文，但使用了中文语音模型，可能影响发音质量")
            
        return False
    
    async def text_to_speech(self, 
                           text: str, 
                           voice_id: str = None, 
                           speed: float = 1.0,
                           output_path: str = None) -> Dict[str, Any]:
        """
        将文本转换为语音
        
        Args:
            text: 要转换的文本
            voice_id: 语音ID，例如 zh-female1
            speed: 语速，范围为0.5到2.0
            output_path: 输出文件路径，如果为None则生成临时文件
            
        Returns:
            Dict: 包含生成的音频文件路径和状态的字典
        """
        start_time = time.time()
        self.perf_stats["total_requests"] += 1
        
        try:
            # 确保服务已初始化
            if not self.is_initialized:
                if not self.initialize():
                    return {"status": "error", "message": "离线TTS服务初始化失败"}
            
            # 验证语音ID
            original_voice_id = voice_id
            voice_id = self.validate_voice_id(voice_id)
            voice_config = self.get_voice_config(voice_id)
            
            # 如果语音ID与原始请求不同，记录替换信息
            if original_voice_id and original_voice_id != voice_id and original_voice_id not in [None, 'system', '']:
                logger.info(f"语音ID已从 {original_voice_id} 替换为 {voice_id}")
            
            # 验证语速
            if not 0.5 <= speed <= 2.0:
                raise ValueError(f"语速必须在0.5到2.0之间，当前值: {speed}")
            
            # 生成临时文件名
            if output_path is None:
                temp_dir = tempfile.gettempdir()
                output_path = os.path.join(temp_dir, f"tts_{uuid.uuid4()}.wav")
            
            # 检查缓存
            cached_path = self.tts_cache.get_cached_audio(text, voice_id, speed)
            if cached_path:
                logger.info(f"使用缓存语音: {cached_path}")
                self.perf_stats["cache_hits"] += 1
                
                # 如果需要不同的输出路径，复制缓存文件
                if output_path != cached_path:
                    import shutil
                    try:
                        # 确保输出目录存在
                        os.makedirs(os.path.dirname(output_path), exist_ok=True)
                        shutil.copy2(cached_path, output_path)
                    except Exception as e:
                        logger.warning(f"复制缓存文件时发生错误: {e}")
                        output_path = cached_path
                else:
                    output_path = cached_path
                
                processing_time = time.time() - start_time
                return {
                    "status": "success",
                    "message": "已从缓存生成语音",
                    "output_path": output_path,
                    "voice_id": voice_id,
                    "processing_time": processing_time,
                    "text_length": len(text),
                    "from_cache": True
                }
            
            # 获取语音合成器
            model_name = voice_config["model"]
            logger.info(f"使用模型 {model_name} 合成语音，文本长度: {len(text)} 字符")
            
            try:
                synthesizer = self._get_synthesizer(model_name)
                if not synthesizer:
                    # 如果获取合成器失败，尝试使用替代模型
                    logger.warning(f"获取模型 {model_name} 的合成器失败，尝试使用替代模型")
                    
                    # 尝试找到可用的替代模型
                    for alt_voice in ["zh-male1", "zh-female2", "en-female1", "en-male1"]:
                        if alt_voice != voice_id and alt_voice in self.available_voices:
                            alt_config = self.available_voices[alt_voice]
                            alt_model = alt_config.get("model", "")
                            if alt_model:
                                synthesizer = self._get_synthesizer(alt_model)
                                if synthesizer:
                                    logger.info(f"使用替代模型 {alt_model} (语音ID: {alt_voice})")
                                    voice_id = alt_voice
                                    voice_config = alt_config
                                    model_name = alt_model
                                    break
                    
                    if not synthesizer:
                        return {
                            "status": "error", 
                            "message": f"无法获取有效的语音合成器，请检查TTS模型配置"
                        }
            except Exception as e:
                logger.error(f"获取合成器时发生错误: {e}")
                return {"status": "error", "message": f"获取语音合成器时出错: {str(e)}"}
                
            # 处理多语言XTTS模型
            if "xtts" in model_name.lower():
                try:
                    # XTTS模型特殊处理
                    language = voice_config.get("language", "zh-cn")
                    gender = voice_config.get("gender", "female")
                    
                    logger.info(f"使用XTTS模型合成语音，语言: {language}, 性别: {gender}")
                    
                    # XTTS合成需要指定语言和说话人
                    wavs = synthesizer.tts(
                        text,
                        language=language,
                        speaker_wav=None,
                        speed=speed
                    )
                    
                    synthesizer.save_wav(wavs, output_path)
                except Exception as e:
                    logger.error(f"使用XTTS模型合成语音时出错: {e}")
                    
                    # 检查是否是语言不匹配问题，如果是尝试使用其他模型
                    if self._handle_tts_error(text, voice_id, e):
                        try:
                            logger.info("尝试使用替代模型...")
                            # 尝试使用其他语音模型
                            for alt_voice in ["en-female1", "en-male1"] if "zh-" in voice_id else ["zh-male1", "zh-female2"]:
                                if alt_voice in self.available_voices:
                                    alt_config = self.available_voices[alt_voice]
                                    alt_model = alt_config.get("model", "")
                                    if "xtts" in alt_model.lower():  # 确保仍然使用XTTS模型
                                        alt_synthesizer = self._get_synthesizer(alt_model)
                                        if alt_synthesizer:
                                            alt_language = alt_config.get("language", "en")
                                            logger.info(f"使用替代XTTS模型合成语音，语言: {alt_language}")
                                            
                                            wavs = alt_synthesizer.tts(
                                                text,
                                                language=alt_language,
                                                speaker_wav=None,
                                                speed=speed
                                            )
                                            
                                            alt_synthesizer.save_wav(wavs, output_path)
                                            logger.info(f"使用替代语音 {alt_voice} 成功")
                                            voice_id = alt_voice
                                            break
                            else:
                                raise Exception("没有找到合适的替代XTTS模型")
                        except Exception as alt_e:
                            logger.error(f"使用替代XTTS模型合成语音时出错: {alt_e}")
                            return {"status": "error", "message": f"语音合成失败: {str(e)}"}
            else:
                # 标准TTS模型处理
                try:
                    # 检查是否有特殊语言设置
                    language = voice_config.get("language", None)
                    
                    # Tacotron等标准模型合成
                    outputs = synthesizer.tts(text)
                    
                    if "multilingual" in model_name and language:
                        logger.info(f"使用多语言模型合成语音，指定语言: {language}")
                        outputs = synthesizer.tts(text, language=language)
                        
                    # 调整语速
                    if speed != 1.0:
                        try:
                            import librosa
                            import soundfile as sf
                            
                            y = outputs[0]
                            if speed > 1.0:
                                # 加速
                                y_fast = librosa.effects.time_stretch(y, rate=speed)
                                sf.write(output_path, y_fast, synthesizer.output_sample_rate)
                            else:
                                # 减速
                                y_slow = librosa.effects.time_stretch(y, rate=speed)
                                sf.write(output_path, y_slow, synthesizer.output_sample_rate)
                        except Exception as se:
                            logger.warning(f"调整语速时出错，使用原始语速: {se}")
                            synthesizer.save_wav(outputs, output_path)
                    else:
                        synthesizer.save_wav(outputs, output_path)
                        
                except Exception as e:
                    logger.error(f"使用标准TTS模型合成语音时出错: {e}")
                    
                    # 检查是否是语言不匹配问题，如果是尝试使用其他模型
                    if self._handle_tts_error(text, voice_id, e):
                        try:
                            logger.info("尝试使用替代模型...")
                            # 尝试其他模型
                            for alt_voice in ["en-female1", "en-male1"] if "zh-" in voice_id else ["zh-male1", "zh-female2"]:
                                if alt_voice in self.available_voices:
                                    alt_config = self.available_voices[alt_voice]
                                    alt_synthesizer = self._get_synthesizer(alt_config.get("model", ""))
                                    if alt_synthesizer:
                                        outputs = alt_synthesizer.tts(text)
                                        alt_synthesizer.save_wav(outputs, output_path)
                                        logger.info(f"使用替代语音 {alt_voice} 成功")
                                        voice_id = alt_voice
                                        break
                            else:
                                raise Exception("没有找到合适的替代模型")
                        except Exception as alt_e:
                            logger.error(f"使用替代模型合成语音时出错: {alt_e}")
                            return {"status": "error", "message": f"语音合成失败: {str(e)}"}
            
            # 添加到缓存
            self.tts_cache.add_to_cache(text, voice_id, speed, output_path)
            
            # 计算处理时间
            processing_time = time.time() - start_time
            self.perf_stats["generation_times"].append(processing_time)
            
            logger.info(f"语音合成完成，用时: {processing_time:.2f}秒，输出文件: {output_path}")
            
            return {
                "status": "success",
                "message": "语音合成成功",
                "output_path": output_path,
                "voice_id": voice_id,
                "processing_time": processing_time,
                "text_length": len(text),
                "from_cache": False
            }
            
        except Exception as e:
            logger.error(f"文本转语音过程中出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            
            return {
                "status": "error",
                "message": f"文本转语音失败: {str(e)}"
            }
    
    async def generate_speech(self, 
                            text: str, 
                            voice_id: str = None, 
                            speed: float = 1.0,
                            output_path: str = None) -> Dict[str, Any]:
        """
        将文本转换为语音
        
        Args:
            text: 要转换的文本
            voice_id: 语音ID
            speed: 语速，范围为0.5到2.0
            output_path: 输出文件路径，如果为None则生成临时文件
            
        Returns:
            Dict: 包含生成的音频文件路径和状态的字典
        """
        logger.info(f"调用离线TTS generate_speech方法，文本: '{text[:30]}...'，语音ID: {voice_id}")
        
        try:
            # 确保服务已初始化
            if not self.is_initialized:
                if not self.initialize():
                    return {"status": "error", "message": "离线TTS服务初始化失败"}
            
            # 验证语音ID
            voice_id = self.validate_voice_id(voice_id)
            
            # 确保输出路径有效
            if not output_path:
                temp_dir = tempfile.gettempdir()
                output_path = os.path.join(temp_dir, f"tts_{uuid.uuid4()}.wav")
                logger.info(f"使用临时文件路径: {output_path}")
            
            return await self.text_to_speech(text, voice_id, speed, output_path)
            
        except Exception as e:
            logger.error(f"生成离线语音失败: {e}")
            return {"status": "error", "message": str(e)}
    
    def get_available_voices(self) -> Dict:
        """
        获取可用的语音列表
        
        Returns:
            available_voices: 可用语音字典
        """
        return self.available_voices
    
    def get_performance_stats(self) -> Dict:
        """
        获取性能统计信息
        
        Returns:
            Dict: 性能统计信息
        """
        stats = self.perf_stats.copy()
        if stats["generation_times"]:
            stats["avg_generation_time"] = sum(stats["generation_times"]) / len(stats["generation_times"])
        else:
            stats["avg_generation_time"] = 0
            
        stats["cache_hit_rate"] = 0
        if stats["total_requests"] > 0:
            stats["cache_hit_rate"] = (stats["cache_hits"] / stats["total_requests"]) * 100
            
        # 移除原始时间列表，减少数据量
        if "generation_times" in stats:
            del stats["generation_times"]
            
        return stats

_offline_tts_service = None

def get_offline_tts_service(log_level='info') -> OfflineTTSService:
    """
    获取离线TTS服务实例
    
    Args:
        log_level: 日志级别，可选值: debug, info, warning, error, critical
    
    Returns:
        OfflineTTSService: TTS服务实例
    """
    global _offline_tts_service
    if _offline_tts_service is None:
        _offline_tts_service = OfflineTTSService(log_level=log_level)
    else:
        # 如果实例已存在但想要更改日志级别
        _offline_tts_service.set_log_level(log_level)
    return _offline_tts_service

def initialize(log_level='info') -> bool:
    """
    初始化离线TTS服务
    
    Args:
        log_level: 日志级别，可选值: debug, info, warning, error, critical
    
    Returns:
        bool: 是否成功初始化
    """
    return get_offline_tts_service(log_level).initialize() 