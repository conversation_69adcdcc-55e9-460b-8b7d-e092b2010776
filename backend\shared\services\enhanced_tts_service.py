#!/usr/bin/env python3
"""
增强型文本转语音服务

支持情感化语音合成，具有以下特性：
1. 多平台TTS引擎支持（本地、Azure、ElevenLabs等）
2. 情感映射到语音特性
3. 语音定制
4. 支持SSML标记
"""

import os
import logging
import json
import base64
import tempfile
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple, Union, BinaryIO
import time
from datetime import datetime
import uuid

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EnhancedTTSService:
    """增强型文本转语音服务"""
    
    # 支持的TTS提供商
    PROVIDERS = ['local', 'azure', 'elevenlabs', 'edge_tts']
    
    # 情感到语音特性的默认映射
    DEFAULT_EMOTION_MAPPING = {
        'neutral': {
            'rate': 1.0,      # 正常语速
            'pitch': 0.0,     # 正常音调
            'volume': 1.0,    # 正常音量
            'style': None     # 无特殊风格
        },
        'happy': {
            'rate': 1.1,      # 稍快
            'pitch': 0.5,     # 提高音调
            'volume': 1.2,    # 增加音量
            'style': 'cheerful'
        },
        'sad': {
            'rate': 0.9,      # 稍慢
            'pitch': -0.3,    # 降低音调
            'volume': 0.9,    # 降低音量
            'style': 'sad'
        },
        'angry': {
            'rate': 1.2,      # 更快
            'pitch': 0.3,     # 稍微提高音调
            'volume': 1.4,    # 明显增加音量
            'style': 'angry'
        },
        'surprised': {
            'rate': 1.1,      # 稍快
            'pitch': 0.7,     # 明显提高音调
            'volume': 1.2,    # 增加音量
            'style': 'excited'
        },
        'fearful': {
            'rate': 1.2,      # 更快
            'pitch': 0.2,     # 稍微提高音调
            'volume': 0.9,    # 降低音量
            'style': 'scared'
        },
        'disgusted': {
            'rate': 0.95,     # 稍慢
            'pitch': -0.2,    # 稍微降低音调
            'volume': 0.95,   # 稍微降低音量
            'style': 'unfriendly'
        }
    }
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化TTS服务
        
        Args:
            config: TTS服务配置
        """
        self.config = config or {}
        self.default_provider = self.config.get('default_provider', 'local')
        self.providers = {}
        self.voices = {}
        
        # 加载TTS提供商
        self._load_providers()
        
        # 加载语音列表
        self._load_voices()
    
    def _load_providers(self):
        """加载TTS提供商"""
        providers_config = self.config.get('providers', {})
        
        # 本地TTS
        if 'local' in providers_config:
            try:
                import pyttsx3
                engine = pyttsx3.init()
                self.providers['local'] = {
                    'engine': engine,
                    'config': providers_config.get('local', {})
                }
                logger.info("已加载本地TTS引擎")
            except ImportError:
                logger.warning("未安装pyttsx3库，无法使用本地TTS")
        
        # Azure TTS
        if 'azure' in providers_config:
            try:
                # 示例实现，实际使用时需要导入相应的库
                # from azure.cognitiveservices.speech import SpeechConfig, SpeechSynthesizer
                self.providers['azure'] = {
                    'config': providers_config.get('azure', {})
                }
                logger.info("已加载Azure TTS配置")
            except Exception as e:
                logger.warning(f"加载Azure TTS失败: {e}")
        
        # ElevenLabs TTS
        if 'elevenlabs' in providers_config:
            try:
                self.providers['elevenlabs'] = {
                    'config': providers_config.get('elevenlabs', {})
                }
                logger.info("已加载ElevenLabs TTS配置")
            except Exception as e:
                logger.warning(f"加载ElevenLabs TTS失败: {e}")
        
        # Edge TTS
        if 'edge_tts' in providers_config:
            try:
                self.providers['edge_tts'] = {
                    'config': providers_config.get('edge_tts', {})
                }
                logger.info("已加载Edge TTS配置")
            except Exception as e:
                logger.warning(f"加载Edge TTS失败: {e}")
        
        if not self.providers:
            logger.warning("未加载任何TTS提供商，语音合成功能将不可用")
    
    def _load_voices(self):
        """加载语音列表"""
        # 尝试从voices.json配置文件加载语音
        config_path = os.path.join(os.path.dirname(__file__), '..', 'config', 'voices.json')
        voices_config = {}

        try:
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                    voices_config = config_data.get('enhanced_voices', {})
                    # 保存其他配置信息
                    self.voice_categories = getattr(self, 'voice_categories', {})
                    self.voice_categories.update(config_data.get('voice_categories', {}))
                    self.emotion_presets = getattr(self, 'emotion_presets', {})
                    self.emotion_presets.update(config_data.get('emotion_presets', {}))
                    logger.info(f"已从配置文件加载语音配置: {config_path}")
            else:
                logger.warning(f"语音配置文件不存在: {config_path}")
        except Exception as e:
            logger.error(f"加载语音配置文件失败: {e}")

        # 如果没有从配置文件加载到语音，使用原有逻辑
        if not voices_config:
            voices_config = self.config.get('voices', {})

        for provider in self.providers:
            if provider == 'local':
                # 优先使用配置文件中的语音定义
                if provider in voices_config:
                    self.voices['local'] = voices_config[provider]
                    logger.info(f"已从配置文件加载{len(self.voices['local'])}个本地TTS语音")
                else:
                    # 获取本地TTS引擎的语音
                    try:
                        engine = self.providers['local']['engine']
                        available_voices = engine.getProperty('voices')
                        self.voices['local'] = []

                        for voice in available_voices:
                            voice_data = {
                                'id': voice.id,
                                'name': voice.name,
                                'gender': 'male' if 'male' in voice.name.lower() else 'female',
                                'language': voice.languages[0] if voice.languages else 'unknown',
                                'provider': 'local'
                            }
                            self.voices['local'].append(voice_data)

                        logger.info(f"已动态加载{len(self.voices['local'])}个本地TTS语音")
                    except Exception as e:
                        logger.error(f"加载本地TTS语音失败: {e}")
                        self.voices['local'] = []
            else:
                # 从配置中加载其他提供商的语音
                self.voices[provider] = voices_config.get(provider, [])
                logger.info(f"已从配置加载{len(self.voices[provider])}个{provider}语音")
    
    def get_available_voices(self, provider: str = None) -> List[Dict[str, Any]]:
        """
        获取可用的语音列表
        
        Args:
            provider: 指定TTS提供商，如果为None则返回所有提供商的语音
        
        Returns:
            语音列表
        """
        if provider:
            return self.voices.get(provider, [])
        
        # 返回所有提供商的语音
        all_voices = []
        for p, voices in self.voices.items():
            all_voices.extend(voices)
        
        return all_voices
    
    def get_voice_by_id(self, voice_id: str) -> Optional[Dict[str, Any]]:
        """
        通过ID获取语音
        
        Args:
            voice_id: 语音ID
        
        Returns:
            语音信息，如果未找到则返回None
        """
        for provider, voices in self.voices.items():
            for voice in voices:
                if voice['id'] == voice_id:
                    return voice
        
        return None

    def get_voices_by_category(self, category: str) -> List[Dict[str, Any]]:
        """
        根据分类获取推荐语音

        Args:
            category: 语音分类 (customer_service, teacher, broadcaster, assistant, salesperson)

        Returns:
            推荐语音列表
        """
        if not hasattr(self, 'voice_categories') or category not in self.voice_categories:
            return []

        recommended_voice_ids = self.voice_categories[category].get('recommended_voices', [])
        recommended_voices = []

        for voice_id in recommended_voice_ids:
            voice = self.get_voice_by_id(voice_id)
            if voice:
                recommended_voices.append(voice)

        return recommended_voices

    def get_emotion_preset(self, emotion: str) -> Dict[str, float]:
        """
        获取情感预设参数

        Args:
            emotion: 情感名称

        Returns:
            情感参数字典
        """
        if not hasattr(self, 'emotion_presets'):
            return {'rate': 1.0, 'pitch': 0.0, 'volume': 1.0, 'emphasis': 1.0}

        return self.emotion_presets.get(emotion, {'rate': 1.0, 'pitch': 0.0, 'volume': 1.0, 'emphasis': 1.0})

    def get_voice_categories(self) -> Dict[str, Any]:
        """
        获取所有语音分类

        Returns:
            语音分类字典
        """
        return getattr(self, 'voice_categories', {})

    def get_emotion_presets(self) -> Dict[str, Any]:
        """
        获取所有情感预设

        Returns:
            情感预设字典
        """
        return getattr(self, 'emotion_presets', {})

    def _apply_emotion_to_text(self, text: str, emotion: str, provider: str) -> str:
        """
        将情感应用到文本中（主要用于支持SSML的提供商）
        
        Args:
            text: 原始文本
            emotion: 情感
            provider: TTS提供商
        
        Returns:
            应用了情感的文本（可能是SSML格式）
        """
        if provider == 'azure':
            # Azure TTS支持SSML，可以指定情感
            emotion_style = self.DEFAULT_EMOTION_MAPPING.get(emotion, {}).get('style')
            
            if emotion_style and '<speak' not in text:
                ssml = f"""
                <speak version="1.0" xmlns="http://www.w3.org/2001/10/synthesis" 
                       xmlns:mstts="https://www.w3.org/2001/mstts" xml:lang="zh-CN">
                    <voice name="{{voice_name}}">
                        <mstts:express-as style="{emotion_style}">
                            {text}
                        </mstts:express-as>
                    </voice>
                </speak>
                """
                return ssml.strip()
        
        # 对于不支持SSML的提供商，直接返回原文本
        return text
    
    def _apply_emotion_to_params(self, params: Dict[str, Any], emotion: str) -> Dict[str, Any]:
        """
        将情感参数应用到TTS参数中
        
        Args:
            params: 原始TTS参数
            emotion: 情感
        
        Returns:
            应用了情感的TTS参数
        """
        emotion_params = self.DEFAULT_EMOTION_MAPPING.get(emotion, self.DEFAULT_EMOTION_MAPPING['neutral'])
        
        # 应用情感参数
        for key, value in emotion_params.items():
            if key != 'style':  # style通常通过SSML应用
                if key in params:
                    # 如果参数已存在，则调整而非覆盖
                    if key == 'rate':
                        params[key] *= value  # 调整语速
                    elif key == 'pitch':
                        params[key] += value  # 调整音调
                    elif key == 'volume':
                        params[key] *= value  # 调整音量
                else:
                    # 如果参数不存在，直接设置
                    params[key] = value
        
        return params
    
    def synthesize_speech(
        self, 
        text: str, 
        voice_id: str = None,
        emotion: str = 'neutral',
        params: Dict[str, Any] = None,
        output_format: str = 'wav'
    ) -> Dict[str, Any]:
        """
        将文本转换为语音
        
        Args:
            text: 待转换的文本
            voice_id: 语音ID，如果为None则使用默认语音
            emotion: 情感，用于调整语音特性
            params: 其他TTS参数
            output_format: 输出音频格式
        
        Returns:
            包含音频数据的字典
        """
        if not text:
            return {
                'success': False,
                'error': '文本不能为空',
                'audio_data': None
            }
        
        # 确定要使用的语音
        voice = None
        provider = self.default_provider
        
        if voice_id:
            voice = self.get_voice_by_id(voice_id)
            if voice:
                provider = voice.get('provider', self.default_provider)
        
        # 检查提供商是否可用
        if provider not in self.providers:
            available_providers = list(self.providers.keys())
            if not available_providers:
                return {
                    'success': False,
                    'error': '没有可用的TTS提供商',
                    'audio_data': None
                }
            
            provider = available_providers[0]
            logger.warning(f"指定的TTS提供商不可用，使用{provider}代替")
        
        # 准备TTS参数
        tts_params = params or {}
        
        # 应用情感参数
        tts_params = self._apply_emotion_to_params(tts_params, emotion)
        
        # 应用情感到文本（如SSML）
        text = self._apply_emotion_to_text(text, emotion, provider)
        
        # 根据不同提供商合成语音
        try:
            if provider == 'local':
                return self._synthesize_local(text, voice, tts_params, output_format)
            elif provider == 'azure':
                return self._synthesize_azure(text, voice, tts_params, output_format)
            elif provider == 'elevenlabs':
                return self._synthesize_elevenlabs(text, voice, tts_params, output_format)
            elif provider == 'edge_tts':
                return self._synthesize_edge_tts(text, voice, tts_params, output_format)
            else:
                return {
                    'success': False,
                    'error': f'不支持的TTS提供商: {provider}',
                    'audio_data': None
                }
        except Exception as e:
            logger.error(f"语音合成失败: {e}")
            return {
                'success': False,
                'error': f'语音合成失败: {str(e)}',
                'audio_data': None
            }
    
    def _synthesize_local(
        self, 
        text: str, 
        voice: Dict[str, Any], 
        params: Dict[str, Any], 
        output_format: str
    ) -> Dict[str, Any]:
        """使用本地TTS引擎合成语音"""
        try:
            engine = self.providers['local']['engine']
            
            # 设置语音
            if voice:
                engine.setProperty('voice', voice['id'])
            
            # 设置音量、语速和音调
            if 'volume' in params:
                engine.setProperty('volume', min(max(params['volume'], 0.0), 1.0))
            
            if 'rate' in params:
                engine.setProperty('rate', params['rate'] * 200)  # 典型范围是100-500
            
            # 保存到临时文件
            temp_dir = tempfile.gettempdir()
            temp_file = os.path.join(temp_dir, f"tts_{uuid.uuid4()}.{output_format}")
            
            engine.save_to_file(text, temp_file)
            engine.runAndWait()
            
            # 读取音频数据
            with open(temp_file, 'rb') as f:
                audio_data = f.read()
            
            # 清理临时文件
            os.unlink(temp_file)
            
            return {
                'success': True,
                'audio_data': base64.b64encode(audio_data).decode('utf-8'),
                'audio_format': output_format,
                'duration': None,  # 本地引擎不提供持续时间
                'voice_id': voice['id'] if voice else None,
                'provider': 'local'
            }
        except Exception as e:
            logger.error(f"本地TTS合成失败: {e}")
            raise
    
    def _synthesize_azure(
        self, 
        text: str, 
        voice: Dict[str, Any], 
        params: Dict[str, Any], 
        output_format: str
    ) -> Dict[str, Any]:
        """使用Azure TTS服务合成语音"""
        # 这里是示例实现，实际使用时需要完善
        logger.info("Azure TTS合成尚未完全实现，返回模拟数据")
        
        # 返回模拟数据
        return {
            'success': True,
            'audio_data': None,  # 应该是base64编码的音频数据
            'audio_format': output_format,
            'duration': 1.0,  # 估计的音频持续时间（秒）
            'voice_id': voice['id'] if voice else None,
            'provider': 'azure'
        }
    
    def _synthesize_elevenlabs(
        self, 
        text: str, 
        voice: Dict[str, Any], 
        params: Dict[str, Any], 
        output_format: str
    ) -> Dict[str, Any]:
        """使用ElevenLabs TTS服务合成语音"""
        # 这里是示例实现，实际使用时需要完善
        logger.info("ElevenLabs TTS合成尚未完全实现，返回模拟数据")
        
        # 返回模拟数据
        return {
            'success': True,
            'audio_data': None,  # 应该是base64编码的音频数据
            'audio_format': output_format,
            'duration': 1.0,  # 估计的音频持续时间（秒）
            'voice_id': voice['id'] if voice else None,
            'provider': 'elevenlabs'
        }
    
    def _synthesize_edge_tts(
        self, 
        text: str, 
        voice: Dict[str, Any], 
        params: Dict[str, Any], 
        output_format: str
    ) -> Dict[str, Any]:
        """使用Edge TTS服务合成语音"""
        try:
            # 尝试导入edge-tts
            import edge_tts
            import asyncio
            
            # 异步函数需要包装一下
            async def synthesize():
                voice_name = voice['name'] if voice else "zh-CN-XiaoxiaoNeural"
                communicate = edge_tts.Communicate(text, voice_name)
                
                # 应用参数
                if 'rate' in params:
                    communicate.rate = f"{int((params['rate'] - 1.0) * 100)}%"
                
                if 'volume' in params:
                    communicate.volume = f"{int(params['volume'] * 100)}%"
                
                if 'pitch' in params:
                    # Edge TTS的pitch范围是-100到100
                    pitch_value = int(params['pitch'] * 50)  # 将-1.0到1.0映射到-50到50
                    communicate.pitch = f"{pitch_value}%"
                
                # 保存到临时文件
                temp_dir = tempfile.gettempdir()
                temp_file = os.path.join(temp_dir, f"tts_{uuid.uuid4()}.{output_format}")
                
                await communicate.save(temp_file)
                
                # 读取音频数据
                with open(temp_file, 'rb') as f:
                    audio_data = f.read()
                
                # 清理临时文件
                os.unlink(temp_file)
                
                return audio_data
            
            # 运行异步函数
            audio_data = asyncio.run(synthesize())
            
            return {
                'success': True,
                'audio_data': base64.b64encode(audio_data).decode('utf-8'),
                'audio_format': output_format,
                'duration': None,  # Edge TTS不直接提供持续时间
                'voice_id': voice['id'] if voice else None,
                'provider': 'edge_tts'
            }
        except ImportError:
            logger.error("Edge TTS合成失败: 未安装edge-tts库")
            raise
        except Exception as e:
            logger.error(f"Edge TTS合成失败: {e}")
            raise

# 单例模式
_instance = None

def get_enhanced_tts_service(config: Dict[str, Any] = None) -> EnhancedTTSService:
    """
    获取增强型TTS服务实例
    
    Args:
        config: 服务配置
    
    Returns:
        TTS服务实例
    """
    global _instance
    if _instance is None:
        _instance = EnhancedTTSService(config)
    return _instance 