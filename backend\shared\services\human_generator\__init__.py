# Human Generator Service
from .human_generator import HumanGenerator

# 单例模式生成器实例
_human_generator_instance = None

def get_human_generator() -> HumanGenerator:
    """
    获取数字人生成器实例（单例模式）
    
    Returns:
        HumanGenerator: 数字人生成器实例
    """
    global _human_generator_instance
    
    if _human_generator_instance is None:
        _human_generator_instance = HumanGenerator()
        
    return _human_generator_instance

__all__ = ['HumanGenerator', 'get_human_generator'] 