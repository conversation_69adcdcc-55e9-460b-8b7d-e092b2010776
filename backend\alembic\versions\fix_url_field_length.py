"""Fix URL field length limitations

Revision ID: fix_url_field_length
Revises: 
Create Date: 2024-01-20 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'fix_url_field_length'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    """Upgrade database schema"""
    
    # 修改 digital_humans 表中的URL字段类型
    # 将 String(500) 改为 Text 以支持长URL和base64数据
    
    op.alter_column('digital_humans', 'uploaded_image_url',
                   existing_type=sa.String(500),
                   type_=sa.Text(),
                   existing_nullable=True)
    
    op.alter_column('digital_humans', 'generated_image_url',
                   existing_type=sa.String(500),
                   type_=sa.Text(),
                   existing_nullable=True)
    
    op.alter_column('digital_humans', 'custom_voice_url',
                   existing_type=sa.String(500),
                   type_=sa.Text(),
                   existing_nullable=True)
    
    op.alter_column('digital_humans', 'avatar_video_url',
                   existing_type=sa.String(500),
                   type_=sa.Text(),
                   existing_nullable=True)
    
    op.alter_column('digital_humans', 'voice_sample_url',
                   existing_type=sa.String(500),
                   type_=sa.Text(),
                   existing_nullable=True)


def downgrade():
    """Downgrade database schema"""
    
    # 回滚时将 Text 改回 String(500)
    # 注意：这可能会导致数据截断，如果有超长数据的话
    
    op.alter_column('digital_humans', 'uploaded_image_url',
                   existing_type=sa.Text(),
                   type_=sa.String(500),
                   existing_nullable=True)
    
    op.alter_column('digital_humans', 'generated_image_url',
                   existing_type=sa.Text(),
                   type_=sa.String(500),
                   existing_nullable=True)
    
    op.alter_column('digital_humans', 'custom_voice_url',
                   existing_type=sa.Text(),
                   type_=sa.String(500),
                   existing_nullable=True)
    
    op.alter_column('digital_humans', 'avatar_video_url',
                   existing_type=sa.Text(),
                   type_=sa.String(500),
                   existing_nullable=True)
    
    op.alter_column('digital_humans', 'voice_sample_url',
                   existing_type=sa.Text(),
                   type_=sa.String(500),
                   existing_nullable=True)
