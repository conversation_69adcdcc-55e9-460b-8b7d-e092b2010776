"""
音频处理辅助函数模块
提供音频分析、格式转换和预处理功能
"""

import os
import numpy as np
import logging
import tempfile
from typing import Dict, Any, Optional, Tuple
import traceback

logger = logging.getLogger(__name__)

try:
    from pydub import AudioSegment
    from pydub.silence import detect_nonsilent
    PYDUB_AVAILABLE = True
except ImportError:
    logger.warning("pydub 库未安装，某些音频处理功能将不可用")
    PYDUB_AVAILABLE = False

try:
    import librosa
    import soundfile as sf
    LIBROSA_AVAILABLE = True
except ImportError:
    logger.warning("librosa 或 soundfile 库未安装，高级音频分析功能将不可用")
    LIBROSA_AVAILABLE = False

def analyze_audio_quality(audio_file_path: str) -> Dict[str, Any]:
    """
    分析音频文件质量，检测问题并提供改进建议
    
    Args:
        audio_file_path: 音频文件路径
        
    Returns:
        包含分析结果的字典
    """
    if not os.path.exists(audio_file_path):
        return {
            "status": "error",
            "error_details": "文件不存在",
            "is_recognizable": False,
            "quality_score": 0
        }
        
    if os.path.getsize(audio_file_path) == 0:
        return {
            "status": "error",
            "error_details": "文件大小为0字节",
            "is_recognizable": False,
            "quality_score": 0
        }
    
    result = {
        "status": "success",
        "is_recognizable": True,
        "quality_score": 0,
        "recommendations": [],
        "file_info": {
            "filename": os.path.basename(audio_file_path),
            "size_kb": os.path.getsize(audio_file_path) / 1024,
            "format": os.path.splitext(audio_file_path)[1][1:].lower()
        },
        "audio_properties": {}
    }
    
    # 基本分析
    try:
        if PYDUB_AVAILABLE:
            audio = None
            try:
                # 根据文件扩展名加载音频
                ext = os.path.splitext(audio_file_path)[1][1:].lower()
                if ext in ["mp3", "wav", "ogg", "flac"]:
                    audio = AudioSegment.from_file(audio_file_path)
                    
                    # 基本属性
                    result["audio_properties"]["channels"] = audio.channels
                    result["audio_properties"]["frame_rate"] = audio.frame_rate
                    result["audio_properties"]["sample_width"] = audio.sample_width
                    result["audio_properties"]["duration_seconds"] = len(audio) / 1000
                    result["audio_properties"]["dBFS"] = audio.dBFS
                    
                    # 初始质量分数
                    quality_score = 7
                    
                    # 检查问题并提供建议
                    # 1. 检查声道数
                    if audio.channels > 1:
                        result["recommendations"].append("音频是多声道的，转换为单声道可能会提高识别准确性")
                        quality_score -= 0.5
                    
                    # 2. 检查采样率
                    if audio.frame_rate < 16000:
                        result["recommendations"].append(f"采样率较低 ({audio.frame_rate} Hz)，建议使用至少16kHz的采样率")
                        quality_score -= 1
                    elif audio.frame_rate > 48000:
                        result["recommendations"].append(f"采样率过高 ({audio.frame_rate} Hz)，可以降低到44.1kHz或48kHz以减小文件大小")
                        quality_score -= 0.2
                    
                    # 3. 检查音频音量
                    if audio.dBFS < -30:
                        result["recommendations"].append(f"音频音量较低 ({audio.dBFS:.2f} dBFS)，可以提高音量以增强识别能力")
                        quality_score -= 1.5
                    elif audio.dBFS > -5:
                        result["recommendations"].append(f"音频音量过高 ({audio.dBFS:.2f} dBFS)，可能导致失真，建议降低音量")
                        quality_score -= 1
                    
                    # 4. 检查时长
                    duration = len(audio) / 1000
                    if duration < 1:
                        result["recommendations"].append(f"音频时长过短 ({duration:.2f} 秒)，可能没有足够的内容进行识别")
                        quality_score -= 2
                        result["is_recognizable"] = False
                    elif duration > 600:
                        result["recommendations"].append(f"音频时长较长 ({duration:.2f} 秒)，可能需要较长处理时间")
                        quality_score -= 0.2
                    
                    # 5. 检查静音
                    try:
                        non_silent_ranges = detect_nonsilent(audio, min_silence_len=500, silence_thresh=audio.dBFS-16)
                        non_silent_duration = sum(end - start for start, end in non_silent_ranges) / 1000
                        silence_percentage = 100 - (non_silent_duration / duration * 100)
                        
                        if silence_percentage > 50:
                            result["recommendations"].append(f"音频包含大量静音 ({silence_percentage:.1f}%)，可以裁剪以提高处理效率")
                            quality_score -= 1
                            
                        result["audio_properties"]["silence_percentage"] = silence_percentage
                    except Exception as silence_error:
                        logger.warning(f"检测静音时出错: {str(silence_error)}")
                    
                    # 保存最终质量分数
                    result["quality_score"] = max(0, min(10, quality_score))
                    
                    # 判断是否可识别
                    result["is_recognizable"] = result["quality_score"] >= 4.0
            
            except Exception as pydub_error:
                logger.warning(f"使用pydub分析音频时出错: {str(pydub_error)}")
                # 降级到基本分析
        
        # 如果pydub分析失败或不可用，尝试使用librosa进行高级分析
        if LIBROSA_AVAILABLE and (not PYDUB_AVAILABLE or "audio_properties" not in result or not result["audio_properties"]):
            try:
                y, sr = librosa.load(audio_file_path, sr=None)
                duration = librosa.get_duration(y=y, sr=sr)
                
                result["audio_properties"]["frame_rate"] = sr
                result["audio_properties"]["duration_seconds"] = duration
                result["audio_properties"]["channels"] = 1 if y.ndim == 1 else y.shape[0]
                
                # 计算音量RMS
                rms = librosa.feature.rms(y=y)[0]
                db_rms = 20 * np.log10(np.mean(rms) + 1e-6)
                result["audio_properties"]["dBFS"] = db_rms
                
                # 初始质量分数
                quality_score = 7
                
                # 检查问题并提供建议
                # 1. 检查采样率
                if sr < 16000:
                    result["recommendations"].append(f"采样率较低 ({sr} Hz)，建议使用至少16kHz的采样率")
                    quality_score -= 1
                
                # 2. 检查音频音量
                if db_rms < -30:
                    result["recommendations"].append(f"音频音量较低 ({db_rms:.2f} dB)，可以提高音量以增强识别能力")
                    quality_score -= 1.5
                
                # 3. 检查时长
                if duration < 1:
                    result["recommendations"].append(f"音频时长过短 ({duration:.2f} 秒)，可能没有足够的内容进行识别")
                    quality_score -= 2
                    result["is_recognizable"] = False
                elif duration > 600:
                    result["recommendations"].append(f"音频时长较长 ({duration:.2f} 秒)，可能需要较长处理时间")
                    quality_score -= 0.2
                
                # 保存最终质量分数
                result["quality_score"] = max(0, min(10, quality_score))
                
                # 判断是否可识别
                result["is_recognizable"] = result["quality_score"] >= 4.0
                
            except Exception as librosa_error:
                logger.warning(f"使用librosa分析音频时出错: {str(librosa_error)}")
    
    except Exception as e:
        logger.error(f"分析音频质量时发生错误: {str(e)}")
        logger.error(traceback.format_exc())
        return {
            "status": "error",
            "error_details": f"分析失败: {str(e)}",
            "is_recognizable": False,
            "quality_score": 0
        }
    
    return result

def preprocess_audio(input_file_path: str, output_file_path: Optional[str] = None, 
                   sample_rate: int = 16000, channels: int = 1, 
                   normalize: bool = True) -> Tuple[str, bool]:
    """
    预处理音频文件，转换为适合语音识别的格式
    
    Args:
        input_file_path: 输入音频文件路径
        output_file_path: 输出音频文件路径 (可选，如果未提供将创建临时文件)
        sample_rate: 目标采样率，默认为16kHz (Whisper推荐)
        channels: 目标声道数，默认为1 (单声道)
        normalize: 是否规范化音频音量
        
    Returns:
        (处理后的音频文件路径, 是否成功)
    """
    if not PYDUB_AVAILABLE:
        logger.warning("pydub 未安装，无法预处理音频")
        return input_file_path, False
        
    if not os.path.exists(input_file_path):
        logger.error(f"预处理音频: 文件不存在 - {input_file_path}")
        return input_file_path, False
    
    # 如果未提供输出路径，创建临时文件
    if not output_file_path:
        temp_dir = "temp"
        os.makedirs(temp_dir, exist_ok=True)
        output_file_path = os.path.join(temp_dir, f"preprocessed_{os.path.basename(input_file_path)}")
        # 确保具有WAV扩展名
        if not output_file_path.lower().endswith('.wav'):
            output_file_path = os.path.splitext(output_file_path)[0] + '.wav'
    
    try:
        # 加载音频
        audio = AudioSegment.from_file(input_file_path)
        
        # 转换为所需声道数
        if audio.channels != channels:
            if channels == 1:
                audio = audio.set_channels(1)  # 转换为单声道
            else:
                audio = audio.set_channels(channels)
        
        # 转换为所需采样率
        if audio.frame_rate != sample_rate:
            audio = audio.set_frame_rate(sample_rate)
        
        # 规范化音量
        if normalize:
            audio = audio.normalize()
        
        # 导出为WAV
        audio.export(output_file_path, format="wav")
        
        logger.info(f"音频预处理成功: {input_file_path} -> {output_file_path}")
        return output_file_path, True
        
    except Exception as e:
        logger.error(f"预处理音频时出错: {str(e)}")
        logger.error(traceback.format_exc())
        return input_file_path, False

def convert_audio_format(input_file_path: str, output_format: str = "mp3", 
                        output_file_path: Optional[str] = None) -> Tuple[str, bool]:
    """
    转换音频文件格式
    
    Args:
        input_file_path: 输入音频文件路径
        output_format: 目标格式 (mp3, wav, ogg)
        output_file_path: 输出文件路径 (可选)
        
    Returns:
        (输出文件路径, 是否成功)
    """
    if not os.path.exists(input_file_path):
        logger.error(f"转换音频格式: 文件不存在 - {input_file_path}")
        return input_file_path, False
    
    # 如果未提供输出路径，创建新路径
    if not output_file_path:
        output_file_path = os.path.splitext(input_file_path)[0] + f".{output_format}"
    
    # 确保目标目录存在
    os.makedirs(os.path.dirname(os.path.abspath(output_file_path)), exist_ok=True)
    
    try:
        # 尝试使用pydub转换
        if PYDUB_AVAILABLE:
            try:
                audio = AudioSegment.from_file(input_file_path)
                audio.export(output_file_path, format=output_format)
                logger.info(f"使用pydub转换音频格式成功: {output_file_path}")
                return output_file_path, True
            except Exception as pydub_error:
                logger.warning(f"使用pydub转换音频格式失败: {str(pydub_error)}")
                # 继续尝试其他方法
        
        # 尝试使用ffmpeg
        try:
            import subprocess
            cmd = [
                "ffmpeg", "-i", input_file_path, 
                "-y",  # 覆盖输出文件
                "-loglevel", "error"  # 仅显示错误
            ]
            
            # 添加格式特定选项
            if output_format == "mp3":
                cmd.extend(["-acodec", "libmp3lame", "-q:a", "4"])
            elif output_format == "wav":
                cmd.extend(["-acodec", "pcm_s16le"])
            elif output_format == "ogg":
                cmd.extend(["-acodec", "libvorbis", "-q:a", "4"])
            
            # 添加输出文件路径
            cmd.append(output_file_path)
            
            # 执行命令
            result = subprocess.run(cmd, check=True, capture_output=True)
            logger.info(f"使用ffmpeg转换音频格式成功: {output_file_path}")
            return output_file_path, True
            
        except Exception as ffmpeg_error:
            logger.warning(f"使用ffmpeg转换音频格式失败: {str(ffmpeg_error)}")
            # 降级到简单复制
            try:
                import shutil
                shutil.copy2(input_file_path, output_file_path)
                logger.warning(f"直接复制音频文件: {output_file_path}")
                return output_file_path, False
            except Exception as copy_error:
                logger.error(f"复制音频文件失败: {str(copy_error)}")
                return input_file_path, False
                
    except Exception as e:
        logger.error(f"转换音频格式时出错: {str(e)}")
        logger.error(traceback.format_exc())
        return input_file_path, False

def format_time(seconds: float) -> str:
    """
    将秒数格式化为 MM:SS 格式
    
    Args:
        seconds: 时间秒数
        
    Returns:
        格式化的时间字符串
    """
    minutes, seconds = divmod(seconds, 60)
    return f"{int(minutes)}:{int(seconds):02d}"

def detect_speech_segments(audio_file_path: str) -> list:
    """
    检测音频文件中的语音段落
    
    Args:
        audio_file_path: 音频文件路径
        
    Returns:
        语音段落列表，每个段落包含开始和结束时间
    """
    if not PYDUB_AVAILABLE:
        logger.warning("pydub 未安装，无法检测语音段落")
        return []
    
    try:
        # 加载音频
        audio = AudioSegment.from_file(audio_file_path)
        
        # 检测非静音部分
        non_silent_ranges = detect_nonsilent(
            audio, 
            min_silence_len=500,  # 500ms 以上视为静音
            silence_thresh=audio.dBFS - 16  # 比平均音量低 16dB 视为静音
        )
        
        # 转换为秒
        segments = [(start / 1000, end / 1000) for start, end in non_silent_ranges]
        return segments
        
    except Exception as e:
        logger.error(f"检测语音段落时出错: {str(e)}")
        return [] 