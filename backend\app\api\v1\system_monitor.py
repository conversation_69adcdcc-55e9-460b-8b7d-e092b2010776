#!/usr/bin/env python3
"""
系统监控API
提供系统状态、性能指标和健康检查
"""

from fastapi import APIRouter, HTTPException
from fastapi.responses import JSONResponse
import logging
from typing import Dict, Any

from app.services.performance_optimizer import performance_optimizer
from app.services.multilingual_service import multilingual_service
from app.services.speech_recognition_service import speech_recognition_service
from app.services.realtime_conversation import conversation_manager

logger = logging.getLogger(__name__)
router = APIRouter()

@router.get("/health")
async def health_check():
    """健康检查"""
    try:
        # 检查各个服务状态
        services_status = {
            "api": "healthy",
            "ollama": await check_ollama_status(),
            "speech_recognition": await check_speech_recognition_status(),
            "conversation_manager": check_conversation_manager_status(),
            "multilingual": check_multilingual_status()
        }
        
        # 判断整体健康状态
        overall_status = "healthy"
        for service, status in services_status.items():
            if status != "healthy":
                overall_status = "degraded"
                break
        
        return JSONResponse({
            "status": overall_status,
            "timestamp": performance_optimizer.performance_monitor.start_time,
            "services": services_status
        })
        
    except Exception as e:
        logger.error(f"❌ 健康检查失败: {e}")
        return JSONResponse({
            "status": "unhealthy",
            "error": str(e)
        }, status_code=500)

@router.get("/performance")
async def get_performance_metrics():
    """获取性能指标"""
    try:
        optimization_status = performance_optimizer.get_optimization_status()
        
        return JSONResponse({
            "success": True,
            "data": optimization_status
        })
        
    except Exception as e:
        logger.error(f"❌ 获取性能指标失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/gpu")
async def get_gpu_status():
    """获取GPU状态"""
    try:
        gpu_status = performance_optimizer.gpu_manager.get_gpu_status()
        
        return JSONResponse({
            "success": True,
            "data": gpu_status
        })
        
    except Exception as e:
        logger.error(f"❌ 获取GPU状态失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/cache")
async def get_cache_status():
    """获取缓存状态"""
    try:
        cache_stats = performance_optimizer.cache_manager.get_stats()
        
        return JSONResponse({
            "success": True,
            "data": cache_stats
        })
        
    except Exception as e:
        logger.error(f"❌ 获取缓存状态失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/cache/clear")
async def clear_cache():
    """清空缓存"""
    try:
        performance_optimizer.cache_manager.clear()
        
        return JSONResponse({
            "success": True,
            "message": "缓存已清空"
        })
        
    except Exception as e:
        logger.error(f"❌ 清空缓存失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/conversations")
async def get_conversation_stats():
    """获取对话统计"""
    try:
        active_sessions = len(conversation_manager.sessions)
        session_details = []
        
        for session_id, session in conversation_manager.sessions.items():
            session_details.append({
                "session_id": session_id,
                "is_active": session.is_active,
                "is_listening": session.is_listening,
                "is_generating": session.is_generating,
                "conversation_length": len(session.conversation_history),
                "digital_human_name": session.digital_human_config.get("name", "Unknown")
            })
        
        return JSONResponse({
            "success": True,
            "data": {
                "active_sessions": active_sessions,
                "sessions": session_details
            }
        })
        
    except Exception as e:
        logger.error(f"❌ 获取对话统计失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/languages")
async def get_language_support():
    """获取语言支持信息"""
    try:
        supported_languages = multilingual_service.get_supported_languages()
        
        return JSONResponse({
            "success": True,
            "data": {
                "supported_languages": supported_languages,
                "total_languages": len(supported_languages)
            }
        })
        
    except Exception as e:
        logger.error(f"❌ 获取语言支持信息失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/ollama")
async def get_ollama_status():
    """获取Ollama状态"""
    try:
        import requests
        
        # 检查Ollama连接
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        
        if response.status_code == 200:
            models_data = response.json()
            models = [model["name"] for model in models_data.get("models", [])]
            
            return JSONResponse({
                "success": True,
                "data": {
                    "status": "running",
                    "available_models": models,
                    "model_count": len(models),
                    "endpoint": "http://localhost:11434"
                }
            })
        else:
            return JSONResponse({
                "success": False,
                "data": {
                    "status": "error",
                    "error": f"HTTP {response.status_code}"
                }
            })
            
    except Exception as e:
        logger.error(f"❌ 获取Ollama状态失败: {e}")
        return JSONResponse({
            "success": False,
            "data": {
                "status": "offline",
                "error": str(e)
            }
        })

@router.post("/optimize")
async def optimize_system():
    """优化系统性能"""
    try:
        # 清理缓存中的过期项
        performance_optimizer.cache_manager.clear()
        
        # 重新优化GPU设置
        performance_optimizer.gpu_manager.optimize_for_inference()
        
        # 获取优化后的状态
        status = performance_optimizer.get_optimization_status()
        
        return JSONResponse({
            "success": True,
            "message": "系统优化完成",
            "data": status
        })
        
    except Exception as e:
        logger.error(f"❌ 系统优化失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# 辅助函数
async def check_ollama_status() -> str:
    """检查Ollama状态"""
    try:
        import requests
        response = requests.get("http://localhost:11434/api/tags", timeout=3)
        return "healthy" if response.status_code == 200 else "unhealthy"
    except:
        return "offline"

async def check_speech_recognition_status() -> str:
    """检查语音识别状态"""
    try:
        if speech_recognition_service.is_initialized:
            return "healthy"
        else:
            # 尝试初始化
            success = await speech_recognition_service.initialize()
            return "healthy" if success else "unavailable"
    except:
        return "error"

def check_conversation_manager_status() -> str:
    """检查对话管理器状态"""
    try:
        # 简单检查对话管理器是否可用
        session_count = len(conversation_manager.sessions)
        return "healthy"
    except:
        return "error"

def check_multilingual_status() -> str:
    """检查多语言服务状态"""
    try:
        languages = multilingual_service.get_supported_languages()
        return "healthy" if languages else "error"
    except:
        return "error"
