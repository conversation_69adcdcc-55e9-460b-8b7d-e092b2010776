import cv2
import numpy as np
import mediapipe as mp
import logging
from typing import Dict, List, Optional, Tuple, Any

logger = logging.getLogger(__name__)

class LandmarksDetector:
    """
    面部关键点检测器，使用MediaPipe提取面部特征点
    """
    
    def __init__(self):
        """初始化面部关键点检测器"""
        self.mp_face_mesh = mp.solutions.face_mesh
        self.mp_drawing = mp.solutions.drawing_utils
        self.mp_drawing_styles = mp.solutions.drawing_styles
        
        # 关键点索引定义，基于MediaPipe的FaceMesh模型
        # 这些索引代表面部的不同部位
        self.FACE_OVAL = [
            10, 338, 297, 332, 284, 251, 389, 356, 454, 323, 361, 288,
            397, 365, 379, 378, 400, 377, 152, 148, 176, 149, 150, 136,
            172, 58, 132, 93, 234, 127, 162, 21, 54, 103, 67, 109
        ]
        
        self.LIPS = [
            61, 146, 91, 181, 84, 17, 314, 405, 321, 375, 291, 308, 324, 318, 402, 317, 14, 87, 178, 88, 95, 
            185, 40, 39, 37, 0, 267, 269, 270, 409, 415, 310, 311, 312, 13, 82, 81, 42, 183, 78
        ]
        
        self.LEFT_EYE = [
            263, 249, 390, 373, 374, 380, 381, 382, 362, 398, 384, 385, 386, 387, 388, 466, 263
        ]
        
        self.RIGHT_EYE = [
            33, 7, 163, 144, 145, 153, 154, 155, 133, 173, 157, 158, 159, 160, 161, 246, 33
        ]
        
        self.LEFT_EYEBROW = [
            276, 283, 282, 295, 300, 293, 334, 296, 336
        ]
        
        self.RIGHT_EYEBROW = [
            46, 53, 52, 65, 70, 63, 105, 66, 107
        ]
        
        self.NOSE = [
            168, 6, 197, 195, 5, 4, 45, 220, 115, 218, 219, 49, 97, 2, 326, 327
        ]
        
        logger.info("面部关键点检测器已初始化")
    
    def detect_landmarks(self, image_path: str) -> Dict[str, Any]:
        """
        检测图片中的面部关键点
        
        Args:
            image_path: 图片文件路径
            
        Returns:
            Dict[str, Any]: 检测结果
        """
        try:
            # 读取图片
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError(f"无法读取图片: {image_path}")
            
            # 转换为RGB（MediaPipe需要RGB格式）
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            h, w, _ = image.shape
            
            # 使用MediaPipe进行关键点检测
            with self.mp_face_mesh.FaceMesh(
                static_image_mode=True,
                max_num_faces=1,
                min_detection_confidence=0.5
            ) as face_mesh:
                results = face_mesh.process(image_rgb)
                
                if not results.multi_face_landmarks:
                    logger.warning(f"未在图片中检测到面部关键点: {image_path}")
                    return {
                        "success": False,
                        "message": "未检测到面部关键点",
                        "landmarks": None
                    }
                
                # 提取第一个检测到的面部的关键点
                landmarks = results.multi_face_landmarks[0]
                
                # 转换关键点为图像坐标
                landmarks_dict = self._landmarks_to_dict(landmarks, w, h)
                
                # 绘制带关键点的调试图像
                debug_image_path = image_path.rsplit(".", 1)[0] + "_landmarks." + image_path.rsplit(".", 1)[1]
                self._draw_landmarks(image, landmarks, debug_image_path)
                
                return {
                    "success": True,
                    "message": "面部关键点检测成功",
                    "landmarks": landmarks_dict,
                    "debug_image": debug_image_path
                }
                
        except Exception as e:
            logger.error(f"面部关键点检测失败: {str(e)}")
            return {
                "success": False,
                "message": f"面部关键点检测失败: {str(e)}",
                "landmarks": None
            }
    
    def calculate_facial_metrics(self, landmarks_dict: Dict[str, List[Tuple[float, float]]]) -> Dict[str, float]:
        """
        计算面部特征指标
        
        Args:
            landmarks_dict: 关键点字典
            
        Returns:
            Dict[str, float]: 面部指标
        """
        if not landmarks_dict or not all(key in landmarks_dict for key in 
                                       ['face_oval', 'left_eye', 'right_eye', 'nose', 'lips']):
            return {}
        
        # 提取关键区域的点
        face_oval = landmarks_dict['face_oval']
        left_eye = landmarks_dict['left_eye']
        right_eye = landmarks_dict['right_eye']
        nose = landmarks_dict['nose']
        lips = landmarks_dict['lips']
        
        # 计算眼间距
        left_eye_center = self._calculate_center(left_eye)
        right_eye_center = self._calculate_center(right_eye)
        eye_distance = self._calculate_distance(left_eye_center, right_eye_center)
        
        # 计算脸部宽度
        face_width = max(p[0] for p in face_oval) - min(p[0] for p in face_oval)
        
        # 计算脸部高度
        face_height = max(p[1] for p in face_oval) - min(p[1] for p in face_oval)
        
        # 计算嘴部宽度
        lips_width = max(p[0] for p in lips) - min(p[0] for p in lips)
        
        # 计算指标
        metrics = {
            "eye_distance_ratio": eye_distance / face_width,  # 眼距/脸宽比例
            "face_width_height_ratio": face_width / face_height,  # 脸宽/脸高比例
            "lips_width_ratio": lips_width / face_width,  # 嘴宽/脸宽比例
        }
        
        return metrics
    
    def _landmarks_to_dict(self, landmarks, img_width, img_height) -> Dict[str, List[Tuple[float, float]]]:
        """
        将MediaPipe关键点转换为字典格式
        
        Args:
            landmarks: MediaPipe关键点
            img_width: 图像宽度
            img_height: 图像高度
            
        Returns:
            Dict[str, List[Tuple[float, float]]]: 按区域分组的关键点字典
        """
        # 将所有关键点转换为图像坐标
        points = []
        for lm in landmarks.landmark:
            x, y = int(lm.x * img_width), int(lm.y * img_height)
            points.append((x, y))
        
        # 按区域分组关键点
        result = {
            "face_oval": [points[i] for i in self.FACE_OVAL],
            "lips": [points[i] for i in self.LIPS],
            "left_eye": [points[i] for i in self.LEFT_EYE],
            "right_eye": [points[i] for i in self.RIGHT_EYE],
            "left_eyebrow": [points[i] for i in self.LEFT_EYEBROW],
            "right_eyebrow": [points[i] for i in self.RIGHT_EYEBROW],
            "nose": [points[i] for i in self.NOSE],
            "all_points": points
        }
        
        return result
    
    def _draw_landmarks(self, image, landmarks, output_path):
        """
        在图像上绘制面部关键点，并保存
        
        Args:
            image: 原始图像
            landmarks: MediaPipe关键点
            output_path: 输出图像路径
        """
        # 复制图像以避免修改原始图像
        debug_img = image.copy()
        
        # 转换为RGB用于绘制
        debug_img = cv2.cvtColor(debug_img, cv2.COLOR_BGR2RGB)
        
        # 绘制所有关键点
        self.mp_drawing.draw_landmarks(
            image=debug_img,
            landmark_list=landmarks,
            connections=self.mp_face_mesh.FACEMESH_TESSELATION,
            landmark_drawing_spec=None,
            connection_drawing_spec=self.mp_drawing_styles.get_default_face_mesh_tesselation_style()
        )
        
        # 单独绘制眼睛
        self.mp_drawing.draw_landmarks(
            image=debug_img,
            landmark_list=landmarks,
            connections=self.mp_face_mesh.FACEMESH_CONTOURS,
            landmark_drawing_spec=None,
            connection_drawing_spec=self.mp_drawing_styles.get_default_face_mesh_contours_style()
        )
        
        # 转换回BGR用于保存
        debug_img = cv2.cvtColor(debug_img, cv2.COLOR_RGB2BGR)
        
        # 保存图像
        cv2.imwrite(output_path, debug_img)
        logger.info(f"已保存带关键点的图像: {output_path}")
    
    def _calculate_center(self, points: List[Tuple[float, float]]) -> Tuple[float, float]:
        """计算一组点的中心点"""
        if not points:
            return (0, 0)
        
        x_sum = sum(p[0] for p in points)
        y_sum = sum(p[1] for p in points)
        return (x_sum / len(points), y_sum / len(points))
    
    def _calculate_distance(self, point1: Tuple[float, float], point2: Tuple[float, float]) -> float:
        """计算两点之间的欧氏距离"""
        return np.sqrt((point1[0] - point2[0])**2 + (point1[1] - point2[1])**2) 