from pydantic import BaseModel
from typing import Optional, Dict, Any

class VideoRequest(BaseModel):
    """视频请求模型，用于WAN 2.1视频生成"""
    task_id: Optional[str] = None
    prompt: str
    negative_prompt: Optional[str] = ""
    num_frames: Optional[int] = 16
    height: Optional[int] = 576
    width: Optional[int] = 1024
    num_inference_steps: Optional[int] = 25
    guidance_scale: Optional[float] = 9.0
    seed: Optional[int] = None
    output_format: Optional[str] = "mp4"
    fps: Optional[int] = 8
    model_size: Optional[str] = "1.3B"
    use_mock: Optional[bool] = False
    force_real_mode: Optional[bool] = False

class VideoResponse(BaseModel):
    """视频响应模型"""
    success: bool
    task_id: str
    video_url: Optional[str] = None
    thumbnail_url: Optional[str] = None
    generation_time: Optional[float] = None
    error: Optional[str] = None
    details: Optional[str] = None

class CheckModelRequest(BaseModel):
    """检查模型请求"""
    model_type: str
    model_size: Optional[str] = "1.3B"
    resolution: Optional[str] = "720p"

class CheckModelResponse(BaseModel):
    """检查模型响应"""
    status: str
    message: Optional[str] = None
    model_path: Optional[str] = None
    download_guide: Optional[str] = None
    timestamp: Optional[str] = None 