#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
语言学习语料导入工具

使用方法:
python import_language_corpus.py --name "商务英语语料库" --language "english" --file input.csv --user_id 1
"""

import sys
import os
import argparse
import csv
import json
from datetime import datetime

# 添加项目根目录到 sys.path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError

from utils.db import get_db, Base, engine
from models.corpus import Corpus, CorpusEntry

def create_corpus(db: Session, name: str, language: str, description: str, owner_id: int) -> Corpus:
    """创建新的语料库"""
    corpus = Corpus(
        name=name,
        description=description,
        type="language_learning",
        language=language,
        is_public=True,  # 默认为公开，可供智能体使用
        owner_id=owner_id
    )
    db.add(corpus)
    db.commit()
    db.refresh(corpus)
    return corpus

def add_corpus_entries(db: Session, corpus_id: int, entries: list, language: str) -> int:
    """添加语料条目"""
    count = 0
    for entry in entries:
        corpus_entry = CorpusEntry(
            corpus_id=corpus_id,
            content=entry,
            language=language,
            source="language_learning_import"
        )
        db.add(corpus_entry)
        count += 1
    
    db.commit()
    return count

def import_from_csv(file_path: str, corpus_id: int, language: str, db: Session) -> int:
    """从CSV文件导入语料"""
    entries = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            for row in reader:
                if row and len(row) > 0 and row[0].strip():
                    entries.append(row[0].strip())
    except Exception as e:
        print(f"读取CSV文件失败: {str(e)}")
        return 0
    
    return add_corpus_entries(db, corpus_id, entries, language)

def import_from_json(file_path: str, corpus_id: int, language: str, db: Session) -> int:
    """从JSON文件导入语料"""
    entries = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
            
            # 支持多种JSON格式
            if isinstance(data, list):
                # 直接的条目列表
                for item in data:
                    if isinstance(item, str):
                        entries.append(item)
                    elif isinstance(item, dict) and "content" in item:
                        entries.append(item["content"])
            elif isinstance(data, dict):
                # 包含场景的结构化数据
                if "entries" in data:
                    for entry in data["entries"]:
                        if isinstance(entry, str):
                            entries.append(entry)
                        elif isinstance(entry, dict) and "content" in entry:
                            entries.append(entry["content"])
                # 类似商务英语场景的结构
                elif "scenarios" in data:
                    for scenario in data["scenarios"]:
                        if "title" in scenario:
                            entries.append(scenario["title"])
                        if "description" in scenario:
                            entries.append(scenario["description"])
                        if "difficulty_levels" in scenario:
                            for level, content in scenario["difficulty_levels"].items():
                                if "vocabulary" in content:
                                    entries.extend(content["vocabulary"])
                                if "phrases" in content:
                                    entries.extend(content["phrases"])
    except Exception as e:
        print(f"读取JSON文件失败: {str(e)}")
        return 0
    
    return add_corpus_entries(db, corpus_id, entries, language)

def import_from_txt(file_path: str, corpus_id: int, language: str, db: Session) -> int:
    """从纯文本文件导入语料"""
    entries = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            for line in lines:
                line = line.strip()
                if line:
                    entries.append(line)
    except Exception as e:
        print(f"读取文本文件失败: {str(e)}")
        return 0
    
    return add_corpus_entries(db, corpus_id, entries, language)

def main():
    parser = argparse.ArgumentParser(description='导入语言学习语料库')
    parser.add_argument('--name', type=str, required=True, help='语料库名称')
    parser.add_argument('--language', type=str, required=True, help='语料库语言 (如 english, chinese)')
    parser.add_argument('--description', type=str, default='', help='语料库描述')
    parser.add_argument('--file', type=str, required=True, help='导入文件路径 (.csv, .json, .txt)')
    parser.add_argument('--user_id', type=int, required=True, help='所有者用户ID')
    
    args = parser.parse_args()
    
    # 检查文件是否存在
    if not os.path.exists(args.file):
        print(f"错误: 文件 {args.file} 不存在")
        return 1
    
    # 获取文件扩展名
    _, ext = os.path.splitext(args.file)
    ext = ext.lower()
    
    if ext not in ['.csv', '.json', '.txt']:
        print(f"错误: 不支持的文件格式 {ext}，支持的格式为 .csv, .json, .txt")
        return 1
    
    # 连接数据库
    try:
        db = next(get_db())
        
        # 创建语料库
        print(f"创建语料库: {args.name}")
        corpus = create_corpus(db, args.name, args.language, args.description, args.user_id)
        print(f"语料库创建成功，ID: {corpus.corpus_id}")
        
        # 根据文件格式导入语料
        entry_count = 0
        if ext == '.csv':
            entry_count = import_from_csv(args.file, corpus.corpus_id, args.language, db)
        elif ext == '.json':
            entry_count = import_from_json(args.file, corpus.corpus_id, args.language, db)
        elif ext == '.txt':
            entry_count = import_from_txt(args.file, corpus.corpus_id, args.language, db)
        
        # 更新语料库条目数量
        corpus.entry_count = entry_count
        db.commit()
        
        print(f"导入完成，成功导入 {entry_count} 条语料")
        return 0
    
    except SQLAlchemyError as e:
        print(f"数据库错误: {str(e)}")
        return 1
    except Exception as e:
        print(f"导入失败: {str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 