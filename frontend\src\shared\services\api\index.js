/**
 * 统一API服务入口
 * 导出API工具函数和请求方法
 */

import { ElMessage, ElNotification } from 'element-plus';
import api, { buildApiUrl } from '@/services/api';
import auth from '@/services/auth';
import { getApiBaseUrl, generateApiUrlCandidates } from '@/config/env';
import axios from 'axios';
import config from '@/config';
// 避免循环依赖，在使用时动态获取userStore
// import { useUserStore } from '@/store/modules/user'; 
import router from '@/router';

// 导入资源路径工具
import { getAssetUrl } from '@/utils/asset-path';

// 获取用户存储，避免循环依赖
function getUserStore() {
  try {
    // 动态导入可能会导致问题，使用全局pinia实例
    if (window.__pinia && window.__pinia.state.value.user) {
      return window.__pinia.state.value.user;
    }
    return null;
  } catch (e) {
    console.warn('获取用户存储失败:', e);
    return null;
  }
}

// 导入auth模块中的认证功能
import { getAuthHeader, isLoggedIn, getCurrentUser, hasRole, hasPermission, diagnoseAndFixAuthIssues } from '@/auth';

/**
 * 创建API客户端
 * @returns {Object} API客户端实例
 */
function createApiClient() {
  // 强制使用相对路径，让Vite代理处理所有API请求
  const apiBaseUrl = '';  // 使用空字符串，所有请求都是相对路径

  console.log(`[API] 创建API客户端, 使用相对路径模式`);
  console.log(`[API] API基础URL: ${apiBaseUrl || '(相对路径)'}`);

  const client = axios.create({
    baseURL: apiBaseUrl, // 使用空字符串，让Vite代理处理
    timeout: 60000, // 请求超时时间 (修改为60秒，提高稳定性)
    headers: {
      'Content-Type': 'application/json'
    }
  });

  // 添加请求拦截器，进行认证令牌检查
  client.interceptors.request.use(
    config => {
      console.log(`[API请求] ${config.method?.toUpperCase()} ${config.url}`, {
        headers: config.headers,
        params: config.params,
        data: config.data
      });
      
      // 检查认证头
      const authHeader = config.headers.Authorization || config.headers.authorization;
      
      // 从localStorage获取token，不依赖userStore
      let token = localStorage.getItem('auth_token') || localStorage.getItem('token');
      
      // 如果没有找到token，尝试从sessionStorage获取
      if (!token) {
        token = sessionStorage.getItem('auth_token') || sessionStorage.getItem('token');
      }
      
      // 如果找到了token
      if (token) {
        // 检查令牌格式并规范化
        try {
          // 检查令牌是否需要规范化格式
          const normalizeToken = (token) => {
            // 先移除所有Bearer前缀，避免重复
            let cleanToken = token;
            while (cleanToken.toLowerCase().startsWith('bearer ')) {
              cleanToken = cleanToken.replace(/^bearer\s+/i, '');
            }
            
            // 移除多余的引号
            if (cleanToken.includes('"')) {
              cleanToken = cleanToken.replace(/"/g, '');
            }
            
            // 添加单个Bearer前缀
            if (cleanToken.includes('.')) {
              return `Bearer ${cleanToken}`;
            }
            
            return cleanToken;
          };
          
          // 规范化令牌
          const normalizedToken = normalizeToken(token);
          if (normalizedToken !== token) {
            console.log('[API请求] 规范化令牌格式:', 
              token.substring(0, 10) + '... => ' + 
              normalizedToken.substring(0, 10) + '...');
              
            // 更新存储的令牌
            if (localStorage.getItem('auth_token') === token) {
              localStorage.setItem('auth_token', normalizedToken);
            }
            if (localStorage.getItem('token') === token) {
              localStorage.setItem('token', normalizedToken);
            }
            if (sessionStorage.getItem('auth_token') === token) {
              sessionStorage.setItem('auth_token', normalizedToken);
            }
            if (sessionStorage.getItem('token') === token) {
              sessionStorage.setItem('token', normalizedToken);
            }
            
            // 使用规范化的令牌
            token = normalizedToken;
          }
        } catch (tokenFormatError) {
          console.warn('[API请求] 规范化令牌格式失败:', tokenFormatError);
        }
        
        // 如果没有认证头但请求API，则添加认证头
        if (!authHeader && config.url && config.url.includes('/api/')) {
          console.log(`[API请求] 自动添加认证头: ${token.substring(0, 15)}...`);
          config.headers.Authorization = token;
        }
      } else if (config.url && config.url.includes('/api/')) {
        console.error('[API请求] 未找到认证令牌，API请求可能会被拒绝');
      }
      
      // 为数字人相关API添加auth_token查询参数（某些后端实现可能需要）
      if (config.url && config.url.includes('/digital-human/')) {
        // 确保params对象已初始化
        config.params = config.params || {};
        
        // 如果认证头中有令牌，提取并添加为查询参数
        if (config.headers.Authorization) {
          const authToken = config.headers.Authorization.replace(/^bearer\s+/i, '');
          config.params.auth_token = authToken;
        }
      }
      
      // 在开发模式下打印请求信息
      if (process.env.NODE_ENV === 'development') {
        console.log(`[API] ${config.method.toUpperCase()} ${config.url}`, config.data || '');
      }
      
      return config;
    },
    error => {
      console.error('[API请求拦截器] 请求错误:', error);
      return Promise.reject(error);
    }
  );

  // 添加响应拦截器，处理认证错误
  client.interceptors.response.use(
    response => {
      console.log(`[API响应] ${response.config.method?.toUpperCase()} ${response.config.url}`, {
        status: response.status,
        statusText: response.statusText,
        data: response.data
      });
      
      // 添加更详细的响应数据日志，帮助调试
      console.log(`[API响应详情] ${response.config.url} 数据类型:`, typeof response.data);
      if (typeof response.data === 'string' && response.data.startsWith('{')) {
        console.log('[API响应详情] 检测到JSON字符串，尝试解析');
        try {
          const parsedData = JSON.parse(response.data);
          console.log('[API响应详情] 解析后:', parsedData);
        } catch (e) {
          console.warn('[API响应详情] JSON解析失败:', e);
        }
      }
      
      return response;
    },
    error => {
      if (error.response) {
        console.error(`[API响应错误] ${error.config?.method?.toUpperCase()} ${error.config?.url}`, {
          status: error.response.status,
          statusText: error.response.statusText,
          data: error.response.data
        });
        
        // 处理401错误
        if (error.response.status === 401) {
          console.warn('[API响应] 收到401未授权错误，检查认证状态');
          
          // 检查是否存在令牌
          const hasToken = localStorage.getItem('auth_token') || localStorage.getItem('token') || 
                          sessionStorage.getItem('auth_token') || sessionStorage.getItem('token');
          
          if (hasToken) {
            console.error('[API响应] 认证状态不一致：前端有令牌但后端拒绝访问');
            
            // 清除令牌
            localStorage.removeItem('auth_token');
            localStorage.removeItem('token');
            sessionStorage.removeItem('auth_token');
            sessionStorage.removeItem('token');
            localStorage.removeItem('user_info');
            
            // 如果有路由实例，重定向到登录页
            if (router) {
              router.push('/user/login?redirect=' + encodeURIComponent(window.location.pathname));
            } else {
              // 如果没有路由实例，使用window.location重定向
              window.location.href = '/user/login?redirect=' + encodeURIComponent(window.location.pathname);
            }
          }
        }
      } else {
        console.error('[API响应错误] 请求失败', error.message);
      }
      
      return Promise.reject(error);
    }
  );

  return client;
}

/**
 * 创建取消令牌
 * 用于取消正在进行的请求
 * @returns {Object} 包含token和cancel方法的对象
 */
export function createCancelToken() {
  const source = axios.CancelToken.source();
  return {
    token: source.token,
    cancel: source.cancel
  };
}

/**
 * 检查错误是否由取消请求引起
 * @param {Error} error - 捕获的错误
 * @returns {boolean} 是否是取消请求导致的错误
 */
export function isCancel(error) {
  return axios.isCancel(error);
}

/**
 * 获取API基础URL，增强版本包含API状态检查
 * @returns {string} API基础URL
 */
export function getApiBaseUrlWithStatus() {
  // 如果检测到有效URL，优先使用它
  const apiStatus = window.__API_STATUS;
  if (apiStatus && apiStatus.url) {
    return apiStatus.url;
  }
  
  // 使用环境变量中配置的API基础URL
  return import.meta.env.VITE_API_BASE_URL || '/api';
}

/**
 * 获取完整API URL
 * @param {string} endpoint - API端点路径
 * @returns {string} 完整API URL
 */
function getFullApiUrl(endpoint) {
  // 检查是否直接访问后端
  const isDirectBackend = window.location.port === '8000';
  
  // 如果直接访问后端且endpoint不以/api开头，添加/api前缀
  if (isDirectBackend && endpoint && !endpoint.startsWith('/api') && !endpoint.startsWith('http')) {
    endpoint = '/api' + (endpoint.startsWith('/') ? endpoint : `/${endpoint}`);
    console.log(`[API] 直接访问后端模式，调整API路径为: ${endpoint}`);
  }
  
  // 使用buildApiUrl处理最终路径
  return buildApiUrl(endpoint);
}

/**
 * 从Response中提取数据
 * @param {Response} response - Axios响应对象
 * @returns {any} 响应数据
 */
function extractResponseData(response) {
  // 如果响应是Axios格式，提取data字段
  if (response && response.data !== undefined) {
    return response.data;
  }
  // 否则直接返回响应
  return response;
}

/**
 * 处理API错误
 * @param {Error} error - 错误对象
 * @param {string} defaultMessage - 默认错误消息
 * @returns {Promise<never>} 始终拒绝的Promise
 */
function handleApiError(error, defaultMessage = '请求失败') {
  console.error('[API] 错误:', error);
  
  // 提取错误消息
  let errorMessage = defaultMessage;
  
  // 检查是否是数字人列表请求
  const isDigitalHumansRequest = error.config && 
                               error.config.url && 
                               error.config.url.includes('/api/digital-humans');
  
  if (error.response) {
    // 服务器返回了错误响应
    const status = error.response.status;
    const data = error.response.data;
    
    // 特殊处理数字人列表404错误
    if (isDigitalHumansRequest && status === 404) {
      console.warn('数字人列表接口返回404，使用空数组作为默认值');
      return Promise.resolve([]);
    }
    
    // 提取详细错误信息
    if (data) {
      // 处理422错误，通常包含验证错误详情
      if (status === 422 && data.errors) {
        try {
          // 尝试提取所有验证错误
          const validationErrors = [];
          if (Array.isArray(data.errors)) {
            // 数组格式的错误
            data.errors.forEach(err => {
              if (typeof err === 'string') {
                validationErrors.push(err);
              } else if (err.msg || err.message) {
                validationErrors.push(err.msg || err.message);
              } else if (err.param && err.value) {
                validationErrors.push(`${err.param}: ${err.value} - ${err.msg || '无效'}`);
              }
            });
          } else if (typeof data.errors === 'object') {
            // 对象格式的错误
            Object.keys(data.errors).forEach(key => {
              const err = data.errors[key];
              if (typeof err === 'string') {
                validationErrors.push(`${key}: ${err}`);
              } else if (Array.isArray(err)) {
                err.forEach(e => validationErrors.push(`${key}: ${e}`));
              }
            });
          }
          
          if (validationErrors.length > 0) {
            errorMessage = `验证错误: ${validationErrors.join('; ')}`;
          } else {
            errorMessage = data.message || '请求数据验证失败';
          }
        } catch (parseError) {
          console.warn('解析验证错误失败:', parseError);
          errorMessage = data.message || '请求数据验证失败';
        }
      } else if (data.message || data.error || data.detail) {
        errorMessage = data.message || data.error || data.detail;
      } else {
        // 根据状态码生成错误消息
        switch (status) {
          case 400: errorMessage = '请求参数错误'; break;
          case 401: errorMessage = '未授权，请登录'; break;
          case 403: errorMessage = '禁止访问'; break;
          case 404: errorMessage = '请求的资源不存在'; break;
          case 422: errorMessage = '请求数据验证失败'; break;
          case 500: errorMessage = '服务器内部错误'; break;
          default: errorMessage = `服务器错误 (${status})`;
        }
      }
    }
  } else if (error.request) {
    // 请求已发送但没有收到响应
    errorMessage = '服务器无响应';
    
    // 特殊处理数字人列表网络错误
    if (isDigitalHumansRequest) {
      console.warn('数字人列表接口网络错误，使用空数组作为默认值');
      return Promise.resolve([]);
    }
  } else if (error.message) {
    // 请求设置时出错
    errorMessage = error.message;
  }
  
  // 显示错误消息
  ElMessage.error(errorMessage);
  
  // 返回拒绝的Promise
  return Promise.reject(new Error(errorMessage));
}

// 创建API客户端实例
const apiClient = createApiClient();

/**
 * 通用API请求函数
 * @param {string} method - 请求方法 (GET, POST, PUT, DELETE)
 * @param {string} endpoint - API端点
 * @param {Object|FormData|null} data - 请求数据
 * @param {Object} params - URL参数
 * @param {Object} options - 请求配置选项
 * @returns {Promise} - 返回Promise对象
 */
export function apiRequest(method, endpoint, data = null, params = {}, options = {}) {
  // 规范化endpoint格式
  const normalizedEndpoint = normalizeEndpoint(endpoint);
  
  // 构建请求配置
  const config = {
    ...(options || {}),
    headers: {
      'Content-Type': 'application/json',
      ...(options.headers || {})
    },
    params: params || {}
  };
  
  // 从auth模块获取认证头
  const authHeader = getAuthHeader();
  if (authHeader) {
    // 将认证头合并到请求头中
    config.headers = {
      ...config.headers,
      ...authHeader
    };
    
    console.log(`[统一API] 使用认证头: ${JSON.stringify(authHeader)}`);
  } else {
    console.warn('[统一API] 未找到有效的认证头');
  }
  
  // 判断是否为FormData（文件上传）
  if (data instanceof FormData) {
    // 对于FormData请求，不应设置Content-Type，让浏览器自动设置带boundary的值
    delete config.headers['Content-Type'];
  }
  
  // 记录请求信息（调试用）
  console.log(`[统一API] ${method} ${normalizedEndpoint}`, {
    params: params,
    headers: config.headers
  });
  
  let finalEndpoint = normalizedEndpoint;
  
  // 直接访问后端模式处理
  if (window.location.port === '8000') {
    // 在直接访问后端模式下，确保URL与FastAPI路由匹配
    const originalUrl = normalizedEndpoint;
    
    // FastAPI通常期望URL末尾有斜杠
    if (normalizedEndpoint !== '/api' && !normalizedEndpoint.endsWith('/')) {
      finalEndpoint = normalizedEndpoint + '/';
      console.log(`[直接访问后端] 已添加末尾斜杠: '${originalUrl}' -> '${finalEndpoint}'`);
    }
    
    // 在直接访问后端模式下，移除/api前缀，因为FastAPI已经配置了路由
    if (finalEndpoint.startsWith('/api/')) {
      const pathWithPrefix = finalEndpoint;
      finalEndpoint = finalEndpoint.substring(4); // 去除'/api'前缀
      console.log(`[直接访问后端] 移除/api前缀: '${pathWithPrefix}' -> '${finalEndpoint}'`);
    }
    
    // 对于FastAPI直接访问模式，确保令牌格式正确，使用Bearer前缀
    if (authHeader && authHeader.Authorization) {
      const token = authHeader.Authorization;
      // 确保Authorization包含Bearer前缀
      if (!token.startsWith('Bearer ')) {
        config.headers.Authorization = `Bearer ${token}`;
        console.log(`[直接访问后端] 添加Bearer前缀到令牌`);
      }
    }
    
    console.log(`[直接访问后端] 最终请求URL: '${finalEndpoint}'`);
  } else {
    // 非直接访问模式，确保术语API等请求使用相对路径
    if (finalEndpoint.includes('terminology')) {
      console.log(`[API请求] 检测到术语API请求: ${finalEndpoint}`);
      
      // 确保使用相对路径
      if (finalEndpoint.includes('http://') || finalEndpoint.includes('https://')) {
        const url = new URL(finalEndpoint);
        finalEndpoint = url.pathname;
        console.log(`[API请求] 将术语API请求转换为相对路径: ${finalEndpoint}`);
      }
      
      // 确保术语API请求不会被直接发送到后端服务器
      if (window.location.port === '8000') {
        console.warn('[API请求] 检测到术语API请求尝试直接访问后端服务器，强制使用前端代理');
        // 修改请求配置，确保使用前端代理
        config.baseURL = '/api';
        // 确保包含认证头
        if (authHeader) {
          console.log('[API请求] 确保术语API请求包含认证头');
          config.headers = {
            ...config.headers,
            ...authHeader
          };
        }
      }
    }
  }
  
  // 根据方法类型发送请求
  let request;
  switch (method.toUpperCase()) {
    case 'GET':
      request = apiClient.get(finalEndpoint, config);
      break;
    case 'POST':
      request = apiClient.post(finalEndpoint, data, config);
      break;
    case 'PUT':
      request = apiClient.put(finalEndpoint, data, config);
      break;
    case 'DELETE':
      request = apiClient.delete(finalEndpoint, config);
      break;
    default:
      return Promise.reject(new Error(`不支持的请求方法: ${method}`));
  }
  
  // 处理响应和错误
  return request
    .then(response => {
      console.log(`[统一API响应] ${method} ${finalEndpoint}:`, {
        status: response.status
      });
      return extractResponseData(response);
    })
    .catch(error => {
      // 记录错误详情
      console.error(`[API错误] ${method} ${finalEndpoint}:`, error);
      
      // 检查错误是否已经包含响应信息
      if (error.response) {
        console.error(`[API响应错误] ${method} ${finalEndpoint}:`, {
          status: error.response.status,
          statusText: error.response.statusText,
          data: error.response.data
        });
        
        // 特殊处理401未授权错误
        if (error.response.status === 401) {
          console.log('[API响应] 收到401未授权错误，检查认证状态');
          
          // 检查是否有认证头但仍然收到401，这表明令牌可能已过期
          if (authHeader) {
            console.log('[API响应] 认证状态不一致：前端有令牌但后端拒绝访问');
            
            // 特殊处理术语API的401错误
            if (finalEndpoint.includes('terminology')) {
              console.log('[API响应] 术语API认证失败，返回友好的错误对象');
              return Promise.resolve({
                _error: {
                  status: 401,
                  message: '需要登录才能访问术语API'
                }
              });
            }
            
            // 尝试刷新令牌
            try {
              // 使用动态import而不是require
              return import('@/auth')
                .then(auth => {
                  return auth.refreshAccessToken()
                    .then(() => {
                      console.log('[API响应] 令牌已刷新，重试请求');
                      // 重新获取认证头
                      const newAuthHeader = getAuthHeader();
                      if (newAuthHeader) {
                        // 更新请求配置
                        config.headers = {
                          ...config.headers,
                          ...newAuthHeader
                        };
                        // 重试请求
                        return apiRequest(method, endpoint, data, params, config);
                      }
                      // 如果无法获取新令牌，继续抛出错误
                      return Promise.reject(error);
                    })
                    .catch(refreshError => {
                      console.error('[API响应] 刷新令牌失败:', refreshError);
                      return Promise.reject(error);
                    });
                })
                .catch(importError => {
                  console.error('[API响应] 导入auth模块失败:', importError);
                  return Promise.reject(error);
                });
            } catch (e) {
              console.error('[API响应] 尝试刷新令牌时出错:', e);
              return Promise.reject(error);
            }
          }
        }
        
        // 直接返回错误，保留所有属性
        return Promise.reject(error);
      }
      
      // 使用统一的错误处理函数处理其他类型的错误
      return handleApiErrorOld(error, `${method} ${finalEndpoint} 请求失败`);
    });
}

/**
 * 规范化API端点路径
 * @param {string} endpoint - 原始端点路径
 * @returns {string} 规范化的端点路径
 */
function normalizeEndpoint(endpoint) {
  if (!endpoint) {
    return '/api';
  }
  
  // 去除首尾空格
  endpoint = endpoint.trim();
  
  // 确保以/开头
  if (!endpoint.startsWith('/')) {
    endpoint = '/' + endpoint;
  }
  
  // 处理所有可能的API前缀重复情况
  const originalEndpoint = endpoint;
  
  // 首先检查是否有多重/api前缀
  const apiPrefixRegex = /^(\/api)+/;
  if (apiPrefixRegex.test(endpoint) && endpoint !== '/api') {
    // 计算/api前缀出现的次数
    const match = endpoint.match(/^(\/api)+/)[0];
    if (match.length > 4) { // 超过一个/api
      // 保留一个/api前缀，提取剩余路径
      const remainingPath = endpoint.substring(match.length);
      endpoint = '/api' + remainingPath;
      console.log(`[URL规范化] 修复多重/api前缀: '${originalEndpoint}' -> '${endpoint}'`);
    }
  } else if (!endpoint.startsWith('/api')) {
    // 没有/api前缀，添加一个
    endpoint = '/api' + endpoint;
    console.log(`[URL规范化] 添加/api前缀: '${originalEndpoint}' -> '${endpoint}'`);
  }
  
  // 修复特殊情况: /api/api/
  if (endpoint.startsWith('/api/api/')) {
    endpoint = endpoint.replace('/api/api/', '/api/');
    console.log(`[URL规范化] 修复特殊情况 /api/api/: '${originalEndpoint}' -> '${endpoint}'`);
  }
  
  // 记录最终的URL
  console.log(`[URL规范化] ${endpoint}`);
  
  return endpoint;
}

/**
 * 检查登录状态
 * @returns {boolean} 是否已登录
 */
export function checkLoginStatus() {
  return auth.isLoggedIn();
}

/**
 * 获取当前用户信息
 * @returns {Object|null} 用户信息或null
 */
export function getUserInfo() {
  return auth.getUserInfo();
}

/**
 * 检查用户权限
 * @param {string|Array<string>} permission - 需要的权限
 * @returns {boolean} 是否拥有权限
 */
export function checkPermission(permission) {
  return auth.hasPermission(permission);
}

/**
 * 检查用户角色
 * @param {string|Array<string>} role - 需要的角色
 * @returns {boolean} 是否拥有角色
 */
export function checkRole(role) {
  return auth.hasRole(role);
}

/**
 * 检查是否处于模拟API模式
 * @returns {boolean} 是否应该使用模拟数据
 */
export function isMockMode() {
  // 检查全局设置
  if (window.__PREFER_MOCK_DATA === true) {
    return true;
  }
  
  // 检查localStorage设置
  if (localStorage.getItem('__PREFER_MOCK_DATA') === 'true') {
    return true;
  }
  
  // 检查是否通过URL参数强制使用模拟模式
  if (new URLSearchParams(window.location.search).get('mock') === 'true') {
    return true;
  }
  
  // 检查环境变量
  if (import.meta.env.VITE_USE_MOCK_API === 'true') {
    return true;
  }
  
  // 默认使用真实数据，不使用模拟模式
  return false;
}

/**
 * DELETE请求
 * @param {string} url - 请求URL
 * @param {Object} config - 其他配置
 * @returns {Promise<any>} 响应数据
 */
export async function deleteRequest(url, config = {}) {
  try {
    const response = await apiClient.delete(url, config);
    return extractResponseData(response);
  } catch (error) {
    return handleApiError(error);
  }
}

/**
 * 文件上传请求
 * @param {string} url - 上传URL
 * @param {FormData} formData - 表单数据
 * @param {Function} onProgress - 进度回调
 * @param {Object} config - 其他配置
 * @returns {Promise<any>} 响应数据
 */
export async function uploadRequest(url, formData, onProgress, config = {}) {
  try {
    const uploadConfig = {
      ...config,
      headers: {
        ...(config.headers || {}),
        'Content-Type': 'multipart/form-data'
      }
    };
    
    if (onProgress) {
      uploadConfig.onUploadProgress = (progressEvent) => {
        const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
        onProgress(percentCompleted, progressEvent);
      };
    }
    
    const response = await apiClient.post(url, formData, uploadConfig);
    return extractResponseData(response);
  } catch (error) {
    return handleApiError(error, '文件上传失败');
  }
}

// 导出API客户端对象
export const apiClientObject = {
  client: apiClient,
  
  /**
   * 发起GET请求
   * @param {string} url - 请求URL
   * @param {Object} params - 请求参数
   * @param {Object} config - 请求配置
   * @returns {Promise} - 请求Promise
   */
  get(url, params = {}, config = {}) {
    return this.client.get(url, { ...config, params });
  },
  
  /**
   * 发起POST请求
   * @param {string} url - 请求URL
   * @param {Object} data - 请求数据
   * @param {Object} config - 请求配置
   * @returns {Promise} - 请求Promise
   */
  post(url, data = {}, config = {}) {
    return this.client.post(url, data, config);
  },
  
  /**
   * 发起PUT请求
   * @param {string} url - 请求URL
   * @param {Object} data - 请求数据
   * @param {Object} config - 请求配置
   * @returns {Promise} - 请求Promise
   */
  put(url, data = {}, config = {}) {
    return this.client.put(url, data, config);
  },
  
  /**
   * 发起DELETE请求
   * @param {string} url - 请求URL
   * @param {Object} config - 请求配置
   * @returns {Promise} - 请求Promise
   */
  delete(url, config = {}) {
    return this.client.delete(url, config);
  },
  
  /**
   * 创建WebSocket连接
   * @param {string} path - WebSocket路径
   * @returns {WebSocket} - WebSocket实例
   */
  createWebSocket(path) {
    // 获取token
    const token = localStorage.getItem('token') || localStorage.getItem('auth_token') || '';
    
    // 构建WebSocket URL
    let wsUrl;
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const currentHost = window.location.host;
    
    // 使用当前页面的主机名和端口
    if (path.startsWith('/api')) {
      wsUrl = `${protocol}//${currentHost}${path}?token=${encodeURIComponent(token)}`;
    } else {
      wsUrl = `${protocol}//${currentHost}/api${path}?token=${encodeURIComponent(token)}`;
    }
    
    console.log('[API] 创建WebSocket连接:', wsUrl.replace(/token=([^&]*)/, 'token=***'));
    
    return new WebSocket(wsUrl);
  }
};

// apiClientObject的createWebSocket方法
const createWebSocket = apiClientObject.createWebSocket;

// 导出该函数
export { createWebSocket };

// 导出所有功能
export default {
  get,
  post,
  put,
  delete: del,
  upload,
  isLoggedIn: checkLoginStatus,
  getUserInfo,
  hasPermission: checkPermission,
  hasRole: checkRole,
  getApiBaseUrlWithStatus,
  getFullApiUrl,
  getAssetUrl,
  isMockMode,
  deleteRequest,
  uploadRequest,
  createCancelToken,
  isCancel,
  createWebSocket
};

/**
 * 确保存在认证令牌
 * 在直接访问后端时，需要确保令牌已正确设置
 */
function ensureAuthToken() {
  // 检查是否直接访问后端
  const isDirectBackend = window.location.port === '8000';
  
  if (!isDirectBackend) {
    // 如果不是直接访问后端，不需要特殊处理
    return;
  }
  
  console.log('[API] 检查直接访问后端的认证令牌');
  
  // 检查localStorage或sessionStorage中是否有令牌
  const token = localStorage.getItem('auth_token') || 
               localStorage.getItem('token') || 
               sessionStorage.getItem('auth_token') || 
               sessionStorage.getItem('token');
  
  if (!token) {
    console.warn('[API] 未找到认证令牌，API请求可能会被拒绝');
    
    // 提示用户登录
    ElNotification({
      title: '认证提醒',
      message: '您当前直接访问后端API服务器，但未检测到有效的认证令牌。请先登录以获取授权。',
      type: 'warning',
      duration: 10000
    });
    
    // 可以选择重定向到登录页面
    // router.push('/login');
  } else {
    console.log('[API] 检测到有效令牌，API请求将携带此令牌');
    
    // 提示用户认证状态
    ElNotification({
      title: '认证状态',
      message: '已检测到认证令牌，直接访问后端API模式已启用。',
      type: 'success',
      duration: 5000
    });
  }
}

// 应用启动时检查认证令牌
ensureAuthToken();

/**
 * 发起GET请求
 * @param {string} url - 请求URL
 * @param {Object} params - 请求参数
 * @param {Object} config - 请求配置
 * @returns {Promise} - 请求Promise
 */
export function get(url, params = {}, config = {}) {
  return apiRequest('GET', url, null, params, config);
}

/**
 * 发起POST请求
 * @param {string} url - 请求URL
 * @param {Object} data - 请求数据
 * @param {Object} config - 请求配置
 * @returns {Promise} - 请求Promise
 */
export function post(url, data = {}, config = {}) {
  // 视频生成特殊处理 - 使用更长的超时时间
  if (url.includes('/video-generation/') || url.includes('/wan-video/')) {
    // 包含视频生成相关端点的请求使用10分钟超时
    const videoConfig = {
      ...config,
      timeout: 600000 // 10分钟超时
    };
    return apiRequest('POST', url, data, {}, videoConfig);
  }
  
  return apiRequest('POST', url, data, {}, config);
}

/**
 * 发起PUT请求
 * @param {string} url - 请求URL
 * @param {Object} data - 请求数据
 * @param {Object} config - 请求配置
 * @returns {Promise} - 请求Promise
 */
export function put(url, data = {}, config = {}) {
  return apiRequest('PUT', url, data, {}, config);
}

/**
 * 发起DELETE请求
 * @param {string} url - 请求URL
 * @param {Object} config - 请求配置
 * @returns {Promise} - 请求Promise
 */
export function del(url, config = {}) {
  return apiRequest('DELETE', url, null, {}, config);
}

/**
 * 上传文件
 * @param {string} url - 上传URL
 * @param {FormData} formData - 表单数据
 * @param {Function} onProgress - 进度回调
 * @param {Object} config - 请求配置
 * @returns {Promise} - 请求Promise
 */
export function upload(url, formData, onProgress = null, config = {}) {
  const uploadConfig = {
    ...config,
    headers: {
      ...(config.headers || {})
      // 不再设置Content-Type，让apiRequest自动处理
    }
  };
  
  if (onProgress) {
    uploadConfig.onUploadProgress = (progressEvent) => {
      const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
      onProgress(percentCompleted, progressEvent);
    };
  }
  
  return apiRequest('POST', url, formData, {}, uploadConfig);
}

// 错误处理函数
function handleApiErrorOld(error, defaultMessage = '请求失败') {
  console.error('[API] 错误:', error);
  
  // 提取错误消息
  let errorMessage = defaultMessage;
  
  // 检查是否是数字人列表请求
  const isDigitalHumansRequest = error.config && 
                               error.config.url && 
                               error.config.url.includes('/api/digital-humans');
  
  if (error.response) {
    // 服务器返回了错误响应
    const status = error.response.status;
    const data = error.response.data;
    
    // 特殊处理数字人列表404错误
    if (isDigitalHumansRequest && status === 404) {
      console.warn('数字人列表接口返回404，使用空数组作为默认值');
      return Promise.resolve([]);
    }
    
    // 提取详细错误信息
    if (data) {
      // 处理422错误，通常包含验证错误详情
      if (status === 422 && data.errors) {
        try {
          // 尝试提取所有验证错误
          const validationErrors = [];
          if (Array.isArray(data.errors)) {
            // 数组格式的错误
            data.errors.forEach(err => {
              if (typeof err === 'string') {
                validationErrors.push(err);
              } else if (err.msg || err.message) {
                validationErrors.push(err.msg || err.message);
              } else if (err.param && err.value) {
                validationErrors.push(`${err.param}: ${err.value} - ${err.msg || '无效'}`);
              }
            });
          } else if (typeof data.errors === 'object') {
            // 对象格式的错误
            Object.keys(data.errors).forEach(key => {
              const err = data.errors[key];
              if (typeof err === 'string') {
                validationErrors.push(`${key}: ${err}`);
              } else if (Array.isArray(err)) {
                err.forEach(e => validationErrors.push(`${key}: ${e}`));
              }
            });
          }
          
          if (validationErrors.length > 0) {
            errorMessage = `验证错误: ${validationErrors.join('; ')}`;
          } else {
            errorMessage = data.message || '请求数据验证失败';
          }
        } catch (parseError) {
          console.warn('解析验证错误失败:', parseError);
          errorMessage = data.message || '请求数据验证失败';
        }
      } else if (data.message || data.error || data.detail) {
        errorMessage = data.message || data.error || data.detail;
      } else {
        // 根据状态码生成错误消息
        switch (status) {
          case 400: errorMessage = '请求参数错误'; break;
          case 401: errorMessage = '未授权，请登录'; break;
          case 403: errorMessage = '禁止访问'; break;
          case 404: errorMessage = '请求的资源不存在'; break;
          case 422: errorMessage = '请求数据验证失败'; break;
          case 500: errorMessage = '服务器内部错误'; break;
          default: errorMessage = `服务器错误 (${status})`;
        }
      }
    }
  } else if (error.request) {
    // 请求已发送但没有收到响应
    errorMessage = '服务器无响应';
    
    // 特殊处理数字人列表网络错误
    if (isDigitalHumansRequest) {
      console.warn('数字人列表接口网络错误，使用空数组作为默认值');
      return Promise.resolve([]);
    }
  } else if (error.message) {
    // 请求设置时出错
    errorMessage = error.message;
  }
  
  // 显示错误消息
  ElMessage.error(errorMessage);
  
  // 返回拒绝的Promise
  return Promise.reject(new Error(errorMessage));
}