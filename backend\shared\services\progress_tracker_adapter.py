import logging
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from .progress_tracker import ProgressTracker, get_progress_tracker as get_original_tracker

# 配置日志
logger = logging.getLogger(__name__)

class ProgressTrackerAdapter:
    """适配器类，使ProgressTracker兼容digital_human.py的调用方式"""
    
    def __init__(self, tracker: ProgressTracker):
        """初始化适配器，包装真实的ProgressTracker实例"""
        self._tracker = tracker
        logger.info("ProgressTrackerAdapter已初始化")
    
    async def _notify_websockets(self, task_id: str, task: Dict[str, Any]):
        """适配_notify_websockets调用，实际调用_broadcast_to_websockets"""
        logger.info(f"通过适配器为任务 {task_id} 广播WebSocket消息")
        return await self._tracker._broadcast_to_websockets(task_id, task)
    
    async def create_task(self, task_id: str, task_type: str = None, **params):
        """创建任务"""
        logger.info(f"通过适配器创建任务: {task_id}, 类型: {task_type}, 参数: {params}")
        return await self._tracker.create_task(task_id, task_type, params)
    
    async def update_task(self, task_id: str, **kwargs):
        """更新任务，适配digital_human.py的调用"""
        # 提取支持的参数
        status = kwargs.get('status')
        progress = kwargs.get('progress')
        result = kwargs.get('result')
        error = kwargs.get('error')
        
        # 处理message参数
        message = kwargs.get('message')
        logs = None
        if message:
            logs = [{"timestamp": datetime.now().isoformat(), "message": message, "type": "info"}]
        
        return await self._tracker.update_task(
            task_id, 
            status=status, 
            progress=progress, 
            result=result, 
            error=error,
            logs=logs
        )
    
    async def get_task_status(self, task_id: str):
        """获取任务状态，适配get_task_status调用"""
        task = await self._tracker.get_task(task_id)
        if not task:
            return None
        
        # 转换为digital_human.py期望的格式
        task_status = task.copy()
        
        # 确保包含digital_human_id
        if 'digital_human_id' not in task_status and 'params' in task_status:
            task_status['digital_human_id'] = task_status['params'].get('digital_human_id', 'unknown')
        
        # 添加消息
        if 'message' not in task_status:
            status = task_status.get('status', 'unknown')
            task_status['message'] = f"任务状态: {status}"
        
        return task_status
    
    async def add_log(self, task_id: str, log_type: str, message: str):
        """添加日志，适配digital_human.py的调用"""
        log = {
            "timestamp": datetime.now().isoformat(),
            "type": log_type,
            "message": message
        }
        
        task = await self._tracker.get_task(task_id)
        if not task:
            logger.warning(f"尝试为不存在的任务 {task_id} 添加日志")
            return None
        
        logs = task.get('logs', [])
        logs.append(log)
        
        return await self._tracker.update_task(task_id, logs=[log])
    
    # 添加get_progress方法解决"无法从缓存获取任务进度: 'ProgressTracker' object has no attribute 'get_progress'"错误
    async def get_progress(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        获取任务进度，实际调用get_task方法
        
        此方法添加是为了兼容task_scheduler.py中对get_progress的调用
        
        参数:
            task_id: 任务ID
            
        返回:
            包含任务进度信息的字典，如果任务不存在则返回None
        """
        logger.debug(f"通过get_progress获取任务进度: {task_id}")
        return await self._tracker.get_task(task_id)
    
    # 转发其他方法到原始tracker
    async def get_task(self, task_id: str):
        return await self._tracker.get_task(task_id)
    
    async def list_tasks(self, **kwargs):
        return await self._tracker.list_tasks(**kwargs)
    
    async def delete_task(self, task_id: str):
        return await self._tracker.delete_task(task_id)
    
    async def register_websocket(self, task_id: str, websocket):
        return await self._tracker.register_websocket(task_id, websocket)
    
    async def unregister_websocket(self, task_id: str, websocket):
        return await self._tracker.unregister_websocket(task_id, websocket)

# 适配后的获取进度跟踪器函数
_adapted_tracker = None

def get_progress_tracker() -> ProgressTrackerAdapter:
    """获取适配后的进度跟踪器单例"""
    global _adapted_tracker
    if _adapted_tracker is None:
        original_tracker = get_original_tracker()
        _adapted_tracker = ProgressTrackerAdapter(original_tracker)
    return _adapted_tracker 