import os
import sys
import logging
import numpy as np
import cv2
from pathlib import Path

# 添加项目根目录到Python路径
current_file = os.path.abspath(__file__)
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_file)))
sys.path.append(project_root)

from services.avatar_generation import AvatarGenerator

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def generate_model_thumbnails():
    """生成所有模型的缩略图"""
    try:
        # 初始化头像生成器
        generator = AvatarGenerator()
        
        # 前端缩略图目录
        thumbnail_dir = os.path.join(project_root, "frontend", "public", "assets", "thumbnails")
        Path(thumbnail_dir).mkdir(parents=True, exist_ok=True)
        
        # 获取可用模型列表
        models = ["default", "business", "cartoon", "realistic"]
        
        # 为每个模型生成缩略图
        for model_id in models:
            # 设置输出路径
            output_path = os.path.join(thumbnail_dir, f"{model_id}.jpg")
            
            # 初始化模型
            generator.initialize_avatar_model(model_id)
            
            # 生成预览图像
            preview_path = generator.generate_preview_image()
            
            if preview_path and os.path.exists(preview_path):
                # 调整大小为缩略图尺寸
                img = cv2.imread(preview_path)
                thumbnail = cv2.resize(img, (300, 200))
                
                # 保存缩略图
                cv2.imwrite(output_path, thumbnail)
                logger.info(f"成功生成缩略图: {output_path}")
                
                # 清理临时文件
                os.remove(preview_path)
            else:
                logger.error(f"为模型 '{model_id}' 生成预览图失败")
    
    except Exception as e:
        logger.error(f"生成缩略图失败: {e}")

def generate_placeholder_thumbnails():
    """生成占位缩略图"""
    try:
        # 前端缩略图目录
        thumbnail_dir = os.path.join(project_root, "frontend", "public", "assets", "thumbnails")
        Path(thumbnail_dir).mkdir(parents=True, exist_ok=True)
        
        # 模型配置
        models = [
            {"id": "default", "name": "默认模型", "color": (255, 120, 0)},
            {"id": "business", "name": "商务模型", "color": (150, 150, 150)},
            {"id": "cartoon", "name": "卡通模型", "color": (255, 200, 50)},
            {"id": "realistic", "name": "写实模型", "color": (160, 100, 70)}
        ]
        
        # 为每个模型生成占位缩略图
        for model in models:
            # 创建空白图像
            img = np.ones((200, 300, 3), dtype=np.uint8)
            
            # 设置背景颜色 (BGR格式)
            img[:, :] = model["color"]
            
            # 添加文本
            font = cv2.FONT_HERSHEY_SIMPLEX
            text = model["name"]
            text_size = cv2.getTextSize(text, font, 1, 2)[0]
            text_x = (img.shape[1] - text_size[0]) // 2
            text_y = (img.shape[0] + text_size[1]) // 2
            cv2.putText(img, text, (text_x, text_y), font, 1, (255, 255, 255), 2)
            
            # 保存图像
            output_path = os.path.join(thumbnail_dir, f"{model['id']}.jpg")
            cv2.imwrite(output_path, img)
            logger.info(f"成功生成占位缩略图: {output_path}")
    
    except Exception as e:
        logger.error(f"生成占位缩略图失败: {e}")

if __name__ == "__main__":
    logger.info("开始生成缩略图...")
    
    try:
        # 首先尝试使用Avatar生成器生成真实缩略图
        logger.info("尝试使用Avatar生成器生成缩略图...")
        generate_model_thumbnails()
    except Exception as e:
        logger.warning(f"使用Avatar生成器生成缩略图失败: {e}")
        
        # 如果失败，创建占位缩略图
        logger.info("创建占位缩略图...")
        generate_placeholder_thumbnails()
    
    logger.info("缩略图生成完成!") 