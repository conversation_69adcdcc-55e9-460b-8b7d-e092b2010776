#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
术语采集任务修复脚本
用于修复术语采集任务的结果，特别是针对汽车领域的任务
"""

import os
import sys
import json
import logging
import argparse
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
import time
from datetime import datetime
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy import Column, String, Float, Integer, Text, DateTime, Boolean, ForeignKey, JSON
from sqlalchemy import inspect

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.append(project_root)

# 导入新的术语提取模块
from terminology_extractor import TerminologyExtractor

Base = declarative_base()

class TerminologyCollectionTask(Base):
    """术语采集任务模型，匹配数据库中的terminology_collection_tasks表"""
    __tablename__ = "terminology_collection_tasks"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    task_id = Column(String(36), nullable=False, unique=True)
    name = Column(String(100), nullable=False)
    mode = Column(String(50))
    keywords = Column(Text)
    language = Column(String(50))
    target_website = Column(Text)
    url_list = Column(JSON)
    status = Column(String(20), nullable=False, default="pending")
    progress = Column(Integer, default=0)
    celery_task_id = Column(String(255))
    error_message = Column(Text)
    created_at = Column(DateTime)
    completed_at = Column(DateTime)
    user_id = Column(Integer)

class TerminologyCollectionResult(Base):
    """术语结果模型，匹配数据库中的terminology_collection_results表"""
    __tablename__ = "terminology_collection_results"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    result_id = Column(String(36), nullable=False)
    task_id = Column(String(36), nullable=False)
    term = Column(String(255), nullable=False)
    definition = Column(Text)
    pos = Column(String(50))  # part of speech (词性)
    confidence = Column(Float)
    source_url = Column(Text)
    context = Column(Text)
    created_at = Column(DateTime)

# 简单的领域检测函数
def detect_domain(text: str) -> str:
    """
    检测文本所属的领域
    
    Args:
        text: 输入文本
        
    Returns:
        str: 领域名称，如果无法确定则返回"通用"
    """
    # 汽车领域关键词
    auto_keywords = ["汽车", "车", "发动机", "轮胎", "底盘", "变速箱", "刹车", "方向盘", "车轮", 
                    "悬挂", "油耗", "排量", "涡轮", "电动汽车", "混合动力", "自动驾驶"]
    
    # 检查文本中是否包含汽车领域关键词
    for keyword in auto_keywords:
        if keyword in text:
            return "汽车"
    
    return "通用"

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('fix_terminology_task.log')
    ]
)
logger = logging.getLogger(__name__)

def get_db_engine():
    """
    获取数据库引擎
    
    Returns:
        Engine: SQLAlchemy引擎
    """
    # 从环境变量获取数据库URL
    db_url = os.environ.get("DATABASE_URL", "postgresql://postgres:langpro8@localhost:5432/ai_platform?client_encoding=utf8")
    
    logger.info(f"使用数据库: {db_url}")
    
    # 创建数据库引擎
    return create_engine(db_url)

def get_db_session() -> Session:
    """
    创建数据库会话
    
    Returns:
        Session: 数据库会话
    """
    engine = get_db_engine()
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    
    return SessionLocal()

def init_db():
    """
    初始化数据库
    
    注意：我们不再创建表，而是使用现有的表
    """
    engine = get_db_engine()
    
    # 检查表是否存在
    inspector = inspect(engine)
    if "terminology_collection_tasks" not in inspector.get_table_names():
        logger.warning("表 terminology_collection_tasks 不存在，请确保数据库已正确设置")
    
    if "terminology_collection_results" not in inspector.get_table_names():
        logger.warning("表 terminology_collection_results 不存在，请确保数据库已正确设置")
    
    logger.info("使用现有数据库表")

def create_sample_task(task_id: str) -> None:
    """
    创建示例任务，用于测试
    
    Args:
        task_id: 任务ID
    """
    # 确保数据库表已创建
    init_db()
    
    logger.info(f"准备创建示例任务，任务ID: {task_id}")
    
    session = get_db_session()
    
    try:
        # 创建示例文本
        sample_text = """
        汽车是一种陆地上的机动车辆，通常由内燃机或电动机驱动。现代汽车包含了许多复杂的系统，如发动机、变速箱、底盘、悬挂系统、制动系统、转向系统、电子控制单元等。
        
        发动机是汽车的动力来源，常见的有汽油发动机、柴油发动机和电动机。汽油发动机通过火花塞点火，而柴油发动机则依靠压缩热量点火。发动机的排量、功率和扭矩是衡量其性能的重要指标。
        
        变速箱负责将发动机的动力传递到车轮，同时调整速度和扭矩。常见的变速箱类型有手动变速箱、自动变速箱、双离合变速箱和无级变速箱。
        
        底盘是汽车的承载系统，包括车架或承载式车身、悬挂系统、转向系统和制动系统。悬挂系统连接车身和车轮，吸收路面震动，提高行驶舒适性和操控稳定性。常见的悬挂类型有麦弗逊式悬挂、多连杆悬挂和扭力梁悬挂。
        
        制动系统用于减速和停车，主要由制动踏板、制动主缸、制动管路、制动卡钳和制动盘或制动鼓组成。现代汽车通常配备防抱死制动系统(ABS)、电子制动力分配系统(EBD)和制动辅助系统(BA)。
        """
        
        # 检查任务是否已存在
        existing_task = session.query(TerminologyCollectionTask).filter(TerminologyCollectionTask.task_id == task_id).first()
        if existing_task:
            logger.info(f"任务已存在: {task_id}，正在更新文本内容")
            # 更新任务文本
            existing_task.target_website = sample_text
            existing_task.status = "pending"
            existing_task.progress = 0
            session.commit()
            logger.info(f"已更新任务: {task_id}")
            return
        
        # 创建任务对象
        task = TerminologyCollectionTask(
            task_id=task_id,  # 确保设置task_id字段
            name="汽车术语采集",
            mode="auto",
            keywords="汽车,发动机,底盘,变速箱",
            language="zh",
            status="pending",
            target_website=sample_text,  # 使用target_website字段存储示例文本
            progress=0,
            created_at=datetime.now()
        )
        
        # 打印任务对象的属性，检查task_id是否正确设置
        logger.info(f"任务对象创建完成，检查task_id: {task.task_id}")
        
        # 添加到会话并提交
        session.add(task)
        session.commit()
        
        logger.info(f"已创建示例任务: {task_id}")
    
    except Exception as e:
        logger.error(f"创建示例任务失败: {str(e)}")
        session.rollback()
    
    finally:
        session.close()

def fix_terminology_task(task_id: str, force: bool = False) -> dict:
    """
    修复术语采集任务
    
    Args:
        task_id: 任务ID
        force: 是否强制重新生成结果，即使任务状态为已完成
        
    Returns:
        dict: 修复结果
    """
    # 确保数据库表已初始化
    init_db()
    
    logger.info(f"开始修复术语采集任务: {task_id}")
    start_time = time.time()
    
    # 获取数据库会话
    session = get_db_session()
    
    try:
        # 获取任务信息
        task = session.query(TerminologyCollectionTask).filter(TerminologyCollectionTask.task_id == task_id).first()
        if not task:
            logger.error(f"任务不存在: {task_id}")
            return {"status": "error", "message": f"任务不存在: {task_id}"}
        
        # 检查任务状态
        if task.status == "completed" and not force:
            logger.info(f"任务已完成，不需要修复: {task_id}")
            return {"status": "info", "message": "任务已完成，不需要修复"}
        
        # 获取任务文本 - 从target_website字段获取
        text = task.target_website
        if not text:
            logger.error(f"任务文本为空: {task_id}")
            return {"status": "error", "message": "任务文本为空"}
        
        # 检测文本领域
        domain = detect_domain(text)
        logger.info(f"检测到文本领域: {domain}")
        
        # 创建术语提取器
        extractor = TerminologyExtractor()
        
        # 使用术语提取器提取术语
        try:
            # 使用所有方法提取术语
            logger.info("开始提取术语...")
            results = extractor.extract_all_methods(text)
            
            # 合并结果
            merged_terms = extractor.merge_results(results)
            
            # 限制结果数量，取前100个
            merged_terms = merged_terms[:100]
            
            # 创建输出目录
            output_dir = os.path.join("output", task_id)
            os.makedirs(output_dir, exist_ok=True)
            
            # 导出结果
            csv_path = os.path.join(output_dir, f"{domain}_terms.csv")
            json_path = os.path.join(output_dir, f"{domain}_terms.json")
            png_path = os.path.join(output_dir, f"{domain}_terms.png")
            
            extractor.export_to_csv(merged_terms, csv_path)
            extractor.export_to_json(merged_terms, json_path)
            
            try:
                extractor.visualize_results(merged_terms[:20], title=f"{domain}领域术语分布", output_file=png_path)
            except Exception as e:
                logger.warning(f"创建可视化图表失败: {str(e)}")
            
            # 评估术语质量
            evaluation = extractor.evaluate_term_quality(merged_terms[:50])
            logger.info(f"术语质量评估结果: 精确度={evaluation['precision']:.2f}, 召回率={evaluation['recall']:.2f}, F1得分={evaluation['f1_score']:.2f}")
            
            # 删除旧的术语结果
            session.query(TerminologyCollectionResult).filter(TerminologyCollectionResult.task_id == task_id).delete()
            
            # 保存术语结果到数据库
            import uuid
            for term, score in merged_terms:
                term_result = TerminologyCollectionResult(
                    result_id=str(uuid.uuid4()),
                    task_id=task_id,
                    term=term,
                    confidence=float(score),
                    definition="",  # 可以添加术语定义
                    pos="",  # 词性
                    context="",  # 上下文
                    created_at=datetime.now()
                )
                session.add(term_result)
            
            # 更新任务状态为已完成
            task.status = "completed"
            task.progress = 100
            task.completed_at = datetime.now()
            
            session.commit()
            
            logger.info(f"术语采集任务修复完成: {task_id}，共找到 {len(merged_terms)} 个术语")
            
            return {
                "status": "success", 
                "task_id": task_id,
                "domain": domain,
                "term_count": len(merged_terms),
                "processing_time": time.time() - start_time
            }
            
        except Exception as e:
            logger.error(f"处理术语时出错: {str(e)}", exc_info=True)
            task.status = "failed"
            task.error_message = str(e)
            session.commit()
            return {"status": "error", "message": str(e)}
    
    except Exception as e:
        logger.error(f"修复术语采集任务时出错: {str(e)}", exc_info=True)
        return {"status": "error", "message": str(e)}
    
    finally:
        session.close()

def test_terminology_extraction(task_id: str) -> dict:
    """
    测试术语提取功能
    
    Args:
        task_id: 任务ID
        
    Returns:
        dict: 测试结果
    """
    logger.info(f"开始测试术语提取功能，任务ID: {task_id}")
    
    # 获取数据库会话
    session = get_db_session()
    
    try:
        # 获取任务信息
        task = session.query(TerminologyCollectionTask).filter(TerminologyCollectionTask.task_id == task_id).first()
        if not task:
            logger.error(f"任务不存在: {task_id}")
            return {"status": "error", "message": f"任务不存在: {task_id}"}
        
        # 获取任务文本
        text = task.target_website
        if not text:
            logger.error(f"任务文本为空: {task_id}")
            return {"status": "error", "message": "任务文本为空"}
        
        # 创建术语提取器
        extractor = TerminologyExtractor()
        
        # 提取术语
        logger.info("使用TF-IDF提取术语...")
        tfidf_terms = extractor.extract_terms_tfidf(text, topK=10)
        
        logger.info("使用TextRank提取术语...")
        textrank_terms = extractor.extract_terms_textrank(text, topK=10)
        
        logger.info("使用领域匹配提取术语...")
        domain_terms = extractor.extract_terms_domain_match(text, topK=10)
        
        # 获取已保存的术语
        saved_terms = session.query(TerminologyCollectionResult).filter(
            TerminologyCollectionResult.task_id == task_id
        ).all()
        
        return {
            "status": "success",
            "task_id": task_id,
            "task_name": task.name,
            "tfidf_terms": tfidf_terms,
            "textrank_terms": textrank_terms,
            "domain_terms": domain_terms,
            "saved_terms_count": len(saved_terms),
            "saved_terms_sample": [(t.term, t.confidence) for t in saved_terms[:5]] if saved_terms else []
        }
        
    except Exception as e:
        logger.error(f"测试术语提取功能时出错: {str(e)}", exc_info=True)
        return {"status": "error", "message": str(e)}
    
    finally:
        session.close()

def view_terminology_results(task_id: str) -> None:
    """
    查看术语结果
    
    Args:
        task_id: 任务ID
    """
    logger.info(f"查看术语结果，任务ID: {task_id}")
    
    # 获取数据库会话
    session = get_db_session()
    
    try:
        # 查询任务
        task = session.query(TerminologyCollectionTask).filter(TerminologyCollectionTask.task_id == task_id).first()
        if not task:
            logger.error(f"任务不存在: {task_id}")
            return
        
        logger.info(f"任务名称: {task.name}")
        logger.info(f"任务状态: {task.status}")
        logger.info(f"任务进度: {task.progress}")
        
        # 查询术语结果
        results = session.query(TerminologyCollectionResult).filter(
            TerminologyCollectionResult.task_id == task_id
        ).order_by(TerminologyCollectionResult.confidence.desc()).limit(20).all()
        
        logger.info(f"术语结果数量: {len(results)}")
        
        # 打印术语结果
        for i, result in enumerate(results):
            logger.info(f"{i+1}. {result.term}: {result.confidence:.4f}")
    
    finally:
        session.close()

def test_create_and_fix_task():
    """
    测试创建和修复任务函数
    """
    # 生成一个新的UUID
    import uuid
    task_id = str(uuid.uuid4())
    
    logger.info(f"测试创建任务，任务ID: {task_id}")
    
    # 创建示例任务
    create_sample_task(task_id)
    
    # 测试查询任务
    session = get_db_session()
    try:
        task = session.query(TerminologyCollectionTask).filter(TerminologyCollectionTask.task_id == task_id).first()
        if task:
            logger.info(f"任务创建成功: {task_id}")
            logger.info(f"任务名称: {task.name}")
            logger.info(f"任务状态: {task.status}")
            
            # 修复任务
            logger.info(f"开始修复任务: {task_id}")
            result = fix_terminology_task(task_id, True)
            
            if result["status"] == "success":
                logger.info(f"修复成功! 任务ID: {result['task_id']}")
                logger.info(f"领域: {result['domain']}")
                logger.info(f"术语数量: {result['term_count']}")
                logger.info(f"处理时间: {result['processing_time']:.2f} 秒")
                
                # 查看术语结果
                view_terminology_results(task_id)
                
                # 测试术语提取
                logger.info(f"测试术语提取功能: {task_id}")
                test_result = test_terminology_extraction(task_id)
                
                if test_result["status"] == "success":
                    logger.info(f"测试成功! 已保存术语数量: {test_result['saved_terms_count']}")
                    if test_result["saved_terms_sample"]:
                        logger.info("已保存术语样本:")
                        for term, score in test_result["saved_terms_sample"]:
                            logger.info(f"  {term}: {score:.4f}")
                    return True
                else:
                    logger.error(f"测试失败: {test_result['message']}")
                    return False
            else:
                logger.error(f"修复失败: {result['message']}")
                return False
        else:
            logger.error(f"任务创建失败，无法查询到任务: {task_id}")
            return False
    finally:
        session.close()

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="术语采集任务修复工具")
    
    # 添加任务ID参数（放在前面，这样才能被子解析器捕获）
    parser.add_argument("task_id", nargs="?", help="任务ID")
    
    # 添加通用选项
    parser.add_argument("--force", action="store_true", help="强制重新生成结果，即使任务状态为已完成")
    parser.add_argument("--init-db", action="store_true", help="初始化数据库")
    parser.add_argument("--create-sample", action="store_true", help="创建示例任务")
    parser.add_argument("--test", action="store_true", help="测试术语提取功能")
    parser.add_argument("--view", action="store_true", help="查看术语结果")
    
    args = parser.parse_args()
    
    # 打印参数，用于调试
    logger.info(f"命令行参数: {args}")
    
    # 处理命令
    if args.init_db:
        init_db()
        print("数据库已初始化")
        return
    
    if args.create_sample:
        if not args.task_id:
            print("错误: 创建示例任务需要指定任务ID")
            parser.print_help()
            return
        logger.info(f"创建示例任务，任务ID: {args.task_id}")
        create_sample_task(args.task_id)
        print(f"已创建示例任务: {args.task_id}")
        return
    
    if args.test:
        if not args.task_id:
            print("错误: 测试术语提取功能需要指定任务ID")
            parser.print_help()
            return
        result = test_terminology_extraction(args.task_id)
        if result["status"] == "success":
            print(f"测试成功! 任务ID: {result['task_id']}")
            print(f"任务名称: {result['task_name']}")
            print("\nTF-IDF术语提取结果:")
            for term, score in result["tfidf_terms"]:
                print(f"  {term}: {score:.4f}")
            
            print("\nTextRank术语提取结果:")
            for term, score in result["textrank_terms"]:
                print(f"  {term}: {score:.4f}")
            
            print("\n领域匹配术语提取结果:")
            for term, score in result["domain_terms"]:
                print(f"  {term}: {score:.4f}")
            
            print(f"\n已保存术语数量: {result['saved_terms_count']}")
            if result["saved_terms_sample"]:
                print("已保存术语样本:")
                for term, score in result["saved_terms_sample"]:
                    print(f"  {term}: {score:.4f}")
        else:
            print(f"测试失败: {result['message']}")
        return
    
    if args.view:
        if not args.task_id:
            print("错误: 查看术语结果需要指定任务ID")
            parser.print_help()
            return
        view_terminology_results(args.task_id)
        return
    
    if args.task_id:
        result = fix_terminology_task(args.task_id, args.force)
        if result["status"] == "success":
            print(f"修复成功! 任务ID: {result['task_id']}")
            print(f"领域: {result['domain']}")
            print(f"术语数量: {result['term_count']}")
            print(f"处理时间: {result['processing_time']:.2f} 秒")
        else:
            print(f"修复失败: {result['message']}")
        return
    
    # 如果没有指定任何命令或参数，显示帮助信息
    parser.print_help()

if __name__ == "__main__":
    # 如果需要测试，取消注释下面的代码
    # test_create_and_fix_task()
    
    # 执行主函数
    main() 