from sqlalchemy.orm import Session
import json
from datetime import datetime
import logging
from typing import Dict, Any, Optional, Union

logger = logging.getLogger(__name__)

# 创建一个通用的日志操作函数
async def log_operation(
    db: Session, 
    user_id: int, 
    operation_type: str, 
    details: Dict[str, Any] = None,
    status: str = "success"
):
    """
    记录用户操作到数据库
    
    Args:
        db: 数据库会话
        user_id: 用户ID
        operation_type: 操作类型
        details: 操作详情
        status: 操作状态
    """
    try:
        # 检查是否导入了UserOperation模型
        try:
            from models.user_operation import UserOperation
        except ImportError:
            logger.warning("UserOperation模型不可用，无法记录操作日志")
            return
        
        # 处理details参数
        if details is None:
            details = {}
        
        # 将details转换为JSON字符串
        if isinstance(details, dict):
            details_json = json.dumps(details, ensure_ascii=False)
        else:
            details_json = str(details)
        
        # 创建操作记录
        operation = UserOperation(
            user_id=user_id,
            operation_type=operation_type,
            details=details_json,
            status=status,
            created_at=datetime.now()
        )
        
        # 添加到数据库
        db.add(operation)
        db.commit()
        
        logger.info(f"记录用户操作: user_id={user_id}, operation={operation_type}, status={status}")
    except Exception as e:
        logger.error(f"记录操作日志时出错: {str(e)}")
        # 尝试回滚数据库事务
        try:
            db.rollback()
        except:
            pass 