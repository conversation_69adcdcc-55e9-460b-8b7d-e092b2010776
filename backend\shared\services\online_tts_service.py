#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
在线TTS服务 - 支持多种在线TTS API，提供可靠的语音生成
"""

import os
import sys
import logging
import tempfile
import time
import uuid
import json
import asyncio
import traceback
from typing import Dict, Any, List, Optional
from concurrent.futures import ThreadPoolExecutor
import requests
from urllib3.util.retry import Retry
from requests.adapters import HTTPAdapter
import urllib3
import shutil

# 导入gTTS
try:
    from gtts import gTTS
    GTTS_AVAILABLE = True
except ImportError:
    GTTS_AVAILABLE = False
    logging.warning("gTTS未安装，部分在线TTS功能将不可用")

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class OnlineTTSService:
    """在线TTS服务 - 集成多种在线TTS API"""
    
    def __init__(self):
        """初始化在线TTS服务"""
        # 服务状态
        self.stats = {
            "requests": 0,
            "success": 0,
            "failures": 0,
            "api_usage": {},
            "start_time": time.time()
        }
        
        # 网络配置
        self.timeout = 30  # 默认超时时间，单位秒
        self.max_retries = 3  # 最大重试次数
        self.backoff_factor = 0.5  # 重试等待时间因子
        
        # 设置全局代理配置
        self.proxy_config = {
            "enabled": False,
            "http": os.environ.get("HTTP_PROXY", None),
            "https": os.environ.get("HTTPS_PROXY", None)
        }
        
        # API配置
        self.api_configs = {
            "gtts": {
                "enabled": GTTS_AVAILABLE,
                "priority": 1
            },
            "azure": {
                "enabled": False,
                "priority": 2,
                "key": os.environ.get("AZURE_TTS_KEY", ""),
                "region": os.environ.get("AZURE_TTS_REGION", "eastasia")
            },
            "huggingface": {
                "enabled": False,
                "priority": 3,
                "api_key": os.environ.get("HF_API_KEY", "")
            }
        }
        
        # 缓存目录
        cache_dir = os.environ.get("TTS_CACHE_DIR", None)
        if not cache_dir:
            cache_dir = os.path.join(tempfile.gettempdir(), "online_tts_cache")
        os.makedirs(cache_dir, exist_ok=True)
        self.cache_dir = cache_dir
        
        # 线程池 - 用于执行API调用
        self.executor = ThreadPoolExecutor(max_workers=4)
        
        # 可用语音列表
        self.voices = self._initialize_voices()
        
        # 会话配置
        self.session = self._create_optimized_session()
        
        # 标记初始化完成
        self.initialized = True
        logger.info("在线TTS服务初始化完成")
    
    def _create_optimized_session(self):
        """创建优化的HTTP会话，支持重试和超时设置"""
        # 会话配置
        session = requests.Session()
        
        # 配置重试策略
        retry_strategy = Retry(
            total=self.max_retries,
            backoff_factor=self.backoff_factor,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["GET", "POST"]
        )
        
        # 应用重试策略到请求适配器
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        # 设置代理（如果启用）
        if self.proxy_config["enabled"]:
            session.proxies = {
                "http": self.proxy_config["http"],
                "https": self.proxy_config["https"]
            }
        
        # 设置合理的超时
        session.timeout = self.timeout
        
        # 设置最长连接时间
        session.keep_alive = False
        
        return session
    
    def _initialize_voices(self):
        """初始化支持的语音列表"""
        voices = {
            # 中文声音
            "zh-female1": {"lang": "zh-cn", "gender": "female", "name": "标准女声", "provider": "gtts"},
            "zh-female2": {"lang": "zh-cn", "gender": "female", "name": "甜美女声", "provider": "gtts", "options": {"tld": "com.hk"}},
            "zh-male1": {"lang": "zh-cn", "gender": "male", "name": "标准男声", "provider": "gtts"},
            
            # 英文声音
            "en-female1": {"lang": "en", "gender": "female", "name": "英语女声", "provider": "gtts"},
            "en-male1": {"lang": "en", "gender": "male", "name": "英语男声", "provider": "gtts", "options": {"tld": "co.uk"}},
            
            # 日语声音
            "ja-female1": {"lang": "ja", "gender": "female", "name": "日语女声", "provider": "gtts"},
            
            # 法语声音
            "fr-female1": {"lang": "fr", "gender": "female", "name": "法语女声", "provider": "gtts"},
            
            # 德语声音
            "de-female1": {"lang": "de", "gender": "female", "name": "德语女声", "provider": "gtts"}
        }
        
        # 如果Azure启用，添加Azure声音
        if self.api_configs["azure"]["enabled"]:
            voices.update({
                "azure-zh-female1": {"lang": "zh-CN", "gender": "female", "name": "Azure小梦", "provider": "azure", "voice_name": "zh-CN-XiaomengNeural"},
                "azure-zh-male1": {"lang": "zh-CN", "gender": "male", "name": "Azure云健", "provider": "azure", "voice_name": "zh-CN-YunjianNeural"},
                "azure-en-female1": {"lang": "en-US", "gender": "female", "name": "Azure Jenny", "provider": "azure", "voice_name": "en-US-JennyNeural"}
            })
        
        return voices
    
    def get_available_voices(self) -> Dict:
        """
        获取可用的语音列表
        
        Returns:
            Dict: 语音ID到语音信息的映射
        """
        return self.voices
    
    def _get_cache_key(self, text: str, voice_id: str, speed: float) -> str:
        """生成缓存键"""
        import hashlib
        # 使用文本、语音ID和速度的组合作为输入
        key_input = f"{text}_{voice_id}_{speed}"
        # 计算MD5哈希作为缓存键
        return hashlib.md5(key_input.encode('utf-8')).hexdigest()
    
    def _get_cached_audio(self, text: str, voice_id: str, speed: float) -> Optional[str]:
        """从缓存获取音频路径"""
        cache_key = self._get_cache_key(text, voice_id, speed)
        cache_path = os.path.join(self.cache_dir, f"{cache_key}.mp3")
        
        if os.path.exists(cache_path) and os.path.getsize(cache_path) > 1000:  # 文件至少1KB
            logger.info(f"在缓存中找到音频: {cache_path}")
            return cache_path
        
        return None
    
    async def text_to_speech(self, 
                           text: str, 
                           voice_id: str = None, 
                           speed: float = 1.0,
                           output_path: str = None) -> Dict[str, Any]:
        """
        将文本转换为语音
        
        Args:
            text: 要转换的文本
            voice_id: 语音ID，例如 zh-female1
            speed: 语速，默认为1.0
            output_path: 输出路径，如果为None则使用临时文件
            
        Returns:
            Dict: 包含结果的字典
        """
        start_time = time.time()
        self.stats["requests"] += 1
        
        # 验证参数
        if not text or len(text.strip()) == 0:
            logger.warning("收到空文本，将生成静音文件")
            if not output_path:
                output_path = os.path.join(tempfile.gettempdir(), f"tts_{uuid.uuid4()}.mp3")
            # 创建一个空文件
            with open(output_path, 'wb') as f:
                f.write(b'')
            return {
                "success": True,
                "message": "收到空文本，生成空文件",
                "audio_path": output_path,
                "from_cache": False,
                "duration": 0
            }
        
        # 验证语音ID
        if not voice_id or voice_id not in self.voices:
            default_voice = "zh-female1"
            logger.warning(f"无效的语音ID: {voice_id}，使用默认值: {default_voice}")
            voice_id = default_voice
        
        # 检查缓存
        cached_path = self._get_cached_audio(text, voice_id, speed)
        if cached_path:
            # 使用缓存的音频
            if output_path:
                shutil.copy2(cached_path, output_path)
                audio_path = output_path
            else:
                audio_path = cached_path
            
            return {
                "success": True,
                "message": "使用缓存的音频",
                "audio_path": audio_path,
                "from_cache": True,
                "duration": 0  # 未计算实际持续时间
            }
        
        # 生成输出路径
        if not output_path:
            cache_key = self._get_cache_key(text, voice_id, speed)
            output_path = os.path.join(self.cache_dir, f"{cache_key}.mp3")
        
        # 获取语音配置
        voice_config = self.voices[voice_id]
        provider = voice_config.get("provider", "gtts")
        
        # 获取API配置的排序列表
        apis = sorted(
            [api for api, config in self.api_configs.items() if config["enabled"]],
            key=lambda x: self.api_configs[x]["priority"]
        )
        
        # 如果指定了provider且该provider可用，优先使用
        if provider in apis:
            apis.remove(provider)
            apis.insert(0, provider)
        
        # 尝试每个API
        result = None
        for api in apis:
            try:
                logger.info(f"尝试使用 {api} API生成语音")
                
                # 根据API类型调用不同的方法
                if api == "gtts" and GTTS_AVAILABLE:
                    result = await self._generate_with_gtts(text, voice_config, output_path, speed)
                elif api == "azure" and self.api_configs["azure"]["enabled"]:
                    result = await self._generate_with_azure(text, voice_config, output_path, speed)
                elif api == "huggingface" and self.api_configs["huggingface"]["enabled"]:
                    result = await self._generate_with_huggingface(text, voice_config, output_path, speed)
                
                # 如果生成成功，记录并返回结果
                if result and result.get("success", False):
                    self.stats["success"] += 1
                    self.stats["api_usage"][api] = self.stats["api_usage"].get(api, 0) + 1
                    return result
                
            except Exception as e:
                logger.error(f"{api} API生成失败: {str(e)}")
                continue
        
        # 如果所有API都失败，返回错误
        self.stats["failures"] += 1
        return {
            "success": False,
            "message": "所有在线TTS API都失败",
            "audio_path": None,
            "from_cache": False,
            "duration": time.time() - start_time
        }
    
    async def _generate_with_gtts(self, text: str, voice_config: Dict, output_path: str, speed: float) -> Dict[str, Any]:
        """使用Google TTS生成语音"""
        if not GTTS_AVAILABLE:
            return {"success": False, "message": "gTTS未安装"}
        
        try:
            # 提取语言设置
            lang = voice_config.get("lang", "zh-cn")
            # 提取TLD（顶级域名）设置 - 影响音色
            tld = voice_config.get("options", {}).get("tld", "com")
            
            # 在线程池中执行gTTS操作
            def generate_gtts():
                try:
                    # 设置更长的超时时间
                    urllib3.util.timeout.Timeout(connect=10.0, read=30.0)
                    # 创建gTTS对象
                    tts = gTTS(text=text, lang=lang, tld=tld, slow=False)
                    # 保存到文件
                    tts.save(output_path)
                    return True
                except Exception as e:
                    logger.error(f"gTTS生成失败: {e}")
                    return False
            
            # 异步执行生成任务
            loop = asyncio.get_event_loop()
            success = await loop.run_in_executor(self.executor, generate_gtts)
            
            if success and os.path.exists(output_path) and os.path.getsize(output_path) > 0:
                # 处理语速调整
                if speed != 1.0 and 0.5 <= speed <= 2.0:
                    # 这里可以添加语速调整的代码
                    # 例如使用FFmpeg或其他库调整音频速度
                    pass
                
                # 返回成功结果
                return {
                    "success": True,
                    "message": "使用gTTS成功生成语音",
                    "audio_path": output_path,
                    "from_cache": False,
                    "duration": 0
                }
            else:
                return {
                    "success": False,
                    "message": "gTTS生成失败",
                    "audio_path": None,
                    "from_cache": False,
                    "duration": 0
                }
        
        except Exception as e:
            logger.error(f"gTTS处理失败: {e}")
            return {
                "success": False,
                "message": f"gTTS处理失败: {str(e)}",
                "audio_path": None,
                "from_cache": False,
                "duration": 0
            }
    
    async def _generate_with_azure(self, text: str, voice_config: Dict, output_path: str, speed: float) -> Dict[str, Any]:
        """使用Azure TTS生成语音"""
        if not self.api_configs["azure"]["enabled"] or not self.api_configs["azure"]["key"]:
            return {"success": False, "message": "Azure TTS未启用或API密钥未设置"}
        
        # 这里添加Azure TTS API的实现
        # 由于Azure TTS需要额外的库和配置，这里仅提供代码结构
        return {"success": False, "message": "Azure TTS实现尚未完成"}
    
    async def _generate_with_huggingface(self, text: str, voice_config: Dict, output_path: str, speed: float) -> Dict[str, Any]:
        """使用Hugging Face API生成语音"""
        if not self.api_configs["huggingface"]["enabled"] or not self.api_configs["huggingface"]["api_key"]:
            return {"success": False, "message": "Hugging Face API未启用或API密钥未设置"}
        
        # 这里添加Hugging Face API的实现
        # 由于需要额外的库和配置，这里仅提供代码结构
        return {"success": False, "message": "Hugging Face TTS实现尚未完成"}
    
    async def generate_speech(self, 
                            text: str, 
                            voice_id: str = None, 
                            speed: float = 1.0,
                            output_path: str = None) -> Dict[str, Any]:
        """
        将文本转换为语音（text_to_speech的别名）
        
        此方法是为了保持与其他TTS服务接口的兼容性。
        功能与text_to_speech完全相同。
        
        Args:
            text: 要转换的文本
            voice_id: 语音ID，例如 zh-female1
            speed: 语速，默认为1.0
            output_path: 输出路径，如果为None则使用临时文件
            
        Returns:
            Dict: 包含结果的字典
        """
        # 调用text_to_speech获取结果
        result = await self.text_to_speech(text, voice_id, speed, output_path)
        
        # 转换结果格式以匹配tts_service.py期望的格式
        converted_result = result.copy()
        
        # 将success布尔值转换为status字符串
        if "success" in result:
            if result["success"]:
                converted_result["status"] = "success"
            else:
                converted_result["status"] = "error"
        
        # 确保路径字段格式统一
        if "audio_path" in result and result["audio_path"] and "path" not in converted_result:
            converted_result["path"] = result["audio_path"]
        
        return converted_result
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """
        获取性能统计信息
        
        Returns:
            Dict: 性能统计数据
        """
        uptime = time.time() - self.stats["start_time"]
        
        return {
            "requests": self.stats["requests"],
            "success": self.stats["success"],
            "failures": self.stats["failures"],
            "success_rate": self.stats["success"] / max(1, self.stats["requests"]),
            "api_usage": self.stats["api_usage"],
            "uptime_seconds": uptime
        }

# 单例模式
_online_tts_instance = None

def get_online_tts_service() -> OnlineTTSService:
    """获取在线TTS服务实例（单例模式）"""
    global _online_tts_instance
    if _online_tts_instance is None:
        _online_tts_instance = OnlineTTSService()
    return _online_tts_instance

def initialize() -> bool:
    """初始化在线TTS服务"""
    try:
        service = get_online_tts_service()
        return service.initialized
    except Exception as e:
        logger.error(f"初始化在线TTS服务失败: {e}")
        logger.error(traceback.format_exc())
        return False 