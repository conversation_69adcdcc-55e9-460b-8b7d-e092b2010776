#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复缺失模型文件和常见问题的脚本

该脚本会自动检测和修复以下问题：
1. 检查SadTalker模型文件并尝试下载
2. 检查Avatar模型文件并创建必要的目录
3. 修复常见的路径和导入问题
"""

import os
import sys
import glob
import shutil
import subprocess
import logging
import requests
import tempfile
import argparse
import traceback
from pathlib import Path
from typing import List, Dict, Optional, Tuple

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("fix_missing_models")

def find_project_root() -> str:
    """
    查找项目根目录
    
    Returns:
        str: 项目根目录的绝对路径
    """
    # 从当前目录开始向上查找，直到找到包含backend目录的目录
    current_dir = os.path.abspath(os.getcwd())
    while current_dir != os.path.dirname(current_dir):  # 直到达到驱动器根目录
        if os.path.exists(os.path.join(current_dir, 'backend')):
            return current_dir
        current_dir = os.path.dirname(current_dir)
    
    # 如果没有找到，则假设当前目录是backend目录的父目录
    if os.path.exists(os.path.join(os.getcwd(), 'backend')):
        return os.getcwd()
    else:
        # 如果我们在backend目录内
        parent_dir = os.path.dirname(os.getcwd())
        if os.path.basename(os.getcwd()) == 'backend' or os.path.basename(os.getcwd()) == 'scripts':
            return parent_dir if os.path.basename(os.getcwd()) == 'backend' else os.path.dirname(parent_dir)
    
    # 如果都不符合，则使用当前目录
    logger.warning("无法确定项目根目录，使用当前目录")
    return os.getcwd()

def check_sadtalker_models(project_root: str) -> bool:
    """
    检查SadTalker模型文件
    
    Args:
        project_root: 项目根目录
        
    Returns:
        bool: 是否所有必要的模型文件都存在
    """
    logger.info("检查SadTalker模型文件...")
    
    # SadTalker目录
    sadtalker_dir = os.path.join(project_root, "backend", "scripts", "third_party", "SadTalker")
    checkpoint_dir = os.path.join(sadtalker_dir, "checkpoints")
    
    # 检查SadTalker目录是否存在
    if not os.path.exists(sadtalker_dir):
        logger.error(f"SadTalker目录不存在: {sadtalker_dir}")
        return False
    
    # 确保checkpoints目录存在
    os.makedirs(checkpoint_dir, exist_ok=True)
    os.makedirs(os.path.join(checkpoint_dir, "hub", "checkpoints"), exist_ok=True)
    
    # 必需的模型文件
    required_model_files = [
        os.path.join(checkpoint_dir, "epoch_20.pth"),
        os.path.join(checkpoint_dir, "BFM_model_front.mat"),
        os.path.join(checkpoint_dir, "face_mobilenet_256_1_0_16_best.pth"),
        os.path.join(checkpoint_dir, "wav2lip.pth"),
        os.path.join(checkpoint_dir, "mapping_00109-model.pth.tar"),
        os.path.join(checkpoint_dir, "mapping_00229-model.pth.tar"),
        os.path.join(checkpoint_dir, "hub", "checkpoints", "parsing_parsenet.pth")
    ]
    
    # 打印每个文件的检查路径和存在状态进行调试
    for model_file in required_model_files:
        file_exists = os.path.exists(model_file)
        logger.info(f"检查文件: {model_file} - {'存在' if file_exists else '不存在'}")
        if file_exists:
            try:
                file_size = os.path.getsize(model_file)
                logger.info(f"文件大小: {file_size} 字节")
            except Exception as e:
                logger.error(f"获取文件大小失败: {e}")
    
    missing_files = [f for f in required_model_files if not os.path.exists(f)]
    
    if not missing_files:
        logger.info("所有SadTalker模型文件都存在")
        return True
    
    logger.warning(f"缺少以下SadTalker模型文件: {missing_files}")
    
    # 检查下载脚本
    download_script = "download_models.sh"
    if os.name == 'nt':  # Windows
        download_script = "download_models.bat"
    
    download_path = os.path.join(sadtalker_dir, "scripts", download_script)
    
    # 检查下载脚本是否存在
    if not os.path.exists(download_path):
        logger.warning(f"下载脚本不存在: {download_path}")
        
        # 如果是Windows，创建下载脚本
        if os.name == 'nt':
            try:
                create_windows_download_script(sadtalker_dir)
                download_path = os.path.join(sadtalker_dir, "scripts", download_script)
                logger.info(f"已创建Windows下载脚本: {download_path}")
            except Exception as e:
                logger.error(f"创建Windows下载脚本失败: {e}")
    
    # 尝试执行下载脚本
    if os.path.exists(download_path):
        try:
            logger.info(f"执行下载脚本: {download_path}")
            
            if os.name == 'nt':  # Windows
                subprocess.run(
                    [download_path],
                    shell=True,
                    cwd=sadtalker_dir,
                    check=False  # 不检查返回码，因为有些下载可能失败
                )
            else:  # Linux/Mac
                subprocess.run(
                    ["bash", download_path],
                    check=False,
                    cwd=sadtalker_dir
                )
            
            logger.info("下载脚本执行完成")
        except Exception as e:
            logger.error(f"执行下载脚本失败: {e}")
    
    # 再次检查缺失的文件
    missing_files = [f for f in required_model_files if not os.path.exists(f)]
    
    # 如果仍有缺失，尝试直接下载
    if missing_files:
        logger.warning(f"下载脚本后仍缺少以下文件: {missing_files}")
        download_missing_models(missing_files)
    
    # 最终检查
    final_missing = [f for f in required_model_files if not os.path.exists(f)]
    if not final_missing:
        logger.info("所有SadTalker模型文件都已成功下载或修复")
        return True
    else:
        logger.warning(f"修复后仍缺少以下文件: {final_missing}")
        return False

def create_windows_download_script(sadtalker_dir: str) -> None:
    """
    创建Windows下载脚本
    
    Args:
        sadtalker_dir: SadTalker目录
    """
    scripts_dir = os.path.join(sadtalker_dir, "scripts")
    os.makedirs(scripts_dir, exist_ok=True)
    
    download_script_path = os.path.join(scripts_dir, "download_models.bat")
    
    script_content = """@echo off
REM SadTalker模型下载脚本 - Windows版本
REM 基于Linux版本download_models.sh转换

echo 创建checkpoints目录...
mkdir .\\checkpoints 2>nul

echo 下载模型文件...
REM 下载映射模型
curl -L -o .\\checkpoints\\mapping_00109-model.pth.tar https://github.com/OpenTalker/SadTalker/releases/download/v0.0.2-rc/mapping_00109-model.pth.tar
if %ERRORLEVEL% neq 0 echo 警告: 下载mapping_00109-model.pth.tar失败

curl -L -o .\\checkpoints\\mapping_00229-model.pth.tar https://github.com/OpenTalker/SadTalker/releases/download/v0.0.2-rc/mapping_00229-model.pth.tar
if %ERRORLEVEL% neq 0 echo 警告: 下载mapping_00229-model.pth.tar失败

REM 下载Safetensors模型
curl -L -o .\\checkpoints\\SadTalker_V0.0.2_256.safetensors https://github.com/OpenTalker/SadTalker/releases/download/v0.0.2-rc/SadTalker_V0.0.2_256.safetensors
if %ERRORLEVEL% neq 0 echo 警告: 下载SadTalker_V0.0.2_256.safetensors失败

curl -L -o .\\checkpoints\\SadTalker_V0.0.2_512.safetensors https://github.com/OpenTalker/SadTalker/releases/download/v0.0.2-rc/SadTalker_V0.0.2_512.safetensors
if %ERRORLEVEL% neq 0 echo 警告: 下载SadTalker_V0.0.2_512.safetensors失败

REM 下载额外必需的模型文件
curl -L -o .\\checkpoints\\BFM_model_front.mat https://github.com/OpenTalker/SadTalker/releases/download/v0.0.1-rc/BFM_model_front.mat
if %ERRORLEVEL% neq 0 echo 警告: 下载BFM_model_front.mat失败

curl -L -o .\\checkpoints\\face_mobilenet_256_1_0_16_best.pth https://github.com/OpenTalker/SadTalker/releases/download/v0.0.1-rc/face_mobilenet_256_1_0_16_best.pth
if %ERRORLEVEL% neq 0 echo 警告: 下载face_mobilenet_256_1_0_16_best.pth失败

curl -L -o .\\checkpoints\\wav2lip.pth https://github.com/OpenTalker/SadTalker/releases/download/v0.0.1-rc/wav2lip.pth
if %ERRORLEVEL% neq 0 echo 警告: 下载wav2lip.pth失败

REM 下载增强器模型
echo 创建增强器权重目录...
mkdir .\\gfpgan\\weights 2>nul

curl -L -o .\\gfpgan\\weights\\alignment_WFLW_4HG.pth https://github.com/xinntao/facexlib/releases/download/v0.1.0/alignment_WFLW_4HG.pth
if %ERRORLEVEL% neq 0 echo 警告: 下载alignment_WFLW_4HG.pth失败

curl -L -o .\\gfpgan\\weights\\detection_Resnet50_Final.pth https://github.com/xinntao/facexlib/releases/download/v0.1.0/detection_Resnet50_Final.pth
if %ERRORLEVEL% neq 0 echo 警告: 下载detection_Resnet50_Final.pth失败

curl -L -o .\\gfpgan\\weights\\GFPGANv1.4.pth https://github.com/TencentARC/GFPGAN/releases/download/v1.3.0/GFPGANv1.4.pth
if %ERRORLEVEL% neq 0 echo 警告: 下载GFPGANv1.4.pth失败

curl -L -o .\\gfpgan\\weights\\parsing_parsenet.pth https://github.com/xinntao/facexlib/releases/download/v0.2.2/parsing_parsenet.pth
if %ERRORLEVEL% neq 0 echo 警告: 下载parsing_parsenet.pth失败

REM 创建hub目录结构
echo 创建hub目录结构...
mkdir .\\checkpoints\\hub\\checkpoints 2>nul

REM 复制解析网络权重到hub目录
copy .\\gfpgan\\weights\\parsing_parsenet.pth .\\checkpoints\\hub\\checkpoints\\ 2>nul

echo 下载完成！
"""
    
    with open(download_script_path, 'w') as f:
        f.write(script_content)

def download_missing_models(missing_files: List[str]) -> None:
    """
    下载缺失的模型文件
    
    Args:
        missing_files: 缺失的文件列表
    """
    # 主要下载源
    primary_urls = {
        "epoch_20.pth": "https://github.com/OpenTalker/SadTalker/releases/download/v0.0.1-rc/epoch_20.pth",
        "BFM_model_front.mat": "https://github.com/OpenTalker/SadTalker/releases/download/v0.0.1-rc/BFM_model_front.mat",
        "face_mobilenet_256_1_0_16_best.pth": "https://github.com/OpenTalker/SadTalker/releases/download/v0.0.1-rc/face_mobilenet_256_1_0_16_best.pth",
        "wav2lip.pth": "https://github.com/OpenTalker/SadTalker/releases/download/v0.0.1-rc/wav2lip.pth",
        "mapping_00109-model.pth.tar": "https://github.com/OpenTalker/SadTalker/releases/download/v0.0.2-rc/mapping_00109-model.pth.tar",
        "mapping_00229-model.pth.tar": "https://github.com/OpenTalker/SadTalker/releases/download/v0.0.2-rc/mapping_00229-model.pth.tar",
        "parsing_parsenet.pth": "https://github.com/xinntao/facexlib/releases/download/v0.2.2/parsing_parsenet.pth"
    }
    
    # 备用下载源
    backup_urls = {
        "epoch_20.pth": "https://huggingface.co/OpenTalker/SadTalker/resolve/main/checkpoints/epoch_20.pth",
        "BFM_model_front.mat": "https://huggingface.co/OpenTalker/SadTalker/resolve/main/checkpoints/BFM_model_front.mat",
        "face_mobilenet_256_1_0_16_best.pth": "https://huggingface.co/OpenTalker/SadTalker/resolve/main/checkpoints/face_mobilenet_256_1_0_16_best.pth",
        "wav2lip.pth": "https://huggingface.co/OpenTalker/SadTalker/resolve/main/checkpoints/wav2lip.pth",
        "mapping_00109-model.pth.tar": "https://huggingface.co/OpenTalker/SadTalker/resolve/main/checkpoints/mapping_00109-model.pth.tar",
        "mapping_00229-model.pth.tar": "https://huggingface.co/OpenTalker/SadTalker/resolve/main/checkpoints/mapping_00229-model.pth.tar",
        "parsing_parsenet.pth": "https://huggingface.co/OpenTalker/SadTalker/resolve/main/checkpoints/parsing_parsenet.pth"
    }
    
    for file_path in missing_files:
        file_name = os.path.basename(file_path)
        
        if file_name in primary_urls:
            logger.info(f"尝试下载 {file_name}...")
            
            # 创建目录
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            # 尝试主要下载源
            try:
                download_file(primary_urls[file_name], file_path)
                if os.path.exists(file_path) and os.path.getsize(file_path) > 1000:
                    logger.info(f"成功下载 {file_name}")
                    continue
                else:
                    logger.warning(f"从主要源下载 {file_name} 失败或文件大小异常")
            except Exception as e:
                logger.error(f"从主要源下载 {file_name} 时出错: {e}")
            
            # 如果主要下载源失败，尝试备用下载源
            if file_name in backup_urls:
                try:
                    logger.info(f"尝试从备用源下载 {file_name}...")
                    download_file(backup_urls[file_name], file_path)
                    if os.path.exists(file_path) and os.path.getsize(file_path) > 1000:
                        logger.info(f"成功从备用源下载 {file_name}")
                    else:
                        logger.warning(f"从备用源下载 {file_name} 失败或文件大小异常")
                except Exception as e:
                    logger.error(f"从备用源下载 {file_name} 时出错: {e}")

def download_file(url: str, dest_path: str) -> None:
    """
    下载文件
    
    Args:
        url: 文件URL
        dest_path: 目标路径
    """
    response = requests.get(url, stream=True, timeout=60)
    if response.status_code == 200:
        with open(dest_path, 'wb') as f:
            total_length = response.headers.get('content-length')
            if total_length is None:
                f.write(response.content)
            else:
                total_length = int(total_length)
                downloaded = 0
                total_mb = total_length / (1024 * 1024)
                for chunk in response.iter_content(chunk_size=4096):
                    downloaded += len(chunk)
                    f.write(chunk)
                    # 每1MB打印一次进度
                    if downloaded % (1024 * 1024) < 4096:
                        percent = (downloaded / total_length) * 100
                        logger.info(f"下载进度: {percent:.1f}% ({downloaded/(1024*1024):.1f}/{total_mb:.1f} MB)")
    else:
        raise Exception(f"下载失败，状态码: {response.status_code}")

def check_avatar_models(project_root: str) -> bool:
    """
    检查Avatar模型文件
    
    Args:
        project_root: 项目根目录
        
    Returns:
        bool: 是否所有必要的目录都已创建
    """
    logger.info("检查Avatar模型目录...")
    
    # 可能的模型目录
    model_dirs = [
        os.path.join(project_root, "backend", "assets", "models"),
        os.path.join(project_root, "assets", "models")
    ]
    
    # 确保目录存在
    for model_dir in model_dirs:
        try:
            os.makedirs(model_dir, exist_ok=True)
            logger.info(f"确保模型目录存在: {model_dir}")
        except Exception as e:
            logger.error(f"创建模型目录失败: {model_dir}, 错误: {e}")
    
    # 检查默认模型文件
    model_types = ['default', 'realistic', 'cartoon', 'stylized']
    
    for model_dir in model_dirs:
        for model_type in model_types:
            model_path = os.path.join(model_dir, f"{model_type}.glb")
            if not os.path.exists(model_path):
                try:
                    # 创建一个简单的占位符模型文件
                    with open(model_path, 'wb') as f:
                        f.write(b'placeholder avatar model file')
                    logger.info(f"创建占位符模型文件: {model_path}")
                except Exception as e:
                    logger.error(f"创建模型文件失败: {model_path}, 错误: {e}")
    
    logger.info("Avatar模型目录检查完成")
    return True

def check_import_problems(project_root: str) -> bool:
    """
    检查常见的导入问题
    
    Args:
        project_root: 项目根目录
        
    Returns:
        bool: 是否所有问题都已修复
    """
    logger.info("检查导入问题...")
    
    # 检查TTS路由器
    tts_router_path = os.path.join(project_root, "backend", "routers", "tts_router.py")
    if not os.path.exists(tts_router_path):
        logger.warning(f"TTS路由器文件不存在: {tts_router_path}")
    else:
        logger.info(f"TTS路由器文件存在: {tts_router_path}")
    
    # 确保routers目录中有__init__.py
    routers_init_path = os.path.join(project_root, "backend", "routers", "__init__.py")
    if not os.path.exists(routers_init_path):
        try:
            with open(routers_init_path, 'w') as f:
                f.write('# Initialize routers package\n')
            logger.info(f"创建routers/__init__.py文件: {routers_init_path}")
        except Exception as e:
            logger.error(f"创建routers/__init__.py文件失败: {routers_init_path}, 错误: {e}")
    
    logger.info("导入问题检查完成")
    return True

def check_pydantic_warnings(project_root: str) -> bool:
    """
    检查Pydantic警告问题
    
    Args:
        project_root: 项目根目录
        
    Returns:
        bool: 是否所有问题都已修复
    """
    logger.info("检查Pydantic警告...")
    
    # 检查main.py中的pydantic_config配置
    main_py_path = os.path.join(project_root, "backend", "main.py")
    if os.path.exists(main_py_path):
        try:
            with open(main_py_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            if "pydantic_config = ConfigDict(protected_namespaces=())" in content:
                logger.info("Pydantic配置已正确设置")
            else:
                logger.warning("Pydantic配置可能需要更新")
        except Exception as e:
            logger.error(f"读取main.py失败: {e}")
    
    logger.info("Pydantic警告检查完成")
    return True

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="修复缺失模型文件和常见问题")
    parser.add_argument("--skip-sadtalker", action="store_true", help="跳过SadTalker模型检查")
    parser.add_argument("--skip-avatar", action="store_true", help="跳过Avatar模型检查")
    parser.add_argument("--skip-imports", action="store_true", help="跳过导入问题检查")
    parser.add_argument("--skip-pydantic", action="store_true", help="跳过Pydantic警告检查")
    
    args = parser.parse_args()
    
    try:
        # 查找项目根目录
        project_root = find_project_root()
        logger.info(f"项目根目录: {project_root}")
        
        results = {}
        
        # 检查SadTalker模型
        if not args.skip_sadtalker:
            results["sadtalker"] = check_sadtalker_models(project_root)
        else:
            logger.info("跳过SadTalker模型检查")
        
        # 检查Avatar模型
        if not args.skip_avatar:
            results["avatar"] = check_avatar_models(project_root)
        else:
            logger.info("跳过Avatar模型检查")
        
        # 检查导入问题
        if not args.skip_imports:
            results["imports"] = check_import_problems(project_root)
        else:
            logger.info("跳过导入问题检查")
        
        # 检查Pydantic警告
        if not args.skip_pydantic:
            results["pydantic"] = check_pydantic_warnings(project_root)
        else:
            logger.info("跳过Pydantic警告检查")
        
        # 打印总结
        logger.info("\n检查结果汇总:")
        for check, result in results.items():
            logger.info(f"{check}: {'成功' if result else '有问题'}")
        
        # 如果所有检查都成功，返回0，否则返回1
        success = all(results.values()) if results else False
        
        logger.info(f"\n总体结果: {'成功' if success else '有问题'}")
        return 0 if success else 1
    
    except Exception as e:
        logger.error(f"脚本执行失败: {e}")
        logger.error(traceback.format_exc())
        return 1

if __name__ == "__main__":
    sys.exit(main()) 