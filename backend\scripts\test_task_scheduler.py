#!/usr/bin/env python
"""
任务调度器测试脚本
用于验证任务调度器的启动、停止和任务调度功能
"""

import os
import sys
import time
import asyncio
import logging
from datetime import datetime

# 确保能够导入后端模块
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("test_task_scheduler")

# 导入任务调度器
from services.task_scheduler import get_task_scheduler, TaskScheduler

# 测试任务函数
async def test_task():
    """测试任务函数"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    logger.info(f"测试任务执行: {timestamp}")
    return {"status": "success", "timestamp": timestamp}

async def main():
    """主测试函数"""
    logger.info("开始测试任务调度器...")
    
    # 获取任务调度器实例
    try:
        scheduler = get_task_scheduler()
        logger.info("成功获取任务调度器实例")
    except Exception as e:
        logger.error(f"获取任务调度器实例失败: {e}")
        return False
    
    # 测试启动方法
    try:
        logger.info("测试启动任务调度器...")
        scheduler.start()
        logger.info("任务调度器启动成功")
    except Exception as e:
        logger.error(f"任务调度器启动失败: {e}")
        return False
    
    # 测试调度一个简单任务
    try:
        logger.info("测试调度一个简单任务...")
        result = await scheduler.schedule_task(
            task_type="test",
            params={"test_param": "value"},
            description="测试任务"
        )
        
        if result.get("success"):
            task_id = result.get("task_id")
            logger.info(f"任务调度成功，任务ID: {task_id}")
            
            # 等待一段时间，让任务有机会执行
            logger.info("等待2秒...")
            await asyncio.sleep(2)
            
            # 获取任务状态
            logger.info(f"获取任务状态...")
            status = await scheduler.get_task_status(task_id)
            logger.info(f"任务状态: {status}")
        else:
            logger.error(f"任务调度失败: {result}")
            return False
    except Exception as e:
        logger.error(f"测试任务调度失败: {e}")
        return False
    
    # 测试停止方法
    try:
        logger.info("测试停止任务调度器...")
        scheduler.stop()
        logger.info("任务调度器停止成功")
    except Exception as e:
        logger.error(f"任务调度器停止失败: {e}")
        return False
    
    logger.info("任务调度器测试完成")
    return True

if __name__ == "__main__":
    try:
        # 运行主测试函数
        result = asyncio.run(main())
        
        if result:
            logger.info("✅ 测试成功")
            sys.exit(0)
        else:
            logger.error("❌ 测试失败")
            sys.exit(1)
    except Exception as e:
        logger.error(f"测试过程中发生异常: {e}")
        sys.exit(1) 