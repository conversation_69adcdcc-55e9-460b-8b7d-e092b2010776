#!/usr/bin/env python3
"""
检查AI服务可用性
"""
import os
import sys
import asyncio
from pathlib import Path
from dotenv import load_dotenv

# 添加后端路径
backend_dir = Path(__file__).parent.parent
sys.path.append(str(backend_dir))

# 加载环境变量
env_path = backend_dir / ".env"
load_dotenv(env_path)

async def check_ollama_service():
    """检查 Ollama 服务"""
    print("🔍 检查 Ollama 服务")
    print("=" * 50)
    
    try:
        import httpx
        
        async with httpx.AsyncClient(timeout=5.0) as client:
            # 检查 Ollama 是否运行
            try:
                response = await client.get("http://localhost:11434/api/tags")
                if response.status_code == 200:
                    models = response.json().get("models", [])
                    print(f"✅ Ollama 服务运行正常")
                    print(f"📊 可用模型数量: {len(models)}")
                    for model in models[:3]:  # 显示前3个模型
                        print(f"   - {model.get('name', 'Unknown')}")
                    return True
                else:
                    print(f"❌ Ollama 服务响应异常: {response.status_code}")
                    return False
            except Exception as e:
                print(f"❌ Ollama 服务不可用: {e}")
                return False
                
    except ImportError:
        print(f"❌ httpx 库未安装")
        return False

async def check_ai_agent_service():
    """检查 AI 智能体服务"""
    print(f"\n🤖 检查 AI 智能体服务")
    print("=" * 50)
    
    try:
        from app.services.ai_agent_service import AIAgentService
        
        service = AIAgentService()
        
        # 模拟智能体数据
        mock_agent = {
            "id": "test-agent",
            "name": "测试智能体",
            "agent_type": "language_tutor",
            "system_prompt": "You are a helpful English teacher."
        }
        
        # 测试对话
        response = await service.chat_with_agent(mock_agent, "Hello, how are you?")
        
        if response and len(response) > 10:
            print(f"✅ AI 智能体服务正常")
            print(f"📊 测试回复: {response[:100]}...")
            return True
        else:
            print(f"❌ AI 智能体服务回复异常: {response}")
            return False
            
    except Exception as e:
        print(f"❌ AI 智能体服务失败: {e}")
        return False

async def check_online_agent_service():
    """检查在线智能体服务"""
    print(f"\n🌐 检查在线智能体服务")
    print("=" * 50)
    
    try:
        from app.services.online_agent_service import OnlineAgentService
        
        service = OnlineAgentService()
        
        # 模拟智能体数据
        mock_agent = {
            "id": "test-agent",
            "name": "测试智能体",
            "agent_type": "language_tutor"
        }
        
        # 测试对话
        response = await service.chat_with_agent(mock_agent, "Hello")
        
        if response and len(response) > 5:
            print(f"✅ 在线智能体服务正常")
            print(f"📊 测试回复: {response[:100]}...")
            return True
        else:
            print(f"❌ 在线智能体服务回复异常: {response}")
            return False
            
    except Exception as e:
        print(f"❌ 在线智能体服务失败: {e}")
        return False

async def check_simple_agent_service():
    """检查简单智能体服务"""
    print(f"\n🔧 检查简单智能体服务")
    print("=" * 50)
    
    try:
        from app.services.simple_agent_service import SimpleAgentService
        
        service = SimpleAgentService()
        
        # 模拟智能体数据
        mock_agent = {
            "id": "test-agent",
            "name": "测试智能体",
            "agent_type": "language_tutor"
        }
        
        # 测试对话
        response = await service.chat_with_agent(mock_agent, "Hello")
        
        if response:
            print(f"✅ 简单智能体服务正常")
            print(f"📊 测试回复: {response}")
            return True
        else:
            print(f"❌ 简单智能体服务无回复")
            return False
            
    except Exception as e:
        print(f"❌ 简单智能体服务失败: {e}")
        return False

async def check_basic_agent_service():
    """检查基础智能体服务"""
    print(f"\n📝 检查基础智能体服务")
    print("=" * 50)
    
    try:
        from app.services.basic_agent_service import BasicAgentService
        
        service = BasicAgentService()
        
        # 模拟智能体数据
        mock_agent = {
            "id": "test-agent",
            "name": "测试智能体",
            "agent_type": "language_tutor"
        }
        
        # 测试对话
        response = await service.chat_with_agent(mock_agent, "Hello")
        
        if response:
            print(f"✅ 基础智能体服务正常")
            print(f"📊 测试回复: {response}")
            return True
        else:
            print(f"❌ 基础智能体服务无回复")
            return False
            
    except Exception as e:
        print(f"❌ 基础智能体服务失败: {e}")
        return False

async def test_chat_api():
    """测试聊天 API"""
    print(f"\n🧪 测试聊天 API")
    print("=" * 50)
    
    try:
        import httpx
        
        url = "http://localhost:8000/api/v1/agents/e55f5e84-6d8b-4265-8e55-728bdb0d2455/chat"
        data = {
            "message": "Hello, this is a test message",
            "user_id": "test-user"
        }
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(url, json=data)
            
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    print(f"✅ 聊天 API 正常")
                    print(f"📊 API 回复: {result.get('response', '')[:100]}...")
                    return True
                else:
                    print(f"❌ 聊天 API 返回错误: {result.get('error')}")
                    return False
            else:
                print(f"❌ 聊天 API 状态码错误: {response.status_code}")
                return False
                
    except Exception as e:
        print(f"❌ 聊天 API 测试失败: {e}")
        return False

async def main():
    """主函数"""
    print("🔍 AI 服务可用性检查")
    print("=" * 60)
    
    # 检查各种服务
    ollama_ok = await check_ollama_service()
    ai_agent_ok = await check_ai_agent_service()
    online_agent_ok = await check_online_agent_service()
    simple_agent_ok = await check_simple_agent_service()
    basic_agent_ok = await check_basic_agent_service()
    api_ok = await test_chat_api()
    
    # 总结
    print(f"\n📊 服务可用性总结:")
    print(f"{'✅' if ollama_ok else '❌'} Ollama 服务: {'可用' if ollama_ok else '不可用'}")
    print(f"{'✅' if ai_agent_ok else '❌'} AI 智能体服务: {'可用' if ai_agent_ok else '不可用'}")
    print(f"{'✅' if online_agent_ok else '❌'} 在线智能体服务: {'可用' if online_agent_ok else '不可用'}")
    print(f"{'✅' if simple_agent_ok else '❌'} 简单智能体服务: {'可用' if simple_agent_ok else '不可用'}")
    print(f"{'✅' if basic_agent_ok else '❌'} 基础智能体服务: {'可用' if basic_agent_ok else '不可用'}")
    print(f"{'✅' if api_ok else '❌'} 聊天 API: {'可用' if api_ok else '不可用'}")
    
    # 建议
    print(f"\n💡 建议:")
    if not ollama_ok:
        print(f"   🔧 启动 Ollama 服务以获得最佳 AI 回复质量")
        print(f"      命令: ollama serve")
    
    if not ai_agent_ok and not online_agent_ok:
        print(f"   ⚠️ 所有高质量 AI 服务都不可用，当前使用基础回复")
        print(f"   🔧 建议启动 Ollama 或配置在线 AI API")
    
    if api_ok:
        print(f"   ✅ 聊天功能正常，但回复质量取决于可用的 AI 服务")
    else:
        print(f"   ❌ 聊天 API 有问题，需要检查后端服务")

if __name__ == "__main__":
    asyncio.run(main())
