"""
数字人生成服务的数据模型定义
"""

from enum import Enum, auto
from typing import Optional, Dict, Any, List, Union
from datetime import datetime
import uuid
import json


class TaskStatus(str, Enum):
    """任务状态枚举"""
    CREATED = "created"       # 任务已创建
    QUEUED = "queued"         # 排队中
    WAITING = "waiting"       # 等待资源
    PROCESSING = "processing" # 处理中
    COMPLETED = "completed"   # 已完成
    FAILED = "failed"         # 失败
    CANCELLED = "cancelled"   # 已取消
    TIMEOUT = "timeout"       # 超时


class GenerationStage(str, Enum):
    """生成阶段枚举"""
    INITIAL = "initial"                   # 初始阶段
    MEDIA_VALIDATION = "media_validation" # 媒体验证
    FACE_DETECTION = "face_detection"     # 人脸检测
    AVATAR_CREATION = "avatar_creation"   # 头像创建
    VOICE_SYNTHESIS = "voice_synthesis"   # 语音合成
    LIPSYNC = "lipsync"                   # 唇形同步
    VIDEO_GENERATION = "video_generation" # 视频生成
    POST_PROCESSING = "post_processing"   # 后处理
    FINAL = "final"                       # 最终阶段


class LogLevel(str, Enum):
    """日志级别枚举"""
    DEBUG = "debug"
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"


class LogEntry:
    """日志条目"""
    
    def __init__(self, 
                 message: str, 
                 level: LogLevel = LogLevel.INFO, 
                 timestamp: Optional[datetime] = None,
                 stage: Optional[str] = None,
                 details: Optional[Dict[str, Any]] = None):
        self.message = message
        self.level = level if isinstance(level, LogLevel) else LogLevel(level)
        self.timestamp = timestamp or datetime.now()
        self.stage = stage
        self.details = details or {}
        
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典表示"""
        return {
            "message": self.message,
            "level": self.level.value,
            "timestamp": self.timestamp.isoformat(),
            "stage": self.stage,
            "details": self.details
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'LogEntry':
        """从字典创建日志条目"""
        return cls(
            message=data["message"],
            level=data.get("level", LogLevel.INFO),
            timestamp=datetime.fromisoformat(data["timestamp"]) if "timestamp" in data else None,
            stage=data.get("stage"),
            details=data.get("details", {})
        )


class GenerationTask:
    """数字人生成任务"""
    
    def __init__(self,
                 task_id: Optional[str] = None,
                 digital_human_id: str = "",
                 user_id: Optional[int] = None,
                 media_file_path: Optional[str] = None,
                 status: Union[TaskStatus, str] = TaskStatus.CREATED,
                 progress: float = 0.0,
                 stage: Union[GenerationStage, str] = GenerationStage.INITIAL,
                 create_time: Optional[datetime] = None,
                 update_time: Optional[datetime] = None,
                 start_time: Optional[datetime] = None,
                 end_time: Optional[datetime] = None,
                 result_url: Optional[str] = None,
                 audio_url: Optional[str] = None,
                 thumbnail_url: Optional[str] = None,
                 error_message: Optional[str] = None,
                 logs: Optional[List[LogEntry]] = None,
                 metadata: Optional[Dict[str, Any]] = None):
        """
        初始化任务
        
        Args:
            task_id: 任务ID，如果不提供会自动生成
            digital_human_id: 数字人ID
            user_id: 用户ID
            media_file_path: 媒体文件路径
            status: 任务状态
            progress: 进度百分比 (0-100)
            stage: 生成阶段
            create_time: 创建时间
            update_time: 最后更新时间
            start_time: 开始处理时间
            end_time: 结束时间
            result_url: 结果视频URL
            audio_url: 音频URL
            thumbnail_url: 缩略图URL
            error_message: 错误信息
            logs: 日志列表
            metadata: 元数据
        """
        self.task_id = task_id or str(uuid.uuid4())
        self.digital_human_id = digital_human_id
        self.user_id = user_id
        self.media_file_path = media_file_path
        
        # 设置状态，确保是TaskStatus枚举
        if isinstance(status, TaskStatus):
            self.status = status
        else:
            try:
                self.status = TaskStatus(status)
            except (ValueError, TypeError):
                self.status = TaskStatus.CREATED
        
        # 设置阶段，确保是GenerationStage枚举
        if isinstance(stage, GenerationStage):
            self.stage = stage
        else:
            try:
                self.stage = GenerationStage(stage)
            except (ValueError, TypeError):
                self.stage = GenerationStage.INITIAL
        
        self.progress = float(progress)
        self.create_time = create_time or datetime.now()
        self.update_time = update_time or self.create_time
        self.start_time = start_time
        self.end_time = end_time
        self.result_url = result_url
        self.audio_url = audio_url
        self.thumbnail_url = thumbnail_url
        self.error_message = error_message
        self.logs = logs or []
        self.metadata = metadata or {}
    
    def add_log(self, message: str, level: Union[LogLevel, str] = LogLevel.INFO, 
                stage: Optional[str] = None, details: Optional[Dict[str, Any]] = None) -> LogEntry:
        """
        添加日志
        
        Args:
            message: 日志消息
            level: 日志级别
            stage: 阶段名称
            details: 详细信息
            
        Returns:
            LogEntry: 添加的日志条目
        """
        # 如果未提供阶段，使用当前阶段
        if stage is None:
            stage = self.stage.value if isinstance(self.stage, GenerationStage) else str(self.stage)
            
        # 创建日志条目
        log_entry = LogEntry(
            message=message,
            level=level,
            timestamp=datetime.now(),
            stage=stage,
            details=details or {}
        )
        
        # 添加到日志列表
        self.logs.append(log_entry)
        
        # 更新时间
        self.update_time = datetime.now()
        
        return log_entry
    
    def update_status(self, status: Union[TaskStatus, str], message: Optional[str] = None,
                      progress: Optional[float] = None) -> None:
        """
        更新任务状态
        
        Args:
            status: 新状态
            message: 状态消息
            progress: 新进度
        """
        # 更新状态
        old_status = self.status
        if isinstance(status, TaskStatus):
            self.status = status
        else:
            try:
                self.status = TaskStatus(status)
            except (ValueError, TypeError):
                pass  # 保持原状态
        
        # 处理状态转换的特殊行为
        if old_status != self.status:
            if self.status == TaskStatus.PROCESSING and self.start_time is None:
                self.start_time = datetime.now()
            elif self.status in (TaskStatus.COMPLETED, TaskStatus.FAILED, 
                               TaskStatus.CANCELLED, TaskStatus.TIMEOUT):
                self.end_time = datetime.now()
        
        # 更新进度
        if progress is not None:
            self.progress = float(progress)
        
        # 添加日志
        if message:
            level = LogLevel.ERROR if self.status == TaskStatus.FAILED else LogLevel.INFO
            self.add_log(message, level=level)
        
        # 更新时间
        self.update_time = datetime.now()
    
    def update_stage(self, stage: Union[GenerationStage, str], 
                    message: Optional[str] = None,
                    progress: Optional[float] = None) -> None:
        """
        更新生成阶段
        
        Args:
            stage: 新阶段
            message: 阶段消息
            progress: 新进度
        """
        # 更新阶段
        old_stage = self.stage
        if isinstance(stage, GenerationStage):
            self.stage = stage
        else:
            try:
                self.stage = GenerationStage(stage)
            except (ValueError, TypeError):
                pass  # 保持原阶段
        
        # 更新进度
        if progress is not None:
            self.progress = float(progress)
        
        # 添加日志
        if message or old_stage != self.stage:
            log_message = message or f"进入阶段: {self.stage.value}"
            self.add_log(log_message, stage=self.stage.value)
        
        # 更新时间
        self.update_time = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典表示
        
        Returns:
            Dict[str, Any]: 任务的字典表示
        """
        return {
            "task_id": self.task_id,
            "digital_human_id": self.digital_human_id,
            "user_id": self.user_id,
            "media_file_path": self.media_file_path,
            "status": self.status.value if isinstance(self.status, TaskStatus) else self.status,
            "progress": self.progress,
            "stage": self.stage.value if isinstance(self.stage, GenerationStage) else self.stage,
            "create_time": self.create_time.isoformat() if self.create_time else None,
            "update_time": self.update_time.isoformat() if self.update_time else None,
            "start_time": self.start_time.isoformat() if self.start_time else None,
            "end_time": self.end_time.isoformat() if self.end_time else None,
            "result_url": self.result_url,
            "audio_url": self.audio_url,
            "thumbnail_url": self.thumbnail_url,
            "error_message": self.error_message,
            "logs": [log.to_dict() for log in self.logs],
            "metadata": self.metadata,
            "time_remaining": self.estimate_remaining_time()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'GenerationTask':
        """
        从字典创建任务
        
        Args:
            data: 任务数据字典
            
        Returns:
            GenerationTask: 创建的任务对象
        """
        # 处理日志
        logs = []
        if "logs" in data and isinstance(data["logs"], list):
            logs = [LogEntry.from_dict(log) if isinstance(log, dict) else log 
                   for log in data["logs"]]
            
        # 处理时间
        create_time = datetime.fromisoformat(data["create_time"]) if data.get("create_time") else None
        update_time = datetime.fromisoformat(data["update_time"]) if data.get("update_time") else None
        start_time = datetime.fromisoformat(data["start_time"]) if data.get("start_time") else None
        end_time = datetime.fromisoformat(data["end_time"]) if data.get("end_time") else None
        
        return cls(
            task_id=data.get("task_id"),
            digital_human_id=data.get("digital_human_id", ""),
            user_id=data.get("user_id"),
            media_file_path=data.get("media_file_path"),
            status=data.get("status", TaskStatus.CREATED),
            progress=data.get("progress", 0.0),
            stage=data.get("stage", GenerationStage.INITIAL),
            create_time=create_time,
            update_time=update_time,
            start_time=start_time,
            end_time=end_time,
            result_url=data.get("result_url"),
            audio_url=data.get("audio_url"),
            thumbnail_url=data.get("thumbnail_url"),
            error_message=data.get("error_message"),
            logs=logs,
            metadata=data.get("metadata", {})
        )
    
    def to_json(self) -> str:
        """
        转换为JSON字符串
        
        Returns:
            str: JSON字符串
        """
        return json.dumps(self.to_dict(), ensure_ascii=False)
    
    @classmethod
    def from_json(cls, json_str: str) -> 'GenerationTask':
        """
        从JSON字符串创建任务
        
        Args:
            json_str: JSON字符串
            
        Returns:
            GenerationTask: 创建的任务对象
        """
        data = json.loads(json_str)
        return cls.from_dict(data)
    
    def estimate_remaining_time(self) -> Optional[int]:
        """
        估算任务剩余完成时间（秒）
        
        Returns:
            Optional[int]: 估计的剩余秒数，如果无法估算则返回None
        """
        # 如果任务已完成或失败，则剩余时间为0
        if self.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, 
                         TaskStatus.CANCELLED, TaskStatus.TIMEOUT]:
            return 0
            
        # 如果进度为0或无效值，返回默认估计值
        if not self.progress or self.progress <= 0:
            return 300  # 默认5分钟
            
        # 如果进度接近完成，返回较短时间
        if self.progress >= 90:
            return 30  # 接近完成时，预计只需30秒
            
        # 基于进度的简单线性估算
        # 假设总任务时间为10分钟，根据当前进度计算剩余时间
        total_estimated_time = 600  # 10分钟（秒）
        elapsed_percent = self.progress / 100.0
        remaining_percent = 1.0 - elapsed_percent
        
        # 计算剩余时间（秒）
        remaining_time = int(total_estimated_time * remaining_percent)
        
        # 确保返回合理范围内的值
        return max(min(remaining_time, 1800), 10)  # 最多30分钟，最少10秒
    
    def __repr__(self) -> str:
        return f"<GenerationTask {self.task_id}: {self.digital_human_id} - {self.status.value}>" 