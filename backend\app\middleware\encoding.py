from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
import logging
import json

logger = logging.getLogger(__name__)

class EncodingMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        try:
            # 处理请求体编码
            if request.method in ["POST", "PUT", "PATCH"]:
                content_type = request.headers.get("content-type", "")
                if "application/json" in content_type:
                    body = await request.body()
                    try:
                        # 尝试UTF-8解码
                        body_str = body.decode("utf-8")
                        # 解析JSON
                        json_data = json.loads(body_str)
                        # 重新编码为UTF-8
                        request._body = json.dumps(json_data, ensure_ascii=False).encode("utf-8")
                    except UnicodeDecodeError as e:
                        logger.error(f"UTF-8解码失败: {str(e)}")
                        raise
                    except json.JSONDecodeError as e:
                        logger.error(f"JSON解析失败: {str(e)}")
                        raise

            # 继续处理请求
            response = await call_next(request)

            # 处理响应编码
            if isinstance(response, Response):
                response.headers["content-type"] = "application/json; charset=utf-8"
                if hasattr(response, "body"):
                    try:
                        body = response.body
                        if isinstance(body, bytes):
                            # 确保响应体是UTF-8编码
                            response.body = body.decode("utf-8").encode("utf-8")
                    except Exception as e:
                        logger.error(f"响应编码处理失败: {str(e)}")

            return response

        except Exception as e:
            logger.error(f"编码中间件错误: {str(e)}")
            raise 