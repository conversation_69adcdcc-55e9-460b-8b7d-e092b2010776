from sqlalchemy import Column, Integer, String, Text, ForeignKey, DateTime, Boolean, JSON, Float, ARRAY
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship, foreign
from pydantic import BaseModel, validator
from typing import Optional, List, Dict, Any, Union
from datetime import datetime
import json
import uuid

from utils.db import Base
from utils.model_helpers import safe_json_loads

# 明确指定可以从此模块导出的符号
__all__ = ["DigitalHuman", "DigitalHumanBase", "DigitalHumanCreate", "DigitalHumanResponse", 
           "ConversationBase", "ConversationCreate"]

class DigitalHuman(Base):
    """Digital human database model"""
    __tablename__ = "digital_humans"
    __table_args__ = {'extend_existing': True}  # 允许表被多次定义，解决SQLAlchemy注册冲突

    id = Column(Integer, primary_key=True, index=True)
    digital_human_id = Column(String, unique=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    name = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)
    
    # 直接匹配前端数据的字段
    type = Column(String(50), nullable=True)  # 数字人类型：assistant, customer_service, broadcaster, teacher
    gender = Column(String(10), nullable=True)  # 性别：male, female, other
    
    # 外观设置相关字段
    appearanceType = Column(String, nullable=True)  # 外观类型：template或upload
    selectedTemplate = Column(String, nullable=True)  # 选择的模板ID
    uploadedImageUrl = Column(String, nullable=True)  # 上传的图像URL
    
    # 声音设置相关字段
    voiceSource = Column(String, nullable=True)  # 声音来源：preset或custom
    voiceType = Column(String, nullable=True)  # 声音类型
    voiceSpeed = Column(Float, default=1.0)    # 语速
    voicePitch = Column(Float, default=1.0)    # 音调
    welcomeText = Column(Text, nullable=True)  # 欢迎语
    customVoiceUrl = Column(String, nullable=True)  # 自定义声音URL
    
    # 应用场景（数组）
    scenario = Column(JSON, nullable=True)  # 存储为JSON字符串的数组
    
    # 保留原有字段以确保兼容性
    personality = Column(Text, nullable=True)  # Personality description / system prompt
    avatar_url = Column(String(255), nullable=True)
    voice_id = Column(String, nullable=True)  # ID for TTS service
    digital_model_url = Column(String, nullable=True)  # 3D model URL
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    is_public = Column(Boolean, default=False)
    
    # 视频生成状态相关字段
    last_generation_id = Column(String, nullable=True)  # 最近一次生成的视频响应ID
    generation_status = Column(String, default="pending")  # pending, processing, completed, failed
    generation_progress = Column(Integer, default=0)  # 生成进度，0-100
    video_url = Column(String(255), nullable=True)  # 实际视频URL路径，包括临时目录视频路径
    audio_url = Column(String, nullable=True)  # 音频URL路径，保存生成的欢迎语音等
    thumbnail_url = Column(String(255), nullable=True)  # 缩略图URL路径
    
    # 以下是原来的多步骤创建字段，保留以确保向后兼容
    style = Column(String, nullable=True)  # 风格：realistic, cartoon, anime, sketch, stylized
    tags = Column(JSON, nullable=True)  # 标签，存储为JSON字符串
    appearance = Column(JSON, nullable=True)  # 外观设置，存储为JSON字符串
    voice_settings = Column(JSON, nullable=True)  # 声音设置，存储为JSON字符串
    media_quality_score = Column(Integer, default=0)  # 媒体质量评分
    media_file_path = Column(String, nullable=True)  # 原始媒体文件路径
    media_file_type = Column(String, nullable=True)  # 媒体文件类型
    generation_task_id = Column(String(255), nullable=True, comment="生成任务ID(字符串UUID格式)")  # 生成任务ID
    creation_step = Column(Integer, default=0)  # 创建步骤：0-5
    
    # 关系
    user = relationship("User", back_populates="digital_humans")
    
    # 更新与任务的关系 - 简化定义，只保留一种关联方式
    tasks = relationship(
        "TaskModel", 
        back_populates="digital_human",
        primaryjoin="DigitalHuman.digital_human_id==foreign(TaskModel.digital_human_id)",
        cascade="all, delete-orphan"
    )
    
    # 添加会话关系
    chat_sessions = relationship("ChatSession", back_populates="digital_human", cascade="all, delete-orphan")
    
    def __init__(self, **kwargs):
        """初始化数字人"""
        # 确保generation_task_id为字符串类型
        if 'generation_task_id' in kwargs and kwargs['generation_task_id'] is not None:
            kwargs['generation_task_id'] = str(kwargs['generation_task_id'])
            
        super().__init__(**kwargs)
        
        # 不再设置id，让数据库自动生成
        # 确保digital_human_id有值
        if not self.digital_human_id:
            self.digital_human_id = f"dh_{uuid.uuid4().hex[:8]}"
            
        # 初始化JSON字段为空对象
        if self.appearance is None:
            self.appearance = {}
            
        if self.voice_settings is None:
            self.voice_settings = {}
            
        if self.scenario is None:
            self.scenario = []
            
        if self.tags is None:
            self.tags = []
            
    def update(self, **kwargs):
        """更新数字人信息"""
        # 特殊处理generation_task_id字段
        if 'generation_task_id' in kwargs and kwargs['generation_task_id'] is not None:
            kwargs['generation_task_id'] = str(kwargs['generation_task_id'])
            
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
        self.updated_at = datetime.now()
    
    # 增加属性装饰器对generation_task_id进行类型保护
    @property
    def generation_task_id_safe(self):
        """获取generation_task_id，确保返回字符串类型或None"""
        if hasattr(self, 'generation_task_id') and self.generation_task_id is not None:
            return str(self.generation_task_id)
        return None
    
    @generation_task_id_safe.setter
    def generation_task_id_safe(self, value):
        """设置generation_task_id，强制转换为字符串类型"""
        if value is not None:
            self.generation_task_id = str(value)
        else:
            self.generation_task_id = None
        
    def add_knowledge_base(self, knowledge_base_id):
        """添加知识库"""
        if not hasattr(self, 'knowledge_base_ids') or self.knowledge_base_ids is None:
            self.knowledge_base_ids = []
        if knowledge_base_id not in self.knowledge_base_ids:
            self.knowledge_base_ids.append(knowledge_base_id)
            
    def remove_knowledge_base(self, knowledge_base_id):
        """移除知识库"""
        if hasattr(self, 'knowledge_base_ids') and self.knowledge_base_ids and knowledge_base_id in self.knowledge_base_ids:
            self.knowledge_base_ids.remove(knowledge_base_id)
            
    def add_tag(self, tag):
        """添加标签"""
        if self.tags is None:
            self.tags = []
        if tag not in self.tags:
            self.tags.append(tag)
            
    def remove_tag(self, tag):
        """移除标签"""
        if self.tags and tag in self.tags:
            self.tags.remove(tag)
            
    def add_scenario(self, scenario):
        """添加应用场景"""
        if self.scenario is None:
            self.scenario = []
        if scenario not in self.scenario:
            self.scenario.append(scenario)
            
    def remove_scenario(self, scenario):
        """移除应用场景"""
        if self.scenario and scenario in self.scenario:
            self.scenario.remove(scenario)
            
    def set_appearance(self, key, value):
        """设置外观属性"""
        if self.appearance is None:
            self.appearance = {}
        self.appearance[key] = value
        
    def set_voice_setting(self, key, value):
        """设置声音属性"""
        if self.voice_settings is None:
            self.voice_settings = {}
        self.voice_settings[key] = value
        
    def set_emotion_setting(self, key, value):
        """设置情感表达属性"""
        if not hasattr(self, 'emotion_settings') or self.emotion_settings is None:
            self.emotion_settings = {}
        self.emotion_settings[key] = value
        
    def set_expression_setting(self, key, value):
        """设置表情属性"""
        if not hasattr(self, 'expression_settings') or self.expression_settings is None:
            self.expression_settings = {}
        self.expression_settings[key] = value
        
    def set_movement_setting(self, key, value):
        """设置动作属性"""
        if not hasattr(self, 'movement_settings') or self.movement_settings is None:
            self.movement_settings = {}
        self.movement_settings[key] = value
        
    def set_memory_setting(self, key, value):
        """设置记忆属性"""
        if not hasattr(self, 'memory_settings') or self.memory_settings is None:
            self.memory_settings = {}
        self.memory_settings[key] = value
        
    def deactivate(self):
        """停用数字人"""
        self.status = "inactive"
        
    def activate(self):
        """激活数字人"""
        self.status = "active"
        
    def delete(self):
        """删除数字人"""
        self.status = "deleted"

class Conversation(Base):
    """Conversation with digital human database model"""
    __tablename__ = "conversations"
    __table_args__ = {'extend_existing': True}  # 允许表被多次定义，解决SQLAlchemy注册冲突

    id = Column(Integer, primary_key=True, index=True)
    conversation_id = Column(String, unique=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    digital_human_id = Column(String, ForeignKey("digital_humans.digital_human_id"))
    messages = Column(Text)  # JSON string of messages
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

# 新增模型类
class AppearanceModel(BaseModel):
    skin_type: Optional[str] = None
    age_range: Optional[str] = None
    face_shape: Optional[str] = None
    eye_color: Optional[str] = None
    hair_style: Optional[str] = None
    hair_color: Optional[str] = None

class MediaQualityCheck(BaseModel):
    name: str
    status: str  # pass, warning, fail
    message: str

# Pydantic models for API
class DigitalHumanBase(BaseModel):
    name: str
    description: Optional[str] = None
    
    # 与前端匹配的字段
    type: Optional[str] = None  # 数字人类型
    gender: Optional[str] = None  # 性别
    scenario: Optional[List[str]] = None  # 应用场景
    
    # 外观设置
    appearanceType: Optional[str] = None  # 外观类型：template或upload
    selectedTemplate: Optional[str] = None  # 选择的模板ID
    uploadedImageUrl: Optional[str] = None  # 上传的图像URL
    
    # 声音设置
    voiceSource: Optional[str] = None  # 声音来源：preset或custom
    voiceType: Optional[str] = None  # 声音类型
    voiceSpeed: Optional[float] = 1.0  # 语速
    voicePitch: Optional[float] = 1.0  # 音调
    welcomeText: Optional[str] = None  # 欢迎语
    customVoiceUrl: Optional[str] = None  # 自定义声音URL
    
    # 原有字段以确保兼容性
    personality: Optional[str] = ""
    avatar_url: Optional[str] = None
    voice_id: Optional[str] = None
    digital_model_url: Optional[str] = None
    style: Optional[str] = None
    tags: Optional[List[str]] = None
    appearance: Optional[Dict[str, Any]] = None
    voice_settings: Optional[Dict[str, Any]] = None
    media_quality_score: Optional[int] = None
    creation_step: Optional[int] = None

class DigitalHumanCreate(DigitalHumanBase):
    pass

class DigitalHumanResponse(DigitalHumanBase):
    id: int
    digital_human_id: str
    user_id: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    is_public: bool
    last_generation_id: Optional[str] = None
    generation_status: Optional[str] = "pending"
    generation_progress: Optional[int] = 0
    video_url: Optional[str] = None
    audio_url: Optional[str] = None
    media_file_path: Optional[str] = None
    media_file_type: Optional[str] = None
    generation_task_id: Optional[str] = None
    
    @validator('generation_task_id', pre=True)
    def ensure_string_generation_task_id(cls, v):
        """确保generation_task_id为字符串类型，对各种输入类型进行处理"""
        if v is None:
            return None
        
        # 处理UUID对象
        if hasattr(v, 'hex'):
            return str(v)
            
        # 处理数字类型
        if isinstance(v, (int, float)):
            return str(int(v))
            
        # 默认字符串转换
        return str(v)
    
    # 添加模型构造后验证
    @classmethod
    def construct(cls, *args, **kwargs):
        """在构造模型后进行额外的类型检查"""
        obj = super().construct(*args, **kwargs)
        if hasattr(obj, 'generation_task_id') and obj.generation_task_id is not None:
            obj.generation_task_id = str(obj.generation_task_id)
        return obj

    class Config:
        from_attributes = True

class MessageModel(BaseModel):
    role: str
    content: str

class ConversationBase(BaseModel):
    digital_human_id: str

class ConversationCreate(ConversationBase):
    pass

class ConversationResponse(ConversationBase):
    id: int
    conversation_id: str
    user_id: int
    messages: List[MessageModel]
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True
    
    @classmethod
    def from_orm(cls, obj):
        # Handle the JSON conversion for messages
        obj_dict = {c.name: getattr(obj, c.name) for c in obj.__table__.columns}
        if obj_dict.get("messages"):
            obj_dict["messages"] = safe_json_loads(
                obj_dict["messages"], 
                default_value=[], 
                expected_type=list,
                field_name="messages", 
                entity_id=obj_dict.get("conversation_id")
            )
        else:
            obj_dict["messages"] = []
        return cls(**obj_dict)

# 新增状态响应模型
class GenerationStatusResponse(BaseModel):
    digital_human_id: str
    status: str  # pending, processing, completed, failed, canceled
    progress: int  # 0-100
    response_id: Optional[str] = None
    video_url: Optional[str] = None
    audio_url: Optional[str] = None

class DigitalHumanDB:
    def __init__(self, db):
        self.db = db
    
    async def get_digital_human_by_id(self, digital_human_id: str):
        """根据digital_human_id获取数字人记录"""
        try:
            # 尝试使用digital_human_id字段查询
            query = await self.db.execute(
                "SELECT * FROM digital_humans WHERE digital_human_id = :digital_human_id",
                {"digital_human_id": digital_human_id}
            )
            result = query.fetchone()
            
            # 如果找不到，尝试用ID的其他形式查询
            if not result:
                # 尝试通过UUID前缀查询
                if len(digital_human_id) >= 8:
                    prefix = digital_human_id[:8]
                    query = await self.db.execute(
                        "SELECT * FROM digital_humans WHERE digital_human_id LIKE :prefix",
                        {"prefix": f"{prefix}%"}
                    )
                    result = query.fetchone()
            
            return result
        except Exception as e:
            import logging
            logging.getLogger(__name__).error(f"获取数字人记录失败: {e}")
            return None
    
    async def update_digital_human_video_url(self, digital_human_id: str, video_url: str) -> bool:
        """更新数字人的视频URL"""
        try:
            # 查找数字人
            digital_human = await self.get_digital_human_by_id(digital_human_id)
            if not digital_human:
                return False
                
            # 更新视频URL
            await self.db.execute(
                "UPDATE digital_humans SET video_url = :video_url WHERE digital_human_id = :digital_human_id",
                {"video_url": video_url, "digital_human_id": digital_human.digital_human_id}
            )
            await self.db.commit()
            return True
        except Exception as e:
            import logging
            logging.getLogger(__name__).error(f"更新数字人视频URL失败: {e}")
            await self.db.rollback()
            return False