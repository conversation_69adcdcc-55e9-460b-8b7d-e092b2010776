import re
import logging
import nltk
from typing import List, Dict, Tuple, Optional, Union
import os

# 设置日志
logger = logging.getLogger("terminology_nlp_utils")

# 设置NLTK数据目录
nltk_data_dir = os.path.join(os.path.expanduser("~"), "nltk_data")
os.makedirs(nltk_data_dir, exist_ok=True)
nltk.data.path.append(nltk_data_dir)

# 安全下载NLTK数据
def safe_nltk_download(package):
    try:
        try:
            nltk.data.find(package)
            logger.info(f"NLTK package {package} already exists")
        except LookupError:
            logger.info(f"Downloading NLTK package {package}")
            nltk.download(package, download_dir=nltk_data_dir, quiet=True)
    except Exception as e:
        logger.warning(f"Failed to download/access NLTK package {package}: {e}")
        return False
    return True

# 尝试下载必要的NLTK数据
punkt_available = safe_nltk_download('tokenizers/punkt')
tagger_available = safe_nltk_download('taggers/averaged_perceptron_tagger')
stopwords_available = safe_nltk_download('corpora/stopwords')
wordnet_available = safe_nltk_download('corpora/wordnet')

# 只有当必要的包可用时才导入
try:
    if punkt_available:
        from nltk.tokenize import word_tokenize, sent_tokenize
    if tagger_available:
        from nltk.tag import pos_tag
    if wordnet_available:
        from nltk.stem import WordNetLemmatizer
    if stopwords_available:
        from nltk.corpus import stopwords
except ImportError as e:
    logger.warning(f"Failed to import NLTK modules: {e}")

# 尝试导入其他依赖
try:
    import spacy
    spacy_available = True
except ImportError:
    logger.warning("spaCy not available")
    spacy_available = False

try:
    import jieba
    import jieba.posseg as pseg
    jieba_available = True
except ImportError:
    logger.warning("jieba not available")
    jieba_available = False

try:
    from langdetect import detect, LangDetectException
    langdetect_available = True
except ImportError:
    logger.warning("langdetect not available")
    langdetect_available = False

# 尝试加载spaCy模型
nlp_en = None
if spacy_available:
    try:
        # 英文模型
        nlp_en = spacy.load("en_core_web_sm")
    except OSError:
        logger.warning("SpaCy en_core_web_sm model not found. Some features may be limited.")

# 备用分词和语言检测函数
def simple_tokenize(text):
    """当NLTK不可用时的备用分词函数"""
    return text.split()

def simple_detect_language(text):
    """当langdetect不可用时的备用语言检测函数"""
    # 简单启发式: 根据ASCII字符比例判断是否为中文
    non_ascii = sum(1 for c in text if ord(c) > 127)
    if non_ascii / max(len(text), 1) > 0.5:
        return "zh"
    return "en"

# 语言检测函数
def detect_language(text: str) -> str:
    """
    检测文本语言
    
    Args:
        text: 要检测的文本
        
    Returns:
        语言代码 (en, zh, fr, de, ru, etc.)
    """
    if not langdetect_available:
        return simple_detect_language(text)

    try:
        return detect(text)
    except Exception:
        # 如果检测失败，返回英语作为默认值
        logger.warning(f"Language detection failed for text: {text[:50]}...")
        return "en"

# 分词函数
def tokenize_text(text: str, language: str = None) -> List[str]:
    """
    根据语言对文本进行分词
    
    Args:
        text: 要分词的文本
        language: 语言代码，如果为None则自动检测
        
    Returns:
        分词列表
    """
    if not text:
        return []
    
    if language is None:
        language = detect_language(text)
    
    if language == "zh" and jieba_available:
        # 中文分词
        return list(jieba.cut(text))
    elif punkt_available:
        # 使用NLTK分词
        return word_tokenize(text)
    else:
        # 备用分词方法
        return simple_tokenize(text)

# 词性标注函数
def pos_tag_text(tokens: List[str], language: str = None) -> List[Tuple[str, str]]:
    """
    对分词结果进行词性标注
    
    Args:
        tokens: 分词列表
        language: 语言代码，如果为None则自动检测
        
    Returns:
        (词, 词性)元组列表
    """
    if not tokens:
        return []
    
    if language is None:
        # 用第一个词尝试检测语言
        if tokens:
            language = detect_language(tokens[0])
        else:
            language = "en"
    
    if language == "zh" and jieba_available:
        # 中文词性标注
        text = "".join(tokens)
        return [(word, tag) for word, tag in pseg.cut(text)]
    elif tagger_available:
        # 英文和其他语言使用NLTK
        return pos_tag(tokens)
    else:
        # 备用方法：返回词和"UNKNOWN"标签
        return [(token, "UNKNOWN") for token in tokens]

# 术语提取函数
def extract_terms(text: str, language: str = None, domain: str = None) -> List[Dict]:
    """
    从文本中提取术语
    
    Args:
        text: 源文本
        language: 语言代码，如果为None则自动检测
        domain: 领域，用于调整术语提取规则
        
    Returns:
        术语列表，每个术语为包含term、pos、confidence等字段的字典
    """
    if not text:
        return []
    
    if language is None:
        language = detect_language(text)
    
    results = []
    
    # 分句
    if punkt_available and language != "zh":
        sentences = sent_tokenize(text)
    else:
        sentences = re.split(r'[。？！；.?!;]', text)
    
    # 使用spaCy进行英文术语提取
    if language == "en" and nlp_en:
        for sent in sentences:
            if not sent.strip():
                continue
                
            doc = nlp_en(sent)
            
            # 提取名词短语
            for chunk in doc.noun_chunks:
                # 基本过滤
                if len(chunk.text) < 2:
                    continue
                
                if stopwords_available:
                    try:
                        if chunk.text.lower() in stopwords.words('english'):
                            continue
                    except:
                        pass
                
                # 计算置信度（简化版）
                confidence = 0.5
                
                # 特殊领域的术语可能有更高的置信度
                if domain:
                    # 这里可以加入特定领域的术语识别规则
                    domain_terms = {
                        "tech": ["algorithm", "api", "framework", "interface", "database"],
                        "medical": ["diagnosis", "treatment", "symptom", "patient", "disease"],
                        "legal": ["plaintiff", "defendant", "court", "attorney", "judge"]
                    }
                    
                    if domain in domain_terms and any(term in chunk.text.lower() for term in domain_terms[domain]):
                        confidence += 0.2
                
                # 基于词性的置信度调整
                if chunk.root.pos_ == "NOUN":
                    confidence += 0.2
                
                # 根据长度调整置信度
                words = chunk.text.split()
                if len(words) >= 2 and len(words) <= 4:
                    confidence += 0.1  # 多词术语通常更可靠
                
                results.append({
                    "term": chunk.text,
                    "pos": chunk.root.pos_,
                    "confidence": min(confidence, 0.95)  # 设置最高置信度上限
                })
    
    # 中文术语提取
    elif language == "zh" and jieba_available:
        for sent in sentences:
            if not sent.strip():
                continue
                
            # 使用jieba进行词性标注
            words_pos = pseg.cut(sent)
            
            # 提取名词和名词短语
            i = 0
            words_pos_list = list(words_pos)
            while i < len(words_pos_list):
                word, tag = words_pos_list[i]
                
                # 基本过滤
                if len(word) < 2:
                    i += 1
                    continue
                
                # 初始置信度
                confidence = 0.5
                
                # 根据词性调整置信度
                if tag.startswith('n'):  # 名词
                    confidence += 0.2
                
                # 如果是专有名词，提高置信度
                if tag == 'nr' or tag == 'ns' or tag == 'nt' or tag == 'nz':
                    confidence += 0.1
                
                # 检查是否是多字组合的专业术语
                if len(word) >= 4:
                    confidence += 0.1
                
                # 添加到结果
                results.append({
                    "term": word,
                    "pos": tag,
                    "confidence": min(confidence, 0.95)
                })
                
                i += 1
    
    # 其他语言使用基本的NLTK处理
    elif punkt_available and tagger_available:
        for sent in sentences:
            if not sent.strip():
                continue
                
            tokens = word_tokenize(sent)
            tagged = pos_tag(tokens)
            
            # 提取名词和名词短语
            i = 0
            while i < len(tagged):
                word, tag = tagged[i]
                
                # 基本过滤
                if len(word) < 2:
                    i += 1
                    continue
                    
                if stopwords_available:
                    try:
                        if word.lower() in stopwords.words('english'):
                            i += 1
                            continue
                    except:
                        pass
                
                # 初始置信度
                confidence = 0.5
                
                # 根据词性调整置信度
                if tag.startswith('NN'):  # 名词
                    confidence += 0.2
                    
                    # 获取完整的名词短语
                    phrase = [word]
                    j = i + 1
                    while j < len(tagged) and (tagged[j][1].startswith('NN') or tagged[j][1].startswith('JJ')):
                        phrase.append(tagged[j][0])
                        j += 1
                    
                    # 如果是多词短语，调整置信度
                    if len(phrase) > 1:
                        confidence += 0.1
                    
                    # 添加到结果
                    results.append({
                        "term": " ".join(phrase),
                        "pos": "NOUN_PHRASE",
                        "confidence": min(confidence, 0.95)
                    })
                    
                    i = j  # 跳过已处理的词
                else:
                    i += 1
    else:
        # 备用方法：简单的关键词提取
        words = tokenize_text(text, language)
        word_freq = {}
        for word in words:
            if len(word) >= 2:
                word_freq[word] = word_freq.get(word, 0) + 1
        
        # 选取出现频率最高的词作为术语
        for word, freq in sorted(word_freq.items(), key=lambda x: x[1], reverse=True)[:20]:
            confidence = min(0.3 + (freq / max(word_freq.values())) * 0.5, 0.95)
            results.append({
                "term": word,
                "pos": "UNKNOWN",
                "confidence": confidence
            })
    
    # 对结果按置信度排序
    return sorted(results, key=lambda x: x["confidence"], reverse=True)

# 清理文本函数
def clean_text(text: str) -> str:
    """
    清理文本，删除特殊字符、多余空格等
    
    Args:
        text: 要清理的文本
        
    Returns:
        清理后的文本
    """
    if not text:
        return ""
    
    # 删除HTML标签
    text = re.sub(r'<[^>]+>', ' ', text)
    
    # 替换换行符为空格
    text = re.sub(r'\n+', ' ', text)
    
    # 删除多余空格
    text = re.sub(r'\s+', ' ', text)
    
    # 删除特殊符号（保留基本标点）
    text = re.sub(r'[^\w\s.,;:!?"\'()-]', ' ', text)
    
    return text.strip()

# 翻译术语函数
def translate_term(term: str, source_lang: str, target_lang: str) -> Dict:
    """
    翻译术语
    
    Args:
        term: 术语
        source_lang: 源语言代码
        target_lang: 目标语言代码
        
    Returns:
        翻译结果字典
    """
    # 这里只是一个模拟实现
    # 实际应用中应该调用翻译API或使用翻译模型
    
    # 简单的示例词典
    translations = {
        "en-zh": {
            "algorithm": "算法",
            "artificial intelligence": "人工智能",
            "machine learning": "机器学习",
            "deep learning": "深度学习",
            "neural network": "神经网络",
            "database": "数据库"
        },
        "zh-en": {
            "算法": "algorithm",
            "人工智能": "artificial intelligence",
            "机器学习": "machine learning",
            "深度学习": "deep learning",
            "神经网络": "neural network",
            "数据库": "database"
        }
    }
    
    # 构建翻译键
    trans_key = f"{source_lang}-{target_lang}"
    
    if trans_key in translations and term.lower() in translations[trans_key]:
        translated = translations[trans_key][term.lower()]
        confidence = 0.95  # 字典中的翻译有很高的置信度
    else:
        # 如果找不到翻译，返回原术语
        translated = term
        confidence = 0.1  # 置信度很低
        
    return {
        "translated_term": translated,
        "confidence": confidence
    } 