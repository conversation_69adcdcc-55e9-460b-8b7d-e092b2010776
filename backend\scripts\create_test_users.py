#!/usr/bin/env python3
"""
创建测试用户工具
用于快速生成测试用户到数据库中
"""
import os
import sys
import logging
from dotenv import load_dotenv
import argparse

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("create-users")

# 加载环境变量
load_dotenv()

# 导入所需模块
try:
    from utils.db import db_session, init_db_tables
    from utils.auth import get_password_hash
    from models.user import User
except ImportError as e:
    logger.error(f"导入模块失败: {e}")
    logger.error("请确保您在项目根目录下运行此脚本，或者将项目根目录添加到PYTHONPATH")
    sys.exit(1)

def create_test_users(users_list, force_update=False):
    """创建测试用户"""
    logger.info(f"创建{len(users_list)}个测试用户...")
    
    # 初始化数据库表
    init_db_tables(force_recreate=False)
    
    created_count = 0
    updated_count = 0
    skipped_count = 0
    
    for user_data in users_list:
        username = user_data["username"]
        email = user_data["email"]
        password = user_data["password"]
        full_name = user_data.get("full_name", "")
        is_admin = user_data.get("is_admin", False)
        
        with db_session() as session:
            try:
                # 检查用户名是否已存在
                existing_user = session.query(User).filter(User.username == username).first()
                
                if existing_user:
                    if force_update:
                        # 更新现有用户
                        logger.info(f"更新用户: {username}")
                        existing_user.full_name = full_name
                        existing_user.is_admin = is_admin
                        
                        # 只有当email不存在或属于当前用户时才更新
                        email_user = session.query(User).filter(
                            User.email == email, 
                            User.id != existing_user.id
                        ).first()
                        
                        if not email_user:
                            existing_user.email = email
                        else:
                            logger.warning(f"邮箱 {email} 已被其他用户使用，不更新邮箱")
                        
                        # 更新密码
                        existing_user.hashed_password = get_password_hash(password)
                        
                        session.commit()
                        updated_count += 1
                    else:
                        # 跳过现有用户
                        logger.info(f"用户已存在，跳过: {username}")
                        skipped_count += 1
                    continue
                
                # 检查邮箱是否已存在
                email_exists = session.query(User).filter(User.email == email).first() is not None
                if email_exists:
                    logger.warning(f"邮箱 {email} 已被其他用户使用，修改为唯一邮箱")
                    # 生成唯一邮箱
                    import random
                    random_suffix = ''.join(str(random.randint(0, 9)) for _ in range(6))
                    email = email.replace('@', f'+{random_suffix}@')
                
                # 创建新用户
                hashed_password = get_password_hash(password)
                new_user = User(
                    username=username,
                    email=email,
                    hashed_password=hashed_password,
                    full_name=full_name,
                    is_admin=is_admin
                )
                session.add(new_user)
                session.commit()
                logger.info(f"创建用户: {username} {'(管理员)' if is_admin else ''}")
                created_count += 1
            except Exception as e:
                logger.error(f"创建/更新用户 {username} 时出错: {e}")
                session.rollback()
    
    logger.info(f"用户创建完成: 新建 {created_count}，更新 {updated_count}，跳过 {skipped_count}")
    return created_count, updated_count, skipped_count

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="创建测试用户工具")
    parser.add_argument("--force", action="store_true", help="强制更新现有用户")
    parser.add_argument("--admin-only", action="store_true", help="仅创建管理员用户")
    
    args = parser.parse_args()
    
    # 定义测试用户列表
    test_users = [
        {
            "username": "admin",
            "email": "<EMAIL>",
            "password": "admin123",
            "full_name": "Administrator",
            "is_admin": True
        },
        {
            "username": "test",
            "email": "<EMAIL>",
            "password": "test123",
            "full_name": "Test User",
            "is_admin": False
        },
        {
            "username": "kobe",
            "email": "<EMAIL>",
            "password": "Limei1992",
            "full_name": "Kobe Test",
            "is_admin": False
        }
    ]
    
    # 如果只创建管理员用户
    if args.admin_only:
        test_users = [user for user in test_users if user.get("is_admin", False)]
    
    # 创建测试用户
    created, updated, skipped = create_test_users(test_users, args.force)
    
    # 打印结果
    logger.info("=" * 50)
    logger.info(f"测试用户创建完成: 新建 {created}，更新 {updated}，跳过 {skipped}")
    logger.info("=" * 50)
    
    # 打印登录信息
    logger.info("测试用户登录信息:")
    for user in test_users:
        logger.info(f"用户名: {user['username']}, 密码: {user['password']}, 角色: {'管理员' if user.get('is_admin', False) else '普通用户'}")

if __name__ == "__main__":
    main() 