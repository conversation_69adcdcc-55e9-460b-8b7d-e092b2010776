#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WAN模型一键修复工具

该脚本用于解决WAN 2.1模型相关的常见问题，包括:
1. 将本地模型目录下的模型复制到缓存目录
2. 修复模型目录结构（创建scheduler, unet, vae子目录并移动文件）
3. 检查路径配置是否正确

使用方法：直接运行该脚本即可自动修复WAN模型相关问题
"""

import os
import sys
import json
import shutil
import logging
import platform
import argparse
import time
from pathlib import Path
from typing import List, Tuple, Dict, Any, Optional

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 获取项目根目录
PROJECT_ROOT = Path(__file__).parent.parent.parent.absolute()
logger.info(f"项目根目录: {PROJECT_ROOT}")

# 模型路径
MODELS_CACHE_DIR = os.path.join(PROJECT_ROOT, "backend", "models_cache")
LOCAL_MODELS_DIR = os.path.join(PROJECT_ROOT, "backend", "local_models", "wan", "Wan2.1")

# 模型映射
MODEL_MAPPINGS = [
    {
        "cache_path": os.path.join(MODELS_CACHE_DIR, "Wan2.1-T2V-1.3B"),
        "local_path": os.path.join(LOCAL_MODELS_DIR, "Wan2.1-T2V-1.3B"),
        "type": "t2v",
        "size": "1.3B"
    },
    {
        "cache_path": os.path.join(MODELS_CACHE_DIR, "Wan2.1-T2V-14B"),
        "local_path": os.path.join(LOCAL_MODELS_DIR, "Wan2.1-T2V-14B"),
        "type": "t2v",
        "size": "14B"
    }
]

# 预期的目录结构
EXPECTED_SUBDIRS = ["scheduler", "unet", "vae"]

# 文件映射 - 根据文件名或特征将文件移动到对应的子目录
FILE_MAPPINGS = {
    "scheduler": ["scheduler", "scheduler_config.json"],
    "unet": ["unet", "diffusion_pytorch_model", "model_index.json"],
    "vae": ["vae", "Wan2.1_VAE.pth", "decoder"],
}

def check_directory_permissions(dir_path: str) -> Tuple[bool, bool, bool, str]:
    """
    检查目录的权限状态
    
    Args:
        dir_path: 要检查的目录路径
        
    Returns:
        Tuple:
            - 目录是否存在
            - 是否有读权限
            - 是否有写权限
            - 诊断信息
    """
    exists = os.path.exists(dir_path)
    readable = False
    writable = False
    msg = ""
    
    if not exists:
        msg = f"目录不存在: {dir_path}"
        return exists, readable, writable, msg
    
    if not os.path.isdir(dir_path):
        msg = f"路径不是目录: {dir_path}"
        return exists, readable, writable, msg
    
    readable = os.access(dir_path, os.R_OK)
    writable = os.access(dir_path, os.W_OK)
    
    if readable and writable:
        msg = f"目录存在且拥有读写权限: {dir_path}"
    elif readable and not writable:
        msg = f"目录存在但只读，无写入权限: {dir_path}"
    elif not readable and writable:
        msg = f"目录存在但只写，无读取权限: {dir_path}"
    else:
        msg = f"目录存在但无读写权限: {dir_path}"
    
    return exists, readable, writable, msg

def copy_model_files(source, target):
    """复制模型文件"""
    try:
        # 如果目标目录已存在，先删除
        if os.path.exists(target):
            logger.info(f"移除现有目标目录: {target}")
            shutil.rmtree(target, ignore_errors=True)
        
        # 复制模型目录
        logger.info(f"复制模型目录: {source} -> {target}")
        start_time = time.time()
        
        # 确保目标父目录存在
        os.makedirs(os.path.dirname(target), exist_ok=True)
        
        # 复制整个目录
        shutil.copytree(source, target)
        
        elapsed_time = time.time() - start_time
        logger.info(f"模型复制完成 ({elapsed_time:.2f}秒)")
        return True
    except Exception as e:
        logger.error(f"复制模型文件失败: {e}")
        return False

def create_subdirectories(model_dir):
    """在模型目录下创建所需的子目录"""
    for subdir in EXPECTED_SUBDIRS:
        subdir_path = os.path.join(model_dir, subdir)
        if not os.path.exists(subdir_path):
            os.makedirs(subdir_path, exist_ok=True)
            logger.info(f"创建子目录: {subdir_path}")

def is_file_matching(file_name, patterns):
    """检查文件名是否匹配模式"""
    file_name_lower = file_name.lower()
    for pattern in patterns:
        if pattern.lower() in file_name_lower:
            return True
    return False

def create_scheduler_config(scheduler_dir):
    """创建默认的scheduler配置文件"""
    config_path = os.path.join(scheduler_dir, "scheduler_config.json")
    if not os.path.exists(config_path):
        scheduler_config = {
            "beta_schedule": "linear",
            "beta_start": 0.00085,
            "beta_end": 0.012,
            "clip_sample": False,
            "num_train_timesteps": 1000,
            "prediction_type": "epsilon"
        }
        with open(config_path, 'w') as f:
            json.dump(scheduler_config, f, indent=2)
        logger.info(f"创建默认scheduler配置: {config_path}")

def create_unet_config(unet_dir):
    """创建默认的unet配置文件"""
    config_path = os.path.join(unet_dir, "config.json")
    if not os.path.exists(config_path):
        unet_config = {
            "in_channels": 4,
            "out_channels": 4,
            "model_type": "UNet3DConditionModel",
            "timestep_spacing": "uniform",
            "unified_pc": True,
            "is_wan2": True
        }
        with open(config_path, 'w') as f:
            json.dump(unet_config, f, indent=2)
        logger.info(f"创建默认unet配置: {config_path}")

def create_vae_config(vae_dir):
    """创建默认的vae配置文件"""
    config_path = os.path.join(vae_dir, "config.json")
    if not os.path.exists(config_path):
        vae_config = {
            "act_fn": "silu",
            "down_block_types": ["DownEncoderBlock2D", "DownEncoderBlock2D", "DownEncoderBlock2D", "DownEncoderBlock2D"],
            "latent_channels": 4,
            "layers_per_block": 2,
            "norm_num_groups": 32,
            "out_channels": 3,
            "sample_size": 512,
            "model_type": "AutoencoderKL"
        }
        with open(config_path, 'w') as f:
            json.dump(vae_config, f, indent=2)
        logger.info(f"创建默认vae配置: {config_path}")

def organize_files(model_dir):
    """将模型目录中的文件组织到正确的子目录中"""
    # 首先创建子目录
    create_subdirectories(model_dir)
    
    # 获取模型目录中的所有文件
    files = [f for f in os.listdir(model_dir) if os.path.isfile(os.path.join(model_dir, f))]
    
    # 移动文件到对应的子目录
    moved_files = []
    for file_name in files:
        file_path = os.path.join(model_dir, file_name)
        
        # 跳过一些不需要移动的文件
        if file_name.startswith('.') or file_name in ["README.md", "LICENSE.txt"]:
            continue
            
        # 确定文件应该被移动到哪个子目录
        target_subdir = None
        for subdir, patterns in FILE_MAPPINGS.items():
            if is_file_matching(file_name, patterns):
                target_subdir = subdir
                break
        
        # 如果找不到匹配的子目录，默认移动到unet
        if target_subdir is None:
            if "diffusion" in file_name.lower() or "model" in file_name.lower():
                target_subdir = "unet"
            else:
                logger.warning(f"无法确定文件 {file_name} 的目标子目录，跳过")
                continue
        
        # 移动文件
        target_dir = os.path.join(model_dir, target_subdir)
        target_path = os.path.join(target_dir, file_name)
        
        try:
            shutil.copy2(file_path, target_path)
            logger.info(f"复制文件: {file_path} -> {target_path}")
            moved_files.append((file_name, target_subdir))
        except Exception as e:
            logger.error(f"复制文件 {file_name} 失败: {e}")
    
    # 创建必要的配置文件
    create_scheduler_config(os.path.join(model_dir, "scheduler"))
    create_unet_config(os.path.join(model_dir, "unet"))
    create_vae_config(os.path.join(model_dir, "vae"))
    
    # 复制VAE文件
    vae_file = os.path.join(model_dir, "Wan2.1_VAE.pth")
    if os.path.exists(vae_file):
        target_vae = os.path.join(model_dir, "vae", "diffusion_pytorch_model.safetensors")
        if not os.path.exists(target_vae):
            try:
                shutil.copy2(vae_file, target_vae)
                logger.info(f"复制VAE模型: {vae_file} -> {target_vae}")
            except Exception as e:
                logger.error(f"复制VAE模型失败: {e}")
    
    # 复制Diffusion模型
    diffusion_file = os.path.join(model_dir, "diffusion_pytorch_model.safetensors")
    if os.path.exists(diffusion_file):
        target_unet = os.path.join(model_dir, "unet", "diffusion_pytorch_model.safetensors")
        if not os.path.exists(target_unet):
            try:
                shutil.copy2(diffusion_file, target_unet)
                logger.info(f"复制Diffusion模型: {diffusion_file} -> {target_unet}")
            except Exception as e:
                logger.error(f"复制Diffusion模型失败: {e}")
    
    # 创建模型索引文件
    model_index_path = os.path.join(model_dir, "model_index.json")
    if not os.path.exists(model_index_path):
        model_index = {
            "format": "wan2",
            "submodel_types": {
                "unet": "UNet3DConditionModel",
                "vae": "AutoencoderKL",
                "scheduler": "DDIMScheduler"
            },
            "_diffusers_version": "0.26.0",
            "wan_version": "2.1"
        }
        with open(model_index_path, 'w') as f:
            json.dump(model_index, f, indent=2)
        logger.info(f"创建模型索引: {model_index_path}")
    
    return moved_files

def fix_model_structure(model_dir):
    """修复单个模型的目录结构"""
    logger.info(f"开始修复模型目录结构: {model_dir}")
    
    # 检查模型目录是否存在
    if not os.path.exists(model_dir):
        logger.error(f"模型目录不存在: {model_dir}")
        return False
    
    # 组织文件
    moved_files = organize_files(model_dir)
    
    # 创建日志
    log_message = f"已处理 {len(moved_files)} 个文件:\n"
    for file_name, target_subdir in moved_files:
        log_message += f"  - {file_name} -> {target_subdir}\n"
    logger.info(log_message)
    
    # 检查目录结构是否完整
    missing_subdirs = []
    for subdir in EXPECTED_SUBDIRS:
        subdir_path = os.path.join(model_dir, subdir)
        if not os.path.exists(subdir_path) or not os.path.isdir(subdir_path) or not os.listdir(subdir_path):
            missing_subdirs.append(subdir)
    
    if missing_subdirs:
        logger.error(f"目录结构修复后仍不完整，缺少子目录或子目录为空: {', '.join(missing_subdirs)}")
        return False
    
    logger.info(f"模型目录结构修复完成: {model_dir}")
    return True

def check_models_cache_dir():
    """检查模型缓存目录是否存在并可访问"""
    # 确保模型缓存目录存在
    if not os.path.exists(MODELS_CACHE_DIR):
        logger.info(f"创建模型缓存目录: {MODELS_CACHE_DIR}")
        os.makedirs(MODELS_CACHE_DIR, exist_ok=True)
    
    # 检查权限
    exists, readable, writable, msg = check_directory_permissions(MODELS_CACHE_DIR)
    logger.info(f"模型缓存目录状态: {msg}")
    
    if not (exists and readable and writable):
        logger.error(f"模型缓存目录不可用，权限不足: {MODELS_CACHE_DIR}")
        return False
    
    return True

def copy_from_local_to_cache():
    """从本地模型目录复制到缓存目录"""
    success_count = 0
    failed_count = 0
    
    # 确保本地模型目录存在
    if not os.path.exists(LOCAL_MODELS_DIR):
        logger.warning(f"本地模型目录不存在: {LOCAL_MODELS_DIR}")
        return 0, 0
    
    for mapping in MODEL_MAPPINGS:
        local_path = mapping["local_path"]
        cache_path = mapping["cache_path"]
        model_type = mapping["type"]
        model_size = mapping["size"]
        
        logger.info(f"处理模型: {model_type}-{model_size}")
        
        # 检查本地模型目录是否存在
        if not os.path.exists(local_path):
            logger.warning(f"本地模型目录不存在: {local_path}")
            failed_count += 1
            continue
        
        # 复制模型
        if copy_model_files(local_path, cache_path):
            success_count += 1
        else:
            failed_count += 1
    
    return success_count, failed_count

def fix_configs():
    """修复配置文件"""
    config_file = os.path.join(PROJECT_ROOT, "backend", "config", "models.py")
    if not os.path.exists(config_file):
        logger.warning(f"配置文件不存在: {config_file}")
        return False
    
    # 创建默认配置
    default_config = {
        "model_cache_dir": "backend/models_cache",
        "t2v_14b_model": "Wan2.1-T2V-14B",
        "t2v_1_3b_model": "Wan2.1-T2V-1.3B",
        "i2v_720p_model": "Wan2.1-I2V-720P",
        "i2v_480p_model": "Wan2.1-I2V-480P",
        "device": "cuda",
        "precision": "bfloat16",
        "mock_mode": False,
        "max_retry_attempts": 3
    }
    
    # 读取现有配置
    with open(config_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 简单检查配置是否需要更新
    needs_update = False
    if "model_cache_dir" in content and "E:" in content:
        # 配置包含硬编码的绝对路径，需要更新
        needs_update = True
    
    if not needs_update:
        logger.info("配置文件已是相对路径，无需更新")
        return True
    
    # 创建备份
    backup_file = f"{config_file}.bak.{int(time.time())}"
    logger.info(f"创建配置文件备份: {backup_file}")
    shutil.copy2(config_file, backup_file)
    
    # 尝试更新配置
    try:
        # 读取配置并提取WAN_CONFIG部分
        import re
        
        config_pattern = r"WAN_CONFIG\s*=\s*{[\s\S]*?}"
        new_config = "WAN_CONFIG = {\n"
        for key, value in default_config.items():
            if isinstance(value, str):
                new_config += f'    "{key}": "{value}",\n'
            else:
                new_config += f'    "{key}": {value},\n'
        new_config += "}"
        
        # 替换配置
        new_content = re.sub(config_pattern, new_config, content)
        
        # 写入新配置
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        logger.info(f"配置文件已更新: {config_file}")
        return True
    except Exception as e:
        logger.error(f"更新配置文件失败: {e}")
        # 尝试恢复备份
        if os.path.exists(backup_file):
            shutil.copy2(backup_file, config_file)
            logger.info("已从备份恢复配置文件")
        return False

def main():
    parser = argparse.ArgumentParser(description="WAN模型一键修复工具")
    parser.add_argument("--verbose", action="store_true", help="显示详细日志")
    args = parser.parse_args()
    
    if args.verbose:
        logger.setLevel(logging.DEBUG)
    
    print("\n===== WAN模型一键修复工具 =====\n")
    
    print(f"系统信息:")
    print(f"  操作系统: {platform.system()} {platform.release()}")
    print(f"  Python版本: {platform.python_version()}")
    print(f"  工作目录: {os.getcwd()}")
    print()
    
    print(f"模型路径:")
    print(f"  缓存目录: {MODELS_CACHE_DIR}")
    print(f"  本地目录: {LOCAL_MODELS_DIR}")
    print()
    
    # 第1步: 检查缓存目录
    print("步骤1: 检查模型缓存目录")
    if check_models_cache_dir():
        print("✓ 模型缓存目录正常")
    else:
        print("✗ 模型缓存目录检查失败")
        print("\n请确保程序有权限访问和写入模型缓存目录。")
        return
    print()
    
    # 第2步: 从本地目录复制模型到缓存目录
    print("步骤2: 从本地目录复制模型文件")
    success_count, failed_count = copy_from_local_to_cache()
    if success_count > 0:
        print(f"✓ 已成功复制 {success_count} 个模型")
    else:
        print("✗ 未能复制任何模型")
    if failed_count > 0:
        print(f"  注: {failed_count} 个模型复制失败")
    print()
    
    # 第3步: 修复模型目录结构
    print("步骤3: 修复模型目录结构")
    model_dirs = []
    if os.path.exists(MODELS_CACHE_DIR):
        for item in os.listdir(MODELS_CACHE_DIR):
            if item.startswith("Wan2.1-T2V-"):
                model_dirs.append(os.path.join(MODELS_CACHE_DIR, item))
    
    success_count = 0
    failed_count = 0
    for model_dir in model_dirs:
        model_name = os.path.basename(model_dir)
        print(f"  处理模型: {model_name}")
        if fix_model_structure(model_dir):
            success_count += 1
            print(f"  ✓ 目录结构修复成功: {model_name}")
        else:
            failed_count += 1
            print(f"  ✗ 目录结构修复失败: {model_name}")
    
    if success_count > 0:
        print(f"✓ 已成功修复 {success_count} 个模型的目录结构")
    else:
        print("✗ 未能修复任何模型的目录结构")
    if failed_count > 0:
        print(f"  注: {failed_count} 个模型目录结构修复失败")
    print()
    
    # 第4步: 修复配置文件
    print("步骤4: 修复配置文件")
    if fix_configs():
        print("✓ 配置文件已修复")
    else:
        print("✗ 配置文件修复失败")
    print()
    
    # 总结
    print("\n修复结果:")
    if success_count + failed_count == 0:
        print("未找到需要修复的模型")
    elif failed_count == 0:
        print("所有问题已成功修复！")
    else:
        print("部分问题未能修复，请查看日志了解详情。")
    
    print("\n请重新启动应用程序以应用修复。")
    print("\n===== 修复过程完成 =====\n")

if __name__ == "__main__":
    main() 