import re
import logging
import uuid
from typing import Dict, Any, List, Optional, Callable, Union, Tuple
from datetime import datetime

logger = logging.getLogger(__name__)

def normalize_task_id(task_id: str, task_store: Dict[str, Any], prefix_options: List[str] = None) -> str:
    """
    统一规范化任务ID，处理不同格式的任务ID输入
    
    Args:
        task_id: 原始任务ID
        task_store: 任务存储字典，用于查找任务
        prefix_options: 可选的前缀列表，例如 ["job_", "task_"]
    
    Returns:
        规范化后的任务ID
    """
    # 如果未提供前缀选项，使用默认值
    if prefix_options is None:
        prefix_options = ["job_", "task_"]
    
    # 保存原始ID以便日志记录
    original_id = task_id
    
    # 如果为空，返回原始值
    if not task_id:
        logger.warning(f"尝试规范化空任务ID")
        return task_id
    
    # 如果任务ID已存在于存储中，直接返回
    if task_id in task_store:
        return task_id
        
    # 1. 如果任务ID不带前缀，尝试添加前缀
    if not any(task_id.startswith(prefix) for prefix in prefix_options):
        for prefix in prefix_options:
            potential_id = f"{prefix}{task_id}"
            if potential_id in task_store:
                logger.info(f"任务ID已规范化: {task_id} -> {potential_id}")
                return potential_id
    
    # 2. 如果是完整的UUID格式，尝试获取简短格式
    if "-" in task_id and len(task_id) > 30:  # 可能是完整UUID
        uuid_short = task_id.split("-")[0]
        for prefix in prefix_options:
            potential_id = f"{prefix}{uuid_short}"
            if potential_id in task_store:
                logger.info(f"UUID已转换为短ID: {task_id} -> {potential_id}")
                return potential_id
    
    # 3. 如果带了前缀但不匹配当前存储的前缀，尝试替换前缀
    for prefix in prefix_options:
        if task_id.startswith(prefix):
            # 找到当前前缀，尝试用其他前缀替换
            id_without_prefix = task_id[len(prefix):]
            for other_prefix in prefix_options:
                if other_prefix != prefix:
                    potential_id = f"{other_prefix}{id_without_prefix}"
                    if potential_id in task_store:
                        logger.info(f"替换前缀: {task_id} -> {potential_id}")
                        return potential_id
    
    # 4. 尝试模糊匹配查找
    if task_id not in task_store:
        # 提取任务ID中的数字部分，如果有的话
        num_match = re.search(r'(\d+)', task_id)
        if num_match:
            num_part = num_match.group(1)
            # 搜索所有包含此数字的任务ID
            for active_id in task_store.keys():
                if num_part in active_id:
                    logger.info(f"通过数字部分匹配找到任务: {task_id} -> {active_id}")
                    return active_id
    
    # 5. 最后尝试：列出最近的几个任务ID，检查是否有相似的
    if task_id not in task_store:
        close_matches = []
        for active_id in task_store.keys():
            # 简单的相似度检查：共同字符比例
            common_chars = set(task_id.lower()) & set(active_id.lower())
            if len(common_chars) >= min(3, len(task_id) // 2):
                close_matches.append(active_id)
        
        if close_matches:
            # 使用第一个相似匹配
            matched_id = close_matches[0]
            logger.info(f"使用相似任务ID匹配: {task_id} -> {matched_id}, 可能的匹配: {close_matches}")
            return matched_id
    
    # 6. 如果都找不到，尝试查找最近创建的任务
    if task_store and len(task_store) > 0:
        try:
            # 查找最新的5个任务
            recent_tasks = sorted(
                [(tid, data.get('created_at', '')) for tid, data in task_store.items()],
                key=lambda x: x[1], 
                reverse=True
            )[:5]
            
            if recent_tasks:
                logger.info(f"未找到任务ID匹配: {task_id}，提供最近任务信息: {[t[0] for t in recent_tasks]}")
        except Exception as e:
            logger.error(f"尝试查找最近任务时出错: {str(e)}")
            
    # 7. 如果都找不到，返回原始任务ID
    if task_id not in task_store:
        logger.warning(f"未找到任务匹配 {task_id}，返回原始ID")
    
    return task_id

def generate_task_id(prefix: str = "task_") -> str:
    """
    生成统一格式的任务ID
    
    Args:
        prefix: 任务ID前缀
    
    Returns:
        生成的任务ID
    """
    unique_id = str(uuid.uuid4())
    task_id = f"{prefix}{unique_id[:8]}"
    return task_id

def find_task_by_id(task_id: str, task_stores: List[Dict[str, Any]]) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
    """
    在多个任务存储字典中查找任务
    
    Args:
        task_id: 任务ID
        task_stores: 任务存储字典列表
    
    Returns:
        任务数据和对应的任务字典
    """
    # 首先检查是否可以在任一存储中直接找到任务ID
    for store_name, task_store in task_stores:
        normalized_id = normalize_task_id(task_id, task_store)
        if normalized_id in task_store:
            return task_store[normalized_id], store_name
    
    # 如果没有找到，返回None
    return None, None

def create_task_base(user_id: Optional[int] = None, prefix: str = "task_") -> Dict[str, Any]:
    """
    创建基础任务数据结构
    
    Args:
        user_id: 用户ID
        prefix: 任务ID前缀
    
    Returns:
        基础任务数据字典
    """
    task_id = generate_task_id(prefix)
    
    return {
        "task_id": task_id,
        "job_id": task_id,  # 为兼容性提供job_id字段
        "user_id": user_id,
        "status": "created",
        "progress": 0,
        "created_at": datetime.now().isoformat(),
        "updated_at": datetime.now().isoformat(),
        "message": "任务已创建"
    }

def update_task_status(task: Dict[str, Any], 
                      status: str, 
                      progress: int = None, 
                      message: str = None,
                      **additional_fields) -> Dict[str, Any]:
    """
    更新任务状态
    
    Args:
        task: 任务数据字典
        status: 新状态
        progress: 进度值(0-100)
        message: 状态消息
        additional_fields: 要更新的其他字段
    
    Returns:
        更新后的任务数据字典
    """
    # 更新基本字段
    task["status"] = status
    task["updated_at"] = datetime.now().isoformat()
    
    # 如果提供了进度，更新进度
    if progress is not None:
        task["progress"] = progress
    
    # 如果提供了消息，更新消息
    if message is not None:
        task["message"] = message
    
    # 更新其他字段
    for field, value in additional_fields.items():
        task[field] = value
    
    return task 