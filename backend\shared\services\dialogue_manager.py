#!/usr/bin/env python3
"""
对话管理器

协调语音合成、口型同步和情感分析等服务，管理数字人聊天过程。
"""

import os
import logging
import json
import base64
import asyncio
from typing import Dict, Any, List, Optional, Tuple, Union, Callable
from datetime import datetime
import uuid

# 导入服务
from .emotion_analysis_service import get_emotion_analysis_service
from .enhanced_tts_service import get_enhanced_tts_service
from .lip_sync_service import get_lip_sync_service
from .knowledge_service import get_knowledge_service

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DialogueManager:
    """对话管理器类"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化对话管理器
        
        Args:
            config: 对话管理器配置
        """
        self.config = config or {}
        
        # 加载服务
        self.emotion_service = get_emotion_analysis_service(self.config.get('emotion_analysis', {}))
        self.tts_service = get_enhanced_tts_service(self.config.get('tts', {}))
        self.lip_sync_service = get_lip_sync_service(self.config.get('lip_sync', {}))
        
        try:
            self.knowledge_service = get_knowledge_service(self.config.get('knowledge', {}))
        except Exception as e:
            logger.warning(f"知识服务加载失败: {e}")
            self.knowledge_service = None
        
        # 会话状态
        self.sessions = {}
        
        # 回调函数
        self.callbacks = {}
    
    def register_callback(self, event_type: str, callback: Callable):
        """
        注册事件回调函数
        
        Args:
            event_type: 事件类型
            callback: 回调函数
        """
        if event_type not in self.callbacks:
            self.callbacks[event_type] = []
        
        self.callbacks[event_type].append(callback)
    
    def _trigger_callback(self, event_type: str, data: Dict[str, Any]):
        """
        触发事件回调
        
        Args:
            event_type: 事件类型
            data: 事件数据
        """
        if event_type in self.callbacks:
            for callback in self.callbacks[event_type]:
                try:
                    callback(data)
                except Exception as e:
                    logger.error(f"回调函数执行失败: {e}")
    
    def create_session(self, session_id: str, user_id: str, digital_human_id: str, metadata: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        创建新的对话会话
        
        Args:
            session_id: 会话ID
            user_id: 用户ID
            digital_human_id: 数字人ID
            metadata: 会话元数据
        
        Returns:
            会话信息
        """
        if session_id in self.sessions:
            logger.warning(f"会话已存在: {session_id}")
            return self.sessions[session_id]
        
        # 创建新会话
        session = {
            'session_id': session_id,
            'user_id': user_id,
            'digital_human_id': digital_human_id,
            'start_time': datetime.now().isoformat(),
            'end_time': None,
            'messages': [],
            'metadata': metadata or {},
            'context': {
                'emotion_history': [],
                'topics': [],
                'last_response_time': None
            },
            'voice_settings': {
                'voice_id': None,
                'enable_lip_sync': True,
                'enable_emotion_analysis': True
            }
        }
        
        self.sessions[session_id] = session
        
        # 触发会话创建事件
        self._trigger_callback('session_created', {
            'session_id': session_id,
            'user_id': user_id,
            'digital_human_id': digital_human_id,
            'time': session['start_time']
        })
        
        return session
    
    def end_session(self, session_id: str) -> bool:
        """
        结束对话会话
        
        Args:
            session_id: 会话ID
        
        Returns:
            是否成功结束会话
        """
        if session_id not in self.sessions:
            logger.warning(f"会话不存在: {session_id}")
            return False
        
        session = self.sessions[session_id]
        session['end_time'] = datetime.now().isoformat()
        
        # 触发会话结束事件
        self._trigger_callback('session_ended', {
            'session_id': session_id,
            'user_id': session['user_id'],
            'digital_human_id': session['digital_human_id'],
            'start_time': session['start_time'],
            'end_time': session['end_time'],
            'message_count': len(session['messages'])
        })
        
        return True
    
    def get_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """
        获取会话信息
        
        Args:
            session_id: 会话ID
        
        Returns:
            会话信息，如果不存在则返回None
        """
        return self.sessions.get(session_id)
    
    def update_session_voice_settings(self, session_id: str, settings: Dict[str, Any]) -> bool:
        """
        更新会话语音设置
        
        Args:
            session_id: 会话ID
            settings: 语音设置
        
        Returns:
            是否成功更新
        """
        if session_id not in self.sessions:
            logger.warning(f"会话不存在: {session_id}")
            return False
        
        session = self.sessions[session_id]
        
        # 更新设置
        for key, value in settings.items():
            if key in session['voice_settings']:
                session['voice_settings'][key] = value
        
        return True
    
    async def process_message(
        self, 
        session_id: str, 
        message: Dict[str, Any],
        message_callback: Callable = None
    ) -> Dict[str, Any]:
        """
        处理用户消息
        
        Args:
            session_id: 会话ID
            message: 消息内容
            message_callback: 消息处理回调函数
        
        Returns:
            处理结果
        """
        if session_id not in self.sessions:
            logger.warning(f"会话不存在: {session_id}")
            return {
                'success': False,
                'error': '会话不存在',
                'message_id': None
            }
        
        session = self.sessions[session_id]
        
        # 提取消息内容
        message_id = message.get('message_id', str(uuid.uuid4()))
        text = message.get('text', '')
        audio_data = message.get('audio_data')
        audio_format = message.get('audio_format', 'wav')
        
        # 情感分析
        emotion_result = None
        if session['voice_settings']['enable_emotion_analysis']:
            # 分析文本情感
            text_emotion = self.emotion_service.analyze_text(text)
            
            # 如果有音频，分析音频情感
            audio_emotion = None
            if audio_data:
                audio_bytes = base64.b64decode(audio_data) if isinstance(audio_data, str) else audio_data
                audio_emotion = self.emotion_service.analyze_audio(audio_bytes, audio_format)
            
            # 组合分析结果
            emotion_result = self.emotion_service.combine_analysis_results(text_emotion, audio_emotion)
            
            # 更新情感历史
            session['context']['emotion_history'].append({
                'time': datetime.now().isoformat(),
                'emotion': emotion_result['emotion'],
                'confidence': emotion_result['confidence']
            })
            
            # 保留最近10条情感记录
            if len(session['context']['emotion_history']) > 10:
                session['context']['emotion_history'] = session['context']['emotion_history'][-10:]
        
        # 构建用户消息
        user_message = {
            'message_id': message_id,
            'sender_id': session['user_id'],
            'receiver_id': session['digital_human_id'],
            'text': text,
            'audio': {
                'has_audio': audio_data is not None,
                'format': audio_format
            },
            'emotion': emotion_result,
            'time': datetime.now().isoformat(),
            'type': 'user'
        }
        
        # 添加到会话消息
        session['messages'].append(user_message)
        
        # 触发用户消息事件
        self._trigger_callback('user_message', {
            'session_id': session_id,
            'message': user_message
        })
        
        # 如果提供了回调函数，调用它处理AI回复
        if message_callback:
            try:
                # 调用回调获取AI回复
                ai_response = await message_callback(session, user_message)
                
                # 处理AI回复
                if ai_response:
                    await self.process_ai_response(session_id, ai_response, user_message)
                    return {
                        'success': True,
                        'message_id': message_id,
                        'response_id': ai_response.get('message_id')
                    }
            except Exception as e:
                logger.error(f"处理AI回复失败: {e}")
                return {
                    'success': False,
                    'error': f'处理AI回复失败: {str(e)}',
                    'message_id': message_id
                }
        
        return {
            'success': True,
            'message_id': message_id
        }
    
    async def process_ai_response(
        self, 
        session_id: str, 
        response: Dict[str, Any],
        user_message: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        处理AI回复
        
        Args:
            session_id: 会话ID
            response: AI回复内容
            user_message: 用户消息
        
        Returns:
            处理结果
        """
        if session_id not in self.sessions:
            logger.warning(f"会话不存在: {session_id}")
            return {
                'success': False,
                'error': '会话不存在'
            }
        
        session = self.sessions[session_id]
        
        # 确保response包含必要的字段
        if 'text' not in response:
            logger.error("AI回复缺少文本内容")
            return {
                'success': False,
                'error': 'AI回复缺少文本内容'
            }
        
        # 提取回复内容
        response_id = response.get('message_id', str(uuid.uuid4()))
        text = response['text']
        
        # 获取语音设置
        voice_id = response.get('voice_id') or session['voice_settings'].get('voice_id')
        enable_lip_sync = session['voice_settings'].get('enable_lip_sync', True)
        
        # 检查用户情感，调整回复风格
        response_style = None
        if user_message and 'emotion' in user_message and user_message['emotion']:
            user_emotion = user_message['emotion'].get('emotion', 'neutral')
            user_confidence = user_message['emotion'].get('confidence', 0.5)
            
            # 获取基于用户情感的回复风格
            response_style = self.emotion_service.get_response_style(user_emotion, user_confidence)
        
        # 合成语音
        speech_result = None
        if voice_id:
            # 准备TTS参数
            tts_params = {}
            
            # 如果有情感调整，应用到语音参数
            if response_style:
                # 映射风格到TTS参数
                if response_style['tone'] == 'cheerful':
                    tts_params['rate'] = 1.1
                    tts_params['pitch'] = 0.3
                elif response_style['tone'] == 'soothing':
                    tts_params['rate'] = 0.9
                    tts_params['pitch'] = -0.1
                elif response_style['tone'] == 'calming':
                    tts_params['rate'] = 0.95
                    tts_params['pitch'] = 0.0
                # ...其他映射...
            
            # 合成语音
            speech_result = self.tts_service.synthesize_speech(
                text=text,
                voice_id=voice_id,
                emotion=user_message['emotion']['emotion'] if user_message and 'emotion' in user_message else 'neutral',
                params=tts_params
            )
        
        # 生成口型同步数据
        lip_sync_result = None
        if enable_lip_sync:
            # 计算音频持续时间
            duration = None
            if speech_result and speech_result.get('duration'):
                duration = speech_result['duration']
            
            # 生成口型数据
            lip_sync_result = self.lip_sync_service.generate_lip_sync_data(
                text=text,
                audio_data=base64.b64decode(speech_result['audio_data']) if speech_result and speech_result.get('audio_data') else None,
                duration=duration
            )
            
            # 转换为动画数据
            if lip_sync_result and lip_sync_result.get('success'):
                lip_sync_result = self.lip_sync_service.lip_sync_to_animation_data(lip_sync_result)
        
        # 构建AI回复
        ai_response = {
            'message_id': response_id,
            'sender_id': session['digital_human_id'],
            'receiver_id': session['user_id'],
            'text': text,
            'time': datetime.now().isoformat(),
            'type': 'ai',
            'speech': speech_result,
            'lip_sync': lip_sync_result,
            'style': response_style
        }
        
        # 添加到会话消息
        session['messages'].append(ai_response)
        session['context']['last_response_time'] = ai_response['time']
        
        # 触发AI回复事件
        self._trigger_callback('ai_response', {
            'session_id': session_id,
            'message': ai_response
        })
        
        return {
            'success': True,
            'message_id': response_id,
            'response': ai_response
        }
    
    def get_conversation_context(
        self, 
        session_id: str, 
        message_count: int = 10
    ) -> Dict[str, Any]:
        """
        获取对话上下文
        
        Args:
            session_id: 会话ID
            message_count: 获取的消息数量
        
        Returns:
            对话上下文
        """
        if session_id not in self.sessions:
            logger.warning(f"会话不存在: {session_id}")
            return {
                'success': False,
                'error': '会话不存在',
                'context': None
            }
        
        session = self.sessions[session_id]
        
        # 获取最近的消息
        recent_messages = session['messages'][-message_count:] if len(session['messages']) >= message_count else session['messages']
        
        # 构建上下文
        context = {
            'session_id': session_id,
            'user_id': session['user_id'],
            'digital_human_id': session['digital_human_id'],
            'messages': recent_messages,
            'emotion_history': session['context']['emotion_history'],
            'topics': session['context']['topics']
        }
        
        return {
            'success': True,
            'context': context
        }

# 单例模式
_instance = None

def get_dialogue_manager(config: Dict[str, Any] = None) -> DialogueManager:
    """
    获取对话管理器实例
    
    Args:
        config: 对话管理器配置
    
    Returns:
        对话管理器实例
    """
    global _instance
    if _instance is None:
        _instance = DialogueManager(config)
    return _instance 