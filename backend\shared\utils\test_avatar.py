import os
import sys
import logging
import cv2
import numpy as np
import tempfile
from pathlib import Path

# 添加项目根目录到Python路径
current_file = os.path.abspath(__file__)
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_file)))
sys.path.append(project_root)

from services.avatar_generation import get_avatar_generator
from services.media_utils import get_media_utils
from gtts import gTTS

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_avatar_generation():
    """测试数字人头像生成功能"""
    try:
        # 获取头像生成器和媒体工具
        avatar_generator = get_avatar_generator()
        media_utils = get_media_utils()
        
        # 输出目录
        output_dir = os.path.join(project_root, "test_output")
        Path(output_dir).mkdir(parents=True, exist_ok=True)
        
        # 初始化模型
        model_type = "default"
        logger.info(f"初始化模型: {model_type}")
        if not avatar_generator.initialize_avatar_model(model_type):
            logger.error("初始化模型失败")
            return False
        
        # 1. 测试预览图像生成
        logger.info("测试预览图像生成...")
        preview_path = os.path.join(output_dir, "preview.jpg")
        result = avatar_generator.generate_preview_image(preview_path)
        if not result or not os.path.exists(preview_path):
            logger.error("生成预览图像失败")
            return False
        logger.info(f"预览图像已生成: {preview_path}")
        
        # 2. 测试表情动画
        logger.info("测试表情动画...")
        # 创建一系列表情变化
        expressions = []
        for i in range(90):
            # 周期性变化眨眼、嘴巴等
            expr = {
                "mouth_open": 0.5 * np.sin(i * 0.1),
                "smile": 0.5 + 0.5 * np.sin(i * 0.05),
                "eyebrow_raise": 0.3 * np.sin(i * 0.08),
                "eye_open_left": 1.0 if i % 30 > 3 else 0.1,  # 眨眼
                "eye_open_right": 1.0 if i % 30 > 3 else 0.1,
            }
            expressions.append(expr)
        
        # 生成表情动画视频
        animation_path = os.path.join(output_dir, "animation.mp4")
        video_result = avatar_generator.render_avatar_video(
            expressions=expressions,
            fps=30,
            output_path=animation_path
        )
        if not video_result or not os.path.exists(animation_path):
            logger.error("生成表情动画失败")
            return False
        logger.info(f"表情动画已生成: {animation_path}")
        
        # 3. 测试音频合成和口型同步
        logger.info("测试音频合成和口型同步...")
        
        # 使用GTTS生成测试音频
        text = "这是一段测试音频，用于验证数字人的口型同步功能是否正常工作。"
        audio_path = os.path.join(output_dir, "test_audio.mp3")
        
        try:
            tts = gTTS(text=text, lang='zh-cn')
            tts.save(audio_path)
            logger.info(f"测试音频已生成: {audio_path}")
        except Exception as e:
            logger.error(f"生成测试音频失败: {e}")
            # 如果TTS失败，创建无声视频
            logger.warning("跳过音视频合成测试")
            return True
        
        # 生成无声视频
        silent_video_path = os.path.join(output_dir, "silent_video.mp4")
        silent_video_result = avatar_generator.render_avatar_video(
            duration=5.0,
            fps=30,
            output_path=silent_video_path
        )
        if not silent_video_result or not os.path.exists(silent_video_path):
            logger.error("生成无声视频失败")
            return False
        
        # 合并音频和视频
        combined_path = os.path.join(output_dir, "combined_video.mp4")
        combined_result = media_utils.combine_audio_video(
            video_path=silent_video_path,
            audio_path=audio_path,
            output_path=combined_path
        )
        if not combined_result or not os.path.exists(combined_path):
            logger.error("合并音视频失败")
            return False
        logger.info(f"音视频合成已完成: {combined_path}")
        
        # 4. 测试视频压缩
        logger.info("测试视频压缩...")
        compressed_path = os.path.join(output_dir, "compressed_video.mp4")
        compressed_result = media_utils.compress_video(
            video_path=combined_path,
            output_path=compressed_path,
            quality="medium"
        )
        if not compressed_result or not os.path.exists(compressed_path):
            logger.error("压缩视频失败")
            return False
        logger.info(f"视频压缩已完成: {compressed_path}")
        
        logger.info("所有测试通过!")
        return True
    
    except Exception as e:
        logger.error(f"测试过程中出错: {e}")
        return False

if __name__ == "__main__":
    logger.info("开始测试数字人功能...")
    success = test_avatar_generation()
    
    if success:
        logger.info("数字人功能测试成功!")
    else:
        logger.error("数字人功能测试失败!") 