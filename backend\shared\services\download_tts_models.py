#!/usr/bin/env python
"""
TTS模型下载器

此脚本自动下载所需的TTS模型，解决模型缺失问题。
支持下载多种预训练TTS模型，并自动应用必要的修复。
"""

import os
import sys
import argparse
import logging
import json
import time
import shutil
from pathlib import Path
import importlib.util
from typing import List, Dict, Any, Optional, Tuple, Union

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger("tts_downloader")

def import_tts():
    """
    导入TTS库，如果不存在则提示安装
    
    Returns:
        bool: 是否成功导入
    """
    try:
        import TTS
        return True
    except ImportError:
        logger.error("TTS库未安装，请先安装TTS库：pip install TTS")
        print("\n请运行以下命令安装TTS库：")
        print("pip install TTS")
        print("pip install TTS[all]")
        return False

def get_available_models():
    """
    获取可用的TTS模型列表
    
    Returns:
        Dict: 包含可用模型信息的字典
    """
    # 导入TTS库
    if not import_tts():
        return {}
    
    try:
        from TTS.utils.manage import ModelManager
        
        # 初始化模型管理器
        model_manager = ModelManager(models_file=None, output_prefix=None, progress_bar=True, verbose=True)
        
        # 获取所有模型
        all_models = model_manager.list_models()
        
        # 整理模型信息
        models_info = {}
        for model_type, models in all_models.items():
            if model_type in ["tts_models", "vocoder_models"]:
                for model in models:
                    model_full_name = f"{model_type}/{'/'.join(model)}"
                    model_key = model_full_name.replace("/", "_").replace("-", "_")
                    models_info[model_key] = {
                        "name": model_full_name,
                        "type": model_type,
                        "language": model[0] if model_type == "tts_models" else None,
                        "description": f"{model[0]} {model[1]} {model[2]}模型"
                    }
        
        return models_info
    except Exception as e:
        logger.error(f"获取可用模型列表失败: {e}")
        return {}

def get_recommended_models():
    """
    获取推荐的TTS模型列表
    
    Returns:
        List: 推荐模型列表
    """
    # 按优先级返回推荐的模型
    return [
        # 中文模型
        "tts_models/zh-CN/baker/tacotron2-DDC-GST",  # 中文女声
        
        # 英文模型
        "tts_models/en/ljspeech/tacotron2-DDC",      # 英文女声
        
        # 多语言模型
        "tts_models/multilingual/multi-dataset/your_tts",  # 多语言模型
    ]

def download_model(model_name: str, output_dir: str = None):
    """
    下载指定的TTS模型
    
    Args:
        model_name: 模型名称
        output_dir: 输出目录
        
    Returns:
        Tuple: (是否成功, 模型路径, 配置路径)
    """
    # 导入TTS库
    if not import_tts():
        return False, "", ""
    
    try:
        from TTS.utils.manage import ModelManager
        
        # 确定输出目录
        if output_dir is None:
            # 尝试确定合适的输出目录
            script_dir = os.path.dirname(os.path.abspath(__file__))
            backend_dir = os.path.dirname(script_dir)
            output_dir = os.path.join(backend_dir, "models", "tts")
        
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
        
        logger.info(f"开始下载模型: {model_name}")
        logger.info(f"输出目录: {output_dir}")
        
        # 初始化模型管理器
        model_manager = ModelManager(
            models_file=None,
            output_prefix=output_dir,
            progress_bar=True,
            verbose=True
        )
        
        # 下载模型
        model_path, config_path, _ = model_manager.download_model(model_name)
        
        logger.info(f"模型下载成功: {model_name}")
        logger.info(f"模型路径: {model_path}")
        logger.info(f"配置路径: {config_path}")
        
        # 检查模型文件是否存在
        if not os.path.exists(model_path) or not os.path.exists(config_path):
            logger.warning(f"模型下载可能不完整，文件不存在: {model_path}")
            return False, model_path, config_path
        
        return True, model_path, config_path
    except Exception as e:
        logger.error(f"下载模型 {model_name} 失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False, "", ""

def apply_fixes(models_dir: str):
    """
    应用TTS修复
    
    Args:
        models_dir: 模型目录
        
    Returns:
        bool: 是否成功应用修复
    """
    try:
        # 尝试导入修复模块
        try:
            # 相对导入
            from offline_tts_service_fix import apply_fixes as apply_core_fixes
            from fix_your_tts_config import fix_your_tts_config
        except ImportError:
            # 尝试绝对导入
            sys.path.append(os.path.dirname(os.path.abspath(__file__)))
            from services.offline_tts_service_fix import apply_fixes as apply_core_fixes
            from services.fix_your_tts_config import fix_your_tts_config
        
        # 应用YourTTS配置修复
        logger.info("应用YourTTS配置修复...")
        fix_your_tts_config()
        
        # 应用核心修复
        logger.info("应用TTS核心修复...")
        apply_core_fixes(force=True)
        
        logger.info("TTS修复应用完成")
        return True
    except Exception as e:
        logger.error(f"应用TTS修复失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def download_all_recommended_models(output_dir: str = None):
    """
    下载所有推荐的TTS模型
    
    Args:
        output_dir: 输出目录
        
    Returns:
        bool: 是否全部成功
    """
    # 获取推荐模型列表
    recommended_models = get_recommended_models()
    
    success_count = 0
    failed_models = []
    
    # 下载每个推荐模型
    for model_name in recommended_models:
        logger.info(f"准备下载模型 ({success_count+1}/{len(recommended_models)}): {model_name}")
        success, _, _ = download_model(model_name, output_dir)
        if success:
            success_count += 1
        else:
            failed_models.append(model_name)
    
    # 结果统计
    logger.info(f"模型下载完成: {success_count}/{len(recommended_models)} 成功")
    if failed_models:
        logger.warning(f"以下模型下载失败: {', '.join(failed_models)}")
    
    # 应用修复
    if success_count > 0:
        logger.info("应用TTS修复...")
        apply_fixes(output_dir)
    
    return success_count > 0

def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="TTS模型下载器")
    parser.add_argument("--model", "-m", type=str, help="要下载的模型名称")
    parser.add_argument("--output-dir", "-o", type=str, help="模型输出目录")
    parser.add_argument("--list", "-l", action="store_true", help="列出可用模型")
    parser.add_argument("--all", "-a", action="store_true", help="下载所有推荐模型")
    args = parser.parse_args()
    
    # 确定输出目录
    output_dir = args.output_dir
    if output_dir is None:
        # 尝试确定合适的输出目录
        script_dir = os.path.dirname(os.path.abspath(__file__))
        backend_dir = os.path.dirname(script_dir)
        output_dir = os.path.join(backend_dir, "models", "tts")
    
    # 列出可用模型
    if args.list:
        print("可用的TTS模型:")
        models = get_available_models()
        for key, model in models.items():
            print(f"- {model['name']} ({model.get('language', '未知语言')})")
        return
    
    # 下载所有推荐模型
    if args.all:
        print("开始下载所有推荐的TTS模型...")
        success = download_all_recommended_models(output_dir)
        if success:
            print("推荐模型下载完成，TTS系统现在应该可以正常工作")
        else:
            print("下载失败，请尝试手动下载模型")
        return
    
    # 下载指定模型
    if args.model:
        print(f"开始下载模型: {args.model}")
        success, model_path, config_path = download_model(args.model, output_dir)
        if success:
            print(f"模型下载成功: {model_path}")
            
            # 应用修复
            print("应用TTS修复...")
            apply_fixes(output_dir)
            
            print("模型下载并修复完成")
        else:
            print("模型下载失败，请检查模型名称是否正确")
        return
    
    # 如果没有指定操作，显示帮助信息
    parser.print_help()

if __name__ == "__main__":
    main() 