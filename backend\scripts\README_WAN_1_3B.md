# WAN 2.1 T2V 1.3B 模型下载与配置指南

## 概述

WAN 2.1 提供了两种规模的文本到视频(T2V)模型：
- 14B模型：更高质量，但需要更多显存和计算资源
- 1.3B模型：轻量级模型，可在消费级GPU上运行

本指南将帮助您下载和配置WAN 2.1 T2V 1.3B模型，使您能够在系统中使用它生成视频。

## 系统要求

- **显存要求**：至少8GB显存（使用优化选项）
- **磁盘空间**：约6GB用于模型文件
- **Python环境**：Python 3.7+
- **PyTorch**：1.12.0+

## 快速开始

我们提供了简便的批处理脚本来自动完成整个下载和配置过程：

1. 双击运行 `backend/scripts/download_wan_1_3b.bat`
2. 等待下载完成（取决于您的网络速度，完整下载可能需要10-30分钟）
3. 下载完成后，系统将自动配置模型
4. 使用 `backend/scripts/test_wan_1_3b_model.bat` 验证模型是否正确安装

## 手动下载

如果您需要手动下载模型，可以使用以下方法之一：

### 方法1：使用Git LFS（推荐）

这是最可靠的下载方法，特别是当其他方法失败时：

```bash
# 1. 安装Git LFS (如果尚未安装)
# 从 https://git-lfs.github.com/ 下载并安装

# 2. 初始化Git LFS
git lfs install

# 3. 克隆模型仓库
git clone https://huggingface.co/Wan-AI/Wan2.1-T2V-1.3B

# 4. 将模型复制或移动到正确位置
# 例如: cp -r Wan2.1-T2V-1.3B backend/models_cache/
```

### 方法2：使用HuggingFace CLI

```bash
# 安装huggingface_hub客户端
pip install "huggingface_hub[cli]"

# 下载模型
huggingface-cli download Wan-AI/Wan2.1-T2V-1.3B --local-dir ./Wan2.1-T2V-1.3B
```

### 方法3：使用ModelScope CLI

```bash
# 安装modelscope
pip install modelscope

# 下载模型
modelscope download Wan-AI/Wan2.1-T2V-1.3B --local_dir ./Wan2.1-T2V-1.3B
```

## 配置模型

下载完成后，您需要确保：

1. 模型文件放置在正确位置：`backend/models_cache/Wan2.1-T2V-1.3B/`
2. 模型目录结构完整，包含以下子目录：
   - `vae/`
   - `scheduler/`
   - `unet/`
3. 配置文件正确设置，指向模型位置

如果您使用我们提供的下载脚本，这些步骤会自动完成。如果手动下载，您可以使用 `backend/scripts/test_wan_1_3b_model.py` 脚本检查配置是否正确。

## 模型使用

### 推荐参数设置

WAN 2.1 T2V 1.3B模型在以下参数设置下性能最佳：

- 分辨率：832×480（推荐）
- sample_guide_scale：6
- sample_shift：8-12（可根据效果调整）

### 示例命令

```bash
python generate.py --task t2v-1.3B --size 832*480 --ckpt_dir ./Wan2.1-T2V-1.3B --sample_shift 8 --sample_guide_scale 6 --prompt "你的提示词"
```

如果遇到显存不足问题，可以添加以下参数减少显存使用：

```bash
--offload_model True --t5_cpu
```

## 故障排除

如果您在下载或使用模型时遇到问题，请尝试以下解决方案：

1. **下载失败**：
   - 检查网络连接
   - 尝试使用另一种下载方法（Git LFS、HuggingFace或ModelScope）
   - 确保有足够的磁盘空间

2. **huggingface-cli命令问题**：
   - 如果遇到 `No module named huggingface_hub.__main__` 错误，请尝试：
     ```bash
     # 方法1：使用更新的命令格式
     python -m huggingface_hub.cli.cli download Wan-AI/Wan2.1-T2V-1.3B --local-dir ./Wan2.1-T2V-1.3B
     
     # 方法2：重新安装huggingface_hub
     pip uninstall -y huggingface_hub
     pip install "huggingface_hub[cli]"
     ```
   - 或直接使用Git LFS方法下载

3. **模型加载错误**：
   - 运行 `backend/scripts/test_wan_1_3b_model.bat` 检查模型配置
   - 确保模型目录结构完整
   - 检查配置文件设置是否正确

4. **显存不足**：
   - 使用 `--offload_model True --t5_cpu` 参数减少显存使用
   - 降低生成视频的分辨率或帧数

5. **中文字符显示为乱码**：
   - 在Windows命令行执行 `chcp 65001` 设置UTF-8编码
   - 确保使用的是支持UTF-8的终端

## 更多资源

- [WAN 2.1官方GitHub仓库](https://github.com/Wan-Video/Wan2.1)
- [WAN 2.1 T2V 1.3B模型HuggingFace页面](https://huggingface.co/Wan-AI/Wan2.1-T2V-1.3B)
- [WAN 2.1 T2V 1.3B模型ModelScope页面](https://www.modelscope.cn/models/Wan-AI/Wan2.1-T2V-1.3B) 