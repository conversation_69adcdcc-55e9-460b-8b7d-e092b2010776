"""
PPT生成器模块
负责根据用户输入生成PPT
"""

import os
import time
import logging
import tempfile
import re
import random
from typing import List, Dict, Any, Optional, Tuple
from concurrent.futures import ThreadPoolExecutor
import traceback

# 导入python-pptx库
try:
    from pptx import Presentation
    from pptx.util import Inches, Pt
    from pptx.enum.text import PP_ALIGN
    from pptx.dml.color import RGBColor
except ImportError:
    logging.error("无法导入python-pptx，请确保已安装此依赖")

# 导入任务管理器
from services.task_scheduler import get_task_scheduler

# 导入AI服务，用于生成内容
try:
    from services.ai_service import get_ai_service
except ImportError:
    logging.error("无法导入AI服务，将使用模拟内容")
    
# 配置日志
logger = logging.getLogger(__name__)

class PPTGenerator:
    """PPT生成器类"""
    
    def __init__(self):
        """初始化PPT生成器"""
        self.executor = ThreadPoolExecutor(max_workers=2)
        self.task_scheduler = get_task_scheduler()
        self.style_templates = {
            "business": "business_template.pptx",
            "creative": "creative_template.pptx",
            "academic": "academic_template.pptx",
            "minimalist": "minimalist_template.pptx",
            "elegant": "elegant_template.pptx"
        }
        
        # 尝试导入AI服务
        try:
            from services.ai_service import get_ai_service
            self.ai_service = get_ai_service()
        except (ImportError, Exception) as e:
            logger.warning(f"无法导入AI服务，将使用模拟内容: {e}")
            self.ai_service = None
            
        logger.info("PPT生成器初始化完成")
        
    def generate_ppt(self, task_id: str, title: str, theme: str, outline: str, 
                    style: str = "business", include_images: bool = True, 
                    output_path: str = None, user_id: Optional[int] = None):
        """
        生成PPT
        
        Args:
            task_id: 任务ID
            title: PPT标题
            theme: PPT主题
            outline: 内容大纲
            style: PPT风格
            include_images: 是否包含图片
            output_path: 输出文件路径
            user_id: 用户ID
            
        Returns:
            输出文件路径
        """
        try:
            logger.info(f"开始生成PPT，任务ID: {task_id}")
            
            # 更新任务状态
            self._update_task_status(task_id, "processing", 10, "initialize", "初始化中...")
            
            # 解析大纲
            self._update_task_status(task_id, "processing", 20, "analyzing", "分析内容...")
            parsed_outline = self._parse_outline(outline)
            
            # 生成内容
            self._update_task_status(task_id, "processing", 30, "structuring", "构建结构...")
            content_sections = self._generate_content(parsed_outline, title, theme)
            
            # 创建PPT
            self._update_task_status(task_id, "processing", 50, "content_generation", "生成内容...")
            ppt = self._create_ppt(title, theme, content_sections, style)
            
            # 添加图片（如果需要）
            if include_images:
                self._update_task_status(task_id, "processing", 70, "images", "处理图片...")
                self._add_images(ppt, content_sections)
            
            # 应用设计和样式
            self._update_task_status(task_id, "processing", 80, "design", "设计页面...")
            self._apply_design(ppt, style)
            
            # 保存PPT
            self._update_task_status(task_id, "processing", 90, "finalizing", "完成处理...")
            if not output_path:
                output_dir = tempfile.mkdtemp()
                output_path = os.path.join(output_dir, f"{title}.pptx")
                
            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # 保存文件
            self._update_task_status(task_id, "processing", 95, "export", "导出文件...")
            ppt.save(output_path)
            
            # 更新任务状态为成功
            self._update_task_status(
                task_id, "success", 100, "success", 
                "PPT生成成功",
                {"output_path": output_path, "title": title}
            )
            
            logger.info(f"PPT生成成功，保存到: {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"生成PPT时出错: {e}")
            logger.error(traceback.format_exc())
            
            # 更新任务状态为失败
            self._update_task_status(
                task_id, "error", 0, "", 
                f"生成PPT失败: {str(e)}"
            )
            
            return None
    
    def _update_task_status(self, task_id: str, status: str, progress: int, 
                           stage: str, message: str, result: Dict = None):
        """
        更新任务状态
        
        Args:
            task_id: 任务ID
            status: 状态
            progress: 进度
            stage: 阶段
            message: 消息
            result: 结果数据
        """
        try:
            self.task_scheduler.update_task_status(
                task_id=task_id,
                status=status,
                progress=progress,
                message=message,
                result=result
            )
            
            # 更新任务阶段
            self.task_scheduler.update_task_additional_info(
                task_id=task_id,
                key="stage",
                value=stage
            )
            
            logger.debug(f"任务 {task_id} 状态更新: {status}, 进度: {progress}%, 阶段: {stage}")
        except Exception as e:
            logger.error(f"更新任务状态失败: {e}")
    
    def _parse_outline(self, outline: str) -> List[Dict]:
        """
        解析大纲文本
        
        Args:
            outline: 大纲文本
            
        Returns:
            解析后的结构化大纲
        """
        sections = []
        current_section = None
        current_bullet_points = []
        
        # 按行分割
        lines = outline.strip().split('\n')
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # 检查是否是章节标题
            if line.startswith('#') or re.match(r'^第.+[章节部]|^[0-9]+\.', line) or ':' in line or '：' in line:
                # 如果有一个正在处理的章节，先保存它
                if current_section:
                    sections.append({
                        'title': current_section,
                        'bullet_points': current_bullet_points
                    })
                
                # 开始一个新章节
                current_section = line.lstrip('#').strip()
                # 清除章节标记，如"第一章："
                current_section = re.sub(r'^第.+[章节部][：:]\s*|^[0-9]+\.\s*', '', current_section)
                current_bullet_points = []
            
            # 检查是否是要点
            elif line.startswith('-') or line.startswith('*') or line.startswith('•') or re.match(r'^[0-9]+\)', line):
                bullet_point = line.lstrip('-*•').strip()
                # 清除数字编号，如"1)"
                bullet_point = re.sub(r'^[0-9]+\)\s*', '', bullet_point)
                current_bullet_points.append(bullet_point)
            
            # 其他行，如果已有章节标题，则视为要点
            else:
                if current_section:
                    current_bullet_points.append(line)
                else:
                    # 如果没有章节标题，则将此行作为章节标题
                    current_section = line
        
        # 保存最后一个章节
        if current_section:
            sections.append({
                'title': current_section,
                'bullet_points': current_bullet_points
            })
        
        # 如果没有解析出章节，创建一个默认章节
        if not sections:
            sections.append({
                'title': '概述',
                'bullet_points': [line.strip() for line in lines if line.strip()]
            })
        
        return sections
    
    def _generate_content(self, parsed_outline: List[Dict], title: str, theme: str) -> List[Dict]:
        """
        根据大纲生成内容
        
        Args:
            parsed_outline: 解析后的大纲
            title: 标题
            theme: 主题
            
        Returns:
            生成的内容段落
        """
        content_sections = []
        
        # 添加封面幻灯片
        content_sections.append({
            'type': 'cover',
            'title': title,
            'subtitle': theme
        })
        
        # 添加目录幻灯片
        content_sections.append({
            'type': 'toc',
            'title': '目录',
            'items': [section['title'] for section in parsed_outline]
        })
        
        # 处理各个章节
        for section in parsed_outline:
            # 添加章节标题幻灯片
            content_sections.append({
                'type': 'section',
                'title': section['title']
            })
            
            # 添加章节内容幻灯片
            bullet_points = section['bullet_points']
            
            # 如果要点较多，可能需要分多页
            if len(bullet_points) > 6:
                chunks = [bullet_points[i:i+6] for i in range(0, len(bullet_points), 6)]
                for i, chunk in enumerate(chunks):
                    content_sections.append({
                        'type': 'content',
                        'title': section['title'] + (f' ({i+1}/{len(chunks)})' if len(chunks) > 1 else ''),
                        'bullet_points': chunk,
                        'section': section['title']
                    })
            else:
                content_sections.append({
                    'type': 'content',
                    'title': section['title'],
                    'bullet_points': bullet_points,
                    'section': section['title']
                })
            
            # 使用AI扩充内容（如果有AI服务）
            if self.ai_service and bullet_points:
                try:
                    # 为每个要点生成更详细的内容
                    # 使用AI服务生成PPT内容结构
                    section_content = self.ai_service.generate_ppt_content(
                        title=title,
                        theme=section['title'],
                        outline="\n".join(bullet_points)
                    )
                    
                    # 处理生成的内容
                    if section_content and 'sections' in section_content:
                        for subsection in section_content['sections']:
                            # 添加子章节详细内容
                            detail_title = subsection.get('title', bullet_points[0] if bullet_points else section['title'])
                            detail_content = subsection.get('details', '')
                            
                            if detail_content:
                                content_sections.append({
                                    'type': 'detail',
                                    'title': detail_title,
                                    'content': detail_content,
                                    'section': section['title']
                                })
                            
                            # 处理图表建议
                            chart_suggestion = subsection.get('chart_suggestion')
                            if chart_suggestion:
                                content_sections.append({
                                    'type': 'chart',
                                    'title': f"{detail_title} - 图表",
                                    'chart_type': self._determine_chart_type(chart_suggestion),
                                    'chart_data': self._extract_chart_data(chart_suggestion),
                                    'section': section['title']
                                })
                    else:
                        # 如果没有获得有效的AI生成内容，使用模拟方法
                        for bullet in bullet_points:
                            additional_content = self._mock_generate_details(bullet, section['title'], title)
                            
                            if additional_content:
                                content_sections.append({
                                    'type': 'detail',
                                    'title': bullet,
                                    'content': additional_content,
                                    'section': section['title']
                                })
                except Exception as e:
                    logger.error(f"使用AI生成内容时出错: {e}")
                    logger.error(traceback.format_exc())
                    
                    # 出错时使用模拟内容
                    for bullet in bullet_points:
                        additional_content = self._mock_generate_details(bullet, section['title'], title)
                        
                        if additional_content:
                            content_sections.append({
                                'type': 'detail',
                                'title': bullet,
                                'content': additional_content,
                                'section': section['title']
                            })
        
        # 添加总结幻灯片
        summary_content = "总结内容将在这里显示"
        if self.ai_service:
            try:
                # 获取AI生成的总结内容
                all_content = "\n".join([section['title'] for section in parsed_outline])
                section_summary = self.ai_service.get_text_generation(
                    f"请为一个标题为'{title}'，主题为'{theme}'的演示文稿生成一个简短的总结。以下是主要章节:\n{all_content}",
                    max_tokens=200
                )
                summary_content = section_summary.get("text", "总结内容将在这里显示")
            except Exception as e:
                logger.error(f"生成总结内容时出错: {e}")
        
        content_sections.append({
            'type': 'summary',
            'title': '总结',
            'bullet_points': [
                f"{title}的主要内容回顾",
                "关键要点总结",
                "下一步行动计划"
            ],
            'content': summary_content
        })
        
        # 添加结束幻灯片
        content_sections.append({
            'type': 'end',
            'title': '谢谢观看',
            'subtitle': '欢迎提问'
        })
        
        return content_sections
    
    def _create_ppt(self, title: str, theme: str, content_sections: List[Dict], style: str) -> Presentation:
        """
        创建PPT
        
        Args:
            title: 标题
            theme: 主题
            content_sections: 内容段落
            style: 风格
            
        Returns:
            PPT对象
        """
        # 创建新的PPT或基于模板创建
        ppt = Presentation()
        
        # 处理各个内容段落
        for section in content_sections:
            section_type = section['type']
            
            if section_type == 'cover':
                # 添加封面幻灯片
                slide = ppt.slides.add_slide(ppt.slide_layouts[0])  # 使用标题幻灯片布局
                title_shape = slide.shapes.title
                subtitle_shape = slide.placeholders[1]  # 副标题占位符
                
                title_shape.text = section['title']
                subtitle_shape.text = section['subtitle']
            
            elif section_type == 'toc':
                # 添加目录幻灯片
                slide = ppt.slides.add_slide(ppt.slide_layouts[1])  # 使用标题和内容布局
                title_shape = slide.shapes.title
                content_shape = slide.placeholders[1]  # 内容占位符
                
                title_shape.text = section['title']
                
                # 添加目录项
                tf = content_shape.text_frame
                for item in section['items']:
                    p = tf.add_paragraph()
                    p.text = "• " + item
                    p.level = 0
            
            elif section_type == 'section':
                # 添加章节标题幻灯片
                slide = ppt.slides.add_slide(ppt.slide_layouts[2])  # 使用章节标题布局
                title_shape = slide.shapes.title
                
                title_shape.text = section['title']
            
            elif section_type == 'content':
                # 添加内容幻灯片
                slide = ppt.slides.add_slide(ppt.slide_layouts[1])  # 使用标题和内容布局
                title_shape = slide.shapes.title
                content_shape = slide.placeholders[1]  # 内容占位符
                
                title_shape.text = section['title']
                
                # 添加要点
                tf = content_shape.text_frame
                for item in section['bullet_points']:
                    p = tf.add_paragraph()
                    p.text = "• " + item
                    p.level = 0
            
            elif section_type == 'detail':
                # 添加详细内容幻灯片
                slide = ppt.slides.add_slide(ppt.slide_layouts[3])  # 使用详细内容布局
                title_shape = slide.shapes.title
                content_shape = slide.placeholders[1]  # 内容占位符
                
                title_shape.text = section['title']
                
                # 添加详细内容
                content_shape.text = section['content']
            
            elif section_type == 'summary':
                # 添加总结幻灯片
                slide = ppt.slides.add_slide(ppt.slide_layouts[1])  # 使用标题和内容布局
                title_shape = slide.shapes.title
                content_shape = slide.placeholders[1]  # 内容占位符
                
                title_shape.text = section['title']
                
                # 添加要点
                tf = content_shape.text_frame
                for item in section['bullet_points']:
                    p = tf.add_paragraph()
                    p.text = "• " + item
                    p.level = 0
            
            elif section_type == 'end':
                # 添加结束幻灯片
                slide = ppt.slides.add_slide(ppt.slide_layouts[0])  # 使用标题幻灯片布局
                title_shape = slide.shapes.title
                subtitle_shape = slide.placeholders[1]  # 副标题占位符
                
                title_shape.text = section['title']
                subtitle_shape.text = section['subtitle']
        
        return ppt
    
    def _add_images(self, ppt: Presentation, content_sections: List[Dict]):
        """
        为PPT添加相关图片
        
        Args:
            ppt: PPT对象
            content_sections: 内容段落
        """
        try:
            # 获取所有幻灯片数量
            total_slides = len(ppt.slides)
            if total_slides <= 1:
                return
                
            # 记录已处理的幻灯片
            processed_slides = set()
            
            # 封面图片
            if total_slides > 0:
                slide = ppt.slides[0]
                self._add_image_to_slide(slide, "cover", processed_slides)
            
            # 内容幻灯片图片
            for i, slide in enumerate(ppt.slides):
                if i in processed_slides:
                    continue
                    
                # 获取幻灯片标题
                title_text = ""
                for shape in slide.shapes:
                    if hasattr(shape, "text") and shape.text:
                        title_text = shape.text
                        break
                
                # 根据标题选择合适的图片
                if title_text:
                    # 跳过某些幻灯片
                    if "目录" in title_text or "谢谢" in title_text:
                        continue
                        
                    # 添加图片
                    self._add_image_to_slide(slide, title_text.strip(), processed_slides)
                    
                    # 标记为已处理
                    processed_slides.add(i)
                    
        except Exception as e:
            logger.error(f"添加图片时出错: {e}")
            logger.error(traceback.format_exc())
    
    def _add_image_to_slide(self, slide, keyword, processed_slides):
        """
        向幻灯片添加图片
        
        Args:
            slide: 幻灯片对象
            keyword: 关键字
            processed_slides: 已处理的幻灯片集合
        """
        try:
            # 图片文件夹路径
            image_folder = os.path.join(os.path.dirname(__file__), "templates", "images")
            os.makedirs(image_folder, exist_ok=True)
            
            # 查找相关图片
            image_files = []
            if os.path.exists(image_folder):
                for file in os.listdir(image_folder):
                    if file.lower().endswith(('.png', '.jpg', '.jpeg', '.gif')):
                        image_files.append(os.path.join(image_folder, file))
            
            # 如果没有图片，使用默认图片
            if not image_files:
                return
                
            # 随机选择一张图片
            image_path = random.choice(image_files)
            
            # 添加图片到幻灯片
            left = Inches(6.5)  # 靠右侧放置
            top = Inches(2)
            width = Inches(3)
            height = Inches(3)
            
            # 添加图片
            slide.shapes.add_picture(image_path, left, top, width, height)
            
        except Exception as e:
            logger.error(f"向幻灯片添加图片时出错: {e}")
    
    def _apply_design(self, ppt: Presentation, style: str):
        """
        应用设计和样式
        
        Args:
            ppt: PPT对象
            style: 风格
        """
        try:
            # 根据风格选择颜色方案
            color_scheme = {
                "business": {
                    "primary": RGBColor(0, 82, 136),  # 深蓝色
                    "secondary": RGBColor(176, 214, 255),  # 浅蓝色
                    "accent": RGBColor(255, 128, 0)  # 橙色
                },
                "creative": {
                    "primary": RGBColor(102, 45, 145),  # 紫色
                    "secondary": RGBColor(255, 182, 193),  # 粉色
                    "accent": RGBColor(255, 215, 0)  # 金色
                },
                "academic": {
                    "primary": RGBColor(28, 82, 33),  # 深绿色
                    "secondary": RGBColor(235, 235, 235),  # 浅灰色
                    "accent": RGBColor(206, 17, 38)  # 红色
                },
                "minimalist": {
                    "primary": RGBColor(33, 33, 33),  # 深灰色
                    "secondary": RGBColor(245, 245, 245),  # 浅灰色
                    "accent": RGBColor(232, 17, 35)  # 红色
                },
                "elegant": {
                    "primary": RGBColor(46, 46, 46),  # 黑色
                    "secondary": RGBColor(209, 209, 209),  # 灰色
                    "accent": RGBColor(166, 124, 82)  # 金褐色
                }
            }
            
            # 获取当前风格的颜色方案
            colors = color_scheme.get(style, color_scheme["business"])
            
            # 应用颜色到幻灯片
            for slide in ppt.slides:
                for shape in slide.shapes:
                    # 尝试为标题应用主要颜色
                    if hasattr(shape, "text_frame") and hasattr(shape, "is_title") and shape.is_title:
                        for paragraph in shape.text_frame.paragraphs:
                            for run in paragraph.runs:
                                run.font.color.rgb = colors["primary"]
                    
                    # 为普通文本应用次要颜色
                    elif hasattr(shape, "text_frame") and not (hasattr(shape, "is_title") and shape.is_title):
                        for paragraph in shape.text_frame.paragraphs:
                            for run in paragraph.runs:
                                run.font.color.rgb = colors["primary"]
            
            # 应用背景和其他样式设置
            self._apply_background(ppt, style)
            
        except Exception as e:
            logger.error(f"应用设计和样式时出错: {e}")
            logger.error(traceback.format_exc())
    
    def _apply_background(self, ppt: Presentation, style: str):
        """
        应用背景样式
        
        Args:
            ppt: PPT对象
            style: 风格
        """
        try:
            # 根据风格选择背景
            backgrounds = {
                "business": (255, 255, 255),  # 白色背景
                "creative": (245, 240, 255),  # 浅紫色背景
                "academic": (255, 255, 250),  # 浅黄色背景
                "minimalist": (255, 255, 255),  # 白色背景
                "elegant": (250, 250, 250)  # 近白色背景
            }
            
            # 获取当前风格的背景颜色
            bg_color = backgrounds.get(style, (255, 255, 255))
            
            # 应用背景颜色到每个幻灯片
            # 注意: python-pptx对背景的支持有限，这里的实现可能不完整
            for slide in ppt.slides:
                # 此处的背景设置需要根据python-pptx的功能调整
                # 目前python-pptx不直接支持设置纯色背景
                pass
                
        except Exception as e:
            logger.error(f"应用背景样式时出错: {e}")
    
    def _mock_generate_details(self, bullet_point: str, section_title: str, ppt_title: str) -> str:
        """
        模拟AI生成详细内容
        
        Args:
            bullet_point: 要点
            section_title: 章节标题
            ppt_title: PPT标题
            
        Returns:
            生成的详细内容
        """
        # 这是一个模拟实现，实际应使用AI服务生成内容
        mock_details = [
            f"{bullet_point}是{section_title}中的重要概念。研究表明，它在{ppt_title}领域有广泛应用。",
            f"关于{bullet_point}，我们需要考虑以下几点：\n1. 它的基本原理\n2. 应用场景\n3. 实施策略",
            f"{bullet_point}代表了{section_title}的核心价值。在实践中，它可以帮助我们解决许多问题。",
            f"从历史角度看，{bullet_point}的发展经历了多个阶段。每个阶段都有其特点和贡献。",
            f"{bullet_point}体现了{ppt_title}的创新思维。通过它，我们可以实现更高效的工作流程。"
        ]
        
        return random.choice(mock_details)
    
    def _determine_chart_type(self, chart_suggestion: str) -> str:
        """
        根据图表建议确定图表类型
        
        Args:
            chart_suggestion: 图表建议文本
            
        Returns:
            图表类型
        """
        chart_suggestion = chart_suggestion.lower()
        
        if "柱状图" in chart_suggestion or "条形图" in chart_suggestion or "bar chart" in chart_suggestion:
            return "bar"
        elif "折线图" in chart_suggestion or "line chart" in chart_suggestion:
            return "line"
        elif "饼图" in chart_suggestion or "pie chart" in chart_suggestion:
            return "pie"
        elif "散点图" in chart_suggestion or "scatter plot" in chart_suggestion:
            return "scatter"
        elif "面积图" in chart_suggestion or "area chart" in chart_suggestion:
            return "area"
        else:
            return "bar"  # 默认使用柱状图
    
    def _extract_chart_data(self, chart_suggestion: str) -> Dict:
        """
        从图表建议中提取图表数据
        
        Args:
            chart_suggestion: 图表建议文本
            
        Returns:
            图表数据结构
        """
        # 这里应该实现一个解析图表数据的算法
        # 示例返回模拟数据
        return {
            "labels": ["类别A", "类别B", "类别C", "类别D", "类别E"],
            "datasets": [
                {
                    "label": "数据集1",
                    "data": [random.randint(10, 100) for _ in range(5)]
                },
                {
                    "label": "数据集2",
                    "data": [random.randint(10, 100) for _ in range(5)]
                }
            ]
        } 