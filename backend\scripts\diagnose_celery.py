#!/usr/bin/env python3
"""
Celery 诊断脚本 - 全面检查 Celery 配置和状态
"""
import os
import sys
import asyncio
import time
from pathlib import Path
from dotenv import load_dotenv

# 添加后端路径
backend_dir = Path(__file__).parent.parent
sys.path.append(str(backend_dir))

# 加载环境变量
env_path = backend_dir / ".env"
load_dotenv(env_path)

def check_redis_connection():
    """检查 Redis 连接"""
    print("🔍 检查 Redis 连接")
    print("=" * 50)
    
    try:
        import redis
        
        # 从环境变量获取 Redis 配置
        redis_host = os.getenv("REDIS_HOST", "localhost")
        redis_port = int(os.getenv("REDIS_PORT", "6379"))
        redis_db = int(os.getenv("REDIS_DB", "0"))
        
        print(f"Redis 配置: {redis_host}:{redis_port}/{redis_db}")
        
        # 尝试连接
        r = redis.Redis(host=redis_host, port=redis_port, db=redis_db, socket_timeout=5)
        
        # 测试连接
        response = r.ping()
        if response:
            print("✅ Redis 连接成功")
            
            # 获取 Redis 信息
            info = r.info()
            print(f"📊 Redis 版本: {info.get('redis_version', 'Unknown')}")
            print(f"📊 已用内存: {info.get('used_memory_human', 'Unknown')}")
            print(f"📊 连接数: {info.get('connected_clients', 'Unknown')}")
            
            # 测试读写
            test_key = "celery_test_key"
            r.set(test_key, "test_value", ex=10)
            value = r.get(test_key)
            if value == b"test_value":
                print("✅ Redis 读写测试成功")
                r.delete(test_key)
            else:
                print("❌ Redis 读写测试失败")
            
            return True
        else:
            print("❌ Redis ping 失败")
            return False
            
    except redis.ConnectionError as e:
        print(f"❌ Redis 连接错误: {e}")
        return False
    except ImportError:
        print("❌ redis 库未安装")
        return False
    except Exception as e:
        print(f"❌ Redis 检查失败: {e}")
        return False

def check_celery_config():
    """检查 Celery 配置"""
    print(f"\n🔧 检查 Celery 配置")
    print("=" * 50)
    
    try:
        from app.core.celery_app import celery_app, USE_FAKE_REDIS, REDIS_AVAILABLE
        
        print(f"📊 Celery 应用名: {celery_app.main}")
        print(f"📊 Broker URL: {celery_app.conf.broker_url}")
        print(f"📊 Backend URL: {celery_app.conf.result_backend}")
        print(f"📊 使用 Fake Redis: {USE_FAKE_REDIS}")
        print(f"📊 Redis 可用: {REDIS_AVAILABLE}")
        
        # 检查任务模块
        print(f"\n📋 已注册的任务模块:")
        for module in celery_app.conf.include:
            print(f"   - {module}")
        
        # 检查任务路由
        print(f"\n🛣️ 任务路由配置:")
        for pattern, config in celery_app.conf.task_routes.items():
            print(f"   {pattern} -> {config}")
        
        return True
        
    except Exception as e:
        print(f"❌ Celery 配置检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_celery_tasks():
    """检查 Celery 任务"""
    print(f"\n📋 检查 Celery 任务")
    print("=" * 50)
    
    try:
        from app.core.celery_app import celery_app
        
        # 获取已注册的任务
        registered_tasks = list(celery_app.tasks.keys())
        print(f"📊 已注册任务数量: {len(registered_tasks)}")
        
        # 显示任务列表
        print(f"\n📝 已注册的任务:")
        for task_name in sorted(registered_tasks):
            if not task_name.startswith('celery.'):  # 过滤内置任务
                print(f"   ✅ {task_name}")
        
        # 检查特定任务模块
        task_modules = [
            "app.tasks.simple_test_task",
            "app.tasks.test_unified_task",
            "app.tasks.translation_tasks",
            "app.tasks.digital_human_tasks"
        ]
        
        print(f"\n🔍 检查任务模块:")
        for module in task_modules:
            try:
                __import__(module)
                print(f"   ✅ {module}")
            except Exception as e:
                print(f"   ❌ {module}: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 任务检查失败: {e}")
        return False

def test_simple_task():
    """测试简单任务"""
    print(f"\n🧪 测试简单任务")
    print("=" * 50)
    
    try:
        from app.tasks.simple_test_task import simple_add, simple_hello
        
        # 测试同步执行
        print("📤 测试同步执行...")
        result1 = simple_add(2, 3)
        print(f"   simple_add(2, 3) = {result1}")
        
        result2 = simple_hello("Celery")
        print(f"   simple_hello('Celery') = {result2}")
        
        # 测试异步执行
        print("\n📤 测试异步执行...")
        
        # 发送异步任务
        task1 = simple_add.delay(5, 7)
        print(f"   任务 ID: {task1.id}")
        print(f"   任务状态: {task1.status}")
        
        # 等待结果
        try:
            result = task1.get(timeout=10)
            print(f"   ✅ 异步任务结果: {result}")
            return True
        except Exception as e:
            print(f"   ❌ 异步任务失败: {e}")
            return False
            
    except Exception as e:
        print(f"❌ 简单任务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_celery_worker():
    """检查 Celery Worker 状态"""
    print(f"\n👷 检查 Celery Worker 状态")
    print("=" * 50)
    
    try:
        from app.core.celery_app import celery_app
        
        # 检查活跃的 worker
        inspect = celery_app.control.inspect()
        
        # 获取活跃 worker
        active_workers = inspect.active()
        if active_workers:
            print(f"✅ 发现 {len(active_workers)} 个活跃 worker:")
            for worker_name, tasks in active_workers.items():
                print(f"   📍 Worker: {worker_name}")
                print(f"      活跃任务数: {len(tasks)}")
        else:
            print("❌ 没有发现活跃的 worker")
        
        # 获取已注册任务
        registered = inspect.registered()
        if registered:
            print(f"\n📋 Worker 已注册任务:")
            for worker_name, tasks in registered.items():
                print(f"   📍 Worker: {worker_name}")
                print(f"      注册任务数: {len(tasks)}")
        
        # 获取统计信息
        stats = inspect.stats()
        if stats:
            print(f"\n📊 Worker 统计信息:")
            for worker_name, stat in stats.items():
                print(f"   📍 Worker: {worker_name}")
                print(f"      总任务数: {stat.get('total', 'Unknown')}")
                print(f"      进程池大小: {stat.get('pool', {}).get('max-concurrency', 'Unknown')}")
        
        return bool(active_workers)
        
    except Exception as e:
        print(f"❌ Worker 状态检查失败: {e}")
        return False

def check_task_manager():
    """检查任务管理器"""
    print(f"\n📊 检查任务管理器")
    print("=" * 50)
    
    try:
        from app.core.task_manager import task_manager
        
        print("✅ 任务管理器导入成功")
        
        # 测试任务管理器功能
        test_task_id = f"test_task_{int(time.time())}"
        
        # 创建测试任务
        created_task = task_manager.create_task(
            task_type='test',
            task_subtype='diagnosis',
            user_id='test_user',
            title='Celery 诊断测试任务',
            description='用于测试任务管理器功能',
            input_params={'test': True},
            estimated_duration=60
        )
        if created_task:
            print(f"✅ 创建测试任务成功: {test_task_id}")
            
            # 更新任务
            task_manager.update_task(test_task_id, status='processing', progress=50)
            print("✅ 更新任务状态成功")
            
            # 获取任务
            task_info = task_manager.get_task(test_task_id)
            if task_info:
                print(f"✅ 获取任务信息成功: {task_info.get('status')}")
            
            # 清理测试任务
            # task_manager.delete_task(test_task_id)
            
            return True
        else:
            print("❌ 创建测试任务失败")
            return False
            
    except Exception as e:
        print(f"❌ 任务管理器检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔍 Celery 系统诊断")
    print("=" * 60)
    
    results = {}
    
    # 1. 检查 Redis
    results['redis'] = check_redis_connection()
    
    # 2. 检查 Celery 配置
    results['config'] = check_celery_config()
    
    # 3. 检查任务
    results['tasks'] = check_celery_tasks()
    
    # 4. 检查任务管理器
    results['task_manager'] = check_task_manager()
    
    # 5. 检查 Worker
    results['worker'] = check_celery_worker()
    
    # 6. 测试简单任务
    results['simple_task'] = test_simple_task()
    
    # 总结
    print(f"\n📊 诊断结果总结:")
    print("=" * 60)
    
    for component, status in results.items():
        status_icon = "✅" if status else "❌"
        status_text = "正常" if status else "异常"
        print(f"{status_icon} {component.upper()}: {status_text}")
    
    # 建议
    print(f"\n💡 建议:")
    if not results['redis']:
        print("   🔧 启动 Redis 服务: redis-server")
        print("   🔧 或安装 Redis: https://redis.io/download")
    
    if not results['worker']:
        print("   🔧 启动 Celery Worker:")
        print("      cd backend && celery -A app.core.celery_app worker --loglevel=info")
    
    if not results['simple_task']:
        print("   🔧 检查 Celery 配置和 Worker 状态")
        print("   🔧 确保 Redis 服务正常运行")
    
    # 总体状态
    all_good = all(results.values())
    if all_good:
        print(f"\n🎉 Celery 系统运行正常！")
    else:
        print(f"\n⚠️ Celery 系统存在问题，请根据上述建议进行修复")
    
    return all_good

if __name__ == "__main__":
    main()
