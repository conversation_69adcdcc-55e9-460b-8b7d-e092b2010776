"""
数字人生成流水线

负责数字人生成的各个阶段处理，包括：
1. 媒体验证
2. 人脸检测
3. 头像创建
4. 语音合成
5. 唇形同步
6. 视频生成
7. 后处理
"""

import os
import time
import logging
import asyncio
import traceback
from typing import Dict, Any, Optional, List, Callable, Awaitable, Union, Tuple
from pathlib import Path
import tempfile
import shutil
import uuid
import json
from datetime import datetime

from .models import GenerationTask, TaskStatus, GenerationStage, LogLevel

# 设置日志
logger = logging.getLogger(__name__)


class PipelineStage:
    """流水线阶段基类"""
    
    def __init__(self, name: str, stage_id: str):
        """
        初始化流水线阶段
        
        Args:
            name: 阶段名称
            stage_id: 阶段ID
        """
        self.name = name
        self.stage_id = stage_id
        self.next_stage = None
    
    async def process(self, task: GenerationTask, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理任务
        
        Args:
            task: 任务对象
            context: 上下文数据
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        raise NotImplementedError("子类必须实现process方法")


class MediaValidationStage(PipelineStage):
    """媒体验证阶段"""
    
    def __init__(self):
        super().__init__("媒体验证", GenerationStage.MEDIA_VALIDATION.value)
    
    async def process(self, task: GenerationTask, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证媒体文件
        
        Args:
            task: 任务对象
            context: 上下文数据
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        task.update_stage(GenerationStage.MEDIA_VALIDATION, "开始验证媒体文件", 10)
        
        # 检查文件是否存在
        media_path = task.media_file_path
        if not os.path.exists(media_path):
            task.add_log(f"媒体文件不存在: {media_path}", LogLevel.ERROR)
            return {
                "success": False,
                "error": f"媒体文件不存在: {media_path}",
                "stage": self.stage_id
            }
        
        # 检查文件类型
        file_ext = os.path.splitext(media_path)[1].lower()
        if file_ext not in ['.jpg', '.jpeg', '.png', '.mp4', '.mov']:
            task.add_log(f"不支持的媒体文件类型: {file_ext}", LogLevel.ERROR)
            return {
                "success": False,
                "error": f"不支持的媒体文件类型: {file_ext}",
                "stage": self.stage_id
            }
        
        # 检查文件大小
        file_size = os.path.getsize(media_path)
        if file_size == 0:
            task.add_log("媒体文件为空", LogLevel.ERROR)
            return {
                "success": False,
                "error": "媒体文件为空",
                "stage": self.stage_id
            }
        
        # 添加文件信息到上下文
        context.update({
            "media_path": media_path,
            "file_ext": file_ext,
            "file_size": file_size,
            "is_image": file_ext in ['.jpg', '.jpeg', '.png'],
            "media_validated": True
        })
        
        task.update_stage(GenerationStage.MEDIA_VALIDATION, "媒体文件验证成功", 20)
        
        return {
            "success": True,
            "stage": self.stage_id,
            "message": "媒体文件验证成功"
        }


class FaceDetectionStage(PipelineStage):
    """人脸检测阶段"""
    
    def __init__(self):
        super().__init__("人脸检测", GenerationStage.FACE_DETECTION.value)
    
    async def process(self, task: GenerationTask, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        检测并处理人脸
        
        Args:
            task: 任务对象
            context: 上下文数据
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        task.update_stage(GenerationStage.FACE_DETECTION, "开始人脸检测", 25)
        
        # 检查媒体是否已验证
        if not context.get("media_validated", False):
            task.add_log("媒体文件未验证，无法进行人脸检测", LogLevel.ERROR)
            return {
                "success": False,
                "error": "媒体文件未验证，无法进行人脸检测",
                "stage": self.stage_id
            }
        
        # 获取媒体路径
        media_path = context.get("media_path")
        is_image = context.get("is_image", True)
        
        # 模拟人脸检测过程
        # 实际实现中应当调用人脸检测服务
        await asyncio.sleep(2)  # 模拟处理时间
        
        # 更新进度
        task.update_stage(GenerationStage.FACE_DETECTION, "人脸检测中...", 30)
        
        # 模拟检测结果
        detected_faces = 1
        
        if detected_faces == 0:
            task.add_log("未检测到人脸", LogLevel.ERROR)
            return {
                "success": False,
                "error": "未检测到人脸",
                "stage": self.stage_id
            }
        
        if detected_faces > 1:
            task.add_log(f"检测到多个人脸({detected_faces})，将使用最佳人脸", LogLevel.WARNING)
        
        # 添加处理结果到上下文
        context.update({
            "detected_faces": detected_faces,
            "face_detected": True,
            "face_landmarks": {"dummy": "landmarks"},  # 实际应包含人脸特征点信息
            "face_alignment": {"dummy": "alignment"}  # 实际应包含对齐信息
        })
        
        task.update_stage(GenerationStage.FACE_DETECTION, "人脸检测完成", 35)
        
        return {
            "success": True,
            "stage": self.stage_id,
            "message": f"成功检测到 {detected_faces} 个人脸",
            "detected_faces": detected_faces
        }


class AvatarCreationStage(PipelineStage):
    """头像创建阶段"""
    
    def __init__(self):
        super().__init__("头像创建", GenerationStage.AVATAR_CREATION.value)
    
    async def process(self, task: GenerationTask, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建头像
        
        Args:
            task: 任务对象
            context: 上下文数据
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        task.update_stage(GenerationStage.AVATAR_CREATION, "开始创建头像", 40)
        
        # 检查是否已进行人脸检测
        if not context.get("face_detected", False):
            task.add_log("未进行人脸检测，无法创建头像", LogLevel.ERROR)
            return {
                "success": False,
                "error": "未进行人脸检测，无法创建头像",
                "stage": self.stage_id
            }
        
        # 获取必要信息
        media_path = context.get("media_path")
        face_landmarks = context.get("face_landmarks")
        
        # 创建数字人目录
        dh_dir = os.path.join("data", "digital_humans", task.digital_human_id)
        os.makedirs(dh_dir, exist_ok=True)
        
        # 模拟头像创建过程
        # 实际实现中应当调用头像创建服务
        await asyncio.sleep(3)  # 模拟处理时间
        
        # 更新进度
        task.update_stage(GenerationStage.AVATAR_CREATION, "处理头像特征...", 45)
        
        # 模拟创建缩略图
        thumbnail_path = os.path.join(dh_dir, f"{task.digital_human_id}_thumbnail.jpg")
        # 在实际实现中，这里应当创建实际的缩略图
        # 暂时复制原始图片作为缩略图
        if context.get("is_image", True):
            shutil.copy(media_path, thumbnail_path)
        else:
            # 如果是视频，应当提取一帧作为缩略图
            # 此处只是示例，实际应该从视频中提取帧
            with open(thumbnail_path, "wb") as f:
                f.write(b"dummy thumbnail")
        
        # 添加处理结果到上下文
        context.update({
            "avatar_created": True,
            "thumbnail_path": thumbnail_path,
            "avatar_dir": dh_dir
        })
        
        # 更新任务缩略图URL
        task.thumbnail_url = f"/api/digital-human/media/thumbnail/{task.digital_human_id}"
        
        task.update_stage(GenerationStage.AVATAR_CREATION, "头像创建完成", 50)
        
        return {
            "success": True,
            "stage": self.stage_id,
            "message": "头像创建成功",
            "thumbnail_path": thumbnail_path
        }


class VoiceSynthesisStage(PipelineStage):
    """语音合成阶段"""
    
    def __init__(self):
        super().__init__("语音合成", GenerationStage.VOICE_SYNTHESIS.value)
    
    async def process(self, task: GenerationTask, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        合成语音
        
        Args:
            task: 任务对象
            context: 上下文数据
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        # 初始阶段进度
        task.update_stage(GenerationStage.VOICE_SYNTHESIS, "准备进行语音合成", 35)
        
        # 获取欢迎文本
        welcome_text = task.welcome_text
        if not welcome_text:
            welcome_text = "你好，我是一名数字人助手，很高兴认识你。"
            task.add_log("未提供欢迎文本，将使用默认文本", LogLevel.INFO)
        
        # 获取语音ID
        voice_id = task.voice_id
        if not voice_id:
            voice_id = "zh-female1"  # 默认中文女声
            task.add_log(f"未提供语音ID，将使用默认值: {voice_id}", LogLevel.INFO)
        
        task.update_stage(GenerationStage.VOICE_SYNTHESIS, "正在进行语音合成", 40)
        
        # 创建TTS输出目录
        dh_dir = os.path.join("data", "digital_humans", task.digital_human_id)
        os.makedirs(dh_dir, exist_ok=True)
        
        # TTS输出路径
        audio_filename = f"welcome_{uuid.uuid4().hex}.mp3"
        audio_path = os.path.join(dh_dir, audio_filename)
        
        # 获取TTS服务
        tts_service = services.tts_service.get_tts_service()
        if not tts_service:
            task.add_log("无法获取TTS服务", LogLevel.ERROR)
            return {
                "success": False,
                "error": "无法获取TTS服务",
                "stage": self.stage_id
            }
        
        try:
            # 初始化TTS服务（如果尚未初始化）
            if not services.tts_service.is_initialized():
                task.update_stage(GenerationStage.VOICE_SYNTHESIS, "正在初始化TTS服务", 42)
                init_success = await services.tts_service.initialize()
                if not init_success:
                    task.add_log("初始化TTS服务失败，但将尝试继续生成", LogLevel.WARNING)
            
            # 更新进度
            task.update_stage(GenerationStage.VOICE_SYNTHESIS, "正在生成语音", 45)
            
            # 开始语音合成，记录起始时间
            start_time = time.time()
            
            # 调用TTS服务
            tts_result = await tts_service.generate_speech(
                text=welcome_text,
                voice_id=voice_id,
                output_path=audio_path
            )
            
            # 计算耗时
            elapsed_time = time.time() - start_time
            
            # 根据结果更新任务状态和进度
            if tts_result.get("status") == "success":
                task.update_stage(GenerationStage.VOICE_SYNTHESIS, 
                                 f"语音生成成功，耗时: {elapsed_time:.2f}秒，引擎: {tts_result.get('engine', 'unknown')}", 
                                 50)
                
                # 进一步根据引擎类型记录日志
                engine_type = tts_result.get("engine", "unknown")
                if engine_type == "edge-tts":
                    task.add_log(f"使用Edge TTS引擎成功生成语音，耗时: {elapsed_time:.2f}秒", LogLevel.INFO)
                elif engine_type == "offline":
                    task.add_log(f"使用离线TTS引擎成功生成语音，耗时: {elapsed_time:.2f}秒", LogLevel.INFO)
                elif engine_type == "gtts":
                    task.add_log(f"使用在线gTTS引擎成功生成语音，耗时: {elapsed_time:.2f}秒", LogLevel.INFO)
                elif engine_type == "silent":
                    task.add_log("所有TTS服务失败，已创建静音文件作为后备方案", LogLevel.WARNING)
                else:
                    task.add_log(f"使用{engine_type}引擎成功生成语音，耗时: {elapsed_time:.2f}秒", LogLevel.INFO)
                
                # 如果是从缓存获取，添加额外日志
                if tts_result.get("from_cache", False):
                    task.add_log("语音文件从缓存中获取", LogLevel.INFO)
                
                # 保存音频路径到上下文
                context["audio_path"] = tts_result.get("path") or audio_path
                
                # 确保返回的路径存在
                if not os.path.exists(context["audio_path"]):
                    task.add_log(f"TTS服务报告成功但文件不存在: {context['audio_path']}", LogLevel.WARNING)
                    # 尝试创建静音文件作为后备
                    try:
                        # 创建一个最小的MP3文件
                        with open(audio_path, 'wb') as f:
                            f.write(b'\xff\xfb\x90\x04\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00')
                        context["audio_path"] = audio_path
                        task.add_log("已创建静音文件作为后备方案", LogLevel.WARNING)
                    except Exception as e:
                        task.add_log(f"创建静音文件失败: {str(e)}", LogLevel.ERROR)
                        return {
                            "success": False,
                            "error": f"TTS生成失败并且无法创建静音文件: {str(e)}",
                            "stage": self.stage_id
                        }
            else:
                # TTS服务返回失败
                error_msg = tts_result.get("message", "未知错误")
                task.add_log(f"语音生成失败: {error_msg}", LogLevel.ERROR)
                
                # 尝试创建静音文件作为后备
                try:
                    # 创建一个最小的MP3文件
                    with open(audio_path, 'wb') as f:
                        f.write(b'\xff\xfb\x90\x04\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00')
                    context["audio_path"] = audio_path
                    task.update_stage(GenerationStage.VOICE_SYNTHESIS, "使用静音文件作为后备", 50)
                    task.add_log("已创建静音文件作为后备方案", LogLevel.WARNING)
                except Exception as e:
                    task.add_log(f"创建静音文件失败: {str(e)}", LogLevel.ERROR)
                    return {
                        "success": False,
                        "error": f"TTS生成失败并且无法创建静音文件: {str(e)}",
                        "stage": self.stage_id
                    }
        
        except Exception as e:
            task.add_log(f"语音合成过程中发生错误: {str(e)}", LogLevel.ERROR)
            # 尝试创建静音文件作为后备
            try:
                # 创建一个最小的MP3文件
                with open(audio_path, 'wb') as f:
                    f.write(b'\xff\xfb\x90\x04\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00')
                context["audio_path"] = audio_path
                task.update_stage(GenerationStage.VOICE_SYNTHESIS, "使用静音文件作为后备", 50)
                task.add_log("已创建静音文件作为后备方案", LogLevel.WARNING)
            except Exception as create_error:
                task.add_log(f"创建静音文件失败: {str(create_error)}", LogLevel.ERROR)
                return {
                    "success": False,
                    "error": f"语音合成错误并且无法创建静音文件: {str(e)}",
                    "stage": self.stage_id
                }
        
        # 最终确保音频路径存在于上下文中
        if "audio_path" not in context or not context["audio_path"]:
            return {
                "success": False,
                "error": "语音合成失败且无法创建后备文件",
                "stage": self.stage_id
            }
        
        # 更新进度
        task.update_stage(GenerationStage.VOICE_SYNTHESIS, "语音合成完成", 50)
        
        return {
            "success": True,
            "message": "语音合成成功",
            "stage": self.stage_id,
            "audio_path": context["audio_path"]
        }


class LipsyncStage(PipelineStage):
    """唇形同步阶段"""
    
    def __init__(self):
        super().__init__("唇形同步", GenerationStage.LIPSYNC.value)
    
    async def process(self, task: GenerationTask, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行唇形同步
        
        Args:
            task: 任务对象
            context: 上下文数据
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        task.update_stage(GenerationStage.LIPSYNC, "开始唇形同步", 65)
        
        # 检查必要条件
        if not context.get("avatar_created", False):
            task.add_log("未创建头像，无法进行唇形同步", LogLevel.ERROR)
            return {
                "success": False,
                "error": "未创建头像，无法进行唇形同步",
                "stage": self.stage_id
            }
            
        if not context.get("voice_synthesized", False):
            task.add_log("未合成语音，无法进行唇形同步", LogLevel.ERROR)
            return {
                "success": False,
                "error": "未合成语音，无法进行唇形同步",
                "stage": self.stage_id
            }
        
        # 获取必要信息
        avatar_dir = context.get("avatar_dir")
        audio_path = context.get("audio_path")
        media_path = context.get("media_path")
        
        # 创建输出路径
        lipsync_path = os.path.join(avatar_dir, f"{task.digital_human_id}_lipsync.mp4")
        
        # 模拟唇形同步过程
        # 实际实现中应当调用唇形同步服务
        await asyncio.sleep(5)  # 模拟处理时间，唇形同步通常需要较长时间
        
        # 更新进度
        task.update_stage(GenerationStage.LIPSYNC, "处理唇形特征...", 70)
        await asyncio.sleep(3)  # 继续模拟
        
        task.update_stage(GenerationStage.LIPSYNC, "合成面部动画...", 75)
        await asyncio.sleep(3)  # 继续模拟
        
        # 写入模拟视频文件
        with open(lipsync_path, "wb") as f:
            f.write(b"dummy lipsync video")
        
        # 添加处理结果到上下文
        context.update({
            "lipsync_completed": True,
            "lipsync_path": lipsync_path
        })
        
        task.update_stage(GenerationStage.LIPSYNC, "唇形同步完成", 80)
        
        return {
            "success": True,
            "stage": self.stage_id,
            "message": "唇形同步成功",
            "lipsync_path": lipsync_path
        }


class VideoGenerationStage(PipelineStage):
    """视频生成阶段"""
    
    def __init__(self):
        super().__init__("视频生成", GenerationStage.VIDEO_GENERATION.value)
    
    async def process(self, task: GenerationTask, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        生成最终视频
        
        Args:
            task: 任务对象
            context: 上下文数据
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        task.update_stage(GenerationStage.VIDEO_GENERATION, "开始生成视频", 85)
        
        # 检查必要条件
        if not context.get("lipsync_completed", False):
            task.add_log("未完成唇形同步，无法生成视频", LogLevel.ERROR)
            return {
                "success": False,
                "error": "未完成唇形同步，无法生成视频",
                "stage": self.stage_id
            }
        
        # 获取必要信息
        avatar_dir = context.get("avatar_dir")
        lipsync_path = context.get("lipsync_path")
        
        # 创建输出路径
        video_path = os.path.join(avatar_dir, f"{task.digital_human_id}_video.mp4")
        
        # 模拟视频生成过程
        # 实际实现中应当调用视频处理服务
        await asyncio.sleep(3)  # 模拟处理时间
        
        # 更新进度
        task.update_stage(GenerationStage.VIDEO_GENERATION, "处理视频特效...", 90)
        
        # 这里可以简单地复制唇形同步的结果作为最终视频
        shutil.copy(lipsync_path, video_path)
        
        # 添加处理结果到上下文
        context.update({
            "video_generated": True,
            "video_path": video_path
        })
        
        # 更新任务结果URL
        task.result_url = f"/api/digital-human/media/video/{task.digital_human_id}"
        
        task.update_stage(GenerationStage.VIDEO_GENERATION, "视频生成完成", 95)
        
        return {
            "success": True,
            "stage": self.stage_id,
            "message": "视频生成成功",
            "video_path": video_path
        }


class PostProcessingStage(PipelineStage):
    """后处理阶段"""
    
    def __init__(self):
        super().__init__("后处理", GenerationStage.POST_PROCESSING.value)
    
    async def process(self, task: GenerationTask, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行后处理工作
        
        Args:
            task: 任务对象
            context: 上下文数据
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        task.update_stage(GenerationStage.POST_PROCESSING, "开始后处理", 97)
        
        # 检查视频是否生成完成
        if not context.get("video_generated", False):
            task.add_log("视频未生成完成，无法进行后处理", LogLevel.ERROR)
            return {
                "success": False,
                "error": "视频未生成完成，无法进行后处理",
                "stage": self.stage_id
            }
        
        # 获取必要信息
        avatar_dir = context.get("avatar_dir")
        video_path = context.get("video_path")
        audio_path = context.get("audio_path")
        thumbnail_path = context.get("thumbnail_path")
        
        # 写入元数据
        metadata_path = os.path.join(avatar_dir, f"{task.digital_human_id}_metadata.json")
        metadata = {
            "digital_human_id": task.digital_human_id,
            "user_id": task.user_id,
            "media_file_path": task.media_file_path,
            "create_time": task.create_time.isoformat() if task.create_time else None,
            "update_time": task.update_time.isoformat() if task.update_time else None,
            "voice_id": context.get("voice_id"),
            "welcome_text": context.get("welcome_text"),
            "thumbnail_path": thumbnail_path,
            "audio_path": audio_path,
            "video_path": video_path,
            "generation_metadata": task.metadata
        }
        
        with open(metadata_path, "w", encoding="utf-8") as f:
            json.dump(metadata, f, ensure_ascii=False, indent=2)
        
        # 设置资源路径
        assets = {
            "video": f"/api/digital-human/media/video/{task.digital_human_id}",
            "audio": f"/api/digital-human/media/audio/{task.digital_human_id}",
            "thumbnail": f"/api/digital-human/media/thumbnail/{task.digital_human_id}"
        }
        
        # 更新数据库中的数字人记录
        # 实际实现中应当调用数据库服务更新记录
        # 例如：await update_digital_human_record(task.digital_human_id, assets)
        
        # 添加处理结果到上下文
        context.update({
            "post_processing_completed": True,
            "metadata_path": metadata_path,
            "assets": assets
        })
        
        task.update_stage(GenerationStage.POST_PROCESSING, "后处理完成", 100)
        
        return {
            "success": True,
            "stage": self.stage_id,
            "message": "后处理完成",
            "assets": assets
        }


class FinalStage(PipelineStage):
    """最终阶段"""
    
    def __init__(self):
        super().__init__("完成", GenerationStage.FINAL.value)
    
    async def process(self, task: GenerationTask, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        标记任务完成
        
        Args:
            task: 任务对象
            context: 上下文数据
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        task.update_stage(GenerationStage.FINAL, "数字人生成完成", 100)
        
        # 获取资源路径
        assets = context.get("assets", {})
        
        # 更新任务状态
        task.update_status(TaskStatus.COMPLETED, "数字人生成任务已完成")
        
        return {
            "success": True,
            "stage": self.stage_id,
            "message": "任务完成",
            "assets": assets
        }


class GenerationPipeline:
    """数字人生成流水线"""
    
    def __init__(self, mock_mode: bool = False):
        """
        初始化流水线
        
        Args:
            mock_mode: 是否使用模拟模式
        """
        self.mock_mode = mock_mode
        self.stages = []
        
        # 构建流水线
        self._build_pipeline()
    
    def _build_pipeline(self):
        """构建流水线阶段"""
        # 创建各阶段
        media_validation = MediaValidationStage()
        face_detection = FaceDetectionStage()
        avatar_creation = AvatarCreationStage()
        voice_synthesis = VoiceSynthesisStage()
        lipsync = LipsyncStage()
        video_generation = VideoGenerationStage()
        post_processing = PostProcessingStage()
        final = FinalStage()
        
        # 添加到流水线
        self.stages = [
            media_validation,
            face_detection,
            avatar_creation,
            voice_synthesis,
            lipsync,
            video_generation,
            post_processing,
            final
        ]
        
        # 链接各阶段
        for i in range(len(self.stages) - 1):
            self.stages[i].next_stage = self.stages[i + 1]
        
        logger.info(f"构建了{len(self.stages)}阶段的生成流水线")
    
    async def process_task(self, task: GenerationTask) -> Dict[str, Any]:
        """
        处理任务
        
        Args:
            task: 任务对象
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        if not self.stages:
            logger.error("流水线未构建，无法处理任务")
            task.update_status(TaskStatus.FAILED, "流水线未构建，无法处理任务")
            return {
                "success": False,
                "error": "流水线未构建，无法处理任务"
            }
        
        # 初始化上下文
        context = {
            "task_id": task.task_id,
            "digital_human_id": task.digital_human_id,
            "user_id": task.user_id,
            "start_time": datetime.now(),
            "mock_mode": self.mock_mode
        }
        
        # 添加元数据到上下文
        if task.metadata:
            context.update(task.metadata)
        
        # 处理各阶段
        current_stage = self.stages[0]
        final_result = None
        
        while current_stage:
            logger.info(f"开始处理任务 {task.task_id} 的 {current_stage.name} 阶段")
            
            try:
                # 处理当前阶段
                stage_result = await current_stage.process(task, context)
                
                # 记录结果
                final_result = stage_result
                
                # 检查处理是否成功
                if not stage_result.get("success", False):
                    error_message = stage_result.get("error", f"{current_stage.name}阶段处理失败")
                    logger.error(f"任务 {task.task_id} 在 {current_stage.name} 阶段失败: {error_message}")
                    task.update_status(TaskStatus.FAILED, error_message)
                    return {
                        "success": False,
                        "error": error_message,
                        "stage": current_stage.stage_id,
                        "context": context
                    }
                
                # 转到下一阶段
                current_stage = current_stage.next_stage
                
            except Exception as e:
                error_message = f"{current_stage.name}阶段处理出错: {str(e)}"
                logger.error(f"任务 {task.task_id} 处理异常: {error_message}")
                logger.debug(traceback.format_exc())
                
                task.update_status(TaskStatus.FAILED, error_message)
                return {
                    "success": False,
                    "error": error_message,
                    "stage": current_stage.stage_id,
                    "exception": str(e),
                    "context": context
                }
        
        # 添加处理时间
        context["end_time"] = datetime.now()
        context["duration"] = (context["end_time"] - context["start_time"]).total_seconds()
        
        logger.info(f"任务 {task.task_id} 处理完成，耗时 {context['duration']:.2f} 秒")
        
        return {
            "success": True,
            "message": "任务处理完成",
            "context": context,
            "result": final_result
        } 