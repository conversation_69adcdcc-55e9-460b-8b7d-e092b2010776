import os
import logging
import time
import json
from typing import Dict, Any
from datetime import datetime

# 配置日志
logger = logging.getLogger(__name__)

class TTSServiceStatus:
    """
    TTS服务状态监控工具，用于收集、记录和报告TTS服务的健康状态
    """
    
    def __init__(self, status_file_path=None):
        """
        初始化TTS服务状态监控
        
        Args:
            status_file_path: 状态文件路径，默认为数据目录下的tts_service_status.json
        """
        if status_file_path is None:
            self.status_file_path = os.path.join(
                os.getcwd(), "data", "system", "tts_service_status.json"
            )
        else:
            self.status_file_path = status_file_path
            
        # 确保目录存在
        os.makedirs(os.path.dirname(self.status_file_path), exist_ok=True)
        
        # 初始化状态数据
        self.status_data = {
            "last_check_time": None,
            "is_healthy": True,
            "error_count": 0,
            "success_count": 0,
            "last_error": None,
            "average_response_time": 0,
            "cache_stats": {
                "hit_rate": 0,
                "total_requests": 0
            },
            "voice_usage": {},
            "daily_stats": {}
        }
        
        # 加载历史数据
        self._load_status()
    
    def _load_status(self):
        """加载状态数据"""
        if os.path.exists(self.status_file_path):
            try:
                with open(self.status_file_path, 'r', encoding='utf-8') as f:
                    self.status_data = json.load(f)
            except Exception as e:
                logger.warning(f"加载TTS服务状态数据失败: {e}")
    
    def _save_status(self):
        """保存状态数据"""
        try:
            with open(self.status_file_path, 'w', encoding='utf-8') as f:
                json.dump(self.status_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.warning(f"保存TTS服务状态数据失败: {e}")
    
    def record_tts_result(self, result: Dict[str, Any]):
        """
        记录TTS请求结果
        
        Args:
            result: TTS请求结果字典
        """
        # 更新时间
        current_time = time.time()
        today = datetime.now().strftime("%Y-%m-%d")
        self.status_data["last_check_time"] = current_time
        
        # 初始化每日统计
        if today not in self.status_data["daily_stats"]:
            self.status_data["daily_stats"][today] = {
                "total_requests": 0,
                "success_count": 0,
                "error_count": 0,
                "average_time": 0,
                "voice_usage": {}
            }
        
        # 更新请求统计
        voice_id = result.get("voice_id", "unknown")
        
        # 统计语音使用情况
        if voice_id not in self.status_data["voice_usage"]:
            self.status_data["voice_usage"][voice_id] = 0
        self.status_data["voice_usage"][voice_id] += 1
        
        # 更新今日语音使用
        daily_stats = self.status_data["daily_stats"][today]
        daily_stats["total_requests"] += 1
        
        if voice_id not in daily_stats["voice_usage"]:
            daily_stats["voice_usage"][voice_id] = 0
        daily_stats["voice_usage"][voice_id] += 1
        
        # 处理成功/失败
        if result.get("status") == "success":
            self.status_data["success_count"] += 1
            daily_stats["success_count"] += 1
            
            # 更新响应时间统计
            if "generation_time" in result:
                current_avg = self.status_data["average_response_time"]
                new_time = result["generation_time"]
                total = self.status_data["success_count"]
                
                # 计算新的平均响应时间
                if total > 1:
                    self.status_data["average_response_time"] = current_avg + (new_time - current_avg) / total
                else:
                    self.status_data["average_response_time"] = new_time
                
                # 更新每日平均时间
                current_daily_avg = daily_stats.get("average_time", 0)
                daily_success = daily_stats["success_count"]
                
                if daily_success > 1:
                    daily_stats["average_time"] = current_daily_avg + (new_time - current_daily_avg) / daily_success
                else:
                    daily_stats["average_time"] = new_time
        else:
            self.status_data["error_count"] += 1
            daily_stats["error_count"] += 1
            self.status_data["last_error"] = {
                "message": result.get("message", "未知错误"),
                "time": current_time
            }
        
        # 计算健康状态 - 如果错误率超过20%且至少有5个请求，则认为不健康
        total_requests = self.status_data["success_count"] + self.status_data["error_count"]
        if total_requests >= 5:
            error_rate = self.status_data["error_count"] / total_requests
            self.status_data["is_healthy"] = error_rate < 0.2
        
        # 更新缓存统计
        if "cached" in result:
            cache_hit = result.get("cached", False)
            self.status_data["cache_stats"]["total_requests"] += 1
            
            if cache_hit:
                # 更新缓存命中次数
                if "hit_count" not in self.status_data["cache_stats"]:
                    self.status_data["cache_stats"]["hit_count"] = 0
                self.status_data["cache_stats"]["hit_count"] += 1
            
            # 计算缓存命中率
            hit_count = self.status_data["cache_stats"].get("hit_count", 0)
            total = self.status_data["cache_stats"]["total_requests"]
            self.status_data["cache_stats"]["hit_rate"] = (hit_count / total) * 100 if total > 0 else 0
        
        # 清理过期的每日统计（保留30天）
        days_to_keep = 30
        if len(self.status_data["daily_stats"]) > days_to_keep:
            # 按日期排序
            sorted_days = sorted(self.status_data["daily_stats"].keys())
            for day in sorted_days[:-days_to_keep]:
                del self.status_data["daily_stats"][day]
        
        # 保存状态
        self._save_status()
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取TTS服务状态
        
        Returns:
            Dict: 服务状态数据
        """
        return self.status_data
    
    def get_health_check(self) -> Dict[str, Any]:
        """
        获取健康检查结果，用于API健康监控
        
        Returns:
            Dict: 健康检查数据
        """
        # 计算最近24小时的统计
        current_time = time.time()
        one_day_ago = current_time - (24 * 60 * 60)
        
        today = datetime.now().strftime("%Y-%m-%d")
        yesterday = datetime.fromtimestamp(one_day_ago).strftime("%Y-%m-%d")
        
        recent_stats = {
            "requests_24h": 0,
            "success_24h": 0,
            "error_24h": 0
        }
        
        for day in [today, yesterday]:
            if day in self.status_data["daily_stats"]:
                daily = self.status_data["daily_stats"][day]
                recent_stats["requests_24h"] += daily["total_requests"]
                recent_stats["success_24h"] += daily["success_count"]
                recent_stats["error_24h"] += daily["error_count"]
        
        # 如果最近24小时有至少5个请求
        is_active = recent_stats["requests_24h"] >= 5
        error_rate_24h = 0
        if recent_stats["requests_24h"] > 0:
            error_rate_24h = recent_stats["error_24h"] / recent_stats["requests_24h"]
        
        # 判断24小时健康状态
        healthy_24h = error_rate_24h < 0.2
        
        return {
            "service": "tts",
            "is_healthy": self.status_data["is_healthy"],
            "is_active": is_active,
            "healthy_24h": healthy_24h,
            "error_rate": error_rate_24h * 100,
            "last_check": self.status_data["last_check_time"],
            "response_time_avg": self.status_data["average_response_time"],
            "requests_24h": recent_stats["requests_24h"],
            "last_error": self.status_data["last_error"]
        }
    
    def reset_stats(self):
        """重置统计数据"""
        self.status_data["error_count"] = 0
        self.status_data["success_count"] = 0
        self.status_data["average_response_time"] = 0
        self.status_data["is_healthy"] = True
        self.status_data["last_error"] = None
        self.status_data["cache_stats"] = {
            "hit_rate": 0,
            "total_requests": 0,
            "hit_count": 0
        }
        # 保留每日统计和语音使用情况
        self._save_status()

# 全局实例
_tts_service_status = None

def get_tts_service_status() -> TTSServiceStatus:
    """获取TTS服务状态实例"""
    global _tts_service_status
    if _tts_service_status is None:
        _tts_service_status = TTSServiceStatus()
    return _tts_service_status 