/**
 * 舆情分析API服务
 */

import apiService from './api.js';

const API_BASE = '/api/v1/opinion';

// 定期简报API
export const opinionReportApi = {
  // 创建简报任务
  createReport: (data) => {
    return apiService.post(`${API_BASE}/reports`, data);
  },

  // 获取简报列表
  getReports: () => {
    return apiService.get(`${API_BASE}/reports`);
  },

  // 获取简报详情
  getReport: (id) => {
    return apiService.get(`${API_BASE}/reports/${id}`);
  },

  // 更新简报
  updateReport: (id, data) => {
    return apiService.put(`${API_BASE}/reports/${id}`, data);
  },

  // 删除简报
  deleteReport: (id) => {
    return apiService.delete(`${API_BASE}/reports/${id}`);
  },

  // 生成简报实例
  generateReportInstance: (reportId) => {
    return apiService.post(`${API_BASE}/reports/${reportId}/generate`);
  },

  // 获取简报实例列表
  getReportInstances: (reportId) => {
    return apiService.get(`${API_BASE}/reports/${reportId}/instances`);
  },

  // 获取简报实例详情
  getReportInstance: (reportId, instanceId) => {
    return apiService.get(`${API_BASE}/reports/${reportId}/instances/${instanceId}`);
  }
};

// 舆情检索API
export const opinionSearchApi = {
  // 执行舆情检索
  searchOpinions: (data) => {
    return apiService.post(`${API_BASE}/search`, data);
  },

  // 获取检索结果
  getSearchResults: (searchId) => {
    return apiService.get(`${API_BASE}/search/${searchId}/results`);
  },

  // 导出检索结果
  exportSearchResults: (searchId, format = 'excel') => {
    return apiService.get(`${API_BASE}/search/${searchId}/export?format=${format}`, {
      responseType: 'blob'
    });
  }
};

// 检索方案API
export const opinionSearchSchemeApi = {
  // 创建检索方案
  createSearchScheme: (data) => {
    return apiService.post(`${API_BASE}/search-schemes`, data);
  },

  // 获取检索方案列表
  getSearchSchemes: () => {
    return apiService.get(`${API_BASE}/search-schemes`);
  },

  // 获取检索方案详情
  getSearchScheme: (id) => {
    return apiService.get(`${API_BASE}/search-schemes/${id}`);
  },

  // 更新检索方案
  updateSearchScheme: (id, data) => {
    return apiService.put(`${API_BASE}/search-schemes/${id}`, data);
  },

  // 删除检索方案
  deleteSearchScheme: (id) => {
    return apiService.delete(`${API_BASE}/search-schemes/${id}`);
  }
};

// 预警API
export const opinionAlertApi = {
  // 创建预警
  createAlert: (data) => {
    return apiService.post(`${API_BASE}/alerts`, data);
  },

  // 获取预警列表
  getAlerts: () => {
    return apiService.get(`${API_BASE}/alerts`);
  },

  // 获取预警详情
  getAlert: (id) => {
    return apiService.get(`${API_BASE}/alerts/${id}`);
  },

  // 更新预警
  updateAlert: (id, data) => {
    return apiService.put(`${API_BASE}/alerts/${id}`, data);
  },

  // 删除预警
  deleteAlert: (id) => {
    return apiService.delete(`${API_BASE}/alerts/${id}`);
  },

  // 获取预警记录
  getAlertRecords: (alertId) => {
    return apiService.get(`${API_BASE}/alerts/${alertId}/records`);
  }
};

// 舆情检索API
export const opinionSearchApi = {
  // 执行舆情检索
  searchOpinions: (data) => {
    return apiService.post(`${API_BASE}/search`, data);
  },

  // 获取检索结果
  getSearchResults: (params) => {
    return apiService.get(`${API_BASE}/search-results`, { params });
  },

  // 导出检索结果
  exportSearchResults: (data) => {
    return apiService.post(`${API_BASE}/search/export`, data, {
      responseType: 'blob'
    });
  }
};

// 知识图谱API
export const opinionKnowledgeGraphApi = {
  // 创建知识图谱
  createKnowledgeGraph: (data) => {
    return apiService.post(`${API_BASE}/knowledge-graph`, data);
  },

  // 获取知识图谱列表
  getKnowledgeGraphs: () => {
    return apiService.get(`${API_BASE}/knowledge-graphs`);
  }
};

// 统计API
export const opinionStatisticsApi = {
  // 获取统计信息
  getStatistics: () => {
    return apiService.get(`${API_BASE}/statistics`);
  }
};

// 系统状态API
export const opinionSystemApi = {
  // 获取系统状态
  getStatus: () => {
    return apiService.get(`${API_BASE}/system/status`);
  }
};

// 统一导出
export default {
  reports: opinionReportApi,
  searchSchemes: opinionSearchSchemeApi,
  alerts: opinionAlertApi,
  search: opinionSearchApi,
  knowledgeGraph: opinionKnowledgeGraphApi,
  statistics: opinionStatisticsApi,
  system: opinionSystemApi
};
