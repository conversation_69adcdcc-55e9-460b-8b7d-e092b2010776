#!/usr/bin/env python3
"""
修复数字人生成服务调用脚本
此脚本用于修复digital_human_fixed.py中对generate_digital_human方法的调用，确保参数匹配
"""
import os
import sys
import logging
import re

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def fix_service_call():
    """修复digital_human_fixed.py中对数字人生成服务的调用"""
    try:
        # 定义文件路径
        file_path = os.path.join('backend', 'api', 'digital_human_fixed.py')
        
        if not os.path.exists(file_path):
            logger.error(f"文件不存在: {file_path}")
            return False
            
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
            
        # 检查是否有问题的调用模式
        # 在生成服务的调用中寻找包含task_id但不包含progress_tracker的调用
        problem_pattern = re.compile(r'await\s+service\.generate_digital_human\s*\(\s*(?:[^)]*?task_id\s*=\s*[^,)]+[^)]*)')
        
        if not problem_pattern.search(content):
            logger.info("未找到需要修复的调用模式")
            return True
            
        # 修复调用，将task_id参数重命名为digital_human_id
        fixed_content = re.sub(
            r'(await\s+service\.generate_digital_human\s*\(\s*)'
            r'task_id\s*=\s*([^,)]+)'
            r'(,\s*digital_human_id\s*=\s*[^,)]+)?'
            r'(.*?\))',
            
            # 如果没有digital_human_id参数，保留task_id并将其重命名为digital_human_id
            lambda m: (
                f"{m.group(1)}"
                f"digital_human_id={m.group(2)}"  # 将task_id改为digital_human_id
                f"{'' if m.group(3) else ','}"     # 如果原本有digital_human_id参数，不添加逗号
                f"{m.group(4) if m.group(3) else m.group(4)}"  # 保留剩余参数
            ),
            content
        )
        
        # 将progress_tracker参数改为可选
        fixed_content = re.sub(
            r'(async\s+def\s+generate_digital_human\s*\([^)]*?)'
            r'progress_tracker\s*:\s*[^,)]+\s*'
            r'([^)]*\))',
            
            # 改为可选参数
            r'\1progress_tracker: Optional[Any] = None \2',
            fixed_content
        )
        
        # 备份原文件
        backup_path = file_path + '.bak'
        with open(backup_path, 'w', encoding='utf-8') as file:
            file.write(content)
            logger.info(f"原文件已备份到 {backup_path}")
            
        # 写入修复后的内容
        with open(file_path, 'w', encoding='utf-8') as file:
            file.write(fixed_content)
            
        logger.info(f"文件已修复: {file_path}")
        return True
        
    except Exception as e:
        logger.error(f"修复服务调用时出错: {e}", exc_info=True)
        return False

if __name__ == "__main__":
    print("===== 修复数字人生成服务调用 =====")
    
    # 修复服务调用
    if fix_service_call():
        print("\n✅ 服务调用修复成功")
    else:
        print("\n❌ 服务调用修复失败")
        sys.exit(1)
        
    print("\n✅ 操作完成")
    sys.exit(0) 