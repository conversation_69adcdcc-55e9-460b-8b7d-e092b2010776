@echo off
chcp 65001 >nul
echo ===== WAN 2.1 T2V 1.3B模型下载工具 =====
echo.
echo 该工具将从HuggingFace或ModelScope下载WAN 2.1 T2V 1.3B模型，并配置系统以使用它。
echo.

setlocal EnableDelayedExpansion

:: 检查Python安装
where python >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 未找到Python。请确保已安装Python 3.7+并添加到PATH环境变量。
    goto :error
)

:: 获取Python版本
for /f "tokens=2" %%a in ('python --version 2^>^&1') do set PYTHON_VERSION=%%a
echo 检测到Python版本: %PYTHON_VERSION%

cd %~dp0..\..

echo 设置Python环境...
set PYTHONPATH=%cd%
set PYTHONIOENCODING=utf-8

echo.
echo 开始下载WAN 2.1 T2V 1.3B模型...
echo 这可能需要一些时间，取决于您的网络速度。
echo 模型大小约为6GB，请确保您有足够的磁盘空间和稳定的网络连接。
echo.

:: 检查pip包
echo 检查必要的Python包...
python -m pip show huggingface_hub >nul 2>&1
if %errorlevel% neq 0 (
    echo 需要安装huggingface_hub包...
    python -m pip install "huggingface_hub[cli]" || goto :pip_error
)

echo.
echo [信息] 将使用以下命令下载模型:
echo python -m backend.scripts.download_wan_1_3b --download_dir "./model_download" --target_dir "backend/models_cache/Wan2.1-T2V-1.3B" --method huggingface --copy_model --debug %*
echo.
echo 下载开始时间: %time%
echo.

python -m backend.scripts.download_wan_1_3b --download_dir "./model_download" --target_dir "backend/models_cache/Wan2.1-T2V-1.3B" --method huggingface --copy_model --debug %*

if %errorlevel% neq 0 goto :error

echo.
echo 下载和配置完成！
echo 下载结束时间: %time%
echo.

echo 您现在可以使用WAN 2.1 T2V 1.3B模型生成视频。
echo 推荐参数设置:
echo - sample_guide_scale 6
echo - sample_shift 8-12
echo.

echo 运行验证测试...
python -m backend.scripts.test_wan_1_3b_model

if %errorlevel% neq 0 (
    echo.
    echo [警告] 验证测试未完全通过。请检查上述信息了解详情。
    echo 您可能需要手动完成一些配置步骤。
    echo.
) else (
    echo.
    echo [成功] 验证测试通过！模型已准备就绪。
    echo.
)

goto :end

:pip_error
echo.
echo [错误] 安装Python包失败。
echo 请确保您有正常的网络连接和pip工具。
echo 您可以尝试手动运行: pip install "huggingface_hub[cli]"
echo.
exit /b 1

:error
echo.
echo [错误] 下载失败，错误代码: %errorlevel%
echo 请检查上述日志获取详细信息。
echo.
echo 您可以尝试以下操作:
echo 1. 检查网络连接
echo 2. 确保有足够的磁盘空间
echo 3. 尝试使用ModelScope下载: python -m backend.scripts.download_wan_1_3b --method modelscope
echo 4. 手动下载: 访问 https://huggingface.co/Wan-AI/Wan2.1-T2V-1.3B
echo.
exit /b %errorlevel%

:end
echo.
echo 按任意键退出...
pause > nul 