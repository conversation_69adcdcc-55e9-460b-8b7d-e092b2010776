import os
import logging
import numpy as np
import cv2
import tempfile
import uuid
import shutil  # 添加shutil用于文件操作
from typing import Dict, List, Optional, Tuple, Any
import mediapipe as mp
import time

# 导入SadTalker服务
from services.sadtalker_service import get_sadtalker_service

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 初始化MediaPipe人脸特征检测
mp_face_mesh = mp.solutions.face_mesh
mp_drawing = mp.solutions.drawing_utils
mp_drawing_styles = mp.solutions.drawing_styles

class AvatarGenerator:
    """数字人头像生成器类"""
    
    def __init__(self):
        self.face_mesh = mp_face_mesh.FaceMesh(
            static_image_mode=False,
            max_num_faces=1,
            refine_landmarks=True,
            min_detection_confidence=0.5,
            min_tracking_confidence=0.5
        )
        # 模型路径字典，实际项目中应从配置加载
        self.model_paths = {
            "default": "./frontend/public/assets/models/default.glb",
            "realistic": "./frontend/public/assets/models/female.glb", # 假设female.glb可作为realistic的替代
            "cartoon": "./frontend/public/assets/models/cartoon.glb",
            "stylized": "./frontend/public/assets/models/male.glb", # 假设male.glb可作为stylized的替代
        }
        # 当前加载的模型
        self.current_model = None
        # 当前面部表情参数
        self.expression_params = {}
        
        # 初始化SadTalker服务
        self.sadtalker_service = get_sadtalker_service()
        
        # 设置使用AI生成的标志
        self.use_ai_generation = os.getenv("USE_AI_GENERATION", "true").lower() == "true"
        
        # 初始化默认头像目录和文件
        self._initialize_default_avatars()
        
        # 添加持久化存储目录配置
        self.permanent_storage_enabled = os.getenv("PERMANENT_AVATAR_STORAGE", "false").lower() == "true"
        self.permanent_storage_dir = os.path.join(os.getcwd(), "data", "avatars")
        # 添加临时存储目录属性，便于外部访问
        self.temp_storage_dir = tempfile.gettempdir()
        
        if self.permanent_storage_enabled:
            os.makedirs(self.permanent_storage_dir, exist_ok=True)
            logger.info(f"数字人持久化存储已启用，目录: {self.permanent_storage_dir}")
        else:
            logger.info("数字人持久化存储未启用，仅使用临时目录")
        
        logger.info(f"Avatar generator initialized (AI generation: {self.use_ai_generation})")
        
        # 添加回退模式标记
        self.using_fallback_rendering = False
    
    def _initialize_default_avatars(self):
        """初始化默认头像目录和文件"""
        try:
            # 创建默认头像目录
            default_avatar_dir = os.path.join(os.getcwd(), "backend", "assets", "default_avatars")
            os.makedirs(default_avatar_dir, exist_ok=True)
            
            # 定义默认头像文件
            default_avatars = {
                "default": os.path.join(default_avatar_dir, "default.jpg"),
                "realistic": os.path.join(default_avatar_dir, "realistic.jpg"),
                "cartoon": os.path.join(default_avatar_dir, "cartoon.jpg"),
                "stylized": os.path.join(default_avatar_dir, "stylized.jpg")
            }
            
            # 为每种样式创建默认头像
            for style, path in default_avatars.items():
                if not os.path.exists(path):
                    logger.info(f"创建默认头像: {path}")
                    self._generate_placeholder_image(path, style)
        except Exception as e:
            logger.error(f"初始化默认头像失败: {e}")
    
    def initialize_avatar_model(self, model_type: str = "default") -> bool:
        """
        初始化数字人3D模型
        
        Args:
            model_type: 模型类型，可选值: default, realistic, cartoon, stylized
            
        Returns:
            bool: 是否成功初始化
        """
        try:
            # 查找对应的模型文件
            model_path = self.model_paths.get(model_type)
            if not model_path:
                logger.warning(f"未找到模型类型 '{model_type}'，使用默认模型")
                model_type = "default"
                model_path = self.model_paths["default"]
            
            # 检查模型文件是否存在
            if not os.path.exists(model_path):
                # 如果指定的路径不存在，尝试在不同的相对路径中查找
                alternative_paths = [
                    os.path.join(os.getcwd(), "backend", "assets", "models", f"{model_type}.glb"),
                    os.path.join(os.getcwd(), "assets", "models", f"{model_type}.glb"),
                    os.path.join("backend", "assets", "models", f"{model_type}.glb"),
                    os.path.join("assets", "models", f"{model_type}.glb"),
                    # 添加更多可能的路径，支持多种模型文件格式
                    os.path.join(os.getcwd(), "backend", "assets", "models", f"{model_type}.fbx"),
                    os.path.join(os.getcwd(), "assets", "models", f"{model_type}.fbx"),
                    os.path.join(os.getcwd(), "backend", "assets", "models", f"{model_type}.obj"),
                    os.path.join(os.getcwd(), "assets", "models", f"{model_type}.obj")
                ]
                
                for path in alternative_paths:
                    if os.path.exists(path):
                        model_path = path
                        logger.info(f"找到模型文件: {path}")
                        break
                else:
                    # 创建模型目录，如果不存在
                    model_dirs = [
                        os.path.join(os.getcwd(), "backend", "assets", "models"),
                        os.path.join(os.getcwd(), "assets", "models"),
                        os.path.join("backend", "assets", "models"),
                        os.path.join("assets", "models")
                    ]
                    
                    for model_dir in model_dirs:
                        try:
                            os.makedirs(model_dir, exist_ok=True)
                            logger.info(f"创建模型目录: {model_dir}")
                            # 向第一个成功创建的目录中添加一个占位符模型文件
                            placeholder_model_path = os.path.join(model_dir, f"{model_type}.glb")
                            if not os.path.exists(placeholder_model_path):
                                # 创建一个最小占位符模型文件
                                try:
                                    with open(placeholder_model_path, 'wb') as f:
                                        f.write(b'placeholder model file')
                                    logger.info(f"创建占位符模型文件: {placeholder_model_path}")
                                    model_path = placeholder_model_path
                                    break
                                except Exception as model_err:
                                    logger.error(f"创建占位符模型文件失败: {model_err}")
                        except Exception as dir_err:
                            logger.error(f"创建模型目录失败: {dir_err}")
                    
                    # 如果所有尝试都失败，记录警告
                    logger.warning(f"模型文件不存在，将使用简单渲染模式")
                    
                    # 确保模式标记设置为回退模式
                    self.using_fallback_rendering = True
            else:
                # 找到了模型文件，禁用回退渲染模式
                self.using_fallback_rendering = False
                logger.info(f"使用模型文件: {model_path}")
            
            # 实际项目中应加载3D模型，这里为演示暂时省略复杂的3D加载代码
            # self.model = self.load_3d_model(model_path)
            self.current_model = model_type
            logger.info(f"模型 '{model_type}' 已初始化, 使用回退模式: {self.using_fallback_rendering}")
            
            # 初始化基础表情参数
            self.expression_params = {
                "mouth_open": 0.0,
                "smile": 0.0,
                "eyebrow_raise": 0.0,
                "eye_open_left": 1.0,
                "eye_open_right": 1.0,
                "head_rotation_x": 0.0,
                "head_rotation_y": 0.0,
                "head_rotation_z": 0.0
            }
            return True
        except Exception as e:
            logger.error(f"初始化数字人模型失败: {e}")
            # 设置回退渲染模式
            self.using_fallback_rendering = True
            logger.warning("已启用回退渲染模式，将使用简单渲染")
            return False
    
    def update_expression(self, params: Dict[str, float]) -> None:
        """
        更新数字人表情参数
        
        Args:
            params: 表情参数字典
        """
        for param, value in params.items():
            if param in self.expression_params:
                self.expression_params[param] = value
        logger.debug(f"表情参数已更新: {self.expression_params}")
    
    def extract_face_landmarks(self, image: np.ndarray) -> Optional[List[Tuple[float, float, float]]]:
        """
        从图像中提取人脸特征点
        
        Args:
            image: 输入图像
            
        Returns:
            landmarks: 特征点列表，如果未检测到人脸则返回None
        """
        try:
            # BGR转RGB
            rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            results = self.face_mesh.process(rgb_image)
            
            if results.multi_face_landmarks:
                # 获取第一个人脸的特征点
                face_landmarks = results.multi_face_landmarks[0]
                landmarks = []
                
                for landmark in face_landmarks.landmark:
                    # 转换特征点为图像坐标系
                    x = landmark.x * image.shape[1]
                    y = landmark.y * image.shape[0]
                    z = landmark.z
                    landmarks.append((x, y, z))
                
                return landmarks
            else:
                logger.warning("未检测到人脸")
                return None
        except Exception as e:
            logger.error(f"提取人脸特征点时出错: {e}")
            return None
    
    def landmarks_to_expression(self, landmarks: List[Tuple[float, float, float]]) -> Dict[str, float]:
        """
        将人脸特征点转换为表情参数
        
        Args:
            landmarks: 特征点列表
            
        Returns:
            expression_params: 表情参数字典
        """
        # 这里是简化实现，实际项目中应基于特征点间几何关系计算
        expression = self.expression_params.copy()
        
        if landmarks:
            # 示例：检测嘴巴开合度（使用上下嘴唇特征点）
            # MediaPipe特征点索引：上嘴唇中心13，下嘴唇中心14
            upper_lip = landmarks[13]
            lower_lip = landmarks[14]
            mouth_distance = abs(upper_lip[1] - lower_lip[1])
            
            # 归一化处理
            expression["mouth_open"] = min(mouth_distance / 30.0, 1.0)
            
            # 示例：眼睛开合度（简化计算）
            # 左眼：特征点索引上眼睑159，下眼睑145
            # 右眼：特征点索引上眼睑386，下眼睑374
            left_eye_upper = landmarks[159]
            left_eye_lower = landmarks[145]
            left_eye_distance = abs(left_eye_upper[1] - left_eye_lower[1])
            
            right_eye_upper = landmarks[386]
            right_eye_lower = landmarks[374]
            right_eye_distance = abs(right_eye_upper[1] - right_eye_lower[1])
            
            # 归一化处理
            expression["eye_open_left"] = min(left_eye_distance / 15.0, 1.0)
            expression["eye_open_right"] = min(right_eye_distance / 15.0, 1.0)
            
            # 头部旋转（简化计算）
            # 使用鼻尖(1)和鼻基部(4)估计头部旋转
            nose_tip = landmarks[1]
            nose_base = landmarks[4]
            
            expression["head_rotation_y"] = (nose_tip[0] - nose_base[0]) / 50.0
            expression["head_rotation_x"] = (nose_tip[1] - nose_base[1]) / 50.0
            
        return expression
    
    def generate_avatar_frame(self, expression_params: Optional[Dict[str, float]] = None) -> np.ndarray:
        """
        生成单帧数字人图像
        
        Args:
            expression_params: 表情参数，如果为None则使用当前表情
            
        Returns:
            frame: 生成的数字人图像帧
        """
        if expression_params:
            self.update_expression(expression_params)
        
        # 创建画布
        frame = np.zeros((480, 640, 3), dtype=np.uint8)
        
        # 检查是否使用回退渲染模式
        if self.using_fallback_rendering:
            # 使用简化的回退渲染模式
            return self._generate_fallback_frame()
        
        # 实际项目中应使用3D渲染引擎（如PyTorch3D）基于表情参数渲染数字人
        # 这里为演示，绘制一个简单的表情符号
        
        # 画脸
        cv2.circle(frame, (320, 240), 100, (200, 200, 200), -1)
        
        # 画眼睛（根据eye_open参数调整大小）
        left_eye_h = int(10 * self.expression_params["eye_open_left"])
        right_eye_h = int(10 * self.expression_params["eye_open_right"])
        
        cv2.ellipse(frame, (280, 220), (15, max(left_eye_h, 1)), 
                   0, 0, 360, (0, 0, 0), -1)
        cv2.ellipse(frame, (360, 220), (15, max(right_eye_h, 1)), 
                   0, 0, 360, (0, 0, 0), -1)
        
        # 画嘴（根据mouth_open参数调整）
        mouth_h = int(30 * self.expression_params["mouth_open"])
        cv2.ellipse(frame, (320, 280), (30, max(mouth_h, 5)), 
                   0, 0, 360, (0, 0, 0), -1)
        
        # 为不同模型类型设置不同颜色
        if self.current_model == "realistic":
            # 添加肤色
            cv2.circle(frame, (320, 240), 100, (200, 160, 140), -1)
        elif self.current_model == "cartoon":
            # 添加卡通色彩
            cv2.circle(frame, (320, 240), 100, (240, 240, 100), -1)
        elif self.current_model == "stylized":
            # 添加艺术风格
            cv2.circle(frame, (320, 240), 100, (180, 130, 180), -1)
        
        return frame
    
    def _generate_fallback_frame(self) -> np.ndarray:
        """
        生成回退模式下的简单头像帧
        
        Returns:
            np.ndarray: 简单的头像帧
        """
        # 创建基本画布
        frame = np.zeros((480, 640, 3), dtype=np.uint8)
        frame[:] = (240, 240, 240)  # 浅灰色背景
        
        # 添加简单的头像图形
        # 头部
        cv2.circle(frame, (320, 240), 120, (220, 220, 220), -1)
        cv2.circle(frame, (320, 240), 120, (180, 180, 180), 2)
        
        # 眼睛（基于表情参数）
        left_eye_h = int(15 * self.expression_params["eye_open_left"])
        right_eye_h = int(15 * self.expression_params["eye_open_right"])
        
        # 左眼
        cv2.ellipse(frame, (270, 210), (20, max(left_eye_h, 1)), 
                    0, 0, 360, (100, 100, 100), -1)
        # 右眼
        cv2.ellipse(frame, (370, 210), (20, max(right_eye_h, 1)), 
                    0, 0, 360, (100, 100, 100), -1)
        
        # 嘴巴（基于表情参数）
        mouth_open = self.expression_params["mouth_open"]
        smile = self.expression_params["smile"]
        
        # 根据表情计算嘴巴形状
        mouth_height = int(30 * mouth_open) + 5
        mouth_curve = int(20 * smile)
        
        # 绘制嘴巴
        if mouth_curve > 0:  # 微笑
            cv2.ellipse(frame, (320, 270), (40, mouth_height), 
                        0, 0, 180, (120, 80, 90), -1)
        elif mouth_curve < 0:  # 不高兴
            cv2.ellipse(frame, (320, 310), (40, mouth_height), 
                        0, 180, 360, (120, 80, 90), -1)
        else:  # 中性
            cv2.rectangle(frame, (280, 270), (360, 270 + mouth_height), (120, 80, 90), -1)
        
        # 添加一些风格元素
        if self.current_model == "realistic":
            # 更自然的肤色
            cv2.circle(frame, (320, 240), 118, (200, 170, 150), -1)
            # 画鼻子
            cv2.ellipse(frame, (320, 245), (10, 15), 0, 0, 360, (180, 150, 130), -1)
        elif self.current_model == "cartoon":
            # 卡通风格色彩
            cv2.circle(frame, (320, 240), 118, (240, 220, 100), -1)
            # 简化的鼻子
            cv2.circle(frame, (320, 245), 8, (220, 200, 80), -1)
        elif self.current_model == "stylized":
            # 艺术风格
            cv2.circle(frame, (320, 240), 118, (180, 150, 200), -1)
            # 装饰性元素
            cv2.ellipse(frame, (320, 245), (8, 12), 0, 0, 360, (160, 130, 180), -1)
        
        # 添加"回退模式"水印
        font = cv2.FONT_HERSHEY_SIMPLEX
        cv2.putText(frame, "Fallback Mode", (220, 400), font, 1, (100, 100, 100), 2)
        
        return frame
    
    def generate_frames_for_phonemes(self, phonemes: List[Dict], fps: int = 30) -> List[np.ndarray]:
        """
        基于音素数据生成帧序列
        
        Args:
            phonemes: 音素列表，每个音素包含类型和持续时间
            fps: 视频帧率
            
        Returns:
            frames: 图像帧列表
        """
        frames = []
        total_duration = sum(p["duration"] for p in phonemes)
        total_frames = int(total_duration * fps)
        
        current_time = 0
        current_phoneme_idx = 0
        
        for frame_idx in range(total_frames):
            frame_time = frame_idx / fps
            
            # 找到当前时间对应的音素
            while (current_phoneme_idx < len(phonemes) - 1 and 
                   current_time + phonemes[current_phoneme_idx]["duration"] <= frame_time):
                current_time += phonemes[current_phoneme_idx]["duration"]
                current_phoneme_idx += 1
            
            # 根据当前音素设置嘴型
            current_phoneme = phonemes[current_phoneme_idx]["phoneme"]
            
            # 简化的音素到嘴型映射
            expression = self.expression_params.copy()
            
            # 根据音素调整嘴型
            if current_phoneme in ["a", "ah"]:
                expression["mouth_open"] = 0.8
            elif current_phoneme in ["e", "eh"]:
                expression["mouth_open"] = 0.6
            elif current_phoneme in ["i", "ih"]:
                expression["mouth_open"] = 0.3
            elif current_phoneme in ["o", "oh"]:
                expression["mouth_open"] = 0.7
            elif current_phoneme in ["u", "uh"]:
                expression["mouth_open"] = 0.4
            elif current_phoneme in ["p", "b", "m"]:
                expression["mouth_open"] = 0.1
            else:
                expression["mouth_open"] = 0.2
            
            # 生成当前帧
            frame = self.generate_avatar_frame(expression)
            frames.append(frame)
        
        return frames
    
    def _get_storage_path(self, filename, temp=True):
        """
        获取存储路径，根据配置决定使用临时目录还是永久存储目录
        
        Args:
            filename: 文件名
            temp: 是否使用临时目录，即使启用了永久存储
            
        Returns:
            str: 文件完整路径
        """
        if temp or not self.permanent_storage_enabled:
            # 使用临时目录
            temp_dir = tempfile.gettempdir()
            return os.path.join(temp_dir, filename)
        else:
            # 使用永久存储目录
            return os.path.join(self.permanent_storage_dir, filename)
    
    def render_avatar_video_with_ai(
        self,
        audio_path: str,
        image_path: str,
        expressions: List[Dict[str, float]] = None,
        avatar_style: str = "default",
        output_path: str = None,
        use_permanent_storage: bool = None  # 新增参数控制是否使用永久存储
    ) -> str:
        """
        使用AI渲染数字人视频
        
        Args:
            audio_path: 音频文件路径
            image_path: 图像文件路径
            expressions: 表情数据列表
            avatar_style: 头像样式
            output_path: 输出路径（如果为None则自动生成）
            use_permanent_storage: 是否使用永久存储
            
        Returns:
            生成的视频文件路径
        """
        # 使用SadTalker服务生成说话人视频
        try:
            # 确定是否使用永久存储
            use_permanent = use_permanent_storage if use_permanent_storage is not None else self.permanent_storage_enabled
            
            # 如果未指定输出路径，则生成临时路径或永久路径
            if output_path is None:
                if use_permanent and self.permanent_storage_enabled:
                    # 生成一个唯一ID作为文件名
                    video_id = str(uuid.uuid4())
                    output_path = os.path.join(self.permanent_storage_dir, f"avatar_{video_id}.mp4")
                else:
                    # 使用临时目录
                    output_path = os.path.join(self.temp_storage_dir, f"avatar_{uuid.uuid4()}.mp4")
            
            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # 获取SadTalker服务
            sadtalker = get_sadtalker_service()
            if not sadtalker.initialize():
                logger.error("初始化SadTalker服务失败")
                return None
            
            # 选择合适的预处理方式和姿势样式
            preprocess = "full"  # 可选: crop, extcrop, resize, full, extfull
            pose_style = 0  # 姿势样式 (0-46)
            
            # 如果提供了表情数据，计算表情强度（影响嘴部运动的剧烈程度）
            expression_scale = 1.0
            if expressions:
                # 分析表情数据，计算适当的表情强度
                mouth_openings = [exp.get("mouthOpen", 0) for exp in expressions if "mouthOpen" in exp]
                if mouth_openings:
                    avg_mouth_open = sum(mouth_openings) / len(mouth_openings)
                    max_mouth_open = max(mouth_openings)
                    
                    # 根据嘴部开合程度调整表情强度
                    if max_mouth_open > 0.7:  # 如果最大开口很大
                        expression_scale = 2.0
                    elif avg_mouth_open > 0.3:  # 如果平均开口中等
                        expression_scale = 1.5
                    else:  # 如果嘴部动作很小
                        expression_scale = 1.2
                    
                    logger.info(f"根据表情计算的表情强度: {expression_scale}")
            
            logger.info(f"视频将存储在: {output_path} ({'永久存储' if use_permanent else '临时目录'})")
            
            # 使用SadTalker生成视频
            logger.info(f"使用SadTalker生成视频: 风格={avatar_style}, 姿势={pose_style}, 表情强度={expression_scale}, 预处理={preprocess}")
            result = sadtalker.generate_talking_video(
                image_path=image_path,
                audio_path=audio_path,
                output_path=output_path,
                pose_style=pose_style,
                expression_scale=expression_scale,
                enhance=True,
                preprocess=preprocess,
                fallback=True  # 启用fallback模式，确保即使SadTalker失败也能返回视频
            )
            
            # 检查SadTalker生成结果
            if not result["success"]:
                logger.error(f"SadTalker生成视频失败: {result.get('error', '未知错误')}")
                return None
                
            # 获取实际生成的视频路径（可能与预期路径不同）
            actual_video_path = result["video_path"]
            logger.info(f"SadTalker成功生成视频: {actual_video_path}")
            
            # 生成成功后，如果启用了永久存储但输出在临时目录，则复制到永久目录
            if result["success"] and use_permanent and self.permanent_storage_enabled:
                # 检查实际生成的视频是否已经在永久存储目录中
                if not actual_video_path.startswith(self.permanent_storage_dir):
                    # 从actual_video_path中提取基本文件名
                    base_filename = os.path.basename(actual_video_path)
                    
                    # 如果基本文件名不是以avatar_开头，规范化文件名
                    if not base_filename.startswith("avatar_"):
                        # 生成新的文件名或使用之前的output_path
                        if output_path and output_path.startswith(self.permanent_storage_dir):
                            perm_video_path = output_path
                        else:
                            video_id = str(uuid.uuid4())
                            perm_video_path = os.path.join(self.permanent_storage_dir, f"avatar_{video_id}.mp4")
                    else:
                        # 保持原来的文件名但放在永久存储目录
                        perm_video_path = os.path.join(self.permanent_storage_dir, base_filename)
                    
                    try:
                        # 确保目标目录存在
                        os.makedirs(os.path.dirname(perm_video_path), exist_ok=True)
                        
                        # 复制到永久存储位置
                        shutil.copy2(actual_video_path, perm_video_path)
                        logger.info(f"视频已复制到永久存储位置: {perm_video_path}")
                        
                        # 更新返回路径为永久存储路径
                        return perm_video_path
                    except Exception as copy_err:
                        logger.error(f"复制到永久存储失败: {copy_err}")
                        # 继续返回原始路径
                
            # 返回实际生成的视频路径（可能与预期路径不同）
            return actual_video_path
                
        except Exception as e:
            logger.error(f"渲染AI视频失败: {e}")
            import traceback
            logger.error(f"错误详情: {traceback.format_exc()}")
            return None
    
    def _generate_placeholder_image(self, output_path: str, style: str = "default") -> None:
        """生成占位图像"""
        # 创建简单的占位图像
        width, height = 512, 512
        image = np.ones((height, width, 3), dtype=np.uint8) * 240  # 浅灰色背景
        
        # 根据样式选择颜色
        colors = {
            "default": (200, 200, 200),
            "realistic": (200, 160, 140),
            "cartoon": (240, 240, 100),
            "stylized": (100, 180, 240)
        }
        color = colors.get(style, (200, 200, 200))
        
        # 绘制简单的面部
        center = (width // 2, height // 2)
        cv2.circle(image, center, 150, color, -1)  # 脸
        
        # 眼睛
        eye_y = center[1] - 30
        left_eye = (center[0] - 60, eye_y)
        right_eye = (center[0] + 60, eye_y)
        cv2.circle(image, left_eye, 20, (255, 255, 255), -1)
        cv2.circle(image, right_eye, 20, (255, 255, 255), -1)
        cv2.circle(image, left_eye, 10, (0, 0, 0), -1)
        cv2.circle(image, right_eye, 10, (0, 0, 0), -1)
        
        # 嘴巴
        mouth_y = center[1] + 50
        cv2.ellipse(image, (center[0], mouth_y), (60, 30), 0, 0, 180, (0, 0, 0), 5)
        
        # 保存图像
        cv2.imwrite(output_path, image)
        logger.info(f"生成了占位图像: {output_path}")
    
    def render_avatar_video(self, 
                           expressions: List[Dict[str, float]] = None,
                           phonemes: List[Dict] = None, 
                           duration: float = 3.0, 
                           fps: int = 30,
                           output_path: Optional[str] = None,
                           use_permanent_storage: bool = None) -> str:
        """
        生成数字人视频 (非AI方式，使用简单图形)
        
        Args:
            expressions: 表情参数列表，如果为None但phonemes有值则使用音素数据
            phonemes: 音素数据，用于唇形同步
            duration: 视频时长（秒）
            fps: 视频帧率
            output_path: 输出文件路径，如果为None则生成临时文件
            use_permanent_storage: 是否使用永久存储，覆盖全局设置
            
        Returns:
            output_path: 生成的视频文件路径
        """
        try:
            # 确定存储位置
            use_permanent = use_permanent_storage if use_permanent_storage is not None else self.permanent_storage_enabled
            
            # 生成文件名
            if output_path is None:
                video_id = str(uuid.uuid4())
                filename = f"avatar_{video_id}.mp4"
                output_path = self._get_storage_path(filename, temp=not use_permanent)
            
            logger.info(f"视频将存储在: {output_path} ({'永久存储' if use_permanent else '临时目录'})")
            
            # 设置编码器
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')  # 使用mp4v编码
            
            # 初始化视频写入器
            video_writer = cv2.VideoWriter(output_path, fourcc, fps, (640, 480))
            
            # 根据音素生成帧序列
            if phonemes:
                logger.info(f"基于音素数据生成视频，共{len(phonemes)}个音素")
                frames = self.generate_frames_for_phonemes(phonemes, fps)
            elif expressions:
                # 基于表情参数生成帧
                logger.info(f"基于表情参数生成视频，共{len(expressions)}帧")
                frames = []
                total_frames = len(expressions)
                
                for i in range(total_frames):
                    frame = self.generate_avatar_frame(expressions[i])
                    frames.append(frame)
            else:
                # 使用默认表情生成帧
                logger.info(f"使用默认表情生成视频，时长{duration}秒")
                total_frames = int(duration * fps)
                frames = []
                
                for i in range(total_frames):
                    # 添加一些随机变化使动画更自然
                    variation = {
                        "eyebrow_raise": 0.1 * np.sin(i * 0.1),
                        "eye_open_left": 1.0 if i % 50 > 3 else 0.1,  # 眨眼
                        "eye_open_right": 1.0 if i % 50 > 3 else 0.1,
                        "mouth_open": 0.2 * np.sin(i * 0.2),  # 轻微嘴巴动作
                        "head_rotation_y": 0.1 * np.sin(i * 0.05),  # 轻微头部转动
                    }
                    frame = self.generate_avatar_frame(variation)
                    frames.append(frame)
            
            # 写入视频帧
            for frame in frames:
                video_writer.write(frame)
            
            video_writer.release()
            logger.info(f"数字人视频已生成: {output_path}")
            
            return output_path
        except Exception as e:
            logger.error(f"生成数字人视频时出错: {e}")
            return ""

    def generate_preview_image(self, output_path: Optional[str] = None, background_color: Optional[str] = None) -> str:
        """
        生成模型的预览图像
        
        Args:
            output_path: 输出文件路径，如果为None则生成临时文件
            background_color: 背景颜色，如果为None则使用默认颜色
            
        Returns:
            output_path: 生成的图像文件路径
        """
        try:
            # 生成临时文件名
            if output_path is None:
                temp_dir = tempfile.gettempdir()
                output_path = os.path.join(temp_dir, f"preview_{uuid.uuid4()}.jpg")
            
            # 设置背景颜色
            if background_color is None:
                background_color = (240, 240, 240)  # 浅灰色
            else:
                # 将十六进制颜色转换为RGB
                if isinstance(background_color, str) and background_color.startswith('#'):
                    r = int(background_color[1:3], 16)
                    g = int(background_color[3:5], 16)
                    b = int(background_color[5:7], 16)
                    background_color = (b, g, r)  # OpenCV使用BGR顺序
            
            # 创建图像（宽度x高度，使用合适的显示比例）
            width, height = 640, 480
            image = np.ones((height, width, 3), dtype=np.uint8)
            
            # 设置背景颜色
            image[:] = background_color
            
            # 根据当前模型绘制预览
            if self.current_model == "default":
                color = (0, 120, 255)  # 橙色
                shape_type = "sphere"
            elif self.current_model == "business":
                color = (150, 150, 150)  # 灰色
                shape_type = "cube"
            elif self.current_model == "cartoon":
                color = (50, 200, 255)  # 黄色
                shape_type = "cylinder"
            elif self.current_model == "realistic":
                color = (70, 100, 160)  # 棕色
                shape_type = "cone"
            else:
                color = (0, 255, 0)  # 绿色
                shape_type = "sphere"
            
            # 绘制形状
            center = (width // 2, height // 2)
            
            if shape_type == "sphere":
                cv2.circle(image, center, 150, color, -1)
                # 添加高光效果
                cv2.circle(image, (center[0] - 50, center[1] - 50), 30, (255, 255, 255), -1)
            elif shape_type == "cube":
                # 绘制立方体
                size = 200
                half = size // 2
                pts = np.array([
                    [center[0] - half, center[1] - half],
                    [center[0] + half, center[1] - half],
                    [center[0] + half, center[1] + half],
                    [center[0] - half, center[1] + half]
                ], np.int32)
                pts = pts.reshape((-1, 1, 2))
                cv2.fillPoly(image, [pts], color)
                
                # 添加3D效果
                offset = 40
                pts2 = np.array([
                    [center[0] - half + offset, center[1] - half - offset],
                    [center[0] + half + offset, center[1] - half - offset],
                    [center[0] + half, center[1] - half]
                ], np.int32)
                pts2 = pts2.reshape((-1, 1, 2))
                cv2.fillPoly(image, [pts2], (color[0]+30, color[1]+30, color[2]+30))
            elif shape_type == "cylinder":
                # 绘制圆柱体
                cv2.ellipse(image, center, (100, 50), 0, 0, 360, color, -1)
                cv2.rectangle(image, (center[0] - 100, center[1]), (center[0] + 100, center[1] + 150), color, -1)
                cv2.ellipse(image, (center[0], center[1] + 150), (100, 50), 0, 0, 180, color, -1)
            elif shape_type == "cone":
                # 绘制圆锥
                pt1 = (center[0], center[1] - 150)
                pt2 = (center[0] - 120, center[1] + 100)
                pt3 = (center[0] + 120, center[1] + 100)
                triangle_cnt = np.array([pt1, pt2, pt3])
                cv2.drawContours(image, [triangle_cnt], 0, color, -1)
                cv2.ellipse(image, (center[0], center[1] + 100), (120, 50), 0, 0, 180, color, -1)
            
            # 添加模型名称文本
            font = cv2.FONT_HERSHEY_SIMPLEX
            text = f"{self.current_model.capitalize()} Model"
            text_size = cv2.getTextSize(text, font, 1, 2)[0]
            text_x = (width - text_size[0]) // 2
            text_y = height - 50
            cv2.putText(image, text, (text_x, text_y), font, 1, (0, 0, 0), 2)
            
            # 保存图像
            cv2.imwrite(output_path, image)
            
            logger.info(f"预览图像已生成: {output_path}")
            return output_path
        except Exception as e:
            logger.error(f"生成预览图像失败: {e}")
            return ""

    def get_available_models(self):
        """
        获取可用的模型列表
        
        Returns:
            list: 模型信息列表
        """
        try:
            models = [
                {
                    "id": "default",
                    "name": "默认模型",
                    "type": "基础",
                    "thumbnail_url": "/assets/thumbnails/default.jpg",
                    "description": "基础的头像模型"
                },
                {
                    "id": "business",
                    "name": "商务人士",
                    "type": "职业",
                    "thumbnail_url": "/assets/thumbnails/business.jpg",
                    "description": "适合商务场景的头像模型"
                },
                {
                    "id": "cartoon",
                    "name": "卡通人物",
                    "type": "卡通",
                    "thumbnail_url": "/assets/thumbnails/cartoon.jpg",
                    "description": "可爱的卡通风格头像模型"
                },
                {
                    "id": "realistic",
                    "name": "写实人物",
                    "type": "写实",
                    "thumbnail_url": "/assets/thumbnails/realistic.jpg",
                    "description": "高度写实的头像模型"
                }
            ]
            
            # 检查模型文件是否存在
            for model in models:
                model_path = self.model_paths.get(model["id"])
                if model_path and os.path.exists(model_path):
                    model["available"] = True
                else:
                    model["available"] = False
            
            return models
        except Exception as e:
            logger.error(f"获取模型列表失败: {e}")
            return []

    async def detect_face(self, image_path: str) -> Dict[str, Any]:
        """
        检测图像中的人脸
        
        Args:
            image_path: 图像文件路径
            
        Returns:
            Dict: 包含检测结果的字典，格式为:
            {
                "success": bool,  # 是否成功检测到人脸
                "message": str,   # 详细信息或错误信息
                "face_found": bool, # 是否找到人脸
                "face_ratio": float, # 人脸占图像的比例
                "face_box": List[int], # 人脸边界框 [x, y, width, height]
                "quality_score": float # 人脸质量分数
            }
        """
        try:
            # 检查文件是否存在
            if not os.path.exists(image_path):
                return {
                    "success": False,
                    "message": f"文件不存在: {image_path}",
                    "face_found": False
                }
                
            # 读取图像
            image = cv2.imread(image_path)
            if image is None:
                return {
                    "success": False,
                    "message": "无法读取图像文件",
                    "face_found": False
                }
                
            # 转换为RGB格式
            rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            
            # 获取图像尺寸
            height, width, _ = image.shape
            image_area = height * width
            
            # 使用MediaPipe检测人脸
            with mp_face_mesh.FaceMesh(
                static_image_mode=True,
                max_num_faces=1,
                refine_landmarks=True,
                min_detection_confidence=0.5) as face_mesh:
                
                results = face_mesh.process(rgb_image)
                
                # 检查是否检测到人脸
                if not results.multi_face_landmarks:
                    return {
                        "success": True,
                        "message": "未检测到人脸",
                        "face_found": False,
                        "quality_score": 0.0
                    }
                
                # 获取第一个人脸的特征点
                face_landmarks = results.multi_face_landmarks[0]
                
                # 计算人脸边界框
                x_coordinates = [landmark.x * width for landmark in face_landmarks.landmark]
                y_coordinates = [landmark.y * height for landmark in face_landmarks.landmark]
                
                x_min, x_max = min(x_coordinates), max(x_coordinates)
                y_min, y_max = min(y_coordinates), max(y_coordinates)
                
                face_width = x_max - x_min
                face_height = y_max - y_min
                
                face_box = [int(x_min), int(y_min), int(face_width), int(face_height)]
                face_area = face_width * face_height
                
                # 计算人脸占比
                face_ratio = face_area / image_area
                
                # 计算质量分数 (简化版)
                # 这里可以添加更复杂的质量评估逻辑
                quality_score = 0.0
                
                # 基于人脸大小计算分数
                if face_ratio >= 0.15:  # 理想的人脸占比
                    size_score = 1.0
                elif face_ratio >= 0.05:  # 可接受的人脸占比
                    size_score = 0.7
                else:  # 人脸太小
                    size_score = 0.3
                
                # 基于人脸位置计算分数
                center_x = (x_min + x_max) / 2
                center_y = (y_min + y_max) / 2
                
                # 计算人脸中心到图像中心的距离
                image_center_x = width / 2
                image_center_y = height / 2
                
                # 归一化距离
                distance_x = abs(center_x - image_center_x) / (width / 2)
                distance_y = abs(center_y - image_center_y) / (height / 2)
                distance = (distance_x + distance_y) / 2
                
                # 居中程度分数
                if distance <= 0.2:  # 很居中
                    position_score = 1.0
                elif distance <= 0.4:  # 比较居中
                    position_score = 0.8
                else:  # 不太居中
                    position_score = 0.5
                
                # 总体质量分数 (可以添加更多因素)
                quality_score = (size_score * 0.6 + position_score * 0.4) * 100
                
                return {
                    "success": True,
                    "message": "成功检测到人脸",
                    "face_found": True,
                    "face_ratio": face_ratio,
                    "face_box": face_box,
                    "quality_score": quality_score
                }
                
        except Exception as e:
            logger.error(f"人脸检测失败: {str(e)}")
            return {
                "success": False,
                "message": f"人脸检测过程中发生错误: {str(e)}",
                "face_found": False
            }

    async def create_avatar(
        self,
        digital_human_id: str,
        image_path: str,
        output_dir: str,
        style: Optional[str] = None,
        gender: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        创建数字人头像
        
        Args:
            digital_human_id: 数字人ID
            image_path: 输入图像路径
            output_dir: 输出目录
            style: 头像风格，可选: default, realistic, cartoon, stylized
            gender: 性别，可选: male, female, neutral
            
        Returns:
            Dict: 包含结果的字典，格式为:
            {
                "success": bool,         # 是否成功
                "message": str,          # 详细信息或错误信息
                "thumbnail_path": str,   # 缩略图路径
                "processed_path": str    # 处理后的图像路径
            }
        """
        try:
            logger.info(f"开始创建数字人头像: ID={digital_human_id}, 风格={style}, 性别={gender}")
            
            # 1. 验证输入参数
            if not os.path.exists(image_path):
                return {
                    "success": False,
                    "message": f"输入图像不存在: {image_path}"
                }
            
            # 确保输出目录存在
            os.makedirs(output_dir, exist_ok=True)
            
            # 2. 设置默认风格和性别
            style = style or "default"
            gender = gender or "neutral"
            
            # 3. 检测人脸并获取人脸区域
            face_result = await self.detect_face(image_path)
            if not face_result["success"]:
                return {
                    "success": False,
                    "message": f"人脸检测失败: {face_result.get('message', '未知错误')}"
                }
            
            if not face_result.get("face_found", False):
                return {
                    "success": False,
                    "message": "未在图像中检测到人脸"
                }
            
            # 4. 读取原始图像
            image = cv2.imread(image_path)
            if image is None:
                return {
                    "success": False,
                    "message": "无法读取输入图像"
                }
            
            # 5. 裁剪和处理人脸区域
            processed_image = self._process_face_image(image, face_result, style, gender)
            
            # 6. 保存处理后的图像
            processed_path = os.path.join(output_dir, f"{digital_human_id}_processed.jpg")
            cv2.imwrite(processed_path, processed_image)
            
            # 7. 生成缩略图
            thumbnail_path = os.path.join(output_dir, f"{digital_human_id}_thumbnail.jpg")
            thumbnail_image = self._create_thumbnail(processed_image, size=(256, 256))
            cv2.imwrite(thumbnail_path, thumbnail_image)
            
            # 8. 设置模型类型
            self.initialize_avatar_model(style)
            
            logger.info(f"数字人头像创建成功: ID={digital_human_id}, 路径={processed_path}")
            
            return {
                "success": True,
                "message": "数字人头像创建成功",
                "thumbnail_path": thumbnail_path,
                "processed_path": processed_path
            }
            
        except Exception as e:
            logger.error(f"创建数字人头像失败: {str(e)}")
            import traceback
            logger.error(f"错误详情: {traceback.format_exc()}")
            return {
                "success": False,
                "message": f"创建数字人头像失败: {str(e)}"
            }
    
    def _process_face_image(
        self,
        image: np.ndarray,
        face_result: Dict[str, Any],
        style: str,
        gender: str
    ) -> np.ndarray:
        """
        处理人脸图像，包括裁剪、调整和应用风格
        """
        # 获取人脸边界框
        face_box = face_result.get("face_box", [0, 0, 0, 0])
        x, y, w, h = face_box
        
        # 确保坐标在图像范围内
        h, w_img, _ = image.shape
        x = max(0, x)
        y = max(0, y)
        w = min(w, w_img - x)
        h = min(h, h - y)
        
        # 扩展边界框以包含更多的脸部上下文（扩展因子）
        expansion_factor = 0.5
        x_expanded = max(0, int(x - w * expansion_factor / 2))
        y_expanded = max(0, int(y - h * expansion_factor / 2))
        w_expanded = min(w_img - x_expanded, int(w * (1 + expansion_factor)))
        h_expanded = min(h - y_expanded, int(h * (1 + expansion_factor)))
        
        # 裁剪扩展后的人脸区域
        face_image = image[y_expanded:y_expanded+h_expanded, x_expanded:x_expanded+w_expanded]
        
        # 调整为标准大小（例如512x512）
        standard_size = (512, 512)
        resized_image = cv2.resize(face_image, standard_size)
        
        # 根据风格应用不同的图像处理
        processed_image = resized_image.copy()
        if style == "realistic":
            # 增强真实感效果
            processed_image = self._enhance_realism(processed_image)
        elif style == "cartoon":
            # 应用卡通风格
            processed_image = self._apply_cartoon_effect(processed_image)
        elif style == "stylized":
            # 应用艺术风格
            processed_image = self._apply_stylized_effect(processed_image)
        
        # 根据性别进行调整（可选）
        if gender == "male":
            # 可以添加特定于男性的调整
            processed_image = self._adjust_for_gender(processed_image, "male")
        elif gender == "female":
            # 可以添加特定于女性的调整
            processed_image = self._adjust_for_gender(processed_image, "female")
        
        return processed_image
    
    def _create_thumbnail(self, image: np.ndarray, size: Tuple[int, int] = (256, 256)) -> np.ndarray:
        """生成缩略图"""
        return cv2.resize(image, size)
    
    def _enhance_realism(self, image: np.ndarray) -> np.ndarray:
        """增强图像的真实感效果"""
        # 简单实现：调整对比度和亮度
        alpha = 1.2  # 对比度
        beta = 10    # 亮度
        enhanced = cv2.convertScaleAbs(image, alpha=alpha, beta=beta)
        return enhanced
    
    def _apply_cartoon_effect(self, image: np.ndarray) -> np.ndarray:
        """应用卡通效果"""
        # 简单实现：边缘检测和颜色量化
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        gray = cv2.medianBlur(gray, 5)
        edges = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_MEAN_C, cv2.THRESH_BINARY, 9, 9)
        
        # 颜色量化
        color = cv2.bilateralFilter(image, 9, 300, 300)
        
        # 合并边缘和颜色
        cartoon = cv2.bitwise_and(color, color, mask=edges)
        
        # 为简单起见，在无法应用复杂效果的情况下，混合原始图像和处理后的图像
        result = cv2.addWeighted(image, 0.3, cartoon, 0.7, 0)
        return result
    
    def _apply_stylized_effect(self, image: np.ndarray) -> np.ndarray:
        """应用艺术化风格"""
        # 简单实现：使用滤波器创建油画效果
        stylized = cv2.stylization(image, sigma_s=60, sigma_r=0.07)
        return stylized
    
    def _adjust_for_gender(self, image: np.ndarray, gender: str) -> np.ndarray:
        """根据性别调整图像（可选）"""
        # 这里只是一个示例实现，实际项目中可能需要更复杂的处理
        if gender == "male":
            # 例如增强轮廓
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            edges = cv2.Canny(gray, 100, 200)
            edges_colored = cv2.cvtColor(edges, cv2.COLOR_GRAY2BGR)
            result = cv2.addWeighted(image, 0.9, edges_colored, 0.1, 0)
            return result
        elif gender == "female":
            # 例如柔化处理
            blurred = cv2.GaussianBlur(image, (5, 5), 0)
            result = cv2.addWeighted(image, 0.8, blurred, 0.2, 0)
            return result
        return image

# 单例模式，全局可访问的生成器实例
avatar_generator = AvatarGenerator()

def get_avatar_generator() -> AvatarGenerator:
    """获取数字人生成器单例"""
    return avatar_generator

def initialize() -> bool:
    """初始化数字人生成服务"""
    try:
        avatar_generator.initialize_avatar_model()
        return True
    except Exception as e:
        logger.error(f"初始化数字人生成服务失败: {e}")
        return False 