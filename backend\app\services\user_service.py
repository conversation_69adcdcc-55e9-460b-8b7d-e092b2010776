"""
用户业务服务
"""

import hashlib
import jwt
import bcrypt
from datetime import datetime, timedelta
from ..core.config import settings
from ..core.database import get_db_manager


class UserService:
    """用户服务类"""
    
    def __init__(self):
        self.secret_key = settings.SECRET_KEY
        self.algorithm = "HS256"
        self.access_token_expire_minutes = settings.ACCESS_TOKEN_EXPIRE_MINUTES
        self.refresh_token_expire_days = 30  # refresh token 30天过期
    
    def hash_password(self, password: str) -> str:
        """密码哈希"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """验证密码"""
        try:
            print(f"[Auth] 密码验证详情:")
            print(f"  - 明文密码: '{plain_password}' (长度: {len(plain_password)})")
            print(f"  - 哈希密码: '{hashed_password}' (长度: {len(hashed_password)})")
            print(f"  - 哈希类型: {'bcrypt' if hashed_password.startswith('$2b$') else 'SHA256'}")

            # 检查是否是bcrypt哈希（以$2b$开头）
            if hashed_password.startswith('$2b$'):
                # 使用bcrypt验证
                plain_bytes = plain_password.encode('utf-8')
                hash_bytes = hashed_password.encode('utf-8')
                print(f"  - 明文字节: {plain_bytes}")
                print(f"  - 哈希字节: {hash_bytes[:50]}...")

                result = bcrypt.checkpw(plain_bytes, hash_bytes)
                print(f"  - bcrypt验证结果: {result}")
                return result
            else:
                # 使用SHA256验证（向后兼容）
                sha256_hash = self.hash_password(plain_password)
                result = sha256_hash == hashed_password
                print(f"  - SHA256验证结果: {result}")
                return result
        except Exception as e:
            print(f"[Auth] 密码验证异常: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def create_access_token(self, data: dict):
        """创建访问令牌"""
        to_encode = data.copy()
        expire = datetime.utcnow() + timedelta(minutes=self.access_token_expire_minutes)
        to_encode.update({"exp": expire})
        encoded_jwt = jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
        return encoded_jwt

    def create_refresh_token(self, data: dict):
        """创建刷新令牌"""
        to_encode = data.copy()
        expire = datetime.utcnow() + timedelta(days=self.refresh_token_expire_days)
        to_encode.update({"exp": expire, "type": "refresh"})
        encoded_jwt = jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
        return encoded_jwt
    
    async def create_user(self, username: str, email: str, password: str):
        """创建用户"""
        try:
            db_manager = get_db_manager()
            
            # 哈希密码
            hashed_password = self.hash_password(password)
            
            # 插入用户
            result = db_manager.execute_query(
                """
                INSERT INTO users (username, email, password_hash)
                VALUES (?, ?, ?)
                """,
                (username, email, hashed_password)
            )
            
            if result:
                return {
                    "success": True,
                    "message": "用户创建成功",
                    "user": {
                        "username": username,
                        "email": email
                    }
                }
            else:
                return {"success": False, "error": "用户创建失败"}
                
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def authenticate_user(self, username: str, password: str):
        """用户认证"""
        try:
            print(f"[Auth] 开始认证用户: {username}")
            db_manager = get_db_manager()

            # 查找用户
            query = "SELECT * FROM users WHERE username = ? OR email = ?"
            params = (username, username)
            print(f"[Auth] 执行查询: {query}")
            print(f"[Auth] 查询参数: {params}")

            user = db_manager.execute_query(query, params)
            print(f"[Auth] 查询结果: {user}")

            if not user:
                return {"success": False, "error": "用户不存在"}

            user_data = user[0]
            print(f"[Auth] 用户数据: {user_data}")
            print(f"[Auth] 用户数据类型: {type(user_data)}")
            print(f"[Auth] 用户数据键: {list(user_data.keys()) if hasattr(user_data, 'keys') else 'No keys method'}")
            
            # 验证密码
            print(f"[Auth] 验证密码: 输入密码='{password}', 哈希='{user_data['hashed_password']}'")
            password_valid = self.verify_password(password, user_data["hashed_password"])
            print(f"[Auth] 密码验证结果: {password_valid}")

            if not password_valid:
                return {"success": False, "error": "密码错误"}
            
            # 创建访问令牌和刷新令牌
            token_data = {"sub": user_data["username"], "user_id": user_data["id"]}
            access_token = self.create_access_token(data=token_data)
            refresh_token = self.create_refresh_token(data=token_data)

            return {
                "success": True,
                "access_token": access_token,
                "refresh_token": refresh_token,
                "token_type": "bearer",
                "user": {
                    "id": user_data["id"],
                    "username": user_data["username"],
                    "email": user_data["email"]
                }
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def refresh_access_token(self, refresh_token: str):
        """使用刷新令牌获取新的访问令牌"""
        try:
            # 解码刷新令牌
            payload = jwt.decode(refresh_token, self.secret_key, algorithms=[self.algorithm])

            # 检查是否是刷新令牌
            if payload.get("type") != "refresh":
                return {"success": False, "error": "无效的刷新令牌"}

            username = payload.get("sub")
            user_id = payload.get("user_id")

            if not username or not user_id:
                return {"success": False, "error": "刷新令牌格式错误"}

            # 验证用户是否仍然存在
            db_manager = get_db_manager()
            user = db_manager.execute_query(
                "SELECT * FROM users WHERE id = ? AND username = ?",
                (user_id, username)
            )

            if not user:
                return {"success": False, "error": "用户不存在"}

            user_data = user[0]

            # 创建新的访问令牌和刷新令牌
            token_data = {"sub": user_data["username"], "user_id": user_data["id"]}
            new_access_token = self.create_access_token(data=token_data)
            new_refresh_token = self.create_refresh_token(data=token_data)

            return {
                "success": True,
                "access_token": new_access_token,
                "refresh_token": new_refresh_token,
                "token_type": "bearer",
                "user": {
                    "id": user_data["id"],
                    "username": user_data["username"],
                    "email": user_data["email"]
                }
            }

        except jwt.ExpiredSignatureError:
            return {"success": False, "error": "刷新令牌已过期"}
        except jwt.InvalidTokenError:
            return {"success": False, "error": "无效的刷新令牌"}
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def get_user_by_token(self, token: str):
        """通过token获取用户信息"""
        try:
            # 解码token
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            username = payload.get("sub")
            user_id = payload.get("user_id")
            
            if username is None or user_id is None:
                return None
            
            # 查找用户
            db_manager = get_db_manager()
            user = db_manager.execute_query(
                "SELECT * FROM users WHERE id = ?",
                (user_id,)
            )
            
            if user:
                user_data = user[0]
                return {
                    "id": user_data["id"],
                    "username": user_data["username"],
                    "email": user_data["email"],
                    "created_at": user_data["created_at"]
                }
            
            return None
            
        except jwt.PyJWTError:
            return None
    
    async def update_user(self, user_id: int, user_data: dict):
        """更新用户信息"""
        try:
            db_manager = get_db_manager()
            
            # 构建更新语句
            update_fields = []
            params = []
            
            if "email" in user_data:
                update_fields.append("email = ?")
                params.append(user_data["email"])
            
            if "password" in user_data:
                update_fields.append("password_hash = ?")
                params.append(self.hash_password(user_data["password"]))
            
            if not update_fields:
                return {"success": False, "error": "没有要更新的字段"}
            
            # 添加更新时间和用户ID
            update_fields.append("updated_at = CURRENT_TIMESTAMP")
            params.append(user_id)
            
            # 执行更新
            query = f"UPDATE users SET {', '.join(update_fields)} WHERE id = ?"
            result = db_manager.execute_query(query, tuple(params))
            
            if result > 0:
                return {"success": True, "message": "用户信息更新成功"}
            else:
                return {"success": False, "error": "用户不存在"}
                
        except Exception as e:
            return {"success": False, "error": str(e)}
