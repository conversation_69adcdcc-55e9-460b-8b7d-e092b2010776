#!/usr/bin/env python3
"""
验证修复脚本
此脚本用于验证数据库表结构和服务调用已正确修复
"""
import os
import sys
import logging
import psycopg2
import re
from dotenv import load_dotenv

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def verify_database_column():
    """验证digital_humans表是否包含gender列"""
    try:
        # 加载环境变量
        load_dotenv()
        
        # 获取数据库URL
        database_url = os.getenv("DATABASE_URL", "")
        if not database_url or not database_url.startswith("postgresql://"):
            logger.error("未找到有效的PostgreSQL连接URL")
            return False
            
        # 解析连接URL
        url_parts = database_url.replace("postgresql://", "").split("/")
        if len(url_parts) < 2:
            logger.error("数据库URL格式不正确")
            return False
            
        # 获取主机部分和数据库名
        auth_host = url_parts[0]
        dbname = url_parts[1].split("?")[0]
        
        # 分离用户名密码和主机
        if "@" in auth_host:
            auth, host = auth_host.split("@")
            if ":" in auth:
                username, password = auth.split(":")
            else:
                username = auth
                password = ""
        else:
            host = auth_host
            username = "postgres"
            password = ""
            
        # 分离端口
        if ":" in host:
            hostname, port = host.split(":")
        else:
            hostname = host
            port = 5432
            
        logger.info(f"连接到数据库: {dbname}")
        
        # 连接到数据库
        conn = psycopg2.connect(
            host=hostname,
            port=int(port),
            user=username,
            password=password,
            dbname=dbname
        )
        
        try:
            with conn.cursor() as cursor:
                # 检查digital_humans表是否存在
                cursor.execute("SELECT EXISTS(SELECT 1 FROM information_schema.tables WHERE table_name = 'digital_humans')")
                if not cursor.fetchone()[0]:
                    logger.error("digital_humans表不存在")
                    return False
                    
                # 检查gender列是否存在
                cursor.execute("""
                    SELECT EXISTS(
                        SELECT 1 
                        FROM information_schema.columns 
                        WHERE table_name = 'digital_humans' AND column_name = 'gender'
                    )
                """)
                if cursor.fetchone()[0]:
                    logger.info("✅ 验证成功: gender列存在于digital_humans表中")
                    return True
                else:
                    logger.error("❌ 验证失败: gender列不存在于digital_humans表中")
                    return False
        finally:
            conn.close()
            
    except psycopg2.Error as e:
        logger.error(f"PostgreSQL错误: {e}", exc_info=True)
        return False
    except Exception as e:
        logger.error(f"验证数据库列时出错: {e}", exc_info=True)
        return False

def verify_service_call():
    """验证服务调用是否已修复"""
    try:
        # 定义文件路径
        file_path = os.path.join('backend', 'api', 'digital_human_fixed.py')
        
        if not os.path.exists(file_path):
            logger.error(f"文件不存在: {file_path}")
            return False
            
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
            
        # 检查文件中的generate_digital_human调用
        task_id_pattern = re.compile(r'await\s+service\.generate_digital_human\s*\(\s*task_id\s*=')
        digital_human_id_pattern = re.compile(r'await\s+service\.generate_digital_human\s*\(\s*digital_human_id\s*=')
        
        # 检查是否存在错误的调用
        if task_id_pattern.search(content):
            logger.error("❌ 验证失败: 仍然使用task_id参数调用generate_digital_human")
            return False
            
        # 检查是否存在正确的调用
        if digital_human_id_pattern.search(content):
            logger.info("✅ 验证成功: 正确使用digital_human_id参数调用generate_digital_human")
            return True
        else:
            logger.error("❌ 验证失败: 未找到使用digital_human_id参数的调用")
            return False
            
    except Exception as e:
        logger.error(f"验证服务调用时出错: {e}", exc_info=True)
        return False

def main():
    """主函数"""
    print("===== 验证修复 =====")
    
    # 验证数据库列
    db_result = verify_database_column()
    if db_result:
        print("\n✅ 数据库列验证成功")
    else:
        print("\n❌ 数据库列验证失败")
        
    # 验证服务调用
    service_result = verify_service_call()
    if service_result:
        print("\n✅ 服务调用验证成功")
    else:
        print("\n❌ 服务调用验证失败")
        
    # 总结
    if db_result and service_result:
        print("\n✅ 所有修复都已成功验证")
        return 0
    else:
        print("\n❌ 部分修复验证失败，请查看日志")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 