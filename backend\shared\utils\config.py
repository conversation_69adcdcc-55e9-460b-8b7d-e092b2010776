"""
配置工具模块
提供获取配置的函数
"""

import os
from dotenv import load_dotenv
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 加载环境变量
try:
    # 查找并加载.env文件
    current_dir = os.path.dirname(os.path.abspath(__file__))
    backend_dir = os.path.dirname(current_dir)
    repo_root = os.path.dirname(backend_dir)
    
    env_files = [
        os.path.join(repo_root, '.env'),  # 项目根目录
        os.path.join(backend_dir, '.env'),  # 后端目录
        '.env'  # 当前目录
    ]
    
    env_loaded = False
    for env_file in env_files:
        if os.path.exists(env_file):
            logger.info(f"加载环境变量文件: {env_file}")
            load_dotenv(env_file)
            env_loaded = True
            break
            
    if not env_loaded:
        logger.warning("未找到.env文件，将使用系统环境变量")
except Exception as e:
    logger.error(f"加载环境变量时出错: {e}")

def get_database_url():
    """
    获取数据库URL
    """
    database_url = os.getenv("DATABASE_URL")
    if not database_url:
        logger.error("未找到DATABASE_URL环境变量")
        raise ValueError("DATABASE_URL环境变量未设置")
    
    return database_url

def get_config(key, default=None):
    """
    获取配置值
    
    参数:
        key: 配置键
        default: 默认值
        
    返回:
        配置值或默认值
    """
    return os.getenv(key, default)

def get_int_config(key, default=0):
    """
    获取整数配置值
    
    参数:
        key: 配置键
        default: 默认值
        
    返回:
        整数配置值或默认值
    """
    value = os.getenv(key)
    if value is None:
        return default
    try:
        return int(value)
    except ValueError:
        logger.warning(f"配置项 {key} 的值 '{value}' 不是有效的整数，使用默认值 {default}")
        return default

def get_bool_config(key, default=False):
    """
    获取布尔配置值
    
    参数:
        key: 配置键
        default: 默认值
        
    返回:
        布尔配置值或默认值
    """
    value = os.getenv(key)
    if value is None:
        return default
    return value.lower() in ("yes", "true", "t", "1", "on") 

class Settings:
    """
    配置设置类，用于获取和管理配置
    """
    def __init__(self):
        """初始化配置设置"""
        # 设置默认值
        self._config = {
            # 常用配置项
            "DEBUG": get_bool_config("DEBUG", False),
            "ENVIRONMENT": get_config("ENVIRONMENT", "development"),
            "TEMP_DIR": get_config("TEMP_DIR", os.path.join(backend_dir, "temp")),
            "UPLOAD_DIR": get_config("UPLOAD_DIR", os.path.join(backend_dir, "uploads")),
            "LOG_LEVEL": get_config("LOG_LEVEL", "INFO"),
            "SECRET_KEY": get_config("SECRET_KEY", ""),
            "DATABASE_URL": get_database_url(),
            
            # 文档翻译相关配置
            "DOCUMENT_TRANSLATION_TEMP_DIR": get_config(
                "DOCUMENT_TRANSLATION_TEMP_DIR", 
                os.path.join(backend_dir, "temp", "documents")
            ),
            "DOCUMENT_PREVIEW_DIR": get_config(
                "DOCUMENT_PREVIEW_DIR",
                os.path.join(backend_dir, "temp", "previews")
            ),
            
            # API相关配置
            "API_PREFIX": get_config("API_PREFIX", "/api"),
            "CORS_ORIGINS": get_config("CORS_ORIGINS", "*").split(","),
            
            # 任务管理相关配置
            "TASK_LOG_DIR": get_config("TASK_LOG_DIR", os.path.join(backend_dir, "logs", "tasks")),
            "MAX_TASK_HISTORY": get_int_config("MAX_TASK_HISTORY", 100),
            "TASK_TIMEOUT_SECONDS": get_int_config("TASK_TIMEOUT_SECONDS", 3600),
        }
        
    def __getattr__(self, name):
        """获取配置值"""
        # 优先从环境变量获取
        env_value = os.getenv(name)
        if env_value is not None:
            return env_value
            
        # 如果环境变量中不存在，从预设配置中获取
        if name in self._config:
            return self._config[name]
            
        # 配置不存在时返回None
        logger.warning(f"尝试访问不存在的配置项: {name}")
        return None
        
    def __getitem__(self, key):
        """支持字典方式访问配置"""
        return self.__getattr__(key)
        
    def get(self, key, default=None):
        """获取配置，如果不存在则返回默认值"""
        value = self.__getattr__(key)
        return value if value is not None else default
        
    def __contains__(self, key):
        """检查配置是否存在"""
        return key in self._config or os.getenv(key) is not None

# 创建全局配置实例
settings = Settings() 