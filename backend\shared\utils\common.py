import re
import uuid
import string
import random
import datetime
from typing import Optional, Dict, Any, List, Union

def generate_unique_id(prefix: str = "task") -> str:
    """生成唯一ID"""
    random_str = ''.join(random.choices(string.ascii_lowercase + string.digits, k=8))
    timestamp = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
    return f"{prefix}_{timestamp}_{random_str}"

def camel_to_snake(name: str) -> str:
    """驼峰命名转换为下划线命名"""
    s1 = re.sub('(.)([A-Z][a-z]+)', r'\1_\2', name)
    return re.sub('([a-z0-9])([A-Z])', r'\1_\2', s1).lower()

def snake_to_camel(name: str) -> str:
    """下划线命名转换为驼峰命名"""
    components = name.split('_')
    return components[0] + ''.join(x.title() for x in components[1:])

def clean_dict(d: Dict[str, Any]) -> Dict[str, Any]:
    """清理字典，移除None值"""
    return {k: v for k, v in d.items() if v is not None}

def extract_error_message(error: Exception) -> str:
    """从异常中提取错误信息"""
    if hasattr(error, 'detail'):
        return str(error.detail)
    return str(error)

def truncate_text(text: str, max_length: int = 100, ellipsis: str = "...") -> str:
    """截断文本，超过最大长度时添加省略号"""
    if len(text) <= max_length:
        return text
    return text[:max_length-len(ellipsis)] + ellipsis

def format_datetime(dt: datetime.datetime, format_str: str = "%Y-%m-%d %H:%M:%S") -> str:
    """格式化日期时间"""
    return dt.strftime(format_str)

def parse_datetime(dt_str: str, format_str: str = "%Y-%m-%d %H:%M:%S") -> datetime.datetime:
    """解析日期时间字符串"""
    return datetime.datetime.strptime(dt_str, format_str)

def is_valid_email(email: str) -> bool:
    """验证邮箱格式是否有效"""
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email))

def is_valid_phone(phone: str) -> bool:
    """验证手机号格式是否有效（中国手机号）"""
    pattern = r'^1[3-9]\d{9}$'
    return bool(re.match(pattern, phone))

def safe_get(obj: Dict[str, Any], keys: List[str], default: Any = None) -> Any:
    """安全获取嵌套字典中的值"""
    current = obj
    for key in keys:
        if isinstance(current, dict) and key in current:
            current = current[key]
        else:
            return default
    return current 