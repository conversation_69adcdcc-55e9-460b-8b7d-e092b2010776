"""
初始化存储目录结构
确保系统目录结构符合规范
"""
import os
import logging
import sys
from typing import List, Dict, Any

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 存储根目录
STORAGE_ROOT = os.path.join("backend", "storage")

# 定义目录结构
DIRECTORY_STRUCTURE = {
    # 上传目录
    "uploads": [
        "audio",
        "video",
        "image",
        "documents"
    ],
    
    # 结果目录
    "results": [
        "audio",
        "video",
        "transcriptions",
        "translations"
    ],
    
    # 临时目录
    "temp": []
}

# 模型目录
MODEL_ROOT = os.path.join("backend", "models")

# 定义模型目录结构
MODEL_STRUCTURE = [
    "tts",
    "wan",
    "translation",
    "transcription"
]

def create_directory_structure(base_dir: str, structure: Dict[str, List[str]]) -> None:
    """
    创建目录结构
    
    Args:
        base_dir: 基础目录
        structure: 目录结构定义
    """
    # 确保基础目录存在
    os.makedirs(base_dir, exist_ok=True)
    logger.info(f"确保基础目录存在: {base_dir}")
    
    # 创建子目录
    for dir_name, subdirs in structure.items():
        # 创建一级目录
        dir_path = os.path.join(base_dir, dir_name)
        os.makedirs(dir_path, exist_ok=True)
        logger.info(f"创建目录: {dir_path}")
        
        # 创建二级目录
        for subdir in subdirs:
            subdir_path = os.path.join(dir_path, subdir)
            os.makedirs(subdir_path, exist_ok=True)
            logger.info(f"创建子目录: {subdir_path}")

def create_model_directories(base_dir: str, subdirs: List[str]) -> None:
    """
    创建模型目录
    
    Args:
        base_dir: 基础目录
        subdirs: 模型子目录列表
    """
    # 确保基础目录存在
    os.makedirs(base_dir, exist_ok=True)
    logger.info(f"确保模型基础目录存在: {base_dir}")
    
    # 创建子目录
    for subdir in subdirs:
        subdir_path = os.path.join(base_dir, subdir)
        os.makedirs(subdir_path, exist_ok=True)
        logger.info(f"创建模型子目录: {subdir_path}")

def verify_permissions(dir_path: str) -> bool:
    """
    验证目录权限
    
    Args:
        dir_path: 目录路径
        
    Returns:
        是否具有读写权限
    """
    try:
        # 创建测试文件
        test_file_path = os.path.join(dir_path, ".permission_test")
        with open(test_file_path, 'w') as f:
            f.write("test")
        
        # 读取测试文件
        with open(test_file_path, 'r') as f:
            content = f.read()
            
        # 删除测试文件
        os.remove(test_file_path)
        
        return content == "test"
    except Exception as e:
        logger.error(f"权限验证失败: {e}")
        return False

def initialize_storage():
    """
    初始化存储目录
    """
    logger.info("开始初始化存储目录...")
    
    # 创建存储目录结构
    create_directory_structure(STORAGE_ROOT, DIRECTORY_STRUCTURE)
    
    # 创建模型目录结构
    create_model_directories(MODEL_ROOT, MODEL_STRUCTURE)
    
    # 验证权限
    if not verify_permissions(STORAGE_ROOT):
        logger.warning(f"存储目录权限不足: {STORAGE_ROOT}")
    
    if not verify_permissions(MODEL_ROOT):
        logger.warning(f"模型目录权限不足: {MODEL_ROOT}")
    
    logger.info("存储目录初始化完成")

if __name__ == "__main__":
    # 设置执行目录为项目根目录
    if os.path.basename(os.getcwd()) != "backend":
        try:
            if os.path.exists("backend"):
                os.chdir("backend")
                logger.info("已切换到backend目录")
            else:
                logger.warning("未找到backend目录，使用当前目录")
        except Exception as e:
            logger.error(f"切换目录失败: {e}")
    
    # 初始化存储
    initialize_storage()
    
    # 返回成功状态码
    sys.exit(0) 