#!/usr/bin/env python3
"""
检查和修复文本翻译历史表的脚本
"""

import os
import sys
import asyncio
import logging
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from datetime import datetime

# 添加父目录到路径，以便导入模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.config import get_database_url

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 创建数据库引擎
try:
    engine = create_engine(get_database_url())
    logger.info("数据库引擎创建成功")
except Exception as e:
    logger.error(f"创建数据库引擎失败: {e}")
    sys.exit(1)

# 创建会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

async def check_and_fix_table():
    """检查和修复文本翻译历史表"""
    try:
        # 创建会话
        session = SessionLocal()
        
        try:
            # 检查表是否存在
            result = session.execute(text("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'public'
                    AND table_name = 'text_translation_history'
                )
            """))
            table_exists = result.scalar()
            
            if not table_exists:
                logger.warning("文本翻译历史表不存在，正在创建...")
                # 创建表
                session.execute(text("""
                    CREATE TABLE IF NOT EXISTS text_translation_history (
                        id VARCHAR(255) PRIMARY KEY,
                        user_id INTEGER NOT NULL,
                        original_text TEXT NOT NULL,
                        translated_text TEXT NOT NULL,
                        source_lang VARCHAR(10) NOT NULL,
                        target_lang VARCHAR(10) NOT NULL,
                        domain VARCHAR(50) DEFAULT 'general',
                        style VARCHAR(50) DEFAULT 'normal',
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
                    );
                    
                    -- 创建索引以加速查询
                    CREATE INDEX IF NOT EXISTS idx_text_translation_user_id ON text_translation_history(user_id);
                    CREATE INDEX IF NOT EXISTS idx_text_translation_created_at ON text_translation_history(created_at);
                """))
                session.commit()
                logger.info("文本翻译历史表创建成功")
            else:
                logger.info("文本翻译历史表已存在")
                
                # 检查表结构
                result = session.execute(text("""
                    SELECT column_name, data_type 
                    FROM information_schema.columns 
                    WHERE table_schema = 'public'
                    AND table_name = 'text_translation_history'
                """))
                columns = {row[0]: row[1] for row in result}
                logger.info(f"表结构: {columns}")
                
                # 检查是否有必要的列
                required_columns = {
                    'id': 'character varying',
                    'user_id': 'integer',
                    'original_text': 'text',
                    'translated_text': 'text',
                    'source_lang': 'character varying',
                    'target_lang': 'character varying',
                    'created_at': 'timestamp without time zone'
                }
                
                missing_columns = []
                for col_name, col_type in required_columns.items():
                    if col_name not in columns:
                        missing_columns.append((col_name, col_type))
                    elif columns[col_name] != col_type and not (columns[col_name] == 'timestamp with time zone' and col_type == 'timestamp without time zone'):
                        logger.warning(f"列 {col_name} 的类型不匹配: 期望 {col_type}，实际 {columns[col_name]}")
                
                if missing_columns:
                    logger.warning(f"缺少列: {missing_columns}")
                    # 添加缺失的列
                    for col_name, col_type in missing_columns:
                        logger.info(f"添加列 {col_name}")
                        session.execute(text(f"""
                            ALTER TABLE text_translation_history
                            ADD COLUMN IF NOT EXISTS {col_name} {col_type}
                        """))
                    session.commit()
                    logger.info("已添加缺失的列")
            
            # 检查记录数
            result = session.execute(text("SELECT COUNT(*) FROM text_translation_history"))
            record_count = result.scalar()
            logger.info(f"表中有 {record_count} 条记录")
            
            # 测试插入记录
            test_id = f"test_{datetime.now().strftime('%Y%m%d%H%M%S')}"
            try:
                session.execute(text("""
                    INSERT INTO text_translation_history
                    (id, user_id, original_text, translated_text, source_lang, target_lang, domain, style, created_at)
                    VALUES (:id, :user_id, :original_text, :translated_text, :source_lang, :target_lang, :domain, :style, :created_at)
                """), {
                    "id": test_id, 
                    "user_id": 1,  # 假设ID为1的用户存在
                    "original_text": "测试文本", 
                    "translated_text": "Test text", 
                    "source_lang": "zh", 
                    "target_lang": "en", 
                    "domain": "general", 
                    "style": "normal", 
                    "created_at": datetime.now()
                })
                session.commit()
                logger.info(f"测试记录插入成功，ID: {test_id}")
                
                # 删除测试记录
                session.execute(text("DELETE FROM text_translation_history WHERE id = :id"), {"id": test_id})
                session.commit()
                logger.info("测试记录删除成功")
            except Exception as e:
                session.rollback()
                logger.error(f"测试记录操作失败: {e}")
                
                # 检查外键约束
                result = session.execute(text("""
                    SELECT conname, conrelid::regclass, confrelid::regclass
                    FROM pg_constraint
                    WHERE contype = 'f'
                    AND conrelid = 'text_translation_history'::regclass
                """))
                foreign_keys = list(result)
                logger.info(f"外键约束: {foreign_keys}")
                
                # 检查users表是否存在
                result = session.execute(text("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_schema = 'public'
                        AND table_name = 'users'
                    )
                """))
                users_table_exists = result.scalar()
                logger.info(f"users表是否存在: {users_table_exists}")
                
                if users_table_exists:
                    # 检查ID为1的用户是否存在
                    result = session.execute(text("SELECT EXISTS (SELECT 1 FROM users WHERE id = 1)"))
                    user_exists = result.scalar()
                    logger.info(f"ID为1的用户是否存在: {user_exists}")
                    
                    if not user_exists:
                        logger.warning("ID为1的用户不存在，尝试查找其他用户")
                        result = session.execute(text("SELECT id FROM users LIMIT 1"))
                        first_user = result.scalar()
                        if first_user:
                            logger.info(f"找到用户ID: {first_user}")
                            # 使用找到的用户ID重试
                            session.execute(text("""
                                INSERT INTO text_translation_history
                                (id, user_id, original_text, translated_text, source_lang, target_lang, domain, style, created_at)
                                VALUES (:id, :user_id, :original_text, :translated_text, :source_lang, :target_lang, :domain, :style, :created_at)
                            """), {
                                "id": test_id, 
                                "user_id": first_user,
                                "original_text": "测试文本", 
                                "translated_text": "Test text", 
                                "source_lang": "zh", 
                                "target_lang": "en", 
                                "domain": "general", 
                                "style": "normal", 
                                "created_at": datetime.now()
                            })
                            session.commit()
                            logger.info(f"使用用户ID {first_user} 测试记录插入成功")
                            
                            # 删除测试记录
                            session.execute(text("DELETE FROM text_translation_history WHERE id = :id"), {"id": test_id})
                            session.commit()
                            logger.info("测试记录删除成功")
            
            logger.info("检查和修复完成")
            
        except Exception as e:
            session.rollback()
            logger.error(f"操作失败: {e}")
            raise
        finally:
            session.close()
            
    except Exception as e:
        logger.error(f"检查和修复过程中出错: {e}")
        raise

if __name__ == "__main__":
    try:
        asyncio.run(check_and_fix_table())
    except KeyboardInterrupt:
        logger.info("操作被用户中断")
    except Exception as e:
        logger.error(f"脚本执行失败: {e}")
        sys.exit(1) 