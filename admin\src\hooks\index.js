// 导出所有钩子函数

// 请求相关
import useRequest from './useRequest';

// 表单相关
import useForm from './useForm';

// UI相关
import useModal from './useModal';

// 文件上传
import useUpload from './useUpload';

// 剪贴板
import useClipboard from './useClipboard';

// 倒计时
import useCountdown from './useCountdown';

// 函数优化
import useThrottleFn from './useThrottleFn';
import useDebounceFn from './useDebounceFn';

// 事件监听
import useEventListener from './useEventListener';

// 存储相关
import useLocalStorage from './useLocalStorage';

// 表格相关
import useTable from './useTable';

export {
  // 请求相关
  useRequest,
  
  // 表单相关
  useForm,
  
  // UI相关
  useModal,
  
  // 文件上传
  useUpload,
  
  // 剪贴板
  useClipboard,
  
  // 倒计时
  useCountdown,
  
  // 函数优化
  useThrottleFn,
  useDebounceFn,
  
  // 事件监听
  useEventListener,
  
  // 存储相关
  useLocalStorage,
  
  // 表格相关
  useTable
};

// 默认导出所有hooks
export default {
  useRequest,
  useForm,
  useModal,
  useUpload,
  useClipboard,
  useCountdown,
  useThrottleFn,
  useDebounceFn,
  useEventListener,
  useLocalStorage,
  useTable
}; 