#!/usr/bin/env python3
"""
检查用户密码的脚本
"""

import sys
import os
import bcrypt

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from dotenv import load_dotenv
load_dotenv()

from app.core.database import db_manager

def check_user_password():
    """检查用户密码"""
    
    # 查询用户
    user = db_manager.execute_query(
        "SELECT * FROM users WHERE username = %s",
        ("test",)
    )
    
    if not user:
        print("❌ 用户不存在")
        return
    
    user_data = user[0]
    print(f"✅ 找到用户: {user_data['username']}")
    print(f"📧 邮箱: {user_data['email']}")
    print(f"🔐 密码哈希: {user_data['hashed_password']}")
    
    # 测试不同的密码
    test_passwords = ['test123', 'test', 'password', '123456', 'admin']
    
    for password in test_passwords:
        try:
            # 使用bcrypt验证
            is_valid = bcrypt.checkpw(
                password.encode('utf-8'), 
                user_data['hashed_password'].encode('utf-8')
            )
            
            status = "✅ 正确" if is_valid else "❌ 错误"
            print(f"密码 '{password}': {status}")
            
            if is_valid:
                print(f"🎉 找到正确密码: {password}")
                break
                
        except Exception as e:
            print(f"密码 '{password}': ❌ 验证出错 - {e}")

if __name__ == "__main__":
    check_user_password()
