"""
Celery 配置 - 内存模式（无需 Redis）
用于开发和测试环境
"""
import os
from celery import Celery

def create_celery_app_memory():
    """创建使用内存存储的 Celery 应用"""
    
    # 使用内存作为 broker 和 backend
    celery_app = Celery(
        'ai_platform_memory',
        broker='memory://',  # 内存 broker
        backend='cache+memory://',  # 内存 backend
        include=[
            'app.tasks.video_tasks',
            'app.tasks.translation_tasks_simple',
            'app.tasks.image_tasks',
            'app.tasks.audio_tasks',
            'app.tasks.digital_human_tasks_simple',
        ]
    )
    
    # 内存模式配置
    celery_app.conf.update(
        # 基础配置
        task_serializer='json',
        accept_content=['json'],
        result_serializer='json',
        timezone='UTC',
        enable_utc=True,
        
        # 任务配置
        task_track_started=True,
        task_time_limit=1800,  # 30分钟
        worker_prefetch_multiplier=1,
        task_acks_late=True,
        
        # 内存模式特定配置
        task_always_eager=False,  # 允许异步执行
        task_eager_propagates=True,
        task_store_eager_result=True,
        
        # Windows 兼容性
        worker_pool='solo' if os.name == 'nt' else 'prefork',
        worker_concurrency=1 if os.name == 'nt' else 2,
        
        # 结果过期（内存模式下较短）
        result_expires=300,  # 5分钟
        
        # 监控
        worker_send_task_events=True,
        task_send_sent_event=True,
        
        # 内存限制
        worker_max_memory_per_child=200000,  # 200MB
    )
    
    return celery_app

# 创建全局实例
celery_app = create_celery_app_memory()

# 简单的任务状态管理器（内存版）
class MemoryTaskManager:
    """内存任务状态管理器"""
    
    def __init__(self):
        self.tasks = {}
    
    def create_task(self, task_id: str, task_type: str, **kwargs):
        """创建任务记录"""
        self.tasks[task_id] = {
            'task_id': task_id,
            'task_type': task_type,
            'status': 'PENDING',
            'progress': 0,
            'message': 'Task created',
            **kwargs
        }
        return task_id
    
    def update_task(self, task_id: str, **kwargs):
        """更新任务状态"""
        if task_id in self.tasks:
            self.tasks[task_id].update(kwargs)
        return self.tasks.get(task_id)
    
    def get_task(self, task_id: str):
        """获取任务信息"""
        return self.tasks.get(task_id)
    
    def get_tasks_by_type(self, task_type: str):
        """按类型获取任务"""
        return [task for task in self.tasks.values() if task.get('task_type') == task_type]
    
    def clear_expired_tasks(self):
        """清理过期任务（内存管理）"""
        import time
        current_time = time.time()
        expired_tasks = []
        
        for task_id, task_info in self.tasks.items():
            created_time = task_info.get('created_at', current_time)
            if current_time - created_time > 3600:  # 1小时过期
                expired_tasks.append(task_id)
        
        for task_id in expired_tasks:
            del self.tasks[task_id]
        
        return len(expired_tasks)

# 全局任务管理器实例
memory_task_manager = MemoryTaskManager()

# 进度更新辅助函数
def update_task_progress_memory(task_id: str, progress: int, message: str = ""):
    """更新任务进度（内存版）"""
    memory_task_manager.update_task(
        task_id=task_id,
        progress=progress,
        message=message
    )
    
    # 同时更新 Celery 状态
    try:
        celery_app.backend.store_result(
            task_id, 
            {'progress': progress, 'message': message}, 
            'PROGRESS'
        )
    except Exception as e:
        print(f"Warning: Could not update Celery backend: {e}")

if __name__ == '__main__':
    print("Starting Celery with memory backend...")
    print("Note: Tasks will not persist between restarts")
    celery_app.start()
