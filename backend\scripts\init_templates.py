#!/usr/bin/env python3
"""
初始化模板数据脚本
"""

import sys
import os
from pathlib import Path

# 设置环境变量确保使用PostgreSQL
os.environ['DATABASE_URL'] = 'postgresql://postgres:langpro8@localhost:5432/ai_platform?client_encoding=utf8'

# 添加项目根目录到Python路径
PROJECT_ROOT = Path(__file__).parent.parent.parent
sys.path.append(str(PROJECT_ROOT))
sys.path.append(str(PROJECT_ROOT / "backend"))

from sqlalchemy.orm import Session
from backend.app.core.sqlalchemy_db import SessionLocal, engine, Base
from backend.app.models.avatar_template import AvatarTemplate
from backend.app.models.voice_template import VoiceTemplate

# 创建所有表
Base.metadata.create_all(bind=engine)

def init_avatar_templates():
    """初始化头像模板数据"""
    db = SessionLocal()
    
    try:
        # 清除现有数据并重新创建
        db.query(AvatarTemplate).delete()
        print("清除现有头像模板数据")
        
        templates = [
            {
                "id": "female-teacher",
                "name": "温和女教师",
                "description": "温和亲切的女性教师形象，适合在线教育、培训讲解",
                "gender": "female",
                "age_range": "28-35",
                "ethnicity": "asian",
                "category": "education",
                "type": "teacher",
                "style": "professional",
                "tags": ["教育", "专业", "亲和", "温和"],
                "hair_color": "black",
                "hair_style": "medium_length",
                "eye_color": "brown",
                "skin_tone": "medium",
                "clothing_style": "business_casual",
                "accessories": ["glasses"],
                "image_url": "https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=400&h=400&fit=crop&crop=face",
                "quality_score": 85.0,
                "match_score": 90.0,
                "features": ["专业形象", "亲和力强", "适合教学"],
                "is_active": True,
                "is_featured": True,
                "sort_order": 1,
                "display_priority": 100,
                "source": "ai_generated"
            },
            {
                "id": "male-teacher",
                "name": "资深男教授",
                "description": "权威专业的男性教授形象，适合学术讲座、专业培训",
                "gender": "male",
                "age_range": "35-45",
                "ethnicity": "asian",
                "category": "education",
                "type": "teacher",
                "style": "authoritative",
                "tags": ["教育", "专业", "权威", "学术"],
                "hair_color": "black",
                "hair_style": "short",
                "eye_color": "brown",
                "skin_tone": "medium",
                "clothing_style": "formal",
                "accessories": ["glasses"],
                "image_url": "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&h=400&fit=crop&crop=face",
                "quality_score": 88.0,
                "match_score": 92.0,
                "features": ["权威感", "专业知识", "学术风范"],
                "is_active": True,
                "is_featured": True,
                "sort_order": 2,
                "display_priority": 95,
                "source": "ai_generated"
            },
            {
                "id": "female-business",
                "name": "职场女性",
                "description": "现代职场女性形象，适合商务演示、企业培训",
                "gender": "female",
                "age_range": "25-32",
                "ethnicity": "asian",
                "category": "business",
                "type": "professional",
                "style": "modern",
                "tags": ["商务", "专业", "现代", "职场"],
                "hair_color": "brown",
                "hair_style": "bob",
                "eye_color": "brown",
                "skin_tone": "medium",
                "clothing_style": "business_formal",
                "accessories": [],
                "image_url": "https://images.unsplash.com/photo-1580489944761-15a19d654956?w=400&h=400&fit=crop&crop=face",
                "quality_score": 87.0,
                "match_score": 89.0,
                "features": ["现代感", "专业形象", "商务气质"],
                "is_active": True,
                "is_featured": True,
                "sort_order": 3,
                "display_priority": 90,
                "source": "ai_generated"
            },
            {
                "id": "male-business",
                "name": "商务精英",
                "description": "专业商务男性形象，适合企业宣传、商务汇报",
                "gender": "male",
                "age_range": "30-40",
                "ethnicity": "asian",
                "category": "business",
                "type": "executive",
                "style": "professional",
                "tags": ["商务", "专业", "精英", "领导"],
                "hair_color": "black",
                "hair_style": "short_formal",
                "eye_color": "brown",
                "skin_tone": "medium",
                "clothing_style": "business_formal",
                "accessories": ["tie"],
                "image_url": "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face",
                "quality_score": 90.0,
                "match_score": 93.0,
                "features": ["领导气质", "专业形象", "商务精英"],
                "is_active": True,
                "is_featured": True,
                "sort_order": 4,
                "display_priority": 85,
                "source": "ai_generated"
            },
            {
                "id": "female-service",
                "name": "亲和客服",
                "description": "友好亲和的客服形象，适合客户服务、产品介绍",
                "gender": "female",
                "age_range": "22-28",
                "ethnicity": "asian",
                "category": "service",
                "type": "customer_service",
                "style": "friendly",
                "tags": ["客服", "友好", "亲和", "服务"],
                "hair_color": "brown",
                "hair_style": "long",
                "eye_color": "brown",
                "skin_tone": "light",
                "clothing_style": "casual_professional",
                "accessories": [],
                "image_url": "https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?w=400&h=400&fit=crop&crop=face",
                "quality_score": 83.0,
                "match_score": 88.0,
                "features": ["亲和力", "服务意识", "友好形象"],
                "is_active": True,
                "is_featured": True,
                "sort_order": 5,
                "display_priority": 80,
                "source": "ai_generated"
            },
            {
                "id": "male-host",
                "name": "专业主持",
                "description": "专业主持人形象，适合新闻播报、活动主持",
                "gender": "male",
                "age_range": "28-35",
                "ethnicity": "asian",
                "category": "media",
                "type": "host",
                "style": "professional",
                "tags": ["主持", "专业", "端庄", "媒体"],
                "hair_color": "black",
                "hair_style": "styled",
                "eye_color": "brown",
                "skin_tone": "medium",
                "clothing_style": "formal",
                "accessories": [],
                "image_url": "https://images.unsplash.com/photo-1560250097-0b93528c311a?w=400&h=400&fit=crop&crop=face",
                "quality_score": 86.0,
                "match_score": 91.0,
                "features": ["专业主持", "端庄形象", "媒体气质"],
                "is_active": True,
                "is_featured": True,
                "sort_order": 6,
                "display_priority": 75,
                "source": "ai_generated"
            },
            {
                "id": "female-tech",
                "name": "科技女神",
                "description": "现代时尚的女性科技工作者，适合科技产品展示",
                "gender": "female",
                "age_range": "25-32",
                "ethnicity": "asian",
                "category": "technology",
                "type": "professional",
                "style": "modern",
                "tags": ["科技", "现代", "时尚", "专业"],
                "hair_color": "brown",
                "hair_style": "long",
                "eye_color": "brown",
                "skin_tone": "light",
                "clothing_style": "smart_casual",
                "accessories": [],
                "image_url": "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=400&h=400&fit=crop&crop=face",
                "quality_score": 89.0,
                "match_score": 94.0,
                "features": ["科技感", "现代气质", "亲和力"],
                "is_active": True,
                "is_featured": True,
                "sort_order": 7,
                "display_priority": 85,
                "source": "ai_generated"
            },
            {
                "id": "male-tech",
                "name": "技术专家",
                "description": "专业的男性技术专家，适合技术讲解和产品演示",
                "gender": "male",
                "age_range": "28-38",
                "ethnicity": "asian",
                "category": "technology",
                "type": "professional",
                "style": "casual_professional",
                "tags": ["技术", "专业", "可靠", "创新"],
                "hair_color": "black",
                "hair_style": "short",
                "eye_color": "brown",
                "skin_tone": "medium",
                "clothing_style": "smart_casual",
                "accessories": [],
                "image_url": "https://images.unsplash.com/photo-1507591064344-4c6ce005b128?w=400&h=400&fit=crop&crop=face",
                "quality_score": 87.0,
                "match_score": 92.0,
                "features": ["技术专业", "创新思维", "可靠形象"],
                "is_active": True,
                "is_featured": True,
                "sort_order": 8,
                "display_priority": 80,
                "source": "ai_generated"
            },
            {
                "id": "female-health",
                "name": "健康顾问",
                "description": "温和专业的女性健康顾问，适合医疗健康咨询",
                "gender": "female",
                "age_range": "30-40",
                "ethnicity": "asian",
                "category": "healthcare",
                "type": "consultant",
                "style": "professional",
                "tags": ["健康", "专业", "温和", "可信"],
                "hair_color": "black",
                "hair_style": "medium_length",
                "eye_color": "brown",
                "skin_tone": "medium",
                "clothing_style": "professional",
                "accessories": [],
                "image_url": "https://images.unsplash.com/photo-**********-2b71ea197ec2?w=400&h=400&fit=crop&crop=face",
                "quality_score": 91.0,
                "match_score": 95.0,
                "features": ["专业可信", "温和亲切", "健康形象"],
                "is_active": True,
                "is_featured": True,
                "sort_order": 9,
                "display_priority": 90,
                "source": "ai_generated"
            },
            {
                "id": "male-finance",
                "name": "金融顾问",
                "description": "成熟稳重的男性金融顾问，适合投资理财咨询",
                "gender": "male",
                "age_range": "35-45",
                "ethnicity": "asian",
                "category": "finance",
                "type": "consultant",
                "style": "authoritative",
                "tags": ["金融", "稳重", "专业", "可靠"],
                "hair_color": "black",
                "hair_style": "short",
                "eye_color": "brown",
                "skin_tone": "medium",
                "clothing_style": "business_formal",
                "accessories": ["tie"],
                "image_url": "https://images.unsplash.com/photo-1560250097-0b93528c311a?w=400&h=400&fit=crop&crop=face",
                "quality_score": 93.0,
                "match_score": 96.0,
                "features": ["权威专业", "稳重可靠", "金融气质"],
                "is_active": True,
                "is_featured": True,
                "sort_order": 10,
                "display_priority": 88,
                "source": "ai_generated"
            }
        ]
        
        for template_data in templates:
            template = AvatarTemplate(**template_data)
            db.add(template)
        
        db.commit()
        print(f"✅ 成功初始化 {len(templates)} 个头像模板")
        
    except Exception as e:
        print(f"❌ 初始化头像模板失败: {e}")
        db.rollback()
    finally:
        db.close()

def init_voice_templates():
    """初始化声音模板数据"""
    db = SessionLocal()
    
    try:
        # 清除现有数据并重新创建
        db.query(VoiceTemplate).delete()
        print("清除现有声音模板数据")
        
        templates = [
            {
                "id": "zh-cn-xiaoxiao",
                "name": "晓晓 - 温和女声",
                "description": "温和亲切的女性声音，适合教育和客服场景",
                "gender": "female",
                "age_range": "25-30",
                "language": "zh-CN",
                "accent": "standard",
                "pitch": "medium",
                "speed": "normal",
                "tone": "warm",
                "emotion": "friendly",
                "voice_engine": "edge-tts",
                "voice_id": "zh-CN-XiaoxiaoNeural",
                "category": "education",
                "scenarios": ["教育", "客服", "讲解"],
                "tags": ["温和", "亲切", "专业"],
                "quality_score": 90.0,
                "naturalness_score": 88.0,
                "clarity_score": 92.0,
                "sample_text": "您好，欢迎来到我们的在线课堂，今天我将为大家讲解相关知识。",
                "is_active": True,
                "is_featured": True,
                "sort_order": 1,
                "display_priority": 100,
                "source": "edge-tts"
            },
            {
                "id": "zh-cn-yunxi",
                "name": "云希 - 专业男声",
                "description": "专业稳重的男性声音，适合商务和新闻播报",
                "gender": "male",
                "age_range": "30-35",
                "language": "zh-CN",
                "accent": "standard",
                "pitch": "low",
                "speed": "normal",
                "tone": "professional",
                "emotion": "serious",
                "voice_engine": "edge-tts",
                "voice_id": "zh-CN-YunxiNeural",
                "category": "business",
                "scenarios": ["商务", "新闻", "汇报"],
                "tags": ["专业", "稳重", "权威"],
                "quality_score": 92.0,
                "naturalness_score": 90.0,
                "clarity_score": 94.0,
                "sample_text": "各位同事大家好，今天我们来讨论本季度的业务发展情况。",
                "is_active": True,
                "is_featured": True,
                "sort_order": 2,
                "display_priority": 95,
                "source": "edge-tts"
            },
            {
                "id": "zh-cn-xiaoyi",
                "name": "小艺 - 活泼女声",
                "description": "活泼开朗的女性声音，适合娱乐和互动场景",
                "gender": "female",
                "age_range": "20-25",
                "language": "zh-CN",
                "accent": "standard",
                "pitch": "high",
                "speed": "fast",
                "tone": "cheerful",
                "emotion": "excited",
                "voice_engine": "edge-tts",
                "voice_id": "zh-CN-XiaoyiNeural",
                "category": "entertainment",
                "scenarios": ["娱乐", "互动", "直播"],
                "tags": ["活泼", "开朗", "有趣"],
                "quality_score": 87.0,
                "naturalness_score": 89.0,
                "clarity_score": 85.0,
                "sample_text": "大家好！欢迎来到我们的直播间，今天有很多精彩内容等着大家！",
                "is_active": True,
                "is_featured": True,
                "sort_order": 3,
                "display_priority": 85,
                "source": "edge-tts"
            },
            {
                "id": "zh-cn-yunyang",
                "name": "云扬 - 磁性男声",
                "description": "低沉磁性的男性声音，适合广告和品牌宣传",
                "gender": "male",
                "age_range": "35-40",
                "language": "zh-CN",
                "accent": "standard",
                "pitch": "low",
                "speed": "slow",
                "tone": "deep",
                "emotion": "confident",
                "voice_engine": "edge-tts",
                "voice_id": "zh-CN-YunyangNeural",
                "category": "marketing",
                "scenarios": ["广告", "品牌", "宣传"],
                "tags": ["磁性", "低沉", "有力"],
                "quality_score": 94.0,
                "naturalness_score": 92.0,
                "clarity_score": 96.0,
                "sample_text": "选择我们，就是选择品质与信赖，让我们一起创造美好未来。",
                "is_active": True,
                "is_featured": True,
                "sort_order": 4,
                "display_priority": 90,
                "source": "edge-tts"
            },
            {
                "id": "zh-cn-xiaomo",
                "name": "小墨 - 知性女声",
                "description": "知性优雅的女性声音，适合文化和艺术内容",
                "gender": "female",
                "age_range": "28-35",
                "language": "zh-CN",
                "accent": "standard",
                "pitch": "medium",
                "speed": "slow",
                "tone": "elegant",
                "emotion": "calm",
                "voice_engine": "edge-tts",
                "voice_id": "zh-CN-XiaomoNeural",
                "category": "culture",
                "scenarios": ["文化", "艺术", "朗读"],
                "tags": ["知性", "优雅", "文艺"],
                "quality_score": 91.0,
                "naturalness_score": 93.0,
                "clarity_score": 89.0,
                "sample_text": "在这个充满诗意的午后，让我们一起品味文字的魅力。",
                "is_active": True,
                "is_featured": True,
                "sort_order": 5,
                "display_priority": 88,
                "source": "edge-tts"
            },
            {
                "id": "zh-cn-yunhao",
                "name": "云浩 - 青春男声",
                "description": "青春活力的男性声音，适合年轻化产品和服务",
                "gender": "male",
                "age_range": "22-28",
                "language": "zh-CN",
                "accent": "standard",
                "pitch": "medium",
                "speed": "normal",
                "tone": "energetic",
                "emotion": "enthusiastic",
                "voice_engine": "edge-tts",
                "voice_id": "zh-CN-YunhaoNeural",
                "category": "youth",
                "scenarios": ["青春", "活力", "科技"],
                "tags": ["青春", "活力", "时尚"],
                "quality_score": 88.0,
                "naturalness_score": 87.0,
                "clarity_score": 90.0,
                "sample_text": "嗨！朋友们，让我们一起探索这个充满可能的数字世界！",
                "is_active": True,
                "is_featured": True,
                "sort_order": 6,
                "display_priority": 82,
                "source": "edge-tts"
            }
        ]
        
        for template_data in templates:
            template = VoiceTemplate(**template_data)
            db.add(template)
        
        db.commit()
        print(f"✅ 成功初始化 {len(templates)} 个声音模板")
        
    except Exception as e:
        print(f"❌ 初始化声音模板失败: {e}")
        db.rollback()
    finally:
        db.close()

def main():
    """主函数"""
    print("=" * 60)
    print("初始化模板数据")
    print("=" * 60)
    
    init_avatar_templates()
    init_voice_templates()
    
    print("\n" + "=" * 60)
    print("模板数据初始化完成")
    print("=" * 60)

if __name__ == "__main__":
    main()
