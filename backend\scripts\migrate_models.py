#!/usr/bin/env python3
"""
模型迁移脚本
将data/models目录下的模型迁移到规范的存储结构中
"""

import os
import shutil
import json
from pathlib import Path
from datetime import datetime

# 项目根目录
PROJECT_ROOT = Path(__file__).parent.parent.parent
DATA_MODELS_PATH = PROJECT_ROOT / "data" / "models"
STORAGE_MODELS_PATH = PROJECT_ROOT / "backend" / "storage" / "models"

# 模型配置
MODEL_CONFIGS = {
    "LivePortrait": {
        "type": "digital_human",
        "description": "快手开源的高质量数字人生成模型",
        "version": "1.0.0",
        "gpu_memory_required": 8,
        "dependencies": ["torch", "torchvision", "opencv-python", "numpy", "pillow"],
        "main_script": "app.py",
        "inference_script": "inference.py"
    },
    "MuseTalk": {
        "type": "digital_human", 
        "description": "腾讯开源的实时唇同步数字人模型",
        "version": "1.5.0",
        "gpu_memory_required": 6,
        "dependencies": ["torch", "torchaudio", "librosa", "opencv-python"],
        "main_script": "app.py",
        "inference_script": "inference.py"
    },
    "MuseTalk_official": {
        "type": "digital_human",
        "description": "MuseTalk官方版本",
        "version": "1.5.0", 
        "gpu_memory_required": 6,
        "dependencies": ["torch", "torchaudio", "librosa", "opencv-python"],
        "main_script": "app.py",
        "inference_script": None
    },
    "SadTalker": {
        "type": "digital_human",
        "description": "数字人面部动画生成模型",
        "version": "1.0.0",
        "gpu_memory_required": 8,
        "dependencies": ["torch", "torchvision", "opencv-python"],
        "main_script": "app_sadtalker.py",
        "inference_script": "inference.py"
    },
    "Wav2Lip": {
        "type": "digital_human",
        "description": "唇形同步模型",
        "version": "1.0.0",
        "gpu_memory_required": 4,
        "dependencies": ["torch", "opencv-python"],
        "main_script": None,
        "inference_script": "inference.py"
    }
}

def get_directory_size(path):
    """计算目录大小"""
    total_size = 0
    for dirpath, dirnames, filenames in os.walk(path):
        for filename in filenames:
            filepath = os.path.join(dirpath, filename)
            try:
                total_size += os.path.getsize(filepath)
            except (OSError, FileNotFoundError):
                pass
    return total_size

def create_model_info(model_name, source_path, target_path):
    """创建模型信息文件"""
    config = MODEL_CONFIGS.get(model_name, {})
    
    info = {
        "name": model_name,
        "type": config.get("type", "unknown"),
        "description": config.get("description", ""),
        "version": config.get("version", "1.0.0"),
        "gpu_memory_required": config.get("gpu_memory_required", 0),
        "dependencies": config.get("dependencies", []),
        "main_script": config.get("main_script"),
        "inference_script": config.get("inference_script"),
        "source_path": str(source_path),
        "target_path": str(target_path),
        "size": get_directory_size(source_path),
        "migrated_at": datetime.now().isoformat(),
        "status": "migrated"
    }
    
    # 保存模型信息
    info_file = target_path / "model_info.json"
    with open(info_file, "w", encoding="utf-8") as f:
        json.dump(info, f, indent=2, ensure_ascii=False)
    
    return info

def migrate_model(model_name, source_path, target_base_path):
    """迁移单个模型"""
    print(f"\n开始迁移模型: {model_name}")
    print(f"源路径: {source_path}")
    
    # 确定目标路径
    config = MODEL_CONFIGS.get(model_name, {})
    model_type = config.get("type", "unknown")
    target_path = target_base_path / model_type / model_name.lower()
    
    print(f"目标路径: {target_path}")
    
    # 创建目标目录
    target_path.mkdir(parents=True, exist_ok=True)
    
    try:
        # 检查是否已经迁移
        if (target_path / "model_info.json").exists():
            print(f"模型 {model_name} 已经迁移，跳过")
            return True
        
        # 复制模型文件（而不是移动，保留原文件）
        print("正在复制模型文件...")
        
        # 复制重要文件
        important_files = [
            "*.py", "*.sh", "*.bat", "*.txt", "*.md", "*.json", "*.yaml", "*.yml",
            "LICENSE", "README*", "requirements*", "config*"
        ]
        
        import glob
        copied_files = 0
        
        for pattern in important_files:
            for file_path in glob.glob(str(source_path / pattern)):
                if os.path.isfile(file_path):
                    file_name = os.path.basename(file_path)
                    target_file = target_path / file_name
                    shutil.copy2(file_path, target_file)
                    copied_files += 1
        
        # 复制重要目录
        important_dirs = ["src", "configs", "scripts", "assets", "models", "checkpoints"]
        
        for dir_name in important_dirs:
            source_dir = source_path / dir_name
            if source_dir.exists() and source_dir.is_dir():
                target_dir = target_path / dir_name
                print(f"复制目录: {dir_name}")
                shutil.copytree(source_dir, target_dir, dirs_exist_ok=True)
        
        # 创建模型信息文件
        model_info = create_model_info(model_name, source_path, target_path)
        
        print(f"模型 {model_name} 迁移完成")
        print(f"- 复制文件数: {copied_files}")
        print(f"- 模型大小: {model_info['size'] / (1024*1024):.1f} MB")
        
        return True
        
    except Exception as e:
        print(f"迁移模型 {model_name} 失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("数字人模型迁移脚本")
    print("=" * 60)
    
    # 检查源目录
    if not DATA_MODELS_PATH.exists():
        print(f"源目录不存在: {DATA_MODELS_PATH}")
        return
    
    # 创建目标目录
    STORAGE_MODELS_PATH.mkdir(parents=True, exist_ok=True)
    (STORAGE_MODELS_PATH / "digital_human").mkdir(parents=True, exist_ok=True)
    
    # 统计信息
    total_models = 0
    migrated_models = 0
    failed_models = []
    
    # 迁移每个模型
    for model_name in MODEL_CONFIGS.keys():
        source_path = DATA_MODELS_PATH / model_name
        
        if source_path.exists() and source_path.is_dir():
            total_models += 1
            
            if migrate_model(model_name, source_path, STORAGE_MODELS_PATH):
                migrated_models += 1
            else:
                failed_models.append(model_name)
        else:
            print(f"模型目录不存在，跳过: {model_name}")
    
    # 输出统计结果
    print("\n" + "=" * 60)
    print("迁移完成统计:")
    print(f"- 总模型数: {total_models}")
    print(f"- 成功迁移: {migrated_models}")
    print(f"- 失败数量: {len(failed_models)}")
    
    if failed_models:
        print(f"- 失败模型: {', '.join(failed_models)}")
    
    print(f"\n新的模型存储路径: {STORAGE_MODELS_PATH}")
    print("=" * 60)

if __name__ == "__main__":
    main()
