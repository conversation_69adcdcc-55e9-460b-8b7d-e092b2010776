#!/usr/bin/env python3
"""
简单的 Celery 测试
"""
import os
import sys
import time
from pathlib import Path
from dotenv import load_dotenv

# 添加后端路径
backend_dir = Path(__file__).parent.parent
sys.path.append(str(backend_dir))

# 加载环境变量
env_path = backend_dir / ".env"
load_dotenv(env_path)

def test_simple_task():
    """测试简单任务"""
    print("🧪 测试简单任务")
    print("=" * 50)
    
    try:
        from app.tasks.test_unified_task import test_unified_task
        from app.core.task_manager import task_manager
        
        # 创建任务记录
        task_id = task_manager.create_task(
            task_type="test",
            task_subtype="simple",
            user_id="test-user",
            title="简单 Celery 测试",
            description="测试 Celery 消息格式修复",
            input_params={"test_message": "Hello Celery!", "duration": 3}
        )
        
        print(f"📝 任务ID: {task_id}")
        
        # 提交任务
        result = test_unified_task.delay(
            task_id=task_id,
            test_message="Hello Celery!",
            duration=3
        )
        
        print(f"✅ 任务提交成功")
        print(f"📊 Celery 任务ID: {result.id}")
        print(f"📊 初始状态: {result.status}")
        
        # 监控任务
        print(f"⏳ 监控任务进度...")
        for i in range(10):
            time.sleep(1)
            
            # 检查 Celery 状态
            celery_status = result.status
            
            # 检查数据库状态
            task_info = task_manager.get_task(task_id)
            if task_info:
                db_status = task_info['status']
                progress = task_info['progress']
                message = task_info.get('message', 'N/A')
                
                print(f"   [{i+1:2d}s] Celery: {celery_status} | DB: {db_status} ({progress}%) - {message}")
                
                if db_status == 'completed':
                    print(f"🎉 任务完成！")
                    return True
                elif db_status == 'failed':
                    print(f"❌ 任务失败")
                    return False
            else:
                print(f"   [{i+1:2d}s] Celery: {celery_status} | DB: 任务未找到")
        
        print(f"⏰ 监控超时")
        return False
        
    except Exception as e:
        print(f"❌ 简单任务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_wanx_task():
    """测试 Wanx 任务"""
    print(f"\n🎬 测试 Wanx 任务")
    print("=" * 50)
    
    try:
        from app.tasks.wanx_video_tasks import wanx_text_to_video
        from app.core.task_manager import task_manager
        from app.models.unified_tasks import TaskTypes
        
        # 创建任务记录
        task_id = task_manager.create_task(
            task_type=TaskTypes.VIDEO_GENERATION,
            task_subtype=TaskTypes.VideoGeneration.TEXT_TO_VIDEO,
            user_id="test-user",
            title="Wanx Celery 测试",
            description="测试 Wanx 任务 Celery 处理",
            input_params={
                "prompt": "Wanx Celery 测试",
                "model": "t2v-1.3B",
                "duration": 2
            }
        )
        
        print(f"📝 任务ID: {task_id}")
        
        # 提交任务
        result = wanx_text_to_video.delay(
            task_id=task_id,
            prompt="Wanx Celery 测试",
            model="t2v-1.3B",
            duration=2,
            resolution="512x512",
            fps=8,
            guidance_scale=5.0,
            num_inference_steps=5,
            user_id="test-user"
        )
        
        print(f"✅ Wanx 任务提交成功")
        print(f"📊 Celery 任务ID: {result.id}")
        print(f"📊 初始状态: {result.status}")
        
        # 监控任务（较长时间）
        print(f"⏳ 监控 Wanx 任务进度...")
        for i in range(30):  # 30秒监控
            time.sleep(1)
            
            # 检查 Celery 状态
            celery_status = result.status
            
            # 检查数据库状态
            task_info = task_manager.get_task(task_id)
            if task_info:
                db_status = task_info['status']
                progress = task_info['progress']
                message = task_info.get('message', 'N/A')
                
                # 只在状态变化时打印
                if i == 0 or i % 5 == 0 or db_status != 'pending':
                    print(f"   [{i+1:2d}s] Celery: {celery_status} | DB: {db_status} ({progress}%) - {message}")
                
                if db_status == 'completed':
                    output_data = task_info.get('output_data', {})
                    video_url = output_data.get('video_url', 'N/A')
                    print(f"🎉 Wanx 任务完成！视频URL: {video_url}")
                    return True
                elif db_status == 'failed':
                    error_msg = task_info.get('error_message', 'N/A')
                    print(f"❌ Wanx 任务失败: {error_msg}")
                    return False
            else:
                print(f"   [{i+1:2d}s] Celery: {celery_status} | DB: 任务未找到")
        
        print(f"⏰ Wanx 任务监控超时")
        return False
        
    except Exception as e:
        print(f"❌ Wanx 任务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_worker_status():
    """检查 Worker 状态"""
    print(f"\n🔍 检查 Worker 状态")
    print("=" * 50)
    
    try:
        from app.core.celery_app import celery_app
        
        inspect = celery_app.control.inspect()
        
        # 检查活跃的 Worker
        active_workers = inspect.active()
        if active_workers:
            print(f"✅ 找到 {len(active_workers)} 个活跃的 Worker")
            for worker_name, tasks in active_workers.items():
                print(f"   🔧 {worker_name}: {len(tasks)} 个活跃任务")
            return True
        else:
            print(f"❌ 没有找到活跃的 Worker")
            return False
            
    except Exception as e:
        print(f"❌ 检查 Worker 状态失败: {e}")
        return False

def main():
    """主函数"""
    print("🧪 简单 Celery 测试（消息格式修复后）")
    print("=" * 60)
    
    # 1. 检查 Worker 状态
    worker_ok = check_worker_status()
    if not worker_ok:
        print(f"\n❌ Worker 未运行，无法继续测试")
        return
    
    # 2. 测试简单任务
    simple_ok = test_simple_task()
    
    # 3. 如果简单任务成功，测试 Wanx 任务
    wanx_ok = False
    if simple_ok:
        wanx_ok = test_wanx_task()
    else:
        print(f"\n⚠️ 简单任务失败，跳过 Wanx 任务测试")
    
    # 总结
    print(f"\n📊 测试总结:")
    print(f"✅ Worker 状态: 正常")
    print(f"{'✅' if simple_ok else '❌'} 简单任务: {'成功' if simple_ok else '失败'}")
    print(f"{'✅' if wanx_ok else '❌'} Wanx 任务: {'成功' if wanx_ok else '失败/跳过'}")
    
    if simple_ok:
        print(f"\n🎉 Celery 消息格式修复成功！")
        if wanx_ok:
            print(f"🎬 Wanx 任务也能正常处理！")
            print(f"💡 系统现在完全正常工作")
        else:
            print(f"⚠️ Wanx 任务可能需要更长时间或有其他问题")
    else:
        print(f"\n❌ Celery 仍有问题需要解决")

if __name__ == "__main__":
    main()
