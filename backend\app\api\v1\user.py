"""
用户API路由
"""

from fastapi import APIRouter, HTTPException, Depends, Form
from fastapi.security import OAuth2PasswordBearer, OAuth2PasswordRequestForm
from pydantic import BaseModel, EmailStr
from typing import Optional
from ...core.database import get_db_manager
from ...services.user_service import UserService

router = APIRouter()
user_service = UserService()
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")


class UserRegister(BaseModel):
    """用户注册模型"""
    username: str
    email: EmailStr
    password: str


class UserResponse(BaseModel):
    """用户响应模型"""
    id: int
    username: str
    email: str
    created_at: str


@router.post("/register")
async def register_user(user_data: UserRegister):
    """用户注册"""
    try:
        # 检查用户名是否已存在
        db_manager = get_db_manager()
        existing_user = db_manager.execute_query(
            "SELECT id FROM users WHERE username = ? OR email = ?",
            (user_data.username, user_data.email)
        )
        
        if existing_user:
            return {"success": False, "error": "用户名或邮箱已存在"}
        
        # 创建用户
        result = await user_service.create_user(
            user_data.username,
            user_data.email,
            user_data.password
        )
        
        return result
        
    except Exception as e:
        return {"success": False, "error": str(e)}


@router.post("/login")
async def login_user(form_data: OAuth2PasswordRequestForm = Depends()):
    """用户登录"""
    try:
        result = await user_service.authenticate_user(
            form_data.username,
            form_data.password
        )
        
        return result
        
    except Exception as e:
        return {"success": False, "error": str(e)}


@router.get("/profile")
async def get_user_profile(token: str = Depends(oauth2_scheme)):
    """获取用户资料"""
    try:
        # 验证token并获取用户信息
        user_info = await user_service.get_user_by_token(token)
        
        if user_info:
            return {
                "success": True,
                "user": user_info
            }
        else:
            return {"success": False, "error": "无效的token"}
            
    except Exception as e:
        return {"success": False, "error": str(e)}


@router.put("/profile")
async def update_user_profile(
    user_data: dict,
    token: str = Depends(oauth2_scheme)
):
    """更新用户资料"""
    try:
        # 验证token
        user_info = await user_service.get_user_by_token(token)
        if not user_info:
            return {"success": False, "error": "无效的token"}
        
        # 更新用户信息
        result = await user_service.update_user(user_info["id"], user_data)
        
        return result
        
    except Exception as e:
        return {"success": False, "error": str(e)}


class RefreshTokenRequest(BaseModel):
    refresh_token: str

@router.post("/refresh")
async def refresh_token(
    request: RefreshTokenRequest = None,
    refresh_token: str = Form(None)
):
    """刷新访问令牌 - 支持JSON和表单数据格式"""
    try:
        # 从JSON请求体或表单数据中获取refresh_token
        token = None
        if request and request.refresh_token:
            token = request.refresh_token
        elif refresh_token:
            token = refresh_token

        if not token:
            return {"success": False, "error": "缺少refresh_token参数"}

        result = await user_service.refresh_access_token(token)
        return result

    except Exception as e:
        return {"success": False, "error": str(e)}


@router.post("/logout")
async def logout_user(token: str = Depends(oauth2_scheme)):
    """用户登出"""
    try:
        # 这里可以实现token黑名单等逻辑
        return {"success": True, "message": "登出成功"}

    except Exception as e:
        return {"success": False, "error": str(e)}


@router.get("/info")
async def get_user_info():
    """获取用户信息（兼容性接口）"""
    try:
        # 简单的用户信息返回，用于前端兼容
        return {
            "success": True,
            "user": {
                "id": 1,
                "username": "demo_user",
                "email": "<EMAIL>",
                "avatar": "/assets/default-avatar.png"
            }
        }
        
    except Exception as e:
        return {"success": False, "error": str(e)}
