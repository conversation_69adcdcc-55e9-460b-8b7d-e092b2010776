{"source": "<p>项目名称</p><p>参考案例</p><p>开源</p><p>硬件配置</p><p>开发流程</p><p>备注</p><p>文生视频</p><p>阿里通义万相\nhttps://tongyi.aliyun.com/wanxiang/</p><p>大规模视频生成模型\nhttps://github.com/Wan-Video/Wan2.1</p><p>显卡：NVIDIA RTX 3060及以上（推荐RTX 4060 Ti 16GB）‌\n显存：≥8GB（运行1.3B基础模型）/ ≥16GB（运行14B高清模型）‌\n存储：SSD预留50GB空间（模型文件约35GB）‌</p><p>1、集成到ComfyUI，测试开源库生成视频的效果。（1周）\n2、设计用户操作界面，调用开源库完成视频制作。（1-2周）\n3、测试与部署。（1-2周）</p><p>目前服务器的显卡3060，生成效果不理想</p><p>数字人</p><p>万彩AI\nhttps://ai.kezhan365.com/</p><p>虚拟主播表情驱动 Audio2Face + UE5 \n用户意图理解 Rasa + spaCy</p><p>/</p><p>1、服务器部署deepseek大模型，外部应用调用本地大模型API.(1周）\n2、开源语音、表情等源码集成调试（1-2周）\n3、设计用户操作界面，生成数字人 （2-3周）\n4、测试与部署。（1-2周）</p><p>AI同传翻译</p><p>百度AI同传\nhttps://tongchuan.baidu.com/</p><p>多语种实时翻译 Whisper + Transformers</p><p>/</p><p>1、服务器部署deepseek大模型，外部应用调用本地大模型API.(1周）\n2、开源语音、文字等源码集成调试（1-2周）\n3、设计用户操作界面，生成AI同传翻译 （2-3周）\n4、测试与部署。（1-2周）</p><p>多语数据管理</p><p>开源语言资产管理平台\nhttps://github.com/hanlintao/BiCorpus</p><p>/</p><p>1、测试开源系统 （2天）\n2、设计操作界面（4天）\n3、测试与部署 （3天）</p>", "translated": "<p>Project Name  \nReference Cases  \nOpen Source  \nHardware Configuration  \nDevelopment Process  \nRemarks  \nText Video  \nAli <PERSON>  \nhttps://tongyi.aliyun.com/wanxiang/  \nLarge-Scale Video Generation Model https://github.com/Wan-Video/Wan2.1\nMemory: ≥8GB (running 1.3B base model) / ≥16GB (running 14B high-resolution model)  \nStorage: SDD reserves 50GB of space (model files are approximately 35GB)</p><p>“Integrated into ComfyUI”，而“测试开源库生成视频的效果”则是“Test the open-source library video generation effects”。\n1. Integrated into ComfyUI, test the open-source library video generation effects. (1 week)\n2. Design user operation interface and use open-source library to complete video production. (1-2 weeks)\n3. Test and deployment. (1-2 weeks)\nCurrent server GPU is 3060, effects are not ideal for generation. “Virtual Host Emotion Driven Audio2Face + UE5”。\nDigital Human\nWancai AI\nhttps://ai.kezhan365.com/\nUser Intent Understanding Rasa + spaCy\n1. Deploy the deepseek large model on the server and enable external applications to call the local model API. (1 week)  \n2. Integrate and debug open-source audio,表情等源码. (1-2 weeks)  \n3. Design the user operation interface and generate a digital human. (2-3 weeks)  \n4. Conduct testing and deployment. (1-2 weeks) AI Whisper Translation</p><p>1、Deploy the DeepSeek large model on the server and enable external applications to call the local model API.(1 week)\n2、Integrate and debug open-source speech, text, etc. (1-2 weeks)\n3、Design the user operation interface and generate AI same-transmission translation （2-3 weeks）\n4、Testing and deployment. “(2 days)”。\n(1-2 weeks)  \nMulti-lingual Data Management  \nOpen Source Language Asset Management Platform  \nhttps://github.com/hanlintao/BiCorpus  \n1. Test the open-source system (2 days)  \n2. Design the operation interface (4 days) “3 days”就可以了，不需要任何变化。因此，整个句子应该是：“3. Testing and Deployment (3 days)”。\n3.</p>"}