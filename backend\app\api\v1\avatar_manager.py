"""
头像管理API路由
"""

from fastapi import APIRouter, HTTPException, UploadFile, File, Form, BackgroundTasks
from fastapi.responses import FileResponse
from typing import List, Optional, Dict, Any
import os
import json
import shutil
import asyncio
from pathlib import Path
import requests
from datetime import datetime

router = APIRouter()

# 头像存储路径
AVATAR_STORAGE_PATH = Path("storage/avatars")
AVATAR_STORAGE_PATH.mkdir(parents=True, exist_ok=True)

# 默认头像模板配置 - 与前端模板匹配
DEFAULT_AVATARS = {
    "female-teacher": {
        "name": "温和女教师",
        "type": "female-teacher",
        "category": "education",
        "description": "温和亲切的女性教师形象，适合在线教育、培训讲解",
        "tags": ["教育", "专业", "亲和"],
        "resolution": "512x512",
        "download_url": "https://thispersondoesnotexist.com/",
        "preview_url": "https://thispersondoesnotexist.com/",
        "size": 2048000
    },
    "male-teacher": {
        "name": "资深男教授",
        "type": "male-teacher",
        "category": "education",
        "description": "权威专业的男性教授形象，适合学术讲座、专业培训",
        "tags": ["教育", "专业", "权威"],
        "resolution": "512x512",
        "download_url": "https://thispersondoesnotexist.com/",
        "preview_url": "https://thispersondoesnotexist.com/",
        "size": 1856000
    },
    "female-business": {
        "name": "职场女性",
        "type": "female-business",
        "category": "business",
        "description": "现代职场女性形象，适合商务演示、企业培训",
        "tags": ["商务", "专业", "现代"],
        "resolution": "512x512",
        "download_url": "https://thispersondoesnotexist.com/",
        "preview_url": "https://thispersondoesnotexist.com/",
        "size": 2156000
    },
    "male-business": {
        "name": "商务精英",
        "type": "male-business",
        "category": "business",
        "description": "专业商务男性形象，适合企业宣传、商务汇报",
        "tags": ["商务", "专业", "精英"],
        "resolution": "512x512",
        "download_url": "https://thispersondoesnotexist.com/",
        "preview_url": "https://thispersondoesnotexist.com/",
        "size": 2234000
    },
    "female-service": {
        "name": "亲和客服",
        "type": "female-service",
        "category": "service",
        "description": "友好亲和的客服形象，适合客户服务、产品介绍",
        "tags": ["客服", "友好", "亲和"],
        "resolution": "512x512",
        "download_url": "https://thispersondoesnotexist.com/",
        "preview_url": "https://thispersondoesnotexist.com/",
        "size": 1987000
    },
    "male-host": {
        "name": "专业主持",
        "type": "male-host",
        "category": "media",
        "description": "专业主持人形象，适合新闻播报、活动主持",
        "tags": ["主持", "专业", "端庄"],
        "resolution": "512x512",
        "download_url": "https://thispersondoesnotexist.com/",
        "preview_url": "https://thispersondoesnotexist.com/",
        "size": 2100000
    }
}

@router.get("/status")
async def get_avatar_status():
    """获取头像状态统计"""
    try:
        # 统计已下载的头像
        downloaded_count = 0
        total_size = 0
        
        for avatar_type, config in DEFAULT_AVATARS.items():
            avatar_path = AVATAR_STORAGE_PATH / f"{avatar_type}.jpg"
            if avatar_path.exists():
                downloaded_count += 1
                total_size += avatar_path.stat().st_size
        
        return {
            "success": True,
            "data": {
                "statistics": {
                    "total_files": len(DEFAULT_AVATARS),
                    "downloaded_files": downloaded_count,
                    "available_files": len(DEFAULT_AVATARS) - downloaded_count,
                    "total_size": total_size
                },
                "avatars": DEFAULT_AVATARS
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取头像状态失败: {str(e)}")

@router.get("/list")
async def get_avatar_list(
    category: Optional[str] = None,
    search: Optional[str] = None
):
    """获取头像列表"""
    try:
        avatars = []
        
        for avatar_id, config in DEFAULT_AVATARS.items():
            # 检查是否已下载
            avatar_path = AVATAR_STORAGE_PATH / f"{avatar_id}.jpg"
            is_downloaded = avatar_path.exists()
            
            # 应用筛选
            if category and config.get("category") != category:
                continue
                
            if search and search.lower() not in config["name"].lower():
                continue
            
            avatar_info = {
                "id": avatar_id,
                "downloaded": is_downloaded,
                "local_path": str(avatar_path) if is_downloaded else None,
                **config
            }
            avatars.append(avatar_info)
        
        return {
            "success": True,
            "data": avatars
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取头像列表失败: {str(e)}")

@router.post("/download/{avatar_type}")
async def download_avatar(avatar_type: str, background_tasks: BackgroundTasks):
    """下载指定头像"""
    if avatar_type not in DEFAULT_AVATARS:
        raise HTTPException(status_code=404, detail="头像类型不存在")
    
    try:
        config = DEFAULT_AVATARS[avatar_type]
        avatar_path = AVATAR_STORAGE_PATH / f"{avatar_type}.jpg"
        
        if avatar_path.exists():
            return {
                "success": True,
                "message": "头像已存在",
                "path": str(avatar_path)
            }
        
        # 添加后台下载任务
        background_tasks.add_task(download_avatar_file, avatar_type, config, avatar_path)
        
        return {
            "success": True,
            "message": "开始下载头像",
            "avatar_type": avatar_type
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"下载头像失败: {str(e)}")

@router.get("/image/{avatar_type}")
async def get_avatar_image(avatar_type: str, index: int = 0):
    """获取头像图片"""
    if avatar_type not in DEFAULT_AVATARS:
        raise HTTPException(status_code=404, detail="头像类型不存在")
    
    avatar_path = AVATAR_STORAGE_PATH / f"{avatar_type}.jpg"
    
    if not avatar_path.exists():
        # 如果头像不存在，返回占位图
        placeholder_path = AVATAR_STORAGE_PATH / "placeholder.jpg"
        if placeholder_path.exists():
            return FileResponse(placeholder_path, media_type="image/jpeg")
        else:
            raise HTTPException(status_code=404, detail="头像文件不存在")
    
    return FileResponse(avatar_path, media_type="image/jpeg")

@router.post("/upload")
async def upload_custom_avatar(
    name: str = Form(...),
    category: str = Form("custom"),
    file: UploadFile = File(...)
):
    """上传自定义头像"""
    try:
        # 验证文件类型
        if not file.content_type.startswith("image/"):
            raise HTTPException(status_code=400, detail="只支持图片文件")
        
        # 生成文件名
        file_extension = file.filename.split('.')[-1] if '.' in file.filename else 'jpg'
        avatar_type = f"custom_{name.lower().replace(' ', '_')}"
        avatar_path = AVATAR_STORAGE_PATH / f"{avatar_type}.{file_extension}"
        
        # 保存文件
        content = await file.read()
        with open(avatar_path, "wb") as f:
            f.write(content)
        
        return {
            "success": True,
            "message": "头像上传成功",
            "avatar_type": avatar_type,
            "path": str(avatar_path)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"上传头像失败: {str(e)}")

@router.delete("/{avatar_type}")
async def delete_avatar(avatar_type: str):
    """删除头像"""
    try:
        avatar_path = AVATAR_STORAGE_PATH / f"{avatar_type}.jpg"
        
        if avatar_path.exists():
            avatar_path.unlink()
            return {
                "success": True,
                "message": "头像删除成功"
            }
        else:
            raise HTTPException(status_code=404, detail="头像文件不存在")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除头像失败: {str(e)}")

async def download_avatar_file(avatar_type: str, config: dict, avatar_path: Path):
    """后台下载头像文件"""
    try:
        # 模拟下载过程（实际应该从真实URL下载）
        await asyncio.sleep(2)  # 模拟下载时间
        
        # 创建一个简单的占位图片文件
        # 实际实现中应该从config["download_url"]下载真实图片
        placeholder_content = b"placeholder_image_content"
        
        with open(avatar_path, "wb") as f:
            f.write(placeholder_content)
            
        print(f"头像 {avatar_type} 下载完成: {avatar_path}")
    except Exception as e:
        print(f"下载头像 {avatar_type} 失败: {e}")

@router.get("/categories")
async def get_avatar_categories():
    """获取头像分类"""
    categories = set()
    for config in DEFAULT_AVATARS.values():
        categories.add(config.get("category", "other"))
    
    return {
        "success": True,
        "data": list(categories)
    }
