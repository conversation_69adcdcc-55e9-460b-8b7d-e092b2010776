#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
直接调用WAN 2.1官方脚本生成视频的实用工具

使用方法:
    python wan_direct_generate.py --prompt "一个和尚" --output_file output.mp4
"""

import os
import sys
import logging
import argparse
import subprocess
import json
import time
import traceback
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 获取项目根目录
PROJECT_ROOT = Path(__file__).parent.parent.parent.absolute()
logger.info(f"项目根目录: {PROJECT_ROOT}")

# WAN模型目录
WAN_DIR = os.path.join(PROJECT_ROOT, "backend", "local_models", "wan", "Wan2.1")
WAN_SCRIPT = os.path.join(WAN_DIR, "generate.py")
T2V_1_3B_MODEL = os.path.join(WAN_DIR, "Wan2.1-T2V-1.3B")
T2V_14B_MODEL = os.path.join(WAN_DIR, "Wan2.1-T2V-14B")

# 模型支持的分辨率映射
SUPPORTED_SIZES = {
    "t2v-1.3B": ["480*832", "832*480"],
    "t2v-14B": ["720*1280", "1280*720", "480*832", "832*480"],
    "i2v-14B": ["720*1280", "1280*720", "480*832", "832*480"],
    "i2v-480p": ["480*832", "832*480"],
    "i2v-720p": ["720*1280", "1280*720"]
}

def get_supported_resolution(width, height, model_type, model_size):
    """
    获取给定模型支持的最接近的分辨率
    
    Args:
        width (int): 请求的宽度
        height (int): 请求的高度
        model_type (str): 模型类型 (t2v, i2v)
        model_size (str): 模型大小 (1.3B, 14B)
        
    Returns:
        tuple: (width, height)
    """
    # 构建完整模型类型标识符
    model_id = f"{model_type}-{model_size}"
    
    # 检查是否存在支持的分辨率列表
    if model_id not in SUPPORTED_SIZES:
        logger.warning(f"未知的模型类型 {model_id}，将使用原始分辨率")
        return width, height
    
    # 请求的分辨率
    requested_size = f"{width}*{height}"
    
    # 检查请求的分辨率是否直接支持
    if requested_size in SUPPORTED_SIZES[model_id]:
        return width, height
    
    # 如果不支持，使用第一个支持的分辨率
    logger.warning(f"模型 {model_id} 不支持分辨率 {requested_size}")
    logger.warning(f"支持的分辨率: {', '.join(SUPPORTED_SIZES[model_id])}")
    
    # 反转分辨率（交换宽高）并检查
    reversed_size = f"{height}*{width}"
    if reversed_size in SUPPORTED_SIZES[model_id]:
        logger.info(f"使用相反的分辨率 {reversed_size}")
        return height, width
    
    # 使用第一个支持的分辨率
    supported_size = SUPPORTED_SIZES[model_id][0]
    w, h = map(int, supported_size.split('*'))
    logger.info(f"自动调整为支持的分辨率 {supported_size}")
    return w, h

def generate_video(
    prompt, 
    output_file=None, 
    negative_prompt="", 
    num_frames=80,
    height=720, 
    width=1280, 
    fps=8, 
    guidance_scale=5.0, 
    seed=None, 
    model_size="1.3B"
):
    """
    直接使用WAN 2.1官方脚本生成视频

    Args:
        prompt (str): 提示词
        output_file (str): 输出文件路径
        negative_prompt (str): 负面提示词
        num_frames (int): 帧数
        height (int): 高度
        width (int): 宽度
        fps (int): 帧率
        guidance_scale (float): 引导比例
        seed (int): 随机种子
        model_size (str): 模型大小 (1.3B/14B)

    Returns:
        dict: 包含生成结果的字典
    """
    start_time = time.time()
    logger.info(f"开始生成视频: {prompt}")
    
    # 确保WAN目录和脚本存在
    if not os.path.exists(WAN_DIR):
        logger.error(f"WAN目录不存在: {WAN_DIR}")
        return {"success": False, "error": f"WAN目录不存在: {WAN_DIR}"}
    
    if not os.path.exists(WAN_SCRIPT):
        logger.error(f"WAN脚本不存在: {WAN_SCRIPT}")
        return {"success": False, "error": f"WAN脚本不存在: {WAN_SCRIPT}"}
    
    # 根据模型大小选择模型目录
    model_dir = T2V_1_3B_MODEL if model_size == "1.3B" else T2V_14B_MODEL
    if not os.path.exists(model_dir):
        logger.error(f"模型目录不存在: {model_dir}")
        return {"success": False, "error": f"模型目录不存在: {model_dir}"}
    
    task = f"t2v-{model_size}"
    
    # 调整分辨率以匹配模型支持的分辨率
    original_width, original_height = width, height
    width, height = get_supported_resolution(width, height, "t2v", model_size)
    
    # 如果分辨率被调整，记录并通知
    if original_width != width or original_height != height:
        logger.warning(f"请求的分辨率 {original_width}x{original_height} 不被 {task} 模型支持")
        logger.info(f"已自动调整为支持的分辨率: {width}x{height}")
    
    # 构建命令行参数
    size = f"{width}*{height}"
    
    # 设置随机种子参数
    seed_param = f"--base_seed {seed}" if seed is not None else ""
    
    # 设置sample_shift参数（对于1.3B模型推荐使用8）
    sample_shift = 8 if model_size == "1.3B" else 5
    
    # 构建命令
    cmd = [
        sys.executable, WAN_SCRIPT,
        "--task", task,
        "--size", size,
        "--frame_num", str(num_frames),
        "--ckpt_dir", model_dir,
        "--sample_shift", str(sample_shift),
        "--sample_guide_scale", str(guidance_scale),
        "--prompt", prompt
    ]
    
    # 添加随机种子参数（如果存在）
    if seed is not None:
        cmd.extend(["--base_seed", str(seed)])
    
    # 添加offload_model和t5_cpu参数（对于显存受限的情况）
    cmd.extend(["--offload_model", "True", "--t5_cpu"])
    
    # 记录完整命令
    cmd_str = " ".join(cmd)
    logger.info(f"生成命令: {cmd_str}")
    
    # 创建outputs目录（如果不存在）
    outputs_dir = os.path.join(WAN_DIR, "outputs")
    os.makedirs(outputs_dir, exist_ok=True)
    
    # 执行命令
    try:
        # 切换到WAN目录执行命令
        process = subprocess.Popen(
            cmd,
            cwd=WAN_DIR,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            universal_newlines=True
        )
        
        # 读取输出和错误
        stdout, stderr = process.communicate()
        
        # 检查执行结果
        if process.returncode != 0:
            logger.error(f"生成视频失败: {stderr}")
            return {
                "success": False, 
                "error": stderr, 
                "stdout": stdout
            }
        
        # 查找生成的视频文件
        video_files = [f for f in os.listdir(outputs_dir) if f.endswith(".mp4")]
        video_files.sort(key=lambda x: os.path.getmtime(os.path.join(outputs_dir, x)), reverse=True)
        
        if not video_files:
            logger.error("未找到生成的视频文件")
            return {
                "success": False, 
                "error": "未找到生成的视频文件", 
                "stdout": stdout
            }
        
        # 获取最新生成的视频文件
        latest_video = video_files[0]
        generated_video_path = os.path.join(outputs_dir, latest_video)
        
        # 如果指定了输出文件路径，则复制到指定位置
        if output_file:
            import shutil
            # 确保输出目录存在
            os.makedirs(os.path.dirname(os.path.abspath(output_file)), exist_ok=True)
            # 复制文件
            shutil.copy2(generated_video_path, output_file)
            logger.info(f"已将视频文件复制到: {output_file}")
            result_path = output_file
        else:
            result_path = generated_video_path
        
        # 生成缩略图
        thumbnail_path = os.path.splitext(result_path)[0] + ".jpg"
        try:
            import cv2
            # 打开视频文件
            cap = cv2.VideoCapture(result_path)
            # 读取第一帧
            ret, frame = cap.read()
            if ret:
                # 保存为缩略图
                cv2.imwrite(thumbnail_path, frame)
                logger.info(f"已生成缩略图: {thumbnail_path}")
            else:
                logger.warning(f"无法读取视频帧来生成缩略图")
            # 释放视频文件
            cap.release()
        except Exception as e:
            logger.error(f"生成缩略图时出错: {str(e)}")
            logger.error(traceback.format_exc())
        
        elapsed_time = time.time() - start_time
        logger.info(f"视频生成完成，耗时: {elapsed_time:.2f}秒, 文件: {result_path}")
        
        return {
            "success": True,
            "video_path": result_path,
            "thumbnail_path": thumbnail_path if os.path.exists(thumbnail_path) else None,
            "elapsed_time": elapsed_time,
            "seed": seed,
            "frames": num_frames,
            "fps": fps,
            "width": width,
            "height": height,
            "prompt": prompt,
            "negative_prompt": negative_prompt,
            "guidance_scale": guidance_scale,
            "model_size": model_size
        }
        
    except Exception as e:
        logger.error(f"执行生成命令时出错: {str(e)}")
        logger.error(traceback.format_exc())
        return {"success": False, "error": str(e)}

def main():
    """命令行入口点"""
    parser = argparse.ArgumentParser(description="使用WAN 2.1生成视频")
    parser.add_argument("--prompt", type=str, required=True, help="提示词")
    parser.add_argument("--negative_prompt", type=str, default="", help="负面提示词")
    parser.add_argument("--output_file", type=str, help="输出文件路径")
    parser.add_argument("--num_frames", type=int, default=80, help="帧数")
    parser.add_argument("--height", type=int, default=720, help="高度")
    parser.add_argument("--width", type=int, default=1280, help="宽度")
    parser.add_argument("--fps", type=int, default=8, help="帧率")
    parser.add_argument("--guidance_scale", type=float, default=5.0, help="引导比例")
    parser.add_argument("--seed", type=int, help="随机种子")
    parser.add_argument("--model_size", type=str, default="1.3B", choices=["1.3B", "14B"], help="模型大小")
    
    args = parser.parse_args()
    
    # 生成视频
    result = generate_video(
        prompt=args.prompt,
        output_file=args.output_file,
        negative_prompt=args.negative_prompt,
        num_frames=args.num_frames,
        height=args.height,
        width=args.width,
        fps=args.fps,
        guidance_scale=args.guidance_scale,
        seed=args.seed,
        model_size=args.model_size
    )
    
    # 输出结果
    print(json.dumps(result, indent=2, ensure_ascii=False))
    
    # 返回状态码
    return 0 if result["success"] else 1

if __name__ == "__main__":
    sys.exit(main()) 