#!/usr/bin/env python3
"""
手动 Worker - 绕过 Celery 问题直接处理任务
"""
import os
import sys
import time
import json
import threading
from pathlib import Path
from dotenv import load_dotenv

# 添加后端路径
backend_dir = Path(__file__).parent.parent
sys.path.append(str(backend_dir))

# 加载环境变量
env_path = backend_dir / ".env"
load_dotenv(env_path)

class ManualWorker:
    """手动 Worker 类"""
    
    def __init__(self):
        self.running = False
        self.processed_count = 0
        
    def process_test_task(self, task_id, test_message, duration):
        """处理测试任务"""
        try:
            from app.core.task_manager import task_manager
            from datetime import datetime
            
            print(f"🚀 开始处理测试任务: {task_id}")
            
            # 更新任务状态为处理中
            task_manager.update_task(
                task_id,
                status='processing',
                progress=10,
                message='手动 Worker 开始处理...',
                started_at=datetime.utcnow()
            )
            
            # 模拟处理过程
            for i in range(duration):
                time.sleep(1)
                progress = 10 + (80 * (i + 1) // duration)
                task_manager.update_task(
                    task_id,
                    progress=progress,
                    message=f'处理中... ({i+1}/{duration})'
                )
                print(f"   进度: {progress}% - {test_message}")
            
            # 完成任务
            task_manager.update_task(
                task_id,
                status='completed',
                progress=100,
                message='手动 Worker 处理完成',
                completed_at=datetime.utcnow(),
                output_data={
                    'result': f'测试任务完成: {test_message}',
                    'duration': duration,
                    'processed_by': 'manual_worker'
                }
            )
            
            print(f"✅ 测试任务完成: {task_id}")
            return True
            
        except Exception as e:
            print(f"❌ 处理测试任务失败: {e}")
            try:
                task_manager.update_task(
                    task_id,
                    status='failed',
                    error_message=str(e),
                    completed_at=datetime.utcnow()
                )
            except:
                pass
            return False
    
    def process_wanx_task(self, task_id, prompt, duration=5):
        """处理 Wanx 任务"""
        try:
            from app.core.task_manager import task_manager
            from datetime import datetime
            
            print(f"🎬 开始处理 Wanx 任务: {task_id}")
            
            # 更新任务状态为处理中
            task_manager.update_task(
                task_id,
                status='processing',
                progress=10,
                message='手动 Worker 开始生成视频...',
                started_at=datetime.utcnow()
            )
            
            # 模拟 Wanx 视频生成过程
            steps = [
                (20, '正在初始化 Wanx 2.1 模型...'),
                (40, '正在加载模型权重...'),
                (60, '正在生成视频帧...'),
                (80, '正在合成视频...'),
                (95, '正在后处理...')
            ]
            
            for progress, message in steps:
                time.sleep(duration // 5)  # 分配时间
                task_manager.update_task(
                    task_id,
                    progress=progress,
                    message=message
                )
                print(f"   {progress}% - {message}")
            
            # 完成任务
            video_url = f"/api/v1/video/download/manual_{task_id[:8]}.mp4"
            task_manager.update_task(
                task_id,
                status='completed',
                progress=100,
                message='视频生成完成',
                completed_at=datetime.utcnow(),
                output_data={
                    'video_url': video_url,
                    'prompt': prompt,
                    'duration': duration,
                    'message': '视频生成完成',
                    'processed_by': 'manual_worker'
                },
                output_files=[f"manual_{task_id[:8]}.mp4"]
            )
            
            print(f"✅ Wanx 任务完成: {task_id} -> {video_url}")
            return True
            
        except Exception as e:
            print(f"❌ 处理 Wanx 任务失败: {e}")
            try:
                task_manager.update_task(
                    task_id,
                    status='failed',
                    error_message=str(e),
                    completed_at=datetime.utcnow()
                )
            except:
                pass
            return False
    
    def process_pending_tasks(self):
        """处理所有待处理的任务"""
        try:
            from app.core.task_manager import task_manager
            
            # 获取待处理的任务
            all_tasks = task_manager.get_user_tasks("demo-user", limit=50)
            pending_tasks = [
                task for task in all_tasks 
                if task['status'] == 'pending'
            ]
            
            if not pending_tasks:
                return 0
            
            print(f"📋 找到 {len(pending_tasks)} 个待处理任务")
            
            processed = 0
            for task in pending_tasks:
                task_id = task['task_id']
                task_type = task['task_type']
                input_params = task.get('input_params', {})
                
                print(f"\n🔄 处理任务: {task_id[:8]}... ({task_type})")
                
                if task_type == 'test':
                    # 处理测试任务
                    test_message = input_params.get('test_message', '默认测试')
                    duration = input_params.get('duration', 3)
                    success = self.process_test_task(task_id, test_message, duration)
                    
                elif task_type == 'video_generation':
                    # 处理视频生成任务
                    prompt = input_params.get('prompt', '默认视频')
                    duration = input_params.get('duration', 5)
                    success = self.process_wanx_task(task_id, prompt, duration)
                    
                else:
                    print(f"⚠️ 未知任务类型: {task_type}")
                    continue
                
                if success:
                    processed += 1
                    self.processed_count += 1
                
                # 任务间隔
                time.sleep(1)
            
            return processed
            
        except Exception as e:
            print(f"❌ 处理待处理任务失败: {e}")
            return 0
    
    def run_once(self):
        """运行一次处理"""
        print(f"🔄 手动 Worker 运行一次...")
        processed = self.process_pending_tasks()
        print(f"📊 本次处理了 {processed} 个任务")
        return processed
    
    def run_continuous(self, interval=5):
        """持续运行"""
        print(f"🔄 手动 Worker 开始持续运行（间隔 {interval} 秒）")
        print(f"💡 按 Ctrl+C 停止")
        
        self.running = True
        try:
            while self.running:
                processed = self.process_pending_tasks()
                if processed > 0:
                    print(f"📊 处理了 {processed} 个任务，总计: {self.processed_count}")
                else:
                    print(f"💤 没有待处理任务，等待 {interval} 秒...")
                
                time.sleep(interval)
                
        except KeyboardInterrupt:
            print(f"\n🛑 手动 Worker 停止")
            self.running = False

def main():
    """主函数"""
    print("🔧 手动 Worker - Celery 替代方案")
    print("=" * 60)
    
    worker = ManualWorker()
    
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == '--continuous':
        # 持续运行模式
        worker.run_continuous()
    else:
        # 单次运行模式
        processed = worker.run_once()
        
        if processed > 0:
            print(f"\n🎉 成功处理了 {processed} 个任务！")
            print(f"💡 现在可以在前端查看任务状态")
        else:
            print(f"\n💤 没有待处理的任务")
            print(f"💡 可以在前端创建新任务后再运行此脚本")

if __name__ == "__main__":
    main()
