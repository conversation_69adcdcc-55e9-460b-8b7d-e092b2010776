#!/usr/bin/env python3
"""
高级 MuseTalk 推理实现
基于面部关键点检测的真实嘴部变形
"""

import os
import sys
import argparse
import logging
import cv2
import numpy as np
import torch
import librosa
from pathlib import Path
import subprocess
import tempfile
import mediapipe as mp

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='高级 MuseTalk 数字人生成')
    parser.add_argument('--source_image', required=True, help='源图像路径')
    parser.add_argument('--driving_audio', required=True, help='驱动音频路径')
    parser.add_argument('--output', required=True, help='输出视频路径')
    parser.add_argument('--device', default='cuda', choices=['cuda', 'cpu'], help='设备类型')
    parser.add_argument('--fps', type=int, default=25, help='输出视频帧率')
    parser.add_argument('--quality', default='high', choices=['low', 'medium', 'high'], help='输出质量')
    return parser.parse_args()

class FacialLandmarkDetector:
    """面部关键点检测器"""
    
    def __init__(self):
        self.mp_face_mesh = mp.solutions.face_mesh
        self.face_mesh = self.mp_face_mesh.FaceMesh(
            static_image_mode=True,
            max_num_faces=1,
            refine_landmarks=True,
            min_detection_confidence=0.5
        )
        
        # 嘴部关键点索引 (MediaPipe 468个关键点)
        self.MOUTH_LANDMARKS = [
            # 外嘴唇
            61, 84, 17, 314, 405, 320, 307, 375, 321, 308, 324, 318,
            # 内嘴唇  
            78, 95, 88, 178, 87, 14, 317, 402, 318, 324, 308, 415,
            # 嘴角
            61, 291, 39, 181, 0, 17, 18, 200
        ]
        
        # 上嘴唇关键点
        self.UPPER_LIP = [61, 84, 17, 314, 405, 320, 307, 375, 321, 308, 324, 318]
        # 下嘴唇关键点
        self.LOWER_LIP = [78, 95, 88, 178, 87, 14, 317, 402, 318, 324, 308, 415]
        
    def detect_landmarks(self, image):
        """检测面部关键点"""
        try:
            rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            results = self.face_mesh.process(rgb_image)
            
            if results.multi_face_landmarks:
                landmarks = results.multi_face_landmarks[0]
                h, w = image.shape[:2]
                
                # 转换为像素坐标
                points = []
                for landmark in landmarks.landmark:
                    x = int(landmark.x * w)
                    y = int(landmark.y * h)
                    points.append((x, y))
                
                return points
            
            return None
            
        except Exception as e:
            logger.error(f"面部关键点检测失败: {str(e)}")
            return None
    
    def get_mouth_region(self, landmarks, image_shape):
        """获取嘴部区域"""
        if not landmarks:
            return None
            
        try:
            h, w = image_shape[:2]
            
            # 获取嘴部关键点
            mouth_points = [landmarks[i] for i in self.MOUTH_LANDMARKS if i < len(landmarks)]
            
            if not mouth_points:
                return None
            
            # 计算嘴部边界框
            xs = [p[0] for p in mouth_points]
            ys = [p[1] for p in mouth_points]
            
            x_min, x_max = max(0, min(xs) - 10), min(w, max(xs) + 10)
            y_min, y_max = max(0, min(ys) - 10), min(h, max(ys) + 10)
            
            return {
                'bbox': (x_min, y_min, x_max, y_max),
                'landmarks': mouth_points,
                'upper_lip': [landmarks[i] for i in self.UPPER_LIP if i < len(landmarks)],
                'lower_lip': [landmarks[i] for i in self.LOWER_LIP if i < len(landmarks)]
            }
            
        except Exception as e:
            logger.error(f"获取嘴部区域失败: {str(e)}")
            return None

def extract_audio_features(audio_path, fps=25):
    """提取音频特征"""
    try:
        logger.info("🎵 提取音频特征...")
        
        # 加载音频
        audio, sr = librosa.load(audio_path, sr=16000)
        duration = len(audio) / sr
        total_frames = int(duration * fps)
        
        # 提取特征
        hop_length = int(sr / fps)
        
        # MFCC 特征
        mfcc = librosa.feature.mfcc(y=audio, sr=sr, n_mfcc=13, hop_length=hop_length)
        
        # RMS 能量
        rms = librosa.feature.rms(y=audio, hop_length=hop_length)[0]
        
        # 谱质心
        spectral_centroids = librosa.feature.spectral_centroid(y=audio, sr=sr, hop_length=hop_length)[0]
        
        # 零交叉率
        zcr = librosa.feature.zero_crossing_rate(y=audio, hop_length=hop_length)[0]
        
        # 谱带宽
        spectral_bandwidth = librosa.feature.spectral_bandwidth(y=audio, sr=sr, hop_length=hop_length)[0]
        
        features = []
        for i in range(total_frames):
            idx = min(i, mfcc.shape[1] - 1)
            
            frame_features = {
                'mfcc': mfcc[:, idx],
                'rms': rms[idx],
                'spectral_centroid': spectral_centroids[idx],
                'zcr': zcr[idx],
                'spectral_bandwidth': spectral_bandwidth[idx],
                'time': i / fps
            }
            features.append(frame_features)
        
        logger.info(f"✅ 提取了 {len(features)} 帧音频特征")
        return features, duration
        
    except Exception as e:
        logger.error(f"音频特征提取失败: {str(e)}")
        return None, 0

def predict_mouth_deformation(audio_features):
    """预测嘴部变形参数"""
    try:
        logger.info("👄 预测嘴部变形...")
        
        deformations = []
        
        for i, features in enumerate(audio_features):
            mfcc = features['mfcc']
            rms = features['rms']
            spectral_centroid = features['spectral_centroid']
            zcr = features['zcr']
            spectral_bandwidth = features['spectral_bandwidth']
            
            # 计算嘴部开合程度 (基于 RMS 和第一个 MFCC)
            mouth_openness = np.clip(rms * 4.0 + abs(mfcc[0]) * 0.1, 0, 1)
            
            # 计算嘴部宽度 (基于谱质心和谱带宽)
            mouth_width = np.clip(spectral_centroid / 3000.0 + spectral_bandwidth / 2000.0, 0.8, 1.3)
            
            # 计算嘴部形状 (基于 MFCC 系数)
            mouth_shape = np.clip((mfcc[1] + mfcc[2]) * 0.05 + 0.5, 0, 1)
            
            # 计算上下嘴唇分离度 (基于零交叉率和 RMS)
            lip_separation = np.clip(zcr * 5.0 + rms * 2.0, 0, 1)
            
            # 计算嘴角上扬/下垂 (基于高频 MFCC)
            mouth_corner = np.clip(np.mean(mfcc[6:9]) * 0.1, -0.3, 0.3)
            
            # 添加时间平滑
            if i > 0:
                prev = deformations[-1]
                smooth_factor = 0.7
                mouth_openness = smooth_factor * prev['mouth_openness'] + (1 - smooth_factor) * mouth_openness
                mouth_width = smooth_factor * prev['mouth_width'] + (1 - smooth_factor) * mouth_width
            
            deformation = {
                'mouth_openness': mouth_openness,
                'mouth_width': mouth_width,
                'mouth_shape': mouth_shape,
                'lip_separation': lip_separation,
                'mouth_corner': mouth_corner,
                'frame_index': i
            }
            
            deformations.append(deformation)
        
        logger.info(f"✅ 预测了 {len(deformations)} 帧嘴部变形")
        return deformations
        
    except Exception as e:
        logger.error(f"嘴部变形预测失败: {str(e)}")
        return []

def apply_mouth_deformation(image, mouth_region, deformation):
    """应用嘴部变形"""
    try:
        if not mouth_region:
            return image
        
        result_image = image.copy()
        
        # 获取变形参数
        openness = deformation['mouth_openness']
        width_factor = deformation['mouth_width']
        shape_factor = deformation['mouth_shape']
        separation = deformation['lip_separation']
        corner_factor = deformation['mouth_corner']
        
        # 获取嘴部区域
        x_min, y_min, x_max, y_max = mouth_region['bbox']
        upper_lip = mouth_region['upper_lip']
        lower_lip = mouth_region['lower_lip']
        
        if not upper_lip or not lower_lip:
            return image
        
        # 计算嘴部中心
        all_points = upper_lip + lower_lip
        center_x = int(np.mean([p[0] for p in all_points]))
        center_y = int(np.mean([p[1] for p in all_points]))
        
        # 创建变形后的关键点
        new_upper_lip = []
        new_lower_lip = []
        
        # 变形上嘴唇
        for point in upper_lip:
            x, y = point
            # 相对于中心的偏移
            dx = x - center_x
            dy = y - center_y
            
            # 应用宽度变形
            new_x = center_x + dx * width_factor
            
            # 应用开合变形 (上嘴唇向上移动)
            new_y = y - separation * 3 + corner_factor * abs(dx) * 0.1
            
            new_upper_lip.append((int(new_x), int(new_y)))
        
        # 变形下嘴唇
        for point in lower_lip:
            x, y = point
            dx = x - center_x
            dy = y - center_y
            
            # 应用宽度变形
            new_x = center_x + dx * width_factor
            
            # 应用开合变形 (下嘴唇向下移动)
            new_y = y + openness * 8 + separation * 2 - corner_factor * abs(dx) * 0.1
            
            new_lower_lip.append((int(new_x), int(new_y)))
        
        # 绘制新的嘴唇轮廓
        if len(new_upper_lip) >= 3 and len(new_lower_lip) >= 3:
            # 上嘴唇
            upper_points = np.array(new_upper_lip, dtype=np.int32)
            cv2.fillPoly(result_image, [upper_points], (120, 80, 100))
            cv2.polylines(result_image, [upper_points], True, (100, 60, 80), 1)
            
            # 下嘴唇
            lower_points = np.array(new_lower_lip, dtype=np.int32)
            cv2.fillPoly(result_image, [lower_points], (110, 70, 90))
            cv2.polylines(result_image, [lower_points], True, (90, 50, 70), 1)
            
            # 如果嘴巴张开，绘制口腔内部
            if openness > 0.3:
                # 创建口腔内部区域
                mouth_interior = []
                mouth_interior.extend(new_upper_lip)
                mouth_interior.extend(reversed(new_lower_lip))
                
                if len(mouth_interior) >= 3:
                    interior_points = np.array(mouth_interior, dtype=np.int32)
                    cv2.fillPoly(result_image, [interior_points], (20, 20, 40))
        
        return result_image
        
    except Exception as e:
        logger.warning(f"嘴部变形应用失败: {str(e)}")
        return image

def generate_advanced_musetalk_frames(source_image, audio_features, deformations, output_dir, fps=25):
    """生成高级 MuseTalk 风格帧"""
    try:
        # 读取源图像
        image = cv2.imread(source_image)
        if image is None:
            raise ValueError(f"无法读取图像: {source_image}")
        
        # 初始化面部检测器
        detector = FacialLandmarkDetector()
        
        # 检测面部关键点
        landmarks = detector.detect_landmarks(image)
        if not landmarks:
            logger.warning("未检测到面部关键点，使用估算位置")
            # 使用估算的嘴部位置
            h, w = image.shape[:2]
            mouth_region = {
                'bbox': (int(w*0.35), int(h*0.6), int(w*0.65), int(h*0.8)),
                'landmarks': [],
                'upper_lip': [],
                'lower_lip': []
            }
        else:
            mouth_region = detector.get_mouth_region(landmarks, image.shape)
        
        total_frames = len(deformations)
        logger.info(f"生成 {total_frames} 帧高级 MuseTalk 动画")
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 生成每一帧
        for i, deformation in enumerate(deformations):
            # 应用嘴部变形
            frame = apply_mouth_deformation(image, mouth_region, deformation)
            
            # 保存帧
            frame_path = os.path.join(output_dir, f"frame_{i:06d}.png")
            cv2.imwrite(frame_path, frame)
            
            if i % 50 == 0:
                logger.info(f"生成进度: {i}/{total_frames} ({i/total_frames*100:.1f}%)")
        
        logger.info(f"✅ 生成了 {total_frames} 帧高级 MuseTalk 动画")
        return total_frames
        
    except Exception as e:
        logger.error(f"生成高级 MuseTalk 帧失败: {str(e)}")
        raise

def create_video_from_frames(frames_dir, audio_path, output_path, fps=25, quality='high'):
    """从帧序列创建视频"""
    try:
        frame_files = sorted([f for f in os.listdir(frames_dir) if f.startswith('frame_') and f.endswith('.png')])
        if not frame_files:
            logger.error(f"未找到帧文件在目录: {frames_dir}")
            return False
        
        logger.info(f"找到 {len(frame_files)} 个帧文件")
        
        quality_settings = {
            'low': {'crf': '28', 'preset': 'fast'},
            'medium': {'crf': '23', 'preset': 'medium'},
            'high': {'crf': '18', 'preset': 'slow'}
        }
        
        settings = quality_settings.get(quality, quality_settings['high'])
        
        output_dir = os.path.dirname(output_path)
        if output_dir:
            os.makedirs(output_dir, exist_ok=True)
        
        cmd = [
            "ffmpeg", "-y",
            "-framerate", str(fps),
            "-i", os.path.join(frames_dir, "frame_%06d.png"),
            "-i", audio_path,
            "-c:v", "libx264",
            "-c:a", "aac",
            "-pix_fmt", "yuv420p",
            "-shortest",
            "-movflags", "+faststart",
            "-preset", settings['preset'],
            "-crf", settings['crf'],
            "-r", str(fps),
            output_path
        ]
        
        logger.info(f"创建高级 MuseTalk 视频: {output_path}")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            if os.path.exists(output_path) and os.path.getsize(output_path) > 1024:
                logger.info(f"✅ 高级 MuseTalk 视频创建成功: {output_path}")
                logger.info(f"视频文件大小: {os.path.getsize(output_path)} bytes")
                return True
            else:
                logger.error(f"生成的视频文件无效: {output_path}")
                return False
        else:
            logger.error(f"FFmpeg 执行失败: {result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"视频创建失败: {str(e)}")
        return False

def main():
    """主函数"""
    try:
        args = parse_args()
        
        logger.info("🎬 开始高级 MuseTalk 数字人生成...")
        logger.info(f"源图像: {args.source_image}")
        logger.info(f"驱动音频: {args.driving_audio}")
        logger.info(f"输出视频: {args.output}")
        
        # 检查输入文件
        if not os.path.exists(args.source_image):
            raise FileNotFoundError(f"源图像不存在: {args.source_image}")
        
        if not os.path.exists(args.driving_audio):
            raise FileNotFoundError(f"驱动音频不存在: {args.driving_audio}")
        
        # 创建临时目录
        with tempfile.TemporaryDirectory() as temp_dir:
            frames_dir = os.path.join(temp_dir, "frames")
            
            # 1. 提取音频特征
            audio_features, duration = extract_audio_features(args.driving_audio, args.fps)
            if not audio_features:
                raise ValueError("音频特征提取失败")
            
            # 2. 预测嘴部变形
            deformations = predict_mouth_deformation(audio_features)
            if not deformations:
                raise ValueError("嘴部变形预测失败")
            
            # 3. 生成高级 MuseTalk 风格帧
            logger.info("🎨 生成高级 MuseTalk 风格说话帧...")
            total_frames = generate_advanced_musetalk_frames(
                args.source_image, 
                audio_features,
                deformations,
                frames_dir, 
                args.fps
            )
            
            # 4. 创建视频
            logger.info("🎬 合成最终的高级 MuseTalk 视频...")
            success = create_video_from_frames(
                frames_dir,
                args.driving_audio,
                args.output,
                args.fps,
                args.quality
            )
            
            if success:
                logger.info("🎉 高级 MuseTalk 数字人生成完成！")
                logger.info(f"输出文件: {args.output}")
                sys.exit(0)
            else:
                logger.error("❌ 高级 MuseTalk 数字人生成失败")
                sys.exit(1)
        
    except Exception as e:
        logger.error(f"高级 MuseTalk 推理失败: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
