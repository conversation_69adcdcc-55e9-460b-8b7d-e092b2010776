"""
模型辅助函数 - 提供通用的模型数据处理功能
"""
import json
import logging
from typing import Any, Dict, List, Optional, Union, Type

# 配置日志记录器
logger = logging.getLogger("model_helpers")

def safe_json_loads(
    json_str: Optional[str], 
    default_value: Any = None, 
    expected_type: Optional[Type] = None,
    field_name: str = "未知字段", 
    entity_id: Optional[str] = None
) -> Any:
    """
    安全地解析JSON字符串，提供详细的错误处理和日志记录
    
    Args:
        json_str: 要解析的JSON字符串
        default_value: 解析失败时返回的默认值
        expected_type: 期望的结果类型（如dict, list等）
        field_name: 字段名称，用于日志记录
        entity_id: 实体ID，用于日志记录
        
    Returns:
        解析后的对象或默认值
    """
    # 如果输入为空，直接返回默认值
    if not json_str:
        return default_value if default_value is not None else ({} if expected_type is dict else [] if expected_type is list else None)
    
    # 尝试解析JSON字符串
    try:
        parsed_value = json.loads(json_str)
        
        # 如果指定了期望类型，检查解析结果是否符合期望
        if expected_type and not isinstance(parsed_value, expected_type):
            entity_info = f"ID {entity_id}" if entity_id else "未知实体"
            logger.warning(f"{entity_info}: {field_name}不是{expected_type.__name__}类型: {parsed_value}")
            
            # 对特定类型进行转换处理
            if expected_type is dict:
                # 如果期望字典但不是字典，创建包含原始值的字典
                if isinstance(parsed_value, str):
                    return {"value": parsed_value}
                else:
                    return {"raw": str(parsed_value)}
            
            elif expected_type is list:
                # 如果期望列表但不是列表，尝试转换
                if isinstance(parsed_value, str):
                    return [parsed_value]
                else:
                    return [str(parsed_value)]
            
            # 其他类型，返回默认值
            return default_value if default_value is not None else ({} if expected_type is dict else [] if expected_type is list else None)
        
        return parsed_value
        
    except json.JSONDecodeError as e:
        entity_info = f"ID {entity_id}" if entity_id else "未知实体"
        logger.error(f"{entity_info}: 解析{field_name} JSON失败: {e}, 原始值: {json_str}")
        return default_value if default_value is not None else ({} if expected_type is dict else [] if expected_type is list else None)
    
    except Exception as e:
        entity_info = f"ID {entity_id}" if entity_id else "未知实体"
        logger.error(f"{entity_info}: 处理{field_name}时出现未知错误: {e}")
        return default_value if default_value is not None else ({} if expected_type is dict else [] if expected_type is list else None) 