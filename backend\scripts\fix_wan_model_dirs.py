#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WAN模型目录结构修复工具

该脚本用于检查和修复WAN模型目录结构问题，创建缺失的子目录和配置文件。
主要解决的错误: "模型目录结构不完整，缺少以下子目录: scheduler, unet"
"""

import os
import sys
import argparse
import logging
import traceback
import platform
from pathlib import Path
from typing import List, Dict, Tuple, Optional

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_directory_permissions(dir_path: str) -> Tuple[bool, bool, bool, str]:
    """
    检查目录的权限状态
    
    Args:
        dir_path: 要检查的目录路径
        
    Returns:
        Tuple:
            - 目录是否存在
            - 是否有读权限
            - 是否有写权限
            - 诊断信息
    """
    exists = os.path.exists(dir_path)
    readable = False
    writable = False
    msg = ""
    
    if not exists:
        msg = f"目录不存在: {dir_path}"
        return exists, readable, writable, msg
    
    if not os.path.isdir(dir_path):
        msg = f"路径不是目录: {dir_path}"
        return exists, readable, writable, msg
    
    readable = os.access(dir_path, os.R_OK)
    writable = os.access(dir_path, os.W_OK)
    
    if readable and writable:
        msg = f"目录存在且拥有读写权限: {dir_path}"
    elif readable and not writable:
        msg = f"目录存在但只读，无写入权限: {dir_path}"
    elif not readable and writable:
        msg = f"目录存在但只写，无读取权限: {dir_path}"
    else:
        msg = f"目录存在但无读写权限: {dir_path}"
    
    return exists, readable, writable, msg

def check_model_directory_structure(model_dir: str) -> Tuple[bool, List[str]]:
    """
    检查模型目录结构是否完整
    
    Args:
        model_dir: 模型目录路径
        
    Returns:
        Tuple:
            - 是否完整
            - 缺失的子目录列表
    """
    logger.info(f"检查模型目录结构: {model_dir}")
    
    if not os.path.exists(model_dir):
        logger.error(f"模型目录不存在: {model_dir}")
        return False, ["目录不存在"]
    
    # 必需的子目录
    required_subdirs = ["vae", "scheduler", "unet"]
    missing_subdirs = []
    
    for subdir in required_subdirs:
        subdir_path = os.path.join(model_dir, subdir)
        if not os.path.exists(subdir_path) or not os.path.isdir(subdir_path):
            missing_subdirs.append(subdir)
            logger.warning(f"缺少子目录: {subdir}")
    
    if not missing_subdirs:
        logger.info(f"模型目录结构完整: {model_dir}")
        return True, []
    
    logger.warning(f"模型目录结构不完整，缺少以下子目录: {', '.join(missing_subdirs)}")
    return False, missing_subdirs

def create_sample_config_files(model_dir: str) -> bool:
    """
    创建基本配置文件样例，以避免初始化错误
    
    Args:
        model_dir: 模型目录路径
        
    Returns:
        bool: 是否成功创建配置文件
    """
    try:
        # 创建scheduler子目录
        scheduler_dir = os.path.join(model_dir, "scheduler")
        if not os.path.exists(scheduler_dir):
            os.makedirs(scheduler_dir, exist_ok=True)
            logger.info(f"创建scheduler子目录: {scheduler_dir}")
        
        # 创建unet子目录
        unet_dir = os.path.join(model_dir, "unet")
        if not os.path.exists(unet_dir):
            os.makedirs(unet_dir, exist_ok=True)
            logger.info(f"创建unet子目录: {unet_dir}")
        
        # 创建vae子目录
        vae_dir = os.path.join(model_dir, "vae")
        if not os.path.exists(vae_dir):
            os.makedirs(vae_dir, exist_ok=True)
            logger.info(f"创建vae子目录: {vae_dir}")

        # 创建scheduler配置文件
        scheduler_config = os.path.join(scheduler_dir, "scheduler_config.json")
        if not os.path.exists(scheduler_config):
            with open(scheduler_config, 'w') as f:
                f.write('{"beta_schedule": "linear", "beta_start": 0.00085, "beta_end": 0.012, "clip_sample": false}')
            logger.info(f"创建调度器配置文件: {scheduler_config}")
            
        # 创建VAE配置文件
        vae_config = os.path.join(vae_dir, "config.json")
        if not os.path.exists(vae_config):
            with open(vae_config, 'w') as f:
                f.write('{"sample_size": 32, "latent_channels": 4, "scaling_factor": 0.18215}')
            logger.info(f"创建VAE配置文件: {vae_config}")
            
        # 创建UNet配置文件
        unet_config = os.path.join(unet_dir, "config.json")
        if not os.path.exists(unet_config):
            with open(unet_config, 'w') as f:
                f.write('{"sample_size": 32, "in_channels": 4, "out_channels": 4, "layers_per_block": 2}')
            logger.info(f"创建UNet配置文件: {unet_config}")
            
        return True
        
    except Exception as e:
        logger.error(f"创建配置文件样例失败: {e}")
        logger.error(traceback.format_exc())
        return False

def find_model_cache_dir() -> str:
    """
    查找最佳的模型缓存目录
    
    Returns:
        str: 模型缓存目录路径
    """
    # 检查环境变量
    if "WAN_MODEL_CACHE_DIR" in os.environ:
        cache_dir = os.environ["WAN_MODEL_CACHE_DIR"]
        logger.info(f"从环境变量中获取模型缓存目录: {cache_dir}")
        return cache_dir
        
    # 查找可能的缓存目录位置
    script_dir = os.path.dirname(os.path.abspath(__file__))
    backend_dir = os.path.dirname(script_dir)
    possible_dirs = [
        os.path.join(backend_dir, "models_cache"),
        os.path.join(backend_dir, "local_models", "wan"),
        os.path.join(os.path.expanduser("~"), ".wan_models")
    ]
    
    for cache_dir in possible_dirs:
        if os.path.exists(cache_dir) and os.path.isdir(cache_dir):
            logger.info(f"找到模型缓存目录: {cache_dir}")
            return cache_dir
    
    # 如果都不存在，使用默认位置并创建
    default_dir = possible_dirs[0]
    logger.info(f"使用默认模型缓存目录: {default_dir}")
    return default_dir

def fix_model_directories(model_cache_dir: Optional[str] = None) -> Dict[str, bool]:
    """
    修复所有模型目录结构
    
    Args:
        model_cache_dir: 可选，模型缓存目录路径
        
    Returns:
        Dict[str, bool]: 每个模型目录的修复结果
    """
    # 确定模型缓存目录
    cache_dir = model_cache_dir or find_model_cache_dir()
    logger.info(f"WAN模型缓存目录: {cache_dir}")
    
    # 确保缓存目录存在
    if not os.path.exists(cache_dir):
        try:
            os.makedirs(cache_dir, exist_ok=True)
            logger.info(f"创建模型缓存目录: {cache_dir}")
        except Exception as e:
            logger.error(f"创建模型缓存目录失败: {e}")
            return {"error": False}
    
    # 获取目录权限
    exists, readable, writable, msg = check_directory_permissions(cache_dir)
    logger.info(f"模型缓存目录状态: {msg}")
    
    if not (exists and readable and writable):
        logger.error(f"模型缓存目录权限不足: {cache_dir}")
        return {"error": False}
    
    # 定义模型目录名称
    model_dirs = [
        ("Wan2.1-T2V-14B", "T2V-14B"),
        ("Wan2.1-T2V-1.3B", "T2V-1.3B"),
        ("Wan2.1-I2V-720P", "I2V-720P"),
        ("Wan2.1-I2V-480P", "I2V-480P")
    ]
    
    results = {}
    
    # 检查和修复每个模型目录
    for model_dir_name, model_type in model_dirs:
        model_dir = os.path.join(cache_dir, model_dir_name)
        
        # 确保模型目录存在
        if not os.path.exists(model_dir):
            try:
                os.makedirs(model_dir, exist_ok=True)
                logger.info(f"创建{model_type}模型目录: {model_dir}")
            except Exception as e:
                logger.error(f"创建{model_type}模型目录失败: {e}")
                results[model_dir_name] = False
                continue
        
        # 检查目录结构
        complete, missing_subdirs = check_model_directory_structure(model_dir)
        
        if not complete:
            logger.info(f"修复{model_type}模型目录结构...")
            if create_sample_config_files(model_dir):
                # 验证修复结果
                complete, missing = check_model_directory_structure(model_dir)
                results[model_dir_name] = complete
                if complete:
                    logger.info(f"{model_type}模型目录结构修复成功")
                else:
                    logger.error(f"{model_type}模型目录结构修复失败，仍缺少: {', '.join(missing)}")
            else:
                logger.error(f"{model_type}模型目录结构修复失败")
                results[model_dir_name] = False
        else:
            logger.info(f"{model_type}模型目录结构已完整")
            results[model_dir_name] = True
    
    return results

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="WAN模型目录结构修复工具")
    parser.add_argument("--dir", dest="model_cache_dir", help="模型缓存目录路径")
    parser.add_argument("--verbose", "-v", action="store_true", help="显示详细日志")
    
    args = parser.parse_args()
    
    # 设置日志级别
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
        
    print("\n===== WAN模型目录结构修复工具 =====\n")
    
    # 显示系统信息
    print(f"系统信息:")
    print(f"  操作系统: {platform.system()} {platform.release()}")
    print(f"  Python版本: {platform.python_version()}")
    print(f"  当前用户: {os.getlogin() if hasattr(os, 'getlogin') else '未知'}")
    print(f"  工作目录: {os.getcwd()}\n")
    
    try:
        results = fix_model_directories(args.model_cache_dir)
        
        print("\n修复结果:")
        all_success = True
        for model, success in results.items():
            status = "成功" if success else "失败"
            print(f"  {model}: {status}")
            if not success:
                all_success = False
        
        if all_success:
            print("\n所有模型目录结构已成功修复！")
        else:
            print("\n警告: 部分模型目录结构修复失败，请查看日志获取详细信息。")
            
        print("\n请重新启动应用程序以应用修复。")
        
    except Exception as e:
        logger.error(f"修复过程中出现错误: {e}")
        logger.error(traceback.format_exc())
        print(f"\n错误: 修复过程中出现异常: {e}")
        return 1
    
    print("\n===== 修复过程完成 =====\n")
    return 0

if __name__ == "__main__":
    sys.exit(main()) 