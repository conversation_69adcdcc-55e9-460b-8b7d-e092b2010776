"""
WebSocket实时推送服务
"""

import asyncio
import json
from typing import Dict, List, Set
from fastapi import WebSocket, WebSocketDisconnect
from datetime import datetime
import logging

logger = logging.getLogger(__name__)


class ConnectionManager:
    """WebSocket连接管理器"""
    
    def __init__(self):
        # 存储活跃连接
        self.active_connections: Dict[str, WebSocket] = {}
        # 用户订阅的频道
        self.user_channels: Dict[str, Set[str]] = {}
        # 频道的订阅用户
        self.channel_users: Dict[str, Set[str]] = {}
    
    async def connect(self, websocket: WebSocket, user_id: str):
        """建立WebSocket连接"""
        await websocket.accept()
        self.active_connections[user_id] = websocket
        self.user_channels[user_id] = set()
        logger.info(f"用户 {user_id} 建立WebSocket连接")
    
    def disconnect(self, user_id: str):
        """断开WebSocket连接"""
        if user_id in self.active_connections:
            # 从所有频道中移除用户
            for channel in self.user_channels.get(user_id, set()):
                if channel in self.channel_users:
                    self.channel_users[channel].discard(user_id)
                    if not self.channel_users[channel]:
                        del self.channel_users[channel]
            
            # 清理用户数据
            del self.active_connections[user_id]
            if user_id in self.user_channels:
                del self.user_channels[user_id]
            
            logger.info(f"用户 {user_id} 断开WebSocket连接")
    
    async def subscribe_channel(self, user_id: str, channel: str):
        """订阅频道"""
        if user_id in self.user_channels:
            self.user_channels[user_id].add(channel)
            
            if channel not in self.channel_users:
                self.channel_users[channel] = set()
            self.channel_users[channel].add(user_id)
            
            logger.info(f"用户 {user_id} 订阅频道 {channel}")
    
    async def unsubscribe_channel(self, user_id: str, channel: str):
        """取消订阅频道"""
        if user_id in self.user_channels:
            self.user_channels[user_id].discard(channel)
            
            if channel in self.channel_users:
                self.channel_users[channel].discard(user_id)
                if not self.channel_users[channel]:
                    del self.channel_users[channel]
            
            logger.info(f"用户 {user_id} 取消订阅频道 {channel}")
    
    async def send_personal_message(self, user_id: str, message: dict):
        """发送个人消息"""
        if user_id in self.active_connections:
            try:
                websocket = self.active_connections[user_id]
                await websocket.send_text(json.dumps(message, ensure_ascii=False))
            except Exception as e:
                logger.error(f"发送个人消息失败 {user_id}: {str(e)}")
                self.disconnect(user_id)
    
    async def broadcast_to_channel(self, channel: str, message: dict):
        """向频道广播消息"""
        if channel in self.channel_users:
            disconnected_users = []
            
            for user_id in self.channel_users[channel]:
                if user_id in self.active_connections:
                    try:
                        websocket = self.active_connections[user_id]
                        await websocket.send_text(json.dumps(message, ensure_ascii=False))
                    except Exception as e:
                        logger.error(f"广播消息失败 {user_id}: {str(e)}")
                        disconnected_users.append(user_id)
            
            # 清理断开的连接
            for user_id in disconnected_users:
                self.disconnect(user_id)
    
    async def broadcast_to_all(self, message: dict):
        """向所有连接广播消息"""
        disconnected_users = []
        
        for user_id, websocket in self.active_connections.items():
            try:
                await websocket.send_text(json.dumps(message, ensure_ascii=False))
            except Exception as e:
                logger.error(f"全局广播失败 {user_id}: {str(e)}")
                disconnected_users.append(user_id)
        
        # 清理断开的连接
        for user_id in disconnected_users:
            self.disconnect(user_id)
    
    def get_connection_stats(self) -> dict:
        """获取连接统计"""
        return {
            'total_connections': len(self.active_connections),
            'total_channels': len(self.channel_users),
            'channels': {
                channel: len(users) 
                for channel, users in self.channel_users.items()
            }
        }


class OpinionWebSocketService:
    """舆情WebSocket服务"""
    
    def __init__(self):
        self.manager = ConnectionManager()
        self.alert_queue = asyncio.Queue()
        self.is_running = False
    
    async def start_alert_processor(self):
        """启动预警处理器"""
        if not self.is_running:
            self.is_running = True
            asyncio.create_task(self._process_alerts())
            logger.info("预警处理器已启动")
    
    async def stop_alert_processor(self):
        """停止预警处理器"""
        self.is_running = False
        logger.info("预警处理器已停止")
    
    async def _process_alerts(self):
        """处理预警队列"""
        while self.is_running:
            try:
                # 从队列中获取预警
                alert = await asyncio.wait_for(self.alert_queue.get(), timeout=1.0)
                await self._handle_alert(alert)
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                logger.error(f"处理预警失败: {str(e)}")
    
    async def _handle_alert(self, alert: dict):
        """处理单个预警"""
        try:
            alert_type = alert.get('type', 'general')
            user_id = alert.get('user_id')
            
            # 构建推送消息
            message = {
                'type': 'alert',
                'data': alert,
                'timestamp': datetime.now().isoformat()
            }
            
            if user_id:
                # 发送给特定用户
                await self.manager.send_personal_message(user_id, message)
            else:
                # 广播给相关频道
                channel = f"alerts_{alert_type}"
                await self.manager.broadcast_to_channel(channel, message)
            
            logger.info(f"预警推送成功: {alert.get('title', 'Unknown')}")
            
        except Exception as e:
            logger.error(f"处理预警失败: {str(e)}")
    
    async def send_alert(self, alert_data: dict):
        """发送预警"""
        await self.alert_queue.put(alert_data)
    
    async def send_report_notification(self, user_id: str, report_data: dict):
        """发送简报通知"""
        message = {
            'type': 'report_ready',
            'data': report_data,
            'timestamp': datetime.now().isoformat()
        }
        await self.manager.send_personal_message(user_id, message)
    
    async def send_search_update(self, user_id: str, search_data: dict):
        """发送检索更新"""
        message = {
            'type': 'search_update',
            'data': search_data,
            'timestamp': datetime.now().isoformat()
        }
        await self.manager.send_personal_message(user_id, message)
    
    async def send_system_notification(self, notification_data: dict):
        """发送系统通知"""
        message = {
            'type': 'system_notification',
            'data': notification_data,
            'timestamp': datetime.now().isoformat()
        }
        await self.manager.broadcast_to_all(message)
    
    async def handle_websocket_message(self, user_id: str, message: dict):
        """处理WebSocket消息"""
        try:
            msg_type = message.get('type')
            data = message.get('data', {})
            
            if msg_type == 'subscribe':
                # 订阅频道
                channel = data.get('channel')
                if channel:
                    await self.manager.subscribe_channel(user_id, channel)
                    
                    # 发送订阅确认
                    response = {
                        'type': 'subscribe_success',
                        'data': {'channel': channel},
                        'timestamp': datetime.now().isoformat()
                    }
                    await self.manager.send_personal_message(user_id, response)
            
            elif msg_type == 'unsubscribe':
                # 取消订阅频道
                channel = data.get('channel')
                if channel:
                    await self.manager.unsubscribe_channel(user_id, channel)
                    
                    # 发送取消订阅确认
                    response = {
                        'type': 'unsubscribe_success',
                        'data': {'channel': channel},
                        'timestamp': datetime.now().isoformat()
                    }
                    await self.manager.send_personal_message(user_id, response)
            
            elif msg_type == 'ping':
                # 心跳检测
                response = {
                    'type': 'pong',
                    'timestamp': datetime.now().isoformat()
                }
                await self.manager.send_personal_message(user_id, response)
            
            elif msg_type == 'get_stats':
                # 获取连接统计
                stats = self.manager.get_connection_stats()
                response = {
                    'type': 'stats',
                    'data': stats,
                    'timestamp': datetime.now().isoformat()
                }
                await self.manager.send_personal_message(user_id, response)
            
            else:
                logger.warning(f"未知消息类型: {msg_type}")
        
        except Exception as e:
            logger.error(f"处理WebSocket消息失败: {str(e)}")
    
    async def simulate_real_time_data(self):
        """模拟实时数据推送"""
        while self.is_running:
            try:
                # 模拟舆情数据更新
                await asyncio.sleep(30)  # 每30秒推送一次
                
                # 模拟热点话题更新
                hot_topic_update = {
                    'type': 'hot_topic_update',
                    'data': {
                        'topic': '最新热点话题',
                        'heat_score': 95,
                        'trend': 'rising',
                        'related_count': 156
                    },
                    'timestamp': datetime.now().isoformat()
                }
                
                await self.manager.broadcast_to_channel('hot_topics', hot_topic_update)
                
                # 模拟数据统计更新
                stats_update = {
                    'type': 'stats_update',
                    'data': {
                        'total_articles': 12580,
                        'new_articles': 45,
                        'sentiment_positive': 68,
                        'sentiment_negative': 15
                    },
                    'timestamp': datetime.now().isoformat()
                }
                
                await self.manager.broadcast_to_channel('stats', stats_update)
                
            except Exception as e:
                logger.error(f"模拟实时数据推送失败: {str(e)}")


# 全局WebSocket服务实例
websocket_service = OpinionWebSocketService()
