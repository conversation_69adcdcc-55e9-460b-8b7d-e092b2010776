<template>
  <div class="online-user-container">
    <a-card title="在线用户" :bordered="false">
      <template #extra>
        <a-button type="primary" @click="refreshData">
          <template #icon><ReloadOutlined /></template>
          刷新
        </a-button>
      </template>
      
      <a-table
        :columns="columns"
        :data-source="onlineUsers"
        :loading="loading"
        :pagination="{ pageSize: 10 }"
        rowKey="sessionId"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'action'">
            <a-popconfirm
              title="确定要强制下线该用户吗?"
              @confirm="forceLogout(record)"
              ok-text="确定"
              cancel-text="取消"
            >
              <a-button type="link" danger>强制下线</a-button>
            </a-popconfirm>
          </template>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ReloadOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'

// 表格列定义
const columns = [
  {
    title: '会话ID',
    dataIndex: 'sessionId',
    key: 'sessionId',
    width: 180,
    ellipsis: true,
  },
  {
    title: '用户名',
    dataIndex: 'username',
    key: 'username',
  },
  {
    title: 'IP地址',
    dataIndex: 'ipAddress',
    key: 'ipAddress',
  },
  {
    title: '登录时间',
    dataIndex: 'loginTime',
    key: 'loginTime',
  },
  {
    title: '最后活动时间',
    dataIndex: 'lastActiveTime',
    key: 'lastActiveTime',
  },
  {
    title: '操作',
    key: 'action',
    width: 120,
    align: 'center',
  },
]

// 在线用户数据
const onlineUsers = ref([])
const loading = ref(false)

// 获取在线用户数据
const fetchOnlineUsers = async () => {
  loading.value = true
  try {
    // 这里应该调用实际的API，现在使用模拟数据
    // const response = await api.getOnlineUsers()
    // onlineUsers.value = response.data
    
    // 模拟数据
    setTimeout(() => {
      onlineUsers.value = [
        {
          sessionId: 'sid-8976543210',
          username: 'admin',
          ipAddress: '*************',
          loginTime: '2023-04-02 08:30:45',
          lastActiveTime: '2023-04-02 10:15:22',
        },
        {
          sessionId: 'sid-6543217890',
          username: 'user1',
          ipAddress: '*************',
          loginTime: '2023-04-02 09:05:12',
          lastActiveTime: '2023-04-02 10:12:35',
        },
        {
          sessionId: 'sid-1234567890',
          username: 'guest',
          ipAddress: '*************',
          loginTime: '2023-04-02 09:45:30',
          lastActiveTime: '2023-04-02 10:10:15',
        }
      ]
      loading.value = false
    }, 500)
  } catch (error) {
    console.error('获取在线用户失败:', error)
    message.error('获取在线用户数据失败')
    loading.value = false
  }
}

// 刷新数据
const refreshData = () => {
  fetchOnlineUsers()
}

// 强制登出用户
const forceLogout = async (user) => {
  try {
    // 这里应该调用实际的API
    // await api.forceLogout(user.sessionId)
    message.success(`已强制用户 ${user.username} 下线`)
    // 刷新列表
    fetchOnlineUsers()
  } catch (error) {
    console.error('强制下线用户失败:', error)
    message.error('操作失败，请重试')
  }
}

// 组件挂载时获取数据
onMounted(() => {
  fetchOnlineUsers()
})
</script>

<style lang="less" scoped>
.online-user-container {
  padding: 24px;
  background-color: #f0f2f5;
  min-height: 100%;
}
</style> 