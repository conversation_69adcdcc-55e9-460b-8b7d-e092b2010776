"""
智能会话管理器，用于管理数据库会话并防止连接泄漏
"""
import logging
import threading
import time
import traceback
from contextlib import contextmanager
from typing import Dict, Any, Optional
import uuid

from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError

# 导入数据库工具
from utils.db import get_db

# 配置日志
logger = logging.getLogger(__name__)

# 会话跟踪字典
_session_tracking = {}
_session_lock = threading.RLock()

class SessionTracker:
    """会话跟踪器，用于监控会话使用情况"""
    
    def __init__(self):
        self.session_id = str(uuid.uuid4())
        self.thread_id = id(threading.current_thread())
        self.thread_name = threading.current_thread().name
        self.start_time = time.time()
        self.last_activity = time.time()
        self.operation_count = 0
        self.stack_trace = traceback.format_stack()
        self.closed = False
        self.error = None
    
    def update_activity(self):
        """更新最后活动时间"""
        self.last_activity = time.time()
        self.operation_count += 1
    
    def close(self, error=None):
        """标记会话已关闭"""
        self.closed = True
        self.error = error
        
    def get_duration(self):
        """获取会话持续时间"""
        return time.time() - self.start_time
    
    def get_info(self):
        """获取会话信息"""
        return {
            "session_id": self.session_id,
            "thread_id": self.thread_id,
            "thread_name": self.thread_name,
            "start_time": self.start_time,
            "last_activity": self.last_activity,
            "duration": self.get_duration(),
            "operation_count": self.operation_count,
            "closed": self.closed,
            "error": str(self.error) if self.error else None,
            "stack_trace": self.stack_trace
        }

@contextmanager
def managed_session():
    """
    智能会话管理器，自动跟踪会话使用情况
    
    使用方式:
    with managed_session() as session:
        # 使用session进行数据库操作
    """
    db_gen = get_db()
    session = None
    session_tracker = None
    
    try:
        # 获取会话
        session = next(db_gen)
        
        # 创建会话跟踪器
        session_tracker = SessionTracker()
        session_id = session_tracker.session_id
        
        # 记录会话
        with _session_lock:
            _session_tracking[session_id] = session_tracker
        
        # 提供会话
        yield session
        
        # 提交会话
        session.commit()
        
        # 更新会话状态
        session_tracker.update_activity()
        
    except SQLAlchemyError as e:
        # 记录错误
        logger.error(f"数据库会话错误: {e}")
        if session:
            session.rollback()
        if session_tracker:
            session_tracker.close(error=e)
        raise
        
    except Exception as e:
        # 记录其他异常
        logger.exception(f"会话管理器异常: {e}")
        if session:
            session.rollback()
        if session_tracker:
            session_tracker.close(error=e)
        raise
        
    finally:
        # 关闭会话
        if session:
            session.close()
        
        # 更新会话状态
        if session_tracker:
            session_tracker.close()
            
            # 从跟踪字典中移除会话（如果持续时间超过10分钟，保留记录用于诊断）
            with _session_lock:
                if session_tracker.get_duration() > 600:  # 10分钟
                    # 保留记录但标记为已关闭
                    pass
                else:
                    # 完全移除记录
                    _session_tracking.pop(session_tracker.session_id, None)

def get_active_sessions():
    """获取活跃会话列表"""
    with _session_lock:
        active_sessions = []
        current_time = time.time()
        
        for session_id, tracker in _session_tracking.items():
            if not tracker.closed:
                session_info = tracker.get_info()
                # 添加额外信息
                session_info["inactive_time"] = current_time - tracker.last_activity
                active_sessions.append(session_info)
        
        return active_sessions

def get_long_running_sessions(threshold_seconds=60):
    """获取长时间运行的会话列表"""
    with _session_lock:
        long_sessions = []
        current_time = time.time()
        
        for session_id, tracker in _session_tracking.items():
            if not tracker.closed and (current_time - tracker.start_time) > threshold_seconds:
                session_info = tracker.get_info()
                # 添加额外信息
                session_info["inactive_time"] = current_time - tracker.last_activity
                long_sessions.append(session_info)
        
        return long_sessions

def cleanup_sessions():
    """清理过期会话记录"""
    with _session_lock:
        current_time = time.time()
        expired_ids = []
        
        for session_id, tracker in _session_tracking.items():
            # 清理已关闭超过1小时的会话记录
            if tracker.closed and (current_time - tracker.last_activity) > 3600:
                expired_ids.append(session_id)
        
        # 移除过期记录
        for session_id in expired_ids:
            _session_tracking.pop(session_id, None)
        
        return len(expired_ids) 