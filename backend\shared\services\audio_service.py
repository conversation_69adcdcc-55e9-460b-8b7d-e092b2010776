import os
import logging
import asyncio
from typing import Dict, Any, Optional, Tuple, List
from datetime import datetime

from .asr_service import ASRService
from .tts_service import TTSService
from .mt_service import MachineTranslationService
from .storage_manager import get_storage_manager
from .task_adapter import TaskAdapter
from .progress_updater import ProgressUpdater

logger = logging.getLogger(__name__)

class AudioService(TaskAdapter):
    """音频服务 - 集成音频转写、翻译和合成功能"""
    
    def __init__(self, progress_updater=None):
        """初始化音频服务"""
        self.asr_service = ASRService(model_size="small")
        self.mt_service = MachineTranslationService()
        self.tts_service = TTSService()
        self.storage_manager = get_storage_manager()
        
        # 确保必要的目录存在
        os.makedirs("temp", exist_ok=True)
        os.makedirs("results/audio", exist_ok=True)
        
        # 初始化父类
        super().__init__(None, progress_updater)
        
        logger.info("音频服务初始化完成")
    
    async def translate_audio(
        self,
        audio_path: str,
        source_lang: str = None,
        target_lang: str = "zh",
        quality: str = "standard",
        domain: str = "",
        output_format: str = "mp3",
        keep_original: bool = False,
        task_id: str = None
    ) -> Dict[str, Any]:
        """
        翻译音频文件
        
        Args:
            audio_path: 音频文件路径
            source_lang: 源语言代码，如果为None则自动检测
            target_lang: 目标语言代码
            quality: 翻译质量 (standard/premium)
            domain: 领域
            output_format: 输出音频格式
            keep_original: 是否保留原始音频
            task_id: 任务ID，用于进度更新
            
        Returns:
            Dict: 包含翻译结果的字典
        """
        try:
            # 验证音频文件
            if not os.path.exists(audio_path):
                raise FileNotFoundError(f"音频文件不存在: {audio_path}")
            
            file_size = os.path.getsize(audio_path)
            if file_size == 0:
                raise ValueError("音频文件为空")
            
            # 更新进度
            if task_id:
                await self.update_progress(task_id, 10, "开始音频转写...")
            
            # 1. 转写音频
            transcript, segments, detected_lang = await self.asr_service.transcribe_audio(
                audio_file_path=audio_path,
                source_language=source_lang
            )
            
            if not transcript:
                raise ValueError("音频转写失败")
            
            # 保存转写文本
            transcript_path = f"temp/{task_id}_transcript.txt" if task_id else f"temp/transcript_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            os.makedirs("temp", exist_ok=True)  # 确保temp目录存在
            with open(transcript_path, "w", encoding="utf-8") as f:
                f.write(transcript)
            
            # 更新进度
            if task_id:
                await self.update_progress(task_id, 40, "开始文本翻译...")
            
            # 2. 翻译文本
            translated_text = await self.mt_service.translate_text(
                text=transcript,
                source_language=detected_lang,
                target_language=target_lang,
                quality=quality,
                domain=domain
            )
            
            if not translated_text:
                raise ValueError("文本翻译失败")
            
            # 更新进度
            if task_id:
                await self.update_progress(task_id, 70, "开始生成翻译音频...")
            
            # 3. 生成翻译后的音频
            # 准备输出路径
            output_dir = os.path.join("results", "audio")
            os.makedirs(output_dir, exist_ok=True)
            
            # 生成输出文件名
            base_name = os.path.splitext(os.path.basename(audio_path))[0]
            translated_audio_path = os.path.join(output_dir, f"{base_name}_{target_lang}.{output_format}")
            
            # 生成语音
            tts_result = await self.tts_service.text_to_speech(
                text=translated_text,
                voice_id=f"{target_lang}-female1",  # 使用目标语言的默认女声
                output_path=translated_audio_path
            )
            
            if not tts_result or tts_result.get("status") != "success":
                raise ValueError("语音生成失败")
            
            # 4. 如果需要保留原始音频，合并两个音频文件
            if keep_original:
                # 准备合并后的输出路径
                merged_audio_path = os.path.join(output_dir, f"{base_name}_merged.{output_format}")
                
                # 合并音频文件
                await self.merge_audio_files(
                    original_path=audio_path,
                    translated_path=translated_audio_path,
                    output_path=merged_audio_path
                )
                
                # 使用合并后的文件作为最终输出
                translated_audio_path = merged_audio_path
            
            # 更新进度
            if task_id:
                await self.update_progress(task_id, 90, "处理完成，准备返回结果...")
            
            # 构建返回结果
            result = {
                "source_language": detected_lang,
                "target_language": target_lang,
                "original_text": transcript,
                "translated_text": translated_text,
                "translated_audio_path": translated_audio_path,
                "transcript_path": transcript_path,
                "segments": segments,
                "quality": quality,
                "domain": domain
            }
            
            # 更新最终进度
            if task_id:
                await self.update_progress(task_id, 100, "音频翻译完成")
            
            return result
            
        except Exception as e:
            logger.error(f"音频翻译失败: {e}")
            if task_id:
                await self.update_progress(task_id, 0, f"音频翻译失败: {str(e)}", status="failed")
            raise

    async def merge_audio_files(self, original_path: str, translated_path: str, output_path: str) -> str:
        """
        合并原始音频和翻译后的音频
        
        Args:
            original_path: 原始音频文件路径
            translated_path: 翻译后的音频文件路径
            output_path: 输出文件路径
            
        Returns:
            str: 合并后的音频文件路径
        """
        try:
            # 使用 pydub 合并音频文件
            from pydub import AudioSegment
            
            # 加载音频文件
            original_audio = AudioSegment.from_file(original_path)
            translated_audio = AudioSegment.from_file(translated_path)
            
            # 添加静音间隔
            silence = AudioSegment.silent(duration=1000)  # 1秒静音
            
            # 合并音频
            merged_audio = original_audio + silence + translated_audio
            
            # 导出合并后的音频
            merged_audio.export(output_path, format=output_path.split('.')[-1])
            
            return output_path
            
        except Exception as e:
            logger.error(f"合并音频文件失败: {e}")
            raise

# 全局单例
_audio_service = None

def get_audio_service() -> AudioService:
    """获取音频服务实例"""
    global _audio_service
    if _audio_service is None:
        _audio_service = AudioService()
    return _audio_service 