import os
import time
import uuid
import asyncio
import logging
from typing import List, Dict, Any, Optional, Tuple, Union
from datetime import datetime, timedelta
from pathlib import Path
import mimetypes
import shutil
import re
import tempfile
import json
from concurrent.futures import ThreadPoolExecutor
from sqlalchemy import select

import docx
import pdfplumber
from openpyxl import load_workbook
from pptx import Presentation
from fastapi import UploadFile, HTTPException

from models.document_translation import DocumentTranslation, DocumentTranslationUpdate
from services.translation_service import get_translation_service
from utils.db import get_db
from utils.file_utils import ensure_dir, get_file_extension, get_mime_type, count_words
from utils.common import generate_unique_id
from utils.task_persistence import TaskPersistenceManager

# 获取日志记录器
logger = logging.getLogger(__name__)

# 配置文件存储目录
UPLOAD_DIR = "storage/document_translation/uploads"
RESULT_DIR = "storage/document_translation/results"
PREVIEW_DIR = "storage/document_translation/previews"

# 确保目录存在 - 在初始化时创建
async def ensure_directories():
    """确保所有必要的目录都存在"""
    await ensure_dir(UPLOAD_DIR)
    await ensure_dir(RESULT_DIR)
    await ensure_dir(PREVIEW_DIR)

# 在应用启动时调用此函数
# 修复: 不要直接调用asyncio.create_task，而是创建一个同步初始化函数
def init_directories():
    """初始化目录 - 可以在同步上下文中调用"""
    import asyncio
    try:
        # 为不同环境创建不同的初始化方式
        if asyncio.get_event_loop().is_running():
            # 如果有事件循环在运行，使用create_task
            asyncio.create_task(ensure_directories())
        else:
            # 如果没有事件循环，创建一个新的来运行协程
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(ensure_directories())
            loop.close()
    except RuntimeError:
        # 如果出现"no running event loop"错误，使用新的事件循环
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        loop.run_until_complete(ensure_directories())
        loop.close()

# 调用初始化函数
init_directories()

# 支持的文件格式
SUPPORTED_FORMATS = [
    {
        "name": "Microsoft Word",
        "extensions": [".doc", ".docx"],
        "mimeTypes": ["application/msword", "application/vnd.openxmlformats-officedocument.wordprocessingml.document"]
    },
    {
        "name": "PDF",
        "extensions": [".pdf"],
        "mimeTypes": ["application/pdf"]
    },
    {
        "name": "Text",
        "extensions": [".txt"],
        "mimeTypes": ["text/plain"]
    },
    {
        "name": "Microsoft PowerPoint",
        "extensions": [".ppt", ".pptx"],
        "mimeTypes": ["application/vnd.ms-powerpoint", "application/vnd.openxmlformats-officedocument.presentationml.presentation"]
    },
    {
        "name": "Microsoft Excel",
        "extensions": [".xls", ".xlsx"],
        "mimeTypes": ["application/vnd.ms-excel", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"]
    }
]

class DocumentTranslationService:
    """Document translation service"""
    
    def __init__(self):
        self.translation_service = get_translation_service()
        self.tasks = {}  # 内存中跟踪任务状态
        self.executor = ThreadPoolExecutor(max_workers=5)
        
    def get_supported_formats(self) -> Dict:
        """获取支持的文件格式信息"""
        return {
            "formats": SUPPORTED_FORMATS,
            "maxSize": 50 * 1024 * 1024,  # 50MB
            "sizeLimit": "50MB"
        }
    
    async def create_translation_task(self, file: UploadFile, user_id: int, 
                                     source_language: str, target_language: str,
                                     domain: str = "general", style: str = "standard",
                                     advanced_options: List[str] = None,
                                     glossary_id: str = None) -> str:
        """创建文档翻译任务"""
        if not file:
            raise HTTPException(status_code=400, detail="没有上传文件")
        
        # 验证文件类型
        file_ext = get_file_extension(file.filename)
        if not any(ext in file_ext.lower() for format_info in SUPPORTED_FORMATS 
                  for ext in format_info["extensions"]):
            raise HTTPException(status_code=400, detail="不支持的文件类型")
        
        # 生成唯一的任务ID和文件路径
        task_id = generate_unique_id()
        file_path = os.path.join(UPLOAD_DIR, f"{task_id}{file_ext}")
        
        # 保存上传的文件
        with open(file_path, "wb") as f:
            content = await file.read()
            f.write(content)
            file_size = len(content)
        
        # 计算文档字数
        word_count = await self._count_document_words(file_path, file_ext)
        
        # 确保user_id是整数
        actual_user_id = getattr(user_id, 'id', user_id)
        logger.info(f"将User对象转换为user_id: {actual_user_id}")
        
        # 创建任务记录
        db = next(get_db())
        translation_task = DocumentTranslation(
            task_id=task_id,
            user_id=actual_user_id,
            file_name=file.filename,
            file_type=file_ext.lstrip('.'),
            file_size=file_size,
            source_language=source_language,
            target_language=target_language,
            source_file_path=file_path,
            status="pending",
            progress=0.0,
            word_count=word_count,
            domain=domain,
            style=style,
            advanced_options=advanced_options or [],
            glossary_id=glossary_id
        )
        
        db.add(translation_task)
        db.commit()
        db.refresh(translation_task)
        
        # 启动异步翻译任务
        asyncio.create_task(self._process_document(task_id))
        
        return task_id
    
    async def get_task_status(self, task_id: str) -> Dict:
        """获取任务状态"""
        db = next(get_db())
        task = db.query(DocumentTranslation).filter(DocumentTranslation.task_id == task_id).first()
        
        if not task:
            raise HTTPException(status_code=404, detail="翻译任务不存在")
        
        return {
            "taskId": task.task_id,
            "status": task.status,
            "progress": task.progress,
            "wordCount": task.word_count,
            "fileName": task.file_name,
            "errorMessage": task.error_message
        }
    
    async def get_translation_preview(self, task_id: str) -> Dict[str, str]:
        """获取翻译预览"""
        db = next(get_db())
        task = db.query(DocumentTranslation).filter(DocumentTranslation.task_id == task_id).first()
        
        if not task:
            raise HTTPException(status_code=404, detail="翻译任务不存在")
            
        if task.status != "completed":
            raise HTTPException(status_code=400, detail="翻译尚未完成，无法预览")
        
        # 如果已经生成了预览，直接返回
        if task.preview_path and os.path.exists(task.preview_path):
            with open(task.preview_path, "r", encoding="utf-8") as f:
                preview_data = json.load(f)
                return preview_data
        
        # 否则生成预览
        preview_data = await self._generate_preview(task)
        
        # 保存预览到文件
        preview_path = os.path.join(PREVIEW_DIR, f"{task_id}_preview.json")
        with open(preview_path, "w", encoding="utf-8") as f:
            json.dump(preview_data, f, ensure_ascii=False)
        
        # 更新任务记录
        db.query(DocumentTranslation).filter(DocumentTranslation.task_id == task_id).update(
            {"preview_path": preview_path}
        )
        db.commit()
        
        return preview_data
    
    async def download_translated_file(self, task_id: str) -> Tuple[str, str]:
        """下载翻译文件"""
        db = next(get_db())
        task = db.query(DocumentTranslation).filter(DocumentTranslation.task_id == task_id).first()
        
        if not task:
            raise HTTPException(status_code=404, detail="翻译任务不存在")
            
        if task.status != "completed":
            raise HTTPException(status_code=400, detail="翻译尚未完成，无法下载")
            
        if not task.translated_file_path or not os.path.exists(task.translated_file_path):
            raise HTTPException(status_code=404, detail="翻译文件不存在")
        
        # 更新下载次数
        db.query(DocumentTranslation).filter(DocumentTranslation.task_id == task_id).update(
            {"download_count": DocumentTranslation.download_count + 1}
        )
        db.commit()
        
        file_name = task.file_name
        file_path = task.translated_file_path
        return file_path, file_name
    
    async def get_history(self, user_id: int, page: int = 1, page_size: int = 10, 
                         status: str = None, language: str = None, 
                         file_name: str = None) -> Dict:
        """获取用户的翻译历史"""
        # 确保user_id是整数
        actual_user_id = getattr(user_id, 'id', user_id)
        
        db = next(get_db())
        query = db.query(DocumentTranslation).filter(DocumentTranslation.user_id == actual_user_id)
        
        # 应用筛选条件
        if status:
            query = query.filter(DocumentTranslation.status == status)
        
        if language:
            query = query.filter(
                (DocumentTranslation.source_language == language) | 
                (DocumentTranslation.target_language == language)
            )
            
        if file_name:
            query = query.filter(DocumentTranslation.file_name.like(f"%{file_name}%"))
        
        # 获取总数
        total = query.count()
        
        # 分页
        items = query.order_by(DocumentTranslation.created_at.desc()) \
                    .offset((page - 1) * page_size) \
                    .limit(page_size) \
                    .all()
        
        return {
            "list": items,
            "pagination": {
                "total": total,
                "page": page,
                "pageSize": page_size,
                "pages": (total + page_size - 1) // page_size
            }
        }
    
    async def delete_history(self, task_id: str, user_id: int) -> bool:
        """删除翻译历史记录"""
        # 确保user_id是整数
        actual_user_id = getattr(user_id, 'id', user_id)
        
        db = next(get_db())
        task = db.query(DocumentTranslation).filter(
            DocumentTranslation.task_id == task_id,
            DocumentTranslation.user_id == actual_user_id
        ).first()
        
        if not task:
            raise HTTPException(status_code=404, detail="翻译任务不存在")
        
        # 删除相关文件
        for file_path in [task.source_file_path, task.translated_file_path, task.preview_path]:
            if file_path and os.path.exists(file_path):
                try:
                    os.remove(file_path)
                except Exception as e:
                    logger.warning(f"删除文件失败: {file_path}, 错误: {str(e)}")
        
        # 删除数据库记录
        db.delete(task)
        db.commit()
        
        return True
    
    async def _process_document(self, task_id: str):
        """处理文档翻译任务"""
        from utils.db import get_db
        from utils.task_persistence import TaskPersistenceManager
        from sqlalchemy import select
        
        try:
            # 获取任务信息 - 使用同步方式
            db = next(get_db())
            stmt = select(DocumentTranslation).where(DocumentTranslation.task_id == task_id)
            task = db.execute(stmt).scalar_one_or_none()
            
            if not task:
                logger.error(f"任务不存在: {task_id}")
                return
            
            # 使用同步方法更新状态
            TaskPersistenceManager.update_task_status_sync(
                db=db,
                task_id=task_id,
                status="processing",
                progress=5,
                message="开始处理文档翻译任务"
            )
            
            # 解析文档内容
            TaskPersistenceManager.update_task_status_sync(
                db=db,
                task_id=task_id,
                status="processing",
                progress=10,
                message="正在解析文档内容"
            )
            
            # 使用同步方法提取文档内容
            file_path = task.source_file_path
            file_type = task.file_type
            document_content = self.extract_document_content_sync(file_path, file_type)
            
            # 计算文档字数
            word_count = 0
            for item in document_content:
                if item["type"] == "paragraph" and "text" in item:
                    word_count += len(item["text"].split())
                elif item["type"] == "table" and "data" in item:
                    for row in item["data"]:
                        for cell in row:
                            if isinstance(cell, str):
                                word_count += len(cell.split())
            
            # 更新任务字数统计
            db.query(DocumentTranslation).filter(DocumentTranslation.task_id == task_id).update(
                {"word_count": word_count}
            )
            db.commit()
            
            # 确保源语言设置正确
            source_language = task.source_language
            if source_language == "auto" or not source_language:
                # 如果是自动检测，设置为默认语言（中文）
                source_language = "zh-CN"
                db.query(DocumentTranslation).filter(DocumentTranslation.task_id == task_id).update(
                    {"source_language": source_language}
                )
                db.commit()
                logger.info(f"源语言自动检测设置为: {source_language}")
            
            # 翻译文档内容 - 使用同步方法
            TaskPersistenceManager.update_task_status_sync(
                db=db,
                task_id=task_id,
                status="processing",
                progress=20,
                message="开始翻译文档内容"
            )
            
            # 使用同步方法进行翻译
            translated_content = self.translate_document_content_sync(
                document_content, 
                source_language, 
                task.target_language,
                task.domain,
                task.style,
                task_id
            )
            
            # 生成翻译文档
            TaskPersistenceManager.update_task_status_sync(
                db=db,
                task_id=task_id,
                status="processing",
                progress=80,
                message="正在生成翻译后的文档"
            )
            
            # 使用同步方法生成翻译文档
            translated_file_path = await self._generate_translated_document(
                file_path,
                translated_content,
                task_id,
                task.target_language,
                source_language
            )
            
            # 生成预览数据
            preview_data = self.generate_preview_from_content_sync(document_content, translated_content)
            preview_path = os.path.join("temp", "previews", f"{task_id}_preview.json")
            os.makedirs(os.path.dirname(preview_path), exist_ok=True)
            
            # 保存预览数据
            with open(preview_path, "w", encoding="utf-8") as f:
                json.dump(preview_data, f, ensure_ascii=False, indent=2)
            
            # 准备结果数据
            result_data = {
                "translated_file_path": translated_file_path,
                "preview_path": preview_path,
                "word_count": word_count,
                "source_language": source_language,
                "target_language": task.target_language
            }
            
            # 更新任务完成状态
            TaskPersistenceManager.update_task_status_sync(
                db=db,
                task_id=task_id,
                status="completed",
                progress=100,
                message="文档翻译完成",
                result_url=translated_file_path,
                output_data=result_data
            )
            
            # 记录任务完成日志
            TaskPersistenceManager.add_task_log_sync(
                db=db,
                task_id=task_id,
                log_type="info",
                message=f"文档翻译完成，生成文件: {os.path.basename(translated_file_path)}"
            )
            
            logger.info(f"文档翻译任务完成: {task_id}")
            
        except Exception as e:
            logger.exception(f"处理文档翻译任务失败: {str(e)}")
            
            try:
                # 更新任务失败状态
                db = next(get_db())
                TaskPersistenceManager.update_task_status_sync(
                    db=db,
                    task_id=task_id,
                    status="failed",
                    progress=0,
                    message=f"文档翻译失败: {str(e)}"
                )
                
                # 记录错误日志
                TaskPersistenceManager.add_task_log_sync(
                    db=db,
                    task_id=task_id,
                    log_type="error",
                    message=f"文档翻译失败: {str(e)}"
                )
            except Exception as log_error:
                logger.error(f"更新任务失败状态时出错: {str(log_error)}")
    
    async def _extract_document_content(self, file_path: str, file_type: str) -> List[Dict[str, Any]]:
        """提取文档内容"""
        file_ext = f".{file_type}"
        content = []
        
        if file_ext in ['.txt']:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.readlines()
                # 按段落分割
                content = [p for p in '\n'.join(content).split('\n\n') if p.strip()]
                
        elif file_ext in ['.doc', '.docx']:
            doc = docx.Document(file_path)
            for para in doc.paragraphs:
                if para.text.strip():
                    content.append(para.text)
                    
        elif file_ext == '.pdf':
            with pdfplumber.open(file_path) as pdf:
                for page in pdf.pages:
                    text = page.extract_text()
                    if text:
                        # 按段落分割
                        paragraphs = [p for p in text.split('\n\n') if p.strip()]
                        content.extend(paragraphs)
                        
        elif file_ext in ['.ppt', '.pptx']:
            prs = Presentation(file_path)
            for slide in prs.slides:
                for shape in slide.shapes:
                    if hasattr(shape, "text") and shape.text.strip():
                        content.append(shape.text)
                        
        elif file_ext in ['.xls', '.xlsx']:
            wb = load_workbook(file_path)
            for sheet in wb.worksheets:
                for row in sheet.iter_rows():
                    for cell in row:
                        if cell.value and str(cell.value).strip():
                            content.append(str(cell.value))
        
        # 处理内容，删除空行等
        content = [text.strip() for text in content if text and text.strip()]
        return content
    
    async def _translate_document_content(self, content: List[str], 
                                         source_lang: str, target_lang: str,
                                         domain: str, style: str,
                                         task_id: str) -> List[str]:
        """翻译文档内容"""
        if not content:
            return []
            
        # 更新进度
        await self._update_task_status(task_id, "processing", 30)
        
        # 对于非常长的内容，将其分批处理以避免请求过大
        batch_size = 10  # 每批10段
        batches = [content[i:i+batch_size] for i in range(0, len(content), batch_size)]
        
        translated_content = []
        total_batches = len(batches)
        
        for i, batch in enumerate(batches):
            # 更新翻译进度
            progress = 30 + 50 * (i / total_batches)  # 30%-80%的进度
            await self._update_task_status(task_id, "processing", progress)
            
            # 调用翻译服务
            try:
                # 将段落合并为单个文本，以提高翻译效率
                batch_text = "\n\n".join(batch)
                
                # 只传递必要的参数，术语ID可以通过domain和style来选择
                terminology_id = None
                if domain == "medical" or domain == "legal" or domain == "technical":
                    terminology_id = domain
                    
                translated_text = await self.translation_service.translate_text(
                    text=batch_text,
                    source_lang=source_lang,
                    target_lang=target_lang,
                    terminology_id=terminology_id
                )
                
                # 拆分回段落
                paragraphs = translated_text.split("\n\n")
                translated_content.extend(paragraphs)
                
                # 添加小延迟，避免请求过快
                await asyncio.sleep(0.5)
                
            except Exception as e:
                logger.error(f"翻译内容失败: {str(e)}")
                # 如果翻译失败，添加原文作为占位符
                translated_content.extend(batch)
        
        # 确保翻译内容数量与原始内容匹配
        if len(translated_content) != len(content):
            # 如果数量不匹配，可能是分隔符问题，尝试其他方法拆分
            # 这里简单处理，保持原始段落数量
            if len(translated_content) < len(content):
                translated_content.extend([""] * (len(content) - len(translated_content)))
            else:
                translated_content = translated_content[:len(content)]
                
        return translated_content
    
    async def _generate_translated_document(self, source_file_path: str, 
                                          translated_content: List[str],
                                          task_id: str,
                                          target_language: str,
                                          source_language: str = "auto") -> str:
        """
        生成翻译后的文档
        
        参数:
            source_file_path: 源文件路径
            translated_content: 翻译后的内容
            task_id: 任务ID
            target_language: 目标语言
            source_language: 源语言，默认为auto
            
        返回:
            翻译后的文档路径
        """
        # 确保临时目录存在
        os.makedirs("temp/documents", exist_ok=True)
        
        # 获取文件扩展名
        file_ext = os.path.splitext(source_file_path)[1].lower()
        file_name = os.path.basename(source_file_path)
        file_name_without_ext = os.path.splitext(file_name)[0]
        
        # 生成目标文件名（添加源语言和目标语言代码）
        source_lang_code = source_language if source_language != "auto" else "auto"
        target_file_name = f"{file_name_without_ext}_{source_lang_code}_to_{target_language}{file_ext}"
        target_file_path = os.path.join("temp/documents", f"{task_id}_{target_file_name}")
        
        # 记录翻译方向
        logger.info(f"生成翻译文档: {source_lang_code} -> {target_language}, 文件: {target_file_name}")
        
        # 根据文件类型选择不同的处理方法
        if file_ext in ['.txt', '.md', '.html']:
            # 文本类型文件直接写入
            with open(target_file_path, 'w', encoding='utf-8') as f:
                f.write('\n\n'.join(translated_content))
                
        elif file_ext in ['.doc', '.docx']:
            # 处理Word文档 - 简单实现，复制并替换文本
            shutil.copy2(source_file_path, target_file_path)
                
            # 使用python-docx处理Word文档
            doc = docx.Document(target_file_path)
                
            # 清除所有段落并添加翻译内容
            for i in range(len(doc.paragraphs)-1, -1, -1):
                p = doc.paragraphs[i]
                p.clear()
                
            # 添加翻译信息到文档属性
            doc.core_properties.comments = f"Translated from {source_lang_code} to {target_language}"
            
            # 添加翻译内容
            for para_text in translated_content:
                if para_text.strip():
                    doc.add_paragraph(para_text)
                
            # 保存修改后的文档
            doc.save(target_file_path)
                
        elif file_ext == '.pdf':
            # PDF文件暂时只能生成文本版本
            txt_file_path = f"{os.path.splitext(target_file_path)[0]}_translated.txt"
            with open(txt_file_path, 'w', encoding='utf-8') as f:
                f.write(f"翻译方向: {source_lang_code} -> {target_language}\n\n")
                f.write('\n\n'.join(translated_content))
            
            # 返回文本文件路径
            return txt_file_path
                
        elif file_ext in ['.ppt', '.pptx']:
            # 处理PPT - 简单实现，复制并替换文本
            shutil.copy2(source_file_path, target_file_path)
            
            # TODO: 使用python-pptx库处理PPT内容
            # 当前版本只生成文本版本
            txt_file_path = f"{os.path.splitext(target_file_path)[0]}_translated.txt"
            with open(txt_file_path, 'w', encoding='utf-8') as f:
                f.write(f"翻译方向: {source_lang_code} -> {target_language}\n\n")
                f.write('\n\n'.join(translated_content))
            
            # 返回文本文件路径
            return txt_file_path
            
        else:
            # 其他格式暂不支持，返回原文件
            shutil.copy2(source_file_path, target_file_path)
            
        return target_file_path
    
    async def _generate_preview(self, task: DocumentTranslation) -> Dict[str, str]:
        """生成文档预览"""
        try:
            # 提取原文内容
            source_content = await self._extract_document_content(task.source_file_path, task.file_type)
            
            # 读取翻译内容
            translated_content = []
            if task.translated_file_path and os.path.exists(task.translated_file_path):
                translated_content = await self._extract_document_content(
                    task.translated_file_path, 
                    get_file_extension(task.translated_file_path).lstrip('.')
                )
            
            return await self._generate_preview_from_content(source_content, translated_content)
            
        except Exception as e:
            logger.exception(f"生成预览失败: {str(e)}")
            return {
                "source": "<p>预览生成失败</p>",
                "translated": "<p>预览生成失败</p>"
            }
    
    def generate_preview_from_content_sync(self, original_content: List[Dict[str, Any]], 
                                     translated_content: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        同步方法：根据原文内容和翻译内容生成预览数据
        
        参数:
            original_content: 原始内容
            translated_content: 翻译后的内容
            
        返回:
            预览数据
        """
        try:
            # 创建预览数据结构
            preview_data = []
            
            # 检查输入类型
            if isinstance(original_content, str) and isinstance(translated_content, str):
                # 处理简单字符串情况
                return [
                    {
                        "type": "paragraph",
                        "source": original_content,
                        "translated": translated_content
                    }
                ]
            
            # 确保列表结构正确
            if not isinstance(original_content, list):
                logger.warning(f"原始内容不是列表类型: {type(original_content)}")
                if isinstance(original_content, str):
                    paragraphs = self._extract_paragraphs(original_content)
                    original_content = [{"type": "paragraph", "text": p} for p in paragraphs]
                else:
                    original_content = []
            
            if not isinstance(translated_content, list):
                logger.warning(f"翻译内容不是列表类型: {type(translated_content)}")
                if isinstance(translated_content, str):
                    paragraphs = self._extract_paragraphs(translated_content)
                    translated_content = [{"type": "paragraph", "text": p} for p in paragraphs]
                else:
                    translated_content = []
                    
            # 处理内容为空的情况
            if not original_content and not translated_content:
                return []
                
            # 如果翻译内容为空但原始内容不为空，则创建空翻译占位
            if not translated_content and original_content:
                for item in original_content:
                    preview_item = {
                        "type": item.get("type", "paragraph"),
                        "source": item.get("text", ""),
                        "translated": ""
                    }
                    preview_data.append(preview_item)
                return preview_data
                
            # 如果长度相同，假设一一对应
            if len(original_content) == len(translated_content):
                for i in range(len(original_content)):
                    orig_item = original_content[i]
                    trans_item = translated_content[i]
                
                    preview_item = {
                        "type": orig_item.get("type", "paragraph"),
                        "source": orig_item.get("text", ""),
                        "translated": trans_item.get("text", "")
                    }
                    
                    # 对于表格类型，特殊处理
                    if orig_item.get("type") == "table" and "data" in orig_item:
                        preview_item["headers"] = orig_item.get("metadata", {}).get("headers", [])
                        preview_item["sourceRows"] = orig_item.get("data", [])
                        preview_item["rows"] = trans_item.get("data", [])
                        
                    preview_data.append(preview_item)
            else:
                # 长度不同，无法一一对应，只能分别处理
                # 首先处理原始内容
                for item in original_content:
                    preview_item = {
                        "type": item.get("type", "paragraph"),
                        "source": item.get("text", ""),
                        "translated": ""
                    }
                    
                    if item.get("type") == "table" and "data" in item:
                        preview_item["headers"] = item.get("metadata", {}).get("headers", [])
                        preview_item["sourceRows"] = item.get("data", [])
                        
                    preview_data.append(preview_item)
                
                # 然后添加翻译内容
                for i, item in enumerate(translated_content):
                    if i < len(preview_data):
                        # 更新已有项
                        preview_data[i]["translated"] = item.get("text", "")
                        
                        # 更新表格行
                        if item.get("type") == "table" and "data" in item:
                            preview_data[i]["rows"] = item.get("data", [])
                    else:
                        # 添加新项
                        preview_item = {
                            "type": item.get("type", "paragraph"),
                            "source": "",
                            "translated": item.get("text", "")
                        }
                        
                        if item.get("type") == "table" and "data" in item:
                            preview_item["headers"] = item.get("metadata", {}).get("headers", [])
                            preview_item["rows"] = item.get("data", [])
                            
                    preview_data.append(preview_item)
            
            return preview_data
                
        except Exception as e:
            logger.error(f"生成预览数据失败: {str(e)}", exc_info=True)
            # 返回简单的错误预览
            return [
                {
                    "type": "paragraph",
                    "source": "预览生成失败",
                    "translated": f"错误: {str(e)}"
                }
            ]
            
    async def _count_document_words(self, file_path: str, file_ext: str) -> int:
        """计算文档字数"""
        try:
            content = await self._extract_document_content(file_path, file_ext.lstrip('.'))
            text = " ".join(content)
            return count_words(text)
        except Exception as e:
            logger.error(f"计算字数失败: {str(e)}")
            return 0
    
    async def clean_expired_files(self, days: int = 30) -> int:
        """清理过期的文件
        
        参数:
            days: 文件保留天数，超过此天数的文件将被删除
            
        返回:
            已删除的文件数量
        """
        # 计算过期时间
        expiry_time = time.time() - (days * 24 * 60 * 60)
        deleted_count = 0
        
        # 要清理的目录
        dirs_to_clean = [UPLOAD_DIR, RESULT_DIR, PREVIEW_DIR]
        
        for directory in dirs_to_clean:
            if not os.path.exists(directory):
                continue
                
            logger.info(f"清理目录: {directory}")
            
            # 获取目录中的所有文件
            for filename in os.listdir(directory):
                file_path = os.path.join(directory, filename)
                
                # 跳过目录
                if os.path.isdir(file_path):
                    continue
                    
                # 获取文件修改时间
                file_mod_time = os.path.getmtime(file_path)
                
                # 如果文件超过保留期，删除
                if file_mod_time < expiry_time:
                    try:
                        os.remove(file_path)
                        deleted_count += 1
                        logger.debug(f"已删除过期文件: {file_path}")
                    except Exception as e:
                        logger.error(f"删除文件失败: {file_path}, 错误: {str(e)}")
        
        logger.info(f"清理完成，共删除 {deleted_count} 个过期文件")
        return deleted_count

    def extract_document_content_sync(self, file_path: str, file_type: str) -> List[Dict[str, Any]]:
        """
        同步方法：从文档中提取内容
        
        参数:
            file_path: 文件路径
            file_type: 文件类型
            
        返回:
            文档内容列表
        """
        try:
            if file_type.lower() in ['docx', 'doc']:
                return self._extract_word_content_sync(file_path)
            elif file_type.lower() == 'pdf':
                return self._extract_pdf_content_sync(file_path)
            elif file_type.lower() == 'txt':
                return self._extract_txt_content_sync(file_path)
            elif file_type.lower() in ['pptx', 'ppt']:
                return self._extract_ppt_content_sync(file_path)
            elif file_type.lower() in ['xlsx', 'xls']:
                return self._extract_excel_content_sync(file_path)
            elif file_type.lower() == 'md':
                return self._extract_markdown_content_sync(file_path)
            elif file_type.lower() == 'html':
                return self._extract_html_content_sync(file_path)
            else:
                raise ValueError(f"不支持的文件类型: {file_type}")
        except Exception as e:
            logger.error(f"提取文档内容失败: {str(e)}", exc_info=True)
            raise
            
    def _extract_word_content_sync(self, file_path: str) -> List[Dict[str, Any]]:
        """同步方法：从Word文档中提取内容"""
        import docx
        try:
            doc = docx.Document(file_path)
            content = []
            
            # 处理段落
            for i, para in enumerate(doc.paragraphs):
                if para.text.strip():
                    content.append({
                        "id": f"p{i}",
                        "type": "paragraph",
                        "text": para.text,
                        "metadata": {}
                    })
                    
            # 处理表格
            for t, table in enumerate(doc.tables):
                table_data = []
                for r, row in enumerate(table.rows):
                    row_data = []
                    for c, cell in enumerate(row.cells):
                        row_data.append(cell.text)
                    table_data.append(row_data)
                
                if table_data:
                    content.append({
                        "id": f"t{t}",
                        "type": "table",
                        "data": table_data,
                        "metadata": {}
                    })
            
            return content
        except Exception as e:
            logger.error(f"提取Word文档内容失败: {str(e)}", exc_info=True)
            raise
            
    def _extract_pdf_content_sync(self, file_path: str) -> List[Dict[str, Any]]:
        """同步方法：从PDF文档中提取内容"""
        import fitz  # PyMuPDF
        try:
            doc = fitz.open(file_path)
            content = []
            
            for page_num in range(len(doc)):
                page = doc.load_page(page_num)
                text = page.get_text()
                
                # 简单分段处理
                paragraphs = text.split('\n\n')
                for i, para in enumerate(paragraphs):
                    if para.strip():
                        content.append({
                            "id": f"p{page_num}_{i}",
                            "type": "paragraph",
                            "text": para.strip(),
                            "metadata": {"page": page_num + 1}
                        })
            
            return content
        except Exception as e:
            logger.error(f"提取PDF文档内容失败: {str(e)}", exc_info=True)
            raise
            
    def _extract_txt_content_sync(self, file_path: str) -> List[Dict[str, Any]]:
        """同步方法：从文本文件中提取内容"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                text = f.read()
                
            # 按空行分段
            paragraphs = text.split('\n\n')
            content = []
            
            for i, para in enumerate(paragraphs):
                if para.strip():
                    content.append({
                        "id": f"p{i}",
                        "type": "paragraph",
                        "text": para.strip(),
                        "metadata": {}
                    })
            
            return content
        except UnicodeDecodeError:
            # 尝试其他编码
            try:
                with open(file_path, 'r', encoding='gbk') as f:
                    text = f.read()
                    
                # 按空行分段
                paragraphs = text.split('\n\n')
                content = []
                
                for i, para in enumerate(paragraphs):
                    if para.strip():
                        content.append({
                            "id": f"p{i}",
                            "type": "paragraph",
                            "text": para.strip(),
                            "metadata": {}
                        })
                
                return content
            except Exception as e:
                logger.error(f"提取文本文件内容失败: {str(e)}", exc_info=True)
                raise
        except Exception as e:
            logger.error(f"提取文本文件内容失败: {str(e)}", exc_info=True)
            raise
            
    def _extract_ppt_content_sync(self, file_path: str) -> List[Dict[str, Any]]:
        """同步方法：从PPT文档中提取内容"""
        import pptx
        try:
            prs = pptx.Presentation(file_path)
            content = []
            
            for slide_num, slide in enumerate(prs.slides):
                slide_content = []
                
                # 处理形状中的文本
                for shape in slide.shapes:
                    if hasattr(shape, "text") and shape.text.strip():
                        slide_content.append({
                            "id": f"s{slide_num}_{len(slide_content)}",
                            "type": "paragraph",
                            "text": shape.text,
                            "metadata": {"slide": slide_num + 1}
                        })
                
                # 如果有内容，添加到总内容中
                content.extend(slide_content)
            
            return content
        except Exception as e:
            logger.error(f"提取PPT文档内容失败: {str(e)}", exc_info=True)
            raise
            
    def _extract_excel_content_sync(self, file_path: str) -> List[Dict[str, Any]]:
        """同步方法：提取Excel内容"""
        import openpyxl
        try:
            content = []
            
            # 打开Excel文件
            wb = openpyxl.load_workbook(file_path, data_only=True)
            
            # 处理每个工作表
            for sheet_name in wb.sheetnames:
                sheet = wb[sheet_name]
                
                # 获取工作表的数据范围
                data_rows = sheet.max_row
                data_cols = sheet.max_column
                
                # 如果工作表为空，跳过
                if data_rows == 0 or data_cols == 0:
                    continue
                
                # 提取工作表数据
                sheet_data = []
                for row in range(1, data_rows + 1):
                    row_data = []
                    for col in range(1, data_cols + 1):
                        cell = sheet.cell(row=row, column=col)
                        # 获取单元格值，确保不是None
                        cell_value = cell.value if cell.value is not None else ""
                        row_data.append(cell_value)
                    # 检查行是否全为空
                    if any(cell != "" for cell in row_data):
                        sheet_data.append(row_data)
                
                # 检查表格是否为空
                if not sheet_data:
                    continue
                
                # 为每个表格创建唯一ID
                table_id = f"excel_table_{sheet_name}_{len(content)}"
                
                # 添加到内容列表
                content.append({
                    "id": table_id,
                    "type": "table",
                    "data": sheet_data,
                    "metadata": {
                        "sheet": sheet_name,
                        "start_row": 1,
                        "start_col": 1,
                        "rows": len(sheet_data),
                        "cols": max(len(row) for row in sheet_data) if sheet_data else 0
                    }
                })
                
                logger.info(f"提取Excel工作表: {sheet_name}, 行数: {len(sheet_data)}")
            
            return content
        except Exception as e:
            logger.error(f"提取Excel内容失败: {str(e)}", exc_info=True)
            raise
            
    def _extract_markdown_content_sync(self, file_path: str) -> List[Dict[str, Any]]:
        """同步方法：从Markdown文件中提取内容"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                text = f.read()
                
            # 按段落分割
            paragraphs = text.split('\n\n')
            content = []
            
            for i, para in enumerate(paragraphs):
                if para.strip():
                    content.append({
                        "id": f"p{i}",
                        "type": "paragraph",
                        "text": para.strip(),
                        "metadata": {}
                    })
            
            return content
        except Exception as e:
            logger.error(f"提取Markdown文件内容失败: {str(e)}", exc_info=True)
            raise
            
    def _extract_html_content_sync(self, file_path: str) -> List[Dict[str, Any]]:
        """同步方法：从HTML文件中提取内容"""
        from bs4 import BeautifulSoup
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                html_content = f.read()
                
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 移除脚本和样式元素
            for script in soup(["script", "style"]):
                script.extract()
                
            # 获取文本
            text = soup.get_text()
            
            # 按行分割，然后去除多余的空白
            lines = (line.strip() for line in text.splitlines())
            chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
            text = '\n'.join(chunk for chunk in chunks if chunk)
            
            # 按段落分割
            paragraphs = text.split('\n\n')
            content = []
            
            for i, para in enumerate(paragraphs):
                if para.strip():
                    content.append({
                        "id": f"p{i}",
                        "type": "paragraph",
                        "text": para.strip(),
                        "metadata": {}
                    })
            
            return content
        except Exception as e:
            logger.error(f"提取HTML文件内容失败: {str(e)}", exc_info=True)
            raise
            
    def translate_document_content_sync(self, document_content, source_language: str, target_language: str, 
                                   domain: str = "general", style: str = "standard", 
                                   task_id: str = None) -> List[str]:
        """
        同步翻译文档内容
        
        参数:
            document_content: 文档内容，可以是字符串或包含文档内容的列表
            source_language: 源语言
            target_language: 目标语言
            domain: 专业领域
            style: 翻译风格
            task_id: 任务ID，用于跟踪进度
            
        返回:
            翻译后的文档内容，如果输入是列表则返回列表，否则返回字符串
        """
        # 获取翻译服务
        translation_service = get_translation_service()
            
        # 处理列表类型的文档内容（从extract_document_content_sync方法返回的结构）
        if isinstance(document_content, list):
            # 文档内容是一个列表，包含多个文档元素
            translated_items = []
            
            # 遍历每个文档元素进行翻译
            for item in document_content:
                # 创建翻译后的项
                translated_item = item.copy()  # 复制原始项的结构
                
                if item["type"] == "paragraph" and "text" in item:
                    # 处理文本段落
                    text = item["text"]
                    if text and text.strip():
                        translated_text = translation_service.translate_sync(
                            text,
                            source_language,
                            target_language,
                            domain,
                            style
                        )
                        translated_item["text"] = translated_text
                        
                elif item["type"] == "table" and "data" in item:
                    # 处理表格数据
                    table_data = item["data"]
                    translated_data = []
                    
                    for row in table_data:
                        translated_row = []
                        for cell in row:
                            if isinstance(cell, str) and cell.strip():
                                # 翻译单元格内容
                                translated_cell = translation_service.translate_sync(
                                    cell,
                                    source_language,
                                    target_language,
                                    domain,
                                    style
                                )
                                translated_row.append(translated_cell)
                            else:
                                # 保留原样
                                translated_row.append(cell)
                                
                        translated_data.append(translated_row)
                        
                    translated_item["data"] = translated_data
                    
                # 添加翻译后的项
                translated_items.append(translated_item)
                
            return translated_items
        
        # 处理字符串类型的文档内容
        elif isinstance(document_content, str):
            if not document_content or document_content.strip() == "":
                return ""
                
            # 如果文档较小，直接翻译
            if len(document_content) < 10000:
                return translation_service.translate_sync(
                    document_content,
                    source_language,
                    target_language,
                    domain,
                    style
                )
            
            # 分割文档为段落
            paragraphs = self._extract_paragraphs(document_content)
            total_paragraphs = len(paragraphs)
            
            # 使用批量翻译处理
            translated_content = ""
            
            # 计算最佳批量大小
            batch_size = min(10, max(5, total_paragraphs // 10))
            
            # 分批处理段落
            for i in range(0, total_paragraphs, batch_size):
                batch = paragraphs[i:i + batch_size]
                
                # 检查翻译服务是否支持批量翻译
                if hasattr(translation_service, 'translate_batch'):
                    # 使用批量翻译
                    batch_results = translation_service.translate_batch(
                        batch,
                        source_language,
                        target_language
                    )
                    
                    # 合并批次结果
                    batch_content = "\n\n".join(result for result in batch_results if result)
                    if translated_content:
                        translated_content += "\n\n" + batch_content
                    else:
                        translated_content = batch_content
                else:
                    # 回退到单独翻译
                    for para in batch:
                        if not para.strip():
                            continue
                            
                        # 翻译段落
                        translated_para = translation_service.translate_sync(
                            para,
                            source_language,
                            target_language,
                            domain,
                            style
                        )
                        
                        # 合并结果
                        if translated_content:
                            translated_content += "\n\n" + translated_para
                        else:
                            translated_content = translated_para
            
            return translated_content
        else:
            # 不支持的类型
            logger.error(f"不支持的文档内容类型: {type(document_content)}")
            raise ValueError(f"不支持的文档内容类型: {type(document_content)}")
    
    def _extract_paragraphs(self, document_content: str) -> List[str]:
        """
        从文档内容中提取段落
        
        参数:
            document_content: 文档内容字符串
            
        返回:
            段落列表
        """
        if not document_content:
            return []
            
        # 按换行符分割段落
        paragraphs = re.split(r'\n\s*\n', document_content)
        
        # 过滤空段落并合并短段落
        filtered_paragraphs = []
        current_paragraph = ""
        max_paragraph_length = 2000  # 与MT服务中的最大段落长度保持一致
        
        for para in paragraphs:
            para = para.strip()
            if not para:
                continue
                
            # 如果当前段落加上新段落不超过最大长度，则合并
            if current_paragraph and len(current_paragraph) + len(para) + 2 < max_paragraph_length:
                current_paragraph += "\n\n" + para
            else:
                # 否则添加当前段落并开始新段落
                if current_paragraph:
                    filtered_paragraphs.append(current_paragraph)
                current_paragraph = para
                
        # 添加最后一个段落
        if current_paragraph:
            filtered_paragraphs.append(current_paragraph)
            
        return filtered_paragraphs

    def generate_translated_document_sync(self, source_file_path: str, 
                                   translated_content,
                                   task_id: str,
                                   target_language: str,
                                   source_language: str = "auto") -> str:
        """
        同步生成翻译后的文档
        
        参数:
            source_file_path: 源文件路径
            translated_content: 翻译后的内容，可以是字符串、列表或包含文档内容的字典列表
            task_id: 任务ID
            target_language: 目标语言
            source_language: 源语言，默认为auto
            
        返回:
            翻译后的文档路径
        """
        try:
            # 确保临时目录存在
            os.makedirs("storage/temp", exist_ok=True)
            
            # 获取文件扩展名
            file_ext = os.path.splitext(source_file_path)[1].lower()
            file_name = os.path.basename(source_file_path)
            file_name_without_ext = os.path.splitext(file_name)[0]
            
            # 生成目标文件名（添加源语言和目标语言代码）
            source_lang_code = source_language if source_language != "auto" else "auto"
            target_file_name = f"{file_name_without_ext}_{source_lang_code}_to_{target_language}{file_ext}"
            target_file_path = os.path.join("storage/temp", f"{task_id}_{target_file_name}")
            
            # 记录翻译方向
            logger.info(f"生成翻译文档: {source_lang_code} -> {target_language}, 文件: {target_file_name}")
            
            # 处理字典列表类型的内容（从translate_document_content_sync返回的结构化数据）
            if isinstance(translated_content, list) and len(translated_content) > 0 and isinstance(translated_content[0], dict):
                # 提取文本内容
                extracted_text = []
                for item in translated_content:
                    if item.get("type") == "paragraph" and "text" in item:
                        extracted_text.append(item["text"])
                    elif item.get("type") == "table" and "data" in item:
                        # 简单处理表格数据，将其转换为文本
                        table_text = []
                        for row in item["data"]:
                            row_text = " | ".join(str(cell) for cell in row)
                            table_text.append(row_text)
                        extracted_text.append("\n".join(table_text))
                        
                # 使用提取的文本内容
                text_content = "\n\n".join(extracted_text)
            elif isinstance(translated_content, list) and all(isinstance(item, str) for item in translated_content):
                # 如果是字符串列表，直接合并
                text_content = "\n\n".join(translated_content)
            else:
                # 如果是单个字符串
                text_content = str(translated_content)
            
            # 根据文件类型选择不同的处理方法
            if file_ext in ['.txt', '.md', '.html']:
                # 文本类型文件直接写入
                with open(target_file_path, 'w', encoding='utf-8') as f:
                    f.write(text_content)
            elif file_ext in ['.doc', '.docx']:
                # 处理Word文档 - 简单实现，复制并替换文本
                shutil.copy2(source_file_path, target_file_path)
                
                # 使用python-docx处理Word文档
                from docx import Document
                doc = Document(target_file_path)
                
                # 清除所有段落并添加翻译内容
                for i in range(len(doc.paragraphs)-1, -1, -1):
                    p = doc.paragraphs[i]
                    p.clear()
                
                # 添加翻译信息到文档属性
                doc.core_properties.comments = f"Translated from {source_lang_code} to {target_language}"
                
                # 按段落分割并添加到文档
                for para_text in text_content.split('\n\n'):
                    if isinstance(para_text, str) and para_text.strip():
                        doc.add_paragraph(para_text)
                    elif not isinstance(para_text, str):
                        # 处理非字符串类型
                        doc.add_paragraph(str(para_text))
                
                # 保存修改后的文档
                doc.save(target_file_path)
            else:
                # 对于其他格式，暂时只能复制原文件并添加说明
                shutil.copy2(source_file_path, target_file_path)
                
                # 如果是PDF，可以考虑将翻译内容写入一个附加的文本文件
                if file_ext == '.pdf':
                    txt_file_path = f"{os.path.splitext(target_file_path)[0]}_translated.txt"
                    with open(txt_file_path, 'w', encoding='utf-8') as f:
                        f.write(f"翻译方向: {source_lang_code} -> {target_language}\n\n")
                        f.write(text_content)
                    
                    # 返回文本文件路径
                    return txt_file_path
            
            return target_file_path
        except Exception as e:
            logger.error(f"生成翻译文档失败: {str(e)}", exc_info=True)
            raise

# 创建服务实例
document_translation_service = DocumentTranslationService() 