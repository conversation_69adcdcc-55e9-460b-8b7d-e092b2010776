#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
增强版TTS模型下载脚本

特点:
1. 支持多种下载源切换
2. 自动处理代理问题
3. 支持断点续传
4. 支持本地模型部署
"""

import os
import sys
import argparse
import logging
import requests
import json
import time
import shutil
import hashlib
import zipfile
from pathlib import Path
from tqdm import tqdm

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# TTS模型的基本信息，包含备用下载链接
TTS_MODELS = {
    "zh-CN-baker": {
        "name": "tts_models/zh-CN/baker/tacotron2-DDC-GST",
        "primary_url": "https://coqui.gateway.scarf.sh/v0.6.1_models/tts_models--zh-CN--baker--tacotron2-DDC-GST.zip",
        "backup_urls": [
            "https://huggingface.co/coqui/TTS-models/resolve/main/tts_models--zh-CN--baker--tacotron2-DDC-GST.zip"
        ],
        "description": "中文语音合成模型(Baker数据集)",
        "md5": None  # 可选的MD5校验
    },
    "xtts_v1": {
        "name": "tts_models/multilingual/multi-dataset/xtts_v1",
        "primary_url": "https://coqui.gateway.scarf.sh/v0.10.0_models/tts_models--multilingual--multi-dataset--xtts_v1.zip",
        "backup_urls": [
            "https://huggingface.co/coqui/TTS-models/resolve/main/tts_models--multilingual--multi-dataset--xtts_v1.zip"
        ],
        "description": "多语言XTTS语音模型v1",
        "md5": None
    },
    "en-ljspeech": {
        "name": "tts_models/en/ljspeech/tacotron2-DDC",
        "primary_url": "https://coqui.gateway.scarf.sh/v0.6.1_models/tts_models--en--ljspeech--tacotron2-DDC.zip",
        "backup_urls": [
            "https://huggingface.co/coqui/TTS-models/resolve/main/tts_models--en--ljspeech--tacotron2-DDC.zip"
        ],
        "description": "英语语音合成模型(LJSpeech数据集)",
        "md5": None
    }
}

# 小型测试模型，用于快速测试系统功能
MINIMAL_TEST_MODEL = {
    "name": "tts_models/mini/minimal-test",
    "description": "最小测试模型，仅用于验证系统功能"
}

def setup_argparse():
    """设置命令行参数"""
    parser = argparse.ArgumentParser(description='增强版TTS模型下载器')
    parser.add_argument('--model', type=str, choices=list(TTS_MODELS.keys()), 
                        help='要下载的模型ID，可选值: ' + ', '.join(TTS_MODELS.keys()))
    parser.add_argument('--all', action='store_true', help='下载所有模型')
    parser.add_argument('--output-dir', type=str, default=None, 
                        help='模型输出目录，默认为backend/models/tts')
    parser.add_argument('--proxy', type=str, default=None, 
                        help='使用代理，例如 http://127.0.0.1:1080')
    parser.add_argument('--no-proxy', action='store_true',
                        help='禁用系统代理设置')
    parser.add_argument('--retry', type=int, default=5,
                        help='下载失败重试次数，默认5次')
    parser.add_argument('--timeout', type=int, default=60,
                        help='下载超时时间(秒)，默认60秒')
    parser.add_argument('--use-backup', action='store_true',
                        help='优先使用备用下载链接')
    parser.add_argument('--create-minimal', action='store_true',
                        help='创建最小测试模型(不需要下载)')
    return parser.parse_args()

def get_output_dir(args):
    """获取输出目录"""
    if args.output_dir:
        output_dir = args.output_dir
    else:
        # 获取脚本所在目录
        script_dir = os.path.dirname(os.path.abspath(__file__))
        # 向上一级到backend目录，然后到models/tts
        output_dir = os.path.join(os.path.dirname(script_dir), 'models', 'tts')
    
    # 确保目录存在
    os.makedirs(output_dir, exist_ok=True)
    return output_dir

def get_proxies(args):
    """获取代理设置"""
    if args.no_proxy:
        # 禁用代理
        return {'http': None, 'https': None}
    elif args.proxy:
        # 使用指定代理
        return {'http': args.proxy, 'https': args.proxy}
    else:
        # 使用系统代理
        return None

def md5_checksum(file_path):
    """计算文件MD5校验值"""
    md5 = hashlib.md5()
    with open(file_path, 'rb') as f:
        for chunk in iter(lambda: f.read(4096), b''):
            md5.update(chunk)
    return md5.hexdigest()

def download_file(url, output_path, proxies=None, retry=5, timeout=60):
    """
    下载文件，支持断点续传
    
    Args:
        url: 文件URL
        output_path: 输出路径
        proxies: 代理设置
        retry: 重试次数
        timeout: 超时时间(秒)
    
    Returns:
        bool: 是否下载成功
    """
    # 获取已下载的大小(如果文件存在)
    if os.path.exists(output_path):
        downloaded_size = os.path.getsize(output_path)
        logger.info(f"文件已存在，已下载大小: {downloaded_size} 字节")
        mode = 'ab'  # 追加模式
        headers = {'Range': f'bytes={downloaded_size}-'}
    else:
        downloaded_size = 0
        mode = 'wb'  # 写入模式
        headers = {}
    
    # 尝试下载
    for attempt in range(retry):
        try:
            # 设置超时，获取网络连接
            logger.info(f"尝试下载 {url} (第 {attempt+1}/{retry} 次尝试)")
            
            # 添加随机User-Agent避免被屏蔽
            headers['User-Agent'] = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            
            response = requests.get(
                url, 
                stream=True, 
                headers=headers, 
                proxies=proxies, 
                timeout=timeout,
                allow_redirects=True
            )
            response.raise_for_status()
            
            # 获取文件总大小
            total_size = int(response.headers.get('content-length', 0)) + downloaded_size
            
            # 如果内容长度小于1KB，可能是错误页面或重定向
            if int(response.headers.get('content-length', 0)) < 1024:
                logger.warning("警告：下载内容过小，可能不是实际文件！")
                with open(output_path + '.error.html', 'wb') as f:
                    f.write(response.content)
                if attempt + 1 < retry:
                    logger.info("将重试下载...")
                    time.sleep(2)
                    continue
            
            # 下载文件
            with open(output_path, mode) as f:
                with tqdm(
                    total=total_size, 
                    initial=downloaded_size,
                    unit='B', 
                    unit_scale=True,
                    desc=os.path.basename(output_path)
                ) as pbar:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
                            pbar.update(len(chunk))
            
            logger.info(f"文件下载完成: {output_path}")
            return True
            
        except requests.exceptions.RequestException as e:
            logger.error(f"下载失败: {e}")
            if attempt + 1 < retry:
                logger.info(f"将在 {timeout//3} 秒后重试...")
                time.sleep(timeout//3)
            else:
                logger.error(f"下载 {url} 失败，已达到最大重试次数")
                return False

def extract_zip(zip_path, output_dir):
    """解压ZIP文件"""
    try:
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(output_dir)
        logger.info(f"文件解压完成: {output_dir}")
        return True
    except Exception as e:
        logger.error(f"解压失败: {e}")
        return False

def download_model(model_id, output_dir, proxies=None, retry=5, timeout=60, use_backup=False):
    """下载指定的模型"""
    if model_id not in TTS_MODELS:
        logger.error(f"未知的模型ID: {model_id}")
        return False
    
    model_info = TTS_MODELS[model_id]
    model_name = model_info["name"]
    model_desc = model_info["description"]
    
    urls = []
    if use_backup and "backup_urls" in model_info:
        # 优先使用备用链接
        urls = model_info["backup_urls"] + [model_info["primary_url"]]
    else:
        # 优先使用主链接
        urls = [model_info["primary_url"]] + model_info.get("backup_urls", [])
    
    logger.info(f"准备下载模型: {model_name} - {model_desc}")
    
    # 安全处理模型名称作为目录名
    model_dir_name = model_name.replace('/', '--').replace('_', '-')
    zip_path = os.path.join(output_dir, f"{model_dir_name}.zip")
    
    # 尝试每个URL
    for url in urls:
        logger.info(f"尝试从 {url} 下载...")
        if download_file(url, zip_path, proxies, retry, timeout):
            # 下载成功，检查MD5(如果有)
            if model_info.get("md5") and md5_checksum(zip_path) != model_info["md5"]:
                logger.warning(f"MD5校验失败，文件可能已损坏: {zip_path}")
                continue
                
            # 解压文件
            if extract_zip(zip_path, output_dir):
                return True
    
    # 所有URL都尝试失败
    logger.error(f"所有下载源尝试失败: {model_id}")
    return False

def create_minimal_test_model(output_dir):
    """创建最小测试模型，用于验证系统功能"""
    model_info = MINIMAL_TEST_MODEL
    model_name = model_info["name"]
    model_desc = model_info["description"]
    
    logger.info(f"创建最小测试模型: {model_name} - {model_desc}")
    
    # 构建目标目录
    target_dir = os.path.join(output_dir, *model_name.split('/'))
    os.makedirs(target_dir, exist_ok=True)
    
    # 创建最小配置文件
    config = {
        "model": "tacotron2",
        "text_cleaner": "basic_cleaners",
        "num_chars": 100,
        "audio": {
            "num_mels": 80,
            "sample_rate": 22050
        },
        "use_phonemes": False,
        "is_test_model": True
    }
    
    # 写入配置文件
    config_path = os.path.join(target_dir, "config.json")
    with open(config_path, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=4)
    
    # 创建空模型文件以满足检查
    model_path = os.path.join(target_dir, "dummy_model.pth")
    with open(model_path, 'wb') as f:
        f.write(b'\x00' * 1024)
    
    logger.info(f"最小测试模型创建完成: {target_dir}")
    return True

def fix_model_directory_structure(output_dir):
    """修复模型目录结构，确保符合TTS库期望的格式"""
    logger.info("检查和修复模型目录结构...")
    
    # 检查是否有需要修复的目录
    for root, dirs, files in os.walk(output_dir):
        for d in dirs:
            if '--' in d:
                # 这可能是下载后未正确解压的目录
                src_path = os.path.join(root, d)
                # 将--替换回/创建正确的目录结构
                dest_path = os.path.join(output_dir, d.replace('--', '/'))
                
                logger.info(f"修复目录结构: {src_path} -> {dest_path}")
                
                # 创建目标目录
                os.makedirs(os.path.dirname(dest_path), exist_ok=True)
                
                # 移动文件
                if os.path.exists(dest_path):
                    # 如果目标已存在，合并内容
                    for item in os.listdir(src_path):
                        s = os.path.join(src_path, item)
                        d = os.path.join(dest_path, item)
                        if os.path.isdir(s):
                            shutil.copytree(s, d, dirs_exist_ok=True)
                        else:
                            shutil.copy2(s, d)
                    # 删除源目录
                    shutil.rmtree(src_path)
                else:
                    # 直接移动
                    os.makedirs(os.path.dirname(dest_path), exist_ok=True)
                    shutil.move(src_path, dest_path)
    
    logger.info("目录结构修复完成")
    return True

def main():
    """主函数"""
    args = setup_argparse()
    
    # 获取输出目录
    output_dir = get_output_dir(args)
    logger.info(f"模型输出目录: {output_dir}")
    
    # 获取代理设置
    proxies = get_proxies(args)
    
    # 处理创建最小测试模型请求
    if args.create_minimal:
        if create_minimal_test_model(output_dir):
            logger.info("最小测试模型创建成功")
        else:
            logger.error("最小测试模型创建失败")
        return
    
    # 确定要下载的模型
    models_to_download = []
    if args.all:
        models_to_download = list(TTS_MODELS.keys())
    elif args.model:
        models_to_download = [args.model]
    else:
        logger.error("未指定要下载的模型，请使用--model或--all选项")
        return
    
    # 下载模型
    success_count = 0
    for model_id in models_to_download:
        logger.info(f"=== 开始下载模型: {model_id} ===")
        if download_model(model_id, output_dir, proxies, args.retry, args.timeout, args.use_backup):
            success_count += 1
            logger.info(f"模型 {model_id} 下载成功")
        else:
            logger.error(f"模型 {model_id} 下载失败")
    
    # 修复目录结构
    fix_model_directory_structure(output_dir)
    
    # 总结
    logger.info(f"共尝试下载 {len(models_to_download)} 个模型，成功下载 {success_count} 个")
    if success_count < len(models_to_download):
        logger.warning("部分模型下载失败，您可以尝试使用--use-backup选项使用备用链接，或手动下载")
    
    # 如果没有成功下载任何模型，创建最小测试模型
    if success_count == 0:
        logger.info("所有模型下载失败，创建最小测试模型作为临时解决方案")
        create_minimal_test_model(output_dir)

if __name__ == "__main__":
    main() 