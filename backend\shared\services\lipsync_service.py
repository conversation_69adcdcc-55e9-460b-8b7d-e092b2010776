import os
import logging
import json
import tempfile
import uuid
from typing import Dict, List, Optional, Tuple
import numpy as np
import cv2
import random
import wave

# 导入内部模块
from services.tts_service import get_tts_service

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class LipSyncService:
    """唇形同步服务类"""
    
    def __init__(self):
        # 唇形姿势映射表（简化版）
        self.viseme_map = {
            'sil': {'mouth_open': 0.0, 'smile': 0.0},  # 闭嘴
            'ah': {'mouth_open': 0.8, 'smile': 0.0},   # 大开口元音
            'eh': {'mouth_open': 0.6, 'smile': 0.2},   # 中开口元音
            'ih': {'mouth_open': 0.3, 'smile': 0.3},   # 小开口元音
            'oh': {'mouth_open': 0.7, 'smile': -0.1},  # 圆唇元音
            'uh': {'mouth_open': 0.4, 'smile': -0.2},  # 圆唇小开口
            'b': {'mouth_open': 0.0, 'smile': 0.1},    # 双唇音
            'p': {'mouth_open': 0.0, 'smile': 0.0},    # 双唇音
            'm': {'mouth_open': 0.0, 'smile': -0.1},   # 双唇音
            'f': {'mouth_open': 0.2, 'smile': 0.0},    # 唇齿音
            'v': {'mouth_open': 0.2, 'smile': 0.0},    # 唇齿音
            'd': {'mouth_open': 0.2, 'smile': 0.1},    # 舌尖音
            't': {'mouth_open': 0.2, 'smile': 0.0},    # 舌尖音
            'l': {'mouth_open': 0.3, 'smile': 0.2},    # 舌侧音
            'n': {'mouth_open': 0.2, 'smile': 0.0},    # 舌尖音
            'g': {'mouth_open': 0.3, 'smile': -0.1},   # 舌根音
            'k': {'mouth_open': 0.3, 'smile': 0.0},    # 舌根音
            'h': {'mouth_open': 0.4, 'smile': 0.0},    # 喉音
            'j': {'mouth_open': 0.3, 'smile': 0.3},    # 舌面音
            'sh': {'mouth_open': 0.2, 'smile': 0.1},   # 舌尖音
            'z': {'mouth_open': 0.2, 'smile': 0.1},    # 舌尖音
            'ch': {'mouth_open': 0.2, 'smile': 0.0},   # 舌尖音
            's': {'mouth_open': 0.2, 'smile': 0.2},    # 舌尖音
            'r': {'mouth_open': 0.3, 'smile': -0.1},   # 卷舌音
            'y': {'mouth_open': 0.3, 'smile': 0.3},    # 半元音
            'w': {'mouth_open': 0.2, 'smile': -0.3},   # 半元音
        }
        
        logger.info("唇形同步服务已初始化")
    
    def generate_phonemes(self, text):
        """根据文本生成音素序列"""
        try:
            # 使用TTS服务生成音素
            tts_service = get_tts_service()
            phonemes = tts_service.generate_phonemes(text)
            
            if not phonemes:
                logger.warning(f"TTS服务未能生成音素序列: {text}")
                return []
            
            logger.info(f"从文本生成了{len(phonemes)}个音素点")
            return phonemes
        except Exception as e:
            logger.error(f"生成音素时出错: {e}")
            return []
    
    def generate_expressions(self, audio_path):
        """从音频文件生成表情参数"""
        if not audio_path or not os.path.exists(audio_path):
            logger.error(f"音频文件不存在: {audio_path}")
            return []
            
        try:
            logger.info(f"从音频文件生成表情参数: {audio_path}")
            
            # 尝试使用TTS服务从音频提取音素（如果支持）
            tts_service = get_tts_service()
            
            # 检查TTS服务是否支持从音频中提取音素
            if hasattr(tts_service, 'extract_phonemes_from_audio'):
                logger.info("使用TTS服务从音频中提取音素")
                phonemes = tts_service.extract_phonemes_from_audio(audio_path)
                if phonemes:
                    logger.info(f"成功从音频提取{len(phonemes)}个音素点")
                    return self.generate_expressions_from_phonemes(phonemes)
            
            # 如果TTS服务不支持从音频提取，使用简化方法生成表达式
            # 基于音频时长估算音素序列
            logger.info("TTS服务不支持音频音素提取，使用音频时长估算")
            duration = self._estimate_audio_duration(audio_path)
            
            # 创建简单的音素序列，每0.1秒一个音素
            frames_per_second = 30
            phoneme_interval = 0.1  # 每0.1秒一个音素
            phoneme_count = int(duration / phoneme_interval)
            
            # 创建简化的音素序列（均匀分布）
            phonemes = []
            available_phonemes = ["a", "e", "i", "o", "u", "p", "b", "m", "f", "v", "t", "d", "s", "z", "n", "l", "r"]
            
            for i in range(phoneme_count):
                # 选择随机音素
                phoneme = random.choice(available_phonemes)
                time_sec = i * phoneme_interval
                # 持续时间为固定的0.1秒
                duration_sec = 0.1
                phonemes.append({"phoneme": phoneme, "time": time_sec, "duration": duration_sec})
            
            logger.info(f"基于音频时长{duration}秒，创建了{len(phonemes)}个估算音素点")
            return self.generate_expressions_from_phonemes(phonemes)
        
        except Exception as e:
            logger.error(f"生成表情时出错: {e}")
            return []
    
    def _estimate_audio_duration(self, audio_path):
        """估算音频文件的持续时间（秒）"""
        try:
            # 尝试使用wave库获取音频时长（仅支持WAV格式）
            if audio_path.lower().endswith('.wav'):
                with wave.open(audio_path, 'rb') as wav_file:
                    # 获取采样率和帧数
                    frame_rate = wav_file.getframerate()
                    n_frames = wav_file.getnframes()
                    duration = n_frames / float(frame_rate)
                    logger.info(f"使用wave库估算音频持续时间: {duration}秒")
                    return duration
            
            # 尝试使用OpenCV读取音频文件（支持更多格式）
            try:
                import cv2
                audio = cv2.VideoCapture(audio_path)
                if audio.isOpened():
                    # 获取帧率和帧数
                    fps = audio.get(cv2.CAP_PROP_FPS)
                    frame_count = int(audio.get(cv2.CAP_PROP_FRAME_COUNT))
                    if fps > 0 and frame_count > 0:
                        duration = frame_count / fps
                        logger.info(f"使用OpenCV估算音频持续时间: {duration}秒")
                        audio.release()
                        return duration
                    audio.release()
            except Exception as cv_error:
                logger.warning(f"使用OpenCV估算音频持续时间失败: {cv_error}")
            
            # 如果都失败了，返回默认时长
            logger.warning(f"无法估算音频时长，使用默认值3秒")
            return 3.0
            
        except Exception as e:
            logger.error(f"估算音频持续时间出错: {e}")
            return 3.0
            
    def phoneme_to_viseme(self, phoneme):
        """将音素映射到视觉形态"""
        # 获取唇形参数，如果不存在则使用默认闭嘴参数
        return self.viseme_map.get(phoneme, self.viseme_map['sil'])
    
    def generate_expressions_from_phonemes(self, phonemes: List[Dict], fps: int = 30) -> List[Dict[str, float]]:
        """
        从音素序列生成表情参数序列
        
        Args:
            phonemes: 音素列表，每个音素包含类型和持续时间
            fps: 每秒帧数
            
        Returns:
            expressions: 表情参数列表
        """
        expressions = []
        total_duration = sum(p["duration"] for p in phonemes)
        total_frames = int(total_duration * fps)
        
        current_time = 0
        current_phoneme_idx = 0
        
        for frame_idx in range(total_frames):
            frame_time = frame_idx / fps
            
            # 找到当前时间对应的音素
            while (current_phoneme_idx < len(phonemes) - 1 and 
                   current_time + phonemes[current_phoneme_idx]["duration"] <= frame_time):
                current_time += phonemes[current_phoneme_idx]["duration"]
                current_phoneme_idx += 1
            
            # 获取当前音素和唇形参数
            current_phoneme = phonemes[current_phoneme_idx]["phoneme"]
            viseme = self.phoneme_to_viseme(current_phoneme)
            
            # 如果当前帧与前一帧或后一帧之间有过渡，计算平滑插值
            expression = viseme.copy()
            
            # 添加额外的表情参数用于自然度
            expression["eyebrow_raise"] = 0.0
            expression["eye_open_left"] = 1.0
            expression["eye_open_right"] = 1.0
            
            # 随机眨眼动作（每3秒左右眨眼一次）
            if frame_idx % (fps * 3) >= (fps * 3 - 3) and frame_idx % 3 == 0:
                expression["eye_open_left"] = 0.1
                expression["eye_open_right"] = 0.1
            
            # 根据情绪添加眉毛动作（每10秒变化一次）
            if (frame_idx // (fps * 10)) % 2 == 0:
                expression["eyebrow_raise"] = 0.2
            
            expressions.append(expression)
        
        return expressions
    
    async def text_to_lipsync(self, text: str, voice_id: str = None, lang: str = "zh-cn") -> Tuple[str, List[Dict[str, float]]]:
        """
        将文本转换为语音并生成唇形同步数据
        
        Args:
            text: 输入文本
            voice_id: 语音ID
            lang: 语言代码
            
        Returns:
            audio_path: 生成的音频文件路径
            expressions: 表情参数列表
        """
        try:
            # 获取TTS服务
            tts = get_tts_service()
            
            # 生成语音
            audio_path = await tts.text_to_speech(text, voice_id)
            
            # 生成音素序列
            phonemes = tts.generate_phonemes(text, lang)
            
            # 生成表情序列
            expressions = self.generate_expressions_from_phonemes(phonemes)
            
            logger.info(f"已生成唇形同步数据，共 {len(expressions)} 帧")
            
            return audio_path, expressions
        except Exception as e:
            logger.error(f"唇形同步处理失败: {e}")
            return "", []
    
    def save_lipsync_data(self, expressions: List[Dict[str, float]], output_path: str) -> bool:
        """
        保存唇形同步数据到文件
        
        Args:
            expressions: 表情参数列表
            output_path: 输出文件路径
            
        Returns:
            success: 是否成功保存
        """
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(expressions, f, ensure_ascii=False, indent=2)
            logger.info(f"唇形同步数据已保存至 {output_path}")
            return True
        except Exception as e:
            logger.error(f"保存唇形同步数据失败: {e}")
            return False
    
    def load_lipsync_data(self, file_path: str) -> List[Dict[str, float]]:
        """
        从文件加载唇形同步数据
        
        Args:
            file_path: 文件路径
            
        Returns:
            expressions: 表情参数列表
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                expressions = json.load(f)
            logger.info(f"已加载唇形同步数据，共 {len(expressions)} 帧")
            return expressions
        except Exception as e:
            logger.error(f"加载唇形同步数据失败: {e}")
            return []
    
    def analyze_expressions(self, expressions: List[Dict[str, float]]) -> Dict:
        """
        分析表情参数序列，获取统计信息
        
        Args:
            expressions: 表情参数列表
            
        Returns:
            stats: 统计信息
        """
        if not expressions:
            return {"error": "没有表情数据"}
        
        # 收集所有参数名称
        param_names = set()
        for expr in expressions:
            param_names.update(expr.keys())
        
        # 计算每个参数的统计数据
        stats = {}
        for param in param_names:
            values = [expr.get(param, 0) for expr in expressions]
            stats[param] = {
                "min": min(values),
                "max": max(values),
                "avg": sum(values) / len(values),
                "range": max(values) - min(values)
            }
        
        # 添加基本信息
        stats["frames_count"] = len(expressions)
        stats["duration_seconds"] = len(expressions) / 30  # 假设30fps
        
        return stats

# 单例模式，全局可访问的唇形同步服务实例
lipsync_service = LipSyncService()

def get_lipsync_service() -> LipSyncService:
    """获取唇形同步服务单例"""
    return lipsync_service

async def initialize() -> bool:
    """初始化唇形同步服务"""
    try:
        # 尝试预热TTS服务，确保它能正常工作
        try:
            from services.tts_service import get_tts_service, initialize as init_tts
            
            # 获取TTS服务实例
            tts_service = get_tts_service()
            
            # 记录服务状态
            logger.info("唇形同步服务已初始化，依赖TTS服务状态检查...")
            
            # 检查TTS服务是否已初始化
            if hasattr(tts_service, 'is_initialized') and callable(tts_service.is_initialized):
                tts_initialized = tts_service.is_initialized()
                if not tts_initialized:
                    logger.warning("TTS服务尚未初始化，可能会影响唇形同步功能")
            
            # 预热TTS服务 - 使用简短的文本生成测试
            logger.info("预热TTS服务...")
            try:
                # 使用很短的文本进行测试，减少资源消耗
                test_text = "测试"
                result = await tts_service.text_to_speech(test_text, None)
                logger.info(f"TTS服务预热结果: {result is not None}")
            except Exception as tts_error:
                logger.warning(f"TTS服务预热失败，但这不会阻止唇形同步服务初始化: {tts_error}")
            
        except ImportError as ie:
            logger.warning(f"导入TTS服务时出错，但唇形同步服务仍将继续初始化: {ie}")
        except Exception as e:
            logger.warning(f"预热TTS服务时出错，但唇形同步服务仍将继续初始化: {e}")
        
        logger.info("唇形同步服务初始化完成")
        return True
    except Exception as e:
        logger.error(f"初始化唇形同步服务失败: {e}")
        return False 