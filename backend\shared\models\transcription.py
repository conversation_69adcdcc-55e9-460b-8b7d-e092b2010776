from sqlalchemy import Column, Integer, String, Text, Float, DateTime, ForeignKey, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
import uuid
from datetime import datetime

Base = declarative_base()

class TranscriptionTask(Base):
    """音频转写任务模型"""
    __tablename__ = "transcription_tasks"
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # 文件信息
    file_path = Column(String(255), nullable=False)
    file_name = Column(String(255), nullable=False)
    file_size = Column(Integer, nullable=False)
    
    # 转写设置
    language = Column(String(10), default="zh-CN")
    quality = Column(String(20), default="standard")  # standard, high
    punctuation = Column(Integer, default=1)  # 1=开启, 0=关闭
    speaker_diarization = Column(Integer, default=0)  # 1=开启, 0=关闭
    output_file_name = Column(String(255))
    
    # 任务状态
    status = Column(String(20), default="pending")  # pending, processing, completed, error
    progress = Column(Float, default=0)  # 0-100
    stage = Column(String(20), default="transcribing")  # transcribing, processing, finished
    message = Column(String(255))
    
    # 转写结果
    duration = Column(String(20))  # 格式化的时长，如 "10:30"
    duration_seconds = Column(Integer)  # 时长秒数
    result_path = Column(String(255))  # 转写结果文件路径
    result_text = Column(Text)  # 完整转写文本
    paragraphs = Column(JSON)  # 段落，包含时间戳和文本
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def to_dict(self):
        """将模型转换为字典"""
        return {
            "taskId": self.id,
            "userId": self.user_id,
            "fileName": self.file_name,
            "fileSize": self.file_size,
            "language": self.language,
            "quality": self.quality,
            "punctuation": bool(self.punctuation),
            "speakerDiarization": bool(self.speaker_diarization),
            "outputFileName": self.output_file_name,
            "status": self.status,
            "progress": self.progress,
            "stage": self.stage,
            "message": self.message,
            "duration": self.duration,
            "durationSeconds": self.duration_seconds,
            "resultText": self.result_text,
            "paragraphs": self.paragraphs,
            "createdAt": self.created_at.isoformat() if self.created_at else None,
            "updatedAt": self.updated_at.isoformat() if self.updated_at else None
        } 