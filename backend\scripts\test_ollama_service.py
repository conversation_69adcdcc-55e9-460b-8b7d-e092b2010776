#!/usr/bin/env python3
"""
测试 Ollama 服务和模型
"""
import os
import sys
import asyncio
import httpx
from pathlib import Path
from dotenv import load_dotenv

# 添加后端路径
backend_dir = Path(__file__).parent.parent
sys.path.append(str(backend_dir))

# 加载环境变量
env_path = backend_dir / ".env"
load_dotenv(env_path)

async def test_ollama_connection():
    """测试 Ollama 连接"""
    print("🔍 测试 Ollama 服务连接")
    print("=" * 50)
    
    try:
        async with httpx.AsyncClient(timeout=10.0) as client:
            response = await client.get("http://localhost:11434/api/tags")
            
            if response.status_code == 200:
                models_data = response.json()
                models = models_data.get("models", [])
                
                print(f"✅ Ollama 服务连接成功")
                print(f"📊 可用模型数量: {len(models)}")
                
                for model in models:
                    name = model.get("name", "Unknown")
                    size = model.get("size", 0)
                    size_gb = size / (1024**3) if size > 0 else 0
                    modified = model.get("modified_at", "Unknown")
                    print(f"   📦 {name} ({size_gb:.1f} GB) - {modified}")
                
                return True, models
            else:
                print(f"❌ Ollama 服务响应错误: {response.status_code}")
                return False, []
                
    except httpx.ConnectError:
        print(f"❌ 无法连接到 Ollama 服务")
        print(f"💡 请确保运行: ollama serve")
        return False, []
    except Exception as e:
        print(f"❌ Ollama 连接测试失败: {e}")
        return False, []

async def test_model_chat(model_name: str):
    """测试特定模型的对话功能"""
    print(f"\n🤖 测试模型: {model_name}")
    print("=" * 50)
    
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            payload = {
                "model": model_name,
                "messages": [
                    {
                        "role": "system",
                        "content": "You are a helpful English teacher. Respond naturally and conversationally."
                    },
                    {
                        "role": "user",
                        "content": "Hello! Can you help me learn English?"
                    }
                ],
                "stream": False,
                "options": {
                    "temperature": 0.7,
                    "top_p": 0.9,
                    "max_tokens": 200
                }
            }
            
            print(f"📤 发送测试消息...")
            response = await client.post(
                "http://localhost:11434/api/chat",
                json=payload
            )
            
            if response.status_code == 200:
                result = response.json()
                message = result.get("message", {}).get("content", "")
                
                if message:
                    print(f"✅ 模型响应成功")
                    print(f"📝 回复内容: {message}")
                    print(f"📊 回复长度: {len(message)} 字符")
                    return True, message
                else:
                    print(f"❌ 模型返回空响应")
                    print(f"📊 完整响应: {result}")
                    return False, ""
            else:
                print(f"❌ 模型响应错误: {response.status_code}")
                print(f"📊 错误内容: {response.text}")
                return False, ""
                
    except Exception as e:
        print(f"❌ 模型测试失败: {e}")
        return False, ""

async def test_ai_agent_service():
    """测试 AI 智能体服务"""
    print(f"\n🎯 测试 AI 智能体服务")
    print("=" * 50)
    
    try:
        from app.services.ai_agent_service import AiAgentService
        
        service = AiAgentService()
        
        # 模拟智能体数据
        mock_agent = {
            "id": "test-agent",
            "name": "语言学习助手",
            "agent_type": "language_tutor",
            "system_prompt": "You are a professional English teacher."
        }
        
        print(f"📤 测试智能体对话...")
        response = await service.chat_with_agent(mock_agent, "Hello! How are you today?")
        
        if response and len(response) > 10:
            print(f"✅ AI 智能体服务正常")
            print(f"📝 智能体回复: {response}")
            print(f"📊 回复长度: {len(response)} 字符")
            return True, response
        else:
            print(f"❌ AI 智能体服务回复异常")
            print(f"📊 回复内容: {response}")
            return False, response
            
    except Exception as e:
        print(f"❌ AI 智能体服务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False, ""

async def test_chat_api():
    """测试聊天 API 接口"""
    print(f"\n🌐 测试聊天 API 接口")
    print("=" * 50)
    
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            url = "http://localhost:8000/api/v1/agents/e55f5e84-6d8b-4265-8e55-728bdb0d2455/chat"
            data = {
                "message": "Hello! I want to practice English conversation.",
                "user_id": "test-user"
            }
            
            print(f"📤 调用聊天 API...")
            response = await client.post(url, json=data)
            
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    api_response = result.get("response", "")
                    print(f"✅ 聊天 API 调用成功")
                    print(f"📝 API 回复: {api_response}")
                    print(f"📊 回复长度: {len(api_response)} 字符")
                    return True, api_response
                else:
                    print(f"❌ 聊天 API 返回错误: {result.get('error')}")
                    return False, ""
            else:
                print(f"❌ 聊天 API 状态码错误: {response.status_code}")
                print(f"📊 错误内容: {response.text}")
                return False, ""
                
    except Exception as e:
        print(f"❌ 聊天 API 测试失败: {e}")
        return False, ""

async def main():
    """主函数"""
    print("🚀 Ollama 服务和 AI 模型测试")
    print("=" * 60)
    
    # 1. 测试 Ollama 连接
    ollama_ok, models = await test_ollama_connection()
    
    if not ollama_ok:
        print(f"\n❌ Ollama 服务不可用，无法继续测试")
        print(f"💡 请先启动 Ollama 服务: ollama serve")
        return
    
    # 2. 测试推荐的模型
    recommended_models = ["qwen2.5:7b", "mistral:7b", "llama3.2:3b"]
    working_models = []
    
    for model_name in recommended_models:
        # 检查模型是否存在
        model_exists = any(model.get("name") == model_name for model in models)
        if model_exists:
            model_ok, response = await test_model_chat(model_name)
            if model_ok:
                working_models.append(model_name)
        else:
            print(f"\n⚠️ 模型 {model_name} 不存在，跳过测试")
    
    # 3. 测试 AI 智能体服务
    service_ok, service_response = await test_ai_agent_service()
    
    # 4. 测试聊天 API
    api_ok, api_response = await test_chat_api()
    
    # 总结
    print(f"\n📊 测试结果总结:")
    print(f"{'✅' if ollama_ok else '❌'} Ollama 服务: {'正常' if ollama_ok else '异常'}")
    print(f"📦 可用模型: {len(models)} 个")
    print(f"🎯 工作正常的推荐模型: {len(working_models)} 个")
    for model in working_models:
        print(f"   ✅ {model}")
    print(f"{'✅' if service_ok else '❌'} AI 智能体服务: {'正常' if service_ok else '异常'}")
    print(f"{'✅' if api_ok else '❌'} 聊天 API: {'正常' if api_ok else '异常'}")
    
    # 建议
    print(f"\n💡 建议:")
    if working_models:
        best_model = working_models[0]
        print(f"   🎯 推荐使用模型: {best_model}")
        print(f"   🔧 可在 .env 中设置: OLLAMA_MODEL={best_model}")
    
    if api_ok:
        print(f"   🎉 系统完全正常！现在应该能获得高质量的 AI 回复")
    elif service_ok:
        print(f"   ⚠️ AI 服务正常，但 API 有问题，请检查后端服务")
    else:
        print(f"   ❌ AI 服务有问题，请检查配置和服务状态")

if __name__ == "__main__":
    asyncio.run(main())
