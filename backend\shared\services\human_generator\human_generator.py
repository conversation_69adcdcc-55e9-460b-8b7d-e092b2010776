import os
import logging
import numpy as np
import tempfile
import uuid
import shutil
import time
import asyncio
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
from pathlib import Path
import json
import random
from pydantic import BaseModel

from services.progress_tracker import get_progress_tracker
from services.sad_talker import get_sad_talker, SadTalker
from services.tts_service import get_tts_service
from services.media_utils import get_media_utils
from services.media_quality import get_quality_analyzer
from services.minio_storage import save_image_to_storage
from .task_state_manager import TaskStateManager
from .task_queue import TaskQueue, TaskPriority, TaskStatus
from .checkpoint_manager import CheckpointManager
from .performance_monitor import PerformanceMonitor

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 添加调试模式环境变量
DEBUG_MODE = os.getenv("HUMAN_GENERATOR_DEBUG", "false").lower() in ["true", "1", "yes"]
MOCK_MODE = os.getenv("HUMAN_GENERATOR_MOCK", "false").lower() in ["true", "1", "yes"]

# 自动检测模拟模式 - 如果数据库连接失败则自动启用
AUTO_MOCK_ON_DB_ERROR = os.getenv("AUTO_MOCK_ON_DB_ERROR", "true").lower() in ["true", "1", "yes"]

if DEBUG_MODE:
    logger.setLevel(logging.DEBUG)
    logger.info("数字人生成器调试模式已启用")
    
if MOCK_MODE:
    logger.info("数字人生成器模拟模式已启用 - 将返回模拟结果而不进行实际处理")

class HumanGenerator:
    """数字人生成器"""
    
    def __init__(self):
        self.sad_talker = SadTalker()
        self.task_manager = TaskStateManager()
        self.task_queue = TaskQueue()
        self.checkpoint_manager = CheckpointManager()
        self.performance_monitor = PerformanceMonitor()
        self._processing = False
        self._stop_event = asyncio.Event()
        
    async def start(self):
        """启动任务处理器"""
        self._processing = True
        self._stop_event.clear()
        asyncio.create_task(self._process_tasks())
        await self.performance_monitor.start()
        logger.info("HumanGenerator started")
        
    async def stop(self):
        """停止任务处理器"""
        self._stop_event.set()
        self._processing = False
        await self.performance_monitor.stop()
        logger.info("HumanGenerator stopped")
        
    async def _process_tasks(self):
        """处理任务队列"""
        while self._processing and not self._stop_event.is_set():
            try:
                task = await self.task_queue.get_next_task()
                if task:
                    await self._process_single_task(task)
                else:
                    await asyncio.sleep(1)  # 避免空转
                    
                # 更新队列大小监控
                queue_sizes = self.task_queue.get_queue_size()
                total_size = sum(queue_sizes.values())
                self.performance_monitor.update_queue_size(total_size)
                
            except Exception as e:
                logger.error(f"Error processing task: {str(e)}")
                logger.exception("Detailed error")
                
    async def _process_single_task(self, task: Dict) -> None:
        """
        处理单个生成任务
        
        Args:
            task: 任务信息字典
        """
        task_id = task.get("task_id")
        try:
            # 更新任务状态为处理中
            await self.task_state_manager.update_task_state(
                task_id=task_id,
                state="processing",
                progress=0
            )
            
            # 获取任务参数
            image_path = task.get("image_path")
            text = task.get("text", "")
            voice_id = task.get("voice_id")
            
            # 验证voice_id
            if voice_id in [None, 'system', '']:
                voice_id = self.sad_talker.tts_service.default_voice_id
            
            # 检查图片文件是否存在
            if not os.path.exists(image_path):
                raise FileNotFoundError(f"图片文件不存在: {image_path}")
            
            # 初始化SadTalker
            logger.info(f"初始化SadTalker，任务ID: {task_id}")
            await self.sad_talker.initialize()
            
            # 更新进度
            await self.task_state_manager.update_task_state(
                task_id=task_id,
                progress=20
            )
            
            # 生成语音
            logger.info(f"开始生成语音，任务ID: {task_id}")
            audio_path = await self.sad_talker.generate_speech(
                text=text,
                voice_id=voice_id
            )
            
            # 更新进度
            await self.task_state_manager.update_task_state(
                task_id=task_id,
                progress=50
            )
            
            # 生成视频
            logger.info(f"开始生成视频，任务ID: {task_id}")
            video_path = await self.sad_talker.generate_video(
                image_path=image_path,
                audio_path=audio_path
            )
            
            # 更新任务状态为完成
            await self.task_state_manager.update_task_state(
                task_id=task_id,
                state="completed",
                progress=100,
                result={
                    "video_path": video_path,
                    "audio_path": audio_path
                }
            )
            
            # 记录性能指标
            self.performance_monitor.record_task_completion(
                task_id=task_id,
                duration=time.time() - task.get("start_time", time.time()),
                success=True
            )
            
        except Exception as e:
            logger.error(f"处理任务失败，任务ID: {task_id}, 错误: {str(e)}")
            # 更新任务状态为失败
            await self.task_state_manager.update_task_state(
                task_id=task_id,
                state="failed",
                error=str(e)
            )
            # 记录性能指标
            self.performance_monitor.record_task_completion(
                task_id=task_id,
                duration=time.time() - task.get("start_time", time.time()),
                success=False
            )
            raise
        
    async def add_generation_task(self, task_id: str, data: Dict[str, Any], priority: TaskPriority = TaskPriority.NORMAL) -> None:
        """添加生成任务到队列"""
        await self.task_queue.add_task(task_id, data, priority)
        logger.info(f"Task {task_id} added to generation queue with priority {priority.name}")
        
    async def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        task = await self.task_queue.get_task(task_id)
        if task:
            return {
                "task_id": task.task_id,
                "status": task.status.value,
                "progress": task.progress if hasattr(task, "progress") else None,
                "result": task.result,
                "error": task.error,
                "created_at": task.created_at,
                "updated_at": task.updated_at
            }
        return None
        
    async def cancel_task(self, task_id: str) -> None:
        """取消任务"""
        await self.task_queue.cancel_task(task_id)
        # 删除检查点
        await self.checkpoint_manager.delete_checkpoint(task_id)
        logger.info(f"Task {task_id} cancelled")
        
    def get_queue_status(self) -> Dict[str, int]:
        """获取队列状态"""
        return {
            priority.name: size
            for priority, size in self.task_queue.get_queue_size().items()
        }
        
    def get_performance_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        return self.performance_monitor.get_metrics()
        
    def get_performance_report(self, time_range: Optional[timedelta] = None) -> Dict[str, Any]:
        """获取性能报告"""
        return self.performance_monitor.get_performance_report(time_range)
        
    def initialize_model(self, model_type: str = "default") -> bool:
        """
        初始化数字人模型
        
        Args:
            model_type: 模型类型，可选值: default, realistic, cartoon, stylized
            
        Returns:
            bool: 是否成功初始化
        """
        try:
            if model_type not in self.model_types:
                logger.warning(f"未找到模型类型 '{model_type}'，使用默认模型")
                model_type = "default"
            
            self.current_model_type = model_type
            logger.info(f"数字人模型 '{model_type}' 已初始化")
            return True
        except Exception as e:
            logger.error(f"初始化数字人模型失败: {e}")
            return False
    
    def _get_storage_path(self, filename: str, temp: bool = True) -> str:
        """
        获取存储路径
        
        Args:
            filename: 文件名
            temp: 是否使用临时目录
            
        Returns:
            str: 存储路径
        """
        use_temp = temp
        if self.permanent_storage_enabled:
            use_temp = False
            
        if use_temp:
            return os.path.join(self.temp_storage_dir, filename)
        else:
            return os.path.join(self.permanent_storage_dir, filename)
    
    def generate_digital_human(self, 
                              image_path: str, 
                              model_type: str = "default", 
                              output_path: Optional[str] = None,
                              use_permanent_storage: bool = None,
                              gender: Optional[str] = None) -> Optional[str]:
        """
        生成数字人模型
        
        Args:
            image_path: 输入图像路径
            model_type: 模型类型
            output_path: 输出路径，如果为None则自动生成
            use_permanent_storage: 是否使用永久存储，如果为None则根据设置决定
            gender: 性别 (male/female/other)
            
        Returns:
            str: 生成的数字人模型路径，失败返回None
        """
        try:
            logger.info(f"生成数字人模型: {image_path}, 类型: {model_type}, 性别: {gender or '未指定'}")
            
            # 验证输入
            if not image_path or not os.path.exists(image_path):
                logger.error(f"输入图像路径无效: {image_path}")
                return None
                
            # 确定是否使用临时存储
            if use_permanent_storage is None:
                use_permanent_storage = self.use_permanent_storage
                
            use_temp = not use_permanent_storage
            
            # 确定输出路径
            if output_path is None:
                # 自动生成输出路径
                filename = f"{os.path.splitext(os.path.basename(image_path))[0]}_dh.mp4"
                output_path = self._get_storage_path(filename, temp=use_temp)
            
            # 这里应有实际的数字人生成代码
            # 为示例，我们仅记录日志并模拟处理时间
            logger.info(f"生成数字人模型: 输入={image_path}, 类型={model_type}, 性别={gender}, 输出={output_path}")
            time.sleep(0.5)  # 模拟处理时间
            
            # 模拟生成文件
            with open(output_path, "w") as f:
                f.write("# 数字人模型文件 - 示例内容")
                
            logger.info(f"数字人模型生成成功: {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"生成数字人模型失败: {e}")
            return None
            
    def generate_from_video(self, 
                           video_path: str, 
                           model_type: str = "default",
                           output_path: Optional[str] = None) -> Optional[str]:
        """
        从视频生成数字人模型
        
        Args:
            video_path: 输入视频路径
            model_type: 模型类型
            output_path: 输出路径
            
        Returns:
            str: 生成的数字人模型路径，失败返回None
        """
        try:
            logger.info(f"从视频生成数字人: {video_path}")
            # 实际实现应从视频中提取关键帧，然后生成数字人
            # 这里简化为从视频生成数字人模型
            return self.generate_digital_human(video_path, model_type, output_path)
        except Exception as e:
            logger.error(f"从视频生成数字人失败: {e}")
            return None
            
    def get_available_models(self) -> Dict[str, str]:
        """
        获取可用的模型类型
        
        Returns:
            Dict[str, str]: 模型类型字典 {类型ID: 描述}
        """
        return self.model_types
        
    def predict_generation_time(self, image_size: int, model_type: str = "default") -> int:
        """
        预测生成数字人所需的时间（秒）
        
        Args:
            image_size: 图像大小（字节）
            model_type: 模型类型
            
        Returns:
            int: 预计所需时间（秒）
        """
        # 简单估计，根据图像大小和模型类型
        base_time = 30  # 基础时间30秒
        
        # 根据模型类型调整时间
        model_factor = {
            "default": 1.0,
            "realistic": 2.0,
            "cartoon": 1.2,
            "stylized": 1.5
        }.get(model_type, 1.0)
        
        # 根据图像大小调整时间
        size_factor = max(1.0, image_size / (1024 * 1024) * 0.5)  # 每MB增加0.5的系数
        
        estimated_time = int(base_time * model_factor * size_factor)
        return max(10, estimated_time)  # 最少10秒 

    async def process_generation_task(self, task_id: str, request: Dict[str, Any]) -> Dict[str, Any]:
        """处理数字人生成任务"""
        start_time = time.time()
        
        try:
            # 初始化任务
            await self.task_manager.create_task(task_id, {
                "status": "initialized",
                "progress": 0.0,
                "message": "开始处理生成任务"
            })
            
            # 获取和记录请求参数
            media_file_path = request.get("media_file_path")
            gender = request.get("gender")
            
            logger.info(f"处理生成任务: {task_id}, 媒体文件: {media_file_path}, 性别: {gender or '未指定'}")
            
            if not media_file_path:
                raise ValueError("未提供媒体文件路径")
                
            # 更新任务状态为处理中
            await self.task_manager.update_task(task_id, {
                "status": "processing",
                "progress": 10.0,
                "message": "正在处理媒体文件"
            })
            
            # 初始化SadTalker
            await self.task_manager.update_task(task_id, {
                "progress": 20.0,
                "message": "正在初始化生成器"
            })
            
            # 处理音频
            audio_result = await self._process_audio(
                task_id=task_id,
                welcome_text=request.get("welcome_text", ""),
                voice_id=request.get("voice_id", "default")
            )
            
            # 生成视频 - 如果支持，传递性别参数给_generate_video方法
            video_result = await self._generate_video(
                task_id=task_id,
                media_file_path=media_file_path,
                audio_path=audio_result["audio_path"],
                gender=gender
            )
            
            # 更新任务状态为完成
            end_time = time.time()
            total_time = end_time - start_time
            
            await self.task_manager.update_task(task_id, {
                "status": "completed",
                "progress": 100.0,
                "message": "数字人生成完成",
                "result": {
                    "video_url": video_result["video_url"],
                    "audio_url": audio_result["audio_url"],
                    "processing_time": total_time
                }
            })
            
            return {
                "success": True,
                "output_path": video_result["video_url"],
                "audio_path": audio_result["audio_url"],
                "processing_time": total_time
            }
            
        except Exception as e:
            error_msg = f"Generation task {task_id} failed: {str(e)}"
            logger.error(error_msg)
            logger.exception("详细错误信息")
            
            await self.task_manager.update_task(task_id, {
                "status": "failed",
                "progress": 0.0,
                "message": f"生成失败: {str(e)}"
            })
            
            return {
                "success": False,
                "error": str(e)
            }

    async def _process_audio(self, task_id: str, welcome_text: str, voice_id: str) -> Dict[str, Any]:
        """处理音频生成"""
        try:
            await self.task_manager.update_task(task_id, {
                "progress": 30.0,
                "message": "正在生成语音"
            })
            
            # 验证voice_id，如果是None或系统默认或空字符串，使用默认语音
            if voice_id is None or voice_id == 'system' or voice_id == '':
                logger.info(f"Task {task_id}: 未提供有效语音ID，使用默认语音")
                voice_id = "zh-female1"  # 使用默认语音ID
            
            logger.info(f"Task {task_id}: 使用语音ID: {voice_id} 生成语音")
            
            # 调用generate_speech生成语音
            audio_path = await self.sad_talker.generate_speech(
                text=welcome_text,
                voice_id=voice_id
            )
            
            await self.task_manager.update_task(task_id, {
                "progress": 40.0,
                "message": "正在处理音频"
            })
            
            processed_audio = await self.sad_talker.process_audio(audio_path)
            
            return {
                "audio_path": processed_audio["path"],
                "audio_url": processed_audio["url"]
            }
            
        except Exception as e:
            logger.error(f"Task {task_id}: _process_audio处理失败: {str(e)}")
            logger.exception(f"Task {task_id}: _process_audio详细错误")
            raise

    async def _generate_video(self, task_id: str, media_file_path: str, audio_path: str, gender: Optional[str] = None) -> Dict[str, Any]:
        """生成视频"""
        try:
            await self.task_manager.update_task(task_id, {
                "progress": 60.0,
                "message": "正在处理视频"
            })
            
            video_result = await self.sad_talker.generate_video(
                source_image=media_file_path,
                audio_path=audio_path,
                gender=gender
            )
            
            await self.task_manager.update_task(task_id, {
                "progress": 90.0,
                "message": "正在保存结果"
            })
            
            return {
                "video_path": video_result["path"],
                "video_url": video_result["url"]
            }
            
        except Exception as e:
            logger.error(f"Task {task_id}: _generate_video处理失败: {str(e)}")
            logger.exception(f"Task {task_id}: _generate_video详细错误")
            raise

    async def _process_mock_generation(self, task_id: str, request):
        """
        模拟处理数字人生成任务（用于测试）
        """
        # 导入progress_tracker避免循环引用
        from services.progress_tracker import get_progress_tracker
        progress_tracker = get_progress_tracker()
        
        try:
            logger.info(f"开始模拟处理生成任务: {task_id}")
            
            # 更新任务状态为处理中
            await progress_tracker.update_task(
                task_id=task_id,
                status="processing",
                progress=10,
                message="开始模拟处理生成任务"
            )
            logger.info(f"任务状态已更新为处理中: {task_id}")
            
            # 模拟处理媒体文件
            await progress_tracker.update_task(
                task_id=task_id,
                progress=20,
                message="正在模拟处理媒体文件"
            )
            logger.info(f"模拟处理媒体文件: {task_id}")
            
            # 模拟处理延迟
            await asyncio.sleep(2)
            
            # 模拟生成数字人
            await progress_tracker.update_task(
                task_id=task_id,
                progress=50,
                message="正在模拟生成数字人"
            )
            logger.info(f"模拟生成数字人: {task_id}")
            
            # 模拟生成延迟
            await asyncio.sleep(3)
            
            # 处理请求数据
            digital_human_id = getattr(request, "digital_human_id", f"mock_{task_id}")
            
            # 生成模拟结果
            mock_result = {
                "video_url": f"/api/digital-human/media/video/mock_{task_id}",
                "thumbnail_url": f"/api/digital-human/media/thumbnail/mock_{task_id}",
                "digital_human_id": digital_human_id
            }
            
            # 更新任务状态为完成
            await progress_tracker.update_task(
                task_id=task_id,
                status="completed",
                progress=100,
                message="模拟数字人生成完成",
                result=mock_result
            )
            logger.info(f"模拟任务完成: {task_id}")
            
        except Exception as e:
            error_msg = f"模拟任务处理失败: {str(e)}"
            logger.error(error_msg)
            logger.debug("详细错误信息", exc_info=True)
            try:
                await progress_tracker.update_task(
                    task_id=task_id,
                    status="failed",
                    progress=0,
                    message=error_msg
                )
            except Exception as update_error:
                logger.error(f"更新任务状态失败: {str(update_error)}") 