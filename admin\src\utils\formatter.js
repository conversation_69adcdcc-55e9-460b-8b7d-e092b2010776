/**
 * 格式化日期时间
 * @param {Date|string|number} date - 日期对象、时间戳或日期字符串
 * @param {string} format - 格式化模板，如 'YYYY-MM-DD HH:mm:ss'
 * @returns {string} 格式化后的日期字符串
 */
export function formatDateTime(date, format = 'YYYY-MM-DD HH:mm:ss') {
  if (!date) return '';
  
  const d = typeof date === 'object' ? date : new Date(date);
  
  if (isNaN(d.getTime())) {
    return '';
  }
  
  const year = d.getFullYear();
  const month = d.getMonth() + 1;
  const day = d.getDate();
  const hours = d.getHours();
  const minutes = d.getMinutes();
  const seconds = d.getSeconds();
  
  const formatMap = {
    'YYYY': padZero(year, 4),
    'YY': String(year).slice(-2),
    'MM': padZero(month),
    'M': String(month),
    'DD': padZero(day),
    'D': String(day),
    'HH': padZero(hours),
    'H': String(hours),
    'mm': padZero(minutes),
    'm': String(minutes),
    'ss': padZero(seconds),
    's': String(seconds)
  };
  
  return format.replace(/YYYY|YY|MM|M|DD|D|HH|H|mm|m|ss|s/g, match => formatMap[match]);
}

/**
 * 格式化文件大小
 * @param {number} bytes - 文件大小（字节）
 * @param {number} decimals - 小数位数
 * @returns {string} 格式化后的文件大小
 */
export function formatFileSize(bytes, decimals = 2) {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(decimals)) + ' ' + sizes[i];
}

/**
 * 格式化金额
 * @param {number} amount - 金额
 * @param {string} currency - 货币符号
 * @param {number} decimals - 小数位数
 * @returns {string} 格式化后的金额
 */
export function formatMoney(amount, currency = '¥', decimals = 2) {
  if (amount === null || amount === undefined) return '';
  
  return currency + amount.toFixed(decimals).replace(/\d(?=(\d{3})+\.)/g, '$&,');
}

/**
 * 格式化数字（添加千分位分隔符）
 * @param {number} num - 数字
 * @param {number} decimals - 小数位数
 * @returns {string} 格式化后的数字
 */
export function formatNumber(num, decimals = 0) {
  if (num === null || num === undefined) return '';
  
  return num.toFixed(decimals).replace(/\d(?=(\d{3})+\.)/g, '$&,').replace(/\.0+$/, '');
}

/**
 * 格式化百分比
 * @param {number} value - 原始值 (0-1)
 * @param {number} decimals - 小数位数
 * @returns {string} 格式化后的百分比
 */
export function formatPercent(value, decimals = 2) {
  if (value === null || value === undefined) return '';
  
  return (value * 100).toFixed(decimals) + '%';
}

/**
 * 格式化手机号
 * @param {string} phone - 手机号
 * @param {string} separator - 分隔符
 * @returns {string} 格式化后的手机号
 */
export function formatPhone(phone, separator = ' ') {
  if (!phone) return '';
  
  const cleaned = ('' + phone).replace(/\D/g, '');
  
  if (cleaned.length !== 11) {
    return phone;
  }
  
  return cleaned.replace(/(\d{3})(\d{4})(\d{4})/, `$1${separator}$2${separator}$3`);
}

/**
 * 格式化身份证号
 * @param {string} idCard - 身份证号
 * @param {string} separator - 分隔符
 * @returns {string} 格式化后的身份证号
 */
export function formatIdCard(idCard, separator = ' ') {
  if (!idCard) return '';
  
  const cleaned = ('' + idCard).replace(/\s/g, '');
  
  if (cleaned.length !== 18) {
    return idCard;
  }
  
  return cleaned.replace(/(\d{6})(\d{4})(\d{4})(\w{4})/, `$1${separator}$2${separator}$3${separator}$4`);
}

/**
 * 获取相对时间描述
 * @param {Date|string|number} date - 日期对象、时间戳或日期字符串
 * @param {Date} [now=new Date()] - 当前时间，默认为当前时间
 * @returns {string} 相对时间描述
 */
export function formatRelativeTime(date, now = new Date()) {
  if (!date) return '';
  
  const d = typeof date === 'object' ? date : new Date(date);
  
  if (isNaN(d.getTime())) {
    return '';
  }
  
  const diff = now.getTime() - d.getTime();
  const diffSeconds = Math.floor(diff / 1000);
  const diffMinutes = Math.floor(diff / (1000 * 60));
  const diffHours = Math.floor(diff / (1000 * 60 * 60));
  const diffDays = Math.floor(diff / (1000 * 60 * 60 * 24));
  
  if (diffSeconds < 60) {
    return diffSeconds + '秒前';
  } else if (diffMinutes < 60) {
    return diffMinutes + '分钟前';
  } else if (diffHours < 24) {
    return diffHours + '小时前';
  } else if (diffDays < 30) {
    return diffDays + '天前';
  } else {
    return formatDateTime(d, 'YYYY-MM-DD');
  }
}

/**
 * 格式化地址
 * @param {Object} address - 地址对象
 * @param {string} address.province - 省
 * @param {string} address.city - 市
 * @param {string} address.district - 区
 * @param {string} address.detail - 详细地址
 * @returns {string} 格式化后的地址
 */
export function formatAddress(address) {
  if (!address) return '';
  
  const { province, city, district, detail } = address;
  return [province, city, district, detail].filter(Boolean).join(' ');
}

/**
 * 格式化状态
 * @param {number|string} status - 状态值
 * @param {Object} statusMap - 状态映射
 * @returns {string} 格式化后的状态文本
 */
export function formatStatus(status, statusMap) {
  return statusMap[status] || status;
}

/**
 * 数字补零
 * @param {number} n - 数字
 * @param {number} digits - 位数
 * @returns {string} 补零后的字符串
 */
function padZero(n, digits = 2) {
  let str = String(n);
  while (str.length < digits) {
    str = '0' + str;
  }
  return str;
}

export default {
  formatDateTime,
  formatFileSize,
  formatMoney,
  formatNumber,
  formatPercent,
  formatPhone,
  formatIdCard,
  formatRelativeTime,
  formatAddress,
  formatStatus
}; 