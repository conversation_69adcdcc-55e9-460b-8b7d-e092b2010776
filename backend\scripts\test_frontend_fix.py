#!/usr/bin/env python3
"""
测试前端修复后的参数传递
"""
import requests
import json

def test_correct_params():
    """测试正确的参数格式"""
    print("🧪 测试修复后的前端参数")
    print("=" * 50)
    
    # 模拟修复后前端发送的参数
    correct_params = {
        "prompt": "一个和尚敲木鱼",  # 现在应该是字符串
        "model": "t2v-1.3B",
        "duration": 10,
        "resolution": "768x512",
        "fps": 8,
        "guidance_scale": 5,
        "num_inference_steps": 50
    }
    
    print("📋 修复后的参数:")
    print(json.dumps(correct_params, indent=2, ensure_ascii=False))
    
    try:
        response = requests.post(
            "http://localhost:8000/api/v1/video/text-to-video",
            json=correct_params,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"\n📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ API 调用成功!")
            print(f"📝 响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            return result.get("task_id")
        else:
            print(f"❌ API 调用失败: {response.status_code}")
            try:
                error_detail = response.json()
                print(f"📝 错误详情: {json.dumps(error_detail, indent=2, ensure_ascii=False)}")
            except:
                print(f"📝 错误内容: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return None

def test_wrong_params():
    """测试错误的参数格式（用于对比）"""
    print(f"\n🔍 测试错误的参数格式（对比）")
    print("=" * 50)
    
    # 模拟之前错误的参数格式
    wrong_params = {
        "prompt": {  # 错误：prompt 是对象而不是字符串
            "prompt": "一个和尚敲木鱼",
            "model": "t2v-1.3B",
            "duration": 10,
            "resolution": "768x512",
            "fps": 8,
            "guidance_scale": 5,
            "num_inference_steps": 50
        },
        "model": "t2v-1.3B",
        "duration": 5,
        "resolution": "1024x576",
        "fps": 24,
        "guidance_scale": 7.5,
        "num_inference_steps": 50
    }
    
    print("📋 错误的参数格式:")
    print(json.dumps(wrong_params, indent=2, ensure_ascii=False))
    
    try:
        response = requests.post(
            "http://localhost:8000/api/v1/video/text-to-video",
            json=wrong_params,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"\n📊 响应状态码: {response.status_code}")
        
        if response.status_code == 422:
            print("✅ 预期的 422 错误（参数验证失败）")
            error_detail = response.json()
            print(f"📝 错误详情: {json.dumps(error_detail, indent=2, ensure_ascii=False)}")
        else:
            print(f"⚠️ 意外的响应状态码: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def main():
    """主函数"""
    print("🧪 前端参数修复验证")
    print("=" * 60)
    
    # 测试正确的参数
    task_id = test_correct_params()
    
    # 测试错误的参数（对比）
    test_wrong_params()
    
    # 总结
    print(f"\n📊 测试总结:")
    print(f"{'✅' if task_id else '❌'} 修复后参数: {'成功' if task_id else '失败'}")
    
    if task_id:
        print(f"\n🎉 前端参数修复成功！")
        print(f"💡 任务ID: {task_id}")
        print(f"💡 现在可以在前端界面正常生成视频")
    else:
        print(f"\n⚠️ 参数修复可能还有问题")

if __name__ == "__main__":
    main()
