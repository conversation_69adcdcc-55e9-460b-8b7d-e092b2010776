import { ref, watch } from 'vue';
// 导入独立的useDebounceFn实现
import useDebounceFn from './useDebounceFn';

/**
 * 防抖钩子
 * @param {any} value - 需要防抖的值
 * @param {number} delay - 防抖延迟毫秒数
 * @param {Object} options - 配置选项
 * @param {boolean} options.immediate - 是否立即执行
 * @returns {Object} 防抖后的值
 */
export function useDebounce(value, delay = 300, options = {}) {
  const { immediate = false } = options;
  
  // 防抖后的值
  const debounceValue = ref(value);
  
  // 定时器
  let timer = null;
  
  // 清理定时器
  const clearTimer = () => {
    if (timer) {
      clearTimeout(timer);
      timer = null;
    }
  };
  
  // 监听值变化
  watch(
    () => value,
    (newValue) => {
      clearTimer();
      
      if (immediate) {
        // 如果是第一次或timer为null，则立即执行
        if (timer === null) {
          debounceValue.value = newValue;
        }
        
        // 延迟重置timer，在此期间不会更新值
        timer = setTimeout(() => {
          timer = null;
        }, delay);
      } else {
        // 常规防抖，延迟更新值
        timer = setTimeout(() => {
          debounceValue.value = newValue;
        }, delay);
      }
    },
    { immediate }
  );
  
  return debounceValue;
}

// 重新导出从useDebounceFn.js导入的函数
export { useDebounceFn };

/**
 * 节流函数钩子
 * @param {Function} fn - 需要节流的函数
 * @param {number} delay - 节流延迟毫秒数
 * @param {Object} options - 配置选项
 * @param {boolean} options.leading - 是否在延迟开始前调用函数
 * @param {boolean} options.trailing - 是否在延迟结束后调用函数
 * @returns {Function} 节流后的函数
 */
export function useThrottleFn(fn, delay = 300, options = {}) {
  if (typeof fn !== 'function') {
    throw new Error('参数fn必须是函数');
  }
  
  const { leading = true, trailing = true } = options;
  
  // 上次调用时间
  let lastCallTime = 0;
  // 定时器
  let timer = null;
  // 保存上下文和参数
  let context = null;
  let args = null;
  
  // 执行并重置
  const invokeFunc = () => {
    const time = Date.now();
    lastCallTime = time;
    fn.apply(context, args);
    context = null;
    args = null;
  };
  
  // 节流包装函数
  const throttledFn = function(...params) {
    const time = Date.now();
    context = this;
    args = params;
    
    // 第一次调用
    if (lastCallTime === 0 && leading === false) {
      lastCallTime = time;
    }
    
    // 剩余等待时间
    const remaining = delay - (time - lastCallTime);
    
    // 如果已经过了延迟时间或是首次调用且允许领先调用
    if (remaining <= 0 || remaining > delay) {
      if (timer) {
        clearTimeout(timer);
        timer = null;
      }
      invokeFunc();
    } else if (!timer && trailing) {
      // 如果设置了延迟结束后调用
      timer = setTimeout(() => {
        // 如果不允许领先调用，重置lastCallTime为0，否则为当前时间
        lastCallTime = leading ? Date.now() : 0;
        timer = null;
        fn.apply(context, args);
        context = null;
        args = null;
      }, remaining);
    }
  };
  
  // 取消方法
  throttledFn.cancel = function() {
    if (timer) {
      clearTimeout(timer);
      timer = null;
    }
    lastCallTime = 0;
    context = null;
    args = null;
  };
  
  // 立即执行方法
  throttledFn.flush = function(...params) {
    throttledFn.cancel();
    return fn(...params);
  };
  
  return throttledFn;
} 