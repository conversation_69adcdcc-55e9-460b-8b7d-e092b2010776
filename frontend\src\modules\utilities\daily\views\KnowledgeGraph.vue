<template>
  <div class="knowledge-graph">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1>知识图谱</h1>
      <p>可视化展示热点事件的复杂关联关系</p>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <el-row :gutter="24">
        <!-- 左侧：控制面板 -->
        <el-col :xs="24" :lg="6">
          <el-card header="图谱控制" shadow="never">
            <!-- 数据源选择 -->
            <div class="control-section">
              <h4>数据源</h4>
              <el-select
                v-model="selectedDataSource"
                placeholder="选择数据源"
                style="width: 100%"
                @change="loadGraphData"
              >
                <el-option label="最近简报" value="recent-reports" />
                <el-option label="热点事件" value="hot-events" />
                <el-option label="自定义查询" value="custom" />
              </el-select>
            </div>

            <!-- 时间范围 -->
            <div class="control-section">
              <h4>时间范围</h4>
              <el-date-picker
                v-model="dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                style="width: 100%"
                @change="loadGraphData"
              />
            </div>

            <!-- 关系类型 -->
            <div class="control-section">
              <h4>关系类型</h4>
              <el-checkbox-group
                v-model="selectedRelationTypes"
                @change="filterGraph"
              >
                <el-checkbox label="causal">因果关系</el-checkbox>
                <el-checkbox label="temporal">时间关系</el-checkbox>
                <el-checkbox label="spatial">空间关系</el-checkbox>
                <el-checkbox label="semantic">语义关系</el-checkbox>
              </el-checkbox-group>
            </div>

            <!-- 节点类型 -->
            <div class="control-section">
              <h4>节点类型</h4>
              <el-checkbox-group
                v-model="selectedNodeTypes"
                @change="filterGraph"
              >
                <el-checkbox label="event">事件</el-checkbox>
                <el-checkbox label="person">人物</el-checkbox>
                <el-checkbox label="organization">机构</el-checkbox>
                <el-checkbox label="location">地点</el-checkbox>
                <el-checkbox label="concept">概念</el-checkbox>
              </el-checkbox-group>
            </div>

            <!-- 图谱操作 -->
            <div class="control-section">
              <h4>操作</h4>
              <div class="button-group">
                <el-button style="width: 100%; margin-bottom: 8px;" @click="resetGraph">重置视图</el-button>
                <el-button style="width: 100%; margin-bottom: 8px;" @click="exportGraph">导出图谱</el-button>
                <el-button style="width: 100%;" @click="saveLayout">保存布局</el-button>
              </div>
            </div>
          </el-card>

          <!-- 图例 -->
          <el-card header="图例说明" shadow="never" style="margin-top: 16px;">
            <div class="legend">
              <div class="legend-item">
                <div class="legend-node event"></div>
                <span>事件</span>
              </div>
              <div class="legend-item">
                <div class="legend-node person"></div>
                <span>人物</span>
              </div>
              <div class="legend-item">
                <div class="legend-node organization"></div>
                <span>机构</span>
              </div>
              <div class="legend-item">
                <div class="legend-node location"></div>
                <span>地点</span>
              </div>
              <div class="legend-item">
                <div class="legend-node concept"></div>
                <span>概念</span>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 右侧：图谱可视化 -->
        <el-col :xs="24" :lg="18">
          <el-card header="知识图谱可视化" shadow="never">
            <!-- 工具栏 -->
            <div class="graph-toolbar">
              <div class="toolbar-group">
                <el-button-group>
                  <el-button @click="zoomIn">
                    <el-icon><Plus /></el-icon>
                  </el-button>
                  <el-button @click="zoomOut">
                    <el-icon><Minus /></el-icon>
                  </el-button>
                  <el-button @click="fitView">
                    <el-icon><FullScreen /></el-icon>
                  </el-button>
                </el-button-group>

                <el-select
                  v-model="layoutType"
                  style="width: 120px; margin-left: 12px;"
                  @change="changeLayout"
                >
                  <el-option label="力导向" value="force" />
                  <el-option label="环形" value="circular" />
                  <el-option label="层次" value="hierarchical" />
                  <el-option label="网格" value="grid" />
                </el-select>

                <el-switch
                  v-model="showLabels"
                  active-text="显示标签"
                  inactive-text="隐藏标签"
                  style="margin-left: 12px;"
                  @change="toggleLabels"
                />
              </div>
            </div>

            <!-- 图谱容器 -->
            <div class="graph-container" ref="graphContainer">
              <div v-if="loading" class="graph-loading">
                <el-icon class="loading-icon"><Loading /></el-icon>
                <p>正在加载知识图谱...</p>
              </div>

              <div v-else-if="!graphData.nodes.length" class="graph-empty">
                <el-icon class="empty-icon"><Box /></el-icon>
                <p>暂无数据，请选择数据源</p>
              </div>

              <div v-else id="knowledge-graph" class="graph-canvas"></div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 节点详情模态框 -->
    <el-dialog
      v-model="showNodeDetail"
      :title="selectedNode?.label || '节点详情'"
      width="600px"
    >
      <div v-if="selectedNode" class="node-detail">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="类型">
            <el-tag :color="getNodeColor(selectedNode.type)">
              {{ getNodeTypeText(selectedNode.type) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="标签">
            {{ selectedNode.label }}
          </el-descriptions-item>
          <el-descriptions-item label="描述">
            {{ selectedNode.description || '暂无描述' }}
          </el-descriptions-item>
          <el-descriptions-item label="相关度">
            <el-progress
              :percentage="selectedNode.relevance * 100"
              :color="[
                { color: '#108ee9', percentage: 50 },
                { color: '#87d068', percentage: 100 }
              ]"
            />
          </el-descriptions-item>
          <el-descriptions-item label="连接数">
            {{ selectedNode.connections || 0 }}
          </el-descriptions-item>
        </el-descriptions>

        <!-- 相关链接 -->
        <div v-if="selectedNode.links && selectedNode.links.length" class="node-links">
          <h4>相关链接</h4>
          <div class="links-list">
            <div v-for="item in selectedNode.links" :key="item.url" class="link-item">
              <a :href="item.url" target="_blank">{{ item.title }}</a>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, onMounted, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import dayjs from 'dayjs';
import {
  Plus,
  Minus,
  FullScreen,
  Box,
  Loading
} from '@element-plus/icons-vue';

export default defineComponent({
  name: 'KnowledgeGraph',
  components: {
    Plus,
    Minus,
    FullScreen,
    Box,
    Loading
  },
  setup() {
    const loading = ref(false);
    const graphContainer = ref();
    const showNodeDetail = ref(false);
    const selectedNode = ref(null);

    // 控制参数
    const selectedDataSource = ref('recent-reports');
    const dateRange = ref([dayjs().subtract(30, 'day'), dayjs()]);
    const selectedRelationTypes = ref(['causal', 'temporal', 'semantic']);
    const selectedNodeTypes = ref(['event', 'person', 'organization', 'location']);
    const layoutType = ref('force');
    const showLabels = ref(true);

    // 图谱数据
    const graphData = reactive({
      nodes: [],
      edges: []
    });

    // 模拟图谱数据
    const mockGraphData = {
      nodes: [
        {
          id: '1',
          label: '中美贸易谈判',
          type: 'event',
          description: '中美两国就贸易问题进行的高级别谈判',
          relevance: 0.95,
          connections: 8
        },
        {
          id: '2',
          label: '拜登',
          type: 'person',
          description: '美国总统',
          relevance: 0.88,
          connections: 12
        },
        {
          id: '3',
          label: '商务部',
          type: 'organization',
          description: '中华人民共和国商务部',
          relevance: 0.82,
          connections: 6
        },
        {
          id: '4',
          label: '华盛顿',
          type: 'location',
          description: '美国首都',
          relevance: 0.75,
          connections: 4
        },
        {
          id: '5',
          label: '关税政策',
          type: 'concept',
          description: '国际贸易中的关税相关政策',
          relevance: 0.90,
          connections: 10
        }
      ],
      edges: [
        { source: '1', target: '2', type: 'causal', weight: 0.8 },
        { source: '1', target: '3', type: 'temporal', weight: 0.7 },
        { source: '1', target: '4', type: 'spatial', weight: 0.6 },
        { source: '1', target: '5', type: 'semantic', weight: 0.9 },
        { source: '2', target: '4', type: 'spatial', weight: 0.8 }
      ]
    };

    // 方法
    const loadGraphData = async () => {
      loading.value = true;
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        Object.assign(graphData, mockGraphData);
        
        // 初始化图谱
        await nextTick();
        initGraph();
      } catch (error) {
        ElMessage.error('加载图谱数据失败');
      } finally {
        loading.value = false;
      }
    };

    const initGraph = () => {
      // 这里应该初始化图谱可视化库（如D3.js, vis.js等）
      // 由于这是演示，我们只显示一个占位符
      const container = document.getElementById('knowledge-graph');
      if (container) {
        container.innerHTML = `
          <div style="
            width: 100%; 
            height: 500px; 
            background: linear-gradient(45deg, #f0f2f5, #e6f7ff);
            display: flex; 
            align-items: center; 
            justify-content: center;
            border-radius: 8px;
            border: 2px dashed #d9d9d9;
          ">
            <div style="text-align: center; color: #666;">
              <div style="font-size: 48px; margin-bottom: 16px;">🕸️</div>
              <div style="font-size: 16px;">知识图谱可视化区域</div>
              <div style="font-size: 14px; margin-top: 8px;">
                节点数: ${graphData.nodes.length} | 边数: ${graphData.edges.length}
              </div>
            </div>
          </div>
        `;
      }
    };

    const filterGraph = () => {
      ElMessage.info('图谱筛选功能开发中');
    };

    const resetGraph = () => {
      ElMessage.info('重置图谱视图');
    };

    const exportGraph = () => {
      ElMessage.info('导出图谱功能开发中');
    };

    const saveLayout = () => {
      ElMessage.success('布局已保存');
    };

    const zoomIn = () => {
      ElMessage.info('放大图谱');
    };

    const zoomOut = () => {
      ElMessage.info('缩小图谱');
    };

    const fitView = () => {
      ElMessage.info('适应视图');
    };

    const changeLayout = () => {
      ElMessage.info(`切换到${layoutType.value}布局`);
    };

    const toggleLabels = () => {
      ElMessage.info(showLabels.value ? '显示标签' : '隐藏标签');
    };

    const getNodeColor = (type) => {
      const colors = {
        event: 'blue',
        person: 'green',
        organization: 'orange',
        location: 'purple',
        concept: 'red'
      };
      return colors[type] || 'default';
    };

    const getNodeTypeText = (type) => {
      const texts = {
        event: '事件',
        person: '人物',
        organization: '机构',
        location: '地点',
        concept: '概念'
      };
      return texts[type] || '未知';
    };

    onMounted(() => {
      loadGraphData();
    });

    return {
      loading,
      graphContainer,
      showNodeDetail,
      selectedNode,
      selectedDataSource,
      dateRange,
      selectedRelationTypes,
      selectedNodeTypes,
      layoutType,
      showLabels,
      graphData,
      loadGraphData,
      filterGraph,
      resetGraph,
      exportGraph,
      saveLayout,
      zoomIn,
      zoomOut,
      fitView,
      changeLayout,
      toggleLabels,
      getNodeColor,
      getNodeTypeText
    };
  }
});
</script>

<style scoped>
.knowledge-graph {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 32px;
  margin-bottom: 24px;
  color: white;
  text-align: center;
}

.page-header h1 {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 8px;
  color: white;
}

.page-header p {
  font-size: 1.1rem;
  opacity: 0.9;
  margin: 0;
}

.control-section {
  margin-bottom: 24px;
}

.control-section h4 {
  margin-bottom: 8px;
  color: #333;
  font-weight: 600;
}

.legend {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.legend-node {
  width: 16px;
  height: 16px;
  border-radius: 50%;
}

.legend-node.event { background: #1890ff; }
.legend-node.person { background: #52c41a; }
.legend-node.organization { background: #fa8c16; }
.legend-node.location { background: #722ed1; }
.legend-node.concept { background: #f5222d; }

.graph-toolbar {
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.toolbar-group {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;
}

.button-group {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.graph-container {
  position: relative;
  min-height: 500px;
}

.graph-loading,
.graph-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 500px;
  color: #666;
}

.loading-icon {
  font-size: 32px;
  color: #409eff;
  animation: rotate 2s linear infinite;
  margin-bottom: 16px;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.empty-icon {
  font-size: 48px;
  color: #c0c4cc;
  margin-bottom: 16px;
}

.links-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 16px;
}

.link-item {
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 4px;
  border-left: 3px solid #409eff;
}

.link-item a {
  color: #409eff;
  text-decoration: none;
}

.link-item a:hover {
  text-decoration: underline;
}

.graph-loading p,
.graph-empty p {
  margin-top: 16px;
  font-size: 16px;
}

.graph-canvas {
  width: 100%;
  height: 500px;
}

.node-detail {
  max-height: 60vh;
  overflow-y: auto;
}

.node-links {
  margin-top: 16px;
}

.node-links h4 {
  margin-bottom: 8px;
  color: #333;
}

@media (max-width: 768px) {
  .knowledge-graph {
    padding: 16px;
  }
  
  .page-header {
    padding: 24px 16px;
  }
  
  .graph-toolbar {
    overflow-x: auto;
  }
}
</style>
