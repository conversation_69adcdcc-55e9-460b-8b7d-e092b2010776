<template>
  <div class="knowledge-graph">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1>知识图谱</h1>
      <p>可视化展示热点事件的复杂关联关系</p>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <a-row :gutter="[24, 24]">
        <!-- 左侧：控制面板 -->
        <a-col :xs="24" :lg="6">
          <a-card title="图谱控制" :bordered="false">
            <!-- 数据源选择 -->
            <div class="control-section">
              <h4>数据源</h4>
              <a-select
                v-model:value="selectedDataSource"
                placeholder="选择数据源"
                style="width: 100%"
                @change="loadGraphData"
              >
                <a-select-option value="recent-reports">最近简报</a-select-option>
                <a-select-option value="hot-events">热点事件</a-select-option>
                <a-select-option value="custom">自定义查询</a-select-option>
              </a-select>
            </div>

            <!-- 时间范围 -->
            <div class="control-section">
              <h4>时间范围</h4>
              <a-range-picker
                v-model:value="dateRange"
                style="width: 100%"
                @change="loadGraphData"
              />
            </div>

            <!-- 关系类型 -->
            <div class="control-section">
              <h4>关系类型</h4>
              <a-checkbox-group
                v-model:value="selectedRelationTypes"
                @change="filterGraph"
              >
                <a-checkbox value="causal">因果关系</a-checkbox>
                <a-checkbox value="temporal">时间关系</a-checkbox>
                <a-checkbox value="spatial">空间关系</a-checkbox>
                <a-checkbox value="semantic">语义关系</a-checkbox>
              </a-checkbox-group>
            </div>

            <!-- 节点类型 -->
            <div class="control-section">
              <h4>节点类型</h4>
              <a-checkbox-group
                v-model:value="selectedNodeTypes"
                @change="filterGraph"
              >
                <a-checkbox value="event">事件</a-checkbox>
                <a-checkbox value="person">人物</a-checkbox>
                <a-checkbox value="organization">机构</a-checkbox>
                <a-checkbox value="location">地点</a-checkbox>
                <a-checkbox value="concept">概念</a-checkbox>
              </a-checkbox-group>
            </div>

            <!-- 图谱操作 -->
            <div class="control-section">
              <h4>操作</h4>
              <a-space direction="vertical" style="width: 100%">
                <a-button block @click="resetGraph">重置视图</a-button>
                <a-button block @click="exportGraph">导出图谱</a-button>
                <a-button block @click="saveLayout">保存布局</a-button>
              </a-space>
            </div>
          </a-card>

          <!-- 图例 -->
          <a-card title="图例说明" :bordered="false" style="margin-top: 16px;">
            <div class="legend">
              <div class="legend-item">
                <div class="legend-node event"></div>
                <span>事件</span>
              </div>
              <div class="legend-item">
                <div class="legend-node person"></div>
                <span>人物</span>
              </div>
              <div class="legend-item">
                <div class="legend-node organization"></div>
                <span>机构</span>
              </div>
              <div class="legend-item">
                <div class="legend-node location"></div>
                <span>地点</span>
              </div>
              <div class="legend-item">
                <div class="legend-node concept"></div>
                <span>概念</span>
              </div>
            </div>
          </a-card>
        </a-col>

        <!-- 右侧：图谱可视化 -->
        <a-col :xs="24" :lg="18">
          <a-card title="知识图谱可视化" :bordered="false">
            <!-- 工具栏 -->
            <div class="graph-toolbar">
              <a-space>
                <a-button-group>
                  <a-button @click="zoomIn">
                    <plus-outlined />
                  </a-button>
                  <a-button @click="zoomOut">
                    <minus-outlined />
                  </a-button>
                  <a-button @click="fitView">
                    <expand-outlined />
                  </a-button>
                </a-button-group>
                
                <a-select
                  v-model:value="layoutType"
                  style="width: 120px"
                  @change="changeLayout"
                >
                  <a-select-option value="force">力导向</a-select-option>
                  <a-select-option value="circular">环形</a-select-option>
                  <a-select-option value="hierarchical">层次</a-select-option>
                  <a-select-option value="grid">网格</a-select-option>
                </a-select>

                <a-switch
                  v-model:checked="showLabels"
                  checked-children="显示标签"
                  un-checked-children="隐藏标签"
                  @change="toggleLabels"
                />
              </a-space>
            </div>

            <!-- 图谱容器 -->
            <div class="graph-container" ref="graphContainer">
              <div v-if="loading" class="graph-loading">
                <a-spin size="large" />
                <p>正在加载知识图谱...</p>
              </div>
              
              <div v-else-if="!graphData.nodes.length" class="graph-empty">
                <empty-outlined />
                <p>暂无数据，请选择数据源</p>
              </div>
              
              <div v-else id="knowledge-graph" class="graph-canvas"></div>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 节点详情模态框 -->
    <a-modal
      v-model:open="showNodeDetail"
      :title="selectedNode?.label || '节点详情'"
      width="600px"
      :footer="null"
    >
      <div v-if="selectedNode" class="node-detail">
        <a-descriptions :column="1" bordered>
          <a-descriptions-item label="类型">
            <a-tag :color="getNodeColor(selectedNode.type)">
              {{ getNodeTypeText(selectedNode.type) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="标签">
            {{ selectedNode.label }}
          </a-descriptions-item>
          <a-descriptions-item label="描述">
            {{ selectedNode.description || '暂无描述' }}
          </a-descriptions-item>
          <a-descriptions-item label="相关度">
            <a-progress
              :percent="selectedNode.relevance * 100"
              :stroke-color="{ '0%': '#108ee9', '100%': '#87d068' }"
            />
          </a-descriptions-item>
          <a-descriptions-item label="连接数">
            {{ selectedNode.connections || 0 }}
          </a-descriptions-item>
        </a-descriptions>

        <!-- 相关链接 -->
        <div v-if="selectedNode.links && selectedNode.links.length" class="node-links">
          <h4>相关链接</h4>
          <a-list size="small" :data-source="selectedNode.links">
            <template #renderItem="{ item }">
              <a-list-item>
                <a :href="item.url" target="_blank">{{ item.title }}</a>
              </a-list-item>
            </template>
          </a-list>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, onMounted, nextTick } from 'vue';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';
import {
  PlusOutlined,
  MinusOutlined,
  ExpandOutlined,
  EmptyOutlined
} from '@ant-design/icons-vue';

export default defineComponent({
  name: 'KnowledgeGraph',
  components: {
    PlusOutlined,
    MinusOutlined,
    ExpandOutlined,
    EmptyOutlined
  },
  setup() {
    const loading = ref(false);
    const graphContainer = ref();
    const showNodeDetail = ref(false);
    const selectedNode = ref(null);

    // 控制参数
    const selectedDataSource = ref('recent-reports');
    const dateRange = ref([dayjs().subtract(30, 'day'), dayjs()]);
    const selectedRelationTypes = ref(['causal', 'temporal', 'semantic']);
    const selectedNodeTypes = ref(['event', 'person', 'organization', 'location']);
    const layoutType = ref('force');
    const showLabels = ref(true);

    // 图谱数据
    const graphData = reactive({
      nodes: [],
      edges: []
    });

    // 模拟图谱数据
    const mockGraphData = {
      nodes: [
        {
          id: '1',
          label: '中美贸易谈判',
          type: 'event',
          description: '中美两国就贸易问题进行的高级别谈判',
          relevance: 0.95,
          connections: 8
        },
        {
          id: '2',
          label: '拜登',
          type: 'person',
          description: '美国总统',
          relevance: 0.88,
          connections: 12
        },
        {
          id: '3',
          label: '商务部',
          type: 'organization',
          description: '中华人民共和国商务部',
          relevance: 0.82,
          connections: 6
        },
        {
          id: '4',
          label: '华盛顿',
          type: 'location',
          description: '美国首都',
          relevance: 0.75,
          connections: 4
        },
        {
          id: '5',
          label: '关税政策',
          type: 'concept',
          description: '国际贸易中的关税相关政策',
          relevance: 0.90,
          connections: 10
        }
      ],
      edges: [
        { source: '1', target: '2', type: 'causal', weight: 0.8 },
        { source: '1', target: '3', type: 'temporal', weight: 0.7 },
        { source: '1', target: '4', type: 'spatial', weight: 0.6 },
        { source: '1', target: '5', type: 'semantic', weight: 0.9 },
        { source: '2', target: '4', type: 'spatial', weight: 0.8 }
      ]
    };

    // 方法
    const loadGraphData = async () => {
      loading.value = true;
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        Object.assign(graphData, mockGraphData);
        
        // 初始化图谱
        await nextTick();
        initGraph();
      } catch (error) {
        message.error('加载图谱数据失败');
      } finally {
        loading.value = false;
      }
    };

    const initGraph = () => {
      // 这里应该初始化图谱可视化库（如D3.js, vis.js等）
      // 由于这是演示，我们只显示一个占位符
      const container = document.getElementById('knowledge-graph');
      if (container) {
        container.innerHTML = `
          <div style="
            width: 100%; 
            height: 500px; 
            background: linear-gradient(45deg, #f0f2f5, #e6f7ff);
            display: flex; 
            align-items: center; 
            justify-content: center;
            border-radius: 8px;
            border: 2px dashed #d9d9d9;
          ">
            <div style="text-align: center; color: #666;">
              <div style="font-size: 48px; margin-bottom: 16px;">🕸️</div>
              <div style="font-size: 16px;">知识图谱可视化区域</div>
              <div style="font-size: 14px; margin-top: 8px;">
                节点数: ${graphData.nodes.length} | 边数: ${graphData.edges.length}
              </div>
            </div>
          </div>
        `;
      }
    };

    const filterGraph = () => {
      message.info('图谱筛选功能开发中');
    };

    const resetGraph = () => {
      message.info('重置图谱视图');
    };

    const exportGraph = () => {
      message.info('导出图谱功能开发中');
    };

    const saveLayout = () => {
      message.success('布局已保存');
    };

    const zoomIn = () => {
      message.info('放大图谱');
    };

    const zoomOut = () => {
      message.info('缩小图谱');
    };

    const fitView = () => {
      message.info('适应视图');
    };

    const changeLayout = () => {
      message.info(`切换到${layoutType.value}布局`);
    };

    const toggleLabels = () => {
      message.info(showLabels.value ? '显示标签' : '隐藏标签');
    };

    const getNodeColor = (type) => {
      const colors = {
        event: 'blue',
        person: 'green',
        organization: 'orange',
        location: 'purple',
        concept: 'red'
      };
      return colors[type] || 'default';
    };

    const getNodeTypeText = (type) => {
      const texts = {
        event: '事件',
        person: '人物',
        organization: '机构',
        location: '地点',
        concept: '概念'
      };
      return texts[type] || '未知';
    };

    onMounted(() => {
      loadGraphData();
    });

    return {
      loading,
      graphContainer,
      showNodeDetail,
      selectedNode,
      selectedDataSource,
      dateRange,
      selectedRelationTypes,
      selectedNodeTypes,
      layoutType,
      showLabels,
      graphData,
      loadGraphData,
      filterGraph,
      resetGraph,
      exportGraph,
      saveLayout,
      zoomIn,
      zoomOut,
      fitView,
      changeLayout,
      toggleLabels,
      getNodeColor,
      getNodeTypeText
    };
  }
});
</script>

<style scoped>
.knowledge-graph {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 32px;
  margin-bottom: 24px;
  color: white;
  text-align: center;
}

.page-header h1 {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 8px;
  color: white;
}

.page-header p {
  font-size: 1.1rem;
  opacity: 0.9;
  margin: 0;
}

.control-section {
  margin-bottom: 24px;
}

.control-section h4 {
  margin-bottom: 8px;
  color: #333;
  font-weight: 600;
}

.legend {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.legend-node {
  width: 16px;
  height: 16px;
  border-radius: 50%;
}

.legend-node.event { background: #1890ff; }
.legend-node.person { background: #52c41a; }
.legend-node.organization { background: #fa8c16; }
.legend-node.location { background: #722ed1; }
.legend-node.concept { background: #f5222d; }

.graph-toolbar {
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.graph-container {
  position: relative;
  min-height: 500px;
}

.graph-loading,
.graph-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 500px;
  color: #666;
}

.graph-loading p,
.graph-empty p {
  margin-top: 16px;
  font-size: 16px;
}

.graph-canvas {
  width: 100%;
  height: 500px;
}

.node-detail {
  max-height: 60vh;
  overflow-y: auto;
}

.node-links {
  margin-top: 16px;
}

.node-links h4 {
  margin-bottom: 8px;
  color: #333;
}

@media (max-width: 768px) {
  .knowledge-graph {
    padding: 16px;
  }
  
  .page-header {
    padding: 24px 16px;
  }
  
  .graph-toolbar {
    overflow-x: auto;
  }
}
</style>
