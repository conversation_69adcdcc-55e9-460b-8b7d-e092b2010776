#!/usr/bin/env python
"""
数据库连接清理工具
定期检查和清理长期使用的数据库连接，防止连接泄漏
"""
import os
import sys
import time
import threading
import logging
import argparse
from datetime import datetime
import traceback

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger("db_cleaner")

# 确保脚本可以导入backend模块
sys.path.insert(0, '.')
sys.path.insert(0, '..')

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)
sys.path.insert(0, os.path.join(current_dir, '..'))

# 导入数据库工具
try:
    from utils.db import (
        engine, connection_lock, connection_usage_counter, 
        db_pool_stats, get_db_pool_stats, refresh_pool_settings
    )
    logger.info("成功导入数据库模块")
except ImportError as e:
    logger.error(f"导入数据库模块失败: {e}")
    sys.exit(1)

def log_pool_stats():
    """记录连接池状态"""
    try:
        stats = get_db_pool_stats()
        logger.info("=== 连接池统计 ===")
        logger.info(f"连接池大小: {stats.get('pool_size', 'unknown')}")
        logger.info(f"最大溢出: {stats.get('max_overflow', 'unknown')}")
        logger.info(f"当前大小: {stats.get('current_size', 'unknown')}")
        logger.info(f"已检出: {stats.get('checked_out', 'unknown')}")
        logger.info(f"已检入: {stats.get('checked_in', 'unknown')}")
        logger.info(f"溢出: {stats.get('overflow', 'unknown')}")
        
        with connection_lock:
            logger.info(f"活跃连接: {db_pool_stats.get('active_connections', 'unknown')}")
            logger.info(f"空闲连接: {db_pool_stats.get('idle_connections', 'unknown')}")
            logger.info(f"连接检出总数: {db_pool_stats.get('checkout_count', 'unknown')}")
            logger.info(f"连接归还总数: {db_pool_stats.get('checkin_count', 'unknown')}")
            
            # 高使用量连接统计
            high_usage_count = sum(1 for usage in connection_usage_counter.values() if usage > 30)
            logger.info(f"高使用量连接数 (>30次): {high_usage_count}")
            
            # 列出所有高使用量连接
            if high_usage_count > 0:
                logger.info("--- 高使用量连接详情 ---")
                for conn_id, usage in sorted(
                    connection_usage_counter.items(), 
                    key=lambda x: x[1], 
                    reverse=True
                )[:10]:  # 只显示前10个
                    logger.info(f"连接ID: {conn_id}, 使用次数: {usage}")
    except Exception as e:
        logger.error(f"记录连接池状态时出错: {e}")

def clean_connections(force=False, threshold=30):
    """清理过度使用的连接"""
    try:
        pool = engine.pool
        cleaned_count = 0
        
        with connection_lock:
            # 查找使用次数超过阈值的连接
            high_usage_connections = {
                conn_id: usage for conn_id, usage in connection_usage_counter.items() 
                if usage > threshold
            }
            
            if high_usage_connections:
                logger.warning(f"发现 {len(high_usage_connections)} 个高使用量连接，准备清理")
                
                # 将连接标记为过期
                for conn_id, usage in high_usage_connections.items():
                    logger.warning(f"清理连接 {conn_id}，使用次数: {usage}")
                    # 从计数器中移除
                    connection_usage_counter.pop(conn_id, None)
                    cleaned_count += 1
                
                # 如果强制刷新或超过一定数量，则重置整个连接池
                if force or len(high_usage_connections) > 5:
                    try:
                        logger.warning("尝试完全重置连接池...")
                        pool.dispose()
                        logger.info("连接池已重置")
                    except Exception as e:
                        logger.error(f"重置连接池时出错: {e}")
        
        return cleaned_count
    except Exception as e:
        logger.error(f"清理连接时出错: {e}")
        return 0

def monitor_and_clean(interval=60, threshold=30, force_interval=300):
    """监控并定期清理连接"""
    try:
        last_force_clean = time.time()
        
        while True:
            # 记录连接池状态
            log_pool_stats()
            
            # 检查是否需要强制清理
            force_clean = (time.time() - last_force_clean) >= force_interval
            
            if force_clean:
                logger.info("执行定期强制清理...")
                last_force_clean = time.time()
            
            # 清理连接
            cleaned = clean_connections(force=force_clean, threshold=threshold)
            
            if cleaned > 0:
                logger.info(f"已清理 {cleaned} 个连接")
            else:
                logger.info("没有需要清理的连接")
            
            # 刷新连接池设置
            try:
                refresh_pool_settings()
                logger.info("已刷新连接池设置")
            except Exception as e:
                logger.error(f"刷新连接池设置时出错: {e}")
            
            # 休眠
            logger.info(f"休眠 {interval} 秒...")
            time.sleep(interval)
    except KeyboardInterrupt:
        logger.info("收到中断信号，程序退出")
    except Exception as e:
        logger.exception(f"监控过程中发生错误: {e}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="数据库连接清理工具")
    parser.add_argument(
        "--interval", 
        type=int, 
        default=60, 
        help="监控间隔 (秒)"
    )
    parser.add_argument(
        "--threshold", 
        type=int, 
        default=30, 
        help="连接使用次数阈值，超过则清理"
    )
    parser.add_argument(
        "--force-interval", 
        type=int, 
        default=300, 
        help="强制清理间隔 (秒)"
    )
    parser.add_argument(
        "--once", 
        action="store_true", 
        help="只运行一次清理后退出"
    )
    
    args = parser.parse_args()
    
    logger.info("=== 数据库连接清理工具 ===")
    logger.info(f"监控间隔: {args.interval} 秒")
    logger.info(f"清理阈值: {args.threshold} 次使用")
    logger.info(f"强制清理间隔: {args.force_interval} 秒")
    
    if args.once:
        # 只运行一次
        logger.info("执行单次清理...")
        log_pool_stats()
        cleaned = clean_connections(force=True, threshold=args.threshold)
        logger.info(f"已清理 {cleaned} 个连接")
    else:
        # 持续监控模式
        logger.info("启动持续监控模式...")
        monitor_and_clean(
            interval=args.interval,
            threshold=args.threshold,
            force_interval=args.force_interval
        )
    
    logger.info("程序已退出")

if __name__ == "__main__":
    main() 