-- Create enum types
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'visibility_type') THEN
        CREATE TYPE visibility_type AS ENUM ('team', 'private');
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'file_status') THEN
        CREATE TYPE file_status AS ENUM ('pending', 'processing', 'completed', 'failed');
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'term_status') THEN
        CREATE TYPE term_status AS ENUM ('draft', 'approved', 'rejected');
    END IF;
END
$$;

-- Create mono corpus table
CREATE TABLE IF NOT EXISTS terminology_mono_corpus (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    language VARCHAR(10) NOT NULL,
    description TEXT,
    visibility visibility_type NOT NULL DEFAULT 'team',
    user_id INTEGER REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create mono corpus files table
CREATE TABLE IF NOT EXISTS terminology_mono_corpus_files (
    id SERIAL PRIMARY KEY,
    corpus_id INTEGER REFERENCES terminology_mono_corpus(id) ON DELETE CASCADE,
    filename VARCHAR(255) NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    file_size INTEGER NOT NULL,
    file_type VARCHAR(50) NOT NULL,
    status file_status DEFAULT 'pending',
    processed_entries INTEGER DEFAULT 0,
    total_entries INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create terms table
CREATE TABLE IF NOT EXISTS terminology_terms (
    id SERIAL PRIMARY KEY,
    term_text VARCHAR(255) NOT NULL,
    language VARCHAR(10) NOT NULL,
    domain VARCHAR(100),
    definition TEXT,
    status term_status DEFAULT 'draft',
    user_id INTEGER REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create mono corpus entries table
CREATE TABLE IF NOT EXISTS terminology_mono_corpus_entries (
    id SERIAL PRIMARY KEY,
    corpus_id INTEGER REFERENCES terminology_mono_corpus(id) ON DELETE CASCADE,
    file_id INTEGER REFERENCES terminology_mono_corpus_files(id) ON DELETE CASCADE,
    term_id INTEGER REFERENCES terminology_terms(id),
    text TEXT NOT NULL,
    context_before TEXT,
    context_after TEXT,
    line_number INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create terminology data table
CREATE TABLE IF NOT EXISTS terminology_data (
    id SERIAL PRIMARY KEY,
    term_id VARCHAR(36) UNIQUE,
    user_id INTEGER REFERENCES users(id),
    term VARCHAR(255) NOT NULL,
    definition TEXT,
    language VARCHAR(10) NOT NULL,
    domain VARCHAR(100),
    translations TEXT,
    synonyms TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_terminology_mono_corpus_language ON terminology_mono_corpus(language);
CREATE INDEX IF NOT EXISTS idx_terminology_mono_corpus_user ON terminology_mono_corpus(user_id);
CREATE INDEX IF NOT EXISTS idx_terminology_mono_corpus_files_corpus ON terminology_mono_corpus_files(corpus_id);
CREATE INDEX IF NOT EXISTS idx_terminology_mono_corpus_files_status ON terminology_mono_corpus_files(status);
CREATE INDEX IF NOT EXISTS idx_terminology_terms_language ON terminology_terms(language);
CREATE INDEX IF NOT EXISTS idx_terminology_terms_domain ON terminology_terms(domain);
CREATE INDEX IF NOT EXISTS idx_terminology_terms_status ON terminology_terms(status);
CREATE INDEX IF NOT EXISTS idx_terminology_mono_corpus_entries_corpus ON terminology_mono_corpus_entries(corpus_id);
CREATE INDEX IF NOT EXISTS idx_terminology_mono_corpus_entries_file ON terminology_mono_corpus_entries(file_id);
CREATE INDEX IF NOT EXISTS idx_terminology_data_term ON terminology_data(term);
CREATE INDEX IF NOT EXISTS idx_terminology_data_language ON terminology_data(language);
CREATE INDEX IF NOT EXISTS idx_terminology_data_domain ON terminology_data(domain); 