<template>
  <div class="websocket-test">
    <el-card>
      <template #header>
        <span>WebSocket连接测试</span>
      </template>
      
      <div class="connection-status">
        <el-tag :type="connectionStatus === 'OPEN' ? 'success' : 'danger'">
          连接状态: {{ connectionStatus }}
        </el-tag>
        <el-button 
          v-if="connectionStatus !== 'OPEN'" 
          @click="connect" 
          type="primary" 
          size="small"
        >
          连接
        </el-button>
        <el-button 
          v-else 
          @click="disconnect" 
          type="danger" 
          size="small"
        >
          断开
        </el-button>
      </div>

      <div class="test-actions">
        <el-space wrap>
          <el-button @click="sendPing">发送心跳</el-button>
          <el-button @click="subscribeAlerts">订阅预警</el-button>
          <el-button @click="getStats">获取统计</el-button>
          <el-button @click="testAlert">测试预警</el-button>
        </el-space>
      </div>

      <div class="message-log">
        <h4>消息日志:</h4>
        <div class="log-container">
          <div 
            v-for="(msg, index) in messages" 
            :key="index" 
            class="log-item"
            :class="msg.type"
          >
            <span class="timestamp">{{ msg.timestamp }}</span>
            <span class="type">{{ msg.type }}</span>
            <span class="content">{{ msg.content }}</span>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { defineComponent, ref, onMounted, onUnmounted } from 'vue';
import websocketService from '@/services/websocket.js';

export default defineComponent({
  name: 'WebSocketTest',
  setup() {
    const connectionStatus = ref('CLOSED');
    const messages = ref([]);

    const addMessage = (type, content) => {
      messages.value.unshift({
        type,
        content: typeof content === 'object' ? JSON.stringify(content, null, 2) : content,
        timestamp: new Date().toLocaleTimeString()
      });
      
      // 限制消息数量
      if (messages.value.length > 50) {
        messages.value = messages.value.slice(0, 50);
      }
    };

    const connect = () => {
      const userId = 'test_user_' + Date.now();
      websocketService.connect(userId);
      addMessage('INFO', `尝试连接 WebSocket (用户ID: ${userId})`);
    };

    const disconnect = () => {
      websocketService.disconnect();
      addMessage('INFO', '断开 WebSocket 连接');
    };

    const sendPing = () => {
      websocketService.send({ type: 'ping' });
      addMessage('SEND', '发送心跳包');
    };

    const subscribeAlerts = () => {
      websocketService.subscribe('alerts_general');
      addMessage('SEND', '订阅预警频道');
    };

    const getStats = () => {
      websocketService.getStats();
      addMessage('SEND', '请求连接统计');
    };

    const testAlert = async () => {
      try {
        const response = await fetch('http://localhost:8000/api/v1/ws/alert', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            type: 'test',
            title: '测试预警',
            message: '这是一个测试预警消息',
            level: 'medium',
            user_id: null // 广播给所有用户
          })
        });
        
        const result = await response.json();
        addMessage('INFO', `测试预警发送: ${result.message}`);
      } catch (error) {
        addMessage('ERROR', `发送测试预警失败: ${error.message}`);
      }
    };

    // WebSocket事件监听
    const handleOpen = () => {
      connectionStatus.value = 'OPEN';
      addMessage('SUCCESS', 'WebSocket 连接已建立');
    };

    const handleClose = () => {
      connectionStatus.value = 'CLOSED';
      addMessage('WARNING', 'WebSocket 连接已关闭');
    };

    const handleError = (error) => {
      addMessage('ERROR', `WebSocket 错误: ${error}`);
    };

    const handleMessage = (message) => {
      addMessage('RECEIVE', message);
    };

    const handleAlert = (data) => {
      addMessage('ALERT', `收到预警: ${data.title || '未知预警'}`);
    };

    const updateConnectionStatus = () => {
      connectionStatus.value = websocketService.getConnectionState();
    };

    onMounted(() => {
      // 监听 WebSocket 事件
      websocketService.on('open', handleOpen);
      websocketService.on('close', handleClose);
      websocketService.on('error', handleError);
      websocketService.on('message', handleMessage);
      websocketService.on('alert', handleAlert);

      // 定期更新连接状态
      const statusInterval = setInterval(updateConnectionStatus, 1000);
      
      // 清理函数
      onUnmounted(() => {
        clearInterval(statusInterval);
        websocketService.off('open', handleOpen);
        websocketService.off('close', handleClose);
        websocketService.off('error', handleError);
        websocketService.off('message', handleMessage);
        websocketService.off('alert', handleAlert);
      });
    });

    return {
      connectionStatus,
      messages,
      connect,
      disconnect,
      sendPing,
      subscribeAlerts,
      getStats,
      testAlert
    };
  }
});
</script>

<style scoped>
.websocket-test {
  max-width: 800px;
  margin: 20px auto;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
}

.test-actions {
  margin-bottom: 20px;
}

.message-log h4 {
  margin-bottom: 12px;
}

.log-container {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 12px;
  background: #f8f9fa;
}

.log-item {
  display: flex;
  gap: 12px;
  padding: 4px 0;
  border-bottom: 1px solid #eee;
  font-family: monospace;
  font-size: 12px;
}

.log-item:last-child {
  border-bottom: none;
}

.timestamp {
  color: #909399;
  min-width: 80px;
}

.type {
  min-width: 80px;
  font-weight: bold;
}

.log-item.SUCCESS .type {
  color: #67c23a;
}

.log-item.ERROR .type {
  color: #f56c6c;
}

.log-item.WARNING .type {
  color: #e6a23c;
}

.log-item.ALERT .type {
  color: #f56c6c;
  background: #fef0f0;
}

.log-item.SEND .type {
  color: #409eff;
}

.log-item.RECEIVE .type {
  color: #909399;
}

.content {
  flex: 1;
  word-break: break-all;
  white-space: pre-wrap;
}
</style>
