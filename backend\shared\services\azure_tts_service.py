import os
import logging
import tempfile
import uuid
from typing import Dict, List, Optional
import azure.cognitiveservices.speech as speechsdk
from pydantic import BaseModel

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AzureTTSConfig(BaseModel):
    """Azure TTS 配置"""
    subscription_key: str
    region: str
    default_voice: str = "zh-CN-XiaoxiaoNeural"
    default_speed: float = 1.0
    default_pitch: float = 1.0

class AzureTTSService:
    """Azure 文本到语音服务"""
    
    def __init__(self, config: AzureTTSConfig):
        self.config = config
        self.speech_config = speechsdk.SpeechConfig(
            subscription=config.subscription_key,
            region=config.region
        )
        
        # 设置默认语音
        self.speech_config.speech_synthesis_voice_name = config.default_voice
        
        # 可用语音列表
        self.available_voices = {
            "zh-CN-XiaoxiaoNeural": {"name": "晓晓", "gender": "female", "language": "zh-CN"},
            "zh-CN-YunxiNeural": {"name": "云希", "gender": "male", "language": "zh-CN"},
            "zh-CN-YunyangNeural": {"name": "云扬", "gender": "male", "language": "zh-CN"},
            "en-US-JennyNeural": {"name": "Jenny", "gender": "female", "language": "en-US"},
            "en-US-GuyNeural": {"name": "Guy", "gender": "male", "language": "en-US"},
            "ja-JP-NanamiNeural": {"name": "七海", "gender": "female", "language": "ja-JP"}
        }
        
        logger.info("Azure TTS服务已初始化")
    
    def get_available_voices(self) -> Dict:
        """获取可用的语音列表"""
        return self.available_voices
    
    def text_to_speech(self, 
                      text: str, 
                      voice_id: str = None, 
                      speed: float = None,
                      pitch: float = None,
                      output_path: str = None) -> str:
        """将文本转换为语音"""
        try:
            # 设置语音
            voice_id = voice_id or self.config.default_voice
            if voice_id in self.available_voices:
                self.speech_config.speech_synthesis_voice_name = voice_id
            
            # 设置语速和音调
            ssml = f"""
                <speak version='1.0' xmlns='http://www.w3.org/2001/10/synthesis' xml:lang='{self.available_voices[voice_id]["language"]}'>
                    <prosody rate='{speed or self.config.default_speed}' pitch='{pitch or self.config.default_pitch}'>
                        {text}
                    </prosody>
                </speak>
            """
            
            # 生成临时文件名
            if output_path is None:
                temp_dir = tempfile.gettempdir()
                output_path = os.path.join(temp_dir, f"tts_{uuid.uuid4()}.wav")
            
            # 配置音频输出
            audio_config = speechsdk.audio.AudioOutputConfig(filename=output_path)
            
            # 创建语音合成器
            speech_synthesizer = speechsdk.SpeechSynthesizer(
                speech_config=self.speech_config,
                audio_config=audio_config
            )
            
            # 合成语音
            result = speech_synthesizer.speak_ssml_async(ssml).get()
            
            if result.reason == speechsdk.ResultReason.SynthesizingAudioCompleted:
                logger.info(f"语音已生成: {output_path}")
                return output_path
            else:
                logger.error(f"语音合成失败: {result.reason}")
                return ""
                
        except Exception as e:
            logger.error(f"语音合成失败: {e}")
            return ""
    
    def generate_speech(self, text: str, output_path: str = None, voice_id: str = None, speed: float = None) -> str:
        """
        将文本转换为语音（text_to_speech的别名）
        
        Args:
            text: 要转换的文本
            output_path: 输出文件路径，如果为None则生成临时文件
            voice_id: 语音ID
            speed: 语速，范围为0.5到2.0
            
        Returns:
            output_path: 生成的音频文件路径
        """
        logger.info(f"调用generate_speech方法，文本: '{text[:30]}...'")
        return self.text_to_speech(text, voice_id, speed, None, output_path)
    
    def generate_phonemes(self, text: str, lang: str = "zh-CN") -> List[Dict]:
        """生成文本的音素序列"""
        try:
            # 使用Azure的语音服务获取音素
            speech_config = speechsdk.SpeechConfig(
                subscription=self.config.subscription_key,
                region=self.config.region
            )
            
            # 设置语言
            speech_config.speech_recognition_language = lang
            
            # 创建语音识别器
            speech_recognizer = speechsdk.SpeechRecognizer(speech_config=speech_config)
            
            # 获取音素
            result = speech_recognizer.recognize_once_async().get()
            
            if result.reason == speechsdk.ResultReason.RecognizedSpeech:
                # 解析音素（简化实现）
                phonemes = []
                for word in result.text.split():
                    for char in word:
                        phonemes.append({
                            "phoneme": char,
                            "duration": 0.15  # 默认持续时间
                        })
                return phonemes
            else:
                logger.error(f"音素生成失败: {result.reason}")
                return []
                
        except Exception as e:
            logger.error(f"音素生成失败: {e}")
            return []

def get_azure_tts_service():
    """获取Azure TTS服务实例"""
    config = AzureTTSConfig(
        subscription_key=os.getenv("AZURE_SPEECH_KEY"),
        region=os.getenv("AZURE_SPEECH_REGION", "eastasia"),
        default_voice=os.getenv("AZURE_DEFAULT_VOICE", "zh-CN-XiaoxiaoNeural")
    )
    return AzureTTSService(config) 