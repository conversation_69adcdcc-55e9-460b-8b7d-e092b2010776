"""
Chat Service - 处理与Ollama交互的对话服务
支持对话内容管理和从对话中提取PPT内容
"""

import os
import json
import logging
import re
import time
from typing import List, Dict, Any, Optional, Union

# 引入Ollama交互工具
from .ai_service import AIService

class ChatService:
    """处理AI对话交互并管理对话状态"""
    
    def __init__(self):
        """初始化聊天服务"""
        self.ai_service = AIService()
        self.conversations = {}  # 存储对话历史
        self.logger = logging.getLogger(__name__)
        
        # PPT生成的提示模板
        self.ppt_extraction_prompt = """
        你是一个专业的PPT内容提取器。请根据我们之前的对话内容，提取出用于生成PPT的关键信息，并以JSON格式返回。
        
        请分析对话内容以确定:
        1. PPT的标题
        2. 主要内容章节
        3. 每个章节的关键点
        4. 适合的PPT模板风格
        
        返回的JSON结构应该是:
        {
            "title": "PPT标题",
            "subtitle": "副标题（如果有）",
            "author": "作者（如果有）",
            "template": "适合的模板风格（business/creative/academic/minimalist等）",
            "slides": [
                {
                    "type": "title",
                    "title": "演示文稿标题",
                    "subtitle": "副标题（如果有）"
                },
                {
                    "type": "section",
                    "title": "章节标题",
                    "bullets": ["要点1", "要点2", "要点3"]
                }
                // 可以包含更多幻灯片
            ]
        }
        
        请确保提取的内容完整且准确反映了对话中讨论的PPT需求。如果对话中没有明确提到某些内容，可以根据上下文进行合理推断。
        
        你的回复应该只包含JSON格式，不要添加任何其他解释文字。
        """

    def create_conversation(self, conversation_id: Optional[str] = None) -> str:
        """
        创建新的对话
        
        Args:
            conversation_id: 可选的对话ID，如果未提供则自动生成
            
        Returns:
            创建的对话ID
        """
        if conversation_id is None:
            conversation_id = f"conv_{int(time.time())}"
            
        if conversation_id not in self.conversations:
            self.conversations[conversation_id] = {
                "messages": [],
                "metadata": {
                    "created_at": time.time(),
                    "updated_at": time.time(),
                    "title": "新PPT对话",
                    "status": "active"
                }
            }
            
        return conversation_id

    def add_message(self, conversation_id: str, role: str, content: str) -> Dict[str, Any]:
        """
        添加消息到对话历史
        
        Args:
            conversation_id: 对话ID
            role: 发言者角色 ('user' 或 'assistant')
            content: 消息内容
            
        Returns:
            添加的消息对象
        """
        if conversation_id not in self.conversations:
            self.create_conversation(conversation_id)
            
        message = {
            "role": role,
            "content": content,
            "timestamp": time.time()
        }
        
        self.conversations[conversation_id]["messages"].append(message)
        self.conversations[conversation_id]["metadata"]["updated_at"] = time.time()
        
        # 尝试从首条用户消息中提取标题
        if role == "user" and len(self.conversations[conversation_id]["messages"]) == 1:
            title_match = re.search(r"(?:关于|主题|题目|PPT|演示)[\s:]*([^\n.。,，?？!！]+)", content)
            if title_match:
                extracted_title = title_match.group(1).strip()
                if len(extracted_title) > 3 and len(extracted_title) < 50:
                    self.conversations[conversation_id]["metadata"]["title"] = extracted_title
        
        return message

    def get_messages(self, conversation_id: str) -> List[Dict[str, Any]]:
        """
        获取对话历史消息
        
        Args:
            conversation_id: 对话ID
            
        Returns:
            消息列表
        """
        if conversation_id not in self.conversations:
            return []
            
        return self.conversations[conversation_id]["messages"]

    def get_conversation(self, conversation_id: str) -> Dict[str, Any]:
        """
        获取完整对话信息
        
        Args:
            conversation_id: 对话ID
            
        Returns:
            对话信息，包括消息和元数据
        """
        if conversation_id not in self.conversations:
            return {"messages": [], "metadata": {}}
            
        return self.conversations[conversation_id]

    def send_message(self, message: str, conversation_id: str, 
                    history: Optional[List[Dict[str, str]]] = None) -> Dict[str, Any]:
        """
        发送消息到AI并获取回复
        
        Args:
            message: 用户消息
            conversation_id: 对话ID
            history: 可选的历史消息，如果提供则使用，否则使用存储的对话历史
            
        Returns:
            AI助手的回复
        """
        if conversation_id not in self.conversations:
            self.create_conversation(conversation_id)
            
        # 添加用户消息到历史
        self.add_message(conversation_id, "user", message)
        
        # 准备对话历史
        if history is None:
            history = [
                {"role": msg["role"], "content": msg["content"]} 
                for msg in self.conversations[conversation_id]["messages"]
            ]
            
        # 添加系统提示
        system_prompt = """
        你是一个专业的PPT设计助手，能够引导用户创建高质量的演示文稿。
        请遵循以下原则：
        1. 提供友好、专业的指导
        2. 提取用户需求中的关键信息
        3. 提供结构化的建议
        4. 询问缺失的关键信息
        5. 关注PPT的内容、结构、设计和受众
        
        你的目标是通过对话帮助用户明确PPT需求，提供有用的建议，并准备好生成一份完整的演示文稿。
        """
        
        try:
            # 调用AI服务获取回复
            response = self.ai_service.chat_completion(
                messages=[{"role": "system", "content": system_prompt}] + history,
                max_tokens=1000
            )
            
            # 解析回复
            assistant_message = response.get("message", {}).get("content", "")
            
            # 添加助手回复到历史
            self.add_message(conversation_id, "assistant", assistant_message)
            
            return {
                "response": assistant_message,
                "conversation_id": conversation_id
            }
        except Exception as e:
            self.logger.error(f"发送消息失败: {str(e)}")
            
            # 如果AI服务出错，提供一个友好的默认回复
            default_response = "抱歉，我暂时无法处理您的请求。请稍后再试。"
            self.add_message(conversation_id, "assistant", default_response)
            
            return {
                "response": default_response,
                "conversation_id": conversation_id,
                "error": str(e)
            }

    def extract_ppt_content(self, conversation_id: str, 
                          messages: Optional[List[Dict[str, str]]] = None) -> Dict[str, Any]:
        """
        从对话中提取PPT内容
        
        Args:
            conversation_id: 对话ID
            messages: 可选的消息历史，如果提供则使用，否则使用存储的对话历史
            
        Returns:
            提取的PPT内容结构
        """
        if conversation_id not in self.conversations and messages is None:
            raise ValueError("对话不存在且未提供消息历史")
            
        # 准备对话历史
        if messages is None:
            messages = [
                {"role": msg["role"], "content": msg["content"]} 
                for msg in self.conversations[conversation_id]["messages"]
            ]
            
        try:
            # 构建提取PPT内容的提示
            extraction_messages = [
                {"role": "system", "content": self.ppt_extraction_prompt}
            ]
            
            # 添加对话历史
            extraction_messages.extend(messages)
            
            # 添加最后的提取指令
            extraction_messages.append({
                "role": "user", 
                "content": "根据上述对话，提取PPT内容并以JSON格式返回。"
            })
            
            # 调用AI服务提取内容
            response = self.ai_service.chat_completion(
                messages=extraction_messages,
                max_tokens=2000
            )
            
            # 解析回复
            json_response = response.get("message", {}).get("content", "")
            
            # 尝试从回复中提取JSON
            try:
                # 查找JSON内容
                json_match = re.search(r'```json\s*(.+?)\s*```|({.+})', json_response, re.DOTALL)
                if json_match:
                    json_str = json_match.group(1) or json_match.group(2)
                    ppt_content = json.loads(json_str)
                else:
                    # 尝试直接解析整个回复
                    ppt_content = json.loads(json_response)
                    
                # 确保结果包含必要的字段
                if "title" not in ppt_content:
                    ppt_content["title"] = self.conversations[conversation_id]["metadata"].get(
                        "title", "演示文稿"
                    )
                    
                if "slides" not in ppt_content:
                    ppt_content["slides"] = []
                
                # 如果没有幻灯片，添加一个默认标题幻灯片
                if len(ppt_content["slides"]) == 0:
                    ppt_content["slides"].append({
                        "type": "title",
                        "title": ppt_content["title"],
                        "subtitle": ppt_content.get("subtitle", "")
                    })
                
                return ppt_content
            except json.JSONDecodeError:
                # 如果JSON解析失败，手动构建一个基本结构
                self.logger.warning(f"无法解析JSON响应: {json_response}")
                return self._create_fallback_ppt_content(conversation_id, messages)
                
        except Exception as e:
            self.logger.error(f"提取PPT内容失败: {str(e)}")
            return self._create_fallback_ppt_content(conversation_id, messages)

    def _create_fallback_ppt_content(self, conversation_id: str, 
                                   messages: List[Dict[str, str]]) -> Dict[str, Any]:
        """
        创建备用PPT内容结构，当提取失败时使用
        
        Args:
            conversation_id: 对话ID
            messages: 消息历史
            
        Returns:
            基本的PPT内容结构
        """
        # 尝试提取标题
        title = "演示文稿"
        
        if conversation_id in self.conversations:
            meta_title = self.conversations[conversation_id]["metadata"].get("title")
            if meta_title and meta_title != "新PPT对话":
                title = meta_title
        
        # 从用户消息中提取可能的主题和关键点
        topics = []
        user_messages = [msg["content"] for msg in messages if msg["role"] == "user"]
        
        for msg in user_messages:
            # 尝试从消息中提取主题
            title_match = re.search(r"(?:关于|主题|题目|PPT|演示)[\s:]*([^\n.。,，?？!！]+)", msg)
            if title_match and len(title_match.group(1).strip()) > 3:
                title = title_match.group(1).strip()
                
            # 尝试提取列表项
            list_items = re.findall(r"[*\-•]\s*([^\n]+)", msg)
            topics.extend(list_items)
            
            # 提取数字列表
            numbered_items = re.findall(r"\d+\.\s*([^\n]+)", msg)
            topics.extend(numbered_items)
        
        # 如果找不到足够的主题，使用段落作为主题
        if len(topics) < 3:
            for msg in user_messages:
                paragraphs = [p.strip() for p in msg.split('\n') if p.strip()]
                for p in paragraphs:
                    if len(p) > 10 and len(p) < 100 and p not in topics:
                        topics.append(p)
                        if len(topics) >= 5:  # 最多5个主题
                            break
        
        # 构建幻灯片
        slides = [
            {
                "type": "title",
                "title": title,
                "subtitle": "自动生成的演示文稿"
            }
        ]
        
        # 添加内容幻灯片
        if topics:
            # 添加一个目录幻灯片
            slides.append({
                "type": "section",
                "title": "目录",
                "bullets": topics[:5]  # 最多使用5个主题作为目录
            })
            
            # 为每个主题创建一个幻灯片
            for i, topic in enumerate(topics[:5]):
                slides.append({
                    "type": "content",
                    "title": topic,
                    "content": f"关于{topic}的详细内容将根据对话自动生成。"
                })
        else:
            # 如果没有提取到主题，添加一些通用幻灯片
            slides.extend([
                {
                    "type": "bullets",
                    "title": "主要内容",
                    "bullets": ["关键点1", "关键点2", "关键点3"]
                },
                {
                    "type": "content",
                    "title": "详细说明",
                    "content": "该部分内容将根据对话自动生成。"
                }
            ])
        
        # 添加结束幻灯片
        slides.append({
            "type": "closing",
            "title": "总结",
            "bullets": ["总结要点", "后续行动", "讨论问题"]
        })
        
        return {
            "title": title,
            "subtitle": "自动生成的演示文稿",
            "author": "智能PPT助手",
            "template": "business",  # 默认使用商务模板
            "slides": slides
        }

    def delete_conversation(self, conversation_id: str) -> bool:
        """
        删除对话
        
        Args:
            conversation_id: 对话ID
            
        Returns:
            是否成功删除
        """
        if conversation_id in self.conversations:
            del self.conversations[conversation_id]
            return True
        return False

# 创建单例实例
chat_service = ChatService() 