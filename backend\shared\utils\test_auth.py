"""
认证功能测试
用于调试和定位认证过程中的问题
"""
import logging
import sys
import os
from datetime import datetime
import bcrypt
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from models.user import User
from utils.auth import verify_password, authenticate_user, get_password_hash
from utils.db import get_db

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def test_password_verification():
    """测试密码验证功能"""
    logger.info("开始测试密码验证...")
    
    # 测试用例1：标准bcrypt哈希
    test_hash = "$2b$12$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW"
    test_password = "test123"
    
    try:
        logger.info("测试用例1: 标准bcrypt哈希验证")
        result = verify_password(test_password, test_hash)
        logger.info(f"验证结果: {result}")
    except Exception as e:
        logger.error(f"测试用例1失败: {str(e)}")
        logger.error(f"错误类型: {type(e)}")
        import traceback
        logger.error(traceback.format_exc())
    
    # 测试用例2：直接使用bcrypt库
    try:
        logger.info("测试用例2: 直接使用bcrypt库验证")
        password_bytes = test_password.encode('utf-8')
        hash_bytes = test_hash.encode('utf-8')
        result = bcrypt.checkpw(password_bytes, hash_bytes)
        logger.info(f"验证结果: {result}")
    except Exception as e:
        logger.error(f"测试用例2失败: {str(e)}")
        logger.error(f"错误类型: {type(e)}")
        import traceback
        logger.error(traceback.format_exc())
    
    # 测试用例3：使用passlib库
    try:
        logger.info("测试用例3: 使用passlib库验证")
        from passlib.context import CryptContext
        pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
        result = pwd_context.verify(test_password, test_hash)
        logger.info(f"验证结果: {result}")
    except Exception as e:
        logger.error(f"测试用例3失败: {str(e)}")
        logger.error(f"错误类型: {type(e)}")
        import traceback
        logger.error(traceback.format_exc())

def test_user_authentication():
    """测试用户认证功能"""
    logger.info("开始测试用户认证...")
    
    # 创建测试数据库连接
    DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://postgres:postgres@localhost:5432/ai_platform")
    engine = create_engine(DATABASE_URL)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    
    # 创建测试会话
    db = SessionLocal()
    try:
        # 测试用例1：测试用户认证
        logger.info("测试用例1: 测试用户认证")
        username = "test"
        password = "test123"
        
        try:
            user = authenticate_user(db, username, password)
            logger.info(f"认证结果: {user is not None}")
            if user:
                logger.info(f"用户信息: ID={user.id}, username={user.username}, is_admin={getattr(user, 'is_admin', False)}")
        except Exception as e:
            logger.error(f"测试用例1失败: {str(e)}")
            logger.error(f"错误类型: {type(e)}")
            import traceback
            logger.error(traceback.format_exc())
        
        # 测试用例2：创建新用户并认证
        logger.info("测试用例2: 创建新用户并认证")
        try:
            # 创建新用户
            new_user = User(
                username="test_user",
                email="<EMAIL>",
                full_name="Test User",
                hashed_password=get_password_hash("test123"),
                is_active=True
            )
            db.add(new_user)
            db.commit()
            
            # 尝试认证
            user = authenticate_user(db, "test_user", "test123")
            logger.info(f"认证结果: {user is not None}")
            if user:
                logger.info(f"用户信息: ID={user.id}, username={user.username}")
        except Exception as e:
            logger.error(f"测试用例2失败: {str(e)}")
            logger.error(f"错误类型: {type(e)}")
            import traceback
            logger.error(traceback.format_exc())
            db.rollback()
    finally:
        db.close()

def test_encoding_handling():
    """测试编码处理"""
    logger.info("开始测试编码处理...")
    
    # 测试用例1：字符串编码转换
    try:
        logger.info("测试用例1: 字符串编码转换")
        test_string = "test123"
        encoded = test_string.encode('utf-8')
        decoded = encoded.decode('utf-8')
        logger.info(f"原始字符串: {test_string}")
        logger.info(f"编码后: {encoded}")
        logger.info(f"解码后: {decoded}")
    except Exception as e:
        logger.error(f"测试用例1失败: {str(e)}")
        logger.error(f"错误类型: {type(e)}")
    
    # 测试用例2：特殊字符处理
    try:
        logger.info("测试用例2: 特殊字符处理")
        test_string = "测试密码123"
        encoded = test_string.encode('utf-8')
        decoded = encoded.decode('utf-8')
        logger.info(f"原始字符串: {test_string}")
        logger.info(f"编码后: {encoded}")
        logger.info(f"解码后: {decoded}")
    except Exception as e:
        logger.error(f"测试用例2失败: {str(e)}")
        logger.error(f"错误类型: {type(e)}")

if __name__ == "__main__":
    logger.info("开始运行认证测试...")
    
    # 运行测试
    test_password_verification()
    test_user_authentication()
    test_encoding_handling()
    
    logger.info("认证测试完成") 