#!/usr/bin/env python3
"""
初始化 Wanx 2.1 PostgreSQL 数据库表
"""
import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# 添加后端路径
backend_dir = Path(__file__).parent.parent
sys.path.append(str(backend_dir))

# 确保加载环境变量（从后端目录）
env_path = backend_dir / ".env"
load_dotenv(env_path)
print(f"📁 加载环境变量: {env_path}")

def init_wanx_postgres_tables():
    """初始化 Wanx PostgreSQL 表"""
    print("🔧 初始化 Wanx 2.1 PostgreSQL 数据库表")
    print("=" * 50)
    
    try:
        from app.core.database import get_db_manager
        
        db_manager = get_db_manager()
        
        if not db_manager.use_postgres:
            print("⚠️ 当前使用的不是 PostgreSQL 数据库")
            print(f"📍 数据库类型: {'PostgreSQL' if db_manager.use_postgres else 'SQLite'}")
            return False
        
        print("✅ 连接到 PostgreSQL 数据库")
        print(f"📍 数据库URL: {db_manager.database_url}")
        
        # 创建 Wanx 视频任务表
        print("\n📋 创建 wanx_video_tasks 表...")
        
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS wanx_video_tasks (
            id SERIAL PRIMARY KEY,
            task_id VARCHAR(255) UNIQUE NOT NULL,
            user_id VARCHAR(255) NOT NULL,
            task_type VARCHAR(50) NOT NULL,
            status VARCHAR(20) DEFAULT 'pending',
            progress INTEGER DEFAULT 0,
            message TEXT,
            params JSONB,
            video_url TEXT,
            error_message TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        """
        
        db_manager.execute_query(create_table_sql)
        print("✅ wanx_video_tasks 表创建成功")
        
        # 创建索引
        print("\n📊 创建索引...")
        
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_wanx_tasks_user_id ON wanx_video_tasks(user_id);",
            "CREATE INDEX IF NOT EXISTS idx_wanx_tasks_status ON wanx_video_tasks(status);",
            "CREATE INDEX IF NOT EXISTS idx_wanx_tasks_created_at ON wanx_video_tasks(created_at);",
            "CREATE INDEX IF NOT EXISTS idx_wanx_tasks_task_type ON wanx_video_tasks(task_type);"
        ]
        
        for index_sql in indexes:
            try:
                db_manager.execute_query(index_sql)
                index_name = index_sql.split("idx_")[1].split(" ")[0]
                print(f"✅ 索引 {index_name} 创建成功")
            except Exception as e:
                print(f"⚠️ 索引创建失败: {e}")
        
        # 验证表结构
        print("\n🔍 验证表结构...")

        try:
            # 使用原生 PostgreSQL 连接验证表结构
            import psycopg2

            conn = psycopg2.connect(os.getenv("DATABASE_URL"))
            cursor = conn.cursor()

            cursor.execute("""
                SELECT column_name, data_type, is_nullable, column_default
                FROM information_schema.columns
                WHERE table_name = 'wanx_video_tasks'
                ORDER BY ordinal_position;
            """)

            columns = cursor.fetchall()

            if columns:
                print("📋 表结构:")
                for row in columns:
                    column_name, data_type, is_nullable, column_default = row
                    nullable = "NULL" if is_nullable == "YES" else "NOT NULL"
                    default = f" DEFAULT {column_default}" if column_default else ""
                    print(f"   - {column_name}: {data_type} {nullable}{default}")

            # 检查表是否为空
            cursor.execute("SELECT COUNT(*) FROM wanx_video_tasks")
            task_count = cursor.fetchone()[0]

            print(f"\n📊 当前任务数量: {task_count}")

            cursor.close()
            conn.close()

        except Exception as e:
            print(f"⚠️ 验证表结构失败: {e}")
            task_count = 0
        
        # 插入测试数据（可选）
        if task_count == 0:
            print("\n💡 是否插入测试数据？(y/n): ", end="")
            choice = input().lower().strip()
            
            if choice == 'y':
                import json
                from datetime import datetime
                
                test_data = {
                    "task_id": "test-wanx-task-001",
                    "user_id": "demo-user",
                    "task_type": "text-to-video",
                    "status": "completed",
                    "progress": 100,
                    "message": "测试任务完成",
                    "params": json.dumps({
                        "prompt": "一只可爱的猫咪在草地上奔跑",
                        "model": "t2v-1.3B",
                        "duration": 5,
                        "resolution": "768x512"
                    }),
                    "video_url": "/api/v1/video/download/test-video.mp4",
                    "created_at": datetime.now(),
                    "updated_at": datetime.now()
                }
                
                db_manager.execute_query("""
                    INSERT INTO wanx_video_tasks 
                    (task_id, user_id, task_type, status, progress, message, params, video_url, created_at, updated_at)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, [
                    test_data["task_id"], test_data["user_id"], test_data["task_type"],
                    test_data["status"], test_data["progress"], test_data["message"],
                    test_data["params"], test_data["video_url"], 
                    test_data["created_at"], test_data["updated_at"]
                ])
                
                print("✅ 测试数据插入成功")
        
        print("\n🎉 Wanx 2.1 PostgreSQL 数据库初始化完成！")
        return True
        
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        return False

def check_postgres_connection():
    """检查 PostgreSQL 连接"""
    print("🔍 检查 PostgreSQL 连接")
    print("-" * 30)
    
    try:
        import psycopg2
        
        database_url = os.getenv("DATABASE_URL")
        if not database_url:
            print("❌ 未找到 DATABASE_URL 环境变量")
            return False
        
        print(f"📍 数据库URL: {database_url}")
        
        # 测试连接
        conn = psycopg2.connect(database_url)
        cursor = conn.cursor()
        
        # 获取数据库版本
        cursor.execute("SELECT version();")
        version = cursor.fetchone()[0]
        print(f"✅ PostgreSQL 连接成功")
        print(f"📊 版本: {version.split(',')[0]}")
        
        # 获取数据库信息
        cursor.execute("SELECT current_database(), current_user;")
        db_info = cursor.fetchone()
        print(f"📋 数据库: {db_info[0]}")
        print(f"👤 用户: {db_info[1]}")
        
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ PostgreSQL 连接失败: {e}")
        return False

def main():
    """主函数"""
    print("🧪 Wanx 2.1 PostgreSQL 数据库初始化")
    print("=" * 50)
    
    # 检查 PostgreSQL 连接
    if not check_postgres_connection():
        print("\n💡 请确保:")
        print("   1. PostgreSQL 服务正在运行")
        print("   2. DATABASE_URL 环境变量正确设置")
        print("   3. 数据库用户有足够的权限")
        return
    
    # 初始化表
    success = init_wanx_postgres_tables()
    
    if success:
        print("\n🎯 下一步:")
        print("   1. 启动 Celery Worker: python start_celery.py")
        print("   2. 启动后端服务: python main.py")
        print("   3. 测试 Wanx 2.1 视频生成功能")
    else:
        print("\n⚠️ 初始化失败，请检查错误信息")

if __name__ == "__main__":
    main()
