#!/usr/bin/env python3
"""
创建测试用户脚本
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from dotenv import load_dotenv
load_dotenv()

from app.services.user_service import UserService

async def create_test_user():
    """创建测试用户"""
    user_service = UserService()
    
    # 测试用户信息
    test_user = {
        "username": "test",
        "email": "<EMAIL>", 
        "password": "test123"
    }
    
    print(f"创建测试用户: {test_user['username']}")
    
    try:
        result = await user_service.create_user(
            test_user["username"],
            test_user["email"], 
            test_user["password"]
        )
        
        if result.get("success"):
            print("✅ 测试用户创建成功!")
            print(f"用户名: {test_user['username']}")
            print(f"密码: {test_user['password']}")
        else:
            print(f"❌ 创建失败: {result.get('error')}")
            
    except Exception as e:
        print(f"❌ 创建过程中出错: {e}")

if __name__ == "__main__":
    asyncio.run(create_test_user())
