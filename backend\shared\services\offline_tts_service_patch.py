"""
TTS服务补丁模块 - 处理espeak依赖缺失和其他TTS相关问题
"""

import logging
import os
import sys
import importlib
import types
from pathlib import Path
import platform
import subprocess
import shutil

logger = logging.getLogger(__name__)

# 全局变量跟踪已应用的补丁
_PATCHES_APPLIED = {
    "espeak": False,
    "setup_model": False,
    "load_config": False,
    "strict_loading": False,
    "all_patches": False
}

def apply_espeak_patch():
    """
    应用补丁以处理espeak缺失的情况
    
    当系统中没有安装espeak时，创建一个简单的模拟实现
    
    Returns:
        bool: 是否成功应用补丁
    """
    global _PATCHES_APPLIED
    
    # 如果补丁已经应用，跳过
    if _PATCHES_APPLIED["espeak"]:
        logger.info("espeak补丁已应用，跳过...")
        return True
    
    try:
        # 首先尝试使用espeak，看看是否真的不可用
        try:
            from TTS.tts.utils.text.phonemizers import ESpeak
            test_espeak = ESpeak(language="en-us")
            test_result = test_espeak.phonemize("test")
            # 如果执行到这里，说明espeak可用
            logger.info("espeak在系统中可用，无需应用补丁")
            _PATCHES_APPLIED["espeak"] = True
            return True
        except Exception as e:
            # 错误表明espeak不可用，需要应用补丁
            logger.warning(f"尝试使用真实espeak失败: {e}")
            logger.info("将应用espeak模拟补丁...")
        
        # 检查是否已有phonemizer模块
        try:
            import TTS.tts.utils.text.phonemizers as phonemizers
            from TTS.tts.utils.text.phonemizers import ESpeak
            
            # 创建一个虚拟的ESpeak类
            class MockESpeak:
                def __init__(self, language="en-us", backend="espeak", with_stress=True, 
                            phoneme_separator="|", keep_puncs=True, njobs=1):
                    self.language = language
                    self.backend = backend
                    self.with_stress = with_stress
                    self.phoneme_separator = phoneme_separator
                    self.keep_puncs = keep_puncs
                    self.njobs = njobs
                    logger.info(f"已创建MockESpeak，使用语言: {language}")
                    
                def phonemize(self, text, separator=None):
                    """简单返回原始文本，不执行真正的音素化"""
                    logger.debug(f"MockESpeak.phonemize被调用: {text[:30]}")
                    # 简单的音素化模拟
                    if isinstance(text, list):
                        return [t for t in text]
                    return text
                    
                def is_available(self):
                    """报告可用性"""
                    return True
                    
                def version(self):
                    """返回版本信息"""
                    return "mock-1.0"
                    
                def language_switch(self, language):
                    """切换语言"""
                    self.language = language
                    return True
            
            # 保存原始方法
            original_get_phonemizer = phonemizers.get_phonemizer_by_name
            
            # 创建补丁函数
            def patched_get_phonemizer_by_name(name, **kwargs):
                """替代phonemizer获取函数"""
                try:
                    # 先尝试原始方法
                    return original_get_phonemizer(name, **kwargs)
                except Exception as e:
                    # 如果失败并且是请求espeak
                    if name.lower() == "espeak":
                        logger.warning(f"无法加载真正的espeak phonemizer: {e}")
                        logger.info("使用MockESpeak替代")
                        return MockESpeak(**kwargs)
                    # 其他错误继续抛出
                    raise
            
            # 应用补丁
            phonemizers.get_phonemizer_by_name = patched_get_phonemizer_by_name
            
            # 替代ESpeak类
            if hasattr(phonemizers, 'ESpeak'):
                phonemizers.ESpeak = MockESpeak
            
            # 直接修补espeak_wrapper模块
            try:
                from TTS.tts.utils.text.phonemizers import espeak_wrapper
                espeak_wrapper.ESpeak = MockESpeak
                # 替换模块中的检查函数
                def mock_is_espeak_available():
                    return True
                espeak_wrapper.is_espeak_available = mock_is_espeak_available
                logger.info("已修补espeak_wrapper模块")
            except Exception as e:
                logger.error(f"修补espeak_wrapper模块失败: {e}")
            
            # 修补TTS.utils.synthesis模块中使用phonemizer的部分
            try:
                from TTS.utils.synthesizer import Synthesizer
                
                # 保存原始初始化方法
                original_init = Synthesizer.__init__
                
                # 创建补丁初始化方法
                def patched_init(self, *args, **kwargs):
                    try:
                        # 调用原始初始化方法
                        original_init(self, *args, **kwargs)
                    except Exception as e:
                        if "phonemizer" in str(e).lower() or "espeak" in str(e).lower():
                            logger.warning(f"合成器初始化失败，尝试修复: {e}")
                            # 如果是phonemizer或espeak相关错误，继续初始化其他部分
                            # 通常这些错误出现在_load_tts中
                            
                            # 手动设置必要的属性
                            if not hasattr(self, 'tts_config') and 'tts_config_path' in kwargs:
                                from TTS.utils.io import load_config
                                self.tts_config = load_config(kwargs['tts_config_path'])
                                
                            if not hasattr(self, 'tts_model') and kwargs.get('tts_checkpoint'):
                                # 尝试使用备选方法加载模型
                                logger.info("尝试使用备选方法加载TTS模型...")
                                try:
                                    from TTS.utils.generic_utils import setup_model
                                    self.tts_model = setup_model(self.tts_config)
                                    # 加载检查点
                                    import torch
                                    checkpoint = torch.load(kwargs['tts_checkpoint'], map_location=torch.device('cpu'))
                                    self.tts_model.load_state_dict(checkpoint['model'])
                                    logger.info("成功使用备选方法加载TTS模型")
                                except Exception as load_e:
                                    logger.error(f"备选加载方法也失败: {load_e}")
                                    # 重新抛出原始异常
                                    raise e
                        else:
                            # 如果是其他错误，重新抛出
                            raise
                
                # 应用补丁
                Synthesizer.__init__ = patched_init
                logger.info("已修补Synthesizer初始化方法")
            except Exception as e:
                logger.error(f"修补Synthesizer初始化方法失败: {e}")
                
            logger.info("espeak补丁已成功应用")
            _PATCHES_APPLIED["espeak"] = True
            return True
            
        except ImportError as e:
            logger.error(f"无法导入TTS.tts.utils.text.phonemizers模块: {e}")
            return False
            
    except Exception as e:
        logger.error(f"应用espeak补丁时出错: {e}")
        return False

def fix_load_config_missing():
    """
    修复TTS.utils.io中缺少load_config函数的问题
    
    Returns:
        bool: 是否成功应用补丁
    """
    global _PATCHES_APPLIED
    
    # 如果补丁已经应用，跳过
    if _PATCHES_APPLIED["load_config"]:
        logger.info("load_config补丁已应用，跳过...")
        return True
    
    try:
        # 首先尝试使用load_config，看看是否已存在
        try:
            from TTS.utils.io import load_config
            # 如果执行到这里，说明函数可用
            logger.info("load_config函数已存在，无需应用补丁")
            _PATCHES_APPLIED["load_config"] = True
            return True
        except ImportError:
            # 函数不存在，需要应用补丁
            logger.warning("TTS.utils.io中缺少load_config函数，将应用补丁...")
        
        # 修补TTS.utils.io模块
        import TTS.utils.io
        
        # 添加load_config函数
        def load_config(config_path):
            """
            从配置文件加载配置
            
            Args:
                config_path: 配置文件路径
                
            Returns:
                AttrDict: 配置对象
            """
            import json
            from TTS.utils.generic_utils import AttrDict
            
            # 读取配置文件
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 转换为AttrDict
            config = AttrDict(config)
            return config
        
        # 添加到模块
        setattr(TTS.utils.io, 'load_config', load_config)
        
        # 检查是否成功添加
        if hasattr(TTS.utils.io, 'load_config'):
            logger.info("已成功添加load_config函数到TTS.utils.io模块")
            _PATCHES_APPLIED["load_config"] = True
            return True
        else:
            logger.error("添加load_config函数到TTS.utils.io模块失败")
            return False
            
    except Exception as e:
        logger.error(f"修复load_config函数时出错: {e}")
        return False

def fix_model_size_mismatch():
    """
    修复模型状态字典尺寸不匹配的问题
    
    Returns:
        bool: 是否成功应用补丁
    """
    global _PATCHES_APPLIED
    
    # 如果补丁已经应用，跳过
    if _PATCHES_APPLIED["strict_loading"]:
        logger.info("模型尺寸不匹配补丁已应用，跳过...")
        return True
    
    try:
        # 修补Tacotron2的load_state_dict方法
        from TTS.tts.models.tacotron2 import Tacotron2
        import torch.nn as nn
        
        # 保存原始方法
        original_load_state_dict = nn.Module.load_state_dict
        
        # 创建补丁函数
        def patched_load_state_dict(self, state_dict, strict=True):
            """补丁版的load_state_dict，自动处理尺寸不匹配的情况"""
            try:
                # 首先尝试非严格模式加载
                return original_load_state_dict(self, state_dict, False)
            except Exception as e:
                logger.warning(f"非严格模式加载失败: {e}")
                
                # 如果是Tacotron2模型，尝试特殊处理
                if isinstance(self, Tacotron2):
                    logger.info("检测到Tacotron2模型，尝试修复状态字典...")
                    
                    # 创建新的state_dict修复尺寸不匹配问题
                    fixed_state_dict = {}
                    model_state = self.state_dict()
                    
                    for key in model_state:
                        if key in state_dict:
                            # 检查尺寸是否匹配
                            if state_dict[key].size() != model_state[key].size():
                                logger.warning(f"尺寸不匹配: {key}, 模型: {model_state[key].size()}, 状态字典: {state_dict[key].size()}")
                                
                                # 对于embedding.weight特殊处理
                                if key == "embedding.weight":
                                    # 获取尺寸
                                    curr_size = model_state[key].size()
                                    load_size = state_dict[key].size()
                                    
                                    # 使用重采样调整尺寸
                                    if curr_size[0] > load_size[0]:
                                        logger.info(f"尝试调整embedding权重: {load_size} -> {curr_size}")
                                        import torch.nn.functional as F
                                        
                                        # 重塑为可插值的形状
                                        reshaped = state_dict[key].unsqueeze(0)  # [D, E] -> [1, D, E]
                                        
                                        # 进行插值操作
                                        interpolated = F.interpolate(
                                            reshaped, 
                                            size=(curr_size[0], curr_size[1]), 
                                            mode='bilinear', 
                                            align_corners=False
                                        )
                                        
                                        # 恢复原来的形状
                                        fixed_state_dict[key] = interpolated.squeeze(0)
                                        continue
                                
                                # 对于线性投影层特殊处理
                                if "linear_projection" in key:
                                    # 创建一个随机初始化的替代
                                    fixed_state_dict[key] = model_state[key]
                                    continue
                            
                            # 尺寸匹配，直接复制
                            fixed_state_dict[key] = state_dict[key]
                        else:
                            # 键不存在，使用模型自己的值
                            fixed_state_dict[key] = model_state[key]
                    
                    # 使用修复后的状态字典
                    logger.info("应用修复后的状态字典")
                    return original_load_state_dict(self, fixed_state_dict, False)
                
                # 其他模型，继续抛出异常
                raise
        
        # 应用补丁
        nn.Module.load_state_dict = patched_load_state_dict
        
        logger.info("已成功应用模型尺寸不匹配补丁")
        _PATCHES_APPLIED["strict_loading"] = True
        return True
        
    except Exception as e:
        logger.error(f"修复模型尺寸不匹配时出错: {e}")
        return False

def provide_espeak_install_guide():
    """
    根据操作系统提供安装espeak的指南
    
    Returns:
        str: 安装指南
    """
    system = platform.system().lower()
    
    if system == "windows":
        return """
在Windows上安装espeak:
1. 访问 http://espeak.sourceforge.net/download.html
2. 下载Windows版本的eSpeak
3. 运行安装程序并按照指示完成安装
4. 将安装目录添加到系统PATH环境变量
        """
    elif system == "linux":
        return """
在Linux上安装espeak:
- Ubuntu/Debian: sudo apt-get install espeak
- Fedora: sudo dnf install espeak
- Arch Linux: sudo pacman -S espeak
        """
    elif system == "darwin":  # macOS
        return """
在macOS上安装espeak:
使用Homebrew: brew install espeak
        """
    else:
        return "请访问 http://espeak.sourceforge.net/download.html 获取适用于您系统的安装说明。"

def attempt_install_espeak():
    """
    尝试自动安装espeak（仅在部分系统上支持）
    
    Returns:
        bool: 是否成功安装
    """
    system = platform.system().lower()
    
    try:
        if system == "linux":
            # 尝试检测Linux发行版
            if os.path.exists("/etc/debian_version"):
                # Debian/Ubuntu
                logger.info("检测到Debian/Ubuntu系统，尝试安装espeak...")
                subprocess.run(["sudo", "apt-get", "update"], check=True)
                subprocess.run(["sudo", "apt-get", "install", "-y", "espeak"], check=True)
                return True
            elif os.path.exists("/etc/fedora-release"):
                # Fedora
                logger.info("检测到Fedora系统，尝试安装espeak...")
                subprocess.run(["sudo", "dnf", "install", "-y", "espeak"], check=True)
                return True
            elif os.path.exists("/etc/arch-release"):
                # Arch Linux
                logger.info("检测到Arch Linux系统，尝试安装espeak...")
                subprocess.run(["sudo", "pacman", "-S", "--noconfirm", "espeak"], check=True)
                return True
        elif system == "darwin":  # macOS
            # 检查是否安装了Homebrew
            if shutil.which("brew"):
                logger.info("检测到macOS系统，尝试使用Homebrew安装espeak...")
                subprocess.run(["brew", "install", "espeak"], check=True)
                return True
            
        # 对于Windows或其他无法自动安装的系统
        logger.warning(f"无法在{system}系统上自动安装espeak，请手动安装")
        logger.info(provide_espeak_install_guide())
        return False
    except Exception as e:
        logger.error(f"尝试安装espeak时出错: {e}")
        return False

def patch_tts_setup_model():
    """
    修补TTS模块中setup_model导入错误
    
    Returns:
        bool: 是否成功应用补丁
    """
    global _PATCHES_APPLIED
    
    # 如果补丁已经应用，跳过
    if _PATCHES_APPLIED["setup_model"]:
        logger.info("setup_model补丁已应用，跳过...")
        return True
    
    try:
        # 尝试导入，看是否已有setup_model
        try:
            from TTS.utils.generic_utils import setup_model
            # 如果成功导入，说明不需要补丁
            logger.info("setup_model函数已存在，无需应用补丁")
            _PATCHES_APPLIED["setup_model"] = True
            return True
        except ImportError:
            # 需要应用补丁
            logger.info("未找到setup_model函数，将应用补丁...")
        
        # 尝试修复setup_model导入
        import TTS.utils.generic_utils
        
        if not hasattr(TTS.utils.generic_utils, 'setup_model'):
            logger.info("添加缺失的setup_model函数到generic_utils...")
            
            # 定义setup_model函数
            def setup_model(config, model_file=None):
                """
                从config创建模型
                
                Args:
                    config: 模型配置
                    model_file: 可选，模型文件路径
                    
                Returns:
                    model: 创建的模型
                """
                try:
                    # 尝试导入TTS.tts.models中的setup_model
                    from TTS.tts.models import setup_model as setup_tts_model
                    return setup_tts_model(config)
                except ImportError as e:
                    logger.error(f"导入TTS.tts.models中的setup_model失败: {e}")
                    
                    # 尝试直接创建模型
                    try:
                        model_name = config.model.lower()
                        
                        if "tacotron" in model_name:
                            from TTS.tts.models.tacotron2 import Tacotron2
                            model = Tacotron2(config)
                        elif "glow" in model_name:
                            from TTS.tts.models.glow_tts import GlowTTS
                            model = GlowTTS(config)
                        else:
                            logger.error(f"未知模型类型: {model_name}")
                            raise ValueError(f"未知模型类型: {model_name}")
                            
                        return model
                    except Exception as model_e:
                        logger.error(f"创建模型失败: {model_e}")
                        raise
            
            # 添加到模块
            setattr(TTS.utils.generic_utils, 'setup_model', setup_model)
            logger.info("已添加setup_model函数")
            
            _PATCHES_APPLIED["setup_model"] = True
            return True
            
    except Exception as e:
        logger.error(f"修补TTS.utils.generic_utils.setup_model失败: {e}")
        return False

def apply_patches(force=False):
    """
    应用所有补丁
    
    Args:
        force: 是否强制重新应用补丁，即使已经应用过
        
    Returns:
        bool: 是否所有补丁都成功应用
    """
    global _PATCHES_APPLIED
    
    # 如果所有补丁已经应用且不是强制模式，直接返回
    if _PATCHES_APPLIED["all_patches"] and not force:
        logger.info("所有补丁已应用，跳过...")
        return True
    
    success = True
    
    # 应用espeak补丁
    if not apply_espeak_patch() and not force:
        logger.warning("应用espeak补丁失败")
        success = False
    
    # 修补setup_model
    if not patch_tts_setup_model() and not force:
        logger.warning("修补setup_model失败")
        success = False
    
    # 修复load_config缺失
    if not fix_load_config_missing() and not force:
        logger.warning("修复load_config缺失失败")
        success = False
    
    # 修复模型尺寸不匹配
    if not fix_model_size_mismatch() and not force:
        logger.warning("修复模型尺寸不匹配失败")
        success = False
    
    if success:
        _PATCHES_APPLIED["all_patches"] = True
        logger.info("所有补丁已成功应用")
    
    return success

if __name__ == "__main__":
    # 配置日志
    logging.basicConfig(level=logging.INFO)
    # 应用补丁
    if apply_patches():
        logger.info("所有补丁已成功应用")
    else:
        logger.error("应用补丁过程中发生错误")