"""
图像处理任务 - 简洁版本
"""
import time
from datetime import datetime
from pathlib import Path
from app.core.celery_unified import celery_app

# 输出目录
OUTPUT_DIR = Path(__file__).parent.parent.parent / "storage" / "images"
OUTPUT_DIR.mkdir(parents=True, exist_ok=True)

@celery_app.task(bind=True, name='image.text_to_image')
def text_to_image(self, prompt: str, model: str = "stable-diffusion", **kwargs):
    """文本生成图像任务"""
    task_id = self.request.id
    
    try:
        print(f"[图像任务] 开始: {task_id}")
        print(f"[图像任务] 提示词: {prompt}")
        
        simple_task_manager.create_task(
            task_id=task_id,
            task_type='text_to_image',
            prompt=prompt,
            model=model,
            **kwargs
        )
        
        update_task_progress(task_id, 20, "加载模型...")
        time.sleep(1)
        
        update_task_progress(task_id, 50, "生成图像...")
        time.sleep(2)
        
        update_task_progress(task_id, 80, "后处理...")
        time.sleep(1)
        
        # 生成输出文件
        output_file = OUTPUT_DIR / f"image_{task_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        
        # 模拟生成图像文件
        with open(output_file.with_suffix('.txt'), 'w', encoding='utf-8') as f:
            f.write(f"图像生成任务\n")
            f.write(f"任务ID: {task_id}\n")
            f.write(f"提示词: {prompt}\n")
            f.write(f"模型: {model}\n")
            f.write(f"生成时间: {datetime.now()}\n")
        
        result = {
            'task_id': task_id,
            'status': 'completed',
            'output_file': str(output_file.with_suffix('.txt')),
            'image_url': f'/api/v1/image/download/{output_file.name}',
            'metadata': {
                'prompt': prompt,
                'model': model,
                'resolution': kwargs.get('resolution', '512x512')
            }
        }
        
        update_task_progress(task_id, 100, "图像生成完成")
        return result
        
    except Exception as e:
        error_msg = f"图像生成失败: {str(e)}"
        simple_task_manager.update_task(task_id=task_id, status='failed', error=error_msg)
        raise

@celery_app.task(name='image.test')
def test_image_task(prompt: str = "A beautiful landscape"):
    """图像模块测试任务"""
    print(f"[图像测试] 提示词: {prompt}")
    time.sleep(0.5)
    return {
        'prompt': prompt,
        'module': 'image',
        'timestamp': datetime.now().isoformat()
    }
