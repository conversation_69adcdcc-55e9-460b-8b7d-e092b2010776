<template>
  <div class="dashboard-container">
    <a-row :gutter="24">
      <!-- 欢迎卡片 -->
      <a-col :span="24">
        <a-card :bordered="false" class="welcome-card">
          <template #title>
            <div class="welcome-title">
              <span>欢迎使用后台管理系统</span>
              <span class="welcome-date">{{ currentDate }}</span>
            </div>
          </template>
          <div class="welcome-content">
            <div class="user-info">
              <a-avatar :size="64" :src="userAvatar || '/avatar-default.png'" />
              <div class="user-detail">
                <div class="user-name">{{ userName }}, 早上好！</div>
                <div class="user-role">{{ userRole }}</div>
              </div>
            </div>
            <div class="data-overview">
              <div class="data-item">
                <div class="data-count">{{ dataStats.totalUsers }}</div>
                <div class="data-name">用户总数</div>
              </div>
              <a-divider type="vertical" class="data-divider" />
              <div class="data-item">
                <div class="data-count">{{ dataStats.totalRoles }}</div>
                <div class="data-name">角色总数</div>
              </div>
              <a-divider type="vertical" class="data-divider" />
              <div class="data-item">
                <div class="data-count">{{ dataStats.totalMenus }}</div>
                <div class="data-name">菜单总数</div>
              </div>
              <a-divider type="vertical" class="data-divider" />
              <div class="data-item">
                <div class="data-count">{{ dataStats.logCount }}</div>
                <div class="data-name">今日日志数</div>
              </div>
            </div>
          </div>
        </a-card>
      </a-col>
    </a-row>

    <a-row :gutter="24" style="margin-top: 24px">
      <!-- 快捷操作区 -->
      <a-col :xs="24" :md="12" :lg="6">
        <a-card class="shortcut-card" :bordered="false">
          <template #title>
            <div class="card-title">
              <environment-outlined />
              <span>快捷操作</span>
            </div>
          </template>
          <div class="shortcuts">
            <a-row :gutter="[16, 16]">
              <a-col :span="12" v-for="item in shortcuts" :key="item.title">
                <div class="shortcut-item" @click="navigate(item.route)">
                  <component :is="item.icon" class="shortcut-icon" />
                  <div class="shortcut-title">{{ item.title }}</div>
                </div>
              </a-col>
            </a-row>
          </div>
        </a-card>
      </a-col>

      <!-- 系统信息 -->
      <a-col :xs="24" :md="12" :lg="6">
        <a-card class="system-card" :bordered="false">
          <template #title>
            <div class="card-title">
              <desktop-outlined />
              <span>系统信息</span>
            </div>
          </template>
          <a-descriptions :column="1" size="small">
            <a-descriptions-item label="系统版本">v1.0.0</a-descriptions-item>
            <a-descriptions-item label="Node版本">{{ systemInfo.nodeVersion }}</a-descriptions-item>
            <a-descriptions-item label="服务器时间">{{ systemInfo.serverTime }}</a-descriptions-item>
            <a-descriptions-item label="服务器IP">{{ systemInfo.serverIp }}</a-descriptions-item>
            <a-descriptions-item label="运行时长">{{ systemInfo.uptime }}</a-descriptions-item>
          </a-descriptions>
        </a-card>
      </a-col>

      <!-- 待办任务 -->
      <a-col :xs="24" :md="12" :lg="12">
        <a-card class="todo-card" :bordered="false">
          <template #title>
            <div class="card-title">
              <check-circle-outlined />
              <span>待办任务</span>
            </div>
          </template>
          <template #extra>
            <a-button type="link" @click="refreshTodos">刷新</a-button>
          </template>
          <a-list
            :loading="todosLoading"
            :data-source="todos"
            :pagination="false"
          >
            <template #renderItem="{ item }">
              <a-list-item>
                <a-list-item-meta>
                  <template #title>
                    <div class="todo-title">
                      <a-tag :color="item.priority === 'high' ? 'red' : item.priority === 'medium' ? 'orange' : 'blue'">
                        {{ item.priority === 'high' ? '高' : item.priority === 'medium' ? '中' : '低' }}
                      </a-tag>
                      <span>{{ item.title }}</span>
                    </div>
                  </template>
                  <template #description>
                    <div class="todo-desc">{{ item.description }}</div>
                    <div class="todo-time">截止时间: {{ item.deadline }}</div>
                  </template>
                </a-list-item-meta>
                <template #actions>
                  <a-button type="link" size="small" @click="completeTodo(item)">完成</a-button>
                </template>
              </a-list-item>
            </template>
            <template #empty>
              <a-empty description="暂无待办任务" />
            </template>
          </a-list>
        </a-card>
      </a-col>
    </a-row>

    <a-row :gutter="24" style="margin-top: 24px">
      <!-- 活跃用户统计 -->
      <a-col :span="24">
        <a-card :bordered="false">
          <template #title>
            <div class="card-title">
              <line-chart-outlined />
              <span>用户活跃度</span>
            </div>
          </template>
          <template #extra>
            <a-radio-group v-model:value="chartTimeRange" button-style="solid" size="small">
              <a-radio-button value="week">本周</a-radio-button>
              <a-radio-button value="month">本月</a-radio-button>
              <a-radio-button value="year">本年</a-radio-button>
            </a-radio-group>
          </template>
          <div class="chart-container" ref="chartRef"></div>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, computed, onMounted, watch } from 'vue';
import { useRouter } from 'vue-router';
// 从全局变量中获取echarts，不再导入
// import * as echarts from 'echarts';
import {
  EnvironmentOutlined,
  DesktopOutlined,
  CheckCircleOutlined,
  LineChartOutlined,
  UserOutlined,
  TeamOutlined,
  FileOutlined,
  SettingOutlined,
  KeyOutlined,
  ClockCircleOutlined
} from '@ant-design/icons-vue';

// 模拟数据
const getDashboardData = () => {
  return new Promise(resolve => {
    setTimeout(() => {
      resolve({
        data: {
          user: {
            name: '管理员',
            avatar: '',
            role: '超级管理员'
          },
          stats: {
            totalUsers: 3582,
            totalRoles: 8,
            totalMenus: 56,
            logCount: 198
          },
          systemInfo: {
            nodeVersion: 'v16.15.0',
            serverTime: new Date().toLocaleString(),
            serverIp: '*************',
            uptime: '10天4小时26分钟'
          },
          todos: [
            {
              id: 1,
              title: '系统安全更新',
              description: '需要对系统进行安全更新，修复潜在漏洞',
              priority: 'high',
              deadline: '2023-10-15 18:00'
            },
            {
              id: 2,
              title: '用户反馈处理',
              description: '处理用户提交的反馈信息，优先级中',
              priority: 'medium',
              deadline: '2023-10-16 12:00'
            },
            {
              id: 3,
              title: '数据库备份',
              description: '执行每周数据库备份任务',
              priority: 'medium',
              deadline: '2023-10-17 10:00'
            },
            {
              id: 4,
              title: '更新操作手册',
              description: '更新系统操作手册，添加新功能使用说明',
              priority: 'low',
              deadline: '2023-10-20 18:00'
            }
          ],
          chartData: {
            week: {
              xAxis: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
              series: [
                {
                  name: '活跃用户',
                  data: [120, 132, 101, 134, 90, 230, 210]
                },
                {
                  name: '新增用户',
                  data: [20, 32, 18, 34, 25, 38, 35]
                }
              ]
            },
            month: {
              xAxis: Array.from({ length: 30 }, (_, i) => `${i + 1}日`),
              series: [
                {
                  name: '活跃用户',
                  data: Array.from({ length: 30 }, () => Math.floor(Math.random() * 300 + 100))
                },
                {
                  name: '新增用户',
                  data: Array.from({ length: 30 }, () => Math.floor(Math.random() * 50 + 10))
                }
              ]
            },
            year: {
              xAxis: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
              series: [
                {
                  name: '活跃用户',
                  data: [900, 850, 950, 1000, 1100, 1050, 1150, 1200, 1100, 1050, 1150, 1200]
                },
                {
                  name: '新增用户',
                  data: [120, 132, 101, 134, 90, 230, 210, 180, 190, 230, 245, 260]
                }
              ]
            }
          }
        }
      });
    }, 500);
  });
};

export default defineComponent({
  name: 'Dashboard',
  components: {
    EnvironmentOutlined,
    DesktopOutlined,
    CheckCircleOutlined,
    LineChartOutlined,
    UserOutlined,
    TeamOutlined,
    FileOutlined,
    SettingOutlined,
    KeyOutlined,
    ClockCircleOutlined
  },
  setup() {
    const router = useRouter();
    
    // 当前日期
    const currentDate = ref(new Date().toLocaleDateString());
    
    // 用户信息
    const userName = ref('');
    const userAvatar = ref('');
    const userRole = ref('');
    
    // 数据统计
    const dataStats = reactive({
      totalUsers: 0,
      totalRoles: 0,
      totalMenus: 0,
      logCount: 0
    });
    
    // 系统信息
    const systemInfo = reactive({
      nodeVersion: '',
      serverTime: '',
      serverIp: '',
      uptime: ''
    });
    
    // 快捷入口
    const shortcuts = [
      {
        title: '用户管理',
        icon: UserOutlined,
        route: '/system/user'
      },
      {
        title: '角色管理',
        icon: TeamOutlined,
        route: '/system/role'
      },
      {
        title: '菜单管理',
        icon: SettingOutlined,
        route: '/system/menu'
      },
      {
        title: '操作日志',
        icon: FileOutlined,
        route: '/monitor/log'
      }
    ];
    
    // 待办任务
    const todos = ref([]);
    const todosLoading = ref(false);
    
    // 图表相关
    const chartRef = ref(null);
    const chartInstance = ref(null);
    const chartTimeRange = ref('week');
    const chartData = ref({
      week: { xAxis: [], series: [] },
      month: { xAxis: [], series: [] },
      year: { xAxis: [], series: [] }
    });
    
    // 获取仪表盘数据
    const fetchDashboardData = async () => {
      try {
        const res = await getDashboardData();
        const data = res.data;
        
        // 更新用户信息
        userName.value = data.user.name;
        userAvatar.value = data.user.avatar;
        userRole.value = data.user.role;
        
        // 更新统计数据
        dataStats.totalUsers = data.stats.totalUsers;
        dataStats.totalRoles = data.stats.totalRoles;
        dataStats.totalMenus = data.stats.totalMenus;
        dataStats.logCount = data.stats.logCount;
        
        // 更新系统信息
        systemInfo.nodeVersion = data.systemInfo.nodeVersion;
        systemInfo.serverTime = data.systemInfo.serverTime;
        systemInfo.serverIp = data.systemInfo.serverIp;
        systemInfo.uptime = data.systemInfo.uptime;
        
        // 更新待办任务
        todos.value = data.todos;
        
        // 更新图表数据
        chartData.value = data.chartData;
        
        // 初始化图表
        initChart();
      } catch (error) {
        console.error('获取仪表盘数据失败:', error);
      }
    };
    
    // 初始化图表
    const initChart = () => {
      if (!chartRef.value) return;
      
      if (!chartInstance.value) {
        // 使用全局变量echarts
        chartInstance.value = window.echarts.init(chartRef.value);
      }
      
      updateChart();
      
      // 监听窗口大小变化，调整图表大小
      window.addEventListener('resize', () => {
        chartInstance.value?.resize();
      });
    };
    
    // 更新图表数据
    const updateChart = () => {
      if (!chartInstance.value) return;
      
      const currentData = chartData.value[chartTimeRange.value];
      
      const option = {
        title: {
          text: '用户数据统计',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: ['活跃用户', '新增用户'],
          bottom: 0
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '10%',
          top: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: currentData.xAxis
        },
        yAxis: {
          type: 'value'
        },
        series: currentData.series.map(item => ({
          name: item.name,
          type: 'line',
          smooth: true,
          data: item.data,
          areaStyle: {
            opacity: 0.2
          }
        }))
      };
      
      chartInstance.value.setOption(option);
    };
    
    // 刷新待办任务
    const refreshTodos = async () => {
      todosLoading.value = true;
      try {
        const res = await getDashboardData();
        todos.value = res.data.todos;
      } catch (error) {
        console.error('获取待办任务失败:', error);
      } finally {
        todosLoading.value = false;
      }
    };
    
    // 完成待办任务
    const completeTodo = (todo) => {
      // 实际应该调用API
      todos.value = todos.value.filter(item => item.id !== todo.id);
    };
    
    // 导航到指定路由
    const navigate = (route) => {
      router.push(route);
    };
    
    // 监听图表时间范围变化
    watch(chartTimeRange, () => {
      updateChart();
    });
    
    // 组件挂载时加载数据
    onMounted(() => {
      fetchDashboardData();
    });
    
    return {
      currentDate,
      userName,
      userAvatar,
      userRole,
      dataStats,
      systemInfo,
      shortcuts,
      todos,
      todosLoading,
      chartRef,
      chartTimeRange,
      refreshTodos,
      completeTodo,
      navigate
    };
  }
});
</script>

<style lang="less" scoped>
.dashboard-container {
  padding: 24px;
  
  .welcome-card {
    background: linear-gradient(to right, #1890ff, #52c41a);
    
    :deep(.ant-card-head) {
      border-bottom: none;
      color: #fff;
      
      .ant-card-head-title {
        padding-bottom: 0;
      }
    }
    
    :deep(.ant-card-body) {
      padding-top: 0;
    }
    
    .welcome-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: #fff;
      font-size: 16px;
      
      .welcome-date {
        font-size: 14px;
        opacity: 0.8;
      }
    }
    
    .welcome-content {
      color: #fff;
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .user-info {
        display: flex;
        align-items: center;
        
        :deep(.ant-avatar) {
          margin-right: 16px;
          border: 2px solid rgba(255, 255, 255, 0.8);
        }
        
        .user-detail {
          .user-name {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 8px;
          }
          
          .user-role {
            font-size: 14px;
            opacity: 0.8;
          }
        }
      }
      
      .data-overview {
        display: flex;
        align-items: center;
        
        .data-item {
          padding: 0 24px;
          text-align: center;
          
          .data-count {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 4px;
          }
          
          .data-name {
            font-size: 14px;
            opacity: 0.8;
          }
        }
        
        .data-divider {
          height: 40px;
          background-color: rgba(255, 255, 255, 0.3);
        }
      }
    }
  }
  
  .card-title {
    display: flex;
    align-items: center;
    
    .anticon {
      margin-right: 8px;
      font-size: 16px;
    }
  }
  
  .shortcut-card {
    height: 100%;
    
    .shortcuts {
      .shortcut-item {
        height: 100px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        background-color: #f0f5ff;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s;
        
        &:hover {
          background-color: #d6e4ff;
          transform: translateY(-2px);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }
        
        .shortcut-icon {
          font-size: 24px;
          margin-bottom: 8px;
          color: #1890ff;
        }
        
        .shortcut-title {
          font-size: 14px;
        }
      }
    }
  }
  
  .system-card {
    height: 100%;
    
    :deep(.ant-descriptions-item-label) {
      color: rgba(0, 0, 0, 0.65);
      width: 120px;
    }
    
    :deep(.ant-descriptions-item-content) {
      color: rgba(0, 0, 0, 0.85);
      font-weight: 500;
    }
  }
  
  .todo-card {
    height: 100%;
    
    .todo-title {
      display: flex;
      align-items: center;
      
      .ant-tag {
        margin-right: 8px;
      }
    }
    
    .todo-desc {
      color: rgba(0, 0, 0, 0.65);
      margin-bottom: 4px;
    }
    
    .todo-time {
      font-size: 12px;
      color: rgba(0, 0, 0, 0.45);
    }
  }
  
  .chart-container {
    height: 400px;
  }
}
</style> 