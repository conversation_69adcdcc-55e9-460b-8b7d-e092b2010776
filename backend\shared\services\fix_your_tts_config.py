#!/usr/bin/env python
"""
修复YourTTS配置文件

此脚本创建一个有效的YourTTS配置文件，替换损坏的配置文件。
"""

import os
import json
import logging
import shutil
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("fix_your_tts_config")

def fix_your_tts_config():
    """修复YourTTS配置文件"""
    try:
        # 确定配置文件路径
        backend_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        config_dir = os.path.join(backend_dir, "models", "tts", "tts", "tts_models--multilingual--multi-dataset--your_tts")
        config_path = os.path.join(config_dir, "config.json")
        
        # 检查目录是否存在
        if not os.path.exists(config_dir):
            os.makedirs(config_dir, exist_ok=True)
            logger.info(f"创建目录: {config_dir}")
        
        # 备份原始文件（如果存在）
        if os.path.exists(config_path):
            backup_path = config_path + ".bak"
            if not os.path.exists(backup_path):
                shutil.copy2(config_path, backup_path)
                logger.info(f"已备份原始配置文件到: {backup_path}")
        
        # 创建干净的配置文件
        clean_config = {
            "model": "your_tts",
            "run_name": "your_tts_multilingual",
            "run_description": "Your TTS multilingual model",
            
            # 音频参数
            "audio": {
                "fft_size": 1024,
                "sample_rate": 16000,
                "win_length": 1024,
                "hop_length": 256,
                "num_mels": 80,
                "mel_fmin": 0,
                "mel_fmax": 8000
            },
            
            # 文本参数
            "use_phonemes": False,
            "phonemizer": None,
            "text_cleaner": "multilingual_cleaners",
            "characters": {
                "characters_class": "TTS.tts.models.vits.VitsCharacters",
                "pad": "_",
                "eos": "&",
                "bos": "*",
                "characters": "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz!'(),-.:;? ",
                "punctuations": "!'(),-.:;? ",
                "phonemes": ""
            },
            
            # 训练参数
            "batch_size": 32,
            "eval_batch_size": 16,
            "num_loader_workers": 4,
            "num_eval_loader_workers": 4,
            "run_eval": True,
            "test_delay_epochs": -1,
            "epochs": 1000,
            "lr": 0.0002,
            "lr_scheduler": None,
            "min_audio_len": 1,
            "max_audio_len": 1000000000,
            "min_text_len": 1,
            "max_text_len": 1000000000,
            
            # 其他参数
            "mixed_precision": False,
            "save_step": 10000,
            "print_step": 100,
            "print_eval": True,
            "save_checkpoints": True
        }
        
        # 写入新配置文件
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(clean_config, f, indent=4, ensure_ascii=False)
        
        logger.info(f"成功创建新的YourTTS配置文件: {config_path}")
        return True
    
    except Exception as e:
        logger.error(f"修复YourTTS配置文件失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    fix_your_tts_config() 