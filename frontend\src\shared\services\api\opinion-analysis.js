/**
 * 舆情分析API服务
 */

import { apiClientObject } from './index.js';

const API_BASE = '/api/v1/opinion';

// 简报管理API
export const opinionReportApi = {
  // 获取简报任务列表
  getReports: (params = {}) => {
    return apiClientObject.get(`${API_BASE}/reports`, params);
  },

  // 创建简报任务
  createReport: (data) => {
    return apiClientObject.post(`${API_BASE}/reports`, data);
  },

  // 获取简报任务详情
  getReport: (reportId) => {
    return apiClientObject.get(`${API_BASE}/reports/${reportId}`);
  },

  // 更新简报任务
  updateReport: (reportId, data) => {
    return apiClientObject.put(`${API_BASE}/reports/${reportId}`, data);
  },

  // 删除简报任务
  deleteReport: (reportId) => {
    return apiClientObject.delete(`${API_BASE}/reports/${reportId}`);
  },

  // 手动生成简报
  generateReport: (reportId) => {
    return apiClientObject.post(`${API_BASE}/reports/${reportId}/generate`);
  },

  // 获取简报实例列表
  getReportInstances: (reportId, params = {}) => {
    return apiClientObject.get(`${API_BASE}/reports/${reportId}/instances`, params);
  }
};

// 检索方案管理API
export const opinionSearchSchemeApi = {
  // 获取检索方案列表
  getSearchSchemes: (params = {}) => {
    return apiClientObject.get(`${API_BASE}/search-schemes`, params);
  },

  // 创建检索方案
  createSearchScheme: (data) => {
    return apiClientObject.post(`${API_BASE}/search-schemes`, data);
  },

  // 删除检索方案
  deleteSearchScheme: (schemeId) => {
    return apiClientObject.delete(`${API_BASE}/search-schemes/${schemeId}`);
  }
};

// 预警管理API
export const opinionAlertApi = {
  // 创建预警配置
  createAlert: (data) => {
    return apiClientObject.post(`${API_BASE}/alerts`, data);
  },

  // 获取最近预警记录
  getRecentAlerts: (params = {}) => {
    return apiClientObject.get(`${API_BASE}/alerts/recent`, params);
  }
};

// 舆情检索API
export const opinionSearchApi = {
  // 执行舆情检索
  searchOpinions: (data) => {
    return apiClientObject.post(`${API_BASE}/search`, data);
  },

  // 获取检索结果分析
  getSearchAnalysis: (params) => {
    return apiClientObject.get(`${API_BASE}/search/analysis`, params);
  }
};

// 知识图谱API
export const opinionKnowledgeGraphApi = {
  // 创建知识图谱
  createKnowledgeGraph: (data) => {
    return apiClientObject.post(`${API_BASE}/knowledge-graph`, data);
  },

  // 获取知识图谱列表
  getKnowledgeGraphs: () => {
    return apiClientObject.get(`${API_BASE}/knowledge-graphs`);
  }
};

// 统计API
export const opinionStatisticsApi = {
  // 获取统计信息
  getStatistics: () => {
    return apiClientObject.get(`${API_BASE}/statistics`);
  }
};

// 统一导出
export default {
  reports: opinionReportApi,
  searchSchemes: opinionSearchSchemeApi,
  alerts: opinionAlertApi,
  search: opinionSearchApi,
  knowledgeGraph: opinionKnowledgeGraphApi,
  statistics: opinionStatisticsApi
};
