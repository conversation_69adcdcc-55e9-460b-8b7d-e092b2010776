"""
图像生成和变换API
"""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import Optional, Dict, Any
import logging
import asyncio
import base64
import io
from PIL import Image, ImageFilter, ImageEnhance
import requests
from urllib.parse import urlparse

logger = logging.getLogger(__name__)

router = APIRouter()

class ImageTransformRequest(BaseModel):
    """图像变换请求"""
    base_image_url: str
    facial_expression: str = "friendly"  # friendly, professional, gentle, energetic
    clothing_style: str = "business"     # business, casual, uniform, fashion
    background: str = "office"           # office, white, home, tech
    gender: str = "female"               # female, male

class ImageTransformResponse(BaseModel):
    """图像变换响应"""
    success: bool
    transformed_image_url: str
    original_image_url: str
    applied_effects: Dict[str, Any]
    processing_time: float

# 表情效果映射
FACIAL_EXPRESSION_EFFECTS = {
    "friendly": {
        "brightness": 1.1,
        "contrast": 1.05,
        "saturation": 1.1,
        "warmth": 1.05
    },
    "professional": {
        "brightness": 0.95,
        "contrast": 1.15,
        "saturation": 0.9,
        "warmth": 0.95
    },
    "gentle": {
        "brightness": 1.05,
        "contrast": 0.95,
        "saturation": 1.05,
        "warmth": 1.1
    },
    "energetic": {
        "brightness": 1.15,
        "contrast": 1.1,
        "saturation": 1.2,
        "warmth": 1.05
    }
}

# 服装风格效果映射
CLOTHING_STYLE_EFFECTS = {
    "business": {
        "hue_shift": 0,
        "saturation_boost": 0.9,
        "contrast_boost": 1.05
    },
    "casual": {
        "hue_shift": 10,
        "saturation_boost": 1.1,
        "contrast_boost": 0.95
    },
    "uniform": {
        "hue_shift": -10,
        "saturation_boost": 0.8,
        "contrast_boost": 1.1
    },
    "fashion": {
        "hue_shift": 5,
        "saturation_boost": 1.3,
        "contrast_boost": 1.05
    }
}

# 背景效果映射
BACKGROUND_EFFECTS = {
    "office": {
        "background_color": (248, 249, 250),
        "vignette": 0.1
    },
    "white": {
        "background_color": (255, 255, 255),
        "vignette": 0.05
    },
    "home": {
        "background_color": (254, 247, 240),
        "vignette": 0.15
    },
    "tech": {
        "background_color": (26, 26, 46),
        "vignette": 0.2
    }
}

async def download_image(url: str) -> Image.Image:
    """下载图片"""
    try:
        response = requests.get(url, timeout=10)
        response.raise_for_status()
        return Image.open(io.BytesIO(response.content)).convert('RGB')
    except Exception as e:
        logger.error(f"下载图片失败: {url}, 错误: {e}")
        raise HTTPException(status_code=400, detail=f"无法下载图片: {str(e)}")

def apply_facial_expression_effects(image: Image.Image, expression: str) -> Image.Image:
    """应用面部表情效果"""
    effects = FACIAL_EXPRESSION_EFFECTS.get(expression, FACIAL_EXPRESSION_EFFECTS["friendly"])
    
    # 调整亮度
    enhancer = ImageEnhance.Brightness(image)
    image = enhancer.enhance(effects["brightness"])
    
    # 调整对比度
    enhancer = ImageEnhance.Contrast(image)
    image = enhancer.enhance(effects["contrast"])
    
    # 调整饱和度
    enhancer = ImageEnhance.Color(image)
    image = enhancer.enhance(effects["saturation"])
    
    return image

def apply_clothing_style_effects(image: Image.Image, style: str) -> Image.Image:
    """应用服装风格效果"""
    effects = CLOTHING_STYLE_EFFECTS.get(style, CLOTHING_STYLE_EFFECTS["business"])
    
    # 调整饱和度
    enhancer = ImageEnhance.Color(image)
    image = enhancer.enhance(effects["saturation_boost"])
    
    # 调整对比度
    enhancer = ImageEnhance.Contrast(image)
    image = enhancer.enhance(effects["contrast_boost"])
    
    # TODO: 实现色调偏移（需要更复杂的图像处理）
    
    return image

def apply_background_effects(image: Image.Image, background: str) -> Image.Image:
    """应用背景效果"""
    effects = BACKGROUND_EFFECTS.get(background, BACKGROUND_EFFECTS["office"])
    
    # 创建背景色
    width, height = image.size
    background_img = Image.new('RGB', (width, height), effects["background_color"])
    
    # 简单的背景替换（实际应用中需要更复杂的背景分离算法）
    # 这里只是添加一个轻微的背景色调
    blended = Image.blend(background_img, image, 0.9)
    
    # 添加暗角效果
    if effects["vignette"] > 0:
        # 创建暗角遮罩
        vignette = Image.new('L', (width, height), 255)
        # TODO: 实现真正的暗角效果
    
    return blended

def image_to_base64(image: Image.Image) -> str:
    """将图片转换为base64"""
    buffer = io.BytesIO()
    image.save(buffer, format='JPEG', quality=85)
    img_str = base64.b64encode(buffer.getvalue()).decode()
    return f"data:image/jpeg;base64,{img_str}"

@router.post("/transform", response_model=ImageTransformResponse)
async def transform_image(request: ImageTransformRequest):
    """
    变换图像
    应用面部表情、服装风格、背景环境的变化
    """
    import time
    start_time = time.time()
    
    try:
        logger.info(f"开始图像变换: {request.base_image_url}")
        
        # 下载原始图片
        original_image = await download_image(request.base_image_url)
        
        # 应用面部表情效果
        transformed_image = apply_facial_expression_effects(
            original_image, 
            request.facial_expression
        )
        
        # 应用服装风格效果
        transformed_image = apply_clothing_style_effects(
            transformed_image, 
            request.clothing_style
        )
        
        # 应用背景效果
        transformed_image = apply_background_effects(
            transformed_image, 
            request.background
        )
        
        # 转换为base64
        transformed_image_url = image_to_base64(transformed_image)
        
        processing_time = time.time() - start_time
        
        applied_effects = {
            "facial_expression": request.facial_expression,
            "clothing_style": request.clothing_style,
            "background": request.background,
            "effects_applied": {
                "facial": FACIAL_EXPRESSION_EFFECTS.get(request.facial_expression, {}),
                "clothing": CLOTHING_STYLE_EFFECTS.get(request.clothing_style, {}),
                "background": BACKGROUND_EFFECTS.get(request.background, {})
            }
        }
        
        logger.info(f"图像变换完成，耗时: {processing_time:.2f}秒")
        
        return ImageTransformResponse(
            success=True,
            transformed_image_url=transformed_image_url,
            original_image_url=request.base_image_url,
            applied_effects=applied_effects,
            processing_time=processing_time
        )
        
    except Exception as e:
        logger.error(f"图像变换失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"图像变换失败: {str(e)}")

@router.get("/effects/preview")
async def get_effects_preview():
    """
    获取效果预览信息
    """
    return {
        "facial_expressions": {
            "friendly": {
                "name": "友善微笑",
                "description": "明亮温暖，增加亲和力",
                "effects": FACIAL_EXPRESSION_EFFECTS["friendly"]
            },
            "professional": {
                "name": "专业严肃", 
                "description": "沉稳可靠，增强专业感",
                "effects": FACIAL_EXPRESSION_EFFECTS["professional"]
            },
            "gentle": {
                "name": "温和亲切",
                "description": "柔和舒适，增加亲近感", 
                "effects": FACIAL_EXPRESSION_EFFECTS["gentle"]
            },
            "energetic": {
                "name": "活力开朗",
                "description": "充满活力，增加感染力",
                "effects": FACIAL_EXPRESSION_EFFECTS["energetic"]
            }
        },
        "clothing_styles": {
            "business": {
                "name": "商务正装",
                "description": "正式专业的商务形象"
            },
            "casual": {
                "name": "休闲装",
                "description": "轻松自然的日常形象"
            },
            "uniform": {
                "name": "制服",
                "description": "统一规范的职业形象"
            },
            "fashion": {
                "name": "时尚装",
                "description": "时尚前卫的个性形象"
            }
        },
        "backgrounds": {
            "office": {
                "name": "办公室",
                "description": "专业的办公环境"
            },
            "white": {
                "name": "简约白色",
                "description": "简洁干净的纯色背景"
            },
            "home": {
                "name": "温馨家居",
                "description": "温暖舒适的家庭环境"
            },
            "tech": {
                "name": "科技感",
                "description": "现代科技的未来感背景"
            }
        }
    }
