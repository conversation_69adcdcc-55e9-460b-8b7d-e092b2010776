"""
舆情分析服务
"""

import asyncio
import json
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session
from sqlalchemy import desc, and_, or_
from ..core.sqlalchemy_db import SessionLocal
from ..models.opinion_analysis import (
    OpinionReport, OpinionReportInstance, OpinionSearchScheme,
    OpinionAlert, OpinionAlertRecord, OpinionSearchResult, OpinionKnowledgeGraph
)
from .ai_opinion_service import AIOpinionService
from .opinion_crawler_service import OpinionCrawlerService
from .websocket_service import websocket_service
import random
import uuid


class SQLAlchemyDBManager:
    """SQLAlchemy数据库管理器"""

    def get_session(self):
        """获取数据库会话的上下文管理器"""
        return SessionLocal()


class OpinionAnalysisService:
    """舆情分析服务类"""
    
    def __init__(self):
        self.db_manager = SQLAlchemyDBManager()
        self.ai_service = AIOpinionService()
        self.crawler_service = OpinionCrawlerService()
    
    async def get_reports(self, user_id: int, page: int = 1, page_size: int = 20, status: Optional[str] = None):
        """获取简报任务列表"""
        with self.db_manager.get_session() as db:
            query = db.query(OpinionReport).filter(OpinionReport.user_id == user_id)
            
            if status:
                query = query.filter(OpinionReport.status == status)
            
            total = query.count()
            items = query.order_by(desc(OpinionReport.created_at))\
                        .offset((page - 1) * page_size)\
                        .limit(page_size)\
                        .all()
            
            return {
                "items": [self._format_report(item) for item in items],
                "total": total
            }
    
    async def create_report(self, user_id: int, **kwargs):
        """创建简报任务"""
        with self.db_manager.get_session() as db:
            # 计算下次执行时间
            next_execution = self._calculate_next_execution(
                kwargs.get('cycle', 'weekly'),
                kwargs.get('execution_time', '09:00')
            )
            
            report = OpinionReport(
                user_id=user_id,
                next_execution=next_execution,
                **kwargs
            )
            
            db.add(report)
            db.commit()
            db.refresh(report)
            
            return self._format_report(report)
    
    async def get_report(self, report_id: int, user_id: int):
        """获取简报任务详情"""
        with self.db_manager.get_session() as db:
            report = db.query(OpinionReport).filter(
                and_(OpinionReport.id == report_id, OpinionReport.user_id == user_id)
            ).first()
            
            if report:
                return self._format_report(report)
            return None
    
    async def update_report(self, report_id: int, user_id: int, **kwargs):
        """更新简报任务"""
        with self.db_manager.get_session() as db:
            report = db.query(OpinionReport).filter(
                and_(OpinionReport.id == report_id, OpinionReport.user_id == user_id)
            ).first()
            
            if not report:
                raise ValueError("简报任务不存在")
            
            # 更新字段
            for key, value in kwargs.items():
                if hasattr(report, key) and value is not None:
                    setattr(report, key, value)
            
            # 如果更新了周期或执行时间，重新计算下次执行时间
            if 'cycle' in kwargs or 'execution_time' in kwargs:
                report.next_execution = self._calculate_next_execution(
                    report.cycle, report.execution_time
                )
            
            db.commit()
            db.refresh(report)
            
            return self._format_report(report)
    
    async def delete_report(self, report_id: int, user_id: int):
        """删除简报任务"""
        with self.db_manager.get_session() as db:
            report = db.query(OpinionReport).filter(
                and_(OpinionReport.id == report_id, OpinionReport.user_id == user_id)
            ).first()
            
            if not report:
                raise ValueError("简报任务不存在")
            
            db.delete(report)
            db.commit()
    
    def generate_report_instance(self, report_id: int, user_id: int):
        """生成简报实例（使用Celery异步任务）"""
        try:
            # 验证简报任务存在
            with self.db_manager.get_session() as db:
                report = db.query(OpinionReport).filter(
                    and_(OpinionReport.id == report_id, OpinionReport.user_id == user_id)
                ).first()

                if not report:
                    raise ValueError("简报任务不存在")

            # 导入并启动Celery任务
            from app.tasks.opinion_analysis_tasks import generate_opinion_report
            task = generate_opinion_report.delay(report_id, user_id)

            return {
                "success": True,
                "task_id": task.id,
                "message": "简报生成任务已启动",
                "status": "processing"
            }

        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    async def get_report_instances(self, report_id: int, user_id: int, page: int = 1, page_size: int = 10):
        """获取简报实例列表"""
        with self.db_manager.get_session() as db:
            # 验证报告所有权
            report = db.query(OpinionReport).filter(
                and_(OpinionReport.id == report_id, OpinionReport.user_id == user_id)
            ).first()
            
            if not report:
                raise ValueError("简报任务不存在")
            
            query = db.query(OpinionReportInstance).filter(
                OpinionReportInstance.report_id == report_id
            )
            
            total = query.count()
            items = query.order_by(desc(OpinionReportInstance.created_at))\
                        .offset((page - 1) * page_size)\
                        .limit(page_size)\
                        .all()
            
            return {
                "items": [self._format_report_instance(item) for item in items],
                "total": total
            }
    
    async def get_search_schemes(self, user_id: int, page: int = 1, page_size: int = 20):
        """获取检索方案列表"""
        with self.db_manager.get_session() as db:
            query = db.query(OpinionSearchScheme).filter(OpinionSearchScheme.user_id == user_id)
            
            total = query.count()
            items = query.order_by(desc(OpinionSearchScheme.created_at))\
                        .offset((page - 1) * page_size)\
                        .limit(page_size)\
                        .all()
            
            return {
                "items": [self._format_search_scheme(item) for item in items],
                "total": total
            }
    
    async def create_search_scheme(self, user_id: int, **kwargs):
        """创建检索方案"""
        with self.db_manager.get_session() as db:
            scheme = OpinionSearchScheme(
                user_id=user_id,
                **kwargs
            )
            
            db.add(scheme)
            db.commit()
            db.refresh(scheme)
            
            return self._format_search_scheme(scheme)
    
    async def delete_search_scheme(self, scheme_id: int, user_id: int):
        """删除检索方案"""
        with self.db_manager.get_session() as db:
            scheme = db.query(OpinionSearchScheme).filter(
                and_(OpinionSearchScheme.id == scheme_id, OpinionSearchScheme.user_id == user_id)
            ).first()
            
            if not scheme:
                raise ValueError("检索方案不存在")
            
            db.delete(scheme)
            db.commit()
    
    async def create_alert(self, user_id: int, **kwargs):
        """创建预警配置"""
        with self.db_manager.get_session() as db:
            # 验证检索方案所有权
            scheme = db.query(OpinionSearchScheme).filter(
                and_(
                    OpinionSearchScheme.id == kwargs['search_scheme_id'],
                    OpinionSearchScheme.user_id == user_id
                )
            ).first()
            
            if not scheme:
                raise ValueError("检索方案不存在")
            
            # 计算下次检查时间
            next_check = self._calculate_next_check(
                kwargs.get('trigger_type', 'time'),
                kwargs.get('frequency', 'daily'),
                kwargs.get('check_time', '09:00')
            )
            
            alert = OpinionAlert(
                next_check=next_check,
                **kwargs
            )
            
            db.add(alert)
            db.commit()
            db.refresh(alert)
            
            return self._format_alert(alert)
    
    async def get_recent_alerts(self, user_id: int, limit: int = 10):
        """获取最近预警记录"""
        with self.db_manager.get_session() as db:
            # 通过关联查询获取用户的预警记录
            alerts = db.query(OpinionAlertRecord)\
                       .join(OpinionAlert)\
                       .join(OpinionSearchScheme)\
                       .filter(OpinionSearchScheme.user_id == user_id)\
                       .order_by(desc(OpinionAlertRecord.triggered_at))\
                       .limit(limit)\
                       .all()
            
            return [self._format_alert_record(alert) for alert in alerts]
    
    async def search_opinions(self, user_id: int, **kwargs):
        """执行舆情检索"""
        try:
            query = kwargs.get('query', '')
            sources = kwargs.get('sources', ['news', 'social'])
            regions = kwargs.get('regions', ['china'])
            language = kwargs.get('language', 'zh')
            date_range = kwargs.get('date_range', {})

            # 发送搜索开始通知
            await websocket_service.send_search_update(str(user_id), {
                'status': 'started',
                'query': query,
                'message': '开始检索舆情数据...'
            })

            # 使用爬虫服务获取真实数据
            all_results = []

            # 爬取新闻数据
            if 'news' in sources:
                news_data = await self.crawler_service.crawl_news_data(
                    keywords=[query],
                    sources=['news', 'rss', 'web'],
                    language=language,
                    max_results=50
                )
                all_results.extend(news_data)

            # 爬取社交媒体数据
            if 'social' in sources:
                social_data = await self.crawler_service.crawl_social_media_data(
                    keywords=[query],
                    platforms=['reddit', 'weibo'],
                    max_results=30
                )
                all_results.extend(social_data)

            # 使用AI进行情感分析
            if all_results:
                texts = [item.get('title', '') + ' ' + item.get('summary', '') for item in all_results]
                sentiment_results = await self.ai_service.analyze_sentiment_batch(texts)

                # 将情感分析结果合并到原数据
                for i, result in enumerate(all_results):
                    if i < len(sentiment_results):
                        sentiment = sentiment_results[i]
                        result['sentiment'] = sentiment.get('sentiment', 'neutral')
                        result['sentiment_score'] = sentiment.get('score', 0.0)
                        result['sentiment_confidence'] = sentiment.get('confidence', 0.0)

            # 生成分析报告
            analysis = await self._generate_real_analysis(all_results, query)

            # 发送搜索完成通知
            await websocket_service.send_search_update(str(user_id), {
                'status': 'completed',
                'query': query,
                'total': len(all_results),
                'message': f'检索完成，共找到 {len(all_results)} 条结果'
            })

            return {
                "results": all_results,
                "total": len(all_results),
                "analysis": analysis
            }

        except Exception as e:
            # 发送错误通知
            await websocket_service.send_search_update(str(user_id), {
                'status': 'error',
                'query': kwargs.get('query', ''),
                'message': f'检索失败: {str(e)}'
            })

            logger.error(f"舆情检索失败: {str(e)}")
            # 返回模拟数据作为备用
            mock_results = self._generate_mock_search_results(kwargs.get('query', ''))
            return {
                "results": mock_results,
                "total": len(mock_results),
                "analysis": self._generate_mock_analysis()
            }
    
    async def get_search_analysis(self, query: str, sources: List[str], date_range: str, user_id: int):
        """获取检索结果分析"""
        # 模拟分析数据
        return self._generate_mock_analysis()
    
    async def create_knowledge_graph(self, user_id: int, name: str, data_source: str, 
                                   keywords: List[str], time_range: str):
        """创建知识图谱"""
        with self.db_manager.get_session() as db:
            # 生成模拟图谱数据
            nodes, edges = self._generate_mock_graph_data(keywords)
            
            graph = OpinionKnowledgeGraph(
                user_id=user_id,
                name=name,
                description=f"基于{data_source}的知识图谱",
                nodes=nodes,
                edges=edges,
                data_source=data_source,
                keywords=keywords,
                node_count=len(nodes),
                edge_count=len(edges),
                time_range_start=datetime.now() - timedelta(days=30),
                time_range_end=datetime.now(),
                generation_time=2.5
            )
            
            db.add(graph)
            db.commit()
            db.refresh(graph)
            
            return self._format_knowledge_graph(graph)
    
    async def get_knowledge_graphs(self, user_id: int):
        """获取知识图谱列表"""
        with self.db_manager.get_session() as db:
            graphs = db.query(OpinionKnowledgeGraph)\
                       .filter(OpinionKnowledgeGraph.user_id == user_id)\
                       .order_by(desc(OpinionKnowledgeGraph.created_at))\
                       .all()
            
            return [self._format_knowledge_graph(graph) for graph in graphs]
    
    async def get_statistics(self, user_id: int):
        """获取统计信息"""
        with self.db_manager.get_session() as db:
            # 统计各种数据
            active_reports = db.query(OpinionReport)\
                              .filter(and_(OpinionReport.user_id == user_id, OpinionReport.status == 'active'))\
                              .count()
            
            total_schemes = db.query(OpinionSearchScheme)\
                             .filter(OpinionSearchScheme.user_id == user_id)\
                             .count()
            
            today_alerts = db.query(OpinionAlertRecord)\
                            .join(OpinionAlert)\
                            .join(OpinionSearchScheme)\
                            .filter(and_(
                                OpinionSearchScheme.user_id == user_id,
                                OpinionAlertRecord.triggered_at >= datetime.now().date()
                            ))\
                            .count()
            
            return {
                "active_reports": active_reports,
                "total_schemes": total_schemes,
                "today_alerts": today_alerts,
                "monthly_reports": random.randint(15, 25)
            }
    
    # 私有辅助方法
    def _calculate_next_execution(self, cycle: str, execution_time: str) -> datetime:
        """计算下次执行时间"""
        now = datetime.now()
        hour, minute = map(int, execution_time.split(':'))
        
        if cycle == 'daily':
            next_time = now.replace(hour=hour, minute=minute, second=0, microsecond=0)
            if next_time <= now:
                next_time += timedelta(days=1)
        elif cycle == 'weekly':
            next_time = now.replace(hour=hour, minute=minute, second=0, microsecond=0)
            days_ahead = 7 - now.weekday()  # 下周一
            if days_ahead <= 0 or (days_ahead == 7 and next_time <= now):
                days_ahead += 7
            next_time += timedelta(days=days_ahead)
        elif cycle == 'monthly':
            if now.month == 12:
                next_time = now.replace(year=now.year + 1, month=1, day=1, hour=hour, minute=minute, second=0, microsecond=0)
            else:
                next_time = now.replace(month=now.month + 1, day=1, hour=hour, minute=minute, second=0, microsecond=0)
        else:
            next_time = now + timedelta(days=1)
        
        return next_time
    
    def _calculate_next_check(self, trigger_type: str, frequency: str, check_time: str) -> datetime:
        """计算下次检查时间"""
        if trigger_type != 'time':
            return None
        
        now = datetime.now()
        hour, minute = map(int, check_time.split(':'))
        
        if frequency == 'hourly':
            return now + timedelta(hours=1)
        elif frequency == 'daily':
            next_time = now.replace(hour=hour, minute=minute, second=0, microsecond=0)
            if next_time <= now:
                next_time += timedelta(days=1)
            return next_time
        elif frequency == 'weekly':
            next_time = now.replace(hour=hour, minute=minute, second=0, microsecond=0)
            days_ahead = 7 - now.weekday()
            if days_ahead <= 0:
                days_ahead += 7
            return next_time + timedelta(days=days_ahead)
        
        return now + timedelta(days=1)
    
    async def _generate_real_report(self, instance_id: int, report: OpinionReport):
        """使用AI生成真实简报"""
        try:
            with self.db_manager.get_session() as db:
                instance = db.query(OpinionReportInstance).filter(
                    OpinionReportInstance.id == instance_id
                ).first()

                if not instance:
                    return

                # 更新进度：开始收集数据
                instance.generation_progress = 10.0
                db.commit()

                # 1. 收集舆情数据
                keywords = report.keywords or ['热点', '政策', '经济']
                sources = report.sources or ['news', 'social']

                raw_data = []

                # 爬取新闻数据
                if 'news' in sources:
                    news_data = await self.crawler_service.crawl_news_data(
                        keywords=keywords,
                        sources=['news', 'rss'],
                        language=report.language,
                        max_results=100
                    )
                    raw_data.extend(news_data)

                # 更新进度：数据收集完成
                instance.generation_progress = 40.0
                db.commit()

                # 2. AI分析和简报生成
                if raw_data:
                    report_content = await self.ai_service.generate_opinion_report(
                        title=instance.title,
                        keywords=keywords,
                        raw_data=raw_data,
                        template_type=report.template_type,
                        language=report.language
                    )

                    # 更新进度：AI分析完成
                    instance.generation_progress = 80.0
                    db.commit()

                    # 3. 保存生成结果
                    instance.content = report_content.get('full_content', '')
                    instance.summary = report_content.get('summary', '')
                    instance.total_items = len(raw_data)
                    instance.data_sources_count = len(set(item.get('source', '') for item in raw_data))
                else:
                    # 没有数据时的备用内容
                    instance.content = "由于数据源限制，本期简报暂时无法生成详细内容。"
                    instance.summary = "数据收集中遇到问题，请稍后重试。"
                    instance.total_items = 0
                    instance.data_sources_count = 0

                # 完成生成
                instance.generation_status = "completed"
                instance.generation_progress = 100.0
                db.commit()

        except Exception as e:
            logger.error(f"AI简报生成失败: {str(e)}")

            # 更新失败状态
            with self.db_manager.get_session() as db:
                instance = db.query(OpinionReportInstance).filter(
                    OpinionReportInstance.id == instance_id
                ).first()

                if instance:
                    instance.generation_status = "failed"
                    instance.error_message = str(e)
                    instance.content = "简报生成失败，请稍后重试。"
                    db.commit()

    async def _generate_real_analysis(self, results: List[Dict], query: str) -> Dict[str, Any]:
        """生成真实的分析数据"""
        try:
            if not results:
                return self._generate_mock_analysis()

            # 统计基础数据
            total = len(results)
            sources = list(set(item.get('source', '') for item in results))

            # 情感分析统计
            sentiment_stats = {'positive': 0, 'neutral': 0, 'negative': 0}
            for item in results:
                sentiment = item.get('sentiment', 'neutral')
                if sentiment in sentiment_stats:
                    sentiment_stats[sentiment] += 1

            # 地域分析
            region_stats = {}
            for item in results:
                region = item.get('region', '未知')
                region_stats[region] = region_stats.get(region, 0) + 1

            # 时间趋势分析
            time_stats = {}
            for item in results:
                publish_time = item.get('publish_time', '')
                if publish_time:
                    try:
                        date = datetime.fromisoformat(publish_time.replace('Z', '+00:00')).date()
                        date_str = date.strftime('%Y-%m-%d')
                        time_stats[date_str] = time_stats.get(date_str, 0) + 1
                    except:
                        pass

            # 使用AI提取热点话题
            texts = [item.get('title', '') + ' ' + item.get('summary', '') for item in results[:20]]
            hot_topics = await self.ai_service.extract_hot_topics(texts, top_k=5)

            return {
                'total': total,
                'sites': len(sources),
                'time_span': len(time_stats),
                'avg_sentiment': sum(sentiment_stats.values()) / total if total > 0 else 0,
                'region_distribution': region_stats,
                'sentiment_distribution': sentiment_stats,
                'trend_data': [
                    {'date': date, 'count': count}
                    for date, count in sorted(time_stats.items())
                ],
                'source_distribution': {source: sum(1 for item in results if item.get('source') == source) for source in sources},
                'hot_topics': hot_topics
            }

        except Exception as e:
            logger.error(f"生成分析数据失败: {str(e)}")
            return self._generate_mock_analysis()
    
    def _format_report(self, report: OpinionReport) -> Dict[str, Any]:
        """格式化简报任务数据"""
        return {
            "id": report.id,
            "name": report.name,
            "description": report.description,
            "template_type": report.template_type,
            "cycle": report.cycle,
            "sources": report.sources or [],
            "keywords": report.keywords or [],
            "regions": report.regions or [],
            "language": report.language,
            "execution_time": report.execution_time,
            "next_execution": report.next_execution.isoformat() if report.next_execution else None,
            "last_execution": report.last_execution.isoformat() if report.last_execution else None,
            "recipients": report.recipients or [],
            "email_enabled": report.email_enabled,
            "status": report.status,
            "execution_count": report.execution_count,
            "created_at": report.created_at.isoformat(),
            "updated_at": report.updated_at.isoformat() if report.updated_at else None
        }
    
    def _format_report_instance(self, instance: OpinionReportInstance) -> Dict[str, Any]:
        """格式化简报实例数据"""
        return {
            "id": instance.id,
            "report_id": instance.report_id,
            "title": instance.title,
            "summary": instance.summary,
            "total_items": instance.total_items,
            "data_sources_count": instance.data_sources_count,
            "generation_status": instance.generation_status,
            "generation_progress": instance.generation_progress,
            "email_sent": instance.email_sent,
            "created_at": instance.created_at.isoformat()
        }
    
    def _format_search_scheme(self, scheme: OpinionSearchScheme) -> Dict[str, Any]:
        """格式化检索方案数据"""
        return {
            "id": scheme.id,
            "name": scheme.name,
            "description": scheme.description,
            "keywords": scheme.keywords or [],
            "sources": scheme.sources or [],
            "regions": scheme.regions or [],
            "language": scheme.language,
            "status": scheme.status,
            "last_execution": scheme.last_execution.isoformat() if scheme.last_execution else None,
            "execution_count": scheme.execution_count,
            "last_result_count": scheme.last_result_count,
            "created_at": scheme.created_at.isoformat()
        }
    
    def _format_alert(self, alert: OpinionAlert) -> Dict[str, Any]:
        """格式化预警配置数据"""
        return {
            "id": alert.id,
            "search_scheme_id": alert.search_scheme_id,
            "enabled": alert.enabled,
            "trigger_type": alert.trigger_type,
            "frequency": alert.frequency,
            "alert_level": alert.alert_level,
            "recipients": alert.recipients or [],
            "alert_count": alert.alert_count,
            "created_at": alert.created_at.isoformat()
        }
    
    def _format_alert_record(self, record: OpinionAlertRecord) -> Dict[str, Any]:
        """格式化预警记录数据"""
        return {
            "id": record.id,
            "title": record.title,
            "description": record.description,
            "alert_level": record.alert_level,
            "status": record.status,
            "triggered_at": record.triggered_at.isoformat()
        }
    
    def _format_knowledge_graph(self, graph: OpinionKnowledgeGraph) -> Dict[str, Any]:
        """格式化知识图谱数据"""
        return {
            "id": graph.id,
            "name": graph.name,
            "description": graph.description,
            "nodes": graph.nodes,
            "edges": graph.edges,
            "node_count": graph.node_count,
            "edge_count": graph.edge_count,
            "created_at": graph.created_at.isoformat()
        }
    
    def _generate_mock_search_results(self, query: str) -> List[Dict[str, Any]]:
        """生成模拟检索结果"""
        results = []
        for i in range(20):
            results.append({
                "id": i + 1,
                "title": f"关于{query}的相关报道 {i + 1}",
                "title_cn": f"关于{query}的相关报道 {i + 1}",
                "url": f"https://example.com/news/{i + 1}",
                "source": random.choice(["新华社", "人民日报", "央视新闻", "环球时报"]),
                "region": random.choice(["中国", "美国", "欧洲", "亚洲"]),
                "language": "中文",
                "publish_time": (datetime.now() - timedelta(days=random.randint(1, 30))).isoformat(),
                "sentiment": random.choice(["positive", "neutral", "negative"]),
                "relevance_score": round(random.uniform(0.6, 1.0), 2),
                "views": random.randint(100, 10000),
                "summary": f"这是关于{query}的新闻摘要内容..."
            })
        return results
    
    def _generate_mock_analysis(self) -> Dict[str, Any]:
        """生成模拟分析数据"""
        return {
            "total": 1250,
            "sites": 45,
            "time_span": 30,
            "avg_sentiment": 6.8,
            "region_distribution": {
                "中国": 45,
                "美国": 25,
                "欧洲": 20,
                "其他": 10
            },
            "sentiment_distribution": {
                "positive": 35,
                "neutral": 45,
                "negative": 20
            },
            "trend_data": [
                {"date": "2024-01-01", "count": 45},
                {"date": "2024-01-02", "count": 52},
                {"date": "2024-01-03", "count": 38}
            ]
        }
    
    def _generate_mock_graph_data(self, keywords: List[str]):
        """生成模拟图谱数据"""
        nodes = []
        edges = []
        
        # 生成节点
        for i, keyword in enumerate(keywords[:5]):
            nodes.append({
                "id": str(i + 1),
                "label": keyword,
                "type": "concept",
                "size": random.randint(20, 50),
                "relevance": round(random.uniform(0.6, 1.0), 2)
            })
        
        # 添加一些相关节点
        related_nodes = ["政策", "经济", "社会", "国际", "技术"]
        for i, node in enumerate(related_nodes):
            nodes.append({
                "id": str(len(keywords) + i + 1),
                "label": node,
                "type": "topic",
                "size": random.randint(15, 35),
                "relevance": round(random.uniform(0.4, 0.8), 2)
            })
        
        # 生成边
        for i in range(len(nodes)):
            for j in range(i + 1, min(i + 3, len(nodes))):
                edges.append({
                    "source": nodes[i]["id"],
                    "target": nodes[j]["id"],
                    "weight": round(random.uniform(0.3, 1.0), 2),
                    "type": random.choice(["causal", "temporal", "semantic"])
                })
        
        return nodes, edges
