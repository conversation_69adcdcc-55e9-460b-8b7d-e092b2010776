#!/usr/bin/env python3
"""
测试独立的 Celery 配置
"""
import os
import sys
import time
from pathlib import Path
from dotenv import load_dotenv

# 添加后端路径
backend_dir = Path(__file__).parent.parent
sys.path.append(str(backend_dir))

# 加载环境变量
env_path = backend_dir / ".env"
load_dotenv(env_path)

def test_standalone_celery():
    """测试独立的 Celery 配置"""
    print("🧪 测试独立的 Celery 配置")
    print("=" * 50)
    
    try:
        from celery import Celery
        
        # 创建一个完全独立的 Celery 应用
        app = Celery(
            'standalone_test',
            broker='redis://localhost:6379/0',
            backend='redis://localhost:6379/0'
        )
        
        # 配置
        app.conf.update(
            task_serializer='json',
            accept_content=['json'],
            result_serializer='json',
            timezone='UTC',
            enable_utc=True,
            task_ignore_result=False,
            result_expires=3600,
        )
        
        print(f"✅ 独立 Celery 应用创建成功")
        print(f"📊 Broker: {app.conf.broker_url}")
        print(f"📊 Backend: {app.conf.result_backend}")
        
        # 创建简单任务
        @app.task
        def standalone_add(x, y):
            return x + y
        
        @app.task
        def standalone_hello(name):
            return f"Hello, {name}!"
        
        print(f"✅ 独立任务创建成功")
        
        # 测试任务提交
        print(f"\n🧮 测试独立加法任务...")
        result1 = standalone_add.delay(10, 20)
        print(f"📊 任务ID: {result1.id}")
        print(f"📊 状态: {result1.status}")
        
        print(f"\n👋 测试独立问候任务...")
        result2 = standalone_hello.delay("Standalone")
        print(f"📊 任务ID: {result2.id}")
        print(f"📊 状态: {result2.status}")
        
        # 等待结果
        print(f"\n⏳ 等待结果...")
        try:
            add_result = result1.get(timeout=5)
            hello_result = result2.get(timeout=5)
            
            print(f"✅ 加法结果: 10 + 20 = {add_result}")
            print(f"✅ 问候结果: {hello_result}")
            return True
            
        except Exception as e:
            print(f"❌ 获取结果失败: {e}")
            print(f"📊 加法状态: {result1.status}")
            print(f"📊 问候状态: {result2.status}")
            return False
        
    except Exception as e:
        print(f"❌ 独立 Celery 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_redis_connection():
    """测试 Redis 连接"""
    print(f"\n🔍 测试 Redis 连接")
    print("=" * 50)
    
    try:
        import redis
        
        r = redis.Redis(host='localhost', port=6379, db=0)
        r.ping()
        print(f"✅ Redis 连接正常")
        
        # 测试基本操作
        test_key = "celery-standalone-test"
        r.set(test_key, "test_value")
        value = r.get(test_key)
        r.delete(test_key)
        
        if value and value.decode() == "test_value":
            print(f"✅ Redis 读写正常")
            return True
        else:
            print(f"❌ Redis 读写失败")
            return False
        
    except Exception as e:
        print(f"❌ Redis 连接失败: {e}")
        return False

def check_environment():
    """检查环境"""
    print(f"\n🔍 检查环境")
    print("=" * 50)
    
    try:
        import celery
        import redis
        import billiard
        
        print(f"✅ Celery 版本: {celery.__version__}")
        print(f"✅ Redis 版本: {redis.__version__}")
        print(f"✅ Billiard 版本: {billiard.__version__}")
        
        # 检查 Python 版本
        print(f"✅ Python 版本: {sys.version}")
        
        # 检查操作系统
        import platform
        print(f"✅ 操作系统: {platform.system()} {platform.release()}")
        
        return True
        
    except Exception as e:
        print(f"❌ 环境检查失败: {e}")
        return False

def main():
    """主函数"""
    print("🧪 独立 Celery 测试")
    print("=" * 60)
    
    # 1. 检查环境
    env_ok = check_environment()
    
    # 2. 测试 Redis 连接
    redis_ok = test_redis_connection()
    
    # 3. 测试独立 Celery
    celery_ok = False
    if env_ok and redis_ok:
        celery_ok = test_standalone_celery()
    
    # 总结
    print(f"\n📊 独立测试总结:")
    print(f"{'✅' if env_ok else '❌'} 环境检查: {'正常' if env_ok else '异常'}")
    print(f"{'✅' if redis_ok else '❌'} Redis 连接: {'正常' if redis_ok else '异常'}")
    print(f"{'✅' if celery_ok else '❌'} 独立 Celery: {'成功' if celery_ok else '失败'}")
    
    if celery_ok:
        print(f"\n🎉 独立 Celery 测试成功！")
        print(f"💡 问题在于我们的 Celery 配置")
        print(f"💡 需要简化或重构 celery_app.py")
    elif redis_ok:
        print(f"\n✅ Redis 正常")
        print(f"❌ 但 Celery 有问题")
        print(f"💡 可能的问题:")
        print(f"   1. Celery 版本兼容性")
        print(f"   2. 配置冲突")
        print(f"   3. 环境变量问题")
    else:
        print(f"\n❌ 基础环境有问题")

if __name__ == "__main__":
    main()
