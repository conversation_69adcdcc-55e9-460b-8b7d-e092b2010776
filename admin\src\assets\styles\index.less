/* 全局样式文件 */

/* 清除默认样式 */
* {
  box-sizing: border-box;
}

/* 常用工具类 */
.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-left {
  text-align: left;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex-around {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-1 {
  flex: 1;
}

/* 间距类 */
.m-0 { margin: 0; }
.m-1 { margin: 4px; }
.m-2 { margin: 8px; }
.m-3 { margin: 16px; }
.m-4 { margin: 24px; }
.m-5 { margin: 32px; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 4px; }
.mt-2 { margin-top: 8px; }
.mt-3 { margin-top: 16px; }
.mt-4 { margin-top: 24px; }
.mt-5 { margin-top: 32px; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 4px; }
.mb-2 { margin-bottom: 8px; }
.mb-3 { margin-bottom: 16px; }
.mb-4 { margin-bottom: 24px; }
.mb-5 { margin-bottom: 32px; }

.ml-0 { margin-left: 0; }
.ml-1 { margin-left: 4px; }
.ml-2 { margin-left: 8px; }
.ml-3 { margin-left: 16px; }
.ml-4 { margin-left: 24px; }
.ml-5 { margin-left: 32px; }

.mr-0 { margin-right: 0; }
.mr-1 { margin-right: 4px; }
.mr-2 { margin-right: 8px; }
.mr-3 { margin-right: 16px; }
.mr-4 { margin-right: 24px; }
.mr-5 { margin-right: 32px; }

.p-0 { padding: 0; }
.p-1 { padding: 4px; }
.p-2 { padding: 8px; }
.p-3 { padding: 16px; }
.p-4 { padding: 24px; }
.p-5 { padding: 32px; }

.pt-0 { padding-top: 0; }
.pt-1 { padding-top: 4px; }
.pt-2 { padding-top: 8px; }
.pt-3 { padding-top: 16px; }
.pt-4 { padding-top: 24px; }
.pt-5 { padding-top: 32px; }

.pb-0 { padding-bottom: 0; }
.pb-1 { padding-bottom: 4px; }
.pb-2 { padding-bottom: 8px; }
.pb-3 { padding-bottom: 16px; }
.pb-4 { padding-bottom: 24px; }
.pb-5 { padding-bottom: 32px; }

.pl-0 { padding-left: 0; }
.pl-1 { padding-left: 4px; }
.pl-2 { padding-left: 8px; }
.pl-3 { padding-left: 16px; }
.pl-4 { padding-left: 24px; }
.pl-5 { padding-left: 32px; }

.pr-0 { padding-right: 0; }
.pr-1 { padding-right: 4px; }
.pr-2 { padding-right: 8px; }
.pr-3 { padding-right: 16px; }
.pr-4 { padding-right: 24px; }
.pr-5 { padding-right: 32px; }

/* 文字颜色 */
.text-primary { color: #1890ff; }
.text-success { color: #52c41a; }
.text-warning { color: #faad14; }
.text-danger { color: #f5222d; }
.text-info { color: #13c2c2; }
.text-dark { color: rgba(0, 0, 0, 0.85); }
.text-secondary { color: rgba(0, 0, 0, 0.45); }
.text-light { color: rgba(0, 0, 0, 0.25); }
.text-white { color: #fff; }

/* 背景颜色 */
.bg-primary { background-color: #1890ff; }
.bg-success { background-color: #52c41a; }
.bg-warning { background-color: #faad14; }
.bg-danger { background-color: #f5222d; }
.bg-info { background-color: #13c2c2; }
.bg-white { background-color: #fff; }
.bg-light { background-color: #f0f2f5; }
.bg-dark { background-color: #001529; }

/* 边框 */
.border { border: 1px solid #e8e8e8; }
.border-t { border-top: 1px solid #e8e8e8; }
.border-r { border-right: 1px solid #e8e8e8; }
.border-b { border-bottom: 1px solid #e8e8e8; }
.border-l { border-left: 1px solid #e8e8e8; }

/* 圆角 */
.rounded { border-radius: 4px; }
.rounded-sm { border-radius: 2px; }
.rounded-lg { border-radius: 8px; }
.rounded-full { border-radius: 9999px; }

/* 阴影 */
.shadow { box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15); }
.shadow-sm { box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08); }
.shadow-lg { box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2); }

/* 宽高 */
.w-full { width: 100%; }
.h-full { height: 100%; }
.min-h-full { min-height: 100%; }

/* 定位 */
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.sticky { position: sticky; }

/* 溢出 */
.overflow-hidden { overflow: hidden; }
.overflow-auto { overflow: auto; }
.overflow-scroll { overflow: scroll; }
.overflow-x-auto { overflow-x: auto; }
.overflow-y-auto { overflow-y: auto; }

/* 鼠标样式 */
.cursor-pointer { cursor: pointer; }
.cursor-not-allowed { cursor: not-allowed; }
.cursor-move { cursor: move; }

/* 可见性 */
.hidden { display: none; }
.invisible { visibility: hidden; }
.visible { visibility: visible; }

/* 表格相关 */
.table-container {
  background-color: #fff;
  border-radius: 4px;
  padding: 16px;
  margin-bottom: 20px;
  
  .table-title {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 16px;
  }
  
  .table-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;
    
    .table-filters {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
    }
    
    .table-actions {
      display: flex;
      gap: 8px;
    }
  }
  
  .table-footer {
    margin-top: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

/* 卡片样式 */
.card-container {
  background-color: #fff;
  border-radius: 4px;
  padding: 16px;
  margin-bottom: 20px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  
  .card-title {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    
    .card-icon {
      margin-right: 8px;
      font-size: 18px;
    }
  }
  
  .card-content {
    padding: 8px 0;
  }
}

/* 表单样式 */
.form-container {
  background-color: #fff;
  border-radius: 4px;
  padding: 24px;
  
  .form-title {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 24px;
  }
  
  .form-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 24px;
    
    .ant-btn + .ant-btn {
      margin-left: 8px;
    }
  }
} 