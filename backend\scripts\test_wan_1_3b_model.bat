@echo off
echo ===== WAN 2.1 T2V 1.3B模型测试工具 =====
echo.
echo 该工具将测试WAN 2.1 T2V 1.3B模型是否已正确下载和配置。
echo.

setlocal
cd %~dp0..\..

echo 设置Python环境...
set PYTHONPATH=%cd%

echo.
echo 开始测试WAN 2.1 T2V 1.3B模型...
echo.

python -m backend.scripts.test_wan_1_3b_model %*

if %errorlevel% neq 0 (
    echo.
    echo 测试失败，错误代码: %errorlevel%
    echo 请查看上述信息了解详情。
    echo.
    echo 您可以尝试运行 'backend/scripts/download_wan_1_3b.bat' 下载并配置模型。
    echo.
)

pause 