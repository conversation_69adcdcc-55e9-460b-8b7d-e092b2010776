import { ref, computed } from 'vue';
import { message } from 'ant-design-vue';
import { get, post } from '@/utils/request';

/**
 * 文件上传钩子
 * @param {Object} options - 配置选项
 * @param {string} options.action - 上传地址
 * @param {string} options.accept - 接受的文件类型
 * @param {number} options.maxSize - 最大文件大小(MB)
 * @param {number} options.maxCount - 最大文件数量
 * @param {Function} options.beforeUpload - 上传前钩子
 * @param {Function} options.onSuccess - 上传成功回调
 * @param {Function} options.onError - 上传失败回调
 * @param {Function} options.onChange - 文件列表变化回调
 * @param {Function} options.customRequest - 自定义上传请求
 * @returns {Object} 上传控制对象
 */
export default function useUpload(options = {}) {
  const {
    action = '/upload',
    accept = '',
    maxSize = 10,
    maxCount = 9,
    beforeUpload,
    onSuccess,
    onError,
    onChange,
    customRequest
  } = options;
  
  // 文件列表
  const fileList = ref([]);
  
  // 上传状态
  const uploading = ref(false);
  
  // 已上传数量
  const uploadedCount = computed(() => {
    return fileList.value.filter(file => file.status === 'done').length;
  });
  
  // 上传进度
  const uploadProgress = ref(0);
  
  // 默认的上传前检查
  const defaultBeforeUpload = (file) => {
    // 检查文件大小
    const isLessThanMaxSize = file.size / 1024 / 1024 < maxSize;
    if (!isLessThanMaxSize) {
      message.error(`文件大小不能超过 ${maxSize}MB`);
      return false;
    }
    
    // 检查文件数量限制
    if (fileList.value.length >= maxCount) {
      message.error(`最多只能上传 ${maxCount} 个文件`);
      return false;
    }
    
    return true;
  };
  
  // 处理文件上传前的逻辑
  const handleBeforeUpload = (file, fileList) => {
    // 执行默认检查
    if (!defaultBeforeUpload(file)) {
      return false;
    }
    
    // 执行用户自定义检查
    if (typeof beforeUpload === 'function') {
      return beforeUpload(file, fileList);
    }
    
    return true;
  };
  
  // 处理上传成功
  const handleSuccess = (res, file, fileList) => {
    uploading.value = false;
    uploadProgress.value = 100;
    
    // 更新文件状态
    file.status = 'done';
    file.response = res;
    
    // 执行成功回调
    if (typeof onSuccess === 'function') {
      onSuccess(res, file, fileList);
    }
  };
  
  // 处理上传失败
  const handleError = (err, file, fileList) => {
    uploading.value = false;
    uploadProgress.value = 0;
    
    // 更新文件状态
    file.status = 'error';
    file.error = err;
    
    message.error(`文件 ${file.name} 上传失败`);
    
    // 执行失败回调
    if (typeof onError === 'function') {
      onError(err, file, fileList);
    }
  };
  
  // 处理上传进度
  const handleProgress = (event, file) => {
    file.percent = event.percent;
    uploadProgress.value = Math.floor(event.percent);
  };
  
  // 处理文件列表变化
  const handleChange = ({ file, fileList: newFileList }) => {
    fileList.value = newFileList;
    
    // 当有文件正在上传时设置上传状态
    uploading.value = newFileList.some(file => file.status === 'uploading');
    
    // 执行变化回调
    if (typeof onChange === 'function') {
      onChange({ file, fileList: newFileList });
    }
  };
  
  // 默认的自定义上传请求
  const defaultCustomRequest = async ({ file, onProgress, onSuccess, onError }) => {
    uploading.value = true;
    uploadProgress.value = 0;
    
    try {
      // 创建FormData对象
      const formData = new FormData();
      formData.append('file', file);
      
      // 发送上传请求
      const res = await post(action, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        },
        onUploadProgress: progressEvent => {
          const percent = Math.floor((progressEvent.loaded * 100) / progressEvent.total);
          onProgress({ percent });
        }
      });
      
      // 上传成功
      onSuccess(res);
    } catch (error) {
      // 上传失败
      onError(error);
    }
  };
  
  // 获取上传控制属性
  const uploadProps = computed(() => {
    return {
      action,
      accept,
      fileList: fileList.value,
      beforeUpload: handleBeforeUpload,
      customRequest: customRequest || defaultCustomRequest,
      onChange: handleChange,
      onProgress: handleProgress,
      onSuccess: handleSuccess,
      onError: handleError
    };
  });
  
  // 重置上传状态
  const reset = () => {
    fileList.value = [];
    uploading.value = false;
    uploadProgress.value = 0;
  };
  
  // 手动上传指定文件
  const uploadFile = async (file) => {
    if (!file || uploading.value) {
      return false;
    }
    
    // 检查文件
    if (!handleBeforeUpload(file, fileList.value)) {
      return false;
    }
    
    // 创建上传文件对象
    const uploadFile = {
      uid: Date.now(),
      name: file.name,
      size: file.size,
      type: file.type,
      status: 'uploading',
      percent: 0,
      originFileObj: file
    };
    
    // 添加到文件列表
    fileList.value = [...fileList.value, uploadFile];
    
    // 执行上传
    if (customRequest) {
      customRequest({
        file,
        onProgress: (event) => handleProgress(event, uploadFile),
        onSuccess: (res) => handleSuccess(res, uploadFile, fileList.value),
        onError: (err) => handleError(err, uploadFile, fileList.value)
      });
    } else {
      defaultCustomRequest({
        file,
        onProgress: (event) => handleProgress(event, uploadFile),
        onSuccess: (res) => handleSuccess(res, uploadFile, fileList.value),
        onError: (err) => handleError(err, uploadFile, fileList.value)
      });
    }
    
    return true;
  };
  
  // 删除文件
  const removeFile = (file) => {
    const index = fileList.value.findIndex(item => item.uid === file.uid);
    if (index !== -1) {
      fileList.value.splice(index, 1);
      fileList.value = [...fileList.value];
      
      if (typeof onChange === 'function') {
        onChange({ file, fileList: fileList.value });
      }
      
      return true;
    }
    return false;
  };
  
  return {
    fileList,
    uploading,
    uploadProgress,
    uploadedCount,
    uploadProps,
    uploadFile,
    removeFile,
    reset
  };
} 