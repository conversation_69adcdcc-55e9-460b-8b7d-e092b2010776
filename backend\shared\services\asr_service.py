import os
import torch
import whisper
from typing import Dict, Any, Optional, List
import tempfile
import logging
from pathlib import Path
import time
# 导入繁体转简体的库
try:
    from opencc import OpenCC
    HAS_OPENCC = True
except ImportError:
    HAS_OPENCC = False
    logging.warning("未安装OpenCC，繁体中文将不会自动转换为简体中文。请安装: pip install opencc-python-reimplemented")

logger = logging.getLogger(__name__)

class ASRService:
    """使用OpenAI Whisper的语音识别服务"""
    
    def __init__(self, model_size: str = "base"):
        """
        初始化Whisper ASR服务
        
        Args:
            model_size: Whisper模型大小，可选值: tiny, base, small, medium, large
        """
        self.model_size = model_size
        self.model = None
        self.initialized = False
        # 如果有OpenCC可用，初始化繁体转简体的转换器
        self.cc = OpenCC('t2s') if HAS_OPENCC else None
        # 先不加载模型，等initialize方法调用时再加载
    
    def initialize(self) -> bool:
        """初始化ASR服务，加载模型"""
        try:
            if self.initialized and self.model is not None:
                logger.info(f"ASR服务已初始化，使用模型: {self.model_size}")
                return True
                
            # 从环境变量或配置获取模型大小
            env_model_size = os.getenv("WHISPER_MODEL_SIZE", "").lower()
            if env_model_size in ["tiny", "base", "small", "medium", "large"]:
                self.model_size = env_model_size
                logger.info(f"从环境变量加载Whisper模型大小: {self.model_size}")
            
            # 加载模型
            self._load_model()
            self.initialized = self.model is not None
            return self.initialized
        except Exception as e:
            logger.error(f"初始化ASR服务失败: {str(e)}")
            return False
    
    def _load_model(self):
        """加载Whisper模型"""
        logger.info(f"正在加载Whisper {self.model_size}模型...")
        try:
            # 检查是否有GPU可用
            device = "cuda" if torch.cuda.is_available() else "cpu"
            if device == "cpu":
                logger.warning("没有检测到GPU，将使用CPU进行处理，这可能会较慢")
            
            # 加载模型
            start_time = time.time()
            self.model = whisper.load_model(self.model_size, device=device)
            elapsed_time = time.time() - start_time
            logger.info(f"Whisper {self.model_size}模型加载成功，用时: {elapsed_time:.2f}秒，使用设备: {device}")
            return True
        except Exception as e:
            logger.error(f"加载Whisper模型时出错: {str(e)}")
            self.model = None
            return False
    
    def get_supported_languages(self) -> List[Dict[str, str]]:
        """获取支持的语言列表"""
        # Whisper支持的主要语言
        return [
            {"code": "zh", "name": "中文"},
            {"code": "en", "name": "英语"},
            {"code": "ja", "name": "日语"},
            {"code": "ko", "name": "韩语"},
            {"code": "fr", "name": "法语"},
            {"code": "de", "name": "德语"},
            {"code": "es", "name": "西班牙语"},
            {"code": "ru", "name": "俄语"},
            {"code": "pt", "name": "葡萄牙语"},
            {"code": "it", "name": "意大利语"},
            {"code": "nl", "name": "荷兰语"},
            {"code": "ar", "name": "阿拉伯语"},
            {"code": "hi", "name": "印地语"},
            {"code": "vi", "name": "越南语"},
        ]
    
    def transcribe(self, audio_file_path: str, language: Optional[str] = None) -> Dict[str, Any]:
        """
        转写音频文件
        
        Args:
            audio_file_path: 音频文件路径
            language: 音频语言代码 (如 'zh', 'en', 'ja')，如为None则自动检测
            
        Returns:
            包含转写结果的字典
        """
        if self.model is None:
            self._load_model()
        
        try:
            # 检查文件是否存在和大小
            if not os.path.exists(audio_file_path):
                logger.error(f"音频文件不存在: {audio_file_path}")
                return {
                    "text": "[音频文件不存在]",
                    "segments": [],
                    "language": language or "unknown",
                    "error": "文件不存在"
                }
            
            # 检查文件大小是否为0
            file_size = os.path.getsize(audio_file_path)
            if file_size == 0:
                logger.error(f"音频文件为空: {audio_file_path}")
                return {
                    "text": "[空音频文件]",
                    "segments": [],
                    "language": language or "unknown",
                    "error": "空音频文件"
                }
            
            # 准备转写选项
            options = {}
            if language:
                options["language"] = language
            
            # 记录转写开始
            start_time = time.time()
            logger.info(f"开始转写音频文件: {audio_file_path}")
            
            # 执行转写
            try:
                result = self.model.transcribe(audio_file_path, **options)
            except RuntimeError as e:
                if "CUDA out of memory" in str(e):
                    logger.warning("CUDA内存不足，尝试使用CPU进行识别")
                    # 保存当前设备
                    original_device = self.model.device
                    # 移动模型到CPU
                    self.model = self.model.to("cpu")
                    result = self.model.transcribe(audio_file_path, **options)
                    # 将模型移回原始设备
                    self.model = self.model.to(original_device)
                else:
                    raise
            
            # 记录转写结束
            elapsed_time = time.time() - start_time
            
            # 记录结果摘要
            detected_language = result.get("language", "unknown")
            text_length = len(result.get("text", ""))
            logger.info(f"音频转写完成: 检测到语言 {detected_language}, 文本长度 {text_length}, 用时 {elapsed_time:.2f}秒")
            
            # 检查结果是否为空
            if text_length == 0:
                logger.warning(f"识别结果为空文本")
                result["text"] = f"[无法识别的{detected_language}音频]"
                result["warning"] = "识别结果为空"
                
            # 格式化分段时间
            if "segments" in result:
                for segment in result["segments"]:
                    if "start" in segment:
                        segment["start_str"] = self._format_time(segment["start"])
                    if "end" in segment:
                        segment["end_str"] = self._format_time(segment["end"])
            
            # 检查是否需要将繁体中文转换为简体中文
            if (language and language.startswith('zh')) or detected_language == 'zh':
                # 如果是中文，且OpenCC可用，则转换繁体为简体
                if self.cc is not None:
                    logger.info("检测到中文，将繁体转为简体")
                    # 转换主要文本
                    if "text" in result and result["text"]:
                        result["text"] = self.cc.convert(result["text"])
                    
                    # 转换段落文本
                    if "segments" in result:
                        for segment in result["segments"]:
                            if "text" in segment and segment["text"]:
                                segment["text"] = self.cc.convert(segment["text"])
                else:
                    logger.warning("检测到中文，但OpenCC不可用，无法进行繁简转换")
            
            return result
        except Exception as e:
            logger.error(f"转写音频时出错: {str(e)}")
            # 返回错误信息，而不是抛出异常
            return {
                "text": f"[音频识别失败: {str(e)}]",
                "segments": [],
                "language": language or "unknown",
                "error": str(e)
            }
    
    def _format_time(self, seconds: float) -> str:
        """将秒数格式化为时:分:秒格式"""
        minutes, seconds = divmod(seconds, 60)
        hours, minutes = divmod(minutes, 60)
        return f"{int(hours):02d}:{int(minutes):02d}:{seconds:.2f}"
    
    async def transcribe_from_bytes(self, audio_bytes: bytes, language: Optional[str] = None) -> Dict[str, Any]:
        """
        从字节数据转写音频
        
        Args:
            audio_bytes: 音频文件的字节数据
            language: 音频语言代码，如为None则自动检测
            
        Returns:
            包含转写结果的字典
        """
        # 检查音频字节是否为空
        if not audio_bytes or len(audio_bytes) == 0:
            logger.error("收到空的音频字节数据")
            return {
                "text": "[空音频数据]",
                "segments": [],
                "language": language or "unknown",
                "error": "空音频数据"
            }
        
        # 创建临时文件
        with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
            temp_path = temp_file.name
            temp_file.write(audio_bytes)
        
        try:
            # 转写临时文件
            result = self.transcribe(temp_path, language)
            return result
        except Exception as e:
            logger.error(f"从字节数据转写音频时出错: {str(e)}")
            return {
                "text": f"[音频识别失败: {str(e)}]",
                "segments": [],
                "language": language or "unknown",
                "error": str(e)
            }
        finally:
            # 删除临时文件
            if os.path.exists(temp_path):
                os.remove(temp_path)

    async def transcribe_audio(self, audio_file_path: str, source_language: Optional[str] = None):
        """
        异步转写音频文件，返回转写文本、段落和检测到的语言
        
        Args:
            audio_file_path: 音频文件路径
            source_language: 指定源语言，如果为None则自动检测
            
        Returns:
            (transcript, segments, detected_language): 转写文本、段落列表和检测到的语言
        """
        if not self.initialized:
            self.initialize()
            
        # 调用同步转写方法
        result = self.transcribe(audio_file_path, source_language)
        
        # 提取转写文本
        transcript = result.get("text", "")
        
        # 格式化分段数据
        segments = []
        for segment in result.get("segments", []):
            segments.append({
                "start": segment.get("start", 0),
                "end": segment.get("end", 0),
                "text": segment.get("text", "")
            })
            
        # 获取检测到的语言
        detected_language = result.get("language", "")
        
        return transcript, segments, detected_language

# 单例模式获取ASR服务
_asr_service_instance = None

def get_asr_service():
    """获取ASR服务实例（单例模式）"""
    global _asr_service_instance
    if _asr_service_instance is None:
        # 从环境变量获取模型大小，默认为base
        model_size = os.getenv("WHISPER_MODEL_SIZE", "base").lower()
        _asr_service_instance = ASRService(model_size=model_size)
    return _asr_service_instance 

# 添加模块级别的函数以支持从translation.py导入
async def transcribe_audio(audio_file_path: str, source_language: Optional[str] = None):
    """
    模块级函数：转写音频文件
    使用ASRService实例调用transcribe_audio方法
    
    Args:
        audio_file_path: 音频文件路径
        source_language: 指定源语言，如果为None则自动检测
        
    Returns:
        (transcript, detected_language): 转写文本和检测到的语言
    """
    asr_service = get_asr_service()
    transcript, segments, detected_language = await asr_service.transcribe_audio(audio_file_path, source_language)
    return transcript, detected_language

async def detect_language(audio_file_path: str) -> str:
    """
    模块级函数：检测音频文件的语言
    使用ASRService实例的转写功能来检测语言
    
    Args:
        audio_file_path: 音频文件路径
        
    Returns:
        detected_language: 检测到的语言代码
    """
    asr_service = get_asr_service()
    result = asr_service.transcribe(audio_file_path)
    return result.get("language", "") 