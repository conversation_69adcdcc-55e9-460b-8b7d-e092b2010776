"""
视频生成服务适配器 - 将视频生成服务适配到统一的任务处理流程
"""
import logging
import os
from typing import Dict, Any, Optional, List

from services.task_adapter import TaskAdapter, register_adapter
from services.storage_manager import get_storage_manager

# 配置日志
logger = logging.getLogger(__name__)

class TextToVideoAdapter(TaskAdapter):
    """文本到视频生成适配器"""
    
    async def execute(self, task_id: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行文本到视频生成任务
        
        Args:
            task_id: 任务ID
            params: 任务参数，包括:
                prompt: 提示文本
                negative_prompt: 负面提示
                num_frames: 帧数
                height: 高度
                width: 宽度
                guidance_scale: 指导比例
                fps: 每秒帧数
                seed: 随机种子
                model_size: 模型大小
                resolution: 分辨率
                
        Returns:
            执行结果
        """
        try:
            # 更新进度
            await self.update_progress(task_id, 5, "准备视频生成参数...")
            
            # 提取参数
            prompt = params.get("prompt")
            negative_prompt = params.get("negative_prompt", "")
            num_frames = params.get("num_frames", 81)
            height = params.get("height", 480)
            width = params.get("width", 832)
            guidance_scale = params.get("guidance_scale", 5.0)
            fps = params.get("fps", 16)
            seed = params.get("seed")
            model_size = params.get("model_size", "1.3B")
            resolution = params.get("resolution", "480p")
            
            # 验证参数
            if not prompt:
                return await self.handle_error(task_id, ValueError("缺少必要的提示文本参数"))
            
            # 更新进度
            await self.update_progress(task_id, 10, "开始生成视频...")
            
            # 获取存储管理器
            storage_manager = get_storage_manager()
            
            # 准备输出文件路径
            video_output = storage_manager.get_result_path("video", f"{task_id}_generated_video.mp4", task_id)
            thumbnail_output = storage_manager.get_result_path("image", f"{task_id}_thumbnail.jpg", task_id)
            
            # 确保目录存在
            os.makedirs(os.path.dirname(video_output), exist_ok=True)
            os.makedirs(os.path.dirname(thumbnail_output), exist_ok=True)
            
            # 更新进度回调函数
            def progress_callback(progress, message=None):
                message = message or f"生成进度: {progress}%"
                return self.update_progress(task_id, 10 + int(progress * 0.8), message)
            
            # 调用视频生成服务
            logger.info(f"开始执行文本到视频生成任务: {task_id}, 模型: {model_size}, 分辨率: {resolution}")
            
            # 执行生成
            generation_result = await self.service.generate_video_from_text(
                task_id=task_id,
                prompt=prompt,
                negative_prompt=negative_prompt,
                num_frames=num_frames,
                height=height,
                width=width,
                guidance_scale=guidance_scale,
                fps=fps,
                seed=seed,
                model_size=model_size,
                resolution=resolution,
                video_output=video_output,
                thumbnail_output=thumbnail_output,
                progress_updater=progress_callback
            )
            
            # 如果生成失败
            if not generation_result or not generation_result.get("success"):
                error_msg = generation_result.get("error", "视频生成失败") if generation_result else "视频生成失败"
                return await self.handle_error(task_id, ValueError(error_msg))
            
            # 更新进度
            await self.update_progress(task_id, 90, "视频生成完成，处理结果...")
            
            # 构建返回结果
            video_url = generation_result.get("video_url", video_output)
            thumbnail_url = generation_result.get("thumbnail_url", thumbnail_output)
            
            result = {
                "video_url": video_url,
                "video_file": video_output,
                "thumbnail_url": thumbnail_url,
                "thumbnail_file": thumbnail_output,
                "prompt": prompt,
                "model_size": model_size,
                "resolution": resolution,
                "fps": fps,
                "num_frames": num_frames,
                "output_files": [video_output, thumbnail_output]
            }
            
            # 更新最终进度
            await self.update_progress(
                task_id, 
                100, 
                "视频生成任务完成", 
                status="completed",
                result=result
            )
            
            # 返回成功结果
            return {
                "success": True,
                "data": result
            }
            
        except Exception as e:
            # 处理异常
            return await self.handle_error(task_id, e)

class ImageToVideoAdapter(TaskAdapter):
    """图像到视频生成适配器"""
    
    async def execute(self, task_id: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行图像到视频生成任务
        
        Args:
            task_id: 任务ID
            params: 任务参数，包括:
                image_path: 图像文件路径
                prompt: 提示文本
                negative_prompt: 负面提示
                num_frames: 帧数
                fps: 每秒帧数
                seed: 随机种子
                resolution: 分辨率
                
        Returns:
            执行结果
        """
        try:
            # 更新进度
            await self.update_progress(task_id, 5, "准备图像到视频生成参数...")
            
            # 提取参数
            image_path = params.get("image_path")
            prompt = params.get("prompt", "")
            negative_prompt = params.get("negative_prompt", "")
            num_frames = params.get("num_frames", 81)
            guidance_scale = params.get("guidance_scale", 5.0)
            fps = params.get("fps", 16)
            seed = params.get("seed")
            resolution = params.get("resolution", "480p")
            
            # 验证参数
            if not image_path:
                return await self.handle_error(task_id, ValueError("缺少必要的图像文件路径参数"))
            
            if not os.path.exists(image_path):
                return await self.handle_error(task_id, FileNotFoundError(f"图像文件不存在: {image_path}"))
            
            # 更新进度
            await self.update_progress(task_id, 10, "开始生成视频...")
            
            # 获取存储管理器
            storage_manager = get_storage_manager()
            
            # 准备输出文件路径
            video_output = storage_manager.get_result_path("video", f"{task_id}_generated_video.mp4", task_id)
            thumbnail_output = storage_manager.get_result_path("image", f"{task_id}_thumbnail.jpg", task_id)
            
            # 确保目录存在
            os.makedirs(os.path.dirname(video_output), exist_ok=True)
            os.makedirs(os.path.dirname(thumbnail_output), exist_ok=True)
            
            # 更新进度回调函数
            def progress_callback(progress, message=None):
                message = message or f"生成进度: {progress}%"
                return self.update_progress(task_id, 10 + int(progress * 0.8), message)
            
            # 调用视频生成服务
            logger.info(f"开始执行图像到视频生成任务: {task_id}, 分辨率: {resolution}")
            
            # 执行生成
            generation_result = await self.service.generate_video_from_image(
                task_id=task_id,
                image_path=image_path,
                prompt=prompt,
                negative_prompt=negative_prompt,
                num_frames=num_frames,
                guidance_scale=guidance_scale,
                fps=fps,
                seed=seed,
                resolution=resolution,
                video_output=video_output,
                thumbnail_output=thumbnail_output,
                progress_updater=progress_callback
            )
            
            # 如果生成失败
            if not generation_result or not generation_result.get("success"):
                error_msg = generation_result.get("error", "视频生成失败") if generation_result else "视频生成失败"
                return await self.handle_error(task_id, ValueError(error_msg))
            
            # 更新进度
            await self.update_progress(task_id, 90, "视频生成完成，处理结果...")
            
            # 构建返回结果
            video_url = generation_result.get("video_url", video_output)
            thumbnail_url = generation_result.get("thumbnail_url", thumbnail_output)
            
            result = {
                "video_url": video_url,
                "video_file": video_output,
                "thumbnail_url": thumbnail_url,
                "thumbnail_file": thumbnail_output,
                "source_image": image_path,
                "prompt": prompt,
                "resolution": resolution,
                "fps": fps,
                "num_frames": num_frames,
                "output_files": [video_output, thumbnail_output]
            }
            
            # 更新最终进度
            await self.update_progress(
                task_id, 
                100, 
                "图像到视频生成任务完成", 
                status="completed",
                result=result
            )
            
            # 返回成功结果
            return {
                "success": True,
                "data": result
            }
            
        except Exception as e:
            # 处理异常
            return await self.handle_error(task_id, e)

# 注册适配器
register_adapter("text_to_video", TextToVideoAdapter)
register_adapter("image_to_video", ImageToVideoAdapter) 