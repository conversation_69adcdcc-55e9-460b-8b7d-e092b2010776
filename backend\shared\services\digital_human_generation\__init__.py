"""
数字人生成服务模块

提供数字人生成的全流程管理，包括：
- 任务管理
- 生成流水线
- 资源控制
- 状态监控
"""

from .service import DigitalHumanGenerationService
from .task_manager import TaskManager
from .pipeline import GenerationPipeline
from .models import TaskStatus, GenerationTask

# 全局服务实例
_service_instance = None


def initialize(
    max_concurrent_tasks=5,
    task_timeout_seconds=1800,
    mock_mode=False,
    data_dir="data/digital_humans",
    enable_monitoring=True,
):
    """
    初始化数字人生成服务
    
    Args:
        max_concurrent_tasks: 最大并发任务数
        task_timeout_seconds: 任务超时时间（秒）
        mock_mode: 是否使用模拟模式
        data_dir: 数据存储目录
        enable_monitoring: 是否启用监控
        
    Returns:
        DigitalHumanGenerationService: 初始化后的服务实例
    """
    global _service_instance
    
    if _service_instance is not None:
        # 如果已经初始化，更新配置
        _service_instance.update_config(
            max_concurrent_tasks=max_concurrent_tasks,
            task_timeout_seconds=task_timeout_seconds,
            mock_mode=mock_mode,
            data_dir=data_dir,
            enable_monitoring=enable_monitoring,
        )
        return _service_instance
    
    # 创建新实例
    _service_instance = DigitalHumanGenerationService(
        max_concurrent_tasks=max_concurrent_tasks,
        task_timeout_seconds=task_timeout_seconds,
        mock_mode=mock_mode,
        data_dir=data_dir,
        enable_monitoring=enable_monitoring,
    )
    
    return _service_instance


def get_digital_human_generation_service():
    """
    获取数字人生成服务实例
    
    Returns:
        DigitalHumanGenerationService: 服务实例，如果未初始化则初始化一个默认实例
    """
    global _service_instance
    
    if _service_instance is None:
        _service_instance = initialize()
        
    return _service_instance 