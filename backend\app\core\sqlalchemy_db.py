"""
SQLAlchemy数据库配置
用于支持ORM模型和模板管理
"""

import os
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from .config import settings

# 数据库URL配置
if settings.DATABASE_URL:
    # 使用PostgreSQL
    DATABASE_URL = settings.DATABASE_URL
else:
    # 使用SQLite
    db_path = os.path.join(os.path.dirname(__file__), "../../database.db")
    DATABASE_URL = f"sqlite:///{db_path}"

print(f"[SQLAlchemy] 数据库URL: {DATABASE_URL}")

# 创建数据库引擎
if DATABASE_URL.startswith("sqlite"):
    engine = create_engine(
        DATABASE_URL,
        connect_args={"check_same_thread": False},
        echo=False  # 设置为True可以看到SQL语句
    )
else:
    engine = create_engine(DATABASE_URL, echo=False)

# 创建SessionLocal类
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 创建Base类
Base = declarative_base()

def get_db():
    """获取数据库会话"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def init_sqlalchemy_db():
    """初始化SQLAlchemy数据库表"""
    try:
        # 导入所有模型以确保它们被注册
        from ..models.avatar_template import AvatarTemplate
        from ..models.voice_template import VoiceTemplate
        
        # 创建所有表
        Base.metadata.create_all(bind=engine)
        print("[SQLAlchemy] 数据库表初始化完成")
        
    except Exception as e:
        print(f"[SQLAlchemy] 数据库初始化失败: {e}")
        raise e

# 在模块加载时初始化数据库
init_sqlalchemy_db()
