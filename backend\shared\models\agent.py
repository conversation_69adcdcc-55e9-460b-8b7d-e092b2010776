from sqlalchemy import Column, String, Float, Integer, Boolean, DateTime, ForeignKey, Table, UniqueConstraint, Text, JSON
from sqlalchemy.orm import relationship
from datetime import datetime
import uuid

from utils.db import Base

# 定义Agent模型
class Agent(Base):
    __tablename__ = "agents"
    
    id = Column(String, primary_key=True, index=True, default=lambda: str(uuid.uuid4()))
    name = Column(String, index=True, nullable=False)
    description = Column(Text, nullable=True)
    category = Column(String, index=True, nullable=True)
    author_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    avatar = Column(String, nullable=True)
    cover_image = Column(String, nullable=True)
    rating = Column(Float, default=0.0)
    review_count = Column(Integer, default=0)
    usage_count = Column(Integer, default=0)
    is_featured = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    pricing_type = Column(String, default="free")  # free, premium, subscription
    price = Column(Float, default=0.0)
    
    # 语言学习智能体相关字段
    agent_type = Column(String, default="general", index=True)  # general, language_learning
    learning_settings = Column(JSON, nullable=True)  # 存储语言、口音、难度等设置
    corpus_id = Column(Integer, ForeignKey("corpus.corpus_id"), nullable=True)  # 关联语料库ID
    
    # 关系
    author = relationship("User", back_populates="created_agents")
    reviews = relationship("AgentReview", back_populates="agent", cascade="all, delete-orphan")
    tags = relationship("AgentTag", back_populates="agent", cascade="all, delete-orphan")
    capabilities = relationship("AgentCapability", back_populates="agent", cascade="all, delete-orphan")
    examples = relationship("AgentExample", back_populates="agent", cascade="all, delete-orphan")
    favorites = relationship("UserFavorite", back_populates="agent", cascade="all, delete-orphan")
    corpus = relationship("Corpus", foreign_keys=[corpus_id])

# 定义AgentTag模型
class AgentTag(Base):
    __tablename__ = "agent_tags"
    
    id = Column(String, primary_key=True, index=True, default=lambda: str(uuid.uuid4()))
    agent_id = Column(String, ForeignKey("agents.id", ondelete="CASCADE"))
    tag = Column(String, index=True)
    
    # 关系
    agent = relationship("Agent", back_populates="tags")
    
    # 唯一约束
    __table_args__ = (
        UniqueConstraint('agent_id', 'tag', name='uix_agent_tag'),
    )

# 定义AgentCapability模型
class AgentCapability(Base):
    __tablename__ = "agent_capabilities"
    
    id = Column(String, primary_key=True, index=True, default=lambda: str(uuid.uuid4()))
    agent_id = Column(String, ForeignKey("agents.id", ondelete="CASCADE"))
    capability = Column(String)
    
    # 关系
    agent = relationship("Agent", back_populates="capabilities")
    
    # 唯一约束
    __table_args__ = (
        UniqueConstraint('agent_id', 'capability', name='uix_agent_capability'),
    )

# 定义AgentExample模型
class AgentExample(Base):
    __tablename__ = "agent_examples"
    
    id = Column(String, primary_key=True, index=True, default=lambda: str(uuid.uuid4()))
    agent_id = Column(String, ForeignKey("agents.id", ondelete="CASCADE"))
    example = Column(Text)
    
    # 关系
    agent = relationship("Agent", back_populates="examples")

# 定义AgentReview模型
class AgentReview(Base):
    __tablename__ = "agent_reviews"
    
    id = Column(String, primary_key=True, index=True, default=lambda: str(uuid.uuid4()))
    agent_id = Column(String, ForeignKey("agents.id", ondelete="CASCADE"))
    user_id = Column(Integer, ForeignKey("users.id"))
    rating = Column(Float)
    comment = Column(Text, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    agent = relationship("Agent", back_populates="reviews")
    user = relationship("User", back_populates="agent_reviews")
    
    # 唯一约束
    __table_args__ = (
        UniqueConstraint('agent_id', 'user_id', name='uix_agent_user_review'),
    )

# 定义UserFavorite模型
class UserFavorite(Base):
    __tablename__ = "user_favorites"
    
    id = Column(String, primary_key=True, index=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(Integer, ForeignKey("users.id"))
    agent_id = Column(String, ForeignKey("agents.id", ondelete="CASCADE"))
    category = Column(String, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # 关系
    user = relationship("User", back_populates="agent_favorites")
    agent = relationship("Agent", back_populates="favorites")
    
    # 唯一约束
    __table_args__ = (
        UniqueConstraint('user_id', 'agent_id', name='uix_user_agent_favorite'),
    )

# 定义AgentSession模型
class AgentSession(Base):
    __tablename__ = "agent_sessions"
    
    id = Column(String, primary_key=True, index=True, default=lambda: str(uuid.uuid4()))
    agent_id = Column(String, ForeignKey("agents.id", ondelete="CASCADE"))
    user_id = Column(Integer, ForeignKey("users.id"))
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    agent = relationship("Agent")
    user = relationship("User")
    messages = relationship("AgentMessage", back_populates="session", cascade="all, delete-orphan")

# 定义AgentMessage模型
class AgentMessage(Base):
    __tablename__ = "agent_messages"
    
    id = Column(String, primary_key=True, index=True, default=lambda: str(uuid.uuid4()))
    session_id = Column(String, ForeignKey("agent_sessions.id", ondelete="CASCADE"))
    sender_type = Column(String)  # "user" or "agent"
    content = Column(Text)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # 关系
    session = relationship("AgentSession", back_populates="messages") 