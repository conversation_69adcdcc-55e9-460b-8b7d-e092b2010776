#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WAN模型复制工具

该脚本用于将local_models目录中的模型复制到models_cache目录，
确保模型可以被正确找到和加载。
"""

import os
import sys
import time
import shutil
import logging
import platform
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 获取项目根目录
PROJECT_ROOT = Path(__file__).parent.parent.parent.absolute()
logger.info(f"项目根目录: {PROJECT_ROOT}")

# 模型路径
MODELS_CACHE_DIR = os.path.join(PROJECT_ROOT, "backend", "models_cache")
LOCAL_MODELS_DIR = os.path.join(PROJECT_ROOT, "backend", "local_models", "wan", "Wan2.1")

# 模型映射
MODEL_MAPPINGS = [
    {
        "cache_path": os.path.join(MODELS_CACHE_DIR, "Wan2.1-T2V-1.3B"),
        "local_path": os.path.join(LOCAL_MODELS_DIR, "Wan2.1-T2V-1.3B")
    },
    {
        "cache_path": os.path.join(MODELS_CACHE_DIR, "Wan2.1-T2V-14B"),
        "local_path": os.path.join(LOCAL_MODELS_DIR, "Wan2.1-T2V-14B")
    }
]

def check_directory_exists(dir_path):
    """检查目录是否存在"""
    exists = os.path.exists(dir_path)
    if exists:
        logger.info(f"目录存在: {dir_path}")
    else:
        logger.warning(f"目录不存在: {dir_path}")
    return exists

def copy_model_files(source, target):
    """复制模型文件"""
    try:
        # 如果目标目录已存在，先删除
        if os.path.exists(target):
            logger.info(f"移除现有目标目录: {target}")
            shutil.rmtree(target, ignore_errors=True)
        
        # 复制模型目录
        logger.info(f"复制模型目录: {source} -> {target}")
        start_time = time.time()
        
        # 确保目标父目录存在
        os.makedirs(os.path.dirname(target), exist_ok=True)
        
        # 复制整个目录
        shutil.copytree(source, target)
        
        elapsed_time = time.time() - start_time
        logger.info(f"模型复制完成 ({elapsed_time:.2f}秒)")
        return True
    except Exception as e:
        logger.error(f"复制模型文件失败: {e}")
        return False

def update_models():
    """更新所有模型"""
    success_count = 0
    failed_count = 0
    
    # 确保模型缓存目录存在
    os.makedirs(MODELS_CACHE_DIR, exist_ok=True)
    
    for mapping in MODEL_MAPPINGS:
        local_path = mapping["local_path"]
        cache_path = mapping["cache_path"]
        
        logger.info(f"处理模型: {os.path.basename(cache_path)}")
        
        # 检查本地模型目录是否存在
        if not check_directory_exists(local_path):
            logger.warning(f"跳过模型复制，源目录不存在: {local_path}")
            failed_count += 1
            continue
        
        # 复制模型文件
        if copy_model_files(local_path, cache_path):
            success_count += 1
        else:
            failed_count += 1
    
    return success_count, failed_count

def main():
    print("\n===== WAN模型复制工具 =====\n")
    
    print(f"系统信息:")
    print(f"  操作系统: {platform.system()} {platform.release()}")
    print(f"  Python版本: {platform.python_version()}")
    print(f"  当前用户: {os.getlogin()}")
    print(f"  工作目录: {os.getcwd()}")
    print()
    
    print(f"模型路径:")
    print(f"  缓存目录: {MODELS_CACHE_DIR}")
    print(f"  本地目录: {LOCAL_MODELS_DIR}")
    print()
    
    # 执行模型更新
    success_count, failed_count = update_models()
    
    print("\n更新结果:")
    print(f"  成功: {success_count}")
    print(f"  失败: {failed_count}")
    print()
    
    if failed_count == 0:
        print("所有模型已成功更新！")
    else:
        print("部分模型更新失败，请检查日志。")
    
    print("\n请重新启动应用程序以应用更新。")
    print("\n===== 更新过程完成 =====\n")

if __name__ == "__main__":
    main() 