import os
import logging
import time
import shutil
import uuid
import tempfile
import subprocess
import threading
import queue
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional, List, Union, Tuple
import asyncio
import threading
from PIL import Image

# 获取日志记录器
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 定义转换结果目录
CONVERSION_DIR = "results/documents"
os.makedirs(CONVERSION_DIR, exist_ok=True)

# 定义上传目录
UPLOAD_DIR = "uploads/documents"
os.makedirs(UPLOAD_DIR, exist_ok=True)

# 任务状态字典（在实际生产环境中，应使用数据库或分布式缓存）
_TASK_STATUS = {}

# 定义任务队列
_TASK_QUEUE = queue.Queue()

# 文档转换服务单例
_document_conversion_service = None

def get_document_conversion_service():
    """获取文档转换服务实例"""
    global _document_conversion_service
    if _document_conversion_service is None:
        _document_conversion_service = DocumentConversionService()
    return _document_conversion_service

class TaskProcessor(threading.Thread):
    """任务处理器，在后台线程中处理转换任务"""
    
    def __init__(self, conversion_service):
        super().__init__(daemon=True)
        self.conversion_service = conversion_service
        self.running = True
        self.loop = None
        logger.info("任务处理器初始化完成")
    
    def run(self):
        """启动任务处理循环"""
        # 创建新的事件循环
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)
        
        logger.info("任务处理器开始运行")
        
        # 记录初始队列状态
        logger.info(f"初始任务队列大小: {_TASK_QUEUE.qsize()}")
        
        while self.running:
            try:
                # 从队列获取任务，等待超时为1秒
                try:
                    task = _TASK_QUEUE.get(timeout=1)
                    logger.info(f"获取到新任务: {task['task_id']}, 当前队列大小: {_TASK_QUEUE.qsize()}")
                except queue.Empty:
                    # 定期记录任务队列状态
                    if _TASK_QUEUE.qsize() > 0:
                        logger.info(f"等待处理的任务: {_TASK_QUEUE.qsize()}")
                    continue
                
                # 检查任务关键字段是否完整
                required_fields = ["task_id", "upload_id", "target_format"]
                missing_fields = [field for field in required_fields if field not in task or not task[field]]
                
                if missing_fields:
                    logger.error(f"任务 {task.get('task_id', 'UNKNOWN')} 缺少必要字段: {missing_fields}")
                    # 更新任务状态为错误
                    if 'task_id' in task and task['task_id']:
                        self.conversion_service._update_task_status(
                            task['task_id'],
                            status="error",
                            message=f"任务配置不完整，缺少必要字段: {missing_fields}"
                        )
                    _TASK_QUEUE.task_done()
                    continue
                
                logger.info(f"开始处理任务: {task['task_id']}, 转换格式: {task.get('target_format')}")
                
                # 异步处理任务
                try:
                    # 创建Future对象跟踪任务执行
                    future = asyncio.run_coroutine_threadsafe(
                        self.conversion_service.process_conversion(
                            task["task_id"],
                            task["upload_id"],
                            task["target_format"],
                            task.get("output_file_name"),
                            task.get("options"),
                            task.get("user_id")
                        ),
                        self.loop
                    )
                    
                    # 等待任务完成或超时
                    try:
                        # 设置较长的超时时间
                        future.result(timeout=300)  # 5分钟超时
                        logger.info(f"任务 {task['task_id']} 处理完成")
                    except asyncio.TimeoutError:
                        logger.error(f"任务 {task['task_id']} 处理超时")
                        # 更新任务状态为错误
                        self.conversion_service._update_task_status(
                            task["task_id"],
                            status="error",
                            message="任务处理超时，请检查后台服务"
                        )
                    except Exception as exec_error:
                        logger.error(f"任务 {task['task_id']} 执行出错: {str(exec_error)}")
                        # 更新任务状态为错误
                        self.conversion_service._update_task_status(
                            task["task_id"],
                            status="error",
                            message=f"任务执行出错: {str(exec_error)}"
                        )
                except Exception as async_error:
                    logger.error(f"启动任务 {task['task_id']} 异步处理失败: {str(async_error)}")
                    # 更新任务状态为错误
                    self.conversion_service._update_task_status(
                        task["task_id"],
                        status="error",
                        message=f"启动任务处理失败: {str(async_error)}"
                    )
                
                # 标记任务为已处理
                _TASK_QUEUE.task_done()
                
            except Exception as e:
                logger.error(f"处理任务时出现未捕获的异常: {str(e)}")
                try:
                    import traceback
                    logger.error(f"异常堆栈: {traceback.format_exc()}")
                except:
                    pass
                time.sleep(1)  # 避免CPU使用率过高
        
        logger.info("任务处理器已停止")
    
    def stop(self):
        """停止任务处理器"""
        self.running = False
        logger.info("任务处理器收到停止信号")

class DocumentConversionService:
    """文档转换服务类"""
    
    def __init__(self):
        """初始化文档转换服务"""
        self.task_status = _TASK_STATUS
        
        # 创建必要的目录
        os.makedirs(CONVERSION_DIR, exist_ok=True)
        os.makedirs(UPLOAD_DIR, exist_ok=True)
        
        # 检查基本工具是否可用
        self._check_requirements()
        
        # 启动任务处理器
        self.task_processor = TaskProcessor(self)
        self.task_processor.start()
        
        logger.info("文档转换服务初始化完成")
    
    def _check_requirements(self):
        """检查必要工具是否安装"""
        try:
            # 检查LibreOffice是否安装 - 使用更轻量级的参数
            libreoffice_path = "C:\\Program Files\\LibreOffice\\program\\soffice.exe"
            if not os.path.exists(libreoffice_path):
                logger.warning(f"未找到LibreOffice可执行文件: {libreoffice_path}")
                self.libreoffice_available = False
            else:
                try:
                    # 使用更轻量的参数，减少超时可能性
                    result = subprocess.run(
                        [libreoffice_path, "--version", "--headless"],
                        capture_output=True, 
                        text=True,
                        timeout=5  # 减少超时时间为5秒
                    )
                    if result.returncode == 0:
                        logger.info(f"检测到LibreOffice: 安装路径 {libreoffice_path}")
                        self.libreoffice_available = True
                    else:
                        logger.warning("LibreOffice检测失败，某些转换功能可能不可用")
                        self.libreoffice_available = False
                except subprocess.TimeoutExpired:
                    # 超时时不报错，只标记为不可用
                    logger.warning("LibreOffice命令执行超时，将假定可用但性能可能受限")
                    # 如果文件存在，仍然尝试使用
                    self.libreoffice_available = os.path.isfile(libreoffice_path)
                    logger.info(f"LibreOffice检测超时但找到可执行文件，将尝试使用: {libreoffice_path}")
                except Exception as lo_error:
                    logger.warning(f"检测LibreOffice时出错: {str(lo_error)}")
                    # 虽然检测失败，但如果文件存在，仍然尝试使用
                    self.libreoffice_available = os.path.isfile(libreoffice_path)
                    logger.info(f"LibreOffice检测失败但找到可执行文件，将尝试使用: {libreoffice_path}")
            
            # 检查ImageMagick
            try:
                result = subprocess.run(
                    ["magick", "--version"], 
                    capture_output=True, 
                    text=True,
                    timeout=5
                )
                if result.returncode == 0:
                    # 修复：先分割字符串，然后在f-string中使用结果
                    first_line = result.stdout.split('\n')[0].strip()
                    logger.info(f"检测到ImageMagick: {first_line}")
                    self.imagemagick_available = True
                else:
                    logger.warning("ImageMagick检测失败，图像转换功能可能受限")
                    self.imagemagick_available = False
            except subprocess.TimeoutExpired:
                # 超时时不报错，只标记为不可用
                logger.warning("ImageMagick命令执行超时，标记为不可用")
                self.imagemagick_available = False
            except Exception as e:
                logger.warning(f"ImageMagick检测失败: {str(e)}")
                self.imagemagick_available = False
            
            self.requirements_checked = True
            
        except Exception as e:
            logger.warning(f"文档转换服务依赖检查失败: {str(e)}")
            self.requirements_checked = False
            self.libreoffice_available = False
            self.imagemagick_available = False
    
    async def process_conversion(
        self,
        task_id: str,
        upload_id: str,
        target_format: str,
        output_file_name: Optional[str] = None,
        options: Optional[Dict[str, Any]] = None,
        user_id: Optional[int] = None
    ):
        """
        处理文档转换
        
        Args:
            task_id (str): 任务ID
            upload_id (str): 上传ID
            target_format (str): 目标格式
            output_file_name (str, optional): 输出文件名
            options (Dict[str, Any], optional): 转换选项
            user_id (int, optional): 用户ID
        
        Returns:
            Dict[str, Any]: 转换结果
        """
        # 更新任务状态为处理中
        self._update_task_status(
            task_id,
            status="processing",
            progress=10,
            message="任务处理中，准备转换文件..."
        )
        
        try:
            # 检查必要依赖
            if not self.requirements_checked:
                logger.warning(f"任务 {task_id} 处理前重新检查依赖...")
                self._check_requirements()
                
            if not self.libreoffice_available and not self.imagemagick_available:
                self._update_task_status(
                    task_id,
                    status="error",
                    message="服务器缺少必要的转换工具（LibreOffice和ImageMagick）"
                )
                logger.error(f"任务 {task_id} 失败: 服务器缺少必要的转换工具")
                return
                
            # 记录详细的上传ID信息
            logger.info(f"任务 {task_id} 查找上传ID: {upload_id}, 用户ID: {user_id}")
            
            # 查找源文件
            user_upload_dir = os.path.join(UPLOAD_DIR, str(user_id) if user_id else "anonymous")
            logger.info(f"上传目录: {user_upload_dir}")
            
            # 检查目录是否存在
            if not os.path.exists(user_upload_dir):
                logger.error(f"用户上传目录不存在: {user_upload_dir}")
                self._update_task_status(
                    task_id,
                    status="error",
                    message=f"用户上传目录不存在: {os.path.basename(user_upload_dir)}"
                )
                return
                
            # 记录目录中的文件
            files_in_dir = os.listdir(user_upload_dir)
            logger.info(f"目录中的文件: {files_in_dir}")
            
            # 在实际生产环境中，应通过upload_id准确查找文件
            # 这里我们查找与upload_id关联的文件
            source_file = None
            # 优先查找精确匹配upload_id的文件
            for file in files_in_dir:
                file_path = os.path.join(user_upload_dir, file)
                # 检查文件名或元数据中是否包含upload_id
                if upload_id in file:
                    source_file = file_path
                    logger.info(f"找到匹配上传ID的文件: {file_path}")
                    break
            
            # 如果没找到，使用最新上传的文件
            if not source_file:
                logger.warning(f"未找到匹配上传ID {upload_id} 的文件，尝试使用最新上传的文件")
                source_files = [os.path.join(user_upload_dir, f) for f in files_in_dir]
                if source_files:
                    source_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)
                    source_file = source_files[0]
                    logger.info(f"使用最新上传的文件: {source_file}")
            
            if not source_file:
                error_msg = f"未找到上传ID为 {upload_id} 的文件"
                logger.error(error_msg)
                self._update_task_status(
                    task_id,
                    status="error",
                    progress=0,
                    message=error_msg
                )
                return
            
            # 检查文件是否可读
            if not os.path.isfile(source_file) or not os.access(source_file, os.R_OK):
                error_msg = f"文件不存在或无法读取: {os.path.basename(source_file)}"
                logger.error(error_msg)
                self._update_task_status(
                    task_id,
                    status="error",
                    message=error_msg
                )
                return
                
            # 检查文件大小
            file_size = os.path.getsize(source_file)
            if file_size == 0:
                error_msg = "上传的文件大小为0字节，可能已损坏"
                logger.error(error_msg)
                self._update_task_status(
                    task_id,
                    status="error",
                    message=error_msg
                )
                return
            logger.info(f"源文件大小: {file_size} 字节")
            
            # 获取源文件信息
            source_format = os.path.splitext(source_file)[1].lower().lstrip('.')
            original_filename = os.path.basename(source_file)
            
            # 更新任务状态
            self._update_task_status(
                task_id,
                progress=20,
                message=f"已找到源文件: {original_filename}"
            )
            
            # 创建用户结果目录
            user_result_dir = os.path.join(CONVERSION_DIR, str(user_id) if user_id else "anonymous")
            os.makedirs(user_result_dir, exist_ok=True)
            
            # 生成输出文件名
            if not output_file_name:
                base_name = os.path.splitext(original_filename)[0]
                output_file_name = f"{base_name}.{target_format}"
            elif not output_file_name.endswith(f".{target_format}"):
                output_file_name = f"{output_file_name}.{target_format}"
            
            # 生成唯一的输出文件路径
            output_filename = f"{int(time.time())}_{str(uuid.uuid4())[:8]}_{output_file_name}"
            output_path = os.path.join(user_result_dir, output_filename)
            
            # 更新任务状态
            self._update_task_status(
                task_id,
                progress=30,
                message=f"准备将 {original_filename} 转换为 {target_format} 格式"
            )
            
            # 验证转换格式是否支持
            image_formats = ['jpg', 'jpeg', 'png', 'webp', 'gif']
            document_formats = ['docx', 'doc', 'rtf', 'odt', 'pdf', 'html', 'htm', 'txt', 'epub']
            
            is_image_conversion = source_format in image_formats and target_format in image_formats + ['pdf']
            is_document_conversion = source_format in document_formats and target_format in ['docx', 'pdf', 'html', 'txt', 'odt', 'rtf']
            
            if not is_image_conversion and not is_document_conversion:
                error_msg = f"不支持从 {source_format} 转换为 {target_format}"
                logger.error(error_msg)
                self._update_task_status(
                    task_id,
                    status="error",
                    message=error_msg
                )
                return
            
            # 检查依赖工具
            if is_image_conversion and not self.imagemagick_available:
                error_msg = "服务器缺少图像处理工具（ImageMagick），无法完成转换"
                logger.error(error_msg)
                self._update_task_status(
                    task_id,
                    status="error",
                    message=error_msg
                )
                return
                
            if is_document_conversion and not self.libreoffice_available:
                error_msg = "服务器缺少文档转换工具（LibreOffice），无法完成转换"
                logger.error(error_msg)
                self._update_task_status(
                    task_id,
                    status="error",
                    message=error_msg
                )
                return
            
            # 执行转换
            logger.info(f"开始执行转换: {source_file} -> {output_path}")
            conversion_success, conversion_message = await self._convert_document(
                source_file, output_path, source_format, target_format, options, task_id
            )
            
            if not conversion_success:
                logger.error(f"转换失败: {conversion_message}")
                self._update_task_status(
                    task_id,
                    status="error",
                    message=f"转换失败: {conversion_message}"
                )
                return
            
            # 检查文件是否存在
            if not os.path.exists(output_path):
                error_msg = "转换完成但未找到输出文件"
                logger.error(error_msg)
                self._update_task_status(
                    task_id,
                    status="error",
                    message=error_msg
                )
                return
                
            # 检查输出文件大小
            output_size = os.path.getsize(output_path)
            if output_size == 0:
                error_msg = "转换完成但输出文件大小为0字节"
                logger.error(error_msg)
                self._update_task_status(
                    task_id,
                    status="error",
                    message=error_msg
                )
                return
            logger.info(f"输出文件大小: {output_size} 字节")
            
            # 更新任务状态为成功
            self._update_task_status(
                task_id,
                status="success",
                progress=100,
                message="转换成功",
                result={
                    "file_name": output_filename,
                    "file_size": output_size,
                    "format": target_format
                },
                result_file=output_path
            )
            
            logger.info(f"任务 {task_id} 处理完成: {source_file} -> {output_path}")
            
        except Exception as e:
            logger.error(f"处理转换任务 {task_id} 时出错: {str(e)}")
            # 记录详细堆栈跟踪
            try:
                import traceback
                logger.error(f"处理任务 {task_id} 异常堆栈: {traceback.format_exc()}")
            except:
                pass
            
            # 更新任务状态为失败
            self._update_task_status(
                task_id,
                status="error",
                message=f"处理失败: {str(e)}"
            )
    
    async def _convert_document(
        self,
        source_path: str,
        output_path: str,
        source_format: str,
        target_format: str,
        options: Optional[Dict[str, Any]] = None,
        task_id: Optional[str] = None
    ) -> Tuple[bool, str]:
        """
        执行文档转换
        
        Args:
            source_path (str): 源文件路径
            output_path (str): 输出文件路径
            source_format (str): 源文件格式
            target_format (str): 目标文件格式
            options (Dict[str, Any], optional): 转换选项
            task_id (str, optional): 任务ID，用于更新进度
            
        Returns:
            Tuple[bool, str]: (成功标志, 消息)
        """
        if task_id:
            self._update_task_status(
                task_id,
                progress=40,
                message=f"开始转换 {source_format} 到 {target_format}..."
            )
        
        try:
            # 标准化格式名称
            source_format = source_format.lower()
            target_format = target_format.lower()
            
            # 对于图片格式的转换
            if source_format in ['jpg', 'jpeg', 'png', 'webp', 'gif'] and target_format in ['jpg', 'jpeg', 'png', 'webp', 'gif', 'pdf']:
                return await self._convert_image(source_path, output_path, source_format, target_format, options, task_id)
            
            # 对于文档格式的转换
            elif source_format in ['docx', 'doc', 'rtf', 'odt', 'pdf', 'html', 'htm', 'txt', 'epub'] and \
                 target_format in ['docx', 'pdf', 'html', 'txt', 'odt', 'rtf']:
                
                # 更新进度
                if task_id:
                    self._update_task_status(
                        task_id,
                        progress=50,
                        message="正在使用LibreOffice转换文档..."
                    )
                
                # 使用LibreOffice转换
                return await self._convert_with_libreoffice(source_path, output_path, source_format, target_format, options, task_id)
            
            else:
                return False, f"不支持从 {source_format} 转换为 {target_format}"
        
        except Exception as e:
            logger.error(f"转换文档时出错: {str(e)}")
            return False, f"转换过程中出错: {str(e)}"
    
    async def _convert_image(
        self,
        source_path: str,
        output_path: str,
        source_format: str,
        target_format: str,
        options: Optional[Dict[str, Any]] = None,
        task_id: Optional[str] = None
    ) -> Tuple[bool, str]:
        """
        转换图片格式
        
        Args:
            source_path (str): 源文件路径
            output_path (str): 输出文件路径
            source_format (str): 源文件格式
            target_format (str): 目标文件格式
            options (Dict[str, Any], optional): 转换选项
            task_id (str, optional): 任务ID，用于更新进度
            
        Returns:
            Tuple[bool, str]: (成功标志, 消息)
        """
        try:
            if task_id:
                self._update_task_status(
                    task_id,
                    progress=50,
                    message="正在处理图片..."
                )
            
            # 设置默认选项
            image_options = options.get('image', {}) if options else {}
            quality = image_options.get('quality', 90)
            dpi = image_options.get('dpi', 300)
            
            # 如果ImageMagick可用，优先使用它
            if hasattr(self, 'imagemagick_available') and self.imagemagick_available and target_format == 'pdf':
                # 使用ImageMagick进行转换
                cmd = [
                    "magick",
                    source_path,
                    "-quality", str(quality),
                    "-density", str(dpi),
                    output_path
                ]
                
                process = await asyncio.create_subprocess_exec(
                    *cmd,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                
                stdout, stderr = await process.communicate()
                
                if process.returncode == 0:
                    if task_id:
                        self._update_task_status(
                            task_id,
                            progress=90,
                            message="图片处理完成，保存结果..."
                        )
                    return True, "图片转换成功"
                else:
                    # 如果ImageMagick失败，回退到PIL
                    logger.warning(f"ImageMagick转换失败: {stderr.decode('utf-8', errors='ignore')}")
                    logger.info("回退到PIL进行图片转换")
            
            # 使用PIL转换图片
            img = Image.open(source_path)
            
            # 应用转换选项
            if 'resize' in image_options:
                width = image_options['resize'].get('width')
                height = image_options['resize'].get('height')
                if width and height:
                    img = img.resize((width, height), Image.LANCZOS)
            
            # 如果目标格式是PDF，需要特殊处理
            if target_format.lower() == 'pdf':
                if img.mode == 'RGBA':
                    img = img.convert('RGB')
                
                # 确保目标目录存在
                os.makedirs(os.path.dirname(output_path), exist_ok=True)
                
                # 保存PDF
                img.save(output_path, 'PDF', resolution=dpi, quality=quality)
            else:
                # 确保目标目录存在
                os.makedirs(os.path.dirname(output_path), exist_ok=True)
                
                # 保存其他格式
                img.save(output_path, quality=quality)
            
            if task_id:
                self._update_task_status(
                    task_id,
                    progress=90,
                    message="图片处理完成，保存结果..."
                )
            
            return True, "图片转换成功"
        
        except Exception as e:
            logger.error(f"转换图片时出错: {str(e)}")
            return False, f"转换图片时出错: {str(e)}"
    
    async def _convert_with_libreoffice(
        self,
        source_path: str,
        output_path: str,
        source_format: str,
        target_format: str,
        options: Optional[Dict[str, Any]] = None,
        task_id: Optional[str] = None
    ) -> Tuple[bool, str]:
        """
        使用LibreOffice转换文档
        
        Args:
            source_path (str): 源文件路径
            output_path (str): 输出文件路径
            source_format (str): 源文件格式
            target_format (str): 目标文件格式
            options (Dict[str, Any], optional): 转换选项
            task_id (str, optional): 任务ID，用于更新进度
            
        Returns:
            Tuple[bool, str]: (成功标志, 消息)
        """
        # 检查LibreOffice是否可用
        if not hasattr(self, 'libreoffice_available') or not self.libreoffice_available:
            logger.error("LibreOffice不可用，无法转换文档")
            return False, "LibreOffice不可用，无法转换文档"
        
        try:
            # 创建临时工作目录
            with tempfile.TemporaryDirectory() as temp_dir:
                # 复制源文件到临时目录
                temp_source = os.path.join(temp_dir, os.path.basename(source_path))
                shutil.copy2(source_path, temp_source)
                
                # 准备LibreOffice命令
                output_format_arg = self._get_libreoffice_format_arg(target_format)
                
                # 设置进度更新
                if task_id:
                    self._update_task_status(
                        task_id,
                        progress=60,
                        message="LibreOffice正在处理文档..."
                    )
                
                # 构建命令
                cmd = [
                    "C:\\Program Files\\LibreOffice\\program\\soffice.exe",
                    "--headless",
                    "--nofirststartwizard",  # 跳过首次启动向导
                    "--norestore",           # 不恢复之前的会话
                    "--nologo",              # 不显示启动屏幕
                    "--nolockcheck",         # 不检查文件锁
                    "--convert-to", output_format_arg,
                    "--outdir", temp_dir,
                    temp_source
                ]
                
                # 执行转换命令，增加超时时间
                process = await asyncio.create_subprocess_exec(
                    *cmd,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                
                try:
                    # 设置较长的超时时间
                    stdout, stderr = await asyncio.wait_for(process.communicate(), timeout=120)  # 2分钟超时
                except asyncio.TimeoutError:
                    logger.error("LibreOffice转换超时（2分钟）")
                    # 尝试终止进程
                    try:
                        process.terminate()
                        await asyncio.sleep(1)
                        if process.returncode is None:
                            process.kill()
                    except:
                        pass
                    return False, "LibreOffice转换超时（2分钟）"
                
                if process.returncode != 0:
                    error_msg = stderr.decode('utf-8', errors='ignore') if stderr else "未知错误"
                    logger.error(f"LibreOffice转换失败: {error_msg}")
                    return False, f"LibreOffice转换失败: {error_msg}"
                
                # 更新进度
                if task_id:
                    self._update_task_status(
                        task_id,
                        progress=80,
                        message="文档转换完成，正在处理输出..."
                    )
                
                # 找到输出文件
                output_files = [f for f in os.listdir(temp_dir) if f != os.path.basename(temp_source)]
                
                if not output_files:
                    return False, "LibreOffice转换完成但未生成输出文件"
                
                # 移动输出文件到目标位置
                temp_output = os.path.join(temp_dir, output_files[0])
                
                # 应用附加选项（如PDF选项）
                if target_format == 'pdf' and options and 'pdf' in options:
                    # 在此可以使用其他工具如pdftk对PDF进行进一步处理
                    pass
                
                # 确保目标目录存在
                os.makedirs(os.path.dirname(output_path), exist_ok=True)
                
                # 将临时输出文件移动到最终位置
                shutil.move(temp_output, output_path)
                
                if task_id:
                    self._update_task_status(
                        task_id,
                        progress=90,
                        message="转换完成，保存结果..."
                    )
                
                return True, "文档转换成功"
                
        except Exception as e:
            logger.error(f"LibreOffice转换过程中出错: {str(e)}")
            return False, f"LibreOffice转换过程中出错: {str(e)}"
    
    def _get_libreoffice_format_arg(self, target_format: str) -> str:
        """
        获取LibreOffice格式参数
        
        Args:
            target_format (str): 目标格式
            
        Returns:
            str: LibreOffice格式参数
        """
        # 格式映射
        format_map = {
            'pdf': 'pdf:writer_pdf_Export',
            'docx': 'docx',
            'odt': 'odt',
            'html': 'html:HTML',
            'txt': 'txt:Text',
            'rtf': 'rtf',
        }
        
        return format_map.get(target_format.lower(), target_format)
    
    def _update_task_status(self, task_id: str, **kwargs):
        """
        更新任务状态
        
        Args:
            task_id (str): 任务ID
            **kwargs: 要更新的状态字段
        """
        if task_id not in self.task_status:
            # 如果任务ID不存在，创建初始状态记录
            logger.info(f"创建新的任务状态记录: {task_id}")
            self.task_status[task_id] = {
                "task_id": task_id,
                "status": "pending",
                "progress": 0,
                "message": "任务初始化中",
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat()
            }
        
        # 更新状态
        for key, value in kwargs.items():
            self.task_status[task_id][key] = value
        
        # 更新时间戳
        self.task_status[task_id]["updated_at"] = datetime.now().isoformat()
        
        logger.debug(f"已更新任务 {task_id} 状态: {kwargs}")
        
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        获取任务状态
        
        Args:
            task_id (str): 任务ID
            
        Returns:
            Optional[Dict[str, Any]]: 任务状态或None（如果任务不存在）
        """
        return self.task_status.get(task_id)

    def get_task_result_file(self, task_id: str) -> Optional[str]:
        """
        获取任务结果文件路径
        
        Args:
            task_id (str): 任务ID
            
        Returns:
            Optional[str]: 结果文件路径或None
        """
        task_status = self.get_task_status(task_id)
        if not task_status or task_status.get('status') != 'success' or 'result_file' not in task_status:
            return None
        
        result_file = task_status.get('result_file')
        if not result_file or not os.path.exists(result_file):
            return None
            
        return result_file

    def create_conversion_task(
        self,
        upload_id: str,
        target_format: str,
        output_file_name: Optional[str] = None,
        options: Optional[Dict[str, Any]] = None,
        user_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        创建文档转换任务
        
        Args:
            upload_id (str): 上传ID
            target_format (str): 目标格式
            output_file_name (str, optional): 输出文件名
            options (Dict[str, Any], optional): 转换选项
            user_id (int, optional): 用户ID
            
        Returns:
            Dict[str, Any]: 任务信息
        """
        # 生成任务ID
        task_id = f"task_{str(uuid.uuid4())}"
        
        # 创建任务状态
        task_status = {
            "task_id": task_id,
            "user_id": user_id,
            "upload_id": upload_id,
            "status": "pending",
            "progress": 0,
            "message": "任务已创建，等待处理",
            "target_format": target_format,
            "options": options or {},
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        }
        
        # 保存任务状态
        self.task_status[task_id] = task_status
        
        # 将任务添加到队列
        _TASK_QUEUE.put({
            "task_id": task_id,
            "upload_id": upload_id,
            "target_format": target_format,
            "output_file_name": output_file_name,
            "options": options,
            "user_id": user_id
        })
        
        logger.info(f"已创建转换任务: {task_id}")
        
        return task_status 