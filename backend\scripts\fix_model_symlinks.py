#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WAN模型符号链接修复工具

该脚本用于创建从models_cache目录到local_models目录的符号链接，
确保WAN 2.1模型可以被正确找到和加载。
"""

import os
import sys
import shutil
import logging
import subprocess
import platform
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 获取项目根目录
PROJECT_ROOT = Path(__file__).parent.parent.parent.absolute()
logger.info(f"项目根目录: {PROJECT_ROOT}")

# 模型路径
MODELS_CACHE_DIR = os.path.join(PROJECT_ROOT, "backend", "models_cache")
LOCAL_MODELS_DIR = os.path.join(PROJECT_ROOT, "backend", "local_models", "wan", "Wan2.1")

# 模型映射
MODEL_MAPPINGS = [
    {
        "cache_path": os.path.join(MODELS_CACHE_DIR, "Wan2.1-T2V-1.3B"),
        "local_path": os.path.join(LOCAL_MODELS_DIR, "Wan2.1-T2V-1.3B")
    },
    {
        "cache_path": os.path.join(MODELS_CACHE_DIR, "Wan2.1-T2V-14B"),
        "local_path": os.path.join(LOCAL_MODELS_DIR, "Wan2.1-T2V-14B")
    }
]

def check_directory_exists(dir_path):
    """检查目录是否存在"""
    exists = os.path.exists(dir_path)
    if exists:
        logger.info(f"目录存在: {dir_path}")
    else:
        logger.warning(f"目录不存在: {dir_path}")
    return exists

def create_symlink_windows(source, target):
    """在Windows上创建符号链接"""
    try:
        # 检查目标是否存在
        if os.path.exists(target):
            logger.info(f"移除现有目标: {target}")
            try:
                # 尝试直接使用cmd命令移除
                subprocess.run(["cmd", "/c", f"rmdir /q /s \"{target}\""], check=False)
                # 检查是否真的被删除
                if os.path.exists(target):
                    logger.warning(f"命令行删除失败，尝试使用Python API")
                    if os.path.isdir(target) and not os.path.islink(target):
                        shutil.rmtree(target, ignore_errors=True)
                    else:
                        os.remove(target)
            except Exception as e:
                logger.error(f"移除目标失败: {e}")
                return False
        
        # 创建符号链接之前先检查
        if os.path.exists(target):
            logger.error(f"无法移除现有目标，无法继续: {target}")
            return False
        
        # 使用mklink创建目录符号链接
        logger.info(f"创建符号链接: {target} -> {source}")
        # 先确保目标目录的父目录存在
        os.makedirs(os.path.dirname(target), exist_ok=True)
        result = subprocess.run(["cmd", "/c", f"mklink /D \"{target}\" \"{source}\""], 
                      capture_output=True, text=True, check=False)
        
        # 检查命令输出
        if result.returncode != 0:
            logger.error(f"创建符号链接失败: {result.stderr}")
            return False
            
        logger.info(f"符号链接创建成功: {result.stdout}")
        return True
    except Exception as e:
        logger.error(f"创建符号链接过程中出错: {e}")
        return False

def create_symlink_unix(source, target):
    """在Unix系统上创建符号链接"""
    if os.path.exists(target):
        logger.info(f"移除现有目标: {target}")
        try:
            if os.path.isdir(target) and not os.path.islink(target):
                shutil.rmtree(target)
            else:
                os.remove(target)
        except Exception as e:
            logger.error(f"移除目标失败: {e}")
            return False
    
    try:
        # 创建符号链接
        os.makedirs(os.path.dirname(target), exist_ok=True)
        logger.info(f"创建符号链接: {target} -> {source}")
        os.symlink(source, target)
        logger.info(f"符号链接创建成功")
        return True
    except Exception as e:
        logger.error(f"创建符号链接失败: {e}")
        return False

def create_symlink(source, target):
    """根据操作系统创建适当的符号链接"""
    if platform.system() == "Windows":
        return create_symlink_windows(source, target)
    else:
        return create_symlink_unix(source, target)

def create_model_symlinks():
    """创建所有模型的符号链接"""
    success_count = 0
    failed_count = 0
    
    # 确保模型缓存目录存在
    os.makedirs(MODELS_CACHE_DIR, exist_ok=True)
    
    for mapping in MODEL_MAPPINGS:
        local_path = mapping["local_path"]
        cache_path = mapping["cache_path"]
        
        logger.info(f"处理模型链接: {os.path.basename(cache_path)}")
        
        # 检查本地模型目录是否存在
        if not check_directory_exists(local_path):
            logger.warning(f"跳过符号链接创建，源目录不存在: {local_path}")
            failed_count += 1
            continue
        
        # 创建符号链接
        if create_symlink(local_path, cache_path):
            success_count += 1
        else:
            failed_count += 1
    
    return success_count, failed_count

def main():
    print("\n===== WAN模型符号链接修复工具 =====\n")
    
    print(f"系统信息:")
    print(f"  操作系统: {platform.system()} {platform.release()}")
    print(f"  Python版本: {platform.python_version()}")
    print(f"  当前用户: {os.getlogin()}")
    print(f"  工作目录: {os.getcwd()}")
    print()
    
    print(f"模型路径:")
    print(f"  缓存目录: {MODELS_CACHE_DIR}")
    print(f"  本地目录: {LOCAL_MODELS_DIR}")
    print()
    
    # 创建符号链接
    success_count, failed_count = create_model_symlinks()
    
    print("\n修复结果:")
    print(f"  成功: {success_count}")
    print(f"  失败: {failed_count}")
    print()
    
    if failed_count == 0:
        print("所有模型符号链接已成功修复！")
    else:
        print("部分模型符号链接修复失败，请检查日志。")
    
    print("\n请重新启动应用程序以应用修复。")
    print("\n===== 修复过程完成 =====\n")

if __name__ == "__main__":
    main() 