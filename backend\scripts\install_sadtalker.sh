#!/bin/bash

# SadTalker安装脚本
# 用于自动化安装SadTalker及其依赖

echo "开始安装SadTalker及其依赖..."

# 创建第三方库目录
mkdir -p third_party
cd third_party

# 检查SadTalker是否已安装
if [ -d "SadTalker" ]; then
    echo "SadTalker目录已存在，跳过克隆步骤"
else
    echo "克隆SadTalker仓库..."
    git clone https://github.com/OpenTalker/SadTalker.git
    cd SadTalker

    # 检查是否成功克隆
    if [ $? -ne 0 ]; then
        echo "错误: 无法克隆SadTalker仓库"
        exit 1
    fi

    # 安装依赖
    echo "安装SadTalker依赖..."
    pip install -r requirements.txt
    
    # 下载预训练模型
    echo "下载预训练模型..."
    bash scripts/download_models.sh
    
    # 返回上级目录
    cd ..
fi

# 添加环境变量到.env文件
echo "配置环境变量..."
cd ..
if [ -f ".env" ]; then
    # 检查SADTALKER_PATH是否已存在
    if grep -q "SADTALKER_PATH" .env; then
        echo "SADTALKER_PATH环境变量已存在，跳过添加"
    else
        echo "添加SADTALKER_PATH环境变量到.env文件"
        echo "SADTALKER_PATH=$(pwd)/third_party/SadTalker" >> .env
    fi
    
    # 检查USE_AI_GENERATION是否已存在
    if grep -q "USE_AI_GENERATION" .env; then
        echo "USE_AI_GENERATION环境变量已存在，跳过添加"
    else
        echo "添加USE_AI_GENERATION环境变量到.env文件"
        echo "USE_AI_GENERATION=true" >> .env
    fi
else
    echo "创建.env文件并添加环境变量"
    echo "SADTALKER_PATH=$(pwd)/third_party/SadTalker" > .env
    echo "USE_AI_GENERATION=true" >> .env
fi

# 创建资源目录
echo "创建资源目录..."
mkdir -p backend/assets/default_avatars
mkdir -p backend/assets/models

# 确保default.glb文件存在
if [ ! -f "frontend/public/assets/models/default.glb" ]; then
    echo "找不到默认模型，正在创建空模型文件..."
    echo "This file simulates a 3D model in GLTF Binary format. In a production environment, this would be replaced with an actual 3D model." > frontend/public/assets/models/default.glb
fi

echo "SadTalker安装和配置完成！"
echo "请运行以下命令来启动服务:"
echo "  cd backend"
echo "  uvicorn main:app --reload" 