<template>
  <div class="app-wrapper" :class="{ collapsed: isCollapsed }">
    <!-- 侧边栏 -->
    <div class="sidebar-container">
      <!-- Logo -->
      <div class="logo-container">
        <dashboard-outlined class="logo-icon" />
        <h1 class="title" v-show="!isCollapsed">管理系统</h1>
      </div>
      
      <!-- 菜单 -->
      <a-menu
        v-model:selectedKeys="selectedKeys"
        v-model:openKeys="openKeys"
        mode="inline"
        :inline-collapsed="isCollapsed"
        class="menu"
      >
        <template v-for="route in routes" :key="route.path">
          <!-- 无子菜单 -->
          <a-menu-item v-if="!route.children || route.children.length === 0" :key="route.path" @click="handleMenuClick(route)">
            <template #icon>
              <component :is="iconMap[route.meta?.icon] || UserOutlined" />
            </template>
            <span>{{ route.meta?.title }}</span>
          </a-menu-item>
          
          <!-- 有子菜单，但仅有一个可见子项 -->
          <a-menu-item 
            v-else-if="route.children.length === 1 || getVisibleChildren(route).length === 1" 
            :key="route.path + '/' + route.children[0].path"
            @click="handleMenuClick(route.children[0], route)"
          >
            <template #icon>
              <component :is="iconMap[route.children[0].meta?.icon] || UserOutlined" />
            </template>
            <span>{{ route.children[0].meta?.title }}</span>
          </a-menu-item>
          
          <!-- 有多个子菜单 -->
          <a-sub-menu v-else :key="route.path">
            <template #icon>
              <component :is="iconMap[route.meta?.icon] || UserOutlined" />
            </template>
            <template #title>{{ route.meta?.title }}</template>
            
            <a-menu-item 
              v-for="child in getVisibleChildren(route)" 
              :key="route.path + '/' + child.path"
              @click="handleMenuClick(child, route)"
            >
              <template #icon>
                <component :is="iconMap[child.meta?.icon] || UserOutlined" />
              </template>
              <span>{{ child.meta?.title }}</span>
            </a-menu-item>
          </a-sub-menu>
        </template>
      </a-menu>
    </div>
    
    <!-- 主区域 -->
    <div class="main-container">
      <!-- 头部导航 -->
      <div class="navbar">
        <div class="left">
          <menu-unfold-outlined
            v-if="isCollapsed"
            class="trigger"
            @click="toggleCollapsed"
          />
          <menu-fold-outlined
            v-else
            class="trigger"
            @click="toggleCollapsed"
          />
          
          <!-- 面包屑 -->
          <a-breadcrumb class="breadcrumb">
            <a-breadcrumb-item v-for="(item, index) in breadcrumbs" :key="index">
              {{ item.title }}
            </a-breadcrumb-item>
          </a-breadcrumb>
        </div>
        
        <div class="right">
          <!-- 全屏按钮 -->
          <div class="action-item">
            <fullscreen-outlined
              v-if="!isFullscreen"
              class="action-icon"
              @click="toggleFullscreen"
            />
            <fullscreen-exit-outlined
              v-else
              class="action-icon"
              @click="toggleFullscreen"
            />
          </div>
          
          <!-- 通知按钮 -->
          <div class="action-item">
            <a-badge :count="notificationCount">
              <bell-outlined class="action-icon" @click="showNotifications" />
            </a-badge>
          </div>
          
          <!-- 用户信息 -->
          <a-dropdown>
            <div class="user-info">
              <a-avatar>
                <template #icon><user-outlined /></template>
              </a-avatar>
              <span class="username">{{ userName }}</span>
            </div>
            <template #content>
              <a-menu>
                <a-menu-item key="profile" @click="navigateTo('/profile')">
                  <user-outlined />
                  个人中心
                </a-menu-item>
                <a-menu-divider />
                <a-menu-item key="logout" @click="handleLogout">
                  <logout-outlined />
                  退出登录
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
      </div>
      
      <!-- 主内容区 -->
      <div class="main-content">
        <router-view v-slot="{ Component }">
          <transition name="fade-transform" mode="out-in">
            <keep-alive :include="cachedViews">
              <component :is="Component" />
            </keep-alive>
          </transition>
        </router-view>
      </div>
    </div>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, computed, watch, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import {
  UserOutlined,
  MenuUnfoldOutlined,
  MenuFoldOutlined,
  DashboardOutlined,
  SettingOutlined,
  TeamOutlined,
  FileOutlined,
  MenuOutlined,
  BellOutlined,
  FullscreenOutlined,
  FullscreenExitOutlined,
  LogoutOutlined,
  MonitorOutlined
} from '@ant-design/icons-vue';

export default defineComponent({
  name: 'Layout',
  components: {
    UserOutlined,
    MenuUnfoldOutlined,
    MenuFoldOutlined,
    BellOutlined,
    FullscreenOutlined,
    FullscreenExitOutlined,
    LogoutOutlined
  },
  setup() {
    const router = useRouter();
    const route = useRoute();
    
    // 用户信息
    const userName = ref('管理员');
    const userAvatar = ref('');
    
    // 控制侧边栏收缩
    const isCollapsed = ref(false);
    
    // 菜单状态
    const selectedKeys = ref([]);
    const openKeys = ref([]);
    
    // 图标映射
    const iconMap = {
      dashboard: DashboardOutlined,
      user: UserOutlined,
      setting: SettingOutlined,
      team: TeamOutlined,
      menu: MenuOutlined,
      file: FileOutlined,
      monitor: MonitorOutlined
    };
    
    // 缓存视图
    const cachedViews = ref([]);
    
    // 全屏控制
    const isFullscreen = ref(false);
    
    // 通知数量
    const notificationCount = ref(5);
    
    // 获取路由
    const routes = computed(() => {
      return router.options.routes.filter(route => !route.meta?.hidden);
    });
    
    // 获取可见子路由
    const getVisibleChildren = (route) => {
      if (!route.children) return [];
      return route.children.filter(child => !child.meta?.hidden);
    };
    
    // 生成面包屑
    const breadcrumbs = computed(() => {
      const result = [];
      
      // 添加首页
      result.push({
        title: '首页',
        path: '/'
      });
      
      // 获取当前路径
      const currentPath = route.path;
      const pathParts = currentPath.split('/').filter(Boolean);
      
      let currentRoutes = router.options.routes;
      let currentPrefix = '';
      
      for (const part of pathParts) {
        currentPrefix = currentPrefix ? `${currentPrefix}/${part}` : `/${part}`;
        
        // 查找匹配的路由
        let matched = null;
        
        for (const r of currentRoutes) {
          if (r.path === currentPrefix || r.path === part) {
            matched = r;
            break;
          }
          
          if (r.children) {
            for (const child of r.children) {
              const fullPath = r.path === '/' ? `/${child.path}` : `${r.path}/${child.path}`;
              if (fullPath === currentPrefix || (currentPrefix === '/' + part && child.path === part)) {
                matched = child;
                if (r.meta?.title && !result.some(item => item.title === r.meta.title)) {
                  result.push({
                    title: r.meta.title,
                    path: r.path
                  });
                }
                break;
              }
            }
            
            if (matched) break;
          }
        }
        
        if (matched && matched.meta?.title) {
          result.push({
            title: matched.meta.title,
            path: currentPrefix
          });
        }
        
        if (matched && matched.children) {
          currentRoutes = matched.children;
        }
      }
      
      return result;
    });
    
    // 切换侧边栏收缩状态
    const toggleCollapsed = () => {
      isCollapsed.value = !isCollapsed.value;
      
      // 保存到本地存储
      localStorage.setItem('sidebarStatus', isCollapsed.value ? '1' : '0');
    };
    
    // 切换全屏状态
    const toggleFullscreen = () => {
      if (!document.fullscreenElement) {
        document.documentElement.requestFullscreen();
        isFullscreen.value = true;
      } else {
        if (document.exitFullscreen) {
          document.exitFullscreen();
          isFullscreen.value = false;
        }
      }
    };
    
    // 处理菜单点击
    const handleMenuClick = (item, parent) => {
      const path = parent ? `${parent.path}/${item.path}` : item.path;
      router.push(path);
    };
    
    // 导航到指定路径
    const navigateTo = (path) => {
      router.push(path);
    };
    
    // 处理退出登录
    const handleLogout = () => {
      localStorage.removeItem('token');
      router.push('/login');
    };
    
    // 显示通知
    const showNotifications = () => {
      // TODO: 实现通知展示逻辑
      notificationCount.value = 0;
    };
    
    // 监听路由变化，更新选中菜单
    watch(() => route.path, (newPath) => {
      selectedKeys.value = [newPath];
      
      // 获取父路径
      const parts = newPath.split('/').filter(Boolean);
      if (parts.length > 1) {
        const parentPath = '/' + parts[0];
        if (!openKeys.value.includes(parentPath)) {
          openKeys.value.push(parentPath);
        }
      }
      
      // 更新缓存视图
      const name = route.name;
      if (name && !cachedViews.value.includes(name)) {
        cachedViews.value.push(name);
      }
    }, { immediate: true });
    
    // 组件挂载时的操作
    onMounted(() => {
      // 从本地存储获取侧边栏状态
      const sidebarStatus = localStorage.getItem('sidebarStatus');
      isCollapsed.value = sidebarStatus === '1';
      
      // 监听全屏变化事件
      document.addEventListener('fullscreenchange', () => {
        isFullscreen.value = !!document.fullscreenElement;
      });
    });
    
    return {
      routes,
      userName,
      userAvatar,
      isCollapsed,
      selectedKeys,
      openKeys,
      iconMap,
      breadcrumbs,
      isFullscreen,
      notificationCount,
      cachedViews,
      toggleCollapsed,
      getVisibleChildren,
      handleMenuClick,
      toggleFullscreen,
      showNotifications,
      navigateTo,
      handleLogout
    };
  }
});
</script>

<style lang="less" scoped>
.app-wrapper {
  position: relative;
  height: 100vh;
  width: 100%;
  display: flex;
  
  /* 侧边栏 */
  .sidebar-container {
    width: 220px;
    height: 100%;
    background-color: #001529;
    transition: width 0.3s;
    overflow-y: auto;
    overflow-x: hidden;
    
    .logo-container {
      height: 64px;
      padding: 0 16px;
      display: flex;
      align-items: center;
      overflow: hidden;
      
      .logo-icon {
        font-size: 28px;
        color: #fff;
        margin-right: 10px;
      }
      
      .title {
        color: #fff;
        font-size: 18px;
        margin: 0 0 0 12px;
        white-space: nowrap;
      }
    }
    
    .menu {
      border-right: none;
    }
  }
  
  /* 主区域 */
  .main-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow-x: hidden;
    
    /* 导航栏 */
    .navbar {
      height: 64px;
      background-color: #fff;
      box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 16px;
      
      .left {
        display: flex;
        align-items: center;
        
        .trigger {
          font-size: 18px;
          cursor: pointer;
          margin-right: 16px;
          transition: color 0.3s;
          
          &:hover {
            color: #1890ff;
          }
        }
        
        .breadcrumb {
          margin-left: 8px;
        }
      }
      
      .right {
        display: flex;
        align-items: center;
        
        .action-item {
          padding: 0 12px;
          cursor: pointer;
          transition: all 0.3s;
          
          &:hover {
            background-color: rgba(0, 0, 0, 0.025);
          }
          
          .action-icon {
            font-size: 18px;
          }
        }
        
        .user-info {
          padding: 0 12px;
          cursor: pointer;
          display: flex;
          align-items: center;
          
          .username {
            margin-left: 8px;
          }
          
          &:hover {
            background-color: rgba(0, 0, 0, 0.025);
          }
        }
      }
    }
    
    /* 内容区 */
    .main-content {
      flex: 1;
      padding: 24px;
      background-color: #f0f2f5;
      overflow-y: auto;
    }
  }
  
  /* 收缩时的样式 */
  &.collapsed {
    .sidebar-container {
      width: 80px;
    }
  }
}

/* 路由过渡动画 */
.fade-transform-enter-active,
.fade-transform-leave-active {
  transition: all 0.3s;
}

.fade-transform-enter-from {
  opacity: 0;
  transform: translateX(30px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(-30px);
}
</style> 