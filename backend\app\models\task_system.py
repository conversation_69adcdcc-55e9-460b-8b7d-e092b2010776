"""
统一任务管理系统模型
"""

from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, Float, JSON, Enum
from sqlalchemy.sql import func
from app.core.sqlalchemy_db import Base
import enum

class TaskType(enum.Enum):
    """任务类型枚举"""
    DIGITAL_HUMAN = "digital_human"           # 数字人生成
    TRANSLATION = "translation"               # 翻译任务
    INTELLIGENT_AGENT = "intelligent_agent"   # 智能体任务
    TTS = "tts"                              # 语音合成
    STT = "stt"                              # 语音识别
    IMAGE_GENERATION = "image_generation"     # 图像生成
    VIDEO_GENERATION = "video_generation"     # 视频生成
    VOICE_CLONING = "voice_cloning"          # 语音克隆
    LIP_SYNC = "lip_sync"                    # 唇型同步
    GESTURE_ANIMATION = "gesture_animation"   # 手势动画
    EYE_CONTACT = "eye_contact"              # 眼神交流
    FACE_ANIMATION = "face_animation"        # 面部动画

class TaskStatus(enum.Enum):
    """任务状态枚举"""
    PENDING = "pending"         # 等待中
    QUEUED = "queued"          # 已排队
    RUNNING = "running"        # 执行中
    PAUSED = "paused"          # 已暂停
    COMPLETED = "completed"    # 已完成
    FAILED = "failed"          # 失败
    CANCELLED = "cancelled"    # 已取消
    TIMEOUT = "timeout"        # 超时

class TaskPriority(enum.Enum):
    """任务优先级枚举"""
    LOW = 1
    NORMAL = 5
    HIGH = 8
    URGENT = 10

class UnifiedTask(Base):
    """统一任务模型"""
    __tablename__ = "unified_tasks"

    id = Column(Integer, primary_key=True, index=True)
    
    # 任务基本信息
    task_id = Column(String(100), unique=True, nullable=False, comment="任务唯一ID")
    task_type = Column(Enum(TaskType), nullable=False, comment="任务类型")
    task_name = Column(String(200), nullable=False, comment="任务名称")
    description = Column(Text, comment="任务描述")
    
    # 任务状态
    status = Column(Enum(TaskStatus), default=TaskStatus.PENDING, comment="任务状态")
    priority = Column(Enum(TaskPriority), default=TaskPriority.NORMAL, comment="任务优先级")
    progress = Column(Integer, default=0, comment="进度百分比")
    
    # 任务配置
    input_data = Column(JSON, comment="输入数据")
    output_data = Column(JSON, comment="输出数据")
    config = Column(JSON, comment="任务配置")
    
    # 执行信息
    celery_task_id = Column(String(100), comment="Celery任务ID")
    worker_name = Column(String(100), comment="执行的Worker名称")
    queue_name = Column(String(50), comment="队列名称")
    
    # 时间信息
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    started_at = Column(DateTime(timezone=True), comment="开始时间")
    completed_at = Column(DateTime(timezone=True), comment="完成时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")
    
    # 性能指标
    estimated_duration = Column(Float, comment="预估时长(秒)")
    actual_duration = Column(Float, comment="实际时长(秒)")
    
    # 错误信息
    error_message = Column(Text, comment="错误信息")
    error_details = Column(JSON, comment="错误详情")
    retry_count = Column(Integer, default=0, comment="重试次数")
    max_retries = Column(Integer, default=3, comment="最大重试次数")
    
    # 资源使用
    cpu_usage = Column(Float, comment="CPU使用率")
    memory_usage = Column(Float, comment="内存使用量(MB)")
    gpu_usage = Column(Float, comment="GPU使用率")
    
    # 关联信息
    parent_task_id = Column(String(100), comment="父任务ID")
    related_entity_id = Column(Integer, comment="关联实体ID")
    related_entity_type = Column(String(50), comment="关联实体类型")
    
    # 用户信息
    created_by = Column(Integer, comment="创建者ID")
    
    # 标签和分类
    tags = Column(JSON, comment="任务标签")
    category = Column(String(50), comment="任务分类")
    
    def to_dict(self):
        """转换为字典"""
        return {
            "id": self.id,
            "task_id": self.task_id,
            "task_type": self.task_type.value if self.task_type else None,
            "task_name": self.task_name,
            "description": self.description,
            "status": self.status.value if self.status else None,
            "priority": self.priority.value if self.priority else None,
            "progress": self.progress,
            "input_data": self.input_data,
            "output_data": self.output_data,
            "config": self.config,
            "celery_task_id": self.celery_task_id,
            "worker_name": self.worker_name,
            "queue_name": self.queue_name,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "estimated_duration": self.estimated_duration,
            "actual_duration": self.actual_duration,
            "error_message": self.error_message,
            "error_details": self.error_details,
            "retry_count": self.retry_count,
            "max_retries": self.max_retries,
            "cpu_usage": self.cpu_usage,
            "memory_usage": self.memory_usage,
            "gpu_usage": self.gpu_usage,
            "parent_task_id": self.parent_task_id,
            "related_entity_id": self.related_entity_id,
            "related_entity_type": self.related_entity_type,
            "created_by": self.created_by,
            "tags": self.tags,
            "category": self.category
        }

class TaskDependency(Base):
    """任务依赖关系"""
    __tablename__ = "task_dependencies"
    
    id = Column(Integer, primary_key=True, index=True)
    task_id = Column(String(100), nullable=False, comment="任务ID")
    depends_on_task_id = Column(String(100), nullable=False, comment="依赖的任务ID")
    dependency_type = Column(String(20), default="sequential", comment="依赖类型")  # sequential, parallel, conditional
    condition = Column(JSON, comment="依赖条件")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")

class TaskLog(Base):
    """任务日志"""
    __tablename__ = "task_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    task_id = Column(String(100), nullable=False, comment="任务ID")
    level = Column(String(10), nullable=False, comment="日志级别")  # DEBUG, INFO, WARNING, ERROR
    message = Column(Text, nullable=False, comment="日志消息")
    details = Column(JSON, comment="详细信息")
    timestamp = Column(DateTime(timezone=True), server_default=func.now(), comment="时间戳")
    source = Column(String(50), comment="日志来源")

class TaskMetrics(Base):
    """任务性能指标"""
    __tablename__ = "task_metrics"
    
    id = Column(Integer, primary_key=True, index=True)
    task_id = Column(String(100), nullable=False, comment="任务ID")
    metric_name = Column(String(50), nullable=False, comment="指标名称")
    metric_value = Column(Float, nullable=False, comment="指标值")
    unit = Column(String(20), comment="单位")
    timestamp = Column(DateTime(timezone=True), server_default=func.now(), comment="时间戳")
    
class TaskQueue(Base):
    """任务队列配置"""
    __tablename__ = "task_queues"
    
    id = Column(Integer, primary_key=True, index=True)
    queue_name = Column(String(50), unique=True, nullable=False, comment="队列名称")
    description = Column(Text, comment="队列描述")
    max_workers = Column(Integer, default=1, comment="最大Worker数量")
    priority_weight = Column(Integer, default=1, comment="优先级权重")
    task_types = Column(JSON, comment="支持的任务类型")
    is_active = Column(Boolean, default=True, comment="是否激活")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")
