#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
设置SadTalker环境变量和目录结构
"""

import os
import sys
import argparse
import logging
import shutil
import subprocess
from pathlib import Path
import dotenv

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def find_repo_root():
    """查找项目根目录"""
    current_dir = os.path.abspath(os.getcwd())
    
    # 向上查找.git目录或包含setup.py的目录
    max_levels = 5
    for _ in range(max_levels):
        if (os.path.exists(os.path.join(current_dir, '.git')) or 
            os.path.exists(os.path.join(current_dir, 'setup.py')) or
            os.path.exists(os.path.join(current_dir, 'pyproject.toml')) or
            os.path.exists(os.path.join(current_dir, 'backend')) and os.path.exists(os.path.join(current_dir, 'frontend'))):
            return current_dir
        
        parent_dir = os.path.dirname(current_dir)
        if parent_dir == current_dir:
            break
        current_dir = parent_dir
    
    # 如果找不到根目录，使用当前目录
    return os.path.abspath(os.getcwd())

def get_backend_dir(repo_root):
    """获取后端目录"""
    backend_dir = os.path.join(repo_root, 'backend')
    if os.path.exists(backend_dir) and os.path.isdir(backend_dir):
        return backend_dir
    
    # 如果已经在backend目录中
    if os.path.basename(repo_root) == 'backend':
        return repo_root
    
    return backend_dir

def setup_sadtalker_env(args):
    """设置SadTalker环境"""
    # 查找项目根目录
    repo_root = find_repo_root()
    logger.info(f"项目根目录: {repo_root}")
    
    # 获取后端目录
    backend_dir = get_backend_dir(repo_root)
    logger.info(f"后端目录: {backend_dir}")
    
    # 设置SadTalker目录
    if args.sadtalker_path:
        sadtalker_dir = args.sadtalker_path
    else:
        # 默认在backend/scripts/third_party/SadTalker
        sadtalker_dir = os.path.join(backend_dir, 'scripts', 'third_party', 'SadTalker')
    
    # 确保路径格式统一
    sadtalker_dir = os.path.abspath(sadtalker_dir)
    logger.info(f"SadTalker目录: {sadtalker_dir}")
    
    # 创建目录结构
    os.makedirs(os.path.dirname(sadtalker_dir), exist_ok=True)
    
    # 写入环境变量
    env_file = os.path.join(repo_root, '.env')
    if os.path.exists(env_file):
        # 加载现有环境变量
        dotenv.load_dotenv(env_file)
        env_vars = {}
        try:
            with open(env_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#'):
                        try:
                            key, value = line.split('=', 1)
                            env_vars[key] = value
                        except ValueError:
                            continue
        except UnicodeDecodeError:
            # 尝试不同的编码
            try:
                with open(env_file, 'r', encoding='gbk') as f:
                    for line in f:
                        line = line.strip()
                        if line and not line.startswith('#'):
                            try:
                                key, value = line.split('=', 1)
                                env_vars[key] = value
                            except ValueError:
                                continue
            except Exception as e:
                logger.error(f"读取.env文件失败，将创建新文件: {e}")
                env_vars = {}
        
        # 更新SadTalker路径
        env_vars['SADTALKER_PATH'] = sadtalker_dir
        env_vars['USE_AI_GENERATION'] = 'true'
        
        # 写回文件
        with open(env_file, 'w', encoding='utf-8') as f:
            for key, value in env_vars.items():
                f.write(f"{key}={value}\n")
        
        logger.info(f"已更新环境变量文件: {env_file}")
    else:
        # 创建新的环境变量文件
        with open(env_file, 'w', encoding='utf-8') as f:
            f.write(f"SADTALKER_PATH={sadtalker_dir}\n")
            f.write("USE_AI_GENERATION=true\n")
        
        logger.info(f"已创建环境变量文件: {env_file}")
    
    # 加载环境变量
    os.environ['SADTALKER_PATH'] = sadtalker_dir
    os.environ['USE_AI_GENERATION'] = 'true'
    
    # 克隆SadTalker仓库（如果不存在）
    if not os.path.exists(sadtalker_dir):
        if args.clone:
            logger.info(f"克隆SadTalker仓库到: {sadtalker_dir}")
            os.makedirs(sadtalker_dir, exist_ok=True)
            
            try:
                subprocess.run(
                    ["git", "clone", "https://github.com/OpenTalker/SadTalker.git", sadtalker_dir],
                    check=True
                )
                logger.info("SadTalker仓库克隆成功")
            except subprocess.CalledProcessError as e:
                logger.error(f"克隆SadTalker仓库失败: {e}")
                return False
            except Exception as e:
                logger.error(f"克隆SadTalker仓库时出错: {e}")
                return False
        else:
            logger.info(f"创建SadTalker目录: {sadtalker_dir}")
            os.makedirs(sadtalker_dir, exist_ok=True)
            # 创建一个README文件，说明如何安装
            readme_path = os.path.join(sadtalker_dir, 'README.md')
            with open(readme_path, 'w', encoding='utf-8') as f:
                f.write("# SadTalker\n\n")
                f.write("请运行以下命令安装SadTalker:\n\n")
                f.write("```bash\n")
                f.write("git clone https://github.com/OpenTalker/SadTalker.git .\n")
                f.write("pip install -r requirements.txt\n")
                if os.name == 'nt':  # Windows
                    f.write("scripts\\download_models.bat\n")
                else:  # Linux/Mac
                    f.write("bash scripts/download_models.sh\n")
                f.write("```\n")
            logger.info(f"已创建README文件: {readme_path}")
    else:
        logger.info(f"SadTalker目录已存在: {sadtalker_dir}")
    
    return True

def main():
    parser = argparse.ArgumentParser(description="设置SadTalker环境")
    parser.add_argument("--sadtalker-path", help="SadTalker路径")
    parser.add_argument("--clone", action="store_true", help="克隆SadTalker仓库")
    parser.add_argument("--force", action="store_true", help="强制更新")
    args = parser.parse_args()
    
    if setup_sadtalker_env(args):
        logger.info("SadTalker环境设置成功")
    else:
        logger.error("SadTalker环境设置失败")
        sys.exit(1)

if __name__ == "__main__":
    main() 