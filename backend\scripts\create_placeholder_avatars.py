#!/usr/bin/env python3
"""
创建占位头像脚本
为头像管理功能创建一些占位图片
"""

import os
from pathlib import Path
from PIL import Image, ImageDraw, ImageFont
import json

# 项目根目录
PROJECT_ROOT = Path(__file__).parent.parent.parent
AVATAR_STORAGE_PATH = PROJECT_ROOT / "backend" / "storage" / "avatars"

# 头像配置
AVATAR_CONFIGS = {
    "female-teacher": {
        "name": "女性教师",
        "color": "#FF6B9D",
        "text": "教师"
    },
    "male-business": {
        "name": "男性商务", 
        "color": "#4ECDC4",
        "text": "商务"
    },
    "female-service": {
        "name": "女性客服",
        "color": "#45B7D1", 
        "text": "客服"
    },
    "male-doctor": {
        "name": "男性医生",
        "color": "#96CEB4",
        "text": "医生"
    },
    "female-news": {
        "name": "女性主播",
        "color": "#FFEAA7",
        "text": "主播"
    }
}

def create_placeholder_image(text, color, size=(512, 512)):
    """创建占位图片"""
    # 创建图片
    img = Image.new('RGB', size, color=color)
    draw = ImageDraw.Draw(img)
    
    # 尝试使用系统字体
    try:
        # Windows系统字体
        font_size = 80
        font = ImageFont.truetype("msyh.ttc", font_size)  # 微软雅黑
    except:
        try:
            # 备用字体
            font = ImageFont.truetype("arial.ttf", 60)
        except:
            # 默认字体
            font = ImageFont.load_default()
    
    # 计算文字位置（居中）
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    
    x = (size[0] - text_width) // 2
    y = (size[1] - text_height) // 2
    
    # 绘制文字
    draw.text((x, y), text, fill='white', font=font)
    
    return img

def create_avatar_info(avatar_type, config):
    """创建头像信息文件"""
    info = {
        "type": avatar_type,
        "name": config["name"],
        "description": f"占位头像 - {config['name']}",
        "size": 512 * 512 * 3,  # 估算大小
        "resolution": "512x512",
        "format": "JPEG",
        "created_at": "2025-07-18T16:00:00Z",
        "is_placeholder": True
    }
    
    info_file = AVATAR_STORAGE_PATH / f"{avatar_type}_info.json"
    with open(info_file, "w", encoding="utf-8") as f:
        json.dump(info, f, indent=2, ensure_ascii=False)
    
    return info

def main():
    """主函数"""
    print("=" * 50)
    print("创建占位头像")
    print("=" * 50)
    
    # 创建头像存储目录
    AVATAR_STORAGE_PATH.mkdir(parents=True, exist_ok=True)
    
    created_count = 0
    
    for avatar_type, config in AVATAR_CONFIGS.items():
        avatar_path = AVATAR_STORAGE_PATH / f"{avatar_type}.jpg"
        
        # 如果头像已存在，跳过
        if avatar_path.exists():
            print(f"头像已存在，跳过: {avatar_type}")
            continue
        
        try:
            # 创建占位图片
            img = create_placeholder_image(config["text"], config["color"])
            
            # 保存图片
            img.save(avatar_path, "JPEG", quality=85)
            
            # 创建信息文件
            create_avatar_info(avatar_type, config)
            
            print(f"创建头像: {avatar_type} -> {avatar_path}")
            created_count += 1
            
        except Exception as e:
            print(f"创建头像失败 {avatar_type}: {e}")
    
    # 创建通用占位图
    placeholder_path = AVATAR_STORAGE_PATH / "placeholder.jpg"
    if not placeholder_path.exists():
        try:
            placeholder_img = create_placeholder_image("头像", "#CCCCCC")
            placeholder_img.save(placeholder_path, "JPEG", quality=85)
            print(f"创建通用占位图: {placeholder_path}")
            created_count += 1
        except Exception as e:
            print(f"创建通用占位图失败: {e}")
    
    print("\n" + "=" * 50)
    print(f"完成! 创建了 {created_count} 个头像文件")
    print(f"头像存储路径: {AVATAR_STORAGE_PATH}")
    print("=" * 50)

if __name__ == "__main__":
    main()
