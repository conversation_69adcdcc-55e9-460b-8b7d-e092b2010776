#!/usr/bin/env python3
"""
CPU优化的MuseTalk推理脚本
专为CPU环境设计，快速生成数字人视频
"""

import os
import sys
import argparse
import logging
import cv2
import numpy as np
from pathlib import Path
import tempfile
import librosa
import subprocess

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='CPU优化的MuseTalk数字人生成')
    parser.add_argument('--source_image', required=True, help='源图像路径')
    parser.add_argument('--driving_audio', required=True, help='驱动音频路径')
    parser.add_argument('--output', required=True, help='输出视频路径')
    parser.add_argument('--device', default='cpu', help='设备类型')
    parser.add_argument('--fps', type=int, default=25, help='输出视频帧率')
    parser.add_argument('--quality', default='medium', help='输出质量')
    return parser.parse_args()

def extract_audio_features_fast(audio_path, fps=25, max_duration=4.0):
    """快速提取音频特征 (限制时长)"""
    try:
        # 加载音频，限制时长
        audio, sr = librosa.load(audio_path, sr=16000, duration=max_duration)
        
        if len(audio) == 0:
            raise ValueError("音频文件为空")
        
        duration = len(audio) / sr
        total_frames = int(duration * fps)
        
        logger.info(f"音频时长: {duration:.2f}秒, 总帧数: {total_frames}")
        
        # 使用更快的特征提取
        hop_length = int(sr / fps)
        
        # 只提取必要的特征
        rms = librosa.feature.rms(y=audio, hop_length=hop_length)[0]
        zcr = librosa.feature.zero_crossing_rate(audio, hop_length=hop_length)[0]
        
        # 简化的MFCC (减少系数)
        mfcc = librosa.feature.mfcc(y=audio, sr=sr, n_mfcc=5, hop_length=hop_length)
        
        # 确保长度一致
        min_length = min(len(rms), len(zcr), mfcc.shape[1])
        
        features = []
        for i in range(min_length):
            frame_features = {
                'rms': rms[i],
                'zcr': zcr[i],
                'mfcc': mfcc[:, i],
                'frame_index': i
            }
            features.append(frame_features)
        
        logger.info(f"✅ 快速提取了 {len(features)} 帧音频特征")
        return features
        
    except Exception as e:
        logger.error(f"音频特征提取失败: {e}")
        raise

def detect_face_region_fast(image):
    """快速检测面部区域"""
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    
    # 使用更快的面部检测
    face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
    faces = face_cascade.detectMultiScale(gray, 1.2, 3, minSize=(50, 50))
    
    if len(faces) > 0:
        # 选择最大的面部
        face = max(faces, key=lambda x: x[2] * x[3])
        fx, fy, fw, fh = face
        
        # 计算嘴部区域
        mouth_x = fx + int(fw * 0.35)
        mouth_y = fy + int(fh * 0.7)
        mouth_w = int(fw * 0.3)
        mouth_h = int(fh * 0.12)
        
        return {
            'face': (fx, fy, fw, fh),
            'mouth': (mouth_x, mouth_y, mouth_w, mouth_h)
        }
    
    # 如果没有检测到面部，使用默认区域
    height, width = image.shape[:2]
    return {
        'face': (width//4, height//4, width//2, height//2),
        'mouth': (int(width*0.4), int(height*0.7), int(width*0.2), int(height*0.1))
    }

def apply_fast_lip_sync(image, face_regions, audio_feature):
    """应用快速唇形同步"""
    mouth_x, mouth_y, mouth_w, mouth_h = face_regions['mouth']
    
    # 计算音频强度
    rms = audio_feature.get('rms', 0)
    zcr = audio_feature.get('zcr', 0)
    
    # 综合强度
    intensity = np.clip((rms * 3 + zcr * 2), 0, 1)
    
    # 如果强度很低，不做修改
    if intensity < 0.05:
        return image
    
    # 确保区域在图像范围内
    height, width = image.shape[:2]
    x1 = max(0, mouth_x)
    y1 = max(0, mouth_y)
    x2 = min(width, mouth_x + mouth_w)
    y2 = min(height, mouth_y + mouth_h)
    
    if x2 <= x1 or y2 <= y1:
        return image
    
    # 提取嘴部区域
    mouth_region = image[y1:y2, x1:x2].copy()
    
    if mouth_region.size == 0:
        return image
    
    # 应用非常轻微的效果
    if intensity > 0.1:
        # 1. 轻微的亮度调整
        brightness = int(intensity * 5)  # 很小的变化
        mouth_region = cv2.convertScaleAbs(mouth_region, alpha=1.0, beta=brightness)
        
        # 2. 轻微的对比度调整
        if intensity > 0.3:
            contrast = 1.0 + intensity * 0.05
            mouth_region = cv2.convertScaleAbs(mouth_region, alpha=contrast, beta=0)
        
        # 3. 轻微的色彩调整 (只在高强度时)
        if intensity > 0.5:
            # 增加红色通道
            mouth_region[:, :, 2] = np.clip(mouth_region[:, :, 2] + int(intensity * 3), 0, 255)
    
    # 将处理后的区域放回原图
    image[y1:y2, x1:x2] = mouth_region
    
    return image

def generate_cpu_optimized_video(source_image, audio_path, output_path, fps=25, quality='medium'):
    """生成CPU优化的数字人视频"""
    try:
        # 读取源图像
        image = cv2.imread(source_image)
        if image is None:
            raise ValueError(f"无法读取图像: {source_image}")
        
        # 调整图像大小以提高处理速度
        target_height = 480  # 降低分辨率
        height, width = image.shape[:2]
        if height > target_height:
            scale = target_height / height
            new_width = int(width * scale)
            image = cv2.resize(image, (new_width, target_height))
            logger.info(f"图像已调整为: {new_width}x{target_height}")
        
        # 快速检测面部区域
        logger.info("🔍 快速检测面部区域...")
        face_regions = detect_face_region_fast(image)
        
        # 快速提取音频特征
        logger.info("🎵 快速提取音频特征...")
        audio_features = extract_audio_features_fast(audio_path, fps, max_duration=4.0)
        
        # 限制帧数
        max_frames = min(len(audio_features), 100)  # 最多100帧
        audio_features = audio_features[:max_frames]
        
        logger.info(f"🎬 生成 {max_frames} 帧优化视频...")
        
        # 创建临时目录
        with tempfile.TemporaryDirectory() as temp_dir:
            frames_dir = Path(temp_dir) / "frames"
            frames_dir.mkdir()
            
            # 快速生成帧
            for i, audio_feature in enumerate(audio_features):
                # 应用快速唇形同步
                frame = apply_fast_lip_sync(image.copy(), face_regions, audio_feature)
                
                # 保存帧
                frame_path = frames_dir / f"frame_{i:06d}.png"
                cv2.imwrite(str(frame_path), frame)
                
                if i % 20 == 0:
                    progress = (i / max_frames) * 100
                    logger.info(f"生成进度: {i}/{max_frames} ({progress:.1f}%)")
            
            # 快速合成视频
            logger.info("🎥 快速合成视频...")
            return create_fast_video(frames_dir, audio_path, output_path, fps, quality)
            
    except Exception as e:
        logger.error(f"CPU优化视频生成失败: {e}")
        raise

def create_fast_video(frames_dir, audio_path, output_path, fps, quality):
    """快速创建视频"""
    try:
        # 根据质量设置编码参数
        if quality == 'high':
            crf = '20'
            preset = 'medium'
        elif quality == 'medium':
            crf = '25'
            preset = 'fast'
        else:
            crf = '30'
            preset = 'ultrafast'
        
        # 构建FFmpeg命令 (优化CPU使用)
        cmd = [
            'ffmpeg', '-y',
            '-framerate', str(fps),
            '-i', str(frames_dir / 'frame_%06d.png'),
            '-i', audio_path,
            '-c:v', 'libx264',
            '-c:a', 'aac',
            '-pix_fmt', 'yuv420p',
            '-preset', preset,
            '-crf', crf,
            '-threads', '4',  # 限制线程数
            '-shortest',
            '-movflags', '+faststart',
            '-r', str(fps),
            str(output_path)
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            file_size = Path(output_path).stat().st_size
            logger.info(f"✅ CPU优化视频创建成功: {output_path}")
            logger.info(f"视频文件大小: {file_size} bytes")
            return True
        else:
            logger.error(f"FFmpeg错误: {result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"视频创建失败: {e}")
        return False

def main():
    """主函数"""
    args = parse_args()
    
    logger.info("🚀 开始CPU优化的MuseTalk数字人生成...")
    logger.info(f"源图像: {args.source_image}")
    logger.info(f"驱动音频: {args.driving_audio}")
    logger.info(f"输出视频: {args.output}")
    logger.info(f"质量: {args.quality}")
    
    try:
        # 生成CPU优化视频
        success = generate_cpu_optimized_video(
            args.source_image,
            args.driving_audio,
            args.output,
            args.fps,
            args.quality
        )
        
        if success:
            logger.info("🎉 CPU优化的MuseTalk数字人生成完成！")
            logger.info(f"输出文件: {args.output}")
        else:
            raise Exception("视频生成失败")
            
    except Exception as e:
        logger.error(f"CPU优化MuseTalk推理失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
