"""
通用后台任务处理模块
提供统一的任务处理基础设施
"""
import logging
import time
import asyncio
import uuid
from typing import Dict, Any, Callable, Optional
from datetime import datetime
from celery import shared_task
from celery.signals import task_prerun, task_postrun, task_failure
import importlib

from services.celery_app import app
from services.progress_updater import ProgressUpdater
from services.task_adapter import get_task_adapter
from services.storage_manager import get_storage_manager
from models.task import TaskModel
from utils.db import get_db

# 配置日志
logger = logging.getLogger(__name__)

# 任务处理钩子
@task_prerun.connect
def task_prerun_handler(task_id=None, task=None, *args, **kwargs):
    """任务开始前的处理"""
    logger.info(f"开始执行任务: {task.name}[{task_id}]")

@task_postrun.connect
def task_postrun_handler(task_id=None, task=None, retval=None, state=None, *args, **kwargs):
    """任务结束后的处理"""
    logger.info(f"完成任务: {task.name}[{task_id}], 状态: {state}")

@task_failure.connect
def task_failure_handler(task_id=None, exception=None, traceback=None, *args, **kwargs):
    """任务失败处理"""
    logger.error(f"任务失败: [{task_id}], 错误: {exception}")
    # 可以在这里添加额外的失败处理逻辑，如通知或重试策略

def create_task_record(task_id: str, task_type: str, params: Dict[str, Any], user_id: Optional[int], celery_task_id: str):
    """
    创建任务记录
    
    Args:
        task_id: 任务ID
        task_type: 任务类型
        params: 任务参数
        user_id: 用户ID
        celery_task_id: Celery任务ID
    """
    try:
        # 创建数据库会话
        db = next(get_db())
        
        # 检查任务是否已存在
        existing_task = db.query(TaskModel).filter(TaskModel.task_id == task_id).first()
        if existing_task:
            # 更新现有任务
            existing_task.status = "pending"
            existing_task.progress = 0
            existing_task.celery_task_id = celery_task_id
            existing_task.input_data = params
            existing_task.started_at = None
            existing_task.completed_at = None
            existing_task.error = None
            db.commit()
            logger.info(f"更新任务记录: {task_id}")
        else:
            # 创建新任务
            new_task = TaskModel(
                task_id=task_id,
                task_type=task_type,
                status="pending",
                progress=0,
                input_data=params,
                user_id=user_id,
                celery_task_id=celery_task_id,
                created_at=datetime.now()
            )
            db.add(new_task)
            db.commit()
            logger.info(f"创建任务记录: {task_id}")
    except Exception as e:
        logger.error(f"创建/更新任务记录失败: {e}")
    finally:
        db.close()

def update_task_record(task_id: str, status: str, output_data: Optional[Dict[str, Any]] = None, error: Optional[str] = None):
    """
    更新任务记录
    
    Args:
        task_id: 任务ID
        status: 任务状态
        output_data: 输出数据
        error: 错误信息
    """
    try:
        # 创建数据库会话
        db = next(get_db())
        
        # 查询任务 - 确保使用task_id字段
        task = db.query(TaskModel).filter(TaskModel.task_id == task_id).first()
        if not task:
            logger.warning(f"任务不存在，无法更新: {task_id}")
            return
        
        # 更新任务状态
        task.status = status
        
        # 更新完成时间
        if status in ["completed", "failed"]:
            task.completed_at = datetime.now()
            task.progress = 100 if status == "completed" else 0
        
        # 更新输出数据
        if output_data:
            task.output_data = output_data
            
            # 如果有输出文件，记录到output_files字段
            if "output_files" in output_data:
                task.output_files = output_data["output_files"]
        
        # 更新错误信息 - 将字典转换为JSON字符串
        if error:
            if isinstance(error, dict):
                import json
                try:
                    task.error = json.dumps(error)
                except Exception as e:
                    logger.warning(f"错误信息序列化失败: {e}")
                    task.error = str(error)
            else:
                task.error = str(error)
        
        # 提交更改
        db.commit()
        logger.info(f"更新任务记录: {task_id}, 状态: {status}")
    except Exception as e:
        logger.error(f"更新任务记录失败: {e}")
    finally:
        db.close()

@shared_task(name="execute_general_task", bind=True, max_retries=3)
def execute_general_task(
    self,
    task_id: str,
    task_type: str,
    params: Dict[str, Any],
    user_id: Optional[int] = None
) -> Dict[str, Any]:
    """
    执行通用任务
    
    参数:
        self: Celery任务实例
        task_id: 任务ID
        task_type: 任务类型
        params: 任务参数
        user_id: 用户ID
        
    返回:
        包含任务结果的字典
    """
    start_time = time.time()
    success = False
    error_message = None
    loop = None
    progress_updater = ProgressUpdater(task_id)
    
    # 创建任务记录
    create_task_record(task_id, task_type, params, user_id, self.request.id)
    
    try:
        # 创建异步事件循环
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        # 更新初始状态
        loop.run_until_complete(progress_updater.update(
            status="initialization",
            progress=5,
            message="正在初始化..."
        ))
        
        # 获取适当的任务适配器
        adapter = get_task_adapter(task_type, progress_updater)
        
        if not adapter:
            error_message = f"未找到任务类型 {task_type} 的适配器"
            logger.error(error_message)
            
            # 更新任务状态 - 错误信息应该是字符串
            error_msg_str = str(error_message)
            loop.run_until_complete(progress_updater.update(
                status="error",
                progress=0,
                message=error_msg_str,
                error=error_msg_str
            ))
            
            # 更新任务记录
            update_task_record(task_id, "failed", None, error_msg_str)
            
            return {
                "success": False,
                "error": error_msg_str,
                "task_id": task_id
            }
        
        # 记录开始时间
        task_started_at = datetime.now()
        
        # 更新任务状态为运行中
        loop.run_until_complete(progress_updater.update(
            status="running",
            progress=10,
            message=f"开始执行{task_type}任务...",
            started_at=task_started_at.isoformat()
        ))
        
        # 执行任务
        logger.info(f"开始执行任务: {task_id}, 类型: {task_type}")
        result = loop.run_until_complete(adapter.execute(task_id, params))
        
        # 处理结果
        if result.get("success", False):
            success = True
            execution_time = time.time() - start_time
            logger.info(f"任务执行成功: {task_id}, 用时: {execution_time:.2f}秒")
            
            # 添加执行时间到结果
            if "data" not in result:
                result["data"] = {}
            result["data"]["execution_time"] = execution_time
            
            # 更新任务状态
            loop.run_until_complete(progress_updater.update(
                status="completed",
                progress=100,
                message="任务完成",
                result=result.get("data"),
                completed_at=datetime.now().isoformat()
            ))
            
            # 更新任务记录
            update_task_record(task_id, "completed", result.get("data"))
            
            return {
                "success": True,
                "task_id": task_id,
                "execution_time": execution_time,
                "data": result.get("data")
            }
        else:
            # 处理失败情况
            error_message = result.get("error", "任务执行失败")
            raise Exception(error_message)
        
    except Exception as e:
        # 异常处理
        error_message = str(e)
        logger.exception(f"任务执行异常: {task_id}, 错误: {e}")
        
        try:
            if loop is None:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
            loop.run_until_complete(progress_updater.update(
                status="error",
                progress=0,
                message="任务执行异常",
                error=error_message,
                completed_at=datetime.now().isoformat()
            ))
            
            # 更新任务记录
            update_task_record(task_id, "failed", None, error_message)
        except Exception as update_error:
            logger.error(f"更新任务状态失败: {update_error}")
        
        # 尝试重试任务
        retry_count = self.request.retries
        if retry_count < self.max_retries:
            logger.info(f"尝试重试任务: {task_id}, 当前重试次数: {retry_count + 1}")
            self.retry(exc=e)
        
        return {
            "success": False,
            "error": error_message,
            "task_id": task_id
        }
    finally:
        # 确保关闭事件循环
        try:
            if loop is not None:
                loop.close()
        except Exception as close_error:
            logger.error(f"关闭事件循环失败: {close_error}")

# 简单任务包装器 - 用于创建特定类型的任务
def task_wrapper(task_type: str):
    """
    创建特定类型任务的包装器
    
    Args:
        task_type: 任务类型
        
    Returns:
        任务函数
    """
    @shared_task(bind=True, max_retries=3, name=f"{task_type}_task")
    def wrapped_task(self, task_id: str, **kwargs):
        # 从kwargs中获取用户ID
        user_id = kwargs.pop("user_id", None)
        
        # 执行通用任务
        return execute_general_task.apply_async(
            kwargs={
                "task_id": task_id,
                "task_type": task_type,
                "params": kwargs,
                "user_id": user_id
            }
        )
    
    # 不再尝试设置__name__属性，而是使用name参数
    
    return wrapped_task

# 创建常用任务类型的快捷方式
translate_text_task = task_wrapper("text_translation")
translate_audio_task = task_wrapper("audio_translation")
translate_video_task = task_wrapper("video_translation")
text_to_speech_task = task_wrapper("text_to_speech")
speech_to_text_task = task_wrapper("speech_to_text")
text_to_video_task = task_wrapper("text_to_video")
image_to_video_task = task_wrapper("image_to_video")
digital_human_task = task_wrapper("digital_human") 