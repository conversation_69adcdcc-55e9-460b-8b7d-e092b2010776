import { get, post, put, del } from './index';

/**
 * 获取模型列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.limit - 每页数量
 * @param {string} params.keyword - 搜索关键词
 * @param {string} params.type - 模型类型
 * @param {string} params.status - 模型状态
 * @returns {Promise}
 */
export function getModelList(params) {
  return get('/models', params);
}

/**
 * 获取模型详情
 * @param {string} id - 模型ID
 * @returns {Promise}
 */
export function getModelDetail(id) {
  return get(`/models/${id}`);
}

/**
 * 创建模型
 * @param {Object} data - 模型数据
 * @returns {Promise}
 */
export function createModel(data) {
  return post('/models', data);
}

/**
 * 更新模型
 * @param {string} id - 模型ID
 * @param {Object} data - 模型数据
 * @returns {Promise}
 */
export function updateModel(id, data) {
  return put(`/models/${id}`, data);
}

/**
 * 删除模型
 * @param {string} id - 模型ID
 * @returns {Promise}
 */
export function deleteModel(id) {
  return del(`/models/${id}`);
}

/**
 * 获取模型类型列表
 * @returns {Promise}
 */
export function getModelTypes() {
  return get('/model-types');
}

/**
 * 上线模型
 * @param {string} id - 模型ID
 * @returns {Promise}
 */
export function deployModel(id) {
  return post(`/models/${id}/deploy`);
}

/**
 * 下线模型
 * @param {string} id - 模型ID
 * @returns {Promise}
 */
export function undeployModel(id) {
  return post(`/models/${id}/undeploy`);
}

/**
 * 获取模型性能指标
 * @param {string} id - 模型ID
 * @param {Object} params - 查询参数
 * @param {string} params.timeRange - 时间范围
 * @returns {Promise}
 */
export function getModelMetrics(id, params) {
  return get(`/models/${id}/metrics`, params);
}

/**
 * 获取模型使用情况
 * @param {string} id - 模型ID
 * @param {Object} params - 查询参数
 * @param {string} params.timeRange - 时间范围
 * @returns {Promise}
 */
export function getModelUsage(id, params) {
  return get(`/models/${id}/usage`, params);
}

/**
 * 获取模型版本历史
 * @param {string} id - 模型ID
 * @returns {Promise}
 */
export function getModelVersions(id) {
  return get(`/models/${id}/versions`);
}

/**
 * 回滚模型版本
 * @param {string} id - 模型ID
 * @param {string} versionId - 版本ID
 * @returns {Promise}
 */
export function rollbackModelVersion(id, versionId) {
  return post(`/models/${id}/rollback`, { versionId });
}

/**
 * 获取模型配置信息
 * @param {string} id - 模型ID
 * @returns {Promise}
 */
export function getModelConfig(id) {
  return get(`/models/${id}/config`);
}

/**
 * 更新模型配置
 * @param {string} id - 模型ID
 * @param {Object} config - 配置信息
 * @returns {Promise}
 */
export function updateModelConfig(id, config) {
  return put(`/models/${id}/config`, config);
}

/**
 * 获取模型可用资源列表
 * @returns {Promise}
 */
export function getModelResources() {
  return get('/model-resources');
}

/**
 * 分配模型资源
 * @param {string} id - 模型ID
 * @param {Object} resources - 资源配置
 * @returns {Promise}
 */
export function allocateModelResources(id, resources) {
  return post(`/models/${id}/resources`, resources);
}

export default {
  getModelList,
  getModelDetail,
  createModel,
  updateModel,
  deleteModel,
  getModelTypes,
  deployModel,
  undeployModel,
  getModelMetrics,
  getModelUsage,
  getModelVersions,
  rollbackModelVersion,
  getModelConfig,
  updateModelConfig,
  getModelResources,
  allocateModelResources
}; 