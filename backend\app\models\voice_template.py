"""
声音模板数据模型
"""

from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, Float, JSON
from sqlalchemy.sql import func
from ..core.sqlalchemy_db import Base

class VoiceTemplate(Base):
    """声音模板模型"""
    __tablename__ = "voice_templates"

    id = Column(String(50), primary_key=True, index=True)
    name = Column(String(100), nullable=False, comment="声音名称")
    description = Column(Text, comment="声音描述")
    
    # 基本属性
    gender = Column(String(10), nullable=False, comment="性别: male/female")
    age_range = Column(String(20), comment="年龄范围")
    language = Column(String(20), nullable=False, comment="语言: zh-CN/en-US等")
    accent = Column(String(50), comment="口音/方言")
    
    # 声音特征
    pitch = Column(String(20), comment="音调: low/medium/high")
    speed = Column(String(20), comment="语速: slow/normal/fast")
    tone = Column(String(50), comment="音色: warm/professional/friendly等")
    emotion = Column(String(50), comment="情感: neutral/happy/serious等")
    
    # 技术参数
    voice_engine = Column(String(50), nullable=False, comment="语音引擎: edge-tts/elevenlabs等")
    voice_id = Column(String(100), comment="引擎中的声音ID")
    model_name = Column(String(100), comment="模型名称")
    
    # 分类和场景
    category = Column(String(50), nullable=False, comment="分类: education/business/entertainment等")
    scenarios = Column(JSON, comment="适用场景列表")
    tags = Column(JSON, comment="标签数组")
    
    # 质量和评价
    quality_score = Column(Float, default=0.0, comment="质量评分 0-100")
    naturalness_score = Column(Float, default=0.0, comment="自然度评分")
    clarity_score = Column(Float, default=0.0, comment="清晰度评分")
    
    # 使用统计
    usage_count = Column(Integer, default=0, comment="使用次数")
    popularity_score = Column(Float, default=0.0, comment="受欢迎程度")
    
    # 音频文件
    sample_url = Column(String(500), comment="试听样本URL")
    sample_path = Column(String(500), comment="本地样本路径")
    sample_text = Column(Text, comment="样本文本")
    
    # 配置参数
    default_speed = Column(Float, default=1.0, comment="默认语速")
    default_pitch = Column(Float, default=1.0, comment="默认音调")
    default_volume = Column(Float, default=1.0, comment="默认音量")
    
    # 状态和管理
    is_active = Column(Boolean, default=True, comment="是否启用")
    is_featured = Column(Boolean, default=False, comment="是否推荐")
    is_premium = Column(Boolean, default=False, comment="是否高级声音")
    
    # 排序和显示
    sort_order = Column(Integer, default=0, comment="排序顺序")
    display_priority = Column(Integer, default=0, comment="显示优先级")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), onupdate=func.now(), comment="更新时间")
    
    # 创建者信息
    created_by = Column(String(100), comment="创建者")
    source = Column(String(100), comment="来源")
    
    def to_dict(self):
        """转换为字典"""
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "gender": self.gender,
            "age_range": self.age_range,
            "language": self.language,
            "accent": self.accent,
            "pitch": self.pitch,
            "speed": self.speed,
            "tone": self.tone,
            "emotion": self.emotion,
            "voice_engine": self.voice_engine,
            "voice_id": self.voice_id,
            "model_name": self.model_name,
            "category": self.category,
            "scenarios": self.scenarios or [],
            "tags": self.tags or [],
            "quality_score": self.quality_score,
            "naturalness_score": self.naturalness_score,
            "clarity_score": self.clarity_score,
            "usage_count": self.usage_count,
            "popularity_score": self.popularity_score,
            "sample_url": self.sample_url,
            "sample_path": self.sample_path,
            "sample_text": self.sample_text,
            "default_speed": self.default_speed,
            "default_pitch": self.default_pitch,
            "default_volume": self.default_volume,
            "is_active": self.is_active,
            "is_featured": self.is_featured,
            "is_premium": self.is_premium,
            "sort_order": self.sort_order,
            "display_priority": self.display_priority,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "created_by": self.created_by,
            "source": self.source
        }
