import { get, post, put, del } from './index';

/**
 * 获取任务列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.limit - 每页数量
 * @param {string} params.status - 任务状态
 * @param {string} params.type - 任务类型
 * @param {string} params.timeRange - 时间范围
 * @returns {Promise}
 */
export function getTaskList(params) {
  return get('/tasks', params);
}

/**
 * 获取任务详情
 * @param {string} id - 任务ID
 * @returns {Promise}
 */
export function getTaskDetail(id) {
  return get(`/tasks/${id}`);
}

/**
 * 取消任务
 * @param {string} id - 任务ID
 * @returns {Promise}
 */
export function cancelTask(id) {
  return post(`/tasks/${id}/cancel`);
}

/**
 * 重试任务
 * @param {string} id - 任务ID
 * @returns {Promise}
 */
export function retryTask(id) {
  return post(`/tasks/${id}/retry`);
}

/**
 * 批量取消任务
 * @param {Array} ids - 任务ID数组
 * @returns {Promise}
 */
export function batchCancelTasks(ids) {
  return post('/tasks/batch-cancel', { ids });
}

/**
 * 获取任务日志
 * @param {string} id - 任务ID
 * @returns {Promise}
 */
export function getTaskLogs(id) {
  return get(`/tasks/${id}/logs`);
}

/**
 * 获取任务统计信息
 * @param {Object} params - 查询参数
 * @param {string} params.timeRange - 时间范围
 * @returns {Promise}
 */
export function getTaskStats(params) {
  return get('/tasks/stats', params);
}

/**
 * 获取任务类型列表
 * @returns {Promise}
 */
export function getTaskTypes() {
  return get('/task-types');
}

/**
 * 获取任务队列状态
 * @returns {Promise}
 */
export function getQueueStatus() {
  return get('/tasks/queue-status');
}

/**
 * 调整队列优先级
 * @param {string} queueName - 队列名称
 * @param {Object} data - 优先级数据
 * @returns {Promise}
 */
export function adjustQueuePriority(queueName, data) {
  return put(`/tasks/queues/${queueName}/priority`, data);
}

/**
 * 暂停队列
 * @param {string} queueName - 队列名称
 * @returns {Promise}
 */
export function pauseQueue(queueName) {
  return post(`/tasks/queues/${queueName}/pause`);
}

/**
 * 恢复队列
 * @param {string} queueName - 队列名称
 * @returns {Promise}
 */
export function resumeQueue(queueName) {
  return post(`/tasks/queues/${queueName}/resume`);
}

/**
 * 清空队列
 * @param {string} queueName - 队列名称
 * @returns {Promise}
 */
export function clearQueue(queueName) {
  return post(`/tasks/queues/${queueName}/clear`);
}

/**
 * 导出任务数据
 * @param {Object} params - 导出参数
 * @returns {Promise}
 */
export function exportTasks(params) {
  return get('/tasks/export', {
    ...params,
    responseType: 'blob'
  });
}

export default {
  getTaskList,
  getTaskDetail,
  cancelTask,
  retryTask,
  batchCancelTasks,
  getTaskLogs,
  getTaskStats,
  getTaskTypes,
  getQueueStatus,
  adjustQueuePriority,
  pauseQueue,
  resumeQueue,
  clearQueue,
  exportTasks
};