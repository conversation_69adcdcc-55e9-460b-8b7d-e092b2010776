#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WAN 2.1 T2V 1.3B模型测试工具

该脚本用于测试WAN 2.1 T2V 1.3B模型是否已正确下载和配置。
"""

import os
import sys
import argparse
import logging
import platform
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_directory_structure(model_dir):
    """检查模型目录结构是否完整"""
    required_subdirs = ["vae", "scheduler", "unet"]
    missing_subdirs = []
    
    for subdir in required_subdirs:
        subdir_path = os.path.join(model_dir, subdir)
        if not os.path.exists(subdir_path) or not os.path.isdir(subdir_path):
            missing_subdirs.append(subdir)
    
    if missing_subdirs:
        logger.warning(f"模型目录结构不完整，缺少以下子目录: {', '.join(missing_subdirs)}")
        return False, missing_subdirs
    
    return True, []

def check_configuration_files(model_dir):
    """检查配置文件是否存在"""
    required_files = [
        os.path.join("vae", "config.json"),
        os.path.join("scheduler", "scheduler_config.json"),
        os.path.join("unet", "config.json"),
    ]
    
    missing_files = []
    for file_path in required_files:
        full_path = os.path.join(model_dir, file_path)
        if not os.path.exists(full_path) or not os.path.isfile(full_path):
            missing_files.append(file_path)
    
    if missing_files:
        logger.warning(f"模型配置文件不完整，缺少以下文件: {', '.join(missing_files)}")
        return False, missing_files
    
    return True, []

def check_model_files(model_dir):
    """检查模型文件是否存在"""
    # 模型文件可能很多，我们只检查几个关键文件
    key_patterns = ["*.pth", "*.pt", "*.safetensors", "*.bin"]
    model_files = []
    
    for pattern in key_patterns:
        for file in Path(model_dir).rglob(pattern):
            model_files.append(file)
    
    if not model_files:
        logger.warning(f"未找到任何模型文件（.pth, .pt, .safetensors, .bin）")
        return False
    
    logger.info(f"找到 {len(model_files)} 个模型文件")
    for file in model_files[:5]:  # 只显示前5个
        logger.info(f"  - {file}")
    
    if len(model_files) > 5:
        logger.info(f"  ...以及其他 {len(model_files) - 5} 个文件")
    
    return True

def check_config_settings(model_name):
    """检查配置文件中的模型设置"""
    try:
        # 获取当前脚本目录
        script_dir = os.path.dirname(os.path.abspath(__file__))
        
        # 项目根目录
        project_root = os.path.dirname(os.path.dirname(script_dir))
        
        # 配置文件路径
        config_file = os.path.join(project_root, "backend", "config", "models.py")
        
        if not os.path.exists(config_file):
            logger.warning(f"配置文件不存在: {config_file}")
            return False
        
        # 读取配置文件内容
        with open(config_file, 'r') as f:
            config_content = f.read()
        
        # 检查模型名称是否在配置中
        if model_name in config_content:
            logger.info(f"模型 '{model_name}' 已在配置文件中配置")
            return True
        else:
            logger.warning(f"配置文件中未找到模型 '{model_name}'")
            return False
    
    except Exception as e:
        logger.error(f"检查配置文件时出错: {e}")
        return False

def test_model_import():
    """测试是否可以导入WAN视频服务模块"""
    try:
        logger.info("尝试导入WAN视频服务模块...")
        
        # 添加项目根目录到Python路径
        script_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(os.path.dirname(script_dir))
        if project_root not in sys.path:
            sys.path.insert(0, project_root)
        
        # 尝试导入
        from backend.services.wan_video_service import get_wan_video_service, check_directory_permissions
        logger.info("成功导入WAN视频服务模块")
        
        # 尝试初始化服务
        logger.info("尝试初始化WAN视频服务...")
        service = get_wan_video_service()
        logger.info("成功初始化WAN视频服务")
        
        # 获取模型信息
        logger.info("获取模型信息...")
        model_info = service.get_model_info()
        logger.info(f"模型信息: {model_info}")
        
        return True
    except ImportError as e:
        logger.error(f"导入WAN视频服务模块失败: {e}")
        return False
    except Exception as e:
        logger.error(f"测试模型导入失败: {e}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="WAN 2.1 T2V 1.3B模型测试工具")
    parser.add_argument("--model_dir", help="模型目录", 
                      default="backend/models_cache/Wan2.1-T2V-1.3B")
    
    args = parser.parse_args()
    model_dir = os.path.abspath(args.model_dir)
    
    print("\n===== WAN 2.1 T2V 1.3B模型测试工具 =====\n")
    print(f"系统信息:")
    print(f"  操作系统: {platform.system()} {platform.release()}")
    print(f"  Python版本: {platform.python_version()}")
    print(f"  当前用户: {os.getlogin() if hasattr(os, 'getlogin') else '未知'}")
    print(f"  工作目录: {os.getcwd()}")
    print(f"  模型目录: {model_dir}\n")
    
    # 检查目录是否存在
    if not os.path.exists(model_dir):
        logger.error(f"模型目录不存在: {model_dir}")
        return 1
    
    # 检查目录结构
    print("1. 检查模型目录结构...")
    structure_ok, missing_dirs = check_directory_structure(model_dir)
    if structure_ok:
        print("  ✅ 目录结构完整")
    else:
        print(f"  ❌ 目录结构不完整，缺少子目录: {', '.join(missing_dirs)}")
    
    # 检查配置文件
    print("\n2. 检查模型配置文件...")
    config_ok, missing_files = check_configuration_files(model_dir)
    if config_ok:
        print("  ✅ 配置文件完整")
    else:
        print(f"  ❌ 配置文件不完整，缺少: {', '.join(missing_files)}")
    
    # 检查模型文件
    print("\n3. 检查模型文件...")
    model_files_ok = check_model_files(model_dir)
    if model_files_ok:
        print("  ✅ 找到模型文件")
    else:
        print("  ❌ 未找到模型文件")
    
    # 检查配置设置
    print("\n4. 检查配置文件设置...")
    config_settings_ok = check_config_settings("Wan2.1-T2V-1.3B")
    if config_settings_ok:
        print("  ✅ 配置设置正确")
    else:
        print("  ❌ 配置设置不正确")
    
    # 测试模型导入
    print("\n5. 测试模型导入...")
    import_ok = test_model_import()
    if import_ok:
        print("  ✅ 模型导入成功")
    else:
        print("  ❌ 模型导入失败")
    
    # 总结
    print("\n===== 测试结果摘要 =====")
    tests = [
        ("目录结构", structure_ok),
        ("配置文件", config_ok),
        ("模型文件", model_files_ok),
        ("配置设置", config_settings_ok),
        ("模型导入", import_ok)
    ]
    
    passed = sum(1 for _, status in tests if status)
    total = len(tests)
    
    for name, status in tests:
        print(f"  {'✅' if status else '❌'} {name}")
    
    print(f"\n总体状态: 通过 {passed}/{total} 项测试")
    
    if passed == total:
        print("\n🎉 恭喜！WAN 2.1 T2V 1.3B模型已正确下载和配置。")
        print("您可以使用以下推荐参数生成视频:")
        print('python generate.py --task t2v-1.3B --size 832*480 --ckpt_dir ./Wan2.1-T2V-1.3B --sample_shift 8 --sample_guide_scale 6 --prompt "您的提示词"')
    else:
        print("\n⚠️ WAN 2.1 T2V 1.3B模型配置存在问题，请检查上述详细信息。")
        print("您可以运行 'backend/scripts/download_wan_1_3b.bat' 下载并配置模型。")
    
    return 0 if passed == total else 1

if __name__ == "__main__":
    sys.exit(main()) 