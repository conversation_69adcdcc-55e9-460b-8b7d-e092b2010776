import { computed } from 'vue';
import { useStore } from 'vuex';
import { hasPermission } from '@/utils/auth';

/**
 * 权限检查钩子
 * @returns {Object} 权限相关方法和计算属性
 */
export default function usePermission() {
  const store = useStore();
  
  // 获取当前用户所有权限
  const permissions = computed(() => store.getters['user/permissions']);
  
  // 获取当前用户角色
  const roles = computed(() => store.getters['user/roles']);
  
  // 检查是否有指定权限
  const hasPermissions = (permissionList) => {
    return hasPermission(permissionList);
  };
  
  // 检查是否有指定角色
  const hasRoles = (roleList) => {
    if (!roleList || roleList.length === 0) {
      return true;
    }
    
    if (!roles.value || roles.value.length === 0) {
      return false;
    }
    
    if (roles.value.includes('admin')) {
      return true;
    }
    
    return roleList.some(role => roles.value.includes(role));
  };
  
  // 检查是否有操作权限（角色或权限）
  const hasAuth = ({ requiredPermissions, requiredRoles }) => {
    // 如果没有设置权限要求，则默认有权限
    if ((!requiredPermissions || requiredPermissions.length === 0) && 
        (!requiredRoles || requiredRoles.length === 0)) {
      return true;
    }
    
    // 如果设置了权限，则检查权限
    if (requiredPermissions && requiredPermissions.length > 0) {
      if (hasPermissions(requiredPermissions)) {
        return true;
      }
    }
    
    // 如果设置了角色，则检查角色
    if (requiredRoles && requiredRoles.length > 0) {
      return hasRoles(requiredRoles);
    }
    
    return false;
  };
  
  // 操作权限指令
  const authDirective = {
    mounted(el, binding) {
      const { value } = binding;
      const hasRight = hasAuth(value);
      
      if (!hasRight) {
        // 如果没有权限，则隐藏元素
        el.style.display = 'none';
      }
    },
    
    updated(el, binding) {
      const { value } = binding;
      const hasRight = hasAuth(value);
      
      if (!hasRight) {
        el.style.display = 'none';
      } else {
        el.style.display = '';
      }
    }
  };
  
  // 初始化权限
  const initPermission = async () => {
    // 如果权限列表为空，则重新获取
    if (!permissions.value || permissions.value.length === 0) {
      try {
        await store.dispatch('user/getInfo');
      } catch (error) {
        console.error('初始化权限失败:', error);
      }
    }
  };
  
  return {
    permissions,
    roles,
    hasPermissions,
    hasRoles,
    hasAuth,
    authDirective,
    initPermission
  };
} 