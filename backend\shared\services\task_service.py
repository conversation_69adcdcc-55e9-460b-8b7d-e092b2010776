import logging
import re
from typing import Dict, Any, List, Optional, Tuple, Union
from datetime import datetime

from utils.task_utils import normalize_task_id
from services.video_task_manager import get_video_task_manager

logger = logging.getLogger(__name__)

class TaskService:
    """
    统一任务服务，用于处理所有类型的任务
    """
    
    def __init__(self):
        """初始化任务服务"""
        self.task_stores = []
        self._initialize_task_stores()
        
    def _initialize_task_stores(self):
        """初始化所有任务存储"""
        # 添加视频翻译任务
        try:
            video_task_manager = get_video_task_manager()
            self.task_stores.append(("video_tasks", video_task_manager.active_tasks))
            logger.info(f"已加载视频任务存储，任务数: {len(video_task_manager.active_tasks)}")
        except Exception as e:
            logger.warning(f"加载视频任务存储失败: {str(e)}")
        
        # 添加音频翻译任务
        try:
            from api.translation import active_translations
            self.task_stores.append(("audio_tasks", active_translations))
            logger.info(f"已加载音频任务存储，任务数: {len(active_translations)}")
        except Exception as e:
            logger.warning(f"加载音频任务存储失败: {str(e)}")
            
        # 添加数字人任务
        try:
            from services.progress_tracker import get_progress_tracker
            progress_tracker = get_progress_tracker()
            if progress_tracker and hasattr(progress_tracker, 'tasks'):
                self.task_stores.append(("digital_human_tasks", progress_tracker.tasks))
                logger.info(f"已加载数字人任务存储，任务数: {len(progress_tracker.tasks)}")
        except Exception as e:
            logger.warning(f"加载数字人任务存储失败: {str(e)}")
    
    def get_task(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        获取任务信息
        
        Args:
            task_id: 任务ID
            
        Returns:
            任务信息字典，若不存在则返回None
        """
        task_data, store_name = self._find_task(task_id)
        if task_data:
            # 添加任务类型信息
            result = dict(task_data)
            result["_task_type"] = store_name
            return result
        return None
    
    def _find_task(self, task_id: str) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
        """
        在所有任务存储中查找任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            (任务数据, 存储名称)元组，若不存在则返回(None, None)
        """
        # 先进行统一处理，规范化任务ID格式
        task_id = task_id.strip() if task_id else task_id
        
        # 检查任务ID是否有效
        if not task_id:
            logger.warning("无效的任务ID: 空")
            return None, None
        
        # 首先尝试在每个存储中直接查找
        for store_name, task_store in self.task_stores:
            if task_id in task_store:
                logger.info(f"通过直接匹配在 {store_name} 中找到任务: {task_id}")
                return task_store[task_id], store_name
        
        # 如果直接查找失败，对每个存储尝试规范化查找
        for store_name, task_store in self.task_stores:
            normalized_id = normalize_task_id(task_id, task_store)
            if normalized_id in task_store:
                logger.info(f"通过规范化在 {store_name} 中找到任务: {task_id} -> {normalized_id}")
                return task_store[normalized_id], store_name
        
        # 如果规范化查找也失败，尝试跨存储前缀替换
        # 尝试提取不带前缀的ID部分
        id_part = task_id
        prefix_found = False
        for prefix in ["job_", "task_"]:
            if task_id.startswith(prefix):
                id_part = task_id[len(prefix):]
                prefix_found = True
                break
        
        # 如果找到了前缀，尝试用其他前缀构建ID
        if prefix_found:
            for store_name, task_store in self.task_stores:
                for prefix in ["job_", "task_"]:
                    potential_id = f"{prefix}{id_part}"
                    if potential_id in task_store:
                        logger.info(f"通过前缀替换在 {store_name} 中找到任务: {task_id} -> {potential_id}")
                        return task_store[potential_id], store_name
        
        # 如果前缀替换也失败，尝试更复杂的匹配
        for store_name, task_store in self.task_stores:
            # UUID部分匹配
            if '-' in task_id:
                uuid_part = task_id.replace('-', '')
                for tid in task_store.keys():
                    if uuid_part in tid:
                        logger.info(f"通过UUID部分匹配在 {store_name} 中找到任务: {task_id} -> {tid}")
                        return task_store[tid], store_name
            
            # 数字匹配
            num_match = re.search(r'(\d+)', task_id)
            if num_match:
                num_part = num_match.group(1)
                for tid in task_store.keys():
                    if num_part in tid:
                        logger.info(f"通过数字部分匹配在 {store_name} 中找到任务: {task_id} -> {tid}")
                        return task_store[tid], store_name
        
        # 如果所有尝试都失败，记录最新任务作为参考
        all_tasks = []
        for store_name, task_store in self.task_stores:
            for tid, task_data in task_store.items():
                try:
                    all_tasks.append((tid, task_data.get("created_at", ""), store_name))
                except Exception:
                    pass
                    
        # 如果有任务，排序并记录最新的5个任务
        if all_tasks:
            try:
                recent_tasks = sorted(all_tasks, key=lambda x: x[1], reverse=True)[:5]
                recent_ids = [f"{tid} ({store})" for tid, _, store in recent_tasks]
                logger.info(f"未找到任务 {task_id}，最近的任务有: {recent_ids}")
            except Exception as e:
                logger.error(f"排序最近任务时出错: {str(e)}")
        
        logger.warning(f"在所有存储中未找到任务: {task_id}")
        return None, None
    
    def get_all_tasks(self, user_id: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        获取所有任务
        
        Args:
            user_id: 用户ID，若提供则只返回该用户的任务
            
        Returns:
            任务列表
        """
        result = []
        
        for store_name, task_store in self.task_stores:
            for task_id, task_data in task_store.items():
                # 检查用户ID
                task_user_id = task_data.get("user_id")
                if user_id is not None and task_user_id != user_id:
                    continue
                    
                # 创建任务副本并添加类型信息
                task_copy = dict(task_data)
                task_copy["_task_type"] = store_name
                task_copy["_task_id"] = task_id
                
                result.append(task_copy)
        
        # 按创建时间排序
        result.sort(key=lambda x: x.get("created_at", ""), reverse=True)
        
        return result
    
    def count_tasks(self) -> Dict[str, int]:
        """
        获取各类任务数量
        
        Returns:
            任务类型及数量的字典
        """
        result = {}
        total = 0
        
        for store_name, task_store in self.task_stores:
            count = len(task_store)
            result[store_name] = count
            total += count
        
        result["total"] = total
        
        return result
    
    def get_task_status_extended(self, task_id: str) -> Dict[str, Any]:
        """
        获取任务状态的扩展信息
        
        Args:
            task_id: 任务ID
            
        Returns:
            任务状态扩展信息字典，若不存在则包含错误信息
        """
        task_data, store_name = self._find_task(task_id)
        
        if not task_data:
            return {
                "found": False,
                "message": f"任务未找到: {task_id}",
                "task_id": task_id,
                "error": "NOT_FOUND"
            }
        
        # 准备基本状态数据
        result = {
            "found": True,
            "task_id": task_id,
            "normalized_id": next((tid for tid in task_data if tid == task_id), task_id),
            "task_type": store_name,
            "status": task_data.get("status", "unknown"),
            "progress": task_data.get("progress", 0),
            "created_at": task_data.get("created_at", ""),
            "updated_at": task_data.get("updated_at", ""),
            "user_id": task_data.get("user_id")
        }
        
        # 添加特定任务类型的额外信息
        if store_name == "video_tasks":
            result.update({
                "source_language": task_data.get("source_language", ""),
                "target_language": task_data.get("target_language", ""),
                "subtitle_format": task_data.get("subtitle_format", ""),
                "video_path": task_data.get("video_path", ""),
                "result_video_path": task_data.get("result_video_path", ""),
                "subtitle_path": task_data.get("subtitle_path", "")
            })
        elif store_name == "audio_tasks":
            result.update({
                "source_language": task_data.get("source_language", ""),
                "target_language": task_data.get("target_language", ""),
                "file_path": task_data.get("file_path", ""),
                "translated_audio_path": task_data.get("translated_audio_path", "")
            })
        elif store_name == "digital_human_tasks":
            result.update({
                "digital_human_id": task_data.get("digital_human_id", ""),
                "result_url": task_data.get("result_url", ""),
                "audio_url": task_data.get("audio_url", ""),
                "thumbnail_url": task_data.get("thumbnail_url", "")
            })
        
        return result

# 单例模式
_task_service_instance = None

def get_task_service() -> TaskService:
    """
    获取任务服务实例
    
    Returns:
        TaskService实例
    """
    global _task_service_instance
    if _task_service_instance is None:
        _task_service_instance = TaskService()
    return _task_service_instance 