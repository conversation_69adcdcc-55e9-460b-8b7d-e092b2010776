import os
import logging
import time
import shutil
import uuid
import tempfile
import subprocess
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional, List, Union, Tuple
import asyncio
import threading

# 获取日志记录器
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 定义转换结果目录
CONVERSION_DIR = "results/videos"
os.makedirs(CONVERSION_DIR, exist_ok=True)

# 定义上传目录
UPLOAD_DIR = "uploads/videos"
os.makedirs(UPLOAD_DIR, exist_ok=True)

# 任务状态字典（在实际生产环境中，应使用数据库或分布式缓存）
_TASK_STATUS = {}

# 视频转换服务单例
_video_conversion_service = None

def get_video_conversion_service():
    """获取视频转换服务实例"""
    global _video_conversion_service
    if _video_conversion_service is None:
        _video_conversion_service = VideoConversionService()
    return _video_conversion_service

class VideoConversionService:
    """视频转换服务类"""
    
    def __init__(self):
        """初始化视频转换服务"""
        self.check_ffmpeg()
    
    def check_ffmpeg(self):
        """检查FFmpeg是否可用"""
        try:
            result = subprocess.run(
                ["ffmpeg", "-version"], 
                stdout=subprocess.PIPE, 
                stderr=subprocess.PIPE,
                text=True,
                check=False
            )
            if result.returncode == 0:
                logger.info("FFmpeg 可用")
                return True
            else:
                logger.warning("FFmpeg 不可用，将使用模拟转换")
                return False
        except Exception as e:
            logger.warning(f"检查 FFmpeg 时出错: {str(e)}")
            return False
    
    def _update_task_status(
        self,
        task_id: str,
        status: Optional[str] = None,
        progress: Optional[int] = None,
        stage: Optional[str] = None,
        message: Optional[str] = None,
        result: Optional[Dict[str, Any]] = None,
        result_file: Optional[str] = None
    ):
        """
        更新任务状态
        
        Args:
            task_id (str): 任务ID
            status (str, optional): 任务状态
            progress (int, optional): 进度百分比
            stage (str, optional): 处理阶段
            message (str, optional): 状态消息
            result (Dict[str, Any], optional): 结果信息
            result_file (str, optional): 结果文件路径
        """
        # 从 API 层的任务状态字典中获取任务
        from api.video_conversion import task_status
        
        if task_id not in task_status:
            logger.warning(f"任务 {task_id} 不存在，无法更新状态")
            return
        
        # 更新任务状态
        if status is not None:
            task_status[task_id]["status"] = status
        
        if progress is not None:
            task_status[task_id]["progress"] = progress
        
        if stage is not None:
            task_status[task_id]["stage"] = stage
        
        if message is not None:
            task_status[task_id]["message"] = message
        
        if result is not None:
            task_status[task_id]["result"] = result
        
        # 如果提供了结果文件路径，添加到结果中
        if result_file is not None:
            if "result" not in task_status[task_id]:
                task_status[task_id]["result"] = {}
            
            task_status[task_id]["result"]["file_path"] = result_file
            task_status[task_id]["result"]["file_name"] = os.path.basename(result_file)
            task_status[task_id]["result"]["file_size"] = os.path.getsize(result_file)
        
        # 更新时间戳
        task_status[task_id]["updated_at"] = datetime.now().isoformat()
    
    async def process_conversion(
        self,
        task_id: str,
        upload_id: str,
        target_format: str,
        output_file_name: Optional[str] = None,
        options: Optional[Dict[str, Any]] = None,
        user_id: Optional[int] = None
    ):
        """
        处理视频转换
        
        Args:
            task_id (str): 任务ID
            upload_id (str): 上传ID
            target_format (str): 目标格式
            output_file_name (str, optional): 输出文件名
            options (Dict[str, Any], optional): 转换选项
            user_id (int, optional): 用户ID
        
        Returns:
            Dict[str, Any]: 转换结果
        """
        # 更新任务状态为处理中
        self._update_task_status(
            task_id,
            status="processing",
            progress=10,
            stage="初始化",
            message="任务处理中，准备转换文件..."
        )
        
        try:
            from api.video_conversion import task_status
            
            # 获取上传信息
            if upload_id not in task_status:
                raise ValueError(f"上传ID {upload_id} 不存在")
            
            upload_info = task_status[upload_id]
            
            # 获取源文件路径和格式
            source_file = upload_info["file_path"]
            original_filename = upload_info["file_name"]
            source_format = upload_info["format"]
            
            if not os.path.exists(source_file):
                raise FileNotFoundError(f"源文件 {source_file} 不存在")
            
            # 确保用户结果目录存在
            user_result_dir = os.path.join(CONVERSION_DIR, str(user_id))
            os.makedirs(user_result_dir, exist_ok=True)
            
            # 生成输出文件名
            if not output_file_name:
                base_name = os.path.splitext(original_filename)[0]
                output_file_name = f"{base_name}.{target_format}"
            elif not output_file_name.endswith(f".{target_format}"):
                output_file_name = f"{output_file_name}.{target_format}"
            
            # 生成唯一的输出文件路径
            output_filename = f"{int(time.time())}_{str(uuid.uuid4())[:8]}_{output_file_name}"
            output_path = os.path.join(user_result_dir, output_filename)
            
            # 更新任务状态
            self._update_task_status(
                task_id,
                progress=30,
                stage="准备转换",
                message=f"准备将 {original_filename} 转换为 {target_format} 格式"
            )
            
            # 执行转换
            conversion_success, conversion_message = await self._convert_video(
                source_file, output_path, source_format, target_format, options, task_id
            )
            
            if not conversion_success:
                self._update_task_status(
                    task_id,
                    status="error",
                    message=f"转换失败: {conversion_message}"
                )
                return
            
            # 检查文件是否存在
            if not os.path.exists(output_path):
                self._update_task_status(
                    task_id,
                    status="error",
                    message="转换完成但未找到输出文件"
                )
                return
            
            # 更新任务状态为成功
            self._update_task_status(
                task_id,
                status="success",
                progress=100,
                stage="已完成",
                message="转换成功",
                result={
                    "file_name": output_filename,
                    "file_size": os.path.getsize(output_path),
                    "format": target_format
                },
                result_file=output_path
            )
            
            logger.info(f"任务 {task_id} 处理完成: {source_file} -> {output_path}")
            
        except Exception as e:
            logger.error(f"处理转换任务 {task_id} 时出错: {str(e)}")
            
            # 更新任务状态为失败
            self._update_task_status(
                task_id,
                status="error",
                stage="错误",
                message=f"处理失败: {str(e)}"
            )
    
    async def _convert_video(
        self,
        source_path: str,
        output_path: str,
        source_format: str,
        target_format: str,
        options: Optional[Dict[str, Any]] = None,
        task_id: Optional[str] = None
    ) -> Tuple[bool, str]:
        """
        执行视频转换
        
        Args:
            source_path (str): 源文件路径
            output_path (str): 输出文件路径
            source_format (str): 源文件格式
            target_format (str): 目标文件格式
            options (Dict[str, Any], optional): 转换选项
            task_id (str, optional): 任务ID，用于更新进度
            
        Returns:
            Tuple[bool, str]: (成功标志, 消息)
        """
        if task_id:
            self._update_task_status(
                task_id,
                progress=40,
                stage="转码中",
                message=f"开始转换 {source_format} 到 {target_format}..."
            )
        
        try:
            # 检查是否使用模拟转换
            use_simulation = not self.check_ffmpeg()
            
            if use_simulation:
                logger.warning("使用模拟转换")
                return await self._simulate_video_conversion(
                    source_path, output_path, source_format, target_format, options, task_id
                )
            
            # 准备FFmpeg命令
            ffmpeg_cmd = ["ffmpeg", "-i", source_path]
            
            # 添加转换选项
            if options:
                # 视频质量
                quality = options.get("quality", "high")
                if quality == "high":
                    ffmpeg_cmd.extend(["-crf", "18"])  # 高质量
                elif quality == "medium":
                    ffmpeg_cmd.extend(["-crf", "23"])  # 中等质量
                elif quality == "low":
                    ffmpeg_cmd.extend(["-crf", "28"])  # 低质量
                
                # 分辨率
                resolution = options.get("resolution", "original")
                if resolution != "original":
                    ffmpeg_cmd.extend(["-s", resolution])
                
                # 视频编码
                video_codec = options.get("videoCodec", "h264")
                if video_codec == "h264":
                    ffmpeg_cmd.extend(["-c:v", "libx264"])
                elif video_codec == "h265":
                    ffmpeg_cmd.extend(["-c:v", "libx265"])
                elif video_codec == "vp9":
                    ffmpeg_cmd.extend(["-c:v", "libvpx-vp9"])
                elif video_codec == "av1":
                    ffmpeg_cmd.extend(["-c:v", "libaom-av1"])
                
                # 音频编码
                audio_codec = options.get("audioCodec", "aac")
                if audio_codec == "aac":
                    ffmpeg_cmd.extend(["-c:a", "aac"])
                elif audio_codec == "mp3":
                    ffmpeg_cmd.extend(["-c:a", "libmp3lame"])
                elif audio_codec == "opus":
                    ffmpeg_cmd.extend(["-c:a", "libopus"])
                
                # 比特率
                if options.get("useBitrate", False) and options.get("videoBitrate"):
                    bitrate = options.get("videoBitrate")
                    ffmpeg_cmd.extend(["-b:v", f"{bitrate}k"])
            
            # 添加输出文件
            ffmpeg_cmd.append(output_path)
            
            # 打印日志
            logger.info(f"执行FFmpeg命令: {' '.join(ffmpeg_cmd)}")
            
            if task_id:
                self._update_task_status(
                    task_id,
                    progress=50,
                    message=f"FFmpeg 转码中..."
                )
            
            # 执行FFmpeg命令
            process = await asyncio.create_subprocess_exec(
                *ffmpeg_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            # 等待命令完成
            stdout, stderr = await process.communicate()
            
            # 检查执行结果
            if process.returncode != 0:
                error_message = stderr.decode()
                logger.error(f"FFmpeg执行失败: {error_message}")
                return False, f"转换失败: {error_message}"
            
            if task_id:
                self._update_task_status(
                    task_id,
                    progress=90,
                    stage="完成中",
                    message="转码完成，正在处理最终文件..."
                )
            
            return True, "转换成功"
            
        except Exception as e:
            logger.error(f"转换视频时出错: {str(e)}")
            return False, f"转换过程中出错: {str(e)}"
    
    async def _simulate_video_conversion(
        self,
        source_path: str,
        output_path: str,
        source_format: str,
        target_format: str,
        options: Optional[Dict[str, Any]] = None,
        task_id: Optional[str] = None
    ) -> Tuple[bool, str]:
        """
        模拟视频转换（当FFmpeg不可用时使用）
        
        Args:
            source_path (str): 源文件路径
            output_path (str): 输出文件路径
            source_format (str): 源文件格式
            target_format (str): 目标文件格式
            options (Dict[str, Any], optional): 转换选项
            task_id (str, optional): 任务ID，用于更新进度
            
        Returns:
            Tuple[bool, str]: (成功标志, 消息)
        """
        try:
            # 模拟转换过程
            logger.info(f"模拟转换视频: {source_path} -> {output_path}")
            
            # 模拟各个转换阶段
            stages = [
                ("解析视频", 40),
                ("准备转码", 50),
                ("视频转码", 60),
                ("音频处理", 70),
                ("合并媒体", 80),
                ("完成处理", 90)
            ]
            
            for stage_name, progress in stages:
                if task_id:
                    self._update_task_status(
                        task_id,
                        progress=progress,
                        stage=stage_name,
                        message=f"正在{stage_name}..."
                    )
                # 模拟处理时间
                await asyncio.sleep(1)
            
            # 简单地复制源文件作为输出文件
            shutil.copy2(source_path, output_path)
            
            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            return True, "模拟转换成功"
            
        except Exception as e:
            logger.error(f"模拟转换视频时出错: {str(e)}")
            return False, f"模拟转换过程中出错: {str(e)}"
    
    async def get_video_info(
        self,
        file_path: str
    ) -> Dict[str, Any]:
        """
        获取视频信息
        
        Args:
            file_path (str): 视频文件路径
            
        Returns:
            Dict[str, Any]: 视频信息
        """
        try:
            # 检查FFmpeg是否可用
            if not self.check_ffmpeg():
                # 返回模拟数据
                return {
                    "duration": 120,  # 假设2分钟
                    "resolution": "1920x1080",
                    "bitrate": 5000,
                    "codec": "h264"
                }
            
            # 使用FFprobe获取视频信息
            ffprobe_cmd = [
                "ffprobe",
                "-v", "error",
                "-show_entries", "stream=width,height,codec_name,duration:format=duration,bit_rate",
                "-of", "json",
                file_path
            ]
            
            logger.info(f"执行FFprobe命令: {' '.join(ffprobe_cmd)}")
            
            process = await asyncio.create_subprocess_exec(
                *ffprobe_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode != 0:
                logger.error(f"FFprobe执行失败: {stderr.decode()}")
                raise Exception(f"无法获取视频信息: {stderr.decode()}")
            
            # 解析JSON输出
            import json
            result = json.loads(stdout.decode())
            
            # 提取视频信息
            video_stream = None
            for stream in result.get("streams", []):
                if stream.get("codec_type") == "video":
                    video_stream = stream
                    break
            
            if not video_stream:
                raise Exception("未找到视频流")
            
            width = video_stream.get("width", 0)
            height = video_stream.get("height", 0)
            codec = video_stream.get("codec_name", "unknown")
            duration = float(video_stream.get("duration", 0))
            
            # 从格式信息中获取比特率
            bit_rate = result.get("format", {}).get("bit_rate", 0)
            if bit_rate:
                bit_rate = int(bit_rate) // 1000  # 转换为kbps
            
            return {
                "duration": duration,
                "resolution": f"{width}x{height}",
                "bitrate": bit_rate,
                "codec": codec
            }
            
        except Exception as e:
            logger.error(f"获取视频信息时出错: {str(e)}")
            # 返回默认值
            return {
                "duration": 0,
                "resolution": "",
                "bitrate": 0,
                "codec": ""
            } 