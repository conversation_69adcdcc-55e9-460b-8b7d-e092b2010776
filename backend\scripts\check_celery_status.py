#!/usr/bin/env python3
"""
检查 Celery 系统状态
"""
import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# 确保加载环境变量
load_dotenv()

# 添加后端路径
backend_dir = Path(__file__).parent.parent
sys.path.append(str(backend_dir))

def check_celery_status():
    """检查 Celery 系统状态"""
    print("🔍 检查 Celery 系统状态")
    print("=" * 50)
    
    try:
        from app.core.celery_app import celery_app
        
        # 获取 Celery 检查器
        inspect = celery_app.control.inspect()
        
        # 检查活跃的 Worker
        print("\n📊 活跃的 Worker:")
        print("-" * 30)
        stats = inspect.stats()
        if stats:
            for worker_name, worker_stats in stats.items():
                print(f"✅ {worker_name}")
                print(f"   - 进程ID: {worker_stats.get('pid', 'N/A')}")
                print(f"   - 启动时间: {worker_stats.get('clock', 'N/A')}")
                print(f"   - 负载: {worker_stats.get('rusage', {}).get('utime', 'N/A')}")
        else:
            print("❌ 没有活跃的 Worker")
        
        # 检查队列状态
        print("\n📋 队列状态:")
        print("-" * 30)
        active_queues = inspect.active_queues()
        if active_queues:
            all_queues = set()
            for worker_name, queues in active_queues.items():
                print(f"🔧 {worker_name}:")
                for queue in queues:
                    queue_name = queue['name']
                    all_queues.add(queue_name)
                    print(f"   - {queue_name}")
            
            print(f"\n📈 总队列数: {len(all_queues)}")
            print(f"🎯 队列列表: {', '.join(sorted(all_queues))}")
        else:
            print("❌ 没有活跃的队列")
        
        # 检查活跃任务
        print("\n🏃 活跃任务:")
        print("-" * 30)
        active_tasks = inspect.active()
        if active_tasks:
            total_tasks = 0
            for worker_name, tasks in active_tasks.items():
                task_count = len(tasks)
                total_tasks += task_count
                print(f"🔧 {worker_name}: {task_count} 个任务")
                for task in tasks[:3]:  # 只显示前3个任务
                    print(f"   - {task['name']} ({task['id'][:8]}...)")
                if len(tasks) > 3:
                    print(f"   - ... 还有 {len(tasks) - 3} 个任务")
            print(f"\n📊 总活跃任务: {total_tasks}")
        else:
            print("✅ 没有活跃任务")
        
        # 检查预定任务
        print("\n⏰ 预定任务:")
        print("-" * 30)
        scheduled_tasks = inspect.scheduled()
        if scheduled_tasks:
            total_scheduled = 0
            for worker_name, tasks in scheduled_tasks.items():
                task_count = len(tasks)
                total_scheduled += task_count
                print(f"🔧 {worker_name}: {task_count} 个预定任务")
            print(f"\n📊 总预定任务: {total_scheduled}")
        else:
            print("✅ 没有预定任务")
        
        # 检查保留任务
        print("\n📦 保留任务:")
        print("-" * 30)
        reserved_tasks = inspect.reserved()
        if reserved_tasks:
            total_reserved = 0
            for worker_name, tasks in reserved_tasks.items():
                task_count = len(tasks)
                total_reserved += task_count
                print(f"🔧 {worker_name}: {task_count} 个保留任务")
            print(f"\n📊 总保留任务: {total_reserved}")
        else:
            print("✅ 没有保留任务")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查 Celery 状态失败: {e}")
        return False

def check_redis_connection():
    """检查 Redis 连接"""
    print("\n🔍 检查 Redis 连接")
    print("-" * 30)
    
    try:
        import redis
        r = redis.Redis(host='localhost', port=6379, db=0)
        r.ping()
        
        # 获取 Redis 信息
        info = r.info()
        print("✅ Redis 连接正常")
        print(f"   - 版本: {info.get('redis_version', 'N/A')}")
        print(f"   - 内存使用: {info.get('used_memory_human', 'N/A')}")
        print(f"   - 连接数: {info.get('connected_clients', 'N/A')}")
        
        # 检查队列长度
        queue_names = [
            'celery', 'digital_human', 'image_processing', 
            'voice_processing', 'video_generation', 'translation', 'wanx_generation'
        ]
        
        print("\n📊 队列长度:")
        for queue_name in queue_names:
            length = r.llen(queue_name)
            if length > 0:
                print(f"   - {queue_name}: {length} 个任务")
        
        return True
        
    except Exception as e:
        print(f"❌ Redis 连接失败: {e}")
        return False

def main():
    """主函数"""
    print("🧪 Celery 系统状态检查")
    print("=" * 50)
    
    # 检查 Redis
    redis_ok = check_redis_connection()
    
    # 检查 Celery
    celery_ok = check_celery_status()
    
    # 总结
    print("\n📊 检查总结")
    print("=" * 50)
    
    if redis_ok and celery_ok:
        print("🎉 Celery 系统运行正常！")
        print("\n💡 提示:")
        print("   - 所有队列都已配置: digital_human, image_processing, voice_processing, video_generation, translation, wanx_generation")
        print("   - Wanx 2.1 视频生成任务会路由到 wanx_generation 队列")
        print("   - 可以通过 start_celery.py 启动统一 Worker")
        print("   - 可以通过 start_wanx_celery.py 启动专用 Wanx Worker")
    else:
        print("⚠️ Celery 系统存在问题，请检查配置")
        if not redis_ok:
            print("   - Redis 连接失败，请确保 Redis 服务运行")
        if not celery_ok:
            print("   - Celery Worker 未运行，请启动 Worker")

if __name__ == "__main__":
    main()
