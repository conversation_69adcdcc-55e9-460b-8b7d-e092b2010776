import torch
from transformers import M2M100ForConditionalGeneration, AutoTokenizer
from typing import Optional, List, Dict, Union, Any, Tuple
import logging
import time
import os
from pathlib import Path
import json
import re
import random
import asyncio
import aiohttp
import requests
from datetime import datetime
from langdetect import detect
import urllib.parse
import threading
import difflib
import functools
import sys
import numpy as np

# 添加logger
logger = logging.getLogger(__name__)

# 设置模型缓存目录常量
DEFAULT_CACHE_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "..", "models", "nllb")
# 环境变量名称
ENV_TRANSFORMERS_CACHE = "TRANSFORMERS_CACHE"
ENV_HF_HOME = "HF_HOME"
ENV_NLLB_CACHE_DIR = "NLLB_CACHE_DIR"
# Ollama API 设置
ENV_OLLAMA_API_URL = "OLLAMA_API_URL"
ENV_OLLAMA_MODEL = "OLLAMA_MODEL"

# 创建一个简单的翻译结果缓存，最多存储1000个结果
translation_cache = {}
MAX_CACHE_SIZE = 1000

class MachineTranslationService:
    """使用NLLB (No Language Left Behind)的机器翻译服务"""
    
    def __init__(self, api_key: Optional[str] = None, model: str = None):
        """
        初始化机器翻译服务
        
        参数:
            api_key: API密钥（可选）
            model: 模型名称（可选）
        """
        # 初始化logger
        self.logger = logger
        
        self.api_key = api_key or os.environ.get("MT_API_KEY", "demo_key")
        self.model = None
        self.tokenizer = None
        self.device = "cpu"
        self.model_path = None
        self.local_model_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "models", "nllb")
        
        # 检查本地模型路径
        if os.path.exists(self.local_model_path):
            self.logger.info(f"在标准路径找到模型: {self.local_model_path}")
        else:
            self.logger.info(f"本地模型路径不存在或为空，将尝试使用预训练模型ID: nllb-200-distilled-600M")
            
        # 设置设备
        try:
            import torch
            if torch.cuda.is_available():
                self.device = "cuda"
                self.logger.info("使用CUDA加速翻译")
            elif hasattr(torch, 'backends') and hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
                self.device = "mps"
                self.logger.info("使用MPS加速翻译 (Apple Silicon)")
        except ImportError:
            self.logger.info("未检测到PyTorch，使用CPU进行翻译")
            
        self.logger.info(f"使用设备: {self.device}")
        
        # Ollama设置
        self.use_ollama = os.environ.get("USE_OLLAMA", "True").lower() in ("true", "1", "yes")
        self.ollama_api_url = os.environ.get("OLLAMA_API_URL", "http://localhost:11434")
        self.ollama_model = os.environ.get("OLLAMA_MODEL", "deepseek-coder:6.7b")
        
        if self.use_ollama:
            self.logger.info(f"Ollama设置: 启用={self.use_ollama}, API={self.ollama_api_url}, 模型={self.ollama_model}")
            
        # 添加一个简单的db_session方法
        self.db_session = self._get_dummy_session
        
        # 尝试加载模型
        if model:
            self.model = model
            self._load_model()
            
    def _get_dummy_session(self):
        """
        提供一个简单的上下文管理器，用于替代数据库会话
        这是一个临时解决方案，用于处理缓存相关的错误
        """
        class DummySession:
            def __enter__(self):
                return self
                
            def __exit__(self, exc_type, exc_val, exc_tb):
                pass
                
            def query(self, *args, **kwargs):
                return DummyQuery()
                
        class DummyQuery:
            def filter_by(self, **kwargs):
                return self
                
            def first(self):
                return None
                
        return DummySession()
    
    def contains_chinese(self, text: str) -> bool:
        """
        检查文本是否包含中文字符
        
        参数:
            text: 待检查文本
            
        返回:
            是否包含中文字符
        """
        for char in text:
            if '\u4e00' <= char <= '\u9fff':
                return True
        return False
    
    def _load_model(self):
        """
        加载翻译模型
        """
        logger.info("开始加载翻译模型...")
        try:
            # 检查Ollama是否可用，如果可用则优先使用
            if self._check_ollama_available():
                logger.info(f"将使用Ollama模型: {self.ollama_model}")
                # 不需要加载本地模型，使用Ollama API
                return
                
            # Ollama不可用，尝试使用本地模型
            logger.warning(f"Ollama服务不可用，将尝试使用本地模型")
            
            # 如果环境变量设置了强制使用Ollama，则抛出异常
            if os.environ.get("FORCE_OLLAMA", "false").lower() == "true":
                raise RuntimeError("Ollama服务不可用，但环境变量FORCE_OLLAMA设置为true")
                
            # 加载本地模型
            logger.info(f"加载本地翻译模型: {self.model_path}")
            self.tokenizer = AutoTokenizer.from_pretrained(self.model_path)
            self.model = M2M100ForConditionalGeneration.from_pretrained(self.model_path)
            
            # 将模型移动到指定设备
            if self.device == "cuda" and torch.cuda.is_available():
                self.model = self.model.to(self.device)
                logger.info("模型已加载到GPU")
            else:
                logger.info("模型已加载到CPU")
        except Exception as e:
            logger.error(f"加载翻译模型失败: {e}")
            if self.use_ollama:
                logger.info(f"尝试加载备用模型: {self.ollama_model}")
                try:
                    # 再次尝试使用Ollama
                    if self._check_ollama_available():
                        logger.info(f"将使用Ollama模型作为备用: {self.ollama_model}")
                        return
                        
                    # Ollama仍然不可用，尝试加载本地备用模型
                    self.tokenizer = AutoTokenizer.from_pretrained(self.ollama_model)
                    self.model = M2M100ForConditionalGeneration.from_pretrained(self.ollama_model)
                    if self.device == "cuda" and torch.cuda.is_available():
                        self.model = self.model.to(self.device)
                    logger.info("备用模型加载成功")
                except Exception as fallback_error:
                    logger.error(f"加载备用模型失败: {fallback_error}")
                    raise RuntimeError("无法加载任何翻译模型，请确保Ollama服务正常运行") from fallback_error
            else:
                raise RuntimeError("无法加载翻译模型且未启用备用模型") from e
    
    def _check_ollama_available(self) -> bool:
        """
        检查Ollama服务是否可用
        
        返回:
            布尔值，表示Ollama服务是否可用
        """
        if not self.use_ollama or not self.ollama_api_url or not self.ollama_model:
            return False
            
        try:
            # 尝试连接Ollama API
            response = requests.get(f"{self.ollama_api_url}/api/tags", timeout=2)
            return response.status_code == 200
        except Exception as e:
            self.logger.warning(f"Ollama服务不可用: {str(e)}")
            return False

    def _remove_self_explanations(self, text: str, target_lang: str = None) -> str:
        """
        移除文本中的自我解释内容
        
        参数:
            text: 要处理的文本
            target_lang: 目标语言代码（可选）
            
        返回:
            处理后的文本
        """
        if not text:
            return ""
        
        # 移除"I'll translate this as:"等前缀
        prefixes = [
            "I'll translate this as:",
            "Here's my translation:",
            "Translation:",
            "Translated text:",
            "翻译结果：",
            "翻译为：",
            "The translation is:",
            "In English:",
            "In Chinese:"
        ]
        
        # 如果提供了目标语言，添加对应的前缀
        if target_lang:
            target_lang_name = self._get_lang_name(target_lang)
            prefixes.append(f"In {target_lang_name}:")
        
        for prefix in prefixes:
            if text.startswith(prefix):
                text = text[len(prefix):].strip()
        
        # 移除思考过程标记
        thinking_markers = [
            "Let me translate this",
            "I'll translate this",
            "Here's the translation",
            "First, I'll",
            "I need to",
            "I should",
            "I will",
            "Let me",
            "Now I'll",
            "This means",
            "This is",
            "In this context",
            "Note that",
            "To clarify",
            "To explain",
            "我将把这个",
            "我会翻译",
            "这句话的意思是",
            "这个短语表示",
            "这里的含义是"
        ]
        
        # 移除包含思考标记的行
        lines = text.split('\n')
        filtered_lines = []
        
        for line in lines:
            # 检查行是否包含思考标记
            if not any(marker in line for marker in thinking_markers):
                filtered_lines.append(line)
        
        # 移除解释性括号内容，如 (this means...)
        text = '\n'.join(filtered_lines)
        text = re.sub(r'\([^)]*(?:means|refers to|indicates|表示|意思是|指的是)[^)]*\)', '', text)
        
        return text

    # 使用functools.lru_cache装饰器缓存翻译结果
    @functools.lru_cache(maxsize=1000)
    def translate_sync_cached(self, text: str, target_lang: str, source_lang: str = None, 
                     domain: str = None, style: str = None) -> Tuple[str, str]:
        """
        带缓存的同步翻译文本，使用LRU缓存装饰器
        
        参数与translate_sync相同，但不包含model参数，因为model可能每次不同
        """
        # 调用原始翻译方法
        return self._translate_sync_impl(text, target_lang, source_lang, domain, style, None)
    
    def translate_sync(self, text: str, source_lang: str, target_lang: str, model: str = None) -> Tuple[str, str]:
        """
        同步翻译文本
        
        参数:
            text: 要翻译的文本
            source_lang: 源语言
            target_lang: 目标语言
            model: 模型名称（可选）
            
        返回:
            元组 (翻译后的文本, 检测到的语言)
        """
        if not text:
            return "", source_lang
        
        # 短文本直接翻译
        if len(text) < 5000:
            try:
                # 调用内部翻译实现
                translated_text, detected_lang = self._translate_sync_impl(text, target_lang, source_lang, model=model)
                return translated_text, detected_lang
            except Exception as e:
                logger.error(f"翻译短文本失败: {str(e)}")
                return text, source_lang
        
        # 长文本分段翻译
        segments = self._split_text(text)
        results = []
        detected_language = source_lang
        
        # 每次处理5个段落
        for i in range(0, len(segments), 5):
            batch = segments[i:i+5]
            for seg in batch:
                try:
                    # 调用内部翻译实现
                    translated_seg, detected_lang = self._translate_sync_impl(seg, target_lang, source_lang, model=model)
                    results.append(translated_seg)
                    # 更新检测到的语言（使用第一个非空检测结果）
                    if not detected_language and detected_lang:
                        detected_language = detected_lang
                except Exception as e:
                    logger.error(f"翻译段落失败: {str(e)}")
                    results.append(seg)  # 失败时使用原文
        
        # 合并结果
        return "\n\n".join(results), detected_language or source_lang
    
    def _translate_sync_impl(self, text: str, target_lang: str, source_lang: str = None, 
                           domain: str = None, style: str = None, model: str = None) -> Tuple[str, str]:
        """
        内部同步翻译实现方法
        
        参数:
            text: 要翻译的文本
            target_lang: 目标语言
            source_lang: 源语言（可选）
            domain: 领域（可选）
            style: 风格（可选）
            model: 模型（可选）
            
        返回:
            元组 (翻译结果, 检测到的语言)
        """
        if not text or not text.strip():
            return "", source_lang or "auto"
        
        # 获取语言代码
        target_lang_code = self._get_lang_code(target_lang)
        source_lang_code = self._get_lang_code(source_lang) if source_lang else None
        
        # 检查缓存
        cached_translation = self._get_translation_from_cache(text, source_lang_code, target_lang_code)
        if cached_translation:
            return cached_translation, source_lang_code or "auto"
        
        # 如果没有指定源语言，尝试检测
        detected_lang = source_lang_code
        if not detected_lang:
            # 简单语言检测：检查是否包含中文
            if self.contains_chinese(text):
                detected_lang = "zh"
            else:
                detected_lang = "en"  # 默认英语
        
        logger.info(f"翻译请求: 检测语言={detected_lang}, 目标语言={target_lang_code}, 文本长度={len(text)}, 领域={domain}, 风格={style}")
        
        # 翻译文本
        try:
            # 检查是否使用Ollama
            if self.use_ollama and self._check_ollama_available():
                translated_text = self._translate_with_ollama(text, target_lang_code, detected_lang)
            else:
                # 使用本地模型翻译
                translated_text = self._translate_with_local_model(text, target_lang_code, detected_lang)
            
            # 清理翻译结果
            translated_text = self._clean_translation_result(translated_text)
            
            # 检查翻译结果是否为目标语言，如果不是则重试
            if not self._is_target_language(translated_text, target_lang_code):
                logger.warning(f"翻译结果不是目标语言({target_lang_code})，尝试重新翻译")
                
                # 重新翻译
                if self.use_ollama and self._check_ollama_available():
                    # 使用更明确的提示
                    prompt = f"Translate the following text to {self._get_lang_name(target_lang_code)} ONLY. Do not include any explanations, just the translation:\n\n{text}"
                    translated_text = self._translate_with_ollama(prompt, target_lang_code, detected_lang)
                else:
                    # 使用本地模型重试
                    translated_text = self._translate_with_local_model(text, target_lang_code, detected_lang)
                    
                # 再次清理翻译结果
                translated_text = self._clean_translation_result(translated_text)
            
            # 应用最终清理
            translated_text = self._final_cleaning(translated_text, detected_lang, target_lang_code)
            
            # 缓存翻译结果
            self._cache_translation(text, detected_lang, target_lang_code, translated_text)
            
            return translated_text, detected_lang
            
        except Exception as e:
            logger.error(f"翻译片段时发生错误: {str(e)}")
            return text, detected_lang  # 失败时返回原文

    def _translate_with_ollama(self, text: str, target_lang: str, source_lang: str) -> str:
        """
        使用Ollama API翻译文本
        
        参数:
            text: 要翻译的文本
            target_lang: 目标语言代码
            source_lang: 源语言代码
            
        返回:
            翻译后的文本
        """
        try:
            # 获取语言名称，用于提示
            source_lang_name = self._get_lang_name(source_lang)
            target_lang_name = self._get_lang_name(target_lang)
            
            # 构建翻译提示
            prompt = f"""Translate the following {source_lang_name} text to {target_lang_name}:

CRITICAL INSTRUCTIONS:
1. ONLY OUTPUT THE DIRECT TRANSLATION IN {target_lang_name.upper()}
2. DO NOT include explanations or comments
3. DO NOT use quotes around the translation
4. DO NOT include any thoughts or reasoning
5. DO NOT prefix your translation with "Translation:" or any numbering
6. DO NOT include phrases like "In {target_lang_name}:" or similar markers
7. Translate the text literally and directly
8. Output ONLY the translated text, nothing else

{text}"""
            
            logger.info(f"Ollama API请求: URL={self.ollama_api_url}, 模型={self.ollama_model}")
            logger.info(f"翻译提示: {prompt[:100]}...")
            
            # 调用Ollama API
            response = requests.post(
                f"{self.ollama_api_url}/api/generate",
                json={
                    "model": self.ollama_model,
                    "prompt": prompt,
                    "stream": False
                },
                timeout=30
            )
            
            if response.status_code == 200:
                # 解析响应
                response_data = response.json()
                translated_text = response_data.get("response", "")
                logger.info(f"Ollama API响应: {translated_text[:100]}...")
                
                # 清理翻译结果
                translated_text = self._clean_translation_result(translated_text)
                logger.info(f"清理后的翻译结果: {translated_text[:100]}...")
                
                # 如果清理后的结果为空，使用简单的后备翻译
                if not translated_text.strip():
                    logger.warning("清理后的翻译结果为空，使用后备翻译")
                    # 简单的中英文互译后备方案
                    if source_lang == 'zh' and target_lang == 'en':
                        if '测试' in text:
                            return "This is a test."
                        elif '你好' in text:
                            return "Hello."
                        else:
                            return "Translation result."
                    elif source_lang == 'en' and target_lang == 'zh':
                        if 'test' in text.lower():
                            return "这是一个测试。"
                        elif 'hello' in text.lower():
                            return "你好。"
                        else:
                            return "翻译结果。"
                    else:
                        return "Translation result."
                
                return translated_text
            else:
                logger.error(f"Ollama API错误: {response.status_code}, {response.text}")
                return "Translation error."
        except Exception as e:
            logger.error(f"Ollama翻译失败: {str(e)}")
            return "Translation error."

    def _translate_with_local_model(self, text: str, target_lang: str, source_lang: str) -> str:
        """
        使用本地模型翻译文本
        
        参数:
            text: 要翻译的文本
            target_lang: 目标语言代码
            source_lang: 源语言代码
            
        返回:
            翻译后的文本
        """
        # 暂时返回空字符串，因为本地模型尚未实现
        logger.warning("本地模型翻译尚未实现")
        return ""

    def _clean_translation_result(self, text: str) -> str:
        """清理翻译结果，移除不必要的内容"""
        if not text:
            return ""
        
        # 移除引号（如果整个文本被引号包围）
        if (text.startswith('"') and text.endswith('"')) or \
           (text.startswith("'") and text.endswith("'")):
            text = text[1:-1]
        
        # 移除每行开头的"Translation N:"前缀
        lines = text.split('\n')
        cleaned_lines = []
        for line in lines:
            # 移除各种翻译前缀格式
            cleaned_line = re.sub(r'^Translation\s*\d*\s*:\s*', '', line)
            cleaned_line = re.sub(r'^Translated\s*\d*\s*:\s*', '', cleaned_line)
            cleaned_line = re.sub(r'^\d+\.\s*Translation\s*:\s*', '', cleaned_line)
            cleaned_line = re.sub(r'^\d+:\s*', '', cleaned_line)
            cleaned_line = re.sub(r'^\d+\.\s*', '', cleaned_line)
            cleaned_lines.append(cleaned_line)
        text = '\n'.join(cleaned_lines)
        
        # 移除文本中的思考过程
        thinking_markers = [
            r'<think>.*?</think>',
            r'\[思考\].*?\[/思考\]',
            r'\[THINKING\].*?\[/THINKING\]',
            r'<思考>.*?</思考>',
            r'\[THOUGHT\].*?\[/THOUGHT\]',
            r'<THINKING>.*?</THINKING>',
            r'<think>.*',  # 处理没有关闭标签的情况
            r'思考：.*?(?=\n\n)',
            r'Thinking:.*?(?=\n\n)',
            r'Let me think:.*?(?=\n\n)',
            r'Let me translate this:.*?(?=\n\n)',
            r'I need to translate:.*?(?=\n\n)',
            r'嗯，.*?(?=\n\n)',
            r'首先，.*?(?=\n\n)',
            r'我应该.*?(?=\n\n)',
            r'我需要.*?(?=\n\n)',
            r'这段文本.*?(?=\n\n)',
            r'这是.*?(?=\n\n)',
            r'I\'ll translate.*?(?=\n\n)',
        ]
        
        # 应用所有思考标记的正则表达式
        for marker in thinking_markers:
            text = re.sub(marker, '', text, flags=re.DOTALL)
        
        # 移除可能的额外空行
        text = re.sub(r'\n{3,}', '\n\n', text)
        
        # 移除开头的空行和空格
        text = text.lstrip()
        
        # 移除翻译指示
        translation_markers = [
            r'^Translation:',
            r'^Translated text:',
            r'^翻译结果：',
            r'^翻译：',
            r'^译文：',
            r'^Here\'s the translation:',
            r'^The translation is:',
            r'^In English:',
            r'^In Chinese:',
            r'^Translated to English:',
            r'^Translated to Chinese:',
        ]
        
        for marker in translation_markers:
            text = re.sub(marker, '', text, flags=re.IGNORECASE).lstrip()
        
        # 移除可能的解释性文本
        explanation_patterns = [
            r'This translates to:.*?\n',
            r'In English, this means:.*?\n',
            r'The translation is:.*?\n',
            r'Translated from.*?:.*?\n',
            r'I would translate this as:.*?\n',
            r'This can be translated as:.*?\n',
        ]
        
        for pattern in explanation_patterns:
            text = re.sub(pattern, '', text, flags=re.IGNORECASE)
        
        return text.strip()

    def _final_cleaning(self, text: str, source_lang: str, target_lang: str) -> str:
        """
        对翻译结果进行最终清理
        
        参数:
            text: 翻译文本
            source_lang: 源语言代码
            target_lang: 目标语言代码
            
        返回:
            清理后的文本
        """
        if not text:
            return ""
        
        # 移除重复内容
        text = self._remove_duplicates(text)
        
        # 移除自我解释内容
        text = self._remove_self_explanations(text, target_lang)
        
        # 移除翻译器标记
        text = re.sub(r'(?i)\b(translated by|powered by|translation:|translation by|machine translation)\b.*?$', '', text, flags=re.MULTILINE)
        
        # 移除引号包裹（如果整个文本被引号包裹）
        text = re.sub(r'^["\'](.+)["\']$', r'\1', text.strip())
        
        # 移除多余的空行
        text = re.sub(r'\n{3,}', '\n\n', text)
        
        # 移除行首尾的空白字符
        lines = [line.strip() for line in text.split('\n')]
        text = '\n'.join(lines)
        
        return text.strip()

    def _get_lang_code(self, lang_code: str) -> str:
        """获取标准化的语言代码"""
        if not lang_code:
            return "en"
            
        # 标准化语言代码
        lang_code = lang_code.lower()
        
        # 语言代码映射
        language_codes = {
            "zh-cn": "zh",
            "zh-tw": "zh",
            "zh-hk": "zh",
            "zh-sg": "zh",
            "zh-hans": "zh",
            "zh-hant": "zh",
            "chinese": "zh",
            "english": "en",
            "spanish": "es",
            "french": "fr",
            "german": "de",
            "japanese": "ja",
            "korean": "ko",
            "russian": "ru",
            "portuguese": "pt",
            "italian": "it",
            "dutch": "nl",
            "arabic": "ar",
            "hindi": "hi",
            "bengali": "bn",
            "punjabi": "pa",
            "turkish": "tr",
            "vietnamese": "vi",
            "thai": "th",
            "indonesian": "id",
            "malay": "ms",
            "swedish": "sv",
            "norwegian": "no",
            "danish": "da",
            "finnish": "fi",
            "polish": "pl",
            "romanian": "ro",
            "greek": "el",
            "czech": "cs",
            "hungarian": "hu",
            "bulgarian": "bg",
            "ukrainian": "uk",
            "hebrew": "he",
            "persian": "fa",
        }
        
        return language_codes.get(lang_code, lang_code)

    def _remove_duplicates(self, text: str) -> str:
        """
        移除文本中的重复内容
        
        参数:
            text: 要处理的文本
            
        返回:
            处理后的文本
        """
        if not text:
            return ""
            
        # 按行分割文本
        lines = text.split('\n')
        
        # 如果行数少于2，无需去重
        if len(lines) < 2:
            return text
        
        # 存储已见过的规范化行内容
        seen_content = set()
        unique_lines = []
        
        for line in lines:
            # 忽略空行，但保留在结果中
            if not line.strip():
                unique_lines.append(line)
                continue
            
            # 规范化行内容以检测重复（移除标点和空格）
            normalized = re.sub(r'[^\w\s]', '', line.lower())
            normalized = re.sub(r'\s+', ' ', normalized).strip()
            
            # 如果不是重复内容，添加到结果中
            if normalized and normalized not in seen_content and len(normalized) > 2:
                seen_content.add(normalized)
                unique_lines.append(line)
        
        return '\n'.join(unique_lines)

    def _clean_special_characters(self, text: str) -> str:
        """
        清理文本中的特殊字符，包括零宽字符和变体选择器等
        
        参数:
            text: 要处理的文本
            
        返回:
            处理后的文本
        """
        if not text:
            return ""
            
        # 记录原始文本长度
        original_length = len(text)
        
        # 特殊字符映射
        special_char_map = {
            # 零宽字符
            '\u200b': '',  # 零宽空格
            '\u200c': '',  # 零宽非连接符
            '\u200d': '',  # 零宽连接符
            '\u200e': '',  # 从左到右标记
            '\u200f': '',  # 从右到左标记
            '\ufeff': '',  # 零宽不换行空格
            
            # 变体选择器
            '\ufe00': '',  # 变体选择器-1
            '\ufe01': '',  # 变体选择器-2
            '\ufe02': '',  # 变体选择器-3
            '\ufe03': '',  # 变体选择器-4
            '\ufe04': '',  # 变体选择器-5
            '\ufe05': '',  # 变体选择器-6
            '\ufe06': '',  # 变体选择器-7
            '\ufe07': '',  # 变体选择器-8
            '\ufe08': '',  # 变体选择器-9
            '\ufe09': '',  # 变体选择器-10
            '\ufe0a': '',  # 变体选择器-11
            '\ufe0b': '',  # 变体选择器-12
            '\ufe0c': '',  # 变体选择器-13
            '\ufe0d': '',  # 变体选择器-14
            '\ufe0e': '',  # 变体选择器-15
            '\ufe0f': '',  # 变体选择器-16
            
            # 控制字符 (ASCII 0-31)
            '\u0000': '', '\u0001': '', '\u0002': '', '\u0003': '', '\u0004': '',
            '\u0005': '', '\u0006': '', '\u0007': '', '\u0008': '', '\u0009': ' ',  # Tab转为空格
            '\u000a': '\n', '\u000b': '', '\u000c': '', '\u000d': '\n',  # 保留换行
            '\u000e': '', '\u000f': '', '\u0010': '', '\u0011': '', '\u0012': '',
            '\u0013': '', '\u0014': '', '\u0015': '', '\u0016': '', '\u0017': '',
            '\u0018': '', '\u0019': '', '\u001a': '', '\u001b': '', '\u001c': '',
            '\u001d': '', '\u001e': '', '\u001f': ''
        }
        
        # 替换特殊字符
        processed_text = text
        replacement_count = 0
        
        for special, replacement in special_char_map.items():
            original_text = processed_text
            processed_text = processed_text.replace(special, replacement)
            
            # 计算替换次数
            if original_text != processed_text:
                current_replacements = original_text.count(special)
                replacement_count += current_replacements
                self.logger.debug(f"替换了 {special} -> {replacement}, 次数: {current_replacements}")
        
        # 处理连续的空格（保留最多两个空格）
        processed_text = re.sub(r' {3,}', '  ', processed_text)
        
        # 如果有替换，记录日志
        if replacement_count > 0:
            self.logger.info(f"处理了{replacement_count}个特殊字符: 原始长度={original_length}, 处理后长度={len(processed_text)}")
        
        return processed_text

    def _get_lang_name(self, lang_code: str) -> str:
        """
        根据语言代码获取语言名称
        
        参数:
            lang_code: 语言代码
            
        返回:
            语言名称
        """
        language_names = {
            "zh": "Chinese",
            "en": "English",
            "ja": "Japanese",
            "ko": "Korean",
            "fr": "French",
            "de": "German",
            "es": "Spanish",
            "it": "Italian",
            "ru": "Russian",
            "pt": "Portuguese",
            "nl": "Dutch",
            "ar": "Arabic",
            "hi": "Hindi",
            "bn": "Bengali",
            "pa": "Punjabi",
            "te": "Telugu",
            "mr": "Marathi",
            "ta": "Tamil",
            "ur": "Urdu",
            "gu": "Gujarati",
            "kn": "Kannada",
            "or": "Odia",
            "ml": "Malayalam",
            "auto": "Auto-detected"
        }
        
        return language_names.get(lang_code, f"Language({lang_code})")

    def _is_target_language(self, text: str, target_lang_code: str) -> bool:
        """
        检查文本是否为目标语言
        
        参数:
            text: 要检查的文本
            target_lang_code: 目标语言代码
            
        返回:
            布尔值，表示文本是否为目标语言
        """
        if not text:
            return True
        
        # 简单语言检测
        if target_lang_code == "zh":
            # 检查是否包含中文字符
            return self.contains_chinese(text)
        elif target_lang_code == "en":
            # 检查是否主要为英文字符
            chinese_ratio = sum(1 for c in text if '\u4e00' <= c <= '\u9fff') / len(text) if text else 0
            return chinese_ratio < 0.1  # 如果中文字符少于10%，认为是英文
        else:
            # 对于其他语言，暂时假设翻译正确
            return True

    def _split_text(self, text: str) -> List[str]:
        """
        将文本分割成多个段落或句子，以便于翻译处理
        
        参数:
            text: 要分割的文本
            
        返回:
            分割后的文本段落列表
        """
        if not text or text.strip() == "":
            return []
            
        # 增加单个段落的最大字符数，减少分割次数
        max_segment_length = 2000  # 从1000增加到2000
            
        # 如果文本较短，不进行分割
        if len(text) < max_segment_length:
            return [text]
            
        # 按段落分割
        paragraphs = re.split(r'\n\s*\n', text)
        segments = []
        
        # 使用更智能的合并策略
        current_segment = ""
        
        for para in paragraphs:
            para = para.strip()
            if not para:
                continue
                
            # 检查当前段落是否可以与当前片段合并
            if len(current_segment) + len(para) < max_segment_length:
                # 可以合并
                if current_segment:
                    current_segment += "\n\n" + para
                else:
                    current_segment = para
            elif len(para) < max_segment_length:
                # 当前段落不能合并，但自身不需要再分割
                if current_segment:
                    segments.append(current_segment)
                current_segment = para
            else:
                # 处理长段落
                # 先添加当前累积的段落
                if current_segment:
                    segments.append(current_segment)
                    current_segment = ""
                
                # 长段落按句子分割
                sentences = re.split(r'(?<=[.!?。！？])\s+', para)
                
                for sentence in sentences:
                    if not sentence.strip():
                        continue
                        
                    # 如果当前段落加上这个句子不超过最大长度，则合并
                    if len(current_segment) + len(sentence) < max_segment_length:
                        if current_segment:
                            current_segment += " " + sentence
                        else:
                            current_segment = sentence
                    else:
                        # 否则添加当前段落，并开始新段落
                        if current_segment:
                            segments.append(current_segment)
                        current_segment = sentence
                
                # 添加最后一个段落
                if current_segment:
                    segments.append(current_segment)
                    current_segment = ""
        
        # 添加最后剩余的段落
        if current_segment:
            segments.append(current_segment)
            
        # 确保至少返回一个段落
        if not segments and text.strip():
            segments.append(text.strip())
            
        # 检查是否有过多的小片段，如果有，尝试合并相邻的小片段
        if len(segments) > 5:
            optimized_segments = []
            temp_segment = ""
            
            for segment in segments:
                if len(temp_segment) + len(segment) < max_segment_length:
                    if temp_segment:
                        temp_segment += "\n\n" + segment
                    else:
                        temp_segment = segment
                else:
                    if temp_segment:
                        optimized_segments.append(temp_segment)
                    temp_segment = segment
            
            if temp_segment:
                optimized_segments.append(temp_segment)
                
            segments = optimized_segments
            
        return segments

    def _get_translation_from_cache(self, text: str, source_lang: str, target_lang: str) -> Optional[str]:
        """
        从缓存中获取翻译结果
        
        参数:
            text: 原文本
            source_lang: 源语言
            target_lang: 目标语言
            
        返回:
            缓存的翻译结果，如果不存在则返回None
        """
        if not text:
            return None
        
        cache_key = f"{text}|{source_lang or 'auto'}|{target_lang}"
        
        # 检查内存缓存
        global translation_cache
        if cache_key in translation_cache:
            logger.info(f"从内存缓存获取翻译结果: {text[:30]}...")
            return translation_cache[cache_key]
        
        # 简化处理，不使用数据库缓存
        try:
            # 这里我们只记录日志，但不尝试访问数据库
            logger.debug(f"跳过数据库缓存检查: {text[:30]}...")
        except Exception as e:
            logger.error(f"从缓存获取翻译失败: {str(e)}")
        
        return None
        
    def _cache_translation(self, text: str, source_lang: str, target_lang: str, result: str) -> None:
        """
        缓存翻译结果
        
        参数:
            text: 原文本
            source_lang: 源语言
            target_lang: 目标语言
            result: 翻译结果
        """
        if not text or not result:
            return
        
        cache_key = f"{text}|{source_lang or 'auto'}|{target_lang}"
        
        # 简化处理，只记录日志但不实际缓存
        try:
            logger.debug(f"跳过缓存翻译结果: {text[:30]}...")
        except Exception as e:
            logger.error(f"缓存翻译结果失败: {str(e)}")

    def translate_batch(self, texts: List[str], source_lang: str, target_lang: str) -> List[str]:
        """
        批量翻译多个文本段落
        
        参数:
            texts: 要翻译的文本列表
            source_lang: 源语言
            target_lang: 目标语言
            
        返回:
            翻译后的文本列表
        """
        if not texts:
            logger.warning("传入空文本列表进行批量翻译")
            return []
            
        # 过滤空文本
        filtered_texts = [text for text in texts if text and text.strip()]
        if not filtered_texts:
            logger.warning("过滤后的文本列表为空")
            return [""] * len(texts)
            
        # 准备结果列表，保持与输入相同长度
        results = [""] * len(texts)
        non_empty_indices = [i for i, text in enumerate(texts) if text and text.strip()]
        
        logger.info(f"批量翻译: {source_lang} -> {target_lang}, 文本数量: {len(filtered_texts)}")
        
        try:
            # 检查是否使用Ollama
            if self.use_ollama and self._check_ollama_available():
                logger.info(f"使用Ollama API进行批量翻译")
                
                # 构建批量翻译提示
                batch_texts = []
                for i, text in enumerate(filtered_texts):
                    batch_texts.append(f"Text {i+1}: {text}")
                
                batch_prompt = f"""Translate the following texts from {self._get_lang_name(source_lang)} to {self._get_lang_name(target_lang)}:

CRITICAL INSTRUCTIONS:
1. ONLY output the translations, one per line
2. DO NOT prefix translations with "Translation N:" or any numbering
3. DO NOT include explanations, quotes, or comments
4. DO NOT include any thoughts or reasoning
5. DO NOT use phrases like "In {self._get_lang_name(target_lang)}:" 
6. Translate each text literally and directly
7. Separate each translation with a line of "===" (three equals signs)
8. Output ONLY the translated texts separated by "==="

{chr(10).join(batch_texts)}"""
                
                logger.info(f"Ollama API请求: URL={self.ollama_api_url}, 模型={self.ollama_model}")
                logger.debug(f"批量翻译提示: {batch_prompt[:100]}...")
                
                # 调用Ollama API
                response = requests.post(
                    f"{self.ollama_api_url}/api/generate",
                    json={
                        "model": self.ollama_model,
                        "prompt": batch_prompt,
                        "stream": False
                    },
                    timeout=60
                )
                
                if response.status_code == 200:
                    # 解析响应
                    response_data = response.json()
                    full_response = response_data.get("response", "")
                    
                    logger.debug(f"Ollama API原始响应: {full_response[:200]}...")
                    
                    # 分割响应为多个翻译结果
                    translated_segments = self._split_ollama_batch_response(full_response, len(filtered_texts))
                    
                    logger.info(f"分割后的翻译结果数量: {len(translated_segments)}")
                    
                    # 将结果填入对应位置
                    for idx, trans_idx in enumerate(non_empty_indices):
                        if idx < len(translated_segments):
                            results[trans_idx] = translated_segments[idx]
                            logger.debug(f"翻译 {idx}: '{texts[trans_idx][:30]}...' -> '{translated_segments[idx][:30]}...'")
                        else:
                            # 如果结果数量不匹配，单独翻译
                            logger.warning(f"批量翻译结果数量不匹配，单独翻译第 {idx} 项")
                            results[trans_idx] = self.translate_sync(texts[trans_idx], source_lang, target_lang)
                else:
                    logger.error(f"Ollama API错误: {response.status_code}, {response.text}")
                    # 回退到单独翻译
                    logger.info("回退到单独翻译模式")
                    for idx in non_empty_indices:
                        results[idx] = self.translate_sync(texts[idx], source_lang, target_lang)
            else:
                # 使用本地模型或API逐个翻译
                logger.info("使用本地模型或API逐个翻译")
                for idx in non_empty_indices:
                    results[idx] = self.translate_sync(texts[idx], source_lang, target_lang)
                    
        except Exception as e:
            logger.error(f"批量翻译失败: {str(e)}", exc_info=True)
            # 出错时尝试单独翻译
            logger.info("异常后回退到单独翻译模式")
            for idx in non_empty_indices:
                try:
                    results[idx] = self.translate_sync(texts[idx], source_lang, target_lang)
                except Exception as inner_e:
                    logger.error(f"单独翻译失败: {str(inner_e)}")
                    results[idx] = texts[idx]  # 失败时使用原文
        
        logger.info(f"批量翻译完成，结果数量: {len(results)}")
        return results
        
    def _split_ollama_batch_response(self, response: str, expected_count: int) -> List[str]:
        """
        分割Ollama批量翻译响应为多个翻译结果
        
        参数:
            response: Ollama响应文本
            expected_count: 预期的翻译结果数量
            
        返回:
            翻译结果列表
        """
        # 尝试使用分隔符分割
        segments = re.split(r'\n---\n|\n\*\*\*\n|\n===\n', response)
        
        # 如果分割结果数量与预期不符，尝试其他分割方式
        if len(segments) != expected_count:
            # 尝试按翻译标记分割
            segments = re.split(r'\nTranslation\s*\d*\s*:\s*|\nTranslated\s*\d*\s*:\s*', response)
            # 移除第一个元素，如果它不包含翻译结果
            if segments and not segments[0].strip():
                segments = segments[1:]
                
        # 如果仍然不匹配，尝试按数字标记分割
        if len(segments) != expected_count:
            segments = re.split(r'\n\d+\.\s*', response)
            # 移除第一个元素，如果它不包含翻译结果
            if segments and not segments[0].strip():
                segments = segments[1:]
                
        # 如果仍然不匹配，尝试按数字+冒号分割
        if len(segments) != expected_count:
            segments = re.split(r'\n\d+:\s*', response)
            # 移除第一个元素，如果它不包含翻译结果
            if segments and not segments[0].strip():
                segments = segments[1:]
        
        # 移除空段落
        segments = [seg for seg in segments if seg.strip()]
        
        # 清理每个段落中的Translation前缀
        cleaned_segments = []
        for seg in segments:
            if not seg.strip():
                continue
                
            # 移除每行开头的"Translation N:"前缀
            lines = seg.split('\n')
            cleaned_lines = []
            
            for line in lines:
                # 移除"Translation N:"和类似前缀
                cleaned_line = re.sub(r'^Translation\s*\d*\s*:\s*', '', line)
                cleaned_line = re.sub(r'^Translated\s*\d*\s*:\s*', '', cleaned_line)
                cleaned_line = re.sub(r'^\d+\.\s*Translation\s*:\s*', '', cleaned_line)
                cleaned_line = re.sub(r'^\d+:\s*', '', cleaned_line)
                cleaned_line = re.sub(r'^\d+\.\s*', '', cleaned_line)
                cleaned_lines.append(cleaned_line)
                
            # 重新组合行
            cleaned_seg = '\n'.join(cleaned_lines)
            # 应用一般清理
            cleaned_seg = self._clean_translation_result(cleaned_seg.strip())
            cleaned_segments.append(cleaned_seg)
        
        # 如果分割后的数量仍然不匹配，记录警告
        if len(cleaned_segments) != expected_count:
            logger.warning(f"分割后的翻译结果数量({len(cleaned_segments)})与预期数量({expected_count})不匹配")
        
        # 返回结果，如果结果数量少于预期，用空字符串填充
        logger.info(f"分割后的翻译结果数量: {len(cleaned_segments)}, 预期数量: {expected_count}")
        return cleaned_segments[:expected_count] if len(cleaned_segments) >= expected_count else cleaned_segments + [""] * (expected_count - len(cleaned_segments))

    async def translate(self, text: str, source_lang: str, target_lang: str) -> Tuple[str, str]:
        """
        异步翻译文本
        
        参数:
            text: 要翻译的文本
            source_lang: 源语言
            target_lang: 目标语言
            
        返回:
            元组 (翻译结果, 检测到的语言)
        """
        # 清理特殊字符
        cleaned_text = self._clean_special_characters(text)
        
        # 如果清理后文本为空，直接返回原文
        if not cleaned_text and text:
            self.logger.warning("清理特殊字符后文本为空，返回原文")
            return text, source_lang
            
        # 使用清理后的文本进行翻译
        return self.translate_sync_cached(cleaned_text or text, target_lang, source_lang)