#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LivePortrait 预训练权重下载脚本
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_huggingface_cli():
    """检查 huggingface-cli 是否可用"""
    try:
        result = subprocess.run(['huggingface-cli', '--version'], 
                              capture_output=True, text=True, check=True)
        logger.info(f"huggingface-cli 版本: {result.stdout.strip()}")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        logger.error("huggingface-cli 未安装或不可用")
        return False

def install_huggingface_hub():
    """安装 huggingface_hub"""
    try:
        logger.info("正在安装 huggingface_hub...")
        subprocess.run([sys.executable, '-m', 'pip', 'install', '-U', 'huggingface_hub[cli]'], 
                      check=True)
        logger.info("huggingface_hub 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"安装 huggingface_hub 失败: {e}")
        return False

def download_liveportrait_weights():
    """下载 LivePortrait 预训练权重"""
    try:
        # 获取当前脚本所在目录
        script_dir = Path(__file__).parent
        pretrained_weights_dir = script_dir / "pretrained_weights"

        logger.info(f"下载目录: {pretrained_weights_dir}")

        # 创建目录
        pretrained_weights_dir.mkdir(exist_ok=True)

        # 尝试使用 huggingface-cli
        try:
            cmd = [
                'huggingface-cli', 'download',
                'KwaiVGI/LivePortrait',
                '--local-dir', str(pretrained_weights_dir),
                '--exclude', '*.git*', 'README.md', 'docs'
            ]

            logger.info(f"尝试使用 huggingface-cli: {' '.join(cmd)}")

            # 执行下载
            process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT,
                                     text=True, universal_newlines=True)

            # 实时输出下载进度
            for line in process.stdout:
                print(line.strip())

            process.wait()

            if process.returncode == 0:
                logger.info("LivePortrait 权重下载成功")
                return True
            else:
                logger.warning(f"huggingface-cli 下载失败，返回码: {process.returncode}")

        except Exception as e:
            logger.warning(f"huggingface-cli 下载失败: {e}")

        # 如果 CLI 失败，尝试使用 Python API
        logger.info("尝试使用 Python API 下载...")
        try:
            from huggingface_hub import snapshot_download

            logger.info("开始使用 Python API 下载 LivePortrait 权重...")

            # 下载整个仓库
            snapshot_download(
                repo_id="KwaiVGI/LivePortrait",
                local_dir=str(pretrained_weights_dir),
                ignore_patterns=["*.git*", "README.md", "docs/*"]
            )

            logger.info("使用 Python API 下载成功")
            return True

        except Exception as e:
            logger.error(f"Python API 下载也失败: {e}")
            return False

    except Exception as e:
        logger.error(f"下载过程中出错: {e}")
        return False

def verify_weights():
    """验证下载的权重文件"""
    script_dir = Path(__file__).parent
    pretrained_weights_dir = script_dir / "pretrained_weights"
    
    # 检查必要的文件
    required_files = [
        "liveportrait/base_models/appearance_feature_extractor.pth",
        "liveportrait/base_models/motion_extractor.pth", 
        "liveportrait/base_models/spade_generator.pth",
        "liveportrait/base_models/warping_module.pth"
    ]
    
    missing_files = []
    for file_path in required_files:
        full_path = pretrained_weights_dir / file_path
        if not full_path.exists():
            missing_files.append(file_path)
        else:
            file_size = full_path.stat().st_size
            logger.info(f"✓ {file_path} ({file_size / 1024 / 1024:.1f} MB)")
    
    if missing_files:
        logger.warning(f"缺少以下文件: {missing_files}")
        return False
    else:
        logger.info("所有必要的权重文件都已下载")
        return True

def main():
    """主函数"""
    logger.info("开始下载 LivePortrait 预训练权重...")
    
    # 检查 huggingface-cli
    if not check_huggingface_cli():
        logger.info("尝试安装 huggingface_hub...")
        if not install_huggingface_hub():
            logger.error("无法安装 huggingface_hub，请手动安装: pip install -U 'huggingface_hub[cli]'")
            return False
        
        # 重新检查
        if not check_huggingface_cli():
            logger.error("安装后仍无法使用 huggingface-cli")
            return False
    
    # 下载权重
    if download_liveportrait_weights():
        # 验证下载结果
        if verify_weights():
            logger.info("LivePortrait 权重下载和验证完成！")
            return True
        else:
            logger.error("权重文件验证失败")
            return False
    else:
        logger.error("权重下载失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
