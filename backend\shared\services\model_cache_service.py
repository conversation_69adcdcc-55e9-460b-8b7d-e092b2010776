#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型缓存和预加载服务
优化数字人生成性能
"""

import os
import time
import json
import hashlib
import threading
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import logging
from dataclasses import dataclass
from pathlib import Path

logger = logging.getLogger(__name__)

@dataclass
class ModelInfo:
    """模型信息"""
    name: str
    path: str
    size: int
    last_used: datetime
    load_time: float
    usage_count: int
    is_loaded: bool = False

@dataclass
class CacheItem:
    """缓存项"""
    key: str
    data: Any
    created_at: datetime
    last_accessed: datetime
    access_count: int
    size: int

class ModelCacheService:
    """模型缓存服务"""
    
    def __init__(self, cache_dir: str = "cache", max_cache_size: int = 10 * 1024 * 1024 * 1024):  # 10GB
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        self.max_cache_size = max_cache_size
        
        # 模型管理
        self.loaded_models: Dict[str, Any] = {}
        self.model_info: Dict[str, ModelInfo] = {}
        
        # 结果缓存
        self.result_cache: Dict[str, CacheItem] = {}
        self.current_cache_size = 0
        
        # 预加载配置
        self.preload_models = [
            "sadtalker_base",
            "tts_base", 
            "face_enhancement",
            "emotion_detection"
        ]
        
        # 线程锁
        self._lock = threading.RLock()
        
        # 启动后台任务
        self._start_background_tasks()
    
    def preload_models(self):
        """预加载常用模型"""
        logger.info("开始预加载模型...")
        
        for model_name in self.preload_models:
            try:
                self.load_model(model_name)
                logger.info(f"预加载模型成功: {model_name}")
            except Exception as e:
                logger.error(f"预加载模型失败: {model_name}, 错误: {e}")
        
        logger.info("模型预加载完成")
    
    def load_model(self, model_name: str) -> Any:
        """加载模型"""
        with self._lock:
            if model_name in self.loaded_models:
                # 更新使用信息
                if model_name in self.model_info:
                    self.model_info[model_name].last_used = datetime.now()
                    self.model_info[model_name].usage_count += 1
                return self.loaded_models[model_name]
            
            start_time = time.time()
            
            try:
                # 根据模型名称加载对应模型
                model = self._load_model_by_name(model_name)
                load_time = time.time() - start_time
                
                # 存储模型
                self.loaded_models[model_name] = model
                
                # 记录模型信息
                self.model_info[model_name] = ModelInfo(
                    name=model_name,
                    path=f"models/{model_name}",
                    size=self._estimate_model_size(model),
                    last_used=datetime.now(),
                    load_time=load_time,
                    usage_count=1,
                    is_loaded=True
                )
                
                logger.info(f"模型加载成功: {model_name}, 耗时: {load_time:.2f}s")
                return model
                
            except Exception as e:
                logger.error(f"模型加载失败: {model_name}, 错误: {e}")
                raise
    
    def _load_model_by_name(self, model_name: str) -> Any:
        """根据名称加载具体模型"""
        if model_name == "sadtalker_base":
            # 模拟SadTalker模型加载
            return {"type": "sadtalker", "loaded": True, "version": "1.0"}
        elif model_name == "tts_base":
            # 模拟TTS模型加载
            return {"type": "tts", "loaded": True, "voices": ["zh-CN", "en-US"]}
        elif model_name == "face_enhancement":
            # 模拟面部增强模型
            return {"type": "face_enhancement", "loaded": True, "quality": "high"}
        elif model_name == "emotion_detection":
            # 模拟情感检测模型
            return {"type": "emotion", "loaded": True, "emotions": ["happy", "sad", "neutral"]}
        else:
            raise ValueError(f"未知模型: {model_name}")
    
    def _estimate_model_size(self, model: Any) -> int:
        """估算模型大小"""
        # 简单估算，实际应该根据模型类型计算
        return 500 * 1024 * 1024  # 500MB
    
    def unload_model(self, model_name: str):
        """卸载模型"""
        with self._lock:
            if model_name in self.loaded_models:
                del self.loaded_models[model_name]
                if model_name in self.model_info:
                    self.model_info[model_name].is_loaded = False
                logger.info(f"模型已卸载: {model_name}")
    
    def get_model_stats(self) -> Dict[str, Any]:
        """获取模型统计信息"""
        with self._lock:
            stats = {
                "loaded_models": len(self.loaded_models),
                "total_models": len(self.model_info),
                "memory_usage": sum(info.size for info in self.model_info.values() if info.is_loaded),
                "models": []
            }
            
            for name, info in self.model_info.items():
                stats["models"].append({
                    "name": name,
                    "is_loaded": info.is_loaded,
                    "size_mb": info.size / (1024 * 1024),
                    "load_time": info.load_time,
                    "usage_count": info.usage_count,
                    "last_used": info.last_used.isoformat()
                })
            
            return stats
    
    def cache_result(self, key: str, data: Any, ttl_hours: int = 24):
        """缓存生成结果"""
        with self._lock:
            # 生成缓存键
            cache_key = self._generate_cache_key(key)
            
            # 序列化数据
            serialized_data = self._serialize_data(data)
            data_size = len(serialized_data)
            
            # 检查缓存大小
            if self.current_cache_size + data_size > self.max_cache_size:
                self._cleanup_cache()
            
            # 存储到文件
            cache_file = self.cache_dir / f"{cache_key}.cache"
            with open(cache_file, 'wb') as f:
                f.write(serialized_data)
            
            # 记录缓存项
            self.result_cache[cache_key] = CacheItem(
                key=cache_key,
                data=cache_file,
                created_at=datetime.now(),
                last_accessed=datetime.now(),
                access_count=1,
                size=data_size
            )
            
            self.current_cache_size += data_size
            logger.info(f"结果已缓存: {key}, 大小: {data_size / (1024*1024):.2f}MB")
    
    def get_cached_result(self, key: str) -> Optional[Any]:
        """获取缓存结果"""
        with self._lock:
            cache_key = self._generate_cache_key(key)
            
            if cache_key not in self.result_cache:
                return None
            
            cache_item = self.result_cache[cache_key]
            cache_file = cache_item.data
            
            # 检查文件是否存在
            if not cache_file.exists():
                del self.result_cache[cache_key]
                return None
            
            # 更新访问信息
            cache_item.last_accessed = datetime.now()
            cache_item.access_count += 1
            
            # 读取数据
            try:
                with open(cache_file, 'rb') as f:
                    serialized_data = f.read()
                
                data = self._deserialize_data(serialized_data)
                logger.info(f"缓存命中: {key}")
                return data
                
            except Exception as e:
                logger.error(f"读取缓存失败: {key}, 错误: {e}")
                return None
    
    def _generate_cache_key(self, key: str) -> str:
        """生成缓存键"""
        return hashlib.md5(key.encode()).hexdigest()
    
    def _serialize_data(self, data: Any) -> bytes:
        """序列化数据"""
        if isinstance(data, (str, bytes)):
            return data.encode() if isinstance(data, str) else data
        else:
            return json.dumps(data, default=str).encode()
    
    def _deserialize_data(self, data: bytes) -> Any:
        """反序列化数据"""
        try:
            return json.loads(data.decode())
        except:
            return data.decode()
    
    def _cleanup_cache(self):
        """清理缓存"""
        logger.info("开始清理缓存...")
        
        # 按最后访问时间排序
        sorted_items = sorted(
            self.result_cache.items(),
            key=lambda x: x[1].last_accessed
        )
        
        # 删除最旧的缓存项，直到释放足够空间
        target_size = self.max_cache_size * 0.7  # 清理到70%
        
        for cache_key, cache_item in sorted_items:
            if self.current_cache_size <= target_size:
                break
            
            # 删除文件
            if cache_item.data.exists():
                cache_item.data.unlink()
            
            # 更新大小
            self.current_cache_size -= cache_item.size
            
            # 删除记录
            del self.result_cache[cache_key]
            
            logger.info(f"清理缓存项: {cache_key}")
        
        logger.info(f"缓存清理完成，当前大小: {self.current_cache_size / (1024*1024):.2f}MB")
    
    def _start_background_tasks(self):
        """启动后台任务"""
        # 预加载模型
        threading.Thread(target=self.preload_models, daemon=True).start()
        
        # 定期清理任务
        def periodic_cleanup():
            while True:
                time.sleep(3600)  # 每小时执行一次
                try:
                    self._cleanup_expired_cache()
                except Exception as e:
                    logger.error(f"定期清理失败: {e}")
        
        threading.Thread(target=periodic_cleanup, daemon=True).start()
    
    def _cleanup_expired_cache(self):
        """清理过期缓存"""
        with self._lock:
            now = datetime.now()
            expired_keys = []
            
            for cache_key, cache_item in self.result_cache.items():
                # 检查是否过期（24小时）
                if now - cache_item.created_at > timedelta(hours=24):
                    expired_keys.append(cache_key)
            
            # 删除过期项
            for cache_key in expired_keys:
                cache_item = self.result_cache[cache_key]
                if cache_item.data.exists():
                    cache_item.data.unlink()
                
                self.current_cache_size -= cache_item.size
                del self.result_cache[cache_key]
            
            if expired_keys:
                logger.info(f"清理过期缓存: {len(expired_keys)}项")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        with self._lock:
            return {
                "cache_items": len(self.result_cache),
                "cache_size_mb": self.current_cache_size / (1024 * 1024),
                "max_size_mb": self.max_cache_size / (1024 * 1024),
                "usage_percent": (self.current_cache_size / self.max_cache_size) * 100,
                "hit_rate": self._calculate_hit_rate()
            }
    
    def _calculate_hit_rate(self) -> float:
        """计算缓存命中率"""
        if not self.result_cache:
            return 0.0
        
        total_access = sum(item.access_count for item in self.result_cache.values())
        if total_access == 0:
            return 0.0
        
        # 简化的命中率计算
        return min(100.0, (total_access / len(self.result_cache)) * 10)

# 全局实例
model_cache_service = ModelCacheService()
