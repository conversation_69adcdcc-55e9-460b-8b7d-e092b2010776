import os
from typing import Dict, Optional
from services.open_source_tts import get_open_source_tts_service
from services.open_source_lip_sync import get_open_source_lip_sync
from services.phonetics_database import get_phonetics_database
from services.progress_tracker import get_progress_tracker

class EnhancedTeachingService:
    """增强的教学服务"""
    
    def __init__(self):
        self.tts = get_open_source_tts_service()
        self.lip_sync = get_open_source_lip_sync()
        self.phonetics_db = get_phonetics_database()
        self.progress_tracker = get_progress_tracker()
    
    async def start_lesson(self,
                          user_id: str,
                          lesson_id: str,
                          language: str) -> Dict:
        """开始课程"""
        try:
            # 获取课程内容
            lesson = self._get_lesson(lesson_id, language)
            if not lesson:
                return {"error": "课程不存在"}
            
            # 生成教学内容
            steps = []
            for step in lesson["steps"]:
                # 生成语音和口型
                audio_path = self.tts.text_to_speech(
                    step["text"],
                    language
                )
                
                visemes = self.lip_sync.generate_visemes(audio_path)
                
                # 获取音标和发音技巧
                phonetics = self.phonetics_db.get_phonetics(
                    step["text"],
                    language
                )
                
                pronunciation_tips = []
                if phonetics:
                    for phoneme in phonetics.split():
                        tip = self.phonetics_db.get_pronunciation_tips(
                            language,
                            phoneme
                        )
                        if tip:
                            pronunciation_tips.append(tip)
                
                steps.append({
                    "text": step["text"],
                    "audio": audio_path,
                    "visemes": visemes,
                    "phonetics": phonetics,
                    "pronunciation_tips": pronunciation_tips,
                    "type": step["type"],
                    "hints": step.get("hints", [])
                })
            
            # 记录课程开始
            self.progress_tracker.update_progress(
                user_id,
                language,
                lesson_id,
                0.0,
                "开始学习"
            )
            
            return {
                "lesson_id": lesson_id,
                "title": lesson["title"],
                "description": lesson["description"],
                "steps": steps
            }
            
        except Exception as e:
            print(f"课程加载失败: {e}")
            return {"error": str(e)}
    
    async def practice_word(self,
                          user_id: str,
                          word: str,
                          language: str,
                          user_audio: Optional[str] = None) -> Dict:
        """练习单词"""
        try:
            # 生成标准发音
            audio_path = self.tts.text_to_speech(word, language)
            visemes = self.lip_sync.generate_visemes(audio_path)
            
            # 获取音标和发音技巧
            phonetics = self.phonetics_db.get_phonetics(word, language)
            pronunciation_tips = []
            if phonetics:
                for phoneme in phonetics.split():
                    tip = self.phonetics_db.get_pronunciation_tips(
                        language,
                        phoneme
                    )
                    if tip:
                        pronunciation_tips.append(tip)
            
            result = {
                "word": word,
                "standard_audio": audio_path,
                "visemes": visemes,
                "phonetics": phonetics,
                "pronunciation_tips": pronunciation_tips
            }
            
            # 如果有用户录音，进行评估
            if user_audio:
                score = self._evaluate_pronunciation(user_audio, word)
                feedback = self._generate_feedback(score)
                
                # 更新进度
                self.progress_tracker.update_progress(
                    user_id,
                    language,
                    f"word_{word}",
                    score,
                    feedback
                )
                
                result.update({
                    "score": score,
                    "feedback": feedback
                })
            
            return result
            
        except Exception as e:
            print(f"单词练习失败: {e}")
            return {"error": str(e)}
    
    def get_learning_progress(self,
                            user_id: str,
                            language: str) -> Dict:
        """获取学习进度"""
        return self.progress_tracker.get_learning_statistics(
            user_id,
            language
        )
    
    def _get_lesson(self, lesson_id: str, language: str) -> Optional[Dict]:
        """获取课程内容"""
        # TODO: 实现课程加载逻辑
        return None
    
    def _evaluate_pronunciation(self,
                              user_audio: str,
                              target_word: str) -> float:
        """评估发音"""
        # TODO: 实现发音评估逻辑
        return 0.0
    
    def _generate_feedback(self, score: float) -> str:
        """生成反馈"""
        if score >= 0.9:
            return "发音非常标准！继续保持！"
        elif score >= 0.7:
            return "发音不错，但还可以更准确。"
        elif score >= 0.5:
            return "发音基本正确，需要多加练习。"
        else:
            return "发音需要改进，请多听标准发音并练习。"

def get_enhanced_teaching_service():
    """获取增强的教学服务实例"""
    return EnhancedTeachingService() 