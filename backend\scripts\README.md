# 系统工具脚本

该目录包含各种用于维护、诊断和修复系统的工具脚本。以下是主要脚本的概述：

## 🛠 WAN 2.1模型修复工具

### 1. 一键修复 - fix_wan_issue.py

WAN模型的一键综合修复工具，可以解决大多数常见问题。

```bash
python backend/scripts/fix_wan_issue.py [--verbose]
```

这个脚本会自动执行以下操作：
- 检查模型缓存目录
- 从local_models复制模型到models_cache
- 修复模型目录结构
- 修复配置文件中的路径

### 2. 修复模型目录结构 - fix_wan_model_structure.py

专门用于修复模型目录结构问题（如缺少scheduler/unet/vae子目录）。

```bash
python backend/scripts/fix_wan_model_structure.py
```

### 3. 复制模型文件 - copy_wan_models.py

将本地模型目录中的模型复制到models_cache目录。

```bash
python backend/scripts/copy_wan_models.py
```

### 4. 测试修复结果 - test_fix.py

测试模型修复是否成功。

```bash
python backend/test_fix.py
```

## 常见错误及修复方法

1. **错误：模型 1.3B 初始化失败，无法生成视频**
   - 运行 `fix_wan_issue.py` 进行一键修复

2. **错误：模型目录结构不完整，缺少以下子目录: scheduler, unet**
   - 运行 `fix_wan_model_structure.py` 修复目录结构

3. **错误：模型目录不存在**
   - 运行 `copy_wan_models.py` 复制模型文件

详细文档请参考 [WAN 2.1 模型修复指南](README_WAN_FIX.md)。

## 其他工具脚本

- `test_services.py` - 测试各个服务是否正常工作
- `download_wan_1_3b.py` - 下载WAN 1.3B模型
- `download_tts_models.py` - 下载TTS语音合成模型
- `fix_model_permissions.py` - 修复模型文件权限问题
- `fix_wan_model_dirs.py` - 修复WAN模型目录结构（旧版）
- `fix_model_symlinks.py` - 创建模型符号链接（仅Linux/Mac）

## 使用说明

1. 大多数脚本应在项目根目录下运行
2. 部分脚本需要管理员权限，特别是处理文件权限相关的脚本
3. 修复完成后通常需要重启应用程序

## 贡献

如果您开发了新的工具脚本，请确保：
1. 添加详细的文档说明
2. 提供适当的错误处理
3. 支持--help参数显示使用说明
4. 在这个README文件中更新相关信息 