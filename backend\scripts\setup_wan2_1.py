#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
设置Wan2.1环境的脚本
包括克隆仓库、设置模型路径和配置环境变量
"""

import os
import sys
import subprocess
import logging
import argparse
import shutil
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def run_command(cmd, cwd=None):
    """运行shell命令并返回结果"""
    try:
        logger.info(f"执行命令: {cmd}")
        result = subprocess.run(
            cmd, 
            shell=True, 
            check=True, 
            cwd=cwd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        logger.info(f"命令执行成功: {result.stdout}")
        return True, result.stdout
    except subprocess.CalledProcessError as e:
        logger.error(f"命令执行失败: {e.stderr}")
        return False, e.stderr

def clone_wan_repo(repo_path, force_clone=False):
    """克隆Wan2.1官方仓库"""
    repo_url = "https://github.com/Wan-Video/Wan2.1.git"
    
    # 检查目录是否已存在
    if os.path.exists(repo_path) and os.path.isdir(repo_path):
        if not force_clone:
            logger.info(f"仓库目录已存在: {repo_path}")
            
            # 检查是否是git仓库
            if os.path.exists(os.path.join(repo_path, ".git")):
                logger.info("检测到Git仓库，尝试拉取最新代码")
                success, output = run_command("git pull", cwd=repo_path)
                if success:
                    logger.info("成功更新仓库")
                else:
                    logger.warning("无法更新仓库，可能需要手动解决冲突")
                return True
            else:
                logger.warning(f"目录存在但不是Git仓库: {repo_path}")
                return False
        else:
            logger.info(f"强制克隆模式，移除现有目录: {repo_path}")
            shutil.rmtree(repo_path)
    
    # 创建父目录
    os.makedirs(os.path.dirname(repo_path), exist_ok=True)
    
    # 克隆仓库
    success, output = run_command(f"git clone {repo_url} {repo_path}")
    if success:
        logger.info(f"成功克隆仓库到: {repo_path}")
        return True
    else:
        logger.error(f"克隆仓库失败: {output}")
        return False

def install_requirements(repo_path):
    """安装Wan2.1的依赖库"""
    requirements_file = os.path.join(repo_path, "requirements.txt")
    
    if not os.path.exists(requirements_file):
        logger.error(f"找不到依赖文件: {requirements_file}")
        return False
    
    success, output = run_command(f"pip install -r {requirements_file}")
    if success:
        logger.info("成功安装依赖")
        return True
    else:
        logger.error(f"安装依赖失败: {output}")
        return False

def setup_model_dirs(repo_path, model_dir):
    """设置模型目录结构"""
    # 创建模型缓存目录
    models_cache_dir = os.path.join(repo_path, "models_cache")
    os.makedirs(models_cache_dir, exist_ok=True)
    
    # 创建输出目录
    outputs_dir = os.path.join(repo_path, "outputs")
    os.makedirs(outputs_dir, exist_ok=True)
    
    # 如果提供了模型目录，创建符号链接或复制
    if model_dir:
        if os.path.exists(model_dir):
            logger.info(f"使用指定的模型目录: {model_dir}")
            
            # 检查t2v和i2v模型是否存在
            t2v_dirs = [f for f in os.listdir(model_dir) if f.startswith("Wan2.1-T2V")]
            i2v_dirs = [f for f in os.listdir(model_dir) if f.startswith("Wan2.1-I2V")]
            
            # 链接T2V模型
            for t2v_dir in t2v_dirs:
                src = os.path.join(model_dir, t2v_dir)
                dst = os.path.join(models_cache_dir, t2v_dir)
                
                if os.path.exists(dst):
                    logger.info(f"目标路径已存在，跳过: {dst}")
                    continue
                
                try:
                    # 在Windows上使用复制，在Unix上使用符号链接
                    if os.name == 'nt':
                        logger.info(f"在Windows上创建目录硬链接: {src} -> {dst}")
                        # 使用mklink /J (目录连接)
                        run_command(f'mklink /J "{dst}" "{src}"')
                    else:
                        logger.info(f"创建符号链接: {src} -> {dst}")
                        os.symlink(src, dst, target_is_directory=True)
                except Exception as e:
                    logger.error(f"创建链接失败: {str(e)}")
                    logger.info("尝试复制模型文件...")
                    try:
                        shutil.copytree(src, dst)
                        logger.info(f"成功复制模型文件: {src} -> {dst}")
                    except Exception as e2:
                        logger.error(f"复制模型文件失败: {str(e2)}")
            
            # 链接I2V模型
            for i2v_dir in i2v_dirs:
                src = os.path.join(model_dir, i2v_dir)
                dst = os.path.join(models_cache_dir, i2v_dir)
                
                if os.path.exists(dst):
                    logger.info(f"目标路径已存在，跳过: {dst}")
                    continue
                
                try:
                    # 在Windows上使用复制，在Unix上使用符号链接
                    if os.name == 'nt':
                        logger.info(f"在Windows上创建目录硬链接: {src} -> {dst}")
                        run_command(f'mklink /J "{dst}" "{src}"')
                    else:
                        logger.info(f"创建符号链接: {src} -> {dst}")
                        os.symlink(src, dst, target_is_directory=True)
                except Exception as e:
                    logger.error(f"创建链接失败: {str(e)}")
                    logger.info("尝试复制模型文件...")
                    try:
                        shutil.copytree(src, dst)
                        logger.info(f"成功复制模型文件: {src} -> {dst}")
                    except Exception as e2:
                        logger.error(f"复制模型文件失败: {str(e2)}")
            
            if not t2v_dirs and not i2v_dirs:
                logger.warning(f"在模型目录中未找到Wan2.1模型: {model_dir}")
                logger.info(f"请手动将Wan2.1模型文件放入: {models_cache_dir}")
                logger.info("模型目录结构应为:")
                logger.info("models_cache/")
                logger.info("  ├── Wan2.1-T2V-14B/")
                logger.info("  ├── Wan2.1-T2V-1.3B/")
                logger.info("  ├── Wan2.1-I2V-14B-720P/")
                logger.info("  └── Wan2.1-I2V-14B-480P/")
        else:
            logger.warning(f"指定的模型目录不存在: {model_dir}")
            logger.info(f"请手动将Wan2.1模型文件放入: {models_cache_dir}")
            logger.info("模型目录结构应为:")
            logger.info("models_cache/")
            logger.info("  ├── Wan2.1-T2V-14B/")
            logger.info("  ├── Wan2.1-T2V-1.3B/")
            logger.info("  ├── Wan2.1-I2V-14B-720P/")
            logger.info("  └── Wan2.1-I2V-14B-480P/")
    
    return True

def validate_setup(repo_path):
    """验证设置是否正确"""
    # 检查必要文件是否存在
    essential_files = [
        "generate.py",
        "requirements.txt"
    ]
    
    for file in essential_files:
        if not os.path.exists(os.path.join(repo_path, file)):
            logger.error(f"缺少必要文件: {file}")
            return False
    
    # 检查模型缓存目录
    models_cache_dir = os.path.join(repo_path, "models_cache")
    if not os.path.exists(models_cache_dir):
        logger.warning(f"模型缓存目录不存在: {models_cache_dir}")
        return False
    
    # 检查输出目录
    outputs_dir = os.path.join(repo_path, "outputs")
    if not os.path.exists(outputs_dir):
        logger.warning(f"输出目录不存在: {outputs_dir}")
        return False
    
    logger.info("验证通过，Wan2.1环境设置正确")
    return True

def create_test_script(repo_path):
    """创建测试脚本"""
    test_script_path = os.path.join(repo_path, "test_wan2_1.py")
    
    test_script_content = """#!/usr/bin/env python
# -*- coding: utf-8 -*-
\"\"\"
测试Wan2.1环境是否正确设置
\"\"\"

import os
import sys
import subprocess
import argparse

def main():
    parser = argparse.ArgumentParser(description="测试Wan2.1环境")
    parser.add_argument("--model", choices=["t2v-14B", "t2v-1.3B", "i2v-14B"], default="t2v-1.3B", help="要测试的模型")
    parser.add_argument("--prompt", default="A beautiful sunset over the mountains", help="测试提示词")
    parser.add_argument("--size", default="832*480", help="视频尺寸")
    args = parser.parse_args()
    
    # 构建命令
    cmd = [
        "python", "generate.py",
        "--task", args.model,
        "--size", args.size,
        "--offload_model", "True",
        "--t5_cpu",
        "--prompt", args.prompt
    ]
    
    # 根据模型类型添加特定参数
    if args.model == "t2v-1.3B":
        cmd.extend(["--sample_shift", "8", "--sample_guide_scale", "6"])
        cmd.extend(["--ckpt_dir", "./models_cache/Wan2.1-T2V-1.3B"])
    elif args.model == "t2v-14B":
        cmd.extend(["--ckpt_dir", "./models_cache/Wan2.1-T2V-14B"])
    elif args.model == "i2v-14B":
        # 对于i2v-14B，我们需要一个图像文件
        cmd.extend(["--ckpt_dir", "./models_cache/Wan2.1-I2V-14B-720P"])
        # 检查是否存在测试图像，如果不存在则跳过i2v测试
        if not os.path.exists("test_image.jpg"):
            print("错误: 测试图像不存在，无法测试i2v-14B模型")
            print("请将测试图像保存为 'test_image.jpg' 后再试")
            return 1
        cmd.extend(["--image", "test_image.jpg"])
    
    # 执行命令
    print(f"执行命令: {' '.join(cmd)}")
    try:
        subprocess.run(cmd, check=True)
        print("测试成功!")
        return 0
    except subprocess.CalledProcessError as e:
        print(f"测试失败: {str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
"""
    
    with open(test_script_path, "w", encoding="utf-8") as f:
        f.write(test_script_content)
    
    logger.info(f"已创建测试脚本: {test_script_path}")
    return test_script_path

def main():
    parser = argparse.ArgumentParser(description="设置Wan2.1环境")
    parser.add_argument("--repo_path", default=os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "Wan2.1"), 
                        help="Wan2.1仓库路径")
    parser.add_argument("--model_dir", default=os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "local_models", "wan", "Wan2.1"), 
                        help="模型目录")
    parser.add_argument("--force_clone", action="store_true", help="强制重新克隆仓库")
    parser.add_argument("--skip_requirements", action="store_true", help="跳过安装依赖")
    parser.add_argument("--create_test", action="store_true", help="创建测试脚本")
    args = parser.parse_args()
    
    # 获取绝对路径
    repo_path = os.path.abspath(args.repo_path)
    model_dir = os.path.abspath(args.model_dir) if args.model_dir else None
    
    logger.info("开始设置Wan2.1环境...")
    logger.info(f"仓库路径: {repo_path}")
    logger.info(f"模型目录: {model_dir}")
    
    # 克隆仓库
    if not clone_wan_repo(repo_path, args.force_clone):
        logger.error("克隆仓库失败")
        return 1
    
    # 安装依赖
    if not args.skip_requirements:
        if not install_requirements(repo_path):
            logger.error("安装依赖失败")
            return 1
    
    # 设置模型目录
    if not setup_model_dirs(repo_path, model_dir):
        logger.error("设置模型目录失败")
        return 1
    
    # 验证设置
    if not validate_setup(repo_path):
        logger.warning("验证未通过，可能需要手动配置")
    
    # 创建测试脚本
    if args.create_test:
        test_script_path = create_test_script(repo_path)
        logger.info(f"可以使用以下命令测试环境: python {test_script_path}")
    
    logger.info("Wan2.1环境设置完成!")
    return 0

if __name__ == "__main__":
    sys.exit(main()) 