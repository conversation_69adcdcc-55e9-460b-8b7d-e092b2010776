#!/usr/bin/env python3
"""
简化的MuseTalk推理脚本
"""
import argparse
import sys
import os
from pathlib import Path

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--source_image', required=True, help='源图像路径')
    parser.add_argument('--driving_audio', required=True, help='驱动音频路径')
    parser.add_argument('--output', required=True, help='输出视频路径')
    parser.add_argument('--device', default='cuda', help='设备类型')
    
    args = parser.parse_args()
    
    print(f"MuseTalk推理:")
    print(f"  输入图片: {args.source_image}")
    print(f"  输入音频: {args.driving_audio}")
    print(f"  输出视频: {args.output}")
    print(f"  设备: {args.device}")
    
    try:
        # 检查输入文件
        if not os.path.exists(args.source_image):
            print(f"错误: 找不到输入图片 {args.source_image}")
            return False
        
        if not os.path.exists(args.driving_audio):
            print(f"错误: 找不到输入音频 {args.driving_audio}")
            return False
        
        # 尝试导入MuseTalk
        sys.path.insert(0, str(Path(__file__).parent))
        
        # 检查模型文件
        models_dir = Path(__file__).parent / "models"
        v15_model = models_dir / "musetalkV15" / "unet.pth"
        v1_model = models_dir / "musetalk" / "pytorch_model.bin"
        
        if v15_model.exists():
            print("使用MuseTalk V1.5模型")
            model_path = v15_model
            config_path = models_dir / "musetalkV15" / "musetalk.json"
        elif v1_model.exists():
            print("使用MuseTalk V1.0模型")
            model_path = v1_model
            config_path = models_dir / "musetalk" / "config.json"
        else:
            print("错误: 找不到MuseTalk模型权重")
            return False
        
        # 调用真正的MuseTalk推理
        print("🎵 开始MuseTalk推理...")

        try:
            # 尝试使用MuseTalk的app.py
            app_py = Path(__file__).parent / "app.py"
            if app_py.exists():
                print("使用MuseTalk app.py进行推理")

                # 使用subprocess调用app.py
                import subprocess

                cmd = [
                    sys.executable, str(app_py),
                    "--source_image", args.source_image,
                    "--driving_audio", args.driving_audio,
                    "--result_dir", str(Path(args.output).parent),
                    "--use_float16"  # 使用float16以节省显存
                ]

                print(f"执行命令: {' '.join(cmd)}")
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)

                if result.returncode == 0:
                    print("✅ MuseTalk推理成功")

                    # 查找生成的视频文件
                    result_dir = Path(args.output).parent
                    video_files = list(result_dir.glob("*.mp4"))

                    if video_files:
                        # 移动到指定输出位置
                        import shutil
                        shutil.move(str(video_files[0]), args.output)
                        print(f"输出视频: {args.output}")
                        return True
                    else:
                        print("⚠️  未找到生成的视频文件")
                        return False
                else:
                    print(f"❌ MuseTalk推理失败: {result.stderr}")
                    return False
            else:
                print("❌ 找不到MuseTalk app.py")
                return False

        except subprocess.TimeoutExpired:
            print("❌ MuseTalk推理超时")
            return False
        except Exception as e:
            print(f"❌ MuseTalk推理异常: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ MuseTalk推理失败: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
