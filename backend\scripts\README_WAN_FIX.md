# WAN 2.1 模型修复指南

本文档提供了解决WAN 2.1视频生成模型相关问题的解决方案和指导。

## 常见问题

1. **错误：模型 1.3B 初始化失败，无法生成视频**
   - 原因：模型目录结构不符合系统预期，缺少必要的子目录（scheduler, unet, vae）
   - 解决方法：运行修复脚本组织目录结构

2. **错误：模型目录结构不完整，缺少以下子目录: scheduler, unet**
   - 原因：模型文件没有按照正确的目录结构组织
   - 解决方法：使用一键修复工具重新组织目录结构

3. **错误：模型目录不存在**
   - 原因：模型文件未正确复制到models_cache目录
   - 解决方法：使用复制脚本将模型从local_models复制到models_cache

## 修复工具

系统提供了几个修复工具，可以解决上述问题：

### 1. 一键修复工具 (推荐)

最简单的方法是使用一键修复工具，该工具会自动检测并修复所有已知的WAN模型问题：

```bash
python backend/scripts/fix_wan_issue.py
```

### 2. 修复模型目录结构

如果只需要修复模型目录结构（缺少子目录问题），可以使用：

```bash
python backend/scripts/fix_wan_model_structure.py
```

### 3. 复制模型文件

如果需要从local_models目录复制模型到models_cache，可以使用：

```bash
python backend/scripts/copy_wan_models.py
```

## 自动修复

最新版本的系统已添加自动修复功能，当检测到模型目录结构有问题时，会自动尝试修复。如果自动修复失败，系统会提供更详细的错误信息。

## 修复流程

以下是完整的修复流程：

1. 首先尝试使用一键修复工具：
   ```bash
   python backend/scripts/fix_wan_issue.py
   ```

2. 如果一键修复工具不能解决问题，可以依次尝试：
   ```bash
   python backend/scripts/copy_wan_models.py
   python backend/scripts/fix_wan_model_structure.py
   ```

3. 修复完成后，重启应用程序以应用更改

## 模型目录结构要求

WAN 2.1模型要求以下目录结构：

```
models_cache/Wan2.1-T2V-1.3B/
├── scheduler/
│   └── scheduler_config.json
├── unet/
│   ├── config.json
│   └── diffusion_pytorch_model.safetensors
├── vae/
│   ├── config.json
│   └── diffusion_pytorch_model.safetensors
└── model_index.json
```

## 常见问题解答

**Q: 为什么模型初始化失败？**  
A: 最常见的原因是模型目录结构不符合要求，缺少必要的子目录或配置文件。使用修复脚本可以解决大部分问题。

**Q: 修复后仍然出现问题怎么办？**  
A: 检查日志文件，确认是否有其他错误。可能需要重新下载模型文件或检查磁盘空间和权限问题。

**Q: 如何查看详细的错误信息？**  
A: 使用以下命令运行修复工具可以查看更详细的日志信息：
```bash
python backend/scripts/fix_wan_issue.py --verbose
```

## 技术支持

如有更多问题，请联系系统管理员或技术支持团队。 