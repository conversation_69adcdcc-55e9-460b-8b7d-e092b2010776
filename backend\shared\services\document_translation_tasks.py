import re
import logging
from datetime import datetime
from typing import List

from backend.db.session import get_db_session
from backend.models.document_translation import DocumentTranslationTask
from backend.services.translation_service import get_translation_service
from backend.services.celery_app import celery_app as celery

# 获取日志记录器
logger = logging.getLogger(__name__)

# 获取Celery实例
# from backend.celery_app import celery

@celery.task(name="process_document_task", bind=True)
def process_document_task(self, task_id: str):
    """
    处理文档翻译任务
    
    参数:
        task_id: 任务ID
    """
    logger.info(f"开始处理文档翻译任务: {task_id}")
    
    try:
        # 创建数据库会话
        with get_db_session() as session:
            # 获取任务信息
            task = session.query(DocumentTranslationTask).filter_by(id=task_id).first()
            
            if not task:
                logger.error(f"任务不存在: {task_id}")
                return
                
            # 更新任务状态为处理中
            task.status = "processing"
            session.commit()
            
            # 获取文档内容
            document_content = task.document_content
            if not document_content:
                logger.error(f"文档内容为空: {task_id}")
                task.status = "failed"
                task.error_message = "文档内容为空"
                session.commit()
                return
                
            # 获取翻译服务
            translation_service = get_translation_service()
            
            # 获取文档段落
            paragraphs = extract_paragraphs(document_content)
            total_paragraphs = len(paragraphs)
            logger.info(f"文档共有 {total_paragraphs} 个段落")
            
            # 创建进度追踪
            task.total_segments = total_paragraphs
            task.processed_segments = 0
            session.commit()
            
            translated_content = ""
            
            # 使用并行处理提高性能
            # 根据段落数量决定并行度，但不超过系统核心数
            import multiprocessing
            import concurrent.futures
            
            # 计算最佳并行度
            max_workers = min(multiprocessing.cpu_count(), 8)  # 最多使用8个工作线程
            chunk_size = min(10, max(1, total_paragraphs // max_workers))  # 每批处理10个段落或更少
            
            # 分批处理段落
            batches = [paragraphs[i:i + chunk_size] for i in range(0, total_paragraphs, chunk_size)]
            total_batches = len(batches)
            
            logger.info(f"将使用 {max_workers} 个工作线程处理 {total_batches} 批段落")
            
            # 定义批量翻译函数
            def translate_batch(batch):
                try:
                    results = []
                    for para in batch:
                        if not para.strip():
                            results.append("")
                            continue
                            
                        # 翻译段落
                        translated_para = translation_service.translate_sync(
                            para,
                            task.source_language,
                            task.target_language
                        )
                        results.append(translated_para)
                    return results
                except Exception as e:
                    logger.error(f"批量翻译失败: {str(e)}")
                    return [para for para in batch]  # 失败时返回原文
            
            # 使用线程池并行处理
            translated_paragraphs = []
            with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
                # 提交所有批次的翻译任务
                future_to_batch = {executor.submit(translate_batch, batch): i for i, batch in enumerate(batches)}
                
                # 处理完成的任务
                for future in concurrent.futures.as_completed(future_to_batch):
                    batch_index = future_to_batch[future]
                    try:
                        batch_results = future.result()
                        # 记录批次结果
                        translated_paragraphs.extend(batch_results)
                        
                        # 更新进度
                        processed_count = min((batch_index + 1) * chunk_size, total_paragraphs)
                        task.processed_segments = processed_count
                        session.commit()
                        
                        # 更新任务进度
                        self.update_state(
                            state='PROGRESS',
                            meta={'current': processed_count, 'total': total_paragraphs}
                        )
                        
                        logger.info(f"已处理 {processed_count}/{total_paragraphs} 个段落")
                    except Exception as e:
                        logger.error(f"处理批次 {batch_index} 失败: {str(e)}")
            
            # 确保段落顺序正确
            if len(translated_paragraphs) == total_paragraphs:
                # 合并翻译结果
                translated_content = "\n\n".join(p for p in translated_paragraphs if p)
            else:
                # 如果段落数量不匹配，记录错误
                logger.error(f"翻译段落数量不匹配: 预期 {total_paragraphs}, 实际 {len(translated_paragraphs)}")
                # 回退到原始顺序
                translated_content = "\n\n".join(
                    translation_service.translate_sync(para, task.source_language, task.target_language)
                    if para.strip() else ""
                    for para in paragraphs
                )
            
            # 更新任务状态
            task.translated_content = translated_content
            task.status = "completed"
            task.processed_segments = total_paragraphs
            task.completed_at = datetime.now()
            session.commit()
            
            logger.info(f"文档翻译任务完成: {task_id}")
            
    except Exception as e:
        logger.error(f"处理文档翻译任务失败: {str(e)}")
        # 更新任务状态为失败
        with get_db_session() as session:
            task = session.query(DocumentTranslationTask).filter_by(id=task_id).first()
            if task:
                task.status = "failed"
                task.error_message = str(e)
                session.commit()

def extract_paragraphs(document_content):
    """
    从文档内容中提取段落
    
    参数:
        document_content: 文档内容
        
    返回:
        段落列表
    """
    if not document_content:
        return []
        
    # 按段落分割
    paragraphs = re.split(r'\n\s*\n', document_content)
    return [p.strip() for p in paragraphs if p.strip()] 