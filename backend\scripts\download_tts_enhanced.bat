@echo off
echo ========================================
echo 增强版TTS模型下载器
echo ========================================
echo 此脚本将尝试使用多种方式下载TTS模型文件
echo.

REM 设置工作目录
cd /d "%~dp0"

REM 检查Python环境
python --version > nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo Python未安装或未添加到PATH环境变量
    echo 请安装Python 3.8或更高版本
    echo 下载地址: https://www.python.org/downloads/
    echo.
    pause
    exit /b
)

REM 检查是否安装必要的库
python -c "import requests" > nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo 正在安装必要的依赖...
    pip install requests tqdm
    if %ERRORLEVEL% NEQ 0 (
        echo 依赖安装失败，请手动安装:
        echo pip install requests tqdm
        pause
        exit /b
    )
)

:MENU
cls
echo 选择要执行的操作:
echo.
echo 1. 尝试下载所有TTS模型 (标准模式)
echo 2. 尝试下载所有TTS模型 (备用链接优先)
echo 3. 尝试下载所有TTS模型 (无代理模式)
echo 4. 仅下载中文模型 (zh-CN-baker)
echo 5. 仅下载英文模型 (en-ljspeech)
echo 6. 仅下载多语言模型 (xtts_v1)
echo 7. 创建最小测试模型 (无需下载，仅用于测试系统功能)
echo 8. 退出
echo.
set /p choice=请输入选择 (1-8): 

if "%choice%"=="1" goto ALL_STANDARD
if "%choice%"=="2" goto ALL_BACKUP
if "%choice%"=="3" goto ALL_NOPROXY
if "%choice%"=="4" goto ZH_MODEL
if "%choice%"=="5" goto EN_MODEL
if "%choice%"=="6" goto XTTS_MODEL
if "%choice%"=="7" goto MINIMAL_MODEL
if "%choice%"=="8" goto END

echo 无效的选择，请重新输入
timeout /t 2 >nul
goto MENU

:ALL_STANDARD
echo.
echo 开始下载所有推荐的TTS模型 (标准模式)...
python enhanced_tts_download.py --all
goto DONE

:ALL_BACKUP
echo.
echo 开始下载所有推荐的TTS模型 (备用链接优先)...
python enhanced_tts_download.py --all --use-backup
goto DONE

:ALL_NOPROXY
echo.
echo 开始下载所有推荐的TTS模型 (无代理模式)...
python enhanced_tts_download.py --all --no-proxy
goto DONE

:ZH_MODEL
echo.
echo 开始下载中文模型 (zh-CN-baker)...
python enhanced_tts_download.py --model zh-CN-baker
goto DONE

:EN_MODEL
echo.
echo 开始下载英文模型 (en-ljspeech)...
python enhanced_tts_download.py --model en-ljspeech
goto DONE

:XTTS_MODEL
echo.
echo 开始下载多语言模型 (xtts_v1)...
python enhanced_tts_download.py --model xtts_v1
goto DONE

:MINIMAL_MODEL
echo.
echo 创建最小测试模型...
python enhanced_tts_download.py --create-minimal
goto DONE

:DONE
echo.
echo 操作已完成
echo.
echo 要执行其他操作吗?
echo 1. 返回主菜单
echo 2. 退出
echo.
set /p next=请选择 (1-2): 
if "%next%"=="1" goto MENU
goto END

:END
echo.
echo 感谢使用，再见！
pause 