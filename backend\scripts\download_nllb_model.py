#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
NLLB翻译模型下载脚本

此脚本用于下载NLLB (No Language Left Behind) 翻译模型文件，用于机器翻译服务。
"""

import os
import sys
import logging
import argparse
import subprocess
import shutil
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# 默认模型ID
DEFAULT_MODEL_ID = "facebook/nllb-200-distilled-600M"
# 备选小模型
FALLBACK_MODEL_ID = "facebook/nllb-200-distilled-600M"

def check_pip_package(package_name):
    """检查是否已安装指定的pip包"""
    try:
        __import__(package_name)
        return True
    except ImportError:
        return False

def install_pip_package(package_name):
    """安装指定的pip包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
        return True
    except subprocess.CalledProcessError:
        return False

def download_model_huggingface(model_id, output_dir):
    """使用HuggingFace CLI下载模型"""
    try:
        logger.info(f"开始使用HuggingFace CLI下载模型: {model_id}")
        
        # 确保安装huggingface_hub
        if not check_pip_package("huggingface_hub"):
            logger.info("安装huggingface_hub[cli]...")
            if not install_pip_package("huggingface_hub[cli]"):
                logger.error("无法安装huggingface_hub[cli]")
                return False
        
        # 尝试查找huggingface-cli命令
        huggingface_cli_command = "huggingface-cli"
        
        # 检查命令是否可用
        try:
            # Windows
            if os.name == 'nt':
                subprocess.check_call(["where", huggingface_cli_command], 
                                    stdout=subprocess.DEVNULL, 
                                    stderr=subprocess.DEVNULL)
            else:
                # Unix/Linux/MacOS
                subprocess.check_call(["which", huggingface_cli_command], 
                                    stdout=subprocess.DEVNULL, 
                                    stderr=subprocess.DEVNULL)
            logger.info(f"找到命令: {huggingface_cli_command}")
        except subprocess.CalledProcessError:
            # 命令不可用，使用Python模块
            logger.warning(f"未找到 {huggingface_cli_command} 命令，尝试直接使用Python调用")
            huggingface_cli_command = [sys.executable, "-m", "huggingface_hub.cli.cli", "download"]
            model_id = [model_id]
            output_dir = ["--local-dir", output_dir]
            
            logger.info(f"执行命令: {' '.join(str(x) for x in huggingface_cli_command + model_id + output_dir)}")
            subprocess.check_call(huggingface_cli_command + model_id + output_dir)
            return True
        
        # 如果huggingface-cli命令可用，直接使用命令行方式
        cmd = [huggingface_cli_command, "download", model_id, "--local-dir", output_dir]
        logger.info(f"执行命令: {' '.join(cmd)}")
        subprocess.check_call(cmd)
        return True
        
    except Exception as e:
        logger.error(f"使用HuggingFace CLI下载模型失败: {e}")
        return False

def check_transformers_import():
    """检查是否能导入transformers库"""
    try:
        import transformers
        logger.info(f"已安装transformers库，版本: {transformers.__version__}")
        return True
    except ImportError:
        logger.error("未安装transformers库。请使用 'pip install transformers' 安装")
        return False

def download_model_with_transformers(model_id, output_dir):
    """使用transformers库下载模型"""
    try:
        logger.info(f"使用transformers库下载模型: {model_id}")
        
        # 检查是否已安装transformers
        if not check_transformers_import():
            return False
        
        from transformers import AutoTokenizer, AutoModelForSeq2SeqLM
        
        # 设置环境变量指向输出目录
        os.environ["TRANSFORMERS_CACHE"] = output_dir
        
        # 下载分词器和模型
        logger.info("下载tokenizer...")
        tokenizer = AutoTokenizer.from_pretrained(model_id, cache_dir=output_dir)
        
        logger.info("下载模型...")
        model = AutoModelForSeq2SeqLM.from_pretrained(model_id, cache_dir=output_dir)
        
        logger.info(f"模型和分词器已成功下载到: {output_dir}")
        return True
        
    except Exception as e:
        logger.error(f"使用transformers库下载模型失败: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description='下载NLLB翻译模型文件')
    parser.add_argument('--model-id', default=DEFAULT_MODEL_ID,
                        help=f'模型ID (默认: {DEFAULT_MODEL_ID})')
    parser.add_argument('--output-dir', default='../../models/nllb',
                        help='模型输出目录 (默认: ../../models/nllb)')
    parser.add_argument('--method', choices=['huggingface', 'transformers'],
                        default='huggingface', help='下载方法')
    parser.add_argument('--fallback', action='store_true',
                        help='如果主模型下载失败，是否下载备选模型')
    
    args = parser.parse_args()
    
    # 获取绝对路径
    model_id = args.model_id
    output_dir = os.path.abspath(args.output_dir)
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    logger.info(f"模型ID: {model_id}")
    logger.info(f"输出目录: {output_dir}")
    
    # 下载模型
    success = False
    if args.method == 'huggingface':
        success = download_model_huggingface(model_id, output_dir)
    else:
        success = download_model_with_transformers(model_id, output_dir)
    
    # 如果下载失败且启用了备选方案
    if not success and args.fallback and model_id != FALLBACK_MODEL_ID:
        logger.warning(f"主模型下载失败，尝试下载备选模型: {FALLBACK_MODEL_ID}")
        if args.method == 'huggingface':
            success = download_model_huggingface(FALLBACK_MODEL_ID, output_dir)
        else:
            success = download_model_with_transformers(FALLBACK_MODEL_ID, output_dir)
    
    if success:
        logger.info("模型下载完成！")
        # 打印一条提示消息，告诉用户如何使用此模型
        logger.info("\n" + "="*50)
        logger.info("使用说明:")
        logger.info("1. 已将模型下载到指定目录")
        logger.info("2. 系统将自动使用此目录中的模型文件")
        logger.info("3. 如果需要使用其他目录，请设置环境变量 NLLB_CACHE_DIR")
        logger.info("="*50)
    else:
        logger.error("模型下载失败！")
        # 打印排查建议
        logger.info("\n" + "="*50)
        logger.info("排查建议:")
        logger.info("1. 检查网络连接")
        logger.info("2. 确保已安装所需依赖 (transformers, huggingface_hub)")
        logger.info("3. 尝试手动从HuggingFace网站下载模型文件")
        logger.info("="*50)
        sys.exit(1)

if __name__ == "__main__":
    main() 