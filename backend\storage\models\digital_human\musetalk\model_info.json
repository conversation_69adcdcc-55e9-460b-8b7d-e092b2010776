{"name": "MuseTalk", "type": "digital_human", "description": "腾讯开源的实时唇同步数字人模型", "version": "1.5.0", "gpu_memory_required": 6, "dependencies": ["torch", "<PERSON><PERSON><PERSON>", "librosa", "opencv-python"], "main_script": "app.py", "inference_script": "inference.py", "source_path": "E:\\workspace\\AI_system\\data\\models\\MuseTalk", "target_path": "E:\\workspace\\AI_system\\backend\\storage\\models\\digital_human\\musetalk", "size": 9319535405, "migrated_at": "2025-07-18T16:01:08.189214", "status": "migrated"}