"""
在线智能体服务 - 使用在线API作为备用方案
"""

import httpx
from typing import Dict, Any
import os


class OnlineAgentService:
    """在线智能体服务类 - 备用方案"""

    def __init__(self):
        # 可以配置多个在线API作为备用
        self.apis = {
            "openai_compatible": {
                "base_url": "https://api.deepseek.com/v1",  # DeepSeek API
                "api_key": os.getenv("DEEPSEEK_API_KEY", ""),
                "model": "deepseek-chat"
            },
            "ollama_online": {
                "base_url": "https://ollama.ai/api",  # 如果有在线版本
                "api_key": "",
                "model": "llama3.2:3b"
            }
        }

    async def chat_with_agent(self, agent: Dict[str, Any], message: str) -> str:
        """与智能体对话 - 在线版本"""
        try:
            agent_type = agent.get("agent_type", "chat")
            agent_name = agent.get("name", "智能体")

            # 构建简洁的系统提示词
            if agent_type == "language_tutor":
                system_prompt = f"""You are {agent_name}, a friendly English teacher.

IMPORTANT: Respond directly as the teacher. Keep it natural and conversational.

Example:
User: "你好"
You: "Hello! Nice to meet you! What's your name?"

Be encouraging, ask questions, and help with English learning."""
            else:
                system_prompt = f"You are {agent_name}, a helpful assistant. Keep responses short and friendly."

            # 尝试不同的API
            for api_name, api_config in self.apis.items():
                try:
                    response = await self._call_online_api(api_config, system_prompt, message)
                    if response:
                        return response
                except Exception as e:
                    print(f"[WARNING] {api_name} API 失败: {e}")
                    continue

            # 如果所有API都失败，返回默认回复
            return self._get_default_response(agent_type, agent_name)

        except Exception as e:
            print(f"[ERROR] 在线智能体对话失败: {str(e)}")
            return "Hello! I'm here to help you. How can I assist you today?"

    async def _call_online_api(self, api_config: Dict, system_prompt: str, user_message: str) -> str:
        """调用在线API"""
        if not api_config.get("api_key") and api_config["base_url"] != "http://localhost:11434":
            return None  # 跳过需要API密钥但没有配置的服务

        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                headers = {"Content-Type": "application/json"}
                
                if api_config.get("api_key"):
                    headers["Authorization"] = f"Bearer {api_config['api_key']}"

                payload = {
                    "model": api_config["model"],
                    "messages": [
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": user_message}
                    ],
                    "max_tokens": 150,
                    "temperature": 0.7
                }

                response = await client.post(
                    f"{api_config['base_url']}/chat/completions",
                    json=payload,
                    headers=headers
                )

                if response.status_code == 200:
                    result = response.json()
                    message = result.get("choices", [{}])[0].get("message", {}).get("content", "")
                    return message.strip() if message else None
                else:
                    print(f"[ERROR] API 错误: {response.status_code} - {response.text}")
                    return None

        except Exception as e:
            print(f"[ERROR] 调用在线API失败: {e}")
            return None

    def _get_default_response(self, agent_type: str, agent_name: str) -> str:
        """获取默认回复"""
        if agent_type == "language_tutor":
            responses = [
                f"Hello! I'm {agent_name}, your English teacher. How can I help you today?",
                f"Hi there! I'm {agent_name}. What would you like to practice in English?",
                f"Nice to meet you! I'm {agent_name}. Let's start our English lesson!",
                f"Hello! I'm {agent_name}. What's your name? Let's chat in English!"
            ]
            import random
            return random.choice(responses)
        else:
            return f"Hello! I'm {agent_name}. How can I assist you today?"

    async def test_connection(self) -> Dict[str, bool]:
        """测试各个API的连接状态"""
        results = {}
        
        for api_name, api_config in self.apis.items():
            try:
                response = await self._call_online_api(
                    api_config, 
                    "You are a helpful assistant.", 
                    "Hello"
                )
                results[api_name] = response is not None
            except Exception:
                results[api_name] = False
        
        return results
