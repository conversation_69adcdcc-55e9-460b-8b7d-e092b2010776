#!/usr/bin/env python3
"""
性能优化服务
GPU加速、缓存管理、并发优化
"""

import asyncio
import logging
import time
from typing import Dict, Any, Optional, List
import psutil
import threading
from pathlib import Path
import json
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class GPUManager:
    """GPU管理器"""
    
    def __init__(self):
        self.gpu_available = False
        self.gpu_info = {}
        self.check_gpu_availability()
    
    def check_gpu_availability(self):
        """检查GPU可用性"""
        try:
            import torch
            
            if torch.cuda.is_available():
                self.gpu_available = True
                self.gpu_info = {
                    "device_count": torch.cuda.device_count(),
                    "current_device": torch.cuda.current_device(),
                    "device_name": torch.cuda.get_device_name(0),
                    "memory_total": torch.cuda.get_device_properties(0).total_memory,
                    "memory_allocated": torch.cuda.memory_allocated(0),
                    "memory_cached": torch.cuda.memory_reserved(0)
                }
                logger.info(f"✅ GPU可用: {self.gpu_info['device_name']}")
            else:
                logger.info("⚠️  GPU不可用，使用CPU模式")
                
        except ImportError:
            logger.info("⚠️  PyTorch未安装，无法使用GPU加速")
    
    def get_gpu_status(self) -> Dict[str, Any]:
        """获取GPU状态"""
        if not self.gpu_available:
            return {"available": False}
        
        try:
            import torch
            
            return {
                "available": True,
                "device_count": torch.cuda.device_count(),
                "current_device": torch.cuda.current_device(),
                "device_name": torch.cuda.get_device_name(0),
                "memory_total_gb": torch.cuda.get_device_properties(0).total_memory / 1024**3,
                "memory_allocated_gb": torch.cuda.memory_allocated(0) / 1024**3,
                "memory_cached_gb": torch.cuda.memory_reserved(0) / 1024**3,
                "memory_free_gb": (torch.cuda.get_device_properties(0).total_memory - torch.cuda.memory_allocated(0)) / 1024**3
            }
        except Exception as e:
            logger.error(f"❌ 获取GPU状态失败: {e}")
            return {"available": False, "error": str(e)}
    
    def optimize_for_inference(self):
        """优化推理性能"""
        if not self.gpu_available:
            return
        
        try:
            import torch
            
            # 设置推理模式
            torch.set_grad_enabled(False)
            
            # 优化CUDA设置
            if torch.cuda.is_available():
                torch.backends.cudnn.benchmark = True
                torch.backends.cudnn.deterministic = False
                
            logger.info("✅ GPU推理优化完成")
            
        except Exception as e:
            logger.error(f"❌ GPU优化失败: {e}")

class CacheManager:
    """缓存管理器"""
    
    def __init__(self, max_size: int = 1000, ttl_seconds: int = 3600):
        self.cache = {}
        self.access_times = {}
        self.max_size = max_size
        self.ttl_seconds = ttl_seconds
        self.lock = threading.RLock()
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存"""
        with self.lock:
            if key not in self.cache:
                return None
            
            # 检查TTL
            if self._is_expired(key):
                self._remove(key)
                return None
            
            # 更新访问时间
            self.access_times[key] = time.time()
            return self.cache[key]
    
    def set(self, key: str, value: Any):
        """设置缓存"""
        with self.lock:
            # 检查缓存大小
            if len(self.cache) >= self.max_size:
                self._evict_lru()
            
            self.cache[key] = value
            self.access_times[key] = time.time()
    
    def remove(self, key: str):
        """移除缓存"""
        with self.lock:
            self._remove(key)
    
    def clear(self):
        """清空缓存"""
        with self.lock:
            self.cache.clear()
            self.access_times.clear()
    
    def _remove(self, key: str):
        """内部移除方法"""
        self.cache.pop(key, None)
        self.access_times.pop(key, None)
    
    def _is_expired(self, key: str) -> bool:
        """检查是否过期"""
        if key not in self.access_times:
            return True
        
        return time.time() - self.access_times[key] > self.ttl_seconds
    
    def _evict_lru(self):
        """移除最少使用的缓存"""
        if not self.access_times:
            return
        
        lru_key = min(self.access_times, key=self.access_times.get)
        self._remove(lru_key)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        with self.lock:
            return {
                "size": len(self.cache),
                "max_size": self.max_size,
                "hit_rate": getattr(self, '_hit_count', 0) / max(getattr(self, '_total_count', 1), 1),
                "expired_count": sum(1 for key in self.cache.keys() if self._is_expired(key))
            }

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.metrics = {}
        self.start_time = time.time()
        self.request_count = 0
        self.error_count = 0
        
    def record_request(self, endpoint: str, duration: float, success: bool = True):
        """记录请求性能"""
        self.request_count += 1
        
        if not success:
            self.error_count += 1
        
        if endpoint not in self.metrics:
            self.metrics[endpoint] = {
                "count": 0,
                "total_duration": 0,
                "min_duration": float('inf'),
                "max_duration": 0,
                "error_count": 0
            }
        
        metric = self.metrics[endpoint]
        metric["count"] += 1
        metric["total_duration"] += duration
        metric["min_duration"] = min(metric["min_duration"], duration)
        metric["max_duration"] = max(metric["max_duration"], duration)
        
        if not success:
            metric["error_count"] += 1
    
    def get_system_stats(self) -> Dict[str, Any]:
        """获取系统统计"""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            return {
                "cpu_percent": cpu_percent,
                "memory_percent": memory.percent,
                "memory_available_gb": memory.available / 1024**3,
                "memory_total_gb": memory.total / 1024**3,
                "disk_percent": disk.percent,
                "disk_free_gb": disk.free / 1024**3,
                "disk_total_gb": disk.total / 1024**3,
                "uptime_seconds": time.time() - self.start_time
            }
        except Exception as e:
            logger.error(f"❌ 获取系统统计失败: {e}")
            return {}
    
    def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告"""
        report = {
            "uptime_seconds": time.time() - self.start_time,
            "total_requests": self.request_count,
            "total_errors": self.error_count,
            "error_rate": self.error_count / max(self.request_count, 1),
            "endpoints": {}
        }
        
        for endpoint, metric in self.metrics.items():
            report["endpoints"][endpoint] = {
                "count": metric["count"],
                "avg_duration": metric["total_duration"] / max(metric["count"], 1),
                "min_duration": metric["min_duration"] if metric["min_duration"] != float('inf') else 0,
                "max_duration": metric["max_duration"],
                "error_count": metric["error_count"],
                "error_rate": metric["error_count"] / max(metric["count"], 1)
            }
        
        return report

class ConcurrencyManager:
    """并发管理器"""
    
    def __init__(self, max_concurrent_tasks: int = 10):
        self.semaphore = asyncio.Semaphore(max_concurrent_tasks)
        self.active_tasks = {}
        self.task_queue = asyncio.Queue()
        
    async def execute_with_limit(self, task_id: str, coro):
        """限制并发执行任务"""
        async with self.semaphore:
            self.active_tasks[task_id] = time.time()
            try:
                result = await coro
                return result
            finally:
                self.active_tasks.pop(task_id, None)
    
    def get_active_tasks(self) -> Dict[str, float]:
        """获取活跃任务"""
        current_time = time.time()
        return {
            task_id: current_time - start_time 
            for task_id, start_time in self.active_tasks.items()
        }

class PerformanceOptimizer:
    """性能优化器主类"""
    
    def __init__(self):
        self.gpu_manager = GPUManager()
        self.cache_manager = CacheManager()
        self.performance_monitor = PerformanceMonitor()
        self.concurrency_manager = ConcurrencyManager()
        
        # 初始化优化
        self.gpu_manager.optimize_for_inference()
    
    async def optimize_ollama_request(self, model_name: str, messages: List[Dict], options: Dict = None):
        """优化Ollama请求"""
        try:
            # 生成缓存键
            cache_key = self._generate_cache_key(model_name, messages, options)
            
            # 检查缓存
            cached_result = self.cache_manager.get(cache_key)
            if cached_result:
                logger.info("✅ 使用缓存的Ollama响应")
                return cached_result
            
            # 执行请求
            start_time = time.time()
            
            import requests
            url = "http://localhost:11434/api/chat"
            data = {
                "model": model_name,
                "messages": messages,
                "stream": False,
                "options": options or {}
            }
            
            response = await asyncio.to_thread(
                requests.post, url, json=data, timeout=30
            )
            
            duration = time.time() - start_time
            
            if response.status_code == 200:
                result = response.json()
                
                # 缓存结果
                self.cache_manager.set(cache_key, result)
                
                # 记录性能
                self.performance_monitor.record_request("ollama_chat", duration, True)
                
                return result
            else:
                self.performance_monitor.record_request("ollama_chat", duration, False)
                raise Exception(f"Ollama请求失败: {response.status_code}")
                
        except Exception as e:
            logger.error(f"❌ 优化Ollama请求失败: {e}")
            raise
    
    def _generate_cache_key(self, model_name: str, messages: List[Dict], options: Dict = None) -> str:
        """生成缓存键"""
        import hashlib
        
        # 只缓存最后一条用户消息（避免缓存过于复杂）
        last_user_message = ""
        for msg in reversed(messages):
            if msg.get("role") == "user":
                last_user_message = msg.get("content", "")
                break
        
        cache_data = {
            "model": model_name,
            "message": last_user_message,
            "options": options or {}
        }
        
        cache_str = json.dumps(cache_data, sort_keys=True)
        return hashlib.md5(cache_str.encode()).hexdigest()
    
    def get_optimization_status(self) -> Dict[str, Any]:
        """获取优化状态"""
        return {
            "gpu": self.gpu_manager.get_gpu_status(),
            "cache": self.cache_manager.get_stats(),
            "performance": self.performance_monitor.get_performance_report(),
            "system": self.performance_monitor.get_system_stats(),
            "active_tasks": self.concurrency_manager.get_active_tasks()
        }

# 全局实例
performance_optimizer = PerformanceOptimizer()
