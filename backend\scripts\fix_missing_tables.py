#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
fix_missing_tables.py - 检查并修复缺失的数据库表

此脚本用于检查数据库中是否存在所有模型定义的表，特别是agents相关表。
如果表不存在，则创建缺失的表。
"""

import os
import sys
import argparse
import logging
from sqlalchemy import inspect, text
from sqlalchemy.exc import SQLAlchemyError, ProgrammingError

# 添加项目根目录到系统路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("fix_missing_tables")

# 导入数据库连接和模型
from utils.db import engine, Base, get_db, init_db
from models.agent import (
    Agent, AgentTag, AgentCapa<PERSON>, <PERSON><PERSON><PERSON><PERSON>, 
    <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Agent<PERSON><PERSON>ion, AgentMessage
)
from models.user import User
from models.task import Task

def check_table_exists(table_name):
    """检查表是否存在"""
    try:
        inspector = inspect(engine)
        return table_name in inspector.get_table_names()
    except SQLAlchemyError as e:
        logger.error(f"检查表 {table_name} 时出错: {e}")
        return False

def list_missing_tables():
    """列出所有缺失的表"""
    missing_tables = []
    inspector = inspect(engine)
    existing_tables = inspector.get_table_names()
    
    # 获取所有模型定义的表名
    defined_tables = Base.metadata.tables.keys()
    
    for table_name in defined_tables:
        if table_name not in existing_tables:
            missing_tables.append(table_name)
    
    return missing_tables

def create_tables(tables=None):
    """创建指定的表或所有缺失的表"""
    try:
        if tables is None:
            # 创建所有表
            logger.info("准备创建所有缺失的表...")
            Base.metadata.create_all(engine)
            logger.info("所有表创建完成")
        else:
            # 创建指定的表
            logger.info(f"准备创建指定的表: {', '.join(tables)}")
            tables_to_create = [Base.metadata.tables[table] for table in tables if table in Base.metadata.tables]
            if tables_to_create:
                Base.metadata.create_all(engine, tables=tables_to_create)
                logger.info(f"指定表创建完成")
            else:
                logger.warning(f"没有找到指定的表定义")
                
        return True
    except SQLAlchemyError as e:
        logger.error(f"创建表时出错: {e}")
        return False

def check_and_fix_tables(args):
    """检查并修复表结构"""
    # 首先列出所有缺失的表
    missing_tables = list_missing_tables()
    
    if not missing_tables:
        logger.info("所有定义的表都已存在，无需修复")
        return True
        
    logger.info(f"发现 {len(missing_tables)} 个缺失的表: {', '.join(missing_tables)}")
    
    # 如果只是检查模式，则到此为止
    if args.check_only:
        logger.info("仅检查模式，不进行修复")
        return False
        
    # 如果指定了表，则只创建指定的表
    tables_to_create = args.tables if args.tables else missing_tables
    
    # 过滤出实际缺失的表
    tables_to_create = [t for t in tables_to_create if t in missing_tables]
    
    if not tables_to_create:
        logger.info("指定的表都已存在，无需创建")
        return True
        
    # 创建缺失的表
    return create_tables(tables_to_create)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='检查并修复缺失的数据库表')
    parser.add_argument('--check-only', action='store_true', help='仅检查缺失的表，不进行修复')
    parser.add_argument('--tables', nargs='+', help='指定要创建的表，如果不指定则创建所有缺失的表')
    parser.add_argument('--yes', '-y', action='store_true', help='自动确认所有操作，不提示')
    
    args = parser.parse_args()
    
    try:
        if not args.check_only and not args.yes:
            confirm = input("此操作将修改数据库结构。确认继续? [y/N]: ")
            if confirm.lower() != 'y':
                logger.info("操作已取消")
                return
        
        success = check_and_fix_tables(args)
        
        if success:
            logger.info("数据库表结构检查/修复完成")
        else:
            logger.warning("数据库表结构检查/修复未完成，可能存在问题")
            sys.exit(1)
    except Exception as e:
        logger.error(f"发生未预期的错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 