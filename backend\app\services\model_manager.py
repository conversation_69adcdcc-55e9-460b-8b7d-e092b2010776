"""
数字人模型管理器
负责加载和调用各种数字人生成模型
"""

import os
import sys
import json
import logging
import subprocess
import tempfile
from typing import Dict, Any, Optional, List
from pathlib import Path
import asyncio

logger = logging.getLogger(__name__)

class ModelManager:
    """数字人模型管理器"""
    
    def __init__(self):
        self.models_base_path = Path("storage/models/digital_human")
        self.loaded_models = {}
        self.model_configs = {}
        self.initialize_models()
    
    def initialize_models(self):
        """初始化模型配置"""
        logger.info("初始化数字人模型...")
        
        # 扫描所有模型目录
        for model_dir in self.models_base_path.iterdir():
            if model_dir.is_dir():
                config_file = model_dir / "model_info.json"
                if config_file.exists():
                    try:
                        with open(config_file, 'r', encoding='utf-8') as f:
                            config = json.load(f)
                        
                        self.model_configs[model_dir.name] = {
                            **config,
                            "path": str(model_dir),
                            "available": self._check_model_availability(model_dir, config)
                        }
                        
                        logger.info(f"发现模型: {config.get('name', model_dir.name)} - {'可用' if self.model_configs[model_dir.name]['available'] else '不可用'}")
                        
                    except Exception as e:
                        logger.error(f"加载模型配置失败 {model_dir.name}: {str(e)}")
        
        logger.info(f"模型初始化完成，共发现 {len(self.model_configs)} 个模型")
    
    def _check_model_availability(self, model_path: Path, config: Dict[str, Any]) -> bool:
        """检查模型是否可用"""
        try:
            # 检查路径是否有效
            if not model_path or not model_path.exists():
                logger.warning(f"模型路径无效或不存在: {model_path}")
                return False

            # 检查主要脚本是否存在
            main_script = config.get("main_script", "app.py")
            inference_script = config.get("inference_script", "inference.py")

            has_script = (model_path / main_script).exists() or (model_path / inference_script).exists()
            if not has_script:
                logger.warning(f"模型 {model_path.name} 缺少主要脚本")
                return False

            # 简化权重文件检查 - 只要有基本结构就认为可用
            model_name = model_path.name.lower()

            if model_name == "musetalk":
                # 检查MuseTalk特定文件 - 需要models目录和主要权重文件
                models_dir = model_path / "models"
                if not models_dir.exists():
                    return False
                # 检查关键权重文件
                musetalk_model = models_dir / "musetalk" / "pytorch_model.bin"
                return musetalk_model.exists()
            elif model_name == "sadtalker":
                # 检查SadTalker特定文件 - 需要checkpoints目录和主要权重文件
                checkpoints_dir = model_path / "checkpoints"
                if not checkpoints_dir.exists():
                    return False
                # 检查关键权重文件
                sadtalker_model = checkpoints_dir / "SadTalker_V0.0.2_256.safetensors"
                return sadtalker_model.exists()
            elif model_name == "wav2lip":
                # 检查Wav2Lip特定文件 - 需要checkpoints目录和主要权重文件
                checkpoints_dir = model_path / "checkpoints"
                if not checkpoints_dir.exists():
                    return False
                # 检查关键权重文件
                wav2lip_model = checkpoints_dir / "wav2lip_gan.pth"
                return wav2lip_model.exists()
            elif model_name == "liveportrait":
                # 检查LivePortrait特定文件 - 需要src目录，但权重文件可能缺失
                src_dir = model_path / "src"
                pretrained_dir = model_path / "pretrained_weights"
                # 如果有src目录但没有权重，标记为不可用
                return src_dir.exists() and pretrained_dir.exists()
            else:
                # 通用检查
                return (model_path / "checkpoints").exists() or (model_path / "models").exists()

        except Exception as e:
            logger.error(f"检查模型可用性失败 {model_path.name}: {str(e)}")
            return False
    
    def get_available_models(self) -> List[Dict[str, Any]]:
        """获取可用的模型列表"""
        available = []
        for model_name, config in self.model_configs.items():
            if config.get("available", False):
                available.append({
                    "name": model_name,
                    "display_name": config.get("name", model_name),
                    "description": config.get("description", ""),
                    "version": config.get("version", "unknown"),
                    "gpu_memory_required": config.get("gpu_memory_required", 0),
                    "type": config.get("type", "digital_human")
                })
        return available
    
    async def generate_with_musetalk(
        self, 
        image_path: str, 
        audio_path: str, 
        output_dir: str,
        config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """使用MuseTalk生成数字人视频"""
        try:
            if "musetalk" not in self.model_configs or not self.model_configs["musetalk"]["available"]:
                raise ValueError("MuseTalk模型不可用")
            
            model_path = Path(self.model_configs["musetalk"]["path"])
            
            # 准备输出目录
            os.makedirs(output_dir, exist_ok=True)
            
            # 构建命令
            cmd = [
                sys.executable,
                str(model_path / "inference.py"),
                "--source_image", image_path,
                "--source_audio", audio_path,
                "--result_dir", output_dir,
                "--fps", str(config.get("fps", 25)) if config else "25",
                "--batch_size", str(config.get("batch_size", 8)) if config else "8"
            ]
            
            logger.info(f"执行MuseTalk命令: {' '.join(cmd)}")
            
            # 执行命令
            process = await asyncio.create_subprocess_exec(
                *cmd,
                cwd=str(model_path),
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0:
                # 查找生成的视频文件
                video_files = list(Path(output_dir).glob("*.mp4"))
                if video_files:
                    return {
                        "success": True,
                        "video_path": str(video_files[0]),
                        "model": "musetalk",
                        "output_dir": output_dir,
                        "log": stdout.decode('utf-8', errors='ignore')
                    }
                else:
                    return {
                        "success": False,
                        "error": "未找到生成的视频文件",
                        "log": stdout.decode('utf-8', errors='ignore'),
                        "error_log": stderr.decode('utf-8', errors='ignore')
                    }
            else:
                return {
                    "success": False,
                    "error": f"MuseTalk执行失败，返回码: {process.returncode}",
                    "log": stdout.decode('utf-8', errors='ignore'),
                    "error_log": stderr.decode('utf-8', errors='ignore')
                }
                
        except Exception as e:
            logger.error(f"MuseTalk生成失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def generate_with_sadtalker(
        self, 
        image_path: str, 
        audio_path: str, 
        output_dir: str,
        config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """使用SadTalker生成数字人视频"""
        try:
            if "sadtalker" not in self.model_configs or not self.model_configs["sadtalker"]["available"]:
                raise ValueError("SadTalker模型不可用")
            
            model_path = Path(self.model_configs["sadtalker"]["path"])
            
            # 准备输出目录
            os.makedirs(output_dir, exist_ok=True)
            
            # 构建命令
            cmd = [
                sys.executable,
                str(model_path / "inference.py"),
                "--driven_audio", audio_path,
                "--source_image", image_path,
                "--result_dir", output_dir,
                "--still",
                "--preprocess", config.get("preprocess", "full") if config else "full"
            ]
            
            # 添加可选参数
            if config:
                if config.get("enhancer"):
                    cmd.extend(["--enhancer", config["enhancer"]])
                if config.get("background_enhancer"):
                    cmd.extend(["--background_enhancer", config["background_enhancer"]])
                if config.get("cpu"):
                    cmd.append("--cpu")
            
            logger.info(f"执行SadTalker命令: {' '.join(cmd)}")
            
            # 执行命令
            process = await asyncio.create_subprocess_exec(
                *cmd,
                cwd=str(model_path),
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0:
                # 查找生成的视频文件
                video_files = []
                for root, dirs, files in os.walk(output_dir):
                    for file in files:
                        if file.endswith('.mp4'):
                            video_files.append(os.path.join(root, file))
                
                if video_files:
                    return {
                        "success": True,
                        "video_path": video_files[0],
                        "model": "sadtalker",
                        "output_dir": output_dir,
                        "log": stdout.decode('utf-8', errors='ignore')
                    }
                else:
                    return {
                        "success": False,
                        "error": "未找到生成的视频文件",
                        "log": stdout.decode('utf-8', errors='ignore'),
                        "error_log": stderr.decode('utf-8', errors='ignore')
                    }
            else:
                return {
                    "success": False,
                    "error": f"SadTalker执行失败，返回码: {process.returncode}",
                    "log": stdout.decode('utf-8', errors='ignore'),
                    "error_log": stderr.decode('utf-8', errors='ignore')
                }
                
        except Exception as e:
            logger.error(f"SadTalker生成失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def generate_with_wav2lip(
        self, 
        image_path: str, 
        audio_path: str, 
        output_path: str,
        config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """使用Wav2Lip生成数字人视频"""
        try:
            if "wav2lip" not in self.model_configs or not self.model_configs["wav2lip"]["available"]:
                raise ValueError("Wav2Lip模型不可用")
            
            model_path = Path(self.model_configs["wav2lip"]["path"])
            
            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # 查找checkpoint文件
            checkpoint_path = model_path / "checkpoints" / "wav2lip_gan.pth"
            if not checkpoint_path.exists():
                # 尝试其他可能的checkpoint文件
                checkpoint_files = list((model_path / "checkpoints").glob("*.pth"))
                if checkpoint_files:
                    checkpoint_path = checkpoint_files[0]
                else:
                    raise ValueError("未找到Wav2Lip权重文件")
            
            # 构建命令
            cmd = [
                sys.executable,
                str(model_path / "inference.py"),
                "--checkpoint_path", str(checkpoint_path),
                "--face", image_path,
                "--audio", audio_path,
                "--outfile", output_path
            ]
            
            # 添加可选参数
            if config:
                if config.get("static", True):
                    cmd.append("--static")
                if config.get("fps"):
                    cmd.extend(["--fps", str(config["fps"])])
                if config.get("pads"):
                    cmd.extend(["--pads"] + [str(p) for p in config["pads"]])
            else:
                cmd.append("--static")  # 默认使用静态模式
            
            logger.info(f"执行Wav2Lip命令: {' '.join(cmd)}")
            
            # 执行命令
            process = await asyncio.create_subprocess_exec(
                *cmd,
                cwd=str(model_path),
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0 and os.path.exists(output_path):
                return {
                    "success": True,
                    "video_path": output_path,
                    "model": "wav2lip",
                    "log": stdout.decode('utf-8', errors='ignore')
                }
            else:
                return {
                    "success": False,
                    "error": f"Wav2Lip执行失败，返回码: {process.returncode}",
                    "log": stdout.decode('utf-8', errors='ignore'),
                    "error_log": stderr.decode('utf-8', errors='ignore')
                }
                
        except Exception as e:
            logger.error(f"Wav2Lip生成失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def generate_with_liveportrait(
        self, 
        image_path: str, 
        audio_path: str, 
        output_dir: str,
        config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """使用LivePortrait生成数字人视频"""
        try:
            if "liveportrait" not in self.model_configs or not self.model_configs["liveportrait"]["available"]:
                raise ValueError("LivePortrait模型不可用")
            
            model_path = Path(self.model_configs["liveportrait"]["path"])
            
            # 准备输出目录
            os.makedirs(output_dir, exist_ok=True)
            
            # 构建命令
            cmd = [
                sys.executable,
                str(model_path / "inference.py"),
                "-s", image_path,
                "-d", audio_path,
                "-o", output_dir
            ]
            
            logger.info(f"执行LivePortrait命令: {' '.join(cmd)}")
            
            # 执行命令
            process = await asyncio.create_subprocess_exec(
                *cmd,
                cwd=str(model_path),
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0:
                # 查找生成的视频文件
                video_files = list(Path(output_dir).glob("*.mp4"))
                if video_files:
                    return {
                        "success": True,
                        "video_path": str(video_files[0]),
                        "model": "liveportrait",
                        "output_dir": output_dir,
                        "log": stdout.decode('utf-8', errors='ignore')
                    }
                else:
                    return {
                        "success": False,
                        "error": "未找到生成的视频文件",
                        "log": stdout.decode('utf-8', errors='ignore'),
                        "error_log": stderr.decode('utf-8', errors='ignore')
                    }
            else:
                return {
                    "success": False,
                    "error": f"LivePortrait执行失败，返回码: {process.returncode}",
                    "log": stdout.decode('utf-8', errors='ignore'),
                    "error_log": stderr.decode('utf-8', errors='ignore')
                }
                
        except Exception as e:
            logger.error(f"LivePortrait生成失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def get_recommended_model(self, requirements: Dict[str, Any]) -> Optional[str]:
        """根据需求推荐最佳模型"""
        available_models = [name for name, config in self.model_configs.items() if config.get("available")]
        
        if not available_models:
            return None
        
        # 根据质量要求推荐
        quality = requirements.get("quality", "standard")
        
        if quality == "high":
            # 高质量优先使用MuseTalk或LivePortrait
            if "musetalk" in available_models:
                return "musetalk"
            elif "liveportrait" in available_models:
                return "liveportrait"
            elif "sadtalker" in available_models:
                return "sadtalker"
        elif quality == "fast":
            # 快速生成优先使用Wav2Lip
            if "wav2lip" in available_models:
                return "wav2lip"
        
        # 默认推荐顺序
        preference_order = ["musetalk", "sadtalker", "liveportrait", "wav2lip"]
        for model in preference_order:
            if model in available_models:
                return model
        
        return available_models[0] if available_models else None

# 全局模型管理器实例
model_manager = ModelManager()
