"""
数字人生成任务
"""

from celery import group, chain, chord
from app.core.celery_app import celery_app, DatabaseTask
from app.tasks.image_processing_tasks import process_avatar_image
from app.tasks.voice_processing_tasks import process_voice_sample
from app.tasks.video_generation_tasks import generate_talking_video
import logging
from datetime import datetime
from sqlalchemy.orm import Session
from app.core.sqlalchemy_db import get_db
from app.models.digital_human import DigitalHuman, DigitalHumanGeneration

logger = logging.getLogger(__name__)

@celery_app.task(bind=True, base=DatabaseTask, name="generate_full_digital_human")
def generate_full_digital_human(self, digital_human_id: int, task_id: str, input_data: dict):
    """
    生成完整数字人的主任务
    使用Celery的工作流来协调各个子任务
    """
    try:
        logger.info(f"开始生成数字人: ID={digital_human_id}, TaskID={task_id}")
        
        # 更新任务状态为开始
        self.update_task_progress(task_id, 5, "开始生成数字人...")
        
        # 创建任务工作流
        # 1. 并行处理图像和声音
        parallel_tasks = group(
            process_avatar_image.s(digital_human_id, task_id, input_data),
            process_voice_sample.s(digital_human_id, task_id, input_data)
        )
        
        # 2. 等待并行任务完成后生成视频
        workflow = chain(
            parallel_tasks,
            generate_talking_video.s(digital_human_id, task_id, input_data),
            finalize_digital_human.s(digital_human_id, task_id)
        )
        
        # 执行工作流
        result = workflow.apply_async()
        
        # 等待完成
        final_result = result.get()
        
        logger.info(f"数字人生成完成: ID={digital_human_id}")
        return final_result
        
    except Exception as e:
        logger.error(f"数字人生成失败: {str(e)}")
        
        # 更新失败状态
        db = next(get_db())
        try:
            generation_task = db.query(DigitalHumanGeneration).filter(
                DigitalHumanGeneration.task_id == task_id
            ).first()
            
            if generation_task:
                generation_task.status = "failed"
                generation_task.error_message = str(e)
                generation_task.end_time = datetime.utcnow()
                db.commit()
                
            digital_human = db.query(DigitalHuman).filter(
                DigitalHuman.id == digital_human_id
            ).first()
            
            if digital_human:
                digital_human.status = "failed"
                digital_human.error_message = str(e)
                db.commit()
                
        finally:
            db.close()
        
        raise

@celery_app.task(bind=True, base=DatabaseTask, name="finalize_digital_human")
def finalize_digital_human(self, results, digital_human_id: int, task_id: str):
    """
    完成数字人生成的最终步骤
    """
    try:
        logger.info(f"完成数字人生成: ID={digital_human_id}")
        
        # 更新进度
        self.update_task_progress(task_id, 95, "正在完成最终配置...")
        
        db = next(get_db())
        
        # 解析结果
        image_result = results[0] if len(results) > 0 else {}
        voice_result = results[1] if len(results) > 1 else {}
        video_result = results[2] if len(results) > 2 else {}
        
        # 更新数字人记录
        digital_human = db.query(DigitalHuman).filter(
            DigitalHuman.id == digital_human_id
        ).first()
        
        if digital_human:
            digital_human.status = "completed"
            digital_human.progress = 100
            digital_human.generated_image_url = image_result.get("processed_image_url")
            digital_human.voice_sample_url = voice_result.get("voice_sample_url")
            digital_human.avatar_video_url = video_result.get("video_url")
            digital_human.model_config = {
                "image_processing": image_result,
                "voice_processing": voice_result,
                "video_generation": video_result,
                "generation_time": datetime.utcnow().isoformat()
            }
            db.commit()
        
        # 更新生成任务记录
        generation_task = db.query(DigitalHumanGeneration).filter(
            DigitalHumanGeneration.task_id == task_id
        ).first()
        
        if generation_task:
            generation_task.status = "completed"
            generation_task.progress = 100
            generation_task.output_data = {
                "image_result": image_result,
                "voice_result": voice_result,
                "video_result": video_result
            }
            generation_task.result_urls = {
                "avatar_image": image_result.get("processed_image_url"),
                "voice_sample": voice_result.get("voice_sample_url"),
                "avatar_video": video_result.get("video_url")
            }
            generation_task.end_time = datetime.utcnow()
            generation_task.processing_time = (
                generation_task.end_time - generation_task.start_time
            ).total_seconds()
            db.commit()
        
        # 最终进度更新
        self.update_task_progress(task_id, 100, "数字人生成完成！")
        
        final_result = {
            "success": True,
            "digital_human_id": digital_human_id,
            "task_id": task_id,
            "urls": {
                "avatar_image": image_result.get("processed_image_url"),
                "voice_sample": voice_result.get("voice_sample_url"),
                "avatar_video": video_result.get("video_url")
            },
            "processing_results": {
                "image": image_result,
                "voice": voice_result,
                "video": video_result
            }
        }
        
        logger.info(f"数字人生成最终完成: {final_result}")
        return final_result
        
    except Exception as e:
        logger.error(f"完成数字人生成失败: {str(e)}")
        raise
    finally:
        db.close()

@celery_app.task(bind=True, base=DatabaseTask, name="cleanup_failed_generation")
def cleanup_failed_generation(self, digital_human_id: int, task_id: str, error_message: str):
    """
    清理失败的生成任务
    """
    try:
        logger.info(f"清理失败的生成任务: ID={digital_human_id}, TaskID={task_id}")
        
        db = next(get_db())
        
        # 更新数字人状态
        digital_human = db.query(DigitalHuman).filter(
            DigitalHuman.id == digital_human_id
        ).first()
        
        if digital_human:
            digital_human.status = "failed"
            digital_human.error_message = error_message
            db.commit()
        
        # 更新生成任务状态
        generation_task = db.query(DigitalHumanGeneration).filter(
            DigitalHumanGeneration.task_id == task_id
        ).first()
        
        if generation_task:
            generation_task.status = "failed"
            generation_task.error_message = error_message
            generation_task.end_time = datetime.utcnow()
            db.commit()
        
        # 清理临时文件
        # TODO: 实现临时文件清理逻辑
        
        return {
            "success": True,
            "message": "清理完成"
        }
        
    except Exception as e:
        logger.error(f"清理失败: {str(e)}")
        raise
    finally:
        db.close()

@celery_app.task(bind=True, base=DatabaseTask, name="get_generation_status")
def get_generation_status(self, task_id: str):
    """
    获取生成任务状态
    """
    try:
        db = next(get_db())
        
        generation_task = db.query(DigitalHumanGeneration).filter(
            DigitalHumanGeneration.task_id == task_id
        ).first()
        
        if not generation_task:
            return {
                "success": False,
                "error": "任务不存在"
            }
        
        digital_human = db.query(DigitalHuman).filter(
            DigitalHuman.id == generation_task.digital_human_id
        ).first()
        
        return {
            "success": True,
            "task_id": task_id,
            "status": generation_task.status,
            "progress": generation_task.progress,
            "digital_human_id": generation_task.digital_human_id,
            "digital_human_name": digital_human.name if digital_human else None,
            "start_time": generation_task.start_time.isoformat() if generation_task.start_time else None,
            "end_time": generation_task.end_time.isoformat() if generation_task.end_time else None,
            "processing_time": generation_task.processing_time,
            "error_message": generation_task.error_message,
            "result_urls": generation_task.result_urls
        }
        
    except Exception as e:
        logger.error(f"获取任务状态失败: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }
    finally:
        db.close()
