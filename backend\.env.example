# Database Configuration
DATABASE_URL=postgresql://postgres:password@localhost/ai_platform

# Security
SECRET_KEY=replace_with_secure_secret_key
ACCESS_TOKEN_EXPIRE_MINUTES=1440  # 24 hours

# MinIO Configuration
MINIO_ENDPOINT=localhost:9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin
MINIO_SECURE=False

# Redis Configuration
REDIS_URL=redis://localhost:6379/0

# AI Model Paths
DEEPSEEK_MODEL_PATH=local_models/deepseek
WANND_MODEL_PATH=local_models/wan

# API Configuration
ALLOW_ORIGINS=http://localhost:3000,http://localhost:8080

# Logging
LOG_LEVEL=INFO

# AI服务配置 (舆情分析平台)
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1
ANTHROPIC_API_KEY=your_anthropic_api_key_here
DEFAULT_AI_MODEL=gpt-3.5-turbo

# 舆情数据源配置
NEWS_API_KEY=your_news_api_key_here
TWITTER_BEARER_TOKEN=your_twitter_bearer_token_here
REDDIT_CLIENT_ID=your_reddit_client_id_here
REDDIT_CLIENT_SECRET=your_reddit_client_secret_here