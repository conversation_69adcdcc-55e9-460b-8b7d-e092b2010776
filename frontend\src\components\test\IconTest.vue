<template>
  <div class="icon-test">
    <el-card>
      <template #header>
        <span>Element Plus 图标测试</span>
      </template>
      
      <div class="icon-grid">
        <div class="icon-item">
          <el-icon><User /></el-icon>
          <span>User</span>
        </div>
        <div class="icon-item">
          <el-icon><Setting /></el-icon>
          <span>Setting</span>
        </div>
        <div class="icon-item">
          <el-icon><SwitchButton /></el-icon>
          <span>SwitchButton</span>
        </div>
        <div class="icon-item">
          <el-icon><Link /></el-icon>
          <span>Link</span>
        </div>
        <div class="icon-item">
          <el-icon><Message /></el-icon>
          <span>Message</span>
        </div>
        <div class="icon-item">
          <el-icon><Phone /></el-icon>
          <span>Phone</span>
        </div>
        <div class="icon-item">
          <el-icon><Document /></el-icon>
          <span>Document</span>
        </div>
        <div class="icon-item">
          <el-icon><DocumentCopy /></el-icon>
          <span>DocumentCopy</span>
        </div>
        <div class="icon-item">
          <el-icon><Refresh /></el-icon>
          <span>Refresh</span>
        </div>
        <div class="icon-item">
          <el-icon><VideoCamera /></el-icon>
          <span>VideoCamera</span>
        </div>
        <div class="icon-item">
          <el-icon><Microphone /></el-icon>
          <span>Microphone</span>
        </div>
        <div class="icon-item">
          <el-icon><Mute /></el-icon>
          <span>Mute</span>
        </div>
        <div class="icon-item">
          <el-icon><Picture /></el-icon>
          <span>Picture</span>
        </div>
        <div class="icon-item">
          <el-icon><Monitor /></el-icon>
          <span>Monitor</span>
        </div>
        <div class="icon-item">
          <el-icon><Calendar /></el-icon>
          <span>Calendar</span>
        </div>
        <div class="icon-item">
          <el-icon><ChatLineRound /></el-icon>
          <span>ChatLineRound</span>
        </div>
        <div class="icon-item">
          <el-icon><Service /></el-icon>
          <span>Service</span>
        </div>
        <div class="icon-item">
          <el-icon><Cloudy /></el-icon>
          <span>Cloudy</span>
        </div>
        <div class="icon-item">
          <el-icon><Box /></el-icon>
          <span>Box</span>
        </div>
        <div class="icon-item">
          <el-icon><Search /></el-icon>
          <span>Search</span>
        </div>
        <div class="icon-item">
          <el-icon><CircleClose /></el-icon>
          <span>CircleClose</span>
        </div>
        <div class="icon-item">
          <el-icon><Histogram /></el-icon>
          <span>Histogram</span>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { defineComponent } from 'vue';
import {
  User,
  Setting,
  SwitchButton,
  Link,
  Message,
  Phone,
  Document,
  DocumentCopy,
  Refresh,
  VideoCamera,
  Microphone,
  Mute,
  Picture,
  Monitor,
  Calendar,
  ChatLineRound,
  Service,
  Cloudy,
  Box,
  Search,
  CircleClose,
  Histogram
} from '@element-plus/icons-vue';

export default defineComponent({
  name: 'IconTest',
  components: {
    User,
    Setting,
    SwitchButton,
    Connection,
    Message,
    Phone,
    Document,
    DocumentCopy,
    Refresh,
    VideoCamera,
    Microphone,
    Mute,
    Picture,
    Monitor,
    Calendar,
    ChatDotRound,
    Service,
    Cloudy,
    Box,
    Search,
    CircleClose,
    Histogram
  }
});
</script>

<style scoped>
.icon-test {
  max-width: 800px;
  margin: 20px auto;
}

.icon-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 16px;
}

.icon-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  transition: all 0.3s;
}

.icon-item:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.icon-item .el-icon {
  font-size: 24px;
  margin-bottom: 8px;
  color: #409eff;
}

.icon-item span {
  font-size: 12px;
  color: #606266;
  text-align: center;
}
</style>
