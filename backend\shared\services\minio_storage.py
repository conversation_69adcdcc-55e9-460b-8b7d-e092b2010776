import os
import uuid
import logging
from datetime import timedelta

# Configure MinIO client
MINIO_DISABLED = True  # 强制禁用MinIO
MINIO_ENDPOINT = os.getenv("MINIO_ENDPOINT", "localhost:9000")
MINIO_ACCESS_KEY = os.getenv("MINIO_ACCESS_KEY", "minioadmin")
MINIO_SECRET_KEY = os.getenv("MINIO_SECRET_KEY", "minioadmin")
MINIO_SECURE = os.getenv("MINIO_SECURE", "False").lower() == "true"

# Bucket names
VIDEO_BUCKET = "videos"
IMAGE_BUCKET = "images"
DOCUMENT_BUCKET = "documents"
AUDIO_BUCKET = "audios"  # 添加音频桶

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 禁用MinIO客户端
minio_client = None
logger.info("MinIO客户端已禁用，将使用本地文件处理")

def save_video_to_storage(file_path, object_name=None):
    """保存视频文件到存储"""
    logger.info(f"保存视频到本地: {file_path}")
    return file_path

def save_image_to_storage(file_path, object_name=None):
    """保存图片文件到存储"""
    logger.info(f"保存图片到本地: {file_path}")
    return file_path

def save_document_to_storage(file_path, object_name=None):
    """保存文档文件到存储"""
    logger.info(f"保存文档到本地: {file_path}")
    return file_path

async def save_audio_to_storage(task_id, file_path, object_name=None):
    """
    保存音频文件到存储
    
    参数:
        task_id: 任务ID
        file_path: 本地文件路径
        object_name: 可选的对象名称
        
    返回:
        音频文件的URL
    """
    logger.info(f"保存音频到本地: {file_path}")
    # 在禁用MinIO的情况下，直接返回本地路径
    return get_audio_url(file_path)

def get_video_url(object_name):
    """为存储中的视频生成URL"""
    logger.info(f"生成视频本地路径: {object_name}")
    return f"local://{object_name}"

def get_audio_url(object_name):
    """
    为存储中的音频生成URL
    
    参数:
        object_name: 对象名称
        
    返回:
        音频文件的URL
    """
    logger.info(f"生成音频本地路径: {object_name}")
    return f"local://{object_name}"

def delete_object(bucket, object_name):
    """从存储中删除对象"""
    logger.warning(f"删除对象请求被忽略: {bucket}/{object_name}")
    return True 