import { get, post, del } from './index';

/**
 * 获取系统日志列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.limit - 每页数量
 * @param {string} params.level - 日志级别
 * @param {string} params.source - 日志来源
 * @param {string} params.timeRange - 时间范围
 * @returns {Promise}
 */
export function getSystemLogs(params) {
  return get('/logs/system', params);
}

/**
 * 获取日志详情
 * @param {string} id - 日志ID
 * @returns {Promise}
 */
export function getLogDetail(id) {
  return get(`/logs/${id}`);
}

/**
 * 清除日志
 * @param {Object} params - 清除参数
 * @param {string} params.before - 清除此日期之前的日志
 * @param {string} params.level - 清除指定级别的日志
 * @returns {Promise}
 */
export function clearLogs(params) {
  return post('/logs/clear', params);
}

/**
 * 导出日志
 * @param {Object} params - 导出参数
 * @returns {Promise}
 */
export function exportLogs(params) {
  return get('/logs/export', {
    ...params,
    responseType: 'blob'
  });
}

/**
 * 获取用户活动日志
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.limit - 每页数量
 * @param {string} params.userId - 用户ID
 * @param {string} params.action - 活动类型
 * @param {string} params.timeRange - 时间范围
 * @returns {Promise}
 */
export function getUserActivityLogs(params) {
  return get('/logs/user-activity', params);
}

/**
 * 获取登录日志
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.limit - 每页数量
 * @param {string} params.username - 用户名
 * @param {string} params.status - 登录状态
 * @param {string} params.ip - IP地址
 * @param {string} params.timeRange - 时间范围
 * @returns {Promise}
 */
export function getLoginLogs(params) {
  return get('/logs/login', params);
}

/**
 * 获取操作日志
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.limit - 每页数量
 * @param {string} params.username - 用户名
 * @param {string} params.module - 模块名称
 * @param {string} params.operation - 操作类型
 * @param {string} params.timeRange - 时间范围
 * @returns {Promise}
 */
export function getOperationLogs(params) {
  return get('/logs/operation', params);
}

/**
 * 获取API请求日志
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.limit - 每页数量
 * @param {string} params.method - 请求方法
 * @param {string} params.path - 请求路径
 * @param {string} params.status - 状态码
 * @param {string} params.timeRange - 时间范围
 * @returns {Promise}
 */
export function getApiLogs(params) {
  return get('/logs/api', params);
}

/**
 * 获取错误日志
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.limit - 每页数量
 * @param {string} params.type - 错误类型
 * @param {string} params.timeRange - 时间范围
 * @returns {Promise}
 */
export function getErrorLogs(params) {
  return get('/logs/error', params);
}

/**
 * 删除指定日志
 * @param {string} id - 日志ID
 * @returns {Promise}
 */
export function deleteLog(id) {
  return del(`/logs/${id}`);
}

/**
 * 获取日志统计数据
 * @param {Object} params - 查询参数
 * @param {string} params.timeRange - 时间范围
 * @returns {Promise}
 */
export function getLogStats(params) {
  return get('/logs/stats', params);
}

/**
 * 下载日志文件
 * @param {string} filename - 文件名
 * @returns {Promise}
 */
export function downloadLogFile(filename) {
  return get(`/logs/files/${filename}`, {
    responseType: 'blob'
  });
}

/**
 * 获取可用日志文件列表
 * @returns {Promise}
 */
export function getLogFiles() {
  return get('/logs/files');
}

export default {
  getSystemLogs,
  getLogDetail,
  clearLogs,
  exportLogs,
  getUserActivityLogs,
  getLoginLogs,
  getOperationLogs,
  getApiLogs,
  getErrorLogs,
  deleteLog,
  getLogStats,
  downloadLogFile,
  getLogFiles
}; 