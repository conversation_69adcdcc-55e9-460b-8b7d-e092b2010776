import { get, post } from './index';

/**
 * 管理员登录
 * @param {string} username - 用户名
 * @param {string} password - 密码
 * @returns {Promise}
 */
export function login(username, password) {
  return post('/auth/login', {
    username,
    password
  });
}

/**
 * 管理员登出
 * @returns {Promise}
 */
export function logout() {
  return post('/auth/logout');
}

/**
 * 获取当前管理员信息
 * @returns {Promise}
 */
export function getInfo() {
  return get('/auth/info');
}

/**
 * 修改密码
 * @param {string} oldPassword - 旧密码
 * @param {string} newPassword - 新密码
 * @returns {Promise}
 */
export function changePassword(oldPassword, newPassword) {
  return post('/auth/change-password', {
    oldPassword,
    newPassword
  });
}

/**
 * 重置密码请求
 * @param {string} email - 邮箱地址
 * @returns {Promise}
 */
export function requestPasswordReset(email) {
  return post('/auth/request-reset', {
    email
  });
}

/**
 * 重置密码
 * @param {string} token - 重置令牌
 * @param {string} newPassword - 新密码
 * @returns {Promise}
 */
export function resetPassword(token, newPassword) {
  return post('/auth/reset-password', {
    token,
    newPassword
  });
}

/**
 * 获取管理员权限列表
 * @returns {Promise}
 */
export function getPermissions() {
  return get('/auth/permissions');
}

/**
 * 验证两步验证
 * @param {string} code - 验证码
 * @returns {Promise}
 */
export function verifyTwoFactor(code) {
  return post('/auth/verify-two-factor', {
    code
  });
}

/**
 * 启用两步验证
 * @returns {Promise} - 返回两步验证设置信息，包含秘钥等
 */
export function enableTwoFactor() {
  return post('/auth/enable-two-factor');
}

/**
 * 禁用两步验证
 * @param {string} code - 当前有效的验证码
 * @returns {Promise}
 */
export function disableTwoFactor(code) {
  return post('/auth/disable-two-factor', {
    code
  });
}

export default {
  login,
  logout,
  getInfo,
  changePassword,
  requestPasswordReset,
  resetPassword,
  getPermissions,
  verifyTwoFactor,
  enableTwoFactor,
  disableTwoFactor
}; 