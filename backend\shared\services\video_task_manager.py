import os
import json
import logging
import time
from datetime import datetime
from typing import Dict, Any, List, Optional
import re

from utils.task_utils import normalize_task_id, generate_task_id, update_task_status

logger = logging.getLogger(__name__)

class VideoTaskManager:
    """视频翻译任务管理器，用于处理任务持久化存储和检索"""
    
    def __init__(self, storage_dir: str = "data/video_tasks"):
        """
        初始化任务管理器
        
        Args:
            storage_dir: 任务存储目录
        """
        # 使用绝对路径处理存储目录
        if not os.path.isabs(storage_dir):
            # 获取当前文件所在目录
            current_dir = os.path.dirname(os.path.abspath(__file__))
            # 获取backend目录（假设services目录在backend目录下）
            backend_dir = os.path.dirname(current_dir)
            # 构建绝对路径
            storage_dir = os.path.join(backend_dir, storage_dir)
            logger.info(f"转换为绝对路径: {storage_dir}")
        
        self.storage_dir = storage_dir
        # 确保存储目录存在
        os.makedirs(storage_dir, exist_ok=True)
        
        # 存储活跃任务
        self.active_tasks = {}
        
        # 加载现有任务
        self.load_tasks()
    
    def load_tasks(self):
        """从存储目录加载任务"""
        task_files = [f for f in os.listdir(self.storage_dir) if f.endswith('.json')]
        loaded_tasks = 0
        
        for filename in task_files:
            try:
                file_path = os.path.join(self.storage_dir, filename)
                with open(file_path, 'r', encoding='utf-8') as f:
                    task_data = json.load(f)
                
                # 获取任务ID - 支持多种ID字段
                task_id = task_data.get('job_id')
                if not task_id:
                    task_id = task_data.get('task_id')
                if not task_id:
                    # 如果没有明确的任务ID，使用文件名
                    task_id = os.path.splitext(filename)[0]
                
                # 确保任务有必要的字段
                if 'status' not in task_data:
                    task_data['status'] = 'unknown'
                if 'progress' not in task_data:
                    task_data['progress'] = 0
                if 'created_at' not in task_data:
                    task_data['created_at'] = datetime.now().isoformat()
                if 'updated_at' not in task_data:
                    task_data['updated_at'] = datetime.now().isoformat()
                
                # 添加到活跃任务
                self.active_tasks[task_id] = task_data
                loaded_tasks += 1
                
            except Exception as e:
                logger.error(f"加载任务文件 {filename} 时出错: {str(e)}")
        
        logger.info(f"从 {self.storage_dir} 加载了 {loaded_tasks} 个任务")
    
    def save_task(self, task_id: str):
        """
        保存任务到文件
        
        Args:
            task_id: 任务ID
        """
        if task_id not in self.active_tasks:
            logger.warning(f"尝试保存不存在的任务: {task_id}")
            return False
        
        task_data = self.active_tasks[task_id]
        
        try:
            # 更新保存时间
            task_data['updated_at'] = datetime.now().isoformat()
            
            # 构建文件名 - 使用任务ID或job_id
            filename = f"{task_id}.json"
            file_path = os.path.join(self.storage_dir, filename)
            
            # 保存到文件
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(task_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"任务 {task_id} 已保存到 {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"保存任务 {task_id} 时出错: {str(e)}")
            return False
    
    def add_task(self, task: Dict[str, Any]) -> str:
        """
        添加新任务
        
        Args:
            task: 任务数据字典
        
        Returns:
            任务ID
        """
        # 验证任务数据中必需的字段
        required_fields = ['job_id']
        for field in required_fields:
            if field not in task:
                if field == 'job_id' and 'task_id' in task:
                    # 如果有task_id但没有job_id，使用task_id作为job_id
                    task['job_id'] = task['task_id']
                elif field == 'job_id':
                    # 生成新的任务ID
                    task['job_id'] = generate_task_id("job_")
                else:
                    logger.warning(f"任务缺少必需字段: {field}，已设置默认值")
        
        # 确保任务有状态和进度
        if 'status' not in task:
            task['status'] = 'created'
        if 'progress' not in task:
            task['progress'] = 0
        if 'created_at' not in task:
            task['created_at'] = datetime.now().isoformat()
        if 'updated_at' not in task:
            task['updated_at'] = datetime.now().isoformat()
        
        # 获取任务ID
        task_id = task['job_id']
        
        # 添加到活跃任务
        self.active_tasks[task_id] = task
        
        # 保存任务到文件
        self.save_task(task_id)
        
        return task_id
    
    def update_task(self, task_id: str, **updates) -> bool:
        """
        更新任务信息
        
        Args:
            task_id: 任务ID
            updates: 要更新的字段和值
        
        Returns:
            是否成功更新
        """
        # 规范化任务ID
        normalized_id = normalize_task_id(task_id, self.active_tasks)
        
        # 检查任务是否存在
        if normalized_id not in self.active_tasks:
            logger.warning(f"尝试更新不存在的任务: {task_id} (规范化为: {normalized_id})")
            return False
        
        # 获取任务
        task = self.active_tasks[normalized_id]
        
        # 更新字段
        for field, value in updates.items():
            task[field] = value
        
        # 更新时间戳
        task['updated_at'] = datetime.now().isoformat()
        
        # 保存到文件
        self.save_task(normalized_id)
        
        return True
    
    def get_task(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        获取任务信息
        
        Args:
            task_id: 任务ID
        
        Returns:
            任务数据字典，如果任务不存在则返回None
        """
        # 先检查任务ID是否有效
        if not task_id:
            logger.warning("无效的任务ID: 空")
            return None
        
        # 规范化任务ID
        normalized_id = normalize_task_id(task_id, self.active_tasks)
        
        # 直接查找
        if normalized_id in self.active_tasks:
            return self.active_tasks[normalized_id]
            
        # 如果直接查找失败，尝试其他匹配方法
        # 1. 如果任务ID不带job_前缀，尝试添加前缀
        if not task_id.startswith("job_"):
            potential_id = f"job_{task_id}"
            if potential_id in self.active_tasks:
                logger.info(f"通过添加前缀找到任务: {task_id} -> {potential_id}")
                return self.active_tasks[potential_id]
                
        # 2. 如果是完整的UUID格式，尝试获取简短格式
        if "-" in task_id and len(task_id) > 30:  # 可能是完整UUID
            uuid_short = task_id.split("-")[0]
            potential_short_ids = [f"job_{uuid_short}", f"task_{uuid_short}"]
            for short_id in potential_short_ids:
                if short_id in self.active_tasks:
                    logger.info(f"从完整UUID匹配到简短任务ID: {task_id} -> {short_id}")
                    return self.active_tasks[short_id]
        
        # 3. 模糊匹配 - 通过数字部分
        num_match = re.search(r'(\d+)', task_id)
        if num_match:
            num_part = num_match.group(1)
            # 搜索所有包含此数字的任务ID
            for active_id in self.active_tasks.keys():
                if num_part in active_id:
                    logger.info(f"通过数字部分匹配找到任务: {task_id} -> {active_id}")
                    return self.active_tasks[active_id]
                    
        # 4. 字符相似度匹配
        close_matches = []
        for active_id in self.active_tasks.keys():
            # 简单的相似度检查：共同字符比例
            common_chars = set(task_id.lower()) & set(active_id.lower())
            if len(common_chars) >= min(3, len(task_id) // 2):
                close_matches.append(active_id)
        
        if close_matches:
            # 使用第一个相似匹配
            matching_id = close_matches[0]
            logger.info(f"通过字符相似度匹配找到任务: {task_id} -> {matching_id}, 可能的匹配: {close_matches}")
            return self.active_tasks[matching_id]
            
        # 未找到任务
        logger.warning(f"找不到任务: {task_id}")
        return None
    
    def update_task_status(self, task_id: str, status: str, progress: int = None, message: str = None, **additional_fields) -> bool:
        """
        更新任务状态
        
        Args:
            task_id: 任务ID
            status: 新状态
            progress: 进度值(0-100)
            message: 状态消息
            additional_fields: 要更新的其他字段
        
        Returns:
            是否成功更新
        """
        # 规范化任务ID
        normalized_id = normalize_task_id(task_id, self.active_tasks)
        
        # 检查任务是否存在
        if normalized_id not in self.active_tasks:
            logger.warning(f"尝试更新不存在任务的状态: {task_id} (规范化为: {normalized_id})")
            return False
        
        # 获取任务
        task = self.active_tasks[normalized_id]
        
        # 使用通用函数更新任务状态
        update_task_status(task, status, progress, message, **additional_fields)
        
        # 保存到文件
        self.save_task(normalized_id)
        
        return True
    
    def delete_task(self, task_id: str) -> bool:
        """
        删除任务
        
        Args:
            task_id: 任务ID
        
        Returns:
            是否成功删除
        """
        # 规范化任务ID
        normalized_id = normalize_task_id(task_id, self.active_tasks)
        
        # 检查任务是否存在
        if normalized_id not in self.active_tasks:
            logger.warning(f"尝试删除不存在的任务: {task_id} (规范化为: {normalized_id})")
            return False
        
        # 删除任务文件
        try:
            filename = f"{normalized_id}.json"
            file_path = os.path.join(self.storage_dir, filename)
            if os.path.exists(file_path):
                os.remove(file_path)
                logger.info(f"任务文件已删除: {file_path}")
        except Exception as e:
            logger.error(f"删除任务文件时出错: {str(e)}")
        
        # 从活跃任务中移除
        del self.active_tasks[normalized_id]
        logger.info(f"任务 {normalized_id} 已从活跃任务中移除")
        
        return True

# 单例模式
_task_manager_instance = None

def get_video_task_manager() -> VideoTaskManager:
    """
    获取任务管理器单例
    
    Returns:
        VideoTaskManager实例
    """
    global _task_manager_instance
    if _task_manager_instance is None:
        _task_manager_instance = VideoTaskManager()
    return _task_manager_instance 