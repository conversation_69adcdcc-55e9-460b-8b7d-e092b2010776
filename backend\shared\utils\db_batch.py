import logging
import asyncio
from typing import List, Dict, Any, Optional, Callable, TypeVar, Generic
from functools import wraps
from sqlalchemy.orm import Session
from sqlalchemy import text

logger = logging.getLogger("db_batch")

# 定义泛型类型
T = TypeVar('T')

class BatchProcessor(Generic[T]):
    """数据库批处理处理器"""
    
    def __init__(self, 
                 batch_size: int = 50, 
                 max_wait_time: float = 2.0,
                 auto_flush: bool = True):
        """
        初始化批处理处理器
        
        Args:
            batch_size: 批处理大小
            max_wait_time: 最大等待时间(秒)
            auto_flush: 是否自动刷新批处理
        """
        self.batch_size = batch_size
        self.max_wait_time = max_wait_time
        self.auto_flush = auto_flush
        self.items: List[T] = []
        self.last_flush_time = asyncio.get_event_loop().time()
        self._flush_task = None
    
    async def add(self, item: T) -> None:
        """
        添加项目到批处理
        
        Args:
            item: 要添加的项目
        """
        self.items.append(item)
        
        # 如果达到批处理大小，立即刷新
        if len(self.items) >= self.batch_size:
            await self.flush()
        # 否则，如果启用自动刷新，启动刷新定时器
        elif self.auto_flush and self._flush_task is None:
            self._schedule_flush()
    
    def _schedule_flush(self) -> None:
        """安排延迟刷新任务"""
        if self._flush_task is None:
            loop = asyncio.get_event_loop()
            self._flush_task = loop.create_task(self._delayed_flush())
    
    async def _delayed_flush(self) -> None:
        """延迟刷新任务"""
        try:
            await asyncio.sleep(self.max_wait_time)
            await self.flush()
        except asyncio.CancelledError:
            pass
        finally:
            self._flush_task = None
    
    async def flush(self) -> List[T]:
        """
        刷新当前批处理
        
        Returns:
            刷新的项目列表
        """
        # 取消任何挂起的刷新任务
        if self._flush_task is not None:
            self._flush_task.cancel()
            self._flush_task = None
        
        # 如果没有项目，返回空列表
        if not self.items:
            return []
        
        # 获取并清除当前项目
        items_to_flush = self.items[:]
        self.items.clear()
        self.last_flush_time = asyncio.get_event_loop().time()
        
        return items_to_flush

class DatabaseBatchManager:
    """数据库批处理管理器"""
    
    def __init__(self):
        """初始化数据库批处理管理器"""
        self._processors: Dict[str, BatchProcessor] = {}
    
    def get_processor(self, 
                      key: str, 
                      batch_size: int = 50, 
                      max_wait_time: float = 2.0,
                      auto_flush: bool = True) -> BatchProcessor:
        """
        获取或创建批处理处理器
        
        Args:
            key: 批处理器键
            batch_size: 批处理大小
            max_wait_time: 最大等待时间(秒)
            auto_flush: 是否自动刷新批处理
            
        Returns:
            批处理处理器
        """
        if key not in self._processors:
            self._processors[key] = BatchProcessor(
                batch_size=batch_size,
                max_wait_time=max_wait_time,
                auto_flush=auto_flush
            )
        return self._processors[key]
    
    async def flush_all(self) -> Dict[str, List[Any]]:
        """
        刷新所有批处理处理器
        
        Returns:
            每个处理器刷新的项目字典
        """
        results = {}
        for key, processor in self._processors.items():
            results[key] = await processor.flush()
        return results

# 全局批处理管理器
batch_manager = DatabaseBatchManager()

def batch_db_operation(batch_key: str, batch_size: int = 50, max_wait_time: float = 2.0):
    """
    数据库批处理操作装饰器
    
    Args:
        batch_key: 批处理键
        batch_size: 批处理大小
        max_wait_time: 最大等待时间(秒)
        
    Returns:
        装饰器函数
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 获取批处理处理器
            processor = batch_manager.get_processor(
                key=batch_key,
                batch_size=batch_size,
                max_wait_time=max_wait_time
            )
            
            # 创建操作项目
            operation_item = {
                "func": func,
                "args": args,
                "kwargs": kwargs
            }
            
            # 添加到批处理
            await processor.add(operation_item)
            
            # 返回一个虚拟结果 - 实际上这个操作会在稍后批量执行
            return True
        return wrapper
    return decorator

async def execute_batch(db: Session, batch_key: str, items: List[Dict[str, Any]]) -> List[Any]:
    """
    执行批处理操作
    
    Args:
        db: 数据库会话
        batch_key: 批处理键
        items: 批处理项目
        
    Returns:
        操作结果列表
    """
    if not items:
        return []
    
    logger.info(f"执行批处理 [Key: {batch_key}, 项目数: {len(items)}]")
    
    results = []
    for item in items:
        func = item["func"]
        args = item["args"]
        kwargs = item["kwargs"]
        
        # 添加数据库会话到关键字参数
        if "db" in kwargs:
            kwargs["db"] = db
        elif "db_session" in kwargs:
            kwargs["db_session"] = db
        
        try:
            # 执行函数
            result = await func(*args, **kwargs)
            results.append({"status": "success", "result": result})
        except Exception as e:
            logger.error(f"批处理操作失败: {str(e)}")
            results.append({"status": "error", "error": str(e)})
    
    return results

# 注册批处理处理函数
async def register_batch_processor(batch_key: str, processor_func: Callable):
    """
    注册批处理处理函数
    
    Args:
        batch_key: 批处理键
        processor_func: 处理函数
    """
    # 在这里可以实现自定义批处理逻辑
    pass

# 启动后台批处理任务
async def start_batch_processing():
    """启动后台批处理任务"""
    while True:
        try:
            # 刷新所有批处理
            await batch_manager.flush_all()
            
            # 等待一小段时间
            await asyncio.sleep(1.0)
        except Exception as e:
            logger.error(f"批处理任务发生错误: {str(e)}")
            await asyncio.sleep(5.0)

# 初始化批处理系统
def initialize_batch_system():
    """初始化批处理系统"""
    loop = asyncio.get_event_loop()
    loop.create_task(start_batch_processing()) 