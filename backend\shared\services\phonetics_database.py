import json
import os
from typing import Dict, Optional

class PhoneticsDatabase:
    """音标数据库服务"""
    
    def __init__(self):
        self.db_path = "data/phonetics"
        os.makedirs(self.db_path, exist_ok=True)
        self.cache = {}
    
    def get_phonetics(self, word: str, language: str) -> Optional[str]:
        """获取单词音标"""
        try:
            # 检查缓存
            cache_key = f"{language}:{word}"
            if cache_key in self.cache:
                return self.cache[cache_key]
            
            # 加载语言音标数据
            lang_file = os.path.join(self.db_path, f"{language}.json")
            if not os.path.exists(lang_file):
                return None
                
            with open(lang_file, "r", encoding="utf-8") as f:
                phonetics_data = json.load(f)
            
            # 获取音标
            phonetics = phonetics_data.get(word)
            if phonetics:
                self.cache[cache_key] = phonetics
            
            return phonetics
            
        except Exception as e:
            print(f"获取音标失败: {e}")
            return None
    
    def add_phonetics(self, word: str, language: str, phonetics: str):
        """添加单词音标"""
        try:
            # 加载现有数据
            lang_file = os.path.join(self.db_path, f"{language}.json")
            phonetics_data = {}
            if os.path.exists(lang_file):
                with open(lang_file, "r", encoding="utf-8") as f:
                    phonetics_data = json.load(f)
            
            # 添加新音标
            phonetics_data[word] = phonetics
            
            # 保存数据
            with open(lang_file, "w", encoding="utf-8") as f:
                json.dump(phonetics_data, f, ensure_ascii=False, indent=2)
            
            # 更新缓存
            cache_key = f"{language}:{word}"
            self.cache[cache_key] = phonetics
            
        except Exception as e:
            print(f"添加音标失败: {e}")
    
    def get_pronunciation_tips(self, language: str, phoneme: str) -> Optional[str]:
        """获取发音技巧"""
        try:
            tips_file = os.path.join(self.db_path, f"{language}_tips.json")
            if not os.path.exists(tips_file):
                return None
                
            with open(tips_file, "r", encoding="utf-8") as f:
                tips_data = json.load(f)
            
            return tips_data.get(phoneme)
            
        except Exception as e:
            print(f"获取发音技巧失败: {e}")
            return None

def get_phonetics_database():
    """获取音标数据库实例"""
    return PhoneticsDatabase() 