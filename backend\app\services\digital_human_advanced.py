"""
数字人高级功能服务
包括唇型同步、手势动作、眼神交流等
"""

import logging
import os
import cv2
import numpy as np
from typing import Dict, Any, List, Optional, Tuple
from enum import Enum
import json
import tempfile

logger = logging.getLogger(__name__)

class AnimationType(Enum):
    """动画类型"""
    LIP_SYNC = "lip_sync"                 # 唇型同步
    GESTURE = "gesture"                   # 手势动作
    EYE_CONTACT = "eye_contact"           # 眼神交流
    FACIAL_EXPRESSION = "facial_expression"  # 面部表情
    HEAD_MOVEMENT = "head_movement"       # 头部动作
    BODY_POSTURE = "body_posture"         # 身体姿态

class GestureType(Enum):
    """手势类型"""
    POINTING = "pointing"                 # 指向
    WAVING = "waving"                     # 挥手
    THUMBS_UP = "thumbs_up"              # 点赞
    OPEN_PALM = "open_palm"              # 摊手
    CLAPPING = "clapping"                # 鼓掌
    THINKING = "thinking"                # 思考
    EXPLAINING = "explaining"            # 解释
    GREETING = "greeting"                # 问候

class EyeContactPattern(Enum):
    """眼神交流模式"""
    DIRECT = "direct"                     # 直视
    NATURAL = "natural"                   # 自然
    SHY = "shy"                          # 害羞
    CONFIDENT = "confident"              # 自信
    THOUGHTFUL = "thoughtful"            # 思考

class DigitalHumanAdvancedService:
    """数字人高级功能服务"""
    
    def __init__(self):
        self.models_path = "storage/models/digital_human"
        self.initialize_models()
    
    def initialize_models(self):
        """初始化模型"""
        self.available_models = {
            "lip_sync": self._check_lip_sync_models(),
            "gesture": self._check_gesture_models(),
            "eye_contact": self._check_eye_contact_models(),
            "facial_expression": self._check_facial_expression_models()
        }
        
        logger.info(f"可用模型: {self.available_models}")
    
    def _check_lip_sync_models(self) -> List[str]:
        """检查唇型同步模型"""
        models = []
        
        # 检查Wav2Lip
        if os.path.exists(os.path.join(self.models_path, "wav2lip")):
            models.append("wav2lip")
        
        # 检查MuseTalk
        if os.path.exists(os.path.join(self.models_path, "musetalk")):
            models.append("musetalk")
        
        # 检查SadTalker
        if os.path.exists(os.path.join(self.models_path, "sadtalker")):
            models.append("sadtalker")
        
        return models
    
    def _check_gesture_models(self) -> List[str]:
        """检查手势模型"""
        models = []
        
        # 检查MediaPipe Hands
        try:
            import mediapipe as mp
            models.append("mediapipe_hands")
        except ImportError:
            pass
        
        # 检查自定义手势模型
        if os.path.exists(os.path.join(self.models_path, "gesture_models")):
            models.append("custom_gesture")
        
        return models
    
    def _check_eye_contact_models(self) -> List[str]:
        """检查眼神交流模型"""
        models = []
        
        # 检查眼部追踪模型
        try:
            import mediapipe as mp
            models.append("mediapipe_face_mesh")
        except ImportError:
            pass
        
        return models
    
    def _check_facial_expression_models(self) -> List[str]:
        """检查面部表情模型"""
        models = []
        
        # 检查LivePortrait
        if os.path.exists(os.path.join(self.models_path, "liveportrait")):
            models.append("liveportrait")
        
        return models
    
    async def generate_lip_sync(
        self, 
        image_path: str, 
        audio_path: str, 
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """生成唇型同步视频"""
        try:
            model = config.get("model", "wav2lip")
            
            if model == "wav2lip" and "wav2lip" in self.available_models["lip_sync"]:
                return await self._generate_wav2lip(image_path, audio_path, config)
            elif model == "musetalk" and "musetalk" in self.available_models["lip_sync"]:
                return await self._generate_musetalk(image_path, audio_path, config)
            elif model == "sadtalker" and "sadtalker" in self.available_models["lip_sync"]:
                return await self._generate_sadtalker(image_path, audio_path, config)
            else:
                # 使用简化的唇型同步
                return await self._generate_simple_lip_sync(image_path, audio_path, config)
                
        except Exception as e:
            logger.error(f"唇型同步生成失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _generate_wav2lip(self, image_path: str, audio_path: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """使用Wav2Lip生成唇型同步"""
        import subprocess
        
        output_dir = tempfile.mkdtemp()
        output_path = os.path.join(output_dir, "wav2lip_result.mp4")
        
        wav2lip_path = os.path.join(self.models_path, "wav2lip")
        checkpoint_path = os.path.join(wav2lip_path, "checkpoints", "wav2lip_gan.pth")
        
        cmd = [
            "python", os.path.join(wav2lip_path, "inference.py"),
            "--checkpoint_path", checkpoint_path,
            "--face", image_path,
            "--audio", audio_path,
            "--outfile", output_path,
            "--static"
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, cwd=wav2lip_path)
        
        if result.returncode == 0 and os.path.exists(output_path):
            return {
                "success": True,
                "video_path": output_path,
                "model": "wav2lip",
                "duration": self._get_video_duration(output_path)
            }
        else:
            return {
                "success": False,
                "error": f"Wav2Lip执行失败: {result.stderr}"
            }
    
    async def _generate_musetalk(self, image_path: str, audio_path: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """使用MuseTalk生成唇型同步"""
        import subprocess
        
        output_dir = tempfile.mkdtemp()
        
        musetalk_path = os.path.join(self.models_path, "musetalk")
        
        cmd = [
            "python", os.path.join(musetalk_path, "inference.py"),
            "--source_image", image_path,
            "--source_audio", audio_path,
            "--result_dir", output_dir,
            "--fps", "25"
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, cwd=musetalk_path)
        
        # 查找生成的视频文件
        video_files = [f for f in os.listdir(output_dir) if f.endswith('.mp4')]
        
        if result.returncode == 0 and video_files:
            video_path = os.path.join(output_dir, video_files[0])
            return {
                "success": True,
                "video_path": video_path,
                "model": "musetalk",
                "duration": self._get_video_duration(video_path)
            }
        else:
            return {
                "success": False,
                "error": f"MuseTalk执行失败: {result.stderr}"
            }
    
    async def _generate_sadtalker(self, image_path: str, audio_path: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """使用SadTalker生成唇型同步"""
        import subprocess
        
        output_dir = tempfile.mkdtemp()
        
        sadtalker_path = os.path.join(self.models_path, "sadtalker")
        
        cmd = [
            "python", os.path.join(sadtalker_path, "inference.py"),
            "--driven_audio", audio_path,
            "--source_image", image_path,
            "--result_dir", output_dir,
            "--still",
            "--preprocess", "full"
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, cwd=sadtalker_path)
        
        # 查找生成的视频文件
        video_files = []
        for root, dirs, files in os.walk(output_dir):
            for file in files:
                if file.endswith('.mp4'):
                    video_files.append(os.path.join(root, file))
        
        if result.returncode == 0 and video_files:
            return {
                "success": True,
                "video_path": video_files[0],
                "model": "sadtalker",
                "duration": self._get_video_duration(video_files[0])
            }
        else:
            return {
                "success": False,
                "error": f"SadTalker执行失败: {result.stderr}"
            }
    
    async def _generate_simple_lip_sync(self, image_path: str, audio_path: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """简化的唇型同步（当没有AI模型时）"""
        # 创建一个简单的视频，图像随音频节拍轻微变化
        try:
            import cv2
            
            # 读取图像
            image = cv2.imread(image_path)
            height, width = image.shape[:2]
            
            # 获取音频时长
            audio_duration = self._get_audio_duration(audio_path)
            fps = 25
            total_frames = int(audio_duration * fps)
            
            # 创建输出视频
            output_path = os.path.join(tempfile.mkdtemp(), "simple_lip_sync.mp4")
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
            
            # 生成帧
            for frame_idx in range(total_frames):
                # 简单的嘴部区域变化模拟
                frame = image.copy()
                
                # 模拟说话时的轻微变化
                if frame_idx % 10 < 5:  # 简单的开合嘴模拟
                    # 在嘴部区域添加轻微的亮度变化
                    mouth_region = frame[int(height*0.6):int(height*0.8), int(width*0.3):int(width*0.7)]
                    mouth_region = cv2.addWeighted(mouth_region, 0.9, mouth_region, 0, 5)
                    frame[int(height*0.6):int(height*0.8), int(width*0.3):int(width*0.7)] = mouth_region
                
                out.write(frame)
            
            out.release()
            
            # 合并音频
            final_output = os.path.join(tempfile.mkdtemp(), "final_lip_sync.mp4")
            cmd = [
                "ffmpeg", "-i", output_path, "-i", audio_path,
                "-c:v", "copy", "-c:a", "aac", "-shortest", final_output
            ]
            
            subprocess.run(cmd, capture_output=True)
            
            if os.path.exists(final_output):
                return {
                    "success": True,
                    "video_path": final_output,
                    "model": "simple",
                    "duration": audio_duration
                }
            else:
                return {
                    "success": False,
                    "error": "简化唇型同步生成失败"
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": f"简化唇型同步失败: {str(e)}"
            }
    
    async def generate_gesture_animation(
        self, 
        base_video_path: str, 
        gesture_type: GestureType, 
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """生成手势动画"""
        try:
            # 根据手势类型生成相应的动画
            gesture_data = self._get_gesture_keyframes(gesture_type, config)
            
            # 应用手势到视频
            result_path = await self._apply_gesture_to_video(base_video_path, gesture_data, config)
            
            return {
                "success": True,
                "video_path": result_path,
                "gesture_type": gesture_type.value,
                "keyframes": gesture_data
            }
            
        except Exception as e:
            logger.error(f"手势动画生成失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _get_gesture_keyframes(self, gesture_type: GestureType, config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """获取手势关键帧数据"""
        duration = config.get("duration", 2.0)
        fps = config.get("fps", 25)
        
        keyframes = []
        
        if gesture_type == GestureType.POINTING:
            # 指向手势的关键帧
            keyframes = [
                {"frame": 0, "hand_position": [0.5, 0.6], "hand_rotation": 0},
                {"frame": int(fps * 0.5), "hand_position": [0.7, 0.4], "hand_rotation": -15},
                {"frame": int(fps * duration), "hand_position": [0.7, 0.4], "hand_rotation": -15}
            ]
        elif gesture_type == GestureType.WAVING:
            # 挥手手势的关键帧
            keyframes = [
                {"frame": 0, "hand_position": [0.6, 0.5], "hand_rotation": 0},
                {"frame": int(fps * 0.3), "hand_position": [0.7, 0.3], "hand_rotation": 20},
                {"frame": int(fps * 0.6), "hand_position": [0.6, 0.5], "hand_rotation": -20},
                {"frame": int(fps * 0.9), "hand_position": [0.7, 0.3], "hand_rotation": 20},
                {"frame": int(fps * duration), "hand_position": [0.6, 0.5], "hand_rotation": 0}
            ]
        elif gesture_type == GestureType.THUMBS_UP:
            # 点赞手势的关键帧
            keyframes = [
                {"frame": 0, "hand_position": [0.5, 0.7], "hand_rotation": 0},
                {"frame": int(fps * 0.5), "hand_position": [0.6, 0.4], "hand_rotation": 0},
                {"frame": int(fps * duration), "hand_position": [0.6, 0.4], "hand_rotation": 0}
            ]
        
        return keyframes
    
    async def _apply_gesture_to_video(
        self, 
        video_path: str, 
        gesture_data: List[Dict[str, Any]], 
        config: Dict[str, Any]
    ) -> str:
        """将手势应用到视频"""
        # 这里应该实现真实的手势合成
        # 简化实现：返回原视频路径
        return video_path
    
    async def generate_eye_contact(
        self, 
        video_path: str, 
        pattern: EyeContactPattern, 
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """生成眼神交流效果"""
        try:
            # 根据模式调整眼部动画
            eye_data = self._get_eye_contact_pattern(pattern, config)
            
            # 应用眼神交流到视频
            result_path = await self._apply_eye_contact_to_video(video_path, eye_data, config)
            
            return {
                "success": True,
                "video_path": result_path,
                "pattern": pattern.value,
                "eye_movements": eye_data
            }
            
        except Exception as e:
            logger.error(f"眼神交流生成失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _get_eye_contact_pattern(self, pattern: EyeContactPattern, config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """获取眼神交流模式数据"""
        duration = config.get("duration", 5.0)
        fps = config.get("fps", 25)
        
        eye_movements = []
        
        if pattern == EyeContactPattern.DIRECT:
            # 直视模式：眼睛始终看向摄像头
            eye_movements = [
                {"frame": 0, "gaze_direction": [0, 0], "blink_probability": 0.1},
                {"frame": int(fps * duration), "gaze_direction": [0, 0], "blink_probability": 0.1}
            ]
        elif pattern == EyeContactPattern.NATURAL:
            # 自然模式：偶尔移开视线
            eye_movements = [
                {"frame": 0, "gaze_direction": [0, 0], "blink_probability": 0.15},
                {"frame": int(fps * 1), "gaze_direction": [0.1, -0.05], "blink_probability": 0.2},
                {"frame": int(fps * 2), "gaze_direction": [0, 0], "blink_probability": 0.15},
                {"frame": int(fps * 3.5), "gaze_direction": [-0.08, 0.03], "blink_probability": 0.18},
                {"frame": int(fps * duration), "gaze_direction": [0, 0], "blink_probability": 0.15}
            ]
        elif pattern == EyeContactPattern.SHY:
            # 害羞模式：经常移开视线
            eye_movements = [
                {"frame": 0, "gaze_direction": [0.15, -0.1], "blink_probability": 0.25},
                {"frame": int(fps * 0.5), "gaze_direction": [0, 0], "blink_probability": 0.2},
                {"frame": int(fps * 1), "gaze_direction": [0.2, -0.15], "blink_probability": 0.3},
                {"frame": int(fps * duration), "gaze_direction": [0.1, -0.08], "blink_probability": 0.25}
            ]
        
        return eye_movements
    
    async def _apply_eye_contact_to_video(
        self, 
        video_path: str, 
        eye_data: List[Dict[str, Any]], 
        config: Dict[str, Any]
    ) -> str:
        """将眼神交流应用到视频"""
        # 这里应该实现真实的眼部动画
        # 简化实现：返回原视频路径
        return video_path
    
    def _get_video_duration(self, video_path: str) -> float:
        """获取视频时长"""
        try:
            import cv2
            cap = cv2.VideoCapture(video_path)
            fps = cap.get(cv2.CAP_PROP_FPS)
            frame_count = cap.get(cv2.CAP_PROP_FRAME_COUNT)
            duration = frame_count / fps
            cap.release()
            return duration
        except:
            return 0.0
    
    def _get_audio_duration(self, audio_path: str) -> float:
        """获取音频时长"""
        try:
            import librosa
            y, sr = librosa.load(audio_path)
            return len(y) / sr
        except:
            return 3.0  # 默认3秒

# 全局服务实例
digital_human_advanced_service = DigitalHumanAdvancedService()
