/**
 * 表格工具函数
 * 用于辅助管理后台的表格操作
 */

/**
 * 创建表格列配置
 * @param {Object} config - 列配置
 * @returns {Object} - 处理后的列配置
 */
export const createColumn = (config) => {
  const defaultConfig = {
    align: 'center',
    ellipsis: true
  };
  
  return {
    ...defaultConfig,
    ...config
  };
};

/**
 * 创建操作列
 * @param {Object} options - 操作列配置选项
 * @returns {Object} - 操作列配置
 */
export const createActionColumn = (options = {}) => {
  const {
    width = 180,
    fixed = 'right',
    actions = ['view', 'edit', 'delete'],
    customActions = []
  } = options;
  
  return createColumn({
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    width,
    fixed,
    customRender: ({ record }) => ({
      actions: [
        ...actions.includes('view') ? [{
          icon: 'eye',
          text: '查看',
          onClick: () => options.onView?.(record)
        }] : [],
        ...actions.includes('edit') ? [{
          icon: 'edit',
          text: '编辑',
          onClick: () => options.onEdit?.(record)
        }] : [],
        ...actions.includes('delete') ? [{
          icon: 'delete',
          text: '删除',
          danger: true,
          onClick: () => options.onDelete?.(record)
        }] : [],
        ...customActions.map(action => ({
          ...action,
          onClick: () => action.onClick?.(record)
        }))
      ]
    })
  });
};

/**
 * 创建状态列
 * @param {Object} options - 状态列配置
 * @returns {Object} - 状态列配置
 */
export const createStatusColumn = (options = {}) => {
  const {
    title = '状态',
    dataIndex = 'status',
    width = 100,
    statusMap = {
      0: { text: '禁用', color: 'red' },
      1: { text: '正常', color: 'green' }
    }
  } = options;
  
  return createColumn({
    title,
    dataIndex,
    width,
    customRender: ({ text }) => {
      const status = statusMap[text] || { text: '未知', color: 'gray' };
      return {
        children: {
          props: { color: status.color },
          text: status.text
        }
      };
    }
  });
};

/**
 * 创建日期列
 * @param {Object} options - 日期列配置
 * @returns {Object} - 日期列配置
 */
export const createDateTimeColumn = (options = {}) => {
  const {
    title = '创建时间',
    dataIndex = 'createTime',
    width = 180,
    format = 'YYYY-MM-DD HH:mm:ss'
  } = options;
  
  return createColumn({
    title,
    dataIndex,
    width,
    customRender: ({ text }) => {
      if (!text) return '-';
      
      const dayjs = window.dayjs || (date => date);
      return dayjs(text).format(format);
    }
  });
};

/**
 * 创建标签列
 * @param {Object} options - 标签列配置
 * @returns {Object} - 标签列配置
 */
export const createTagsColumn = (options = {}) => {
  const {
    title = '标签',
    dataIndex = 'tags',
    width = 200,
    colorMap = {}
  } = options;
  
  return createColumn({
    title,
    dataIndex,
    width,
    customRender: ({ text }) => {
      if (!text || !Array.isArray(text) || text.length === 0) {
        return '-';
      }
      
      return {
        children: text.map(tag => ({
          props: {
            color: colorMap[tag] || 'blue'
          },
          text: tag
        }))
      };
    }
  });
};

/**
 * 创建图片列
 * @param {Object} options - 图片列配置
 * @returns {Object} - 图片列配置
 */
export const createImageColumn = (options = {}) => {
  const {
    title = '图片',
    dataIndex = 'image',
    width = 120,
    height = 40
  } = options;
  
  return createColumn({
    title,
    dataIndex,
    width,
    customRender: ({ text }) => {
      if (!text) return '-';
      
      return {
        children: {
          props: {
            src: text,
            width: height * 1.5,
            height,
            style: {
              objectFit: 'cover',
              borderRadius: '4px'
            }
          }
        }
      };
    }
  });
};

/**
 * 表格分页配置
 * @param {Object} options - 分页配置选项
 * @returns {Object} - 分页配置
 */
export const createPagination = (options = {}) => {
  const {
    current = 1,
    pageSize = 10,
    total = 0,
    showSizeChanger = true,
    showQuickJumper = true,
    showTotal = total => `共 ${total} 条记录`
  } = options;
  
  return {
    current,
    pageSize,
    total,
    showSizeChanger,
    showQuickJumper,
    showTotal,
    pageSizeOptions: ['10', '20', '50', '100'],
    ...options
  };
};

/**
 * 表格排序参数转换
 * @param {Object} sorter - 排序对象
 * @returns {Object} - 排序参数
 */
export const getSorterParams = (sorter) => {
  if (!sorter || !sorter.field) {
    return {};
  }
  
  return {
    orderBy: sorter.field,
    orderType: sorter.order === 'ascend' ? 'asc' : 'desc'
  };
};

/**
 * 表格筛选参数转换
 * @param {Object} filters - 筛选对象
 * @returns {Object} - 筛选参数
 */
export const getFilterParams = (filters) => {
  if (!filters) {
    return {};
  }
  
  const result = {};
  
  Object.keys(filters).forEach(key => {
    if (filters[key] && filters[key].length > 0) {
      result[key] = filters[key];
    }
  });
  
  return result;
};

/**
 * 导出表格数据到Excel
 * @param {Array} columns - 表格列配置
 * @param {Array} dataSource - 表格数据
 * @param {string} filename - 导出文件名
 */
export const exportToExcel = (columns, dataSource, filename = '导出数据') => {
  if (!window.XLSX) {
    console.error('请先引入xlsx库');
    return;
  }
  
  // 过滤出需要导出的列
  const exportColumns = columns.filter(col => col.dataIndex && !col.hideInExport);
  
  // 准备表头
  const headers = exportColumns.map(col => col.title);
  
  // 准备数据
  const data = dataSource.map(record => {
    return exportColumns.map(col => {
      // 获取单元格的值
      const value = record[col.dataIndex];
      
      // 如果列有自定义渲染函数，则使用该函数处理
      if (col.exportRender) {
        return col.exportRender(value, record);
      }
      
      return value;
    });
  });
  
  // 合并表头和数据
  const worksheet = window.XLSX.utils.aoa_to_sheet([headers, ...data]);
  
  // 创建工作簿
  const workbook = window.XLSX.utils.book_new();
  window.XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1');
  
  // 导出Excel
  window.XLSX.writeFile(workbook, `${filename}.xlsx`);
};

export default {
  createColumn,
  createActionColumn,
  createStatusColumn,
  createDateTimeColumn,
  createTagsColumn,
  createImageColumn,
  createPagination,
  getSorterParams,
  getFilterParams,
  exportToExcel
}; 