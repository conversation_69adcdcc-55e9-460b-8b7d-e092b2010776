"""
记忆管理器
负责智能体的记忆存储、检索和管理
"""

import json
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta

from sqlalchemy.orm import Session
from sqlalchemy import desc, and_
from models.true_agent import AgentMemory

logger = logging.getLogger(__name__)

class MemoryManager:
    """记忆管理器"""
    
    def __init__(self):
        # 记忆类型配置
        self.memory_configs = {
            "short_term": {
                "max_items": 20,
                "retention_hours": 24,
                "importance_threshold": 0.0
            },
            "long_term": {
                "max_items": 1000,
                "retention_days": 365,
                "importance_threshold": 0.7
            },
            "episodic": {
                "max_items": 500,
                "retention_days": 90,
                "importance_threshold": 0.5
            },
            "semantic": {
                "max_items": 2000,
                "retention_days": 730,
                "importance_threshold": 0.6
            },
            "procedural": {
                "max_items": 100,
                "retention_days": 365,
                "importance_threshold": 0.8
            }
        }
    
    async def add_memory(
        self,
        agent_id: str,
        memory_type: str,
        content: Any,
        conversation_id: str = None,
        user_id: str = None,
        importance: float = 0.5,
        context: Dict[str, Any] = None,
        db: Session = None
    ) -> AgentMemory:
        """添加记忆"""

        if not db:
            logger.error("数据库会话为None，无法添加记忆")
            raise ValueError("数据库会话不能为None")

        try:
            # 验证记忆类型
            if memory_type not in self.memory_configs:
                raise ValueError(f"无效的记忆类型: {memory_type}")
            
            # 处理内容
            if isinstance(content, dict):
                content_str = json.dumps(content, ensure_ascii=False)
            else:
                content_str = str(content)
            
            # 创建记忆
            memory = AgentMemory(
                agent_id=agent_id,
                memory_type=memory_type,
                content=content_str,
                context=context or {},
                importance=importance,
                conversation_id=conversation_id,
                user_id=user_id
            )
            
            db.add(memory)
            
            # 清理过期记忆
            await self._cleanup_expired_memories(agent_id, memory_type, db)
            
            # 限制记忆数量
            await self._limit_memory_count(agent_id, memory_type, db)
            
            db.commit()
            db.refresh(memory)
            
            logger.info(f"添加记忆成功: {memory_type} - {content_str[:50]}...")
            return memory
            
        except Exception as e:
            logger.error(f"添加记忆失败: {e}")
            raise
    
    async def get_relevant_memories(
        self,
        agent_id: str,
        query: str,
        user_id: str = None,
        memory_types: List[str] = None,
        max_results: int = 10,
        db: Session = None
    ) -> List[Dict[str, Any]]:
        """获取相关记忆"""

        if not db:
            logger.error("数据库会话为None，无法获取相关记忆")
            raise ValueError("数据库会话不能为None")

        try:
            # 构建查询条件
            conditions = [AgentMemory.agent_id == agent_id]
            
            if user_id:
                conditions.append(AgentMemory.user_id == user_id)
            
            if memory_types:
                conditions.append(AgentMemory.memory_type.in_(memory_types))
            
            # 查询记忆
            memories = db.query(AgentMemory).filter(
                and_(*conditions)
            ).order_by(
                desc(AgentMemory.importance),
                desc(AgentMemory.last_accessed)
            ).limit(max_results * 2).all()  # 获取更多记忆用于筛选
            
            if not memories:
                return []
            
            # 计算相关性
            relevant_memories = []
            query_lower = query.lower()
            
            for memory in memories:
                relevance = self._calculate_memory_relevance(memory, query_lower)
                
                if relevance > 0.1:  # 相关性阈值
                    # 更新访问信息
                    memory.last_accessed = datetime.utcnow()
                    memory.access_count += 1
                    
                    relevant_memories.append({
                        "id": memory.id,
                        "type": memory.memory_type,
                        "content": memory.content,
                        "context": memory.context,
                        "importance": memory.importance,
                        "relevance": relevance,
                        "created_at": memory.created_at.isoformat(),
                        "access_count": memory.access_count
                    })
            
            # 按相关性和重要性排序
            relevant_memories.sort(
                key=lambda x: (x["relevance"] * 0.7 + x["importance"] * 0.3),
                reverse=True
            )
            
            db.commit()
            
            return relevant_memories[:max_results]
            
        except Exception as e:
            logger.error(f"获取相关记忆失败: {e}")
            return []
    
    def _calculate_memory_relevance(self, memory: AgentMemory, query: str) -> float:
        """计算记忆相关性"""
        
        relevance = 0.0
        content_lower = memory.content.lower()
        
        # 直接匹配
        if query in content_lower:
            relevance += 0.8
        
        # 关键词匹配
        query_words = query.split()
        content_words = content_lower.split()
        
        matches = sum(1 for word in query_words if word in content_words)
        if len(query_words) > 0:
            relevance += (matches / len(query_words)) * 0.5
        
        # 上下文匹配
        if memory.context:
            context_str = json.dumps(memory.context, ensure_ascii=False).lower()
            if query in context_str:
                relevance += 0.3
        
        # 时间衰减
        days_old = (datetime.utcnow() - memory.created_at).days
        time_factor = max(0.1, 1.0 - (days_old / 365.0))
        relevance *= time_factor
        
        # 重要性加权
        relevance *= (0.5 + memory.importance * 0.5)
        
        return min(1.0, relevance)
    
    async def get_memories_by_type(
        self,
        agent_id: str,
        memory_type: str,
        limit: int = 50,
        db: Session = None
    ) -> List[Dict[str, Any]]:
        """按类型获取记忆"""
        
        try:
            memories = db.query(AgentMemory).filter(
                and_(
                    AgentMemory.agent_id == agent_id,
                    AgentMemory.memory_type == memory_type
                )
            ).order_by(
                desc(AgentMemory.importance),
                desc(AgentMemory.created_at)
            ).limit(limit).all()
            
            return [
                {
                    "id": memory.id,
                    "content": memory.content,
                    "context": memory.context,
                    "importance": memory.importance,
                    "conversation_id": memory.conversation_id,
                    "user_id": memory.user_id,
                    "created_at": memory.created_at.isoformat(),
                    "last_accessed": memory.last_accessed.isoformat() if memory.last_accessed else None,
                    "access_count": memory.access_count
                }
                for memory in memories
            ]
            
        except Exception as e:
            logger.error(f"获取记忆失败: {e}")
            return []
    
    async def update_memory_importance(
        self,
        memory_id: str,
        importance: float,
        db: Session = None
    ) -> bool:
        """更新记忆重要性"""
        
        try:
            memory = db.query(AgentMemory).filter(
                AgentMemory.id == memory_id
            ).first()
            
            if memory:
                memory.importance = max(0.0, min(1.0, importance))
                db.commit()
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"更新记忆重要性失败: {e}")
            return False
    
    async def delete_memory(
        self,
        memory_id: str,
        db: Session = None
    ) -> bool:
        """删除记忆"""
        
        try:
            memory = db.query(AgentMemory).filter(
                AgentMemory.id == memory_id
            ).first()
            
            if memory:
                db.delete(memory)
                db.commit()
                logger.info(f"删除记忆成功: {memory_id}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"删除记忆失败: {e}")
            return False
    
    async def clear_short_term_memory(
        self,
        agent_id: str,
        conversation_id: str = None,
        db: Session = None
    ) -> int:
        """清空短期记忆"""
        
        try:
            conditions = [
                AgentMemory.agent_id == agent_id,
                AgentMemory.memory_type == "short_term"
            ]
            
            if conversation_id:
                conditions.append(AgentMemory.conversation_id == conversation_id)
            
            memories = db.query(AgentMemory).filter(and_(*conditions)).all()
            count = len(memories)
            
            for memory in memories:
                db.delete(memory)
            
            db.commit()
            
            logger.info(f"清空短期记忆成功: {count} 条")
            return count
            
        except Exception as e:
            logger.error(f"清空短期记忆失败: {e}")
            return 0
    
    async def _cleanup_expired_memories(
        self,
        agent_id: str,
        memory_type: str,
        db: Session
    ):
        """清理过期记忆"""
        
        try:
            config = self.memory_configs.get(memory_type, {})
            
            if "retention_hours" in config:
                cutoff_time = datetime.utcnow() - timedelta(hours=config["retention_hours"])
            elif "retention_days" in config:
                cutoff_time = datetime.utcnow() - timedelta(days=config["retention_days"])
            else:
                return
            
            expired_memories = db.query(AgentMemory).filter(
                and_(
                    AgentMemory.agent_id == agent_id,
                    AgentMemory.memory_type == memory_type,
                    AgentMemory.created_at < cutoff_time
                )
            ).all()
            
            for memory in expired_memories:
                db.delete(memory)
            
            if expired_memories:
                logger.info(f"清理过期记忆: {len(expired_memories)} 条 ({memory_type})")
            
        except Exception as e:
            logger.error(f"清理过期记忆失败: {e}")
    
    async def _limit_memory_count(
        self,
        agent_id: str,
        memory_type: str,
        db: Session
    ):
        """限制记忆数量"""
        
        try:
            config = self.memory_configs.get(memory_type, {})
            max_items = config.get("max_items", 1000)
            
            # 获取当前记忆数量
            current_count = db.query(AgentMemory).filter(
                and_(
                    AgentMemory.agent_id == agent_id,
                    AgentMemory.memory_type == memory_type
                )
            ).count()
            
            if current_count > max_items:
                # 删除最旧的、重要性最低的记忆
                excess_count = current_count - max_items
                
                old_memories = db.query(AgentMemory).filter(
                    and_(
                        AgentMemory.agent_id == agent_id,
                        AgentMemory.memory_type == memory_type
                    )
                ).order_by(
                    AgentMemory.importance.asc(),
                    AgentMemory.created_at.asc()
                ).limit(excess_count).all()
                
                for memory in old_memories:
                    db.delete(memory)
                
                logger.info(f"删除多余记忆: {len(old_memories)} 条 ({memory_type})")
            
        except Exception as e:
            logger.error(f"限制记忆数量失败: {e}")
    
    async def get_memory_stats(
        self,
        agent_id: str,
        db: Session
    ) -> Dict[str, Any]:
        """获取记忆统计信息"""
        
        try:
            stats = {}
            total_memories = 0
            
            for memory_type in self.memory_configs.keys():
                count = db.query(AgentMemory).filter(
                    and_(
                        AgentMemory.agent_id == agent_id,
                        AgentMemory.memory_type == memory_type
                    )
                ).count()
                
                stats[memory_type] = count
                total_memories += count
            
            # 获取最近的记忆
            recent_memories = db.query(AgentMemory).filter(
                AgentMemory.agent_id == agent_id
            ).order_by(
                desc(AgentMemory.created_at)
            ).limit(5).all()
            
            return {
                "total_memories": total_memories,
                "memories_by_type": stats,
                "recent_memories": [
                    {
                        "type": memory.memory_type,
                        "content": memory.content[:100] + "..." if len(memory.content) > 100 else memory.content,
                        "importance": memory.importance,
                        "created_at": memory.created_at.isoformat()
                    }
                    for memory in recent_memories
                ]
            }
            
        except Exception as e:
            logger.error(f"获取记忆统计失败: {e}")
            return {
                "total_memories": 0,
                "memories_by_type": {},
                "recent_memories": []
            }
