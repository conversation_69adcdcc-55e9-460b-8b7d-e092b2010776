#!/usr/bin/env python3
"""
最终测试 Celery 完整流程
"""
import os
import sys
import time
from pathlib import Path
from dotenv import load_dotenv

# 添加后端路径
backend_dir = Path(__file__).parent.parent
sys.path.append(str(backend_dir))

# 加载环境变量
env_path = backend_dir / ".env"
load_dotenv(env_path)

def check_worker_status():
    """检查 Worker 状态"""
    print("🔍 检查 Celery Worker 状态")
    print("=" * 50)
    
    try:
        from app.core.celery_app import celery_app
        
        inspect = celery_app.control.inspect()
        
        # 检查活跃的 Worker
        active_workers = inspect.active()
        if active_workers:
            print(f"✅ 找到 {len(active_workers)} 个活跃的 Worker")
            for worker_name, tasks in active_workers.items():
                print(f"   🔧 {worker_name}: {len(tasks)} 个活跃任务")
            
            # 检查注册的任务
            registered = inspect.registered()
            if registered:
                for worker_name, tasks in registered.items():
                    wanx_tasks = [t for t in tasks if 'wanx' in t]
                    if wanx_tasks:
                        print(f"   🎬 {worker_name} Wanx 任务: {wanx_tasks}")
            
            return True
        else:
            print("❌ 没有找到活跃的 Worker")
            print("💡 请确保 Celery Worker 正在运行:")
            print("   cd backend && python start_celery.py")
            return False
            
    except Exception as e:
        print(f"❌ 检查 Worker 状态失败: {e}")
        return False

def create_test_task():
    """创建测试任务"""
    print(f"\n🧪 创建测试任务")
    print("=" * 50)
    
    try:
        from app.core.task_manager import task_manager
        from app.models.unified_tasks import TaskTypes
        
        # 创建任务记录
        task_id = task_manager.create_task(
            task_type=TaskTypes.VIDEO_GENERATION,
            task_subtype=TaskTypes.VideoGeneration.TEXT_TO_VIDEO,
            user_id="test-user",
            title="Celery 最终测试",
            description="测试修复后的 Celery + Wanx 集成",
            input_params={
                "prompt": "Celery 最终测试",
                "model": "t2v-1.3B",
                "duration": 3,
                "resolution": "512x512",
                "fps": 8,
                "guidance_scale": 5.0,
                "num_inference_steps": 10
            }
        )
        
        print(f"✅ 任务创建成功: {task_id}")
        return task_id
        
    except Exception as e:
        print(f"❌ 创建任务失败: {e}")
        return None

def submit_celery_task(task_id):
    """提交 Celery 任务"""
    print(f"\n🚀 提交 Celery 任务")
    print("=" * 50)
    
    try:
        from app.tasks.wanx_video_tasks import wanx_text_to_video
        
        # 提交任务到 Celery
        result = wanx_text_to_video.delay(
            task_id=task_id,
            prompt="Celery 最终测试",
            model="t2v-1.3B",
            duration=3,
            resolution="512x512",
            fps=8,
            guidance_scale=5.0,
            num_inference_steps=10,
            user_id="test-user"
        )
        
        print(f"✅ Celery 任务提交成功")
        print(f"📊 Celery 任务ID: {result.id}")
        print(f"📊 初始状态: {result.status}")
        
        return result
        
    except Exception as e:
        print(f"❌ 提交 Celery 任务失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def monitor_task_progress(task_id, celery_result, max_wait=120):
    """监控任务进度"""
    print(f"\n⏳ 监控任务进度（最多等待 {max_wait} 秒）")
    print("=" * 50)
    
    try:
        from app.core.task_manager import task_manager
        
        start_time = time.time()
        last_status = None
        last_progress = None
        
        while time.time() - start_time < max_wait:
            # 检查数据库中的任务状态
            task_info = task_manager.get_task(task_id)
            if task_info:
                status = task_info['status']
                progress = task_info['progress']
                message = task_info.get('message', 'N/A')
                
                # 只在状态或进度变化时打印
                if status != last_status or progress != last_progress:
                    elapsed = int(time.time() - start_time)
                    print(f"   [{elapsed:3d}s] {status} - {progress}% - {message}")
                    last_status = status
                    last_progress = progress
                
                # 检查是否完成
                if status == 'completed':
                    output_data = task_info.get('output_data', {})
                    video_url = output_data.get('video_url', 'N/A')
                    print(f"🎉 任务完成！视频URL: {video_url}")
                    return True
                elif status == 'failed':
                    error_msg = task_info.get('error_message', 'N/A')
                    print(f"❌ 任务失败: {error_msg}")
                    return False
            
            # 检查 Celery 任务状态
            celery_status = celery_result.status
            if celery_status != 'PENDING':
                print(f"   Celery 状态: {celery_status}")
            
            time.sleep(2)
        
        print(f"⏰ 监控超时")
        return False
        
    except Exception as e:
        print(f"❌ 监控任务失败: {e}")
        return False

def check_redis_queues():
    """检查 Redis 队列状态"""
    print(f"\n🔍 检查 Redis 队列状态")
    print("=" * 50)
    
    try:
        import redis
        
        r = redis.Redis(host='localhost', port=6379, db=0)
        
        queues = ['wanx_generation', 'celery']
        for queue in queues:
            length = r.llen(queue)
            print(f"   {queue}: {length} 个任务")
            
            if length > 0:
                # 查看队列中的任务
                tasks = r.lrange(queue, 0, 2)
                for i, task in enumerate(tasks):
                    task_str = task.decode()[:100]
                    print(f"     任务 {i+1}: {task_str}...")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查 Redis 队列失败: {e}")
        return False

def main():
    """主函数"""
    print("🧪 Celery + Wanx 最终集成测试")
    print("=" * 60)
    
    # 1. 检查 Worker 状态
    worker_ok = check_worker_status()
    if not worker_ok:
        print(f"\n❌ Worker 未运行，无法继续测试")
        return
    
    # 2. 检查 Redis 队列
    redis_ok = check_redis_queues()
    
    # 3. 创建测试任务
    task_id = create_test_task()
    if not task_id:
        return
    
    # 4. 提交 Celery 任务
    celery_result = submit_celery_task(task_id)
    if not celery_result:
        return
    
    # 5. 监控任务进度
    success = monitor_task_progress(task_id, celery_result, max_wait=60)
    
    # 6. 再次检查队列状态
    print(f"\n📊 任务提交后的队列状态:")
    check_redis_queues()
    
    # 总结
    print(f"\n📊 最终测试总结:")
    print(f"✅ Worker 状态: 正常")
    print(f"{'✅' if redis_ok else '❌'} Redis 连接: {'正常' if redis_ok else '异常'}")
    print(f"✅ 任务创建: 成功")
    print(f"✅ Celery 提交: 成功")
    print(f"{'✅' if success else '❌'} 任务执行: {'成功' if success else '失败/超时'}")
    
    if success:
        print(f"\n🎉 Celery + Wanx 集成测试完全成功！")
        print(f"💡 系统现在可以正常处理视频生成任务")
        print(f"💡 前端界面应该能看到任务进度和完成状态")
    else:
        print(f"\n⚠️ 任务执行可能需要更长时间")
        print(f"💡 可以在前端界面查看任务状态")
        print(f"💡 或者检查 Celery Worker 日志")

if __name__ == "__main__":
    main()
