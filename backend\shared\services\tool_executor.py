"""
工具执行器
负责执行各种工具调用
"""

import asyncio
import json
import time
import subprocess
import requests
import logging
from typing import Dict, Any, Optional
from datetime import datetime

from sqlalchemy.orm import Session
from models.true_agent import ToolExecution

logger = logging.getLogger(__name__)

class ToolExecutor:
    """工具执行器"""
    
    def __init__(self):
        self.tools = {}
        self._register_builtin_tools()
    
    def _register_builtin_tools(self):
        """注册内置工具"""
        
        # 网络搜索工具
        self.tools["web_search"] = self._web_search
        
        # 代码执行工具
        self.tools["code_execution"] = self._code_execution
        
        # 翻译工具
        self.tools["translation"] = self._translation
        
        # 数据分析工具
        self.tools["data_analysis"] = self._data_analysis
        
        # 图像处理工具
        self.tools["image_processing"] = self._image_processing
        
        # 文件操作工具
        self.tools["file_operation"] = self._file_operation
        
        # API调用工具
        self.tools["api_call"] = self._api_call
    
    async def execute_tool(
        self,
        tool_type: str,
        params: Dict[str, Any],
        agent_id: str,
        conversation_id: str,
        db: Session
    ) -> Dict[str, Any]:
        """执行工具"""

        if not db:
            logger.error("数据库会话为None，无法执行工具")
            raise ValueError("数据库会话不能为None")

        start_time = time.time()

        # 创建执行记录
        execution = ToolExecution(
            agent_id=agent_id,
            conversation_id=conversation_id,
            tool_type=tool_type,
            input_params=params,
            status="pending"
        )
        try:
            db.add(execution)
            db.commit()
        except Exception as e:
            logger.error(f"保存工具执行记录失败: {e}")
            db.rollback()
            raise
        
        try:
            # 检查工具是否存在
            if tool_type not in self.tools:
                raise ValueError(f"未知的工具类型: {tool_type}")
            
            logger.info(f"[工具执行] {tool_type}: {params}")
            
            # 执行工具
            result = await self.tools[tool_type](params)
            
            # 更新执行记录
            execution.output_result = result
            execution.status = "success" if result.get("success") else "error"
            execution.execution_time = time.time() - start_time
            
            if not result.get("success"):
                execution.error_message = result.get("error", "执行失败")
            
            db.commit()
            
            logger.info(f"[工具结果] {tool_type}: {result}")
            return result
            
        except Exception as e:
            # 更新执行记录
            execution.status = "error"
            execution.error_message = str(e)
            execution.execution_time = time.time() - start_time
            db.commit()
            
            logger.error(f"工具执行失败 {tool_type}: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _web_search(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """网络搜索工具"""
        try:
            query = params.get("query", "")
            max_results = params.get("max_results", 5)
            
            if not query:
                return {"success": False, "error": "搜索关键词不能为空"}
            
            # 模拟搜索结果（实际应该调用真实的搜索API）
            results = [
                {
                    "title": f"关于 \"{query}\" 的搜索结果 1",
                    "url": "https://example.com/1",
                    "snippet": f"这是关于 {query} 的详细信息和相关内容...",
                    "source": "示例网站"
                },
                {
                    "title": f"关于 \"{query}\" 的搜索结果 2", 
                    "url": "https://example.com/2",
                    "snippet": f"更多关于 {query} 的专业分析和见解...",
                    "source": "专业网站"
                },
                {
                    "title": f"\"{query}\" 最新动态",
                    "url": "https://example.com/3", 
                    "snippet": f"最新的 {query} 相关新闻和发展趋势...",
                    "source": "新闻网站"
                }
            ]
            
            return {
                "success": True,
                "results": results[:max_results],
                "total_results": len(results),
                "query": query
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def _code_execution(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """代码执行工具"""
        try:
            code = params.get("code", "")
            timeout = params.get("timeout", 30)
            language = params.get("language", "python")
            
            if not code:
                return {"success": False, "error": "代码不能为空"}
            
            if language != "python":
                return {"success": False, "error": "目前只支持Python代码执行"}
            
            # 安全检查
            dangerous_keywords = ["import os", "import sys", "subprocess", "eval", "exec", "__import__"]
            if any(keyword in code for keyword in dangerous_keywords):
                return {"success": False, "error": "代码包含危险操作，执行被拒绝"}
            
            # 执行代码（在受限环境中）
            try:
                # 创建受限的执行环境
                restricted_globals = {
                    "__builtins__": {
                        "print": print,
                        "len": len,
                        "str": str,
                        "int": int,
                        "float": float,
                        "list": list,
                        "dict": dict,
                        "range": range,
                        "sum": sum,
                        "max": max,
                        "min": min,
                        "abs": abs,
                        "round": round
                    }
                }
                
                # 捕获输出
                import io
                import sys
                old_stdout = sys.stdout
                sys.stdout = captured_output = io.StringIO()
                
                # 执行代码
                exec(code, restricted_globals)
                
                # 恢复输出
                sys.stdout = old_stdout
                output = captured_output.getvalue()
                
                return {
                    "success": True,
                    "output": output or "代码执行完成，无输出",
                    "code": code,
                    "language": language
                }
                
            except Exception as e:
                return {
                    "success": False,
                    "error": f"代码执行错误: {str(e)}",
                    "code": code
                }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def _translation(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """翻译工具"""
        try:
            text = params.get("text", "")
            target_language = params.get("target_language", "zh")
            source_language = params.get("source_language", "auto")
            
            if not text:
                return {"success": False, "error": "待翻译文本不能为空"}
            
            # 模拟翻译（实际应该调用翻译API）
            translations = {
                "hello": {"zh": "你好", "ja": "こんにちは", "fr": "bonjour", "es": "hola"},
                "world": {"zh": "世界", "ja": "世界", "fr": "monde", "es": "mundo"},
                "good morning": {"zh": "早上好", "ja": "おはようございます", "fr": "bonjour", "es": "buenos días"},
                "thank you": {"zh": "谢谢", "ja": "ありがとう", "fr": "merci", "es": "gracias"},
                "how are you": {"zh": "你好吗", "ja": "元気ですか", "fr": "comment allez-vous", "es": "¿cómo estás?"}
            }
            
            text_lower = text.lower().strip()
            if text_lower in translations and target_language in translations[text_lower]:
                translated_text = translations[text_lower][target_language]
            else:
                # 默认翻译
                translated_text = f"[翻译] {text} -> {target_language}"
            
            return {
                "success": True,
                "translated_text": translated_text,
                "source_text": text,
                "source_language": source_language,
                "target_language": target_language
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def _data_analysis(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """数据分析工具"""
        try:
            data = params.get("data", {})
            analysis_type = params.get("analysis_type", "summary")
            
            if not data:
                return {"success": False, "error": "数据不能为空"}
            
            # 模拟数据分析
            if analysis_type == "summary":
                result = {
                    "type": "summary",
                    "data_points": len(data) if isinstance(data, (list, dict)) else 1,
                    "summary": "数据分析完成",
                    "insights": [
                        "数据质量良好",
                        "发现3个关键趋势",
                        "建议进一步分析"
                    ],
                    "chart_data": {
                        "labels": ["A", "B", "C", "D"],
                        "values": [10, 25, 15, 30]
                    }
                }
            else:
                result = {
                    "type": analysis_type,
                    "message": f"已完成 {analysis_type} 分析"
                }
            
            return {
                "success": True,
                "analysis_result": result,
                "analysis_type": analysis_type
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def _image_processing(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """图像处理工具"""
        try:
            image_url = params.get("image_url", "")
            operation = params.get("operation", "analyze")
            
            if not image_url:
                return {"success": False, "error": "图像URL不能为空"}
            
            # 模拟图像处理
            result = {
                "processed_image_url": image_url,
                "operation": operation,
                "analysis": {
                    "objects": ["人物", "建筑", "天空"],
                    "colors": ["蓝色", "白色", "灰色"],
                    "description": "这是一张城市建筑的照片，天空晴朗",
                    "confidence": 0.85
                }
            }
            
            return {
                "success": True,
                "result": result,
                "operation": operation
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def _file_operation(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """文件操作工具"""
        try:
            operation = params.get("operation", "read")
            file_path = params.get("file_path", "")
            
            # 安全检查
            if not file_path or ".." in file_path or file_path.startswith("/"):
                return {"success": False, "error": "文件路径不安全"}
            
            # 模拟文件操作
            if operation == "read":
                return {
                    "success": True,
                    "content": "这是文件的模拟内容...",
                    "file_path": file_path,
                    "operation": operation
                }
            elif operation == "write":
                content = params.get("content", "")
                return {
                    "success": True,
                    "message": f"已写入 {len(content)} 字符到文件",
                    "file_path": file_path,
                    "operation": operation
                }
            else:
                return {"success": False, "error": f"不支持的操作: {operation}"}
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def _api_call(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """API调用工具"""
        try:
            url = params.get("url", "")
            method = params.get("method", "GET").upper()
            headers = params.get("headers", {})
            data = params.get("data", {})
            timeout = params.get("timeout", 10)
            
            if not url:
                return {"success": False, "error": "URL不能为空"}
            
            # 安全检查
            if not url.startswith(("http://", "https://")):
                return {"success": False, "error": "只支持HTTP/HTTPS协议"}
            
            # 模拟API调用
            return {
                "success": True,
                "response": {
                    "status_code": 200,
                    "data": {"message": "API调用成功", "timestamp": datetime.utcnow().isoformat()},
                    "headers": {"content-type": "application/json"}
                },
                "url": url,
                "method": method
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def get_available_tools(self) -> Dict[str, Dict[str, Any]]:
        """获取可用工具列表"""
        return {
            "web_search": {
                "name": "网络搜索",
                "description": "在互联网上搜索信息",
                "icon": "🔍",
                "category": "信息获取",
                "parameters": {
                    "query": {"type": "string", "required": True, "description": "搜索关键词"},
                    "max_results": {"type": "integer", "default": 5, "description": "最大结果数"}
                }
            },
            "code_execution": {
                "name": "代码执行",
                "description": "执行Python代码",
                "icon": "💻",
                "category": "编程开发",
                "parameters": {
                    "code": {"type": "string", "required": True, "description": "Python代码"},
                    "timeout": {"type": "integer", "default": 30, "description": "超时时间（秒）"}
                }
            },
            "translation": {
                "name": "文本翻译",
                "description": "翻译文本到指定语言",
                "icon": "🌐",
                "category": "语言处理",
                "parameters": {
                    "text": {"type": "string", "required": True, "description": "待翻译文本"},
                    "target_language": {"type": "string", "required": True, "description": "目标语言"},
                    "source_language": {"type": "string", "default": "auto", "description": "源语言"}
                }
            },
            "data_analysis": {
                "name": "数据分析",
                "description": "分析和可视化数据",
                "icon": "📊",
                "category": "数据科学",
                "parameters": {
                    "data": {"type": "object", "required": True, "description": "数据"},
                    "analysis_type": {"type": "string", "required": True, "description": "分析类型"}
                }
            },
            "image_processing": {
                "name": "图像处理",
                "description": "处理和分析图像",
                "icon": "🖼️",
                "category": "多媒体",
                "parameters": {
                    "image_url": {"type": "string", "required": True, "description": "图像URL"},
                    "operation": {"type": "string", "required": True, "description": "处理操作"}
                }
            },
            "file_operation": {
                "name": "文件操作",
                "description": "读写文件",
                "icon": "📁",
                "category": "系统操作",
                "parameters": {
                    "operation": {"type": "string", "required": True, "description": "操作类型"},
                    "file_path": {"type": "string", "required": True, "description": "文件路径"}
                }
            },
            "api_call": {
                "name": "API调用",
                "description": "调用外部API",
                "icon": "🔌",
                "category": "网络通信",
                "parameters": {
                    "url": {"type": "string", "required": True, "description": "API地址"},
                    "method": {"type": "string", "default": "GET", "description": "HTTP方法"}
                }
            }
        }
