<template>
  <div id="app">
    <a-layout style="min-height: 100vh">
      <a-layout-header style="background: #001529; padding: 0 20px;">
        <div style="color: white; font-size: 18px; font-weight: bold;">
          AI翻译系统 - 管理后台
        </div>
      </a-layout-header>
      
      <a-layout-content style="padding: 20px;">
        <router-view />
      </a-layout-content>
    </a-layout>
  </div>
</template>

<script>
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'App'
})
</script>

<style>
#app {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}
</style>
