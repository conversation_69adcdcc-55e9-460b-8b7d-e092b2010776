#!/usr/bin/env python3
"""
最基本的 Celery 测试
"""
import os
import sys
import time
from pathlib import Path
from dotenv import load_dotenv

# 添加后端路径
backend_dir = Path(__file__).parent.parent
sys.path.append(str(backend_dir))

# 加载环境变量
env_path = backend_dir / ".env"
load_dotenv(env_path)

def test_celery_ping():
    """测试 Celery ping"""
    print("🏓 测试 Celery ping")
    print("=" * 50)
    
    try:
        from app.core.celery_app import celery_app
        
        # 发送 ping 命令
        result = celery_app.control.ping(timeout=5)
        
        if result:
            print(f"✅ Celery ping 成功: {result}")
            return True
        else:
            print(f"❌ Celery ping 失败: 没有响应")
            return False
            
    except Exception as e:
        print(f"❌ Celery ping 异常: {e}")
        return False

def test_simple_add_task():
    """测试最简单的加法任务"""
    print(f"\n🧮 测试最简单的加法任务")
    print("=" * 50)
    
    try:
        from celery import Celery
        
        # 创建一个临时的简单任务
        app = Celery('test_app', broker='redis://localhost:6379/0')
        
        @app.task
        def add(x, y):
            return x + y
        
        # 发送任务
        result = add.delay(4, 4)
        
        print(f"✅ 任务提交成功")
        print(f"📊 任务ID: {result.id}")
        print(f"📊 初始状态: {result.status}")
        
        # 等待结果
        print(f"⏳ 等待结果...")
        try:
            final_result = result.get(timeout=10)
            print(f"✅ 任务完成: 4 + 4 = {final_result}")
            return True
        except Exception as e:
            print(f"❌ 获取结果失败: {e}")
            return False
            
    except Exception as e:
        print(f"❌ 简单加法任务失败: {e}")
        return False

def check_redis_connection():
    """检查 Redis 连接"""
    print(f"\n🔍 检查 Redis 连接")
    print("=" * 50)
    
    try:
        import redis
        
        r = redis.Redis(host='localhost', port=6379, db=0)
        r.ping()
        print(f"✅ Redis 连接正常")
        
        # 检查队列
        queues = ['test', 'wanx_generation', 'celery']
        for queue in queues:
            length = r.llen(queue)
            print(f"   {queue}: {length} 个任务")
        
        return True
        
    except Exception as e:
        print(f"❌ Redis 连接失败: {e}")
        return False

def test_worker_inspect():
    """测试 Worker 检查"""
    print(f"\n🔍 测试 Worker 检查")
    print("=" * 50)
    
    try:
        from app.core.celery_app import celery_app
        
        inspect = celery_app.control.inspect()
        
        # 检查活跃的 Worker
        print(f"📊 检查活跃 Worker...")
        active = inspect.active()
        if active:
            print(f"✅ 活跃 Worker: {list(active.keys())}")
            for worker, tasks in active.items():
                print(f"   {worker}: {len(tasks)} 个活跃任务")
        else:
            print(f"❌ 没有活跃的 Worker")
            return False
        
        # 检查注册的任务
        print(f"\n📊 检查注册任务...")
        registered = inspect.registered()
        if registered:
            for worker, tasks in registered.items():
                print(f"   {worker}: {len(tasks)} 个注册任务")
                # 只显示前几个任务
                for task in tasks[:5]:
                    print(f"     - {task}")
                if len(tasks) > 5:
                    print(f"     ... 还有 {len(tasks) - 5} 个任务")
        else:
            print(f"❌ 无法获取注册任务")
        
        # 检查统计信息
        print(f"\n📊 检查 Worker 统计...")
        stats = inspect.stats()
        if stats:
            for worker, stat in stats.items():
                print(f"   {worker}:")
                print(f"     总任务数: {stat.get('total', 'N/A')}")
                pool_info = stat.get('pool', {})
                print(f"     进程数: {pool_info.get('max-concurrency', 'N/A')}")
        else:
            print(f"❌ 无法获取 Worker 统计")
        
        return True
        
    except Exception as e:
        print(f"❌ Worker 检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🧪 最基本的 Celery 测试")
    print("=" * 60)
    
    # 1. 检查 Redis 连接
    redis_ok = check_redis_connection()
    
    # 2. 测试 Worker 检查
    worker_ok = test_worker_inspect()
    
    # 3. 测试 Celery ping
    ping_ok = test_celery_ping()
    
    # 4. 测试简单加法任务（如果前面都成功）
    add_ok = False
    if redis_ok and worker_ok:
        add_ok = test_simple_add_task()
    
    # 总结
    print(f"\n📊 基本测试总结:")
    print(f"{'✅' if redis_ok else '❌'} Redis 连接: {'正常' if redis_ok else '失败'}")
    print(f"{'✅' if worker_ok else '❌'} Worker 检查: {'正常' if worker_ok else '失败'}")
    print(f"{'✅' if ping_ok else '❌'} Celery ping: {'成功' if ping_ok else '失败'}")
    print(f"{'✅' if add_ok else '❌'} 简单任务: {'成功' if add_ok else '失败/跳过'}")
    
    if add_ok:
        print(f"\n🎉 Celery 基本功能正常！")
        print(f"💡 问题可能在于我们的自定义任务配置")
    elif ping_ok:
        print(f"\n✅ Celery Worker 能响应 ping")
        print(f"❌ 但无法处理任务")
        print(f"💡 可能的问题:")
        print(f"   1. 任务路由配置有问题")
        print(f"   2. 任务序列化有问题")
        print(f"   3. Worker 配置有问题")
    else:
        print(f"\n❌ Celery 基本功能有问题")
        print(f"💡 需要检查:")
        print(f"   1. Worker 是否真的在运行")
        print(f"   2. Redis 连接是否正常")
        print(f"   3. Celery 配置是否正确")

if __name__ == "__main__":
    main()
