import { onMounted, onUnmounted, ref, unref } from 'vue';

/**
 * 事件监听钩子
 * @param {string|Element|Ref<Element>|Window|Document|null} target - 目标元素或选择器
 * @param {string} event - 事件名称
 * @param {Function} listener - 事件监听函数
 * @param {Object|boolean} options - 事件监听选项或是否捕获阶段处理
 * @returns {Object} 事件监听控制对象
 */
export default function useEventListener(target, event, listener, options = {}) {
  // 检查参数
  if (!target || !event || typeof listener !== 'function') {
    console.error('useEventListener: 必须提供有效的 target, event 和 listener');
    return { remove: () => {} };
  }
  
  // 是否已添加监听器
  const isListening = ref(false);
  
  // 清除函数列表
  let cleanup = null;
  
  // 获取目标元素
  const getTargetElement = (target) => {
    let targetElement;
    
    // 如果是ref，获取其值
    if (target && 'value' in target) {
      targetElement = unref(target);
    } else {
      targetElement = target;
    }
    
    // 如果是字符串，作为选择器查询元素
    if (typeof targetElement === 'string') {
      targetElement = document.querySelector(targetElement);
    }
    
    // 处理特殊情况，如window/document
    if (targetElement === undefined) {
      if (target === window || target === document) {
        targetElement = target;
      }
    }
    
    return targetElement;
  };
  
  // 添加事件监听
  const addListener = () => {
    if (isListening.value) return;
    
    const targetElement = getTargetElement(target);
    if (!targetElement) return;
    
    // 添加事件监听器
    targetElement.addEventListener(event, listener, options);
    isListening.value = true;
    
    // 设置清除函数
    cleanup = () => {
      targetElement.removeEventListener(event, listener, options);
      isListening.value = false;
      cleanup = null;
    };
  };
  
  // 移除事件监听
  const removeListener = () => {
    if (cleanup) {
      cleanup();
    }
  };
  
  // 组件挂载时添加监听
  onMounted(() => {
    addListener();
  });
  
  // 组件卸载时移除监听
  onUnmounted(() => {
    removeListener();
  });
  
  return {
    isListening,
    remove: removeListener,
    add: addListener
  };
} 