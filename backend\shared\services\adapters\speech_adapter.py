"""
语音服务适配器 - 将语音相关服务适配到统一的任务处理流程
"""
import logging
import os
from typing import Dict, Any, Optional, List

from services.task_adapter import TaskAdapter, register_adapter
from services.storage_manager import get_storage_manager

# 配置日志
logger = logging.getLogger(__name__)

class SpeechToTextAdapter(TaskAdapter):
    """语音识别服务适配器 - 录音转文字"""
    
    async def execute(self, task_id: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行语音识别任务
        
        Args:
            task_id: 任务ID
            params: 任务参数，包括:
                audio_path: 音频文件路径
                language: 语言代码
                model: 模型名称（可选）
                
        Returns:
            执行结果
        """
        try:
            # 更新进度
            await self.update_progress(task_id, 10, "准备语音识别参数...")
            
            # 提取参数
            audio_path = params.get("audio_path")
            language = params.get("language", "zh-CN")
            model = params.get("model", "default")
            
            # 验证参数
            if not audio_path:
                return await self.handle_error(task_id, ValueError("缺少必要的音频文件路径参数"))
            
            if not os.path.exists(audio_path):
                return await self.handle_error(task_id, FileNotFoundError(f"音频文件不存在: {audio_path}"))
            
            # 更新进度
            await self.update_progress(task_id, 20, f"开始语音识别，语言: {language}...")
            
            # 获取存储管理器
            storage_manager = get_storage_manager()
            
            # 准备输出文件路径
            transcript_output = storage_manager.get_result_path("transcription", f"{task_id}_transcript.txt", task_id)
            
            # 确保目录存在
            os.makedirs(os.path.dirname(transcript_output), exist_ok=True)
            
            # 调用语音识别服务
            recognition_result = await self.service.transcribe_audio(
                audio_path=audio_path,
                language=language,
                model=model,
                output_path=transcript_output
            )
            
            # 如果识别失败
            if not recognition_result:
                return await self.handle_error(task_id, ValueError("语音识别失败"))
            
            # 更新进度
            await self.update_progress(task_id, 90, "语音识别完成，处理结果...")
            
            # 构建返回结果
            result = {
                "transcript": recognition_result.get("text", ""),
                "transcript_file": transcript_output,
                "language": language,
                "duration": recognition_result.get("duration", 0),
                "confidence": recognition_result.get("confidence", 0),
                "output_files": [transcript_output]
            }
            
            # 更新最终进度
            await self.update_progress(
                task_id, 
                100, 
                "语音识别任务完成", 
                status="completed",
                result=result
            )
            
            # 返回成功结果
            return {
                "success": True,
                "data": result
            }
            
        except Exception as e:
            # 处理异常
            return await self.handle_error(task_id, e)

class TextToSpeechAdapter(TaskAdapter):
    """语音合成服务适配器 - 文字转录音"""
    
    async def execute(self, task_id: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行语音合成任务
        
        Args:
            task_id: 任务ID
            params: 任务参数，包括:
                text: 要合成的文本
                language: 语言代码
                voice_id: 声音ID
                speed: 语速调整
                pitch: 音调调整
                
        Returns:
            执行结果
        """
        try:
            # 更新进度
            await self.update_progress(task_id, 10, "准备语音合成参数...")
            
            # 提取参数
            text = params.get("text")
            language = params.get("language", "zh-CN")
            voice_id = params.get("voice_id", "default")
            speed = params.get("speed", 1.0)
            pitch = params.get("pitch", 0.0)
            
            # 验证参数
            if not text:
                return await self.handle_error(task_id, ValueError("缺少必要的文本参数"))
            
            # 更新进度
            await self.update_progress(task_id, 20, f"开始语音合成，语言: {language}, 声音: {voice_id}...")
            
            # 获取存储管理器
            storage_manager = get_storage_manager()
            
            # 准备输出文件路径
            audio_output = storage_manager.get_result_path("audio", f"{task_id}_speech.mp3", task_id)
            
            # 确保目录存在
            os.makedirs(os.path.dirname(audio_output), exist_ok=True)
            
            # 调用语音合成服务
            synthesis_result = await self.service.synthesize_speech(
                text=text,
                language=language,
                voice_id=voice_id,
                speed=speed,
                pitch=pitch,
                output_path=audio_output
            )
            
            # 如果合成失败
            if not synthesis_result:
                return await self.handle_error(task_id, ValueError("语音合成失败"))
            
            # 更新进度
            await self.update_progress(task_id, 90, "语音合成完成，处理结果...")
            
            # 构建返回结果
            audio_url = synthesis_result.get("audio_url", audio_output)
            
            result = {
                "audio_url": audio_url,
                "audio_file": audio_output,
                "text": text,
                "language": language,
                "voice_id": voice_id,
                "duration": synthesis_result.get("duration", 0),
                "output_files": [audio_output]
            }
            
            # 更新最终进度
            await self.update_progress(
                task_id, 
                100, 
                "语音合成任务完成", 
                status="completed",
                result=result
            )
            
            # 返回成功结果
            return {
                "success": True,
                "data": result
            }
            
        except Exception as e:
            # 处理异常
            return await self.handle_error(task_id, e)

# 注册适配器
register_adapter("speech_to_text", SpeechToTextAdapter)
register_adapter("text_to_speech", TextToSpeechAdapter) 