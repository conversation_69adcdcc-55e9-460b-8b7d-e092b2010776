"""
术语智能采集数据模型
定义了术语采集任务和结果的数据结构
"""

import uuid
from datetime import datetime
from typing import List, Optional, Dict, Any

from sqlalchemy import Column, Integer, String, Text, Float, DateTime, ForeignKey, Boolean, JSON, func
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import ARRAY, UUID

from utils.db import Base

class TerminologyCollectionTask(Base):
    """
    术语采集任务模型
    """
    __tablename__ = "terminology_collection_tasks"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    task_id = Column(String(36), unique=True, nullable=False, default=lambda: str(uuid.uuid4()))
    name = Column(String(255), nullable=False)
    mode = Column(String(50), nullable=False)  # smart, single, list
    keywords = Column(Text)
    language = Column(String(10), default="zh")
    target_website = Column(Text)
    url_list = Column(JSON)
    status = Column(String(50), nullable=False, default="pending")  # pending, processing, completed, failed
    progress = Column(Integer, default=0)
    celery_task_id = Column(String(255))
    error_message = Column(Text)
    created_at = Column(DateTime, default=datetime.now)
    completed_at = Column(DateTime)
    user_id = Column(Integer, ForeignKey("users.id"))
    
    # 定义关系
    results = relationship("TerminologyCollectionResult", back_populates="task", cascade="all, delete-orphan")
    # 使用字符串引用避免循环导入
    user = relationship("User", back_populates="collection_tasks")
    
    def __repr__(self):
        return f"<TerminologyCollectionTask(task_id='{self.task_id}', name='{self.name}', status='{self.status}')>"
    
    def to_dict(self):
        """
        将对象转换为字典
        """
        return {
            "id": self.id,
            "task_id": self.task_id,
            "name": self.name,
            "mode": self.mode,
            "keywords": self.keywords,
            "language": self.language,
            "target_website": self.target_website,
            "url_list": self.url_list,
            "status": self.status,
            "progress": self.progress,
            "celery_task_id": self.celery_task_id,
            "error_message": self.error_message,
            "created_at": self.created_at,
            "completed_at": self.completed_at,
            "user_id": self.user_id
        }

class TerminologyCollectionResult(Base):
    """
    术语采集结果模型
    """
    __tablename__ = "terminology_collection_results"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    result_id = Column(String(36), unique=True, nullable=False, default=lambda: str(uuid.uuid4()))
    task_id = Column(String(36), ForeignKey("terminology_collection_tasks.task_id", ondelete="CASCADE"), nullable=False)
    term = Column(String(255), nullable=False)
    definition = Column(Text)
    pos = Column(String(50))  # 词性
    confidence = Column(Float)  # 置信度
    source_url = Column(Text)  # 来源URL
    context = Column(Text)  # 上下文
    created_at = Column(DateTime, default=datetime.now)
    
    # 定义关系
    task = relationship("TerminologyCollectionTask", back_populates="results")
    
    def __repr__(self):
        return f"<TerminologyCollectionResult(result_id='{self.result_id}', term='{self.term}', confidence={self.confidence})>"
    
    def to_dict(self):
        """
        将对象转换为字典
        """
        return {
            "id": self.id,
            "result_id": self.result_id,
            "task_id": self.task_id,
            "term": self.term,
            "definition": self.definition,
            "pos": self.pos,
            "confidence": self.confidence,
            "source_url": self.source_url,
            "context": self.context,
            "created_at": self.created_at
        } 