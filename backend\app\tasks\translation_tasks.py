"""
翻译相关的Celery任务
"""

import logging
import asyncio
from typing import Dict, Any
from celery import current_task
from ..core.celery_app import celery_app
from ..services.translation_service import TranslationService
from ..core.database import get_db_manager

logger = logging.getLogger(__name__)


@celery_app.task(bind=True, name="translate_text_async")
def translate_text_async(self, text: str, source_lang: str = "auto", target_lang: str = "zh", 
                        domain: str = "general", style: str = "standard", model: str = None):
    """
    异步文本翻译任务
    """
    task_id = self.request.id
    logger.info(f"开始异步翻译任务: {task_id}")
    
    try:
        # 更新任务状态为进行中
        self.update_state(
            state="PROGRESS",
            meta={
                "progress": 10,
                "message": "正在初始化翻译服务...",
                "task_id": task_id
            }
        )
        
        # 创建翻译服务实例
        translation_service = TranslationService()
        
        # 更新进度
        self.update_state(
            state="PROGRESS",
            meta={
                "progress": 30,
                "message": "正在执行翻译...",
                "task_id": task_id
            }
        )
        
        # 执行翻译 - 需要在事件循环中运行异步函数
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            result = loop.run_until_complete(
                translation_service.translate_text(
                    text=text,
                    source_lang=source_lang,
                    target_lang=target_lang,
                    domain=domain,
                    style=style,
                    model=model
                )
            )
        finally:
            loop.close()
        
        if result["success"]:
            # 更新进度
            self.update_state(
                state="PROGRESS",
                meta={
                    "progress": 80,
                    "message": "正在保存翻译结果...",
                    "task_id": task_id
                }
            )
            
            # 保存翻译历史到数据库
            try:
                db_manager = get_db_manager()

                # 根据数据库类型使用不同的查询语法
                if hasattr(db_manager, 'use_postgres') and db_manager.use_postgres:
                    # PostgreSQL语法
                    db_manager.execute_query(
                        """
                        INSERT INTO translation_history
                        (original_text, translated_text, source_language, target_language, translation_type, task_id)
                        VALUES (%s, %s, %s, %s, %s, %s)
                        """,
                        (text, result["translated_text"], source_lang, target_lang, "text", task_id)
                    )
                else:
                    # SQLite语法
                    db_manager.execute_query(
                        """
                        INSERT INTO translation_history
                        (original_text, translated_text, source_language, target_language, translation_type, task_id)
                        VALUES (?, ?, ?, ?, ?, ?)
                        """,
                        (text, result["translated_text"], source_lang, target_lang, "text", task_id)
                    )
            except Exception as db_error:
                logger.warning(f"保存翻译历史失败: {db_error}")
            
            # 任务完成
            return {
                "success": True,
                "translated_text": result["translated_text"],
                "source_language": result.get("source_language", source_lang),
                "target_language": result.get("target_language", target_lang),
                "model_used": result.get("model_used", model),
                "method": result.get("method", "unknown"),
                "task_id": task_id,
                "progress": 100,
                "message": "翻译完成"
            }
        else:
            # 翻译失败
            logger.error(f"翻译失败: {result.get('error', '未知错误')}")
            raise Exception(result.get("error", "翻译失败"))
            
    except Exception as exc:
        logger.error(f"异步翻译任务失败: {str(exc)}")
        self.update_state(
            state="FAILURE",
            meta={
                "error": str(exc),
                "task_id": task_id,
                "progress": 0
            }
        )
        raise exc


@celery_app.task(bind=True, name="translate_document_async")
def translate_document_async(self, file_path: str, source_lang: str = "auto", target_lang: str = "zh"):
    """
    异步文档翻译任务
    """
    task_id = self.request.id
    logger.info(f"开始异步文档翻译任务: {task_id}")
    
    try:
        # 更新任务状态
        self.update_state(
            state="PROGRESS",
            meta={
                "progress": 10,
                "message": "正在读取文档...",
                "task_id": task_id
            }
        )
        
        # 这里可以添加文档翻译逻辑
        # 目前返回模拟结果
        
        self.update_state(
            state="PROGRESS",
            meta={
                "progress": 50,
                "message": "正在翻译文档内容...",
                "task_id": task_id
            }
        )
        
        # 模拟翻译处理时间
        import time
        time.sleep(2)
        
        self.update_state(
            state="PROGRESS",
            meta={
                "progress": 90,
                "message": "正在生成翻译文档...",
                "task_id": task_id
            }
        )
        
        # 返回结果
        return {
            "success": True,
            "translated_file_path": file_path.replace(".txt", "_translated.txt"),
            "source_language": source_lang,
            "target_language": target_lang,
            "task_id": task_id,
            "progress": 100,
            "message": "文档翻译完成"
        }
        
    except Exception as exc:
        logger.error(f"异步文档翻译任务失败: {str(exc)}")
        self.update_state(
            state="FAILURE",
            meta={
                "error": str(exc),
                "task_id": task_id,
                "progress": 0
            }
        )
        raise exc


@celery_app.task(name="cleanup_old_tasks")
def cleanup_old_tasks():
    """
    清理旧的翻译任务记录
    """
    try:
        db_manager = get_db_manager()
        
        # 删除7天前的翻译历史记录
        db_manager.execute_query(
            """
            DELETE FROM translation_history 
            WHERE created_at < datetime('now', '-7 days')
            """
        )
        
        logger.info("清理旧翻译任务完成")
        return {"success": True, "message": "清理完成"}
        
    except Exception as exc:
        logger.error(f"清理旧任务失败: {str(exc)}")
        return {"success": False, "error": str(exc)}


@celery_app.task(bind=True, name="batch_translate_texts")
def batch_translate_texts(self, texts: list, source_lang: str = "auto", target_lang: str = "zh"):
    """
    批量文本翻译任务
    """
    task_id = self.request.id
    logger.info(f"开始批量翻译任务: {task_id}, 文本数量: {len(texts)}")
    
    try:
        translation_service = TranslationService()
        results = []
        total_texts = len(texts)
        
        # 创建事件循环
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            for i, text in enumerate(texts):
                # 更新进度
                progress = int((i / total_texts) * 90)  # 90%用于翻译，10%用于保存
                self.update_state(
                    state="PROGRESS",
                    meta={
                        "progress": progress,
                        "message": f"正在翻译第 {i+1}/{total_texts} 个文本...",
                        "task_id": task_id
                    }
                )
                
                # 执行翻译
                result = loop.run_until_complete(
                    translation_service.translate_text(
                        text=text,
                        source_lang=source_lang,
                        target_lang=target_lang
                    )
                )
                
                results.append({
                    "original_text": text,
                    "translated_text": result.get("translated_text", ""),
                    "success": result.get("success", False),
                    "error": result.get("error", None)
                })
        finally:
            loop.close()
        
        # 保存结果
        self.update_state(
            state="PROGRESS",
            meta={
                "progress": 95,
                "message": "正在保存批量翻译结果...",
                "task_id": task_id
            }
        )
        
        return {
            "success": True,
            "results": results,
            "total_count": total_texts,
            "success_count": sum(1 for r in results if r["success"]),
            "task_id": task_id,
            "progress": 100,
            "message": "批量翻译完成"
        }
        
    except Exception as exc:
        logger.error(f"批量翻译任务失败: {str(exc)}")
        self.update_state(
            state="FAILURE",
            meta={
                "error": str(exc),
                "task_id": task_id,
                "progress": 0
            }
        )
        raise exc
