"""
数字人生成服务

协调任务管理器和生成流水线，提供统一的数字人生成接口
"""

import os
import logging
import asyncio
import time
from typing import Dict, Any, Optional, List, Callable, Awaitable, Union, Tuple
import uuid
from datetime import datetime

from .task_manager import TaskManager
from .pipeline import GenerationPipeline
from .models import GenerationTask, TaskStatus, GenerationStage

# 设置日志
logger = logging.getLogger(__name__)


class DigitalHumanGenerationService:
    """
    数字人生成服务
    
    提供数字人生成的全流程管理，包括：
    1. 任务创建与管理
    2. 生成流程执行
    3. 状态监控与更新
    4. 资源控制
    """
    
    def __init__(self, 
                 max_concurrent_tasks: int = 5,
                 task_timeout_seconds: int = 1800,
                 mock_mode: bool = False,
                 data_dir: str = "data/digital_humans",
                 enable_monitoring: bool = True):
        """
        初始化数字人生成服务
        
        Args:
            max_concurrent_tasks: 最大并发任务数
            task_timeout_seconds: 任务超时时间（秒）
            mock_mode: 是否使用模拟模式
            data_dir: 数据存储目录
            enable_monitoring: 是否启用监控
        """
        self.max_concurrent_tasks = max_concurrent_tasks
        self.task_timeout_seconds = task_timeout_seconds
        self.mock_mode = mock_mode
        self.data_dir = data_dir
        self.enable_monitoring = enable_monitoring
        
        # 创建任务管理器
        self.task_manager = TaskManager(
            max_concurrent_tasks=max_concurrent_tasks,
            task_timeout_seconds=task_timeout_seconds,
            data_dir=data_dir,
            enable_persistence=True
        )
        
        # 创建生成流水线
        self.pipeline = GenerationPipeline(mock_mode=mock_mode)
        
        # 任务处理线程
        self._worker_task = None
        self._stop_event = asyncio.Event()
        
        # 监控任务
        self._monitor_task = None
        
        # 初始化服务
        self._initialize()
        
        logger.info(f"""初始化数字人生成服务:
        最大并发任务: {max_concurrent_tasks}
        任务超时时间: {task_timeout_seconds}秒
        模拟模式: {mock_mode}
        数据目录: {data_dir}
        启用监控: {enable_monitoring}
        """)
    
    def _initialize(self):
        """初始化服务"""
        # 确保目录存在
        os.makedirs(self.data_dir, exist_ok=True)
        
        # 启动任务清理器
        self.task_manager.start_cleaner()
        
        # 启动工作者线程
        self._start_worker()
        
        # 如果启用监控，启动监控线程
        if self.enable_monitoring:
            self._start_monitoring()
    
    def update_config(self, 
                      max_concurrent_tasks: Optional[int] = None,
                      task_timeout_seconds: Optional[int] = None,
                      mock_mode: Optional[bool] = None,
                      data_dir: Optional[str] = None,
                      enable_monitoring: Optional[bool] = None):
        """
        更新服务配置
        
        Args:
            max_concurrent_tasks: 最大并发任务数
            task_timeout_seconds: 任务超时时间（秒）
            mock_mode: 是否使用模拟模式
            data_dir: 数据存储目录
            enable_monitoring: 是否启用监控
        """
        if max_concurrent_tasks is not None:
            self.max_concurrent_tasks = max_concurrent_tasks
            self.task_manager.max_concurrent_tasks = max_concurrent_tasks
            logger.info(f"更新最大并发任务数: {max_concurrent_tasks}")
        
        if task_timeout_seconds is not None:
            self.task_timeout_seconds = task_timeout_seconds
            self.task_manager.task_timeout_seconds = task_timeout_seconds
            logger.info(f"更新任务超时时间: {task_timeout_seconds}秒")
        
        if mock_mode is not None:
            self.mock_mode = mock_mode
            self.pipeline = GenerationPipeline(mock_mode=mock_mode)
            logger.info(f"更新模拟模式: {mock_mode}")
        
        if data_dir is not None:
            # 数据目录更改需要重新初始化
            self.data_dir = data_dir
            os.makedirs(self.data_dir, exist_ok=True)
            logger.info(f"更新数据目录: {data_dir}")
        
        if enable_monitoring is not None:
            if enable_monitoring and not self.enable_monitoring:
                # 启用监控
                self.enable_monitoring = True
                self._start_monitoring()
                logger.info("启用监控")
            elif not enable_monitoring and self.enable_monitoring:
                # 禁用监控
                self.enable_monitoring = False
                self._stop_monitoring()
                logger.info("禁用监控")
    
    async def create_task(self, 
                         digital_human_id: str,
                         media_file_path: str,
                         user_id: Optional[int] = None,
                         voice_id: Optional[str] = None,
                         welcome_text: Optional[str] = None,
                         priority: bool = False,
                         **kwargs) -> Dict[str, Any]:
        """
        创建数字人生成任务
        
        Args:
            digital_human_id: 数字人ID
            media_file_path: 媒体文件路径
            user_id: 用户ID
            voice_id: 语音ID
            welcome_text: 欢迎文本
            priority: 是否优先级任务
            **kwargs: 其他参数
            
        Returns:
            Dict[str, Any]: 任务创建结果
        """
        # 准备元数据
        metadata = {
            "voice_id": voice_id,
            "welcome_text": welcome_text
        }
        
        # 添加其他参数到元数据
        for key, value in kwargs.items():
            metadata[key] = value
        
        # 创建任务
        try:
            task = await self.task_manager.create_task(
                digital_human_id=digital_human_id,
                media_file_path=media_file_path,
                user_id=user_id,
                metadata=metadata,
                priority=priority,
                initial_progress=5  # 设置初始进度为5%
            )
            
            logger.info(f"创建任务成功: {task.task_id}, 数字人ID: {digital_human_id}, 初始进度: 5%")
            
            return {
                "success": True,
                "task_id": task.task_id,
                "digital_human_id": digital_human_id,
                "status": task.status.value,
                "progress": 5,  # 返回初始进度
                "message": "任务创建成功，等待处理"
            }
            
        except Exception as e:
            logger.error(f"创建任务失败: {str(e)}")
            
            return {
                "success": False,
                "error": f"创建任务失败: {str(e)}",
                "message": "任务创建失败"
            }
    
    async def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """
        获取任务状态
        
        Args:
            task_id: 任务ID
            
        Returns:
            Dict[str, Any]: 任务状态信息
        """
        task = await self.task_manager.get_task(task_id)
        
        if not task:
            return {
                "success": False,
                "task_id": task_id,
                "status": "not_found",
                "message": f"任务 {task_id} 不存在",
                "_not_found": True
            }
        
        # 获取队列位置
        queue_position = None
        if task.status in [TaskStatus.CREATED, TaskStatus.QUEUED, TaskStatus.WAITING]:
            queue_position = self.task_manager.get_queue_position(task_id)
        
        # 转换为字典
        task_dict = task.to_dict()
        
        # 添加队列位置
        if queue_position is not None:
            task_dict["queue_position"] = queue_position
            task_dict["queue_length"] = len(self.task_manager.task_queue)
        
        return {
            "success": True,
            **task_dict
        }
    
    async def cancel_task(self, task_id: str, reason: str = "用户取消") -> Dict[str, Any]:
        """
        取消任务
        
        Args:
            task_id: 任务ID
            reason: 取消原因
            
        Returns:
            Dict[str, Any]: 取消结果
        """
        success = await self.task_manager.cancel_task(task_id, message=reason)
        
        if success:
            return {
                "success": True,
                "task_id": task_id,
                "status": TaskStatus.CANCELLED.value,
                "message": f"任务 {task_id} 已取消: {reason}"
            }
        else:
            task = await self.task_manager.get_task(task_id)
            if not task:
                return {
                    "success": False,
                    "task_id": task_id,
                    "status": "not_found",
                    "message": f"任务 {task_id} 不存在"
                }
            else:
                return {
                    "success": False,
                    "task_id": task_id,
                    "status": task.status.value,
                    "message": f"无法取消任务，当前状态: {task.status.value}"
                }
    
    async def get_all_tasks(self, 
                          status_filter: Optional[List[Union[TaskStatus, str]]] = None,
                          user_id: Optional[int] = None,
                          limit: int = 20,
                          offset: int = 0) -> Dict[str, Any]:
        """
        获取所有任务
        
        Args:
            status_filter: 状态过滤
            user_id: 用户ID过滤
            limit: 每页数量
            offset: 起始偏移
            
        Returns:
            Dict[str, Any]: 任务列表
        """
        # 获取任务列表
        tasks = await self.task_manager.get_all_tasks(
            status_filter=status_filter,
            user_id=user_id,
            limit=limit,
            offset=offset,
            sort_by_date=True
        )
        
        # 获取总数
        total = await self.task_manager.get_task_count(
            status_filter=status_filter,
            user_id=user_id
        )
        
        # 转换为字典列表
        tasks_dict = [task.to_dict() for task in tasks]
        
        return {
            "success": True,
            "total": total,
            "limit": limit,
            "offset": offset,
            "tasks": tasks_dict
        }
    
    async def get_system_status(self) -> Dict[str, Any]:
        """
        获取系统状态
        
        Returns:
            Dict[str, Any]: 系统状态信息
        """
        # 获取任务管理器状态
        status = await self.task_manager.get_system_status()
        
        # 添加服务信息
        status.update({
            "mock_mode": self.mock_mode,
            "task_timeout_seconds": self.task_timeout_seconds,
            "data_dir": self.data_dir,
            "enable_monitoring": self.enable_monitoring,
            "service_start_time": getattr(self, '_start_time', None),
            "service_uptime": time.time() - getattr(self, '_start_time', time.time()) if hasattr(self, '_start_time') else 0
        })
        
        return {
            "success": True,
            "status": status
        }
    
    def _start_worker(self):
        """启动工作者线程"""
        try:
            # 尝试获取当前事件循环
            import asyncio
            try:
                loop = asyncio.get_running_loop()
                # 有运行中的事件循环，创建工作者任务
                self._worker_task = asyncio.create_task(self._worker_loop())
                logger.info("工作者线程开始运行")
            except RuntimeError:
                # 没有运行中的事件循环，记录警告
                logger.warning("没有运行中的事件循环，工作者线程将不会自动运行")
                # 存储工作者协程，以便稍后在有事件循环时运行
                self._worker_coro = self._worker_loop()
        except Exception as e:
            logger.error(f"启动工作者线程时出错: {str(e)}")
    
    def _stop_worker(self):
        """停止工作者线程"""
        if self._worker_task:
            self._worker_task.cancel()
            logger.info("工作者线程被取消")
    
    async def _worker_loop(self):
        """工作者线程循环"""
        logger.info("工作者线程开始运行")
        self._start_time = time.time()
        
        try:
            while not self._stop_event.is_set():
                try:
                    # 获取下一个任务
                    task = await self.task_manager.get_next_task()
                    
                    if task:
                        # 找到任务，处理
                        logger.info(f"开始处理任务: {task.task_id}")
                        
                        try:
                            # 执行任务
                            result = await self.pipeline.process_task(task)
                            
                            # 检查结果
                            if result.get("success", False):
                                logger.info(f"任务 {task.task_id} 处理成功")
                            else:
                                error = result.get("error", "未知错误")
                                logger.error(f"任务 {task.task_id} 处理失败: {error}")
                                
                                # 更新任务状态
                                if task.status != TaskStatus.FAILED:
                                    await self.task_manager.update_task(
                                        task_id=task.task_id,
                                        status=TaskStatus.FAILED,
                                        message=f"处理失败: {error}"
                                    )
                            
                        except Exception as e:
                            logger.error(f"处理任务 {task.task_id} 时发生异常: {str(e)}")
                            
                            # 更新任务状态
                            if task.status != TaskStatus.FAILED:
                                await self.task_manager.update_task(
                                    task_id=task.task_id,
                                    status=TaskStatus.FAILED,
                                    message=f"处理异常: {str(e)}"
                                )
                                
                    else:
                        # 没有任务，等待一段时间
                        await asyncio.sleep(1)
                
                except Exception as e:
                    logger.error(f"工作者线程异常: {str(e)}")
                    await asyncio.sleep(5)  # 出错后等待较长时间
            
            logger.info("工作者线程收到停止信号，退出")
            
        except asyncio.CancelledError:
            logger.info("工作者线程被取消")
        except Exception as e:
            logger.error(f"工作者线程发生未处理异常: {str(e)}")
    
    def _start_monitoring(self):
        """启动监控线程"""
        if not self.enable_monitoring:
            return
        
        try:
            # 尝试获取当前事件循环
            import asyncio
            try:
                loop = asyncio.get_running_loop()
                # 有运行中的事件循环，创建监控任务
                self._monitor_task = asyncio.create_task(self._monitoring_loop())
                logger.info("监控线程开始运行")
            except RuntimeError:
                # 没有运行中的事件循环，记录警告
                logger.warning("没有运行中的事件循环，监控线程将不会自动运行")
                # 存储监控协程，以便稍后在有事件循环时运行
                self._monitor_coro = self._monitoring_loop()
        except Exception as e:
            logger.error(f"启动监控线程时出错: {str(e)}")
    
    def _stop_monitoring(self):
        """停止监控线程"""
        if self._monitor_task:
            self._monitor_task.cancel()
            logger.info("监控线程被取消")
    
    async def _monitoring_loop(self):
        """监控线程循环"""
        logger.info("监控线程开始运行")
        
        try:
            while self.enable_monitoring:
                try:
                    # 获取系统状态
                    status = await self.task_manager.get_system_status()
                    
                    # 检查处理中的任务是否超时
                    # 实际监控逻辑会在这里实现
                    
                    # 输出状态日志
                    if status["queue_length"] > 0 or status["active_tasks"] > 0:
                        logger.info(f"系统状态: 活跃任务 {status['active_tasks']}/{status['max_concurrent_tasks']}, "
                                   f"队列长度 {status['queue_length']}, 系统负载 {status['system_load']:.2f}")
                    
                    # 等待下次检查
                    await asyncio.sleep(60)  # 每分钟检查一次
                    
                except Exception as e:
                    logger.error(f"监控线程异常: {str(e)}")
                    await asyncio.sleep(60)
            
            logger.info("监控线程停止，监控功能已禁用")
            
        except asyncio.CancelledError:
            logger.info("监控线程被取消")
        except Exception as e:
            logger.error(f"监控线程发生未处理异常: {str(e)}")
    
    async def close(self):
        """关闭服务"""
        logger.info("正在关闭数字人生成服务")
        
        # 停止工作者线程
        self._stop_worker()
        
        # 停止监控线程
        self._stop_monitoring()
        
        # 等待工作者线程结束
        if self._worker_task and not self._worker_task.done():
            try:
                await asyncio.wait_for(self._worker_task, timeout=5.0)
            except asyncio.TimeoutError:
                logger.warning("工作者线程未能在5秒内停止，强制取消")
                self._worker_task.cancel()
        
        # 等待监控线程结束
        if self._monitor_task and not self._monitor_task.done():
            try:
                await asyncio.wait_for(self._monitor_task, timeout=2.0)
            except asyncio.TimeoutError:
                logger.warning("监控线程未能在2秒内停止，强制取消")
                self._monitor_task.cancel()
        
        logger.info("数字人生成服务已关闭") 