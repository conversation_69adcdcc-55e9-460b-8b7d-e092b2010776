"""
任务基础类和装饰器
提供统一的任务接口和进度管理
"""

import logging
import traceback
from datetime import datetime
from typing import Dict, Any, Optional, Callable
from celery import Task
from sqlalchemy.orm import Session
from app.core.sqlalchemy_db import get_db

logger = logging.getLogger(__name__)

class BaseTask(Task):
    """
    基础任务类
    提供统一的错误处理、进度更新和数据库访问
    """
    
    def __init__(self):
        self.db_session: Optional[Session] = None
        self.progress_callback: Optional[Callable] = None
    
    def __call__(self, *args, **kwargs):
        """任务执行入口"""
        try:
            # 获取数据库会话
            self.db_session = get_db().__next__()
            
            # 执行任务
            result = self.run(*args, **kwargs)
            
            # 提交事务
            if self.db_session:
                self.db_session.commit()
                
            return result
            
        except Exception as exc:
            # 回滚事务
            if self.db_session:
                self.db_session.rollback()
            
            # 记录错误
            logger.error(f"任务执行失败: {self.name}")
            logger.error(f"错误信息: {str(exc)}")
            logger.error(f"错误堆栈: {traceback.format_exc()}")
            
            # 重新抛出异常
            raise
            
        finally:
            # 关闭数据库会话
            if self.db_session:
                self.db_session.close()
    
    def update_progress(self, task_id: str, progress: int, message: str, status: str = "processing"):
        """
        更新任务进度
        
        Args:
            task_id: 任务ID
            progress: 进度百分比 (0-100)
            message: 进度消息
            status: 任务状态
        """
        try:
            # 更新Celery任务状态
            self.update_state(
                state=status.upper(),
                meta={
                    'progress': progress,
                    'message': message,
                    'timestamp': datetime.utcnow().isoformat()
                }
            )
            
            # 如果有数据库会话，更新数据库中的任务状态
            if self.db_session and task_id:
                self._update_task_in_db(task_id, progress, message, status)
            
            # 调用进度回调
            if self.progress_callback:
                self.progress_callback(progress, message, status)
                
            logger.info(f"任务进度更新: {task_id} - {progress}% - {message}")
            
        except Exception as e:
            logger.error(f"更新任务进度失败: {e}")
    
    def _update_task_in_db(self, task_id: str, progress: int, message: str, status: str):
        """在数据库中更新任务状态"""
        try:
            # 这里需要根据具体的数据库模型来实现
            # 暂时使用通用的SQL更新
            from app.models.digital_human import DigitalHumanGeneration
            
            task_record = self.db_session.query(DigitalHumanGeneration).filter(
                DigitalHumanGeneration.task_id == task_id
            ).first()
            
            if task_record:
                task_record.status = status
                task_record.progress = progress
                task_record.message = message
                task_record.updated_at = datetime.utcnow()
                
        except Exception as e:
            logger.error(f"数据库更新失败: {e}")
    
    def log_start(self, task_name: str, **kwargs):
        """记录任务开始"""
        logger.info(f"开始执行任务: {task_name}")
        logger.info(f"任务参数: {kwargs}")
    
    def log_complete(self, task_name: str, result: Any):
        """记录任务完成"""
        logger.info(f"任务执行完成: {task_name}")
        logger.info(f"任务结果: {result}")
    
    def log_error(self, task_name: str, error: Exception):
        """记录任务错误"""
        logger.error(f"任务执行失败: {task_name}")
        logger.error(f"错误信息: {str(error)}")
        logger.error(f"错误堆栈: {traceback.format_exc()}")

class TaskProgressTracker:
    """任务进度跟踪器"""
    
    def __init__(self, task_instance: BaseTask, task_id: str):
        self.task = task_instance
        self.task_id = task_id
        self.current_progress = 0
    
    def update(self, progress: int, message: str, status: str = "processing"):
        """更新进度"""
        self.current_progress = progress
        self.task.update_progress(self.task_id, progress, message, status)
    
    def increment(self, increment: int, message: str):
        """增量更新进度"""
        self.current_progress = min(100, self.current_progress + increment)
        self.update(self.current_progress, message)
    
    def complete(self, message: str = "任务完成"):
        """标记任务完成"""
        self.update(100, message, "completed")
    
    def fail(self, message: str = "任务失败"):
        """标记任务失败"""
        self.update(self.current_progress, message, "failed")

def task_with_progress(name: str = None):
    """
    带进度跟踪的任务装饰器
    
    Args:
        name: 任务名称
    """
    def decorator(func):
        def wrapper(self, task_id: str, *args, **kwargs):
            # 创建进度跟踪器
            tracker = TaskProgressTracker(self, task_id)
            
            # 记录任务开始
            task_name = name or func.__name__
            self.log_start(task_name, task_id=task_id, args=args, kwargs=kwargs)
            
            try:
                # 执行任务，传入进度跟踪器
                result = func(self, task_id, tracker, *args, **kwargs)
                
                # 记录任务完成
                self.log_complete(task_name, result)
                
                return result
                
            except Exception as e:
                # 记录任务错误
                self.log_error(task_name, e)
                tracker.fail(f"任务执行失败: {str(e)}")
                raise
        
        return wrapper
    return decorator
