"""
存储管理配置
"""

import os
from pathlib import Path
from typing import Dict, Any
from .config import settings

class StorageManager:
    """存储管理器"""
    
    def __init__(self):
        # 使用相对于backend目录的路径
        self.base_path = Path("storage")
        self.setup_storage_structure()
    
    def setup_storage_structure(self):
        """设置存储目录结构"""
        # 创建主要存储目录
        storage_dirs = {
            # 数字人相关
            "digital_humans": "数字人生成的视频文件",
            "avatars": "头像模板文件", 
            "audio": "音频文件",
            
            # 模型相关
            "models": "AI模型文件",
            "models/tts": "语音合成模型",
            "models/digital_human": "数字人模型",
            "models/ai": "AI对话模型",
            
            # 媒体文件
            "media": "媒体文件总目录",
            "media/images": "图片文件",
            "media/videos": "视频文件", 
            "media/audio": "音频文件",
            "media/thumbnails": "缩略图",
            
            # 上传文件
            "uploads": "用户上传文件",
            "uploads/avatars": "上传的头像",
            "uploads/audio": "上传的音频",
            "uploads/documents": "上传的文档",
            
            # 临时文件
            "temp": "临时文件",
            "cache": "缓存文件",
            
            # 结果文件
            "results": "处理结果",
            "results/video": "视频处理结果",
            "results/audio": "音频处理结果",
            "results/translations": "翻译结果",
            "results/transcriptions": "转录结果",
            
            # 翻译相关
            "document_translation": "文档翻译",
            "document_translation/uploads": "翻译上传文件",
            "document_translation/results": "翻译结果文件",
            "document_translation/previews": "翻译预览文件",
            
            # 其他
            "videos": "视频文件",
            "images": "图片文件",
            "translations": "翻译文件"
        }
        
        for dir_path, description in storage_dirs.items():
            full_path = self.base_path / dir_path
            full_path.mkdir(parents=True, exist_ok=True)
            
            # 创建说明文件
            readme_path = full_path / "README.md"
            if not readme_path.exists():
                with open(readme_path, "w", encoding="utf-8") as f:
                    f.write(f"# {dir_path}\n\n{description}\n")
    
    def get_path(self, category: str, filename: str = None) -> Path:
        """获取指定类别的存储路径"""
        category_paths = {
            "digital_humans": "digital_humans",
            "avatars": "avatars",
            "audio": "audio", 
            "models": "models",
            "tts_models": "models/tts",
            "digital_human_models": "models/digital_human",
            "ai_models": "models/ai",
            "uploads": "uploads",
            "upload_avatars": "uploads/avatars",
            "upload_audio": "uploads/audio",
            "temp": "temp",
            "cache": "cache",
            "results": "results",
            "video_results": "results/video",
            "audio_results": "results/audio",
            "media": "media",
            "media_images": "media/images",
            "media_videos": "media/videos",
            "media_audio": "media/audio",
            "thumbnails": "media/thumbnails"
        }
        
        if category not in category_paths:
            raise ValueError(f"未知的存储类别: {category}")
        
        base_dir = self.base_path / category_paths[category]
        
        if filename:
            return base_dir / filename
        else:
            return base_dir
    
    def ensure_dir(self, path: Path) -> Path:
        """确保目录存在"""
        if isinstance(path, str):
            path = Path(path)
        
        path.mkdir(parents=True, exist_ok=True)
        return path
    
    def get_file_info(self, file_path: Path) -> Dict[str, Any]:
        """获取文件信息"""
        if not file_path.exists():
            return {"exists": False}
        
        stat = file_path.stat()
        return {
            "exists": True,
            "size": stat.st_size,
            "created": stat.st_ctime,
            "modified": stat.st_mtime,
            "is_file": file_path.is_file(),
            "is_dir": file_path.is_dir(),
            "extension": file_path.suffix,
            "name": file_path.name,
            "stem": file_path.stem
        }
    
    def clean_temp_files(self, max_age_hours: int = 24):
        """清理临时文件"""
        import time
        
        temp_dir = self.get_path("temp")
        current_time = time.time()
        max_age_seconds = max_age_hours * 3600
        
        for file_path in temp_dir.iterdir():
            if file_path.is_file():
                file_age = current_time - file_path.stat().st_mtime
                if file_age > max_age_seconds:
                    try:
                        file_path.unlink()
                        print(f"删除过期临时文件: {file_path}")
                    except Exception as e:
                        print(f"删除临时文件失败 {file_path}: {e}")
    
    def get_storage_stats(self) -> Dict[str, Any]:
        """获取存储统计信息"""
        stats = {}
        
        for category in ["digital_humans", "avatars", "models", "uploads", "temp", "cache"]:
            try:
                category_path = self.get_path(category)
                total_size = 0
                file_count = 0
                
                for file_path in category_path.rglob("*"):
                    if file_path.is_file():
                        total_size += file_path.stat().st_size
                        file_count += 1
                
                stats[category] = {
                    "total_size": total_size,
                    "file_count": file_count,
                    "path": str(category_path)
                }
            except Exception as e:
                stats[category] = {"error": str(e)}
        
        return stats

# 全局存储管理器实例
storage_manager = StorageManager()

def get_storage_manager() -> StorageManager:
    """获取存储管理器实例"""
    return storage_manager
