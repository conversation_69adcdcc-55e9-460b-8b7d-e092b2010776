"""
数字人服务适配器 - 将数字人生成服务适配到统一的任务处理流程
"""
import logging
import os
from typing import Dict, Any, Optional, List

from services.task_adapter import TaskAdapter, register_adapter
from services.storage_manager import get_storage_manager

# 配置日志
logger = logging.getLogger(__name__)

class DigitalHumanAdapter(TaskAdapter):
    """数字人生成服务适配器"""
    
    async def execute(self, task_id: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行数字人生成任务
        
        Args:
            task_id: 任务ID
            params: 任务参数，包括:
                text: 要说的文本
                digital_human_id: 数字人ID
                voice_id: 声音ID
                language: 语言代码
                emotion: 情感类型(happy, sad, neutral等)
                background: 背景图片或视频路径（可选）
                resolution: 分辨率
                
        Returns:
            执行结果
        """
        try:
            # 更新进度
            await self.update_progress(task_id, 5, "准备数字人生成参数...")
            
            # 提取参数
            text = params.get("text")
            digital_human_id = params.get("digital_human_id")
            voice_id = params.get("voice_id", "default")
            language = params.get("language", "zh-CN")
            emotion = params.get("emotion", "neutral")
            background = params.get("background")
            resolution = params.get("resolution", "720p")
            
            # 验证参数
            if not text:
                return await self.handle_error(task_id, ValueError("缺少必要的文本参数"))
            
            if not digital_human_id:
                return await self.handle_error(task_id, ValueError("缺少必要的数字人ID参数"))
            
            # 检查背景文件
            if background and not os.path.exists(background):
                return await self.handle_error(task_id, FileNotFoundError(f"背景文件不存在: {background}"))
            
            # 更新进度
            await self.update_progress(task_id, 10, f"开始数字人生成，角色ID: {digital_human_id}...")
            
            # 获取存储管理器
            storage_manager = get_storage_manager()
            
            # 准备输出文件路径
            video_output = storage_manager.get_result_path("video", f"{task_id}_digital_human.mp4", task_id)
            audio_output = storage_manager.get_result_path("audio", f"{task_id}_speech.mp3", task_id)
            thumbnail_output = storage_manager.get_result_path("image", f"{task_id}_thumbnail.jpg", task_id)
            
            # 确保目录存在
            os.makedirs(os.path.dirname(video_output), exist_ok=True)
            os.makedirs(os.path.dirname(audio_output), exist_ok=True)
            os.makedirs(os.path.dirname(thumbnail_output), exist_ok=True)
            
            # 创建进度回调函数
            def progress_callback(progress, message=None):
                message = message or f"生成进度: {progress}%"
                return self.update_progress(task_id, 10 + int(progress * 0.8), message)
            
            # 调用数字人生成服务
            logger.info(f"开始执行数字人生成任务: {task_id}, 数字人ID: {digital_human_id}")
            
            # 执行生成
            generation_result = await self.service.generate_digital_human(
                task_id=task_id,
                text=text,
                digital_human_id=digital_human_id,
                voice_id=voice_id,
                language=language,
                emotion=emotion,
                background=background,
                resolution=resolution,
                video_output=video_output,
                audio_output=audio_output,
                thumbnail_output=thumbnail_output,
                progress_callback=progress_callback
            )
            
            # 如果生成失败
            if not generation_result or not generation_result.get("success"):
                error_msg = generation_result.get("error", "数字人生成失败") if generation_result else "数字人生成失败"
                return await self.handle_error(task_id, ValueError(error_msg))
            
            # 更新进度
            await self.update_progress(task_id, 90, "数字人生成完成，处理结果...")
            
            # 构建返回结果
            video_url = generation_result.get("video_url", video_output)
            audio_url = generation_result.get("audio_url", audio_output)
            thumbnail_url = generation_result.get("thumbnail_url", thumbnail_output)
            
            result = {
                "video_url": video_url,
                "video_file": video_output,
                "audio_url": audio_url,
                "audio_file": audio_output,
                "thumbnail_url": thumbnail_url,
                "thumbnail_file": thumbnail_output,
                "text": text,
                "digital_human_id": digital_human_id,
                "duration": generation_result.get("duration", 0),
                "resolution": resolution,
                "output_files": [
                    f for f in [video_output, audio_output, thumbnail_output] 
                    if os.path.exists(f)
                ]
            }
            
            # 更新最终进度
            await self.update_progress(
                task_id, 
                100, 
                "数字人生成任务完成", 
                status="completed",
                result=result
            )
            
            # 返回成功结果
            return {
                "success": True,
                "data": result
            }
            
        except Exception as e:
            # 处理异常
            return await self.handle_error(task_id, e)

# 注册适配器
register_adapter("digital_human", DigitalHumanAdapter) 