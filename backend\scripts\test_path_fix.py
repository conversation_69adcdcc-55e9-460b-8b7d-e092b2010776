#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试SadTalker服务的路径修复功能
"""

import os
import sys
import logging
import tempfile
import shutil

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

# 添加backend目录到系统路径
script_dir = os.path.dirname(os.path.abspath(__file__))
backend_dir = os.path.dirname(script_dir)
if backend_dir not in sys.path:
    sys.path.append(backend_dir)

# 导入SadTalker服务
try:
    from services.sadtalker_service import get_sadtalker_service
    logger.info("成功导入SadTalker服务")
except Exception as e:
    logger.error(f"导入SadTalker服务失败: {e}")
    sys.exit(1)

def create_test_file(base_dir, filename="test.jpg"):
    """创建测试文件"""
    # 确保目录存在
    os.makedirs(base_dir, exist_ok=True)
    
    # 生成测试文件路径
    file_path = os.path.join(base_dir, filename)
    
    # 创建一个简单的测试图像文件
    import numpy as np
    import cv2
    
    # 创建一个黑色图像
    img = np.zeros((100, 100, 3), dtype=np.uint8)
    
    # 绘制一个白色圆形
    cv2.circle(img, (50, 50), 30, (255, 255, 255), -1)
    
    # 保存图像
    cv2.imwrite(file_path, img)
    
    logger.info(f"已创建测试文件: {file_path}")
    return file_path

def test_path_normalization():
    """测试路径规范化功能"""
    logger.info("== 测试路径规范化功能 ==")
    
    # 获取SadTalker服务
    sadtalker = get_sadtalker_service()
    
    # 测试不同的路径格式
    test_paths = [
        r"C:\path\to\file.jpg",                    # Windows路径
        "/path/to/file.jpg",                       # Unix路径
        r"C:\path\\to\file.jpg",                   # 重复的Windows分隔符
        "C:/path/to/file.jpg",                     # 混合分隔符
        r"C:\path/to\file.jpg",                    # 混合分隔符2
        "//path/to/file.jpg",                      # 重复的Unix分隔符
        os.path.join(os.getcwd(), "test.jpg"),     # 当前工作目录
    ]
    
    for path in test_paths:
        normalized = sadtalker._normalize_path(path)
        logger.info(f"原始路径: {path}")
        logger.info(f"规范化后: {normalized}")
        logger.info("-" * 50)
    
    return True

def test_file_verification():
    """测试文件验证功能"""
    logger.info("== 测试文件验证功能 ==")
    
    # 获取SadTalker服务
    sadtalker = get_sadtalker_service()
    
    # 创建测试目录和文件
    temp_dir = tempfile.gettempdir()
    backend_temp = os.path.join(backend_dir, "temp")
    static_temp = os.path.join(backend_dir, "static", "temp")
    
    # 确保目录存在
    os.makedirs(backend_temp, exist_ok=True)
    os.makedirs(static_temp, exist_ok=True)
    
    # 创建一个唯一的测试文件名
    import uuid
    test_filename = f"test_{uuid.uuid4()}.jpg"
    
    # 在backend/temp目录创建测试文件
    original_file = create_test_file(backend_temp, test_filename)
    
    # 测试不同的路径格式
    test_paths = [
        original_file,                                                     # 原始路径
        original_file.replace("\\", "/"),                                  # 替换分隔符
        f"/api/static/temp/{test_filename}",                               # API路径格式
        f"/api/temp/{test_filename}",                                      # 新API路径格式
        os.path.join(static_temp, test_filename),                          # 静态目录路径
        os.path.join(backend_dir, "static", "temp", test_filename),        # 完整静态目录路径
    ]
    
    success_count = 0
    
    for path in test_paths:
        logger.info(f"测试路径: {path}")
        exists, verified_path = sadtalker._verify_file_exists(path)
        
        if exists:
            logger.info(f"✅ 文件找到: {verified_path}")
            success_count += 1
        else:
            logger.error(f"❌ 文件未找到: {path}")
        
        logger.info("-" * 50)
    
    # 清理测试文件
    try:
        os.remove(original_file)
        logger.info(f"已删除测试文件: {original_file}")
    except Exception as e:
        logger.warning(f"删除测试文件失败: {e}")
    
    logger.info(f"测试结果: {success_count}/{len(test_paths)} 成功")
    return success_count > 0

def test_mixed_path_format():
    """测试混合路径格式的文件查找"""
    logger.info("== 测试混合路径格式 ==")
    
    # 获取SadTalker服务
    sadtalker = get_sadtalker_service()
    
    # 创建测试目录
    backend_temp = os.path.join(backend_dir, "temp")
    os.makedirs(backend_temp, exist_ok=True)
    
    # 创建一个唯一的测试文件名
    import uuid
    test_filename = f"test_{uuid.uuid4()}.jpg"
    
    # 在backend/temp目录创建测试文件
    original_file = create_test_file(backend_temp, test_filename)
    
    # 构造一个Windows/Unix混合路径，模拟实际问题
    mixed_path = original_file.replace("\\", "/")
    # 确保有一部分使用反斜杠
    parts = mixed_path.split("/")
    if len(parts) > 3:
        mixed_path = "/".join(parts[:2]) + "\\" + "\\".join(parts[2:])
    
    logger.info(f"原始路径: {original_file}")
    logger.info(f"混合路径: {mixed_path}")
    
    # 测试混合路径
    exists, verified_path = sadtalker._verify_file_exists(mixed_path)
    
    if exists:
        logger.info(f"✅ 混合路径测试成功: {verified_path}")
        result = True
    else:
        logger.error(f"❌ 混合路径测试失败: {mixed_path}")
        result = False
    
    # 清理测试文件
    try:
        os.remove(original_file)
        logger.info(f"已删除测试文件: {original_file}")
    except Exception as e:
        logger.warning(f"删除测试文件失败: {e}")
    
    return result

def test_static_temp_to_temp_mapping():
    """测试从/api/static/temp/到/temp/的映射"""
    logger.info("== 测试路径映射 ==")
    
    # 获取SadTalker服务
    sadtalker = get_sadtalker_service()
    
    # 创建测试目录
    backend_temp = os.path.join(backend_dir, "temp")
    os.makedirs(backend_temp, exist_ok=True)
    
    # 创建一个唯一的测试文件名
    import uuid
    test_filename = f"test_{uuid.uuid4()}.jpg"
    
    # 在backend/temp目录创建测试文件
    original_file = create_test_file(backend_temp, test_filename)
    
    # 构造API路径
    api_path = f"/api/static/temp/{test_filename}"
    
    logger.info(f"原始文件: {original_file}")
    logger.info(f"API路径: {api_path}")
    
    # 测试API路径到实际路径的映射
    exists, verified_path = sadtalker._verify_file_exists(api_path)
    
    if exists:
        logger.info(f"✅ 路径映射测试成功: API路径 -> {verified_path}")
        result = True
    else:
        logger.error(f"❌ 路径映射测试失败: {api_path}")
        result = False
    
    # 清理测试文件
    try:
        os.remove(original_file)
        logger.info(f"已删除测试文件: {original_file}")
    except Exception as e:
        logger.warning(f"删除测试文件失败: {e}")
    
    return result

def main():
    """主函数"""
    logger.info("开始测试SadTalker路径修复功能")
    
    # 测试路径规范化
    path_norm_result = test_path_normalization()
    logger.info(f"路径规范化测试: {'成功' if path_norm_result else '失败'}")
    
    # 测试文件验证
    file_verify_result = test_file_verification()
    logger.info(f"文件验证测试: {'成功' if file_verify_result else '失败'}")
    
    # 测试混合路径格式
    mixed_path_result = test_mixed_path_format()
    logger.info(f"混合路径测试: {'成功' if mixed_path_result else '失败'}")
    
    # 测试路径映射
    path_mapping_result = test_static_temp_to_temp_mapping()
    logger.info(f"路径映射测试: {'成功' if path_mapping_result else '失败'}")
    
    # 总结
    all_passed = path_norm_result and file_verify_result and mixed_path_result and path_mapping_result
    logger.info(f"测试总结: {'所有测试通过!' if all_passed else '有测试失败!'}")
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    sys.exit(main()) 