from sqlalchemy import Column, Integer, String, Text, ForeignKey, DateTime
from sqlalchemy.sql import func
from pydantic import BaseModel
from typing import Optional
from datetime import datetime

from utils.db import Base

class UserOperation(Base):
    """用户操作日志数据库模型"""
    __tablename__ = "user_operations"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    operation_type = Column(String, index=True)  # 操作类型，如 "WAN_VIDEO_GENERATE"
    details = Column(Text)  # JSON字符串，包含操作详情
    status = Column(String)  # 操作状态，如 "success", "failed"
    created_at = Column(DateTime(timezone=True), server_default=func.now())

# Pydantic 模型用于API
class UserOperationBase(BaseModel):
    operation_type: str
    details: Optional[str] = None
    status: Optional[str] = "success"

class UserOperationCreate(UserOperationBase):
    user_id: int

class UserOperationResponse(UserOperationBase):
    id: int
    user_id: int
    created_at: datetime

    class Config:
        from_attributes = True 