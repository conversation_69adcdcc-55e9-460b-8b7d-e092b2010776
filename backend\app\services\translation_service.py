"""
翻译业务服务
"""

import os
import uuid
import json
import asyncio
import httpx
import logging
from typing import Optional, Dict, Any
from fastapi import UploadFile
from ..core.config import settings

# 配置日志
logger = logging.getLogger(__name__)


class TranslationService:
    """翻译服务类"""

    def __init__(self):
        self.storage_path = os.path.join(settings.STORAGE_PATH, "translations")
        os.makedirs(self.storage_path, exist_ok=True)

        # Ollama配置
        self.ollama_base_url = os.environ.get("OLLAMA_API_URL", "http://localhost:11434")
        self.default_model = os.environ.get("OLLAMA_MODEL", "deepseek-r1:8b")

        # 异步任务存储
        self.async_tasks = {}

        logger.info(f"翻译服务初始化完成，Ollama URL: {self.ollama_base_url}")

    async def check_ollama_available(self) -> bool:
        """检查Ollama服务是否可用"""
        try:
            async with httpx.AsyncClient(timeout=5.0) as client:
                response = await client.get(f"{self.ollama_base_url}/api/tags")
                return response.status_code == 200
        except Exception as e:
            logger.warning(f"Ollama服务不可用: {e}")
            return False

    async def get_available_models(self) -> list:
        """获取可用的Ollama模型列表"""
        try:
            async with httpx.AsyncClient(timeout=5.0) as client:
                response = await client.get(f"{self.ollama_base_url}/api/tags")
                if response.status_code == 200:
                    data = response.json()
                    return [model["name"] for model in data.get("models", [])]
        except Exception as e:
            logger.warning(f"获取模型列表失败: {e}")

        return [self.default_model]
    
    async def translate_text(self, text: str, source_lang: str = "auto", target_lang: str = "zh",
                           domain: str = "general", style: str = "standard", model: str = None):
        """文本翻译"""
        try:
            if not text.strip():
                return {"success": False, "error": "文本不能为空"}

            # 如果源语言和目标语言相同，直接返回原文
            if source_lang == target_lang and source_lang != "auto":
                return {
                    "success": True,
                    "translated_text": text,
                    "source_language": source_lang,
                    "target_language": target_lang,
                    "detected_language": source_lang
                }

            # 优先使用Ollama进行翻译
            if await self.check_ollama_available():
                result = await self._translate_with_ollama(text, source_lang, target_lang, domain, style, model)
                if result["success"]:
                    return result
                logger.warning("Ollama翻译失败，尝试备用方案")

            # 备用翻译方案
            return await self._translate_fallback(text, source_lang, target_lang)

        except Exception as e:
            logger.error(f"翻译失败: {e}")
            return {"success": False, "error": str(e)}

    async def _translate_with_ollama(self, text: str, source_lang: str, target_lang: str,
                                   domain: str, style: str, model: str = None):
        """使用Ollama进行翻译"""
        try:
            # 选择模型
            if not model:
                model = self.default_model

            # 构建翻译提示词
            prompt = self._build_translation_prompt(text, source_lang, target_lang, domain, style)

            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(
                    f"{self.ollama_base_url}/api/generate",
                    json={
                        "model": model,
                        "prompt": prompt,
                        "stream": False,
                        "options": {
                            "temperature": 0.3,  # 较低温度确保翻译准确性
                            "top_p": 0.9,
                            "num_predict": 1000
                        }
                    }
                )

                if response.status_code == 200:
                    data = response.json()
                    translated_text = data.get("response", "").strip()

                    # 清理翻译结果
                    translated_text = self._clean_translation_result(translated_text)

                    return {
                        "success": True,
                        "translated_text": translated_text,
                        "source_language": source_lang,
                        "target_language": target_lang,
                        "model_used": model,
                        "method": "ollama"
                    }
                else:
                    logger.error(f"Ollama API错误: {response.status_code}")
                    return {"success": False, "error": f"Ollama API错误: {response.status_code}"}

        except Exception as e:
            logger.error(f"Ollama翻译失败: {e}")
            return {"success": False, "error": str(e)}

    def _build_translation_prompt(self, text: str, source_lang: str, target_lang: str,
                                domain: str, style: str) -> str:
        """构建翻译提示词"""
        # 语言映射
        lang_map = {
            "auto": "自动检测",
            "zh": "中文",
            "en": "英语",
            "ja": "日语",
            "ko": "韩语",
            "fr": "法语",
            "de": "德语",
            "es": "西班牙语",
            "ru": "俄语"
        }

        source_name = lang_map.get(source_lang, source_lang)
        target_name = lang_map.get(target_lang, target_lang)

        # 领域和风格描述
        domain_desc = {
            "general": "通用",
            "technology": "科技",
            "medical": "医学",
            "legal": "法律",
            "finance": "金融"
        }.get(domain, "通用")

        style_desc = {
            "standard": "标准",
            "formal": "正式",
            "casual": "口语化",
            "academic": "学术"
        }.get(style, "标准")

        if source_lang == "auto":
            prompt = f"""请将以下文本翻译成{target_name}。

翻译要求：
1. 保持原文的意思和语调
2. 使用{style_desc}的语言风格
3. 适合{domain_desc}领域的表达习惯
4. 直接输出翻译结果，不要添加任何解释或标记

原文：
{text}

翻译："""
        else:
            prompt = f"""请将以下{source_name}文本翻译成{target_name}。

翻译要求：
1. 保持原文的意思和语调
2. 使用{style_desc}的语言风格
3. 适合{domain_desc}领域的表达习惯
4. 直接输出翻译结果，不要添加任何解释或标记

{source_name}原文：
{text}

{target_name}翻译："""

        return prompt

    def _clean_translation_result(self, text: str) -> str:
        """清理翻译结果"""
        # 移除常见的前缀和后缀
        prefixes_to_remove = [
            "翻译：", "Translation:", "译文：", "结果：", "答案：",
            "翻译结果：", "Translation result:", "Translated text:"
        ]

        for prefix in prefixes_to_remove:
            if text.startswith(prefix):
                text = text[len(prefix):].strip()

        # 移除引号
        if (text.startswith('"') and text.endswith('"')) or (text.startswith("'") and text.endswith("'")):
            text = text[1:-1]

        return text.strip()

    async def _translate_fallback(self, text: str, source_lang: str, target_lang: str):
        """备用翻译方案"""
        # 简单的模拟翻译，实际项目中可以集成其他翻译API
        lang_map = {
            "zh": "中文",
            "en": "English",
            "ja": "日本語",
            "ko": "한국어",
            "fr": "Français",
            "de": "Deutsch",
            "es": "Español",
            "ru": "Русский"
        }

        target_name = lang_map.get(target_lang, target_lang)

        return {
            "success": True,
            "translated_text": f"[{target_name}翻译] {text}",
            "source_language": source_lang,
            "target_language": target_lang,
            "method": "fallback"
        }

    async def translate_text_async(self, task_id: str, text: str, source_lang: str = "auto",
                                 target_lang: str = "zh", domain: str = "general",
                                 style: str = "standard", model: str = None):
        """异步文本翻译 - 使用Celery"""
        try:
            from ..tasks.translation_tasks import translate_text_async

            # 启动Celery任务
            celery_task = translate_text_async.apply_async(
                args=[text, source_lang, target_lang, domain, style, model],
                task_id=task_id
            )

            return {
                "success": True,
                "task_id": task_id,
                "status": "processing",
                "message": "翻译任务已启动"
            }

        except Exception as e:
            logger.error(f"启动异步翻译任务失败: {e}")
            return {"success": False, "error": str(e)}

    async def get_translation_result(self, task_id: str):
        """获取异步翻译结果 - 从Celery获取"""
        try:
            from ..core.celery_app import celery_app

            # 获取Celery任务结果
            celery_task = celery_app.AsyncResult(task_id)

            if celery_task.state == "PENDING":
                return {
                    "success": True,
                    "status": "pending",
                    "progress": 0,
                    "message": "任务等待中"
                }
            elif celery_task.state == "PROGRESS":
                meta = celery_task.info or {}
                return {
                    "success": True,
                    "status": "processing",
                    "progress": meta.get("progress", 0),
                    "message": meta.get("message", "处理中...")
                }
            elif celery_task.state == "SUCCESS":
                result = celery_task.result
                return {
                    "success": True,
                    "status": "completed",
                    "progress": 100,
                    "result": result
                }
            elif celery_task.state == "FAILURE":
                return {
                    "success": False,
                    "status": "failed",
                    "error": str(celery_task.info)
                }
            else:
                return {
                    "success": True,
                    "status": celery_task.state.lower(),
                    "progress": 0
                }

        except Exception as e:
            logger.error(f"获取翻译结果失败: {e}")
            return {"success": False, "error": str(e)}
    
    async def translate_document(self, file: UploadFile, from_lang: str, to_lang: str):
        """文档翻译"""
        try:
            # 保存上传的文件
            filename = f"{uuid.uuid4()}_{file.filename}"
            file_path = os.path.join(self.storage_path, filename)
            
            content = await file.read()
            with open(file_path, "wb") as f:
                f.write(content)
            
            # 这里可以集成实际的文档翻译逻辑
            # 目前返回模拟结果
            
            # 生成翻译后的文件名
            translated_filename = f"translated_{filename}"
            translated_path = os.path.join(self.storage_path, translated_filename)
            
            # 模拟翻译处理
            with open(translated_path, "w", encoding="utf-8") as f:
                f.write(f"这是翻译后的文档内容 ({from_lang} -> {to_lang})")
            
            return {
                "success": True,
                "download_url": f"/storage/translations/{translated_filename}",
                "message": "文档翻译完成"
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def translate_audio(self, file: UploadFile, from_lang: str, to_lang: str):
        """音频翻译"""
        try:
            # 保存上传的音频文件
            filename = f"{uuid.uuid4()}_{file.filename}"
            file_path = os.path.join(self.storage_path, filename)
            
            content = await file.read()
            with open(file_path, "wb") as f:
                f.write(content)
            
            # 这里可以集成实际的音频翻译逻辑
            # 1. 语音识别 (ASR)
            # 2. 文本翻译
            # 3. 语音合成 (TTS)
            
            # 目前返回模拟结果
            translated_audio_filename = f"translated_{filename}"
            
            return {
                "success": True,
                "audio_url": f"/storage/translations/{translated_audio_filename}",
                "transcript": "这是识别出的文本内容",
                "translated_text": "这是翻译后的文本内容",
                "message": "音频翻译完成"
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def get_supported_languages(self):
        """获取支持的语言列表"""
        return {
            "auto": "自动检测",
            "zh": "中文",
            "en": "English",
            "ja": "日本語",
            "ko": "한국어",
            "fr": "Français",
            "de": "Deutsch",
            "es": "Español",
            "ru": "Русский"
        }
