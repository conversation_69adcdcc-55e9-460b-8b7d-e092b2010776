import request from '@/utils/request';

/**
 * 获取仪表盘数据
 * @returns {Promise} 返回仪表盘数据
 */
export function fetchDashboardData() {
  return request({
    url: '/dashboard/overview',
    method: 'get'
  });
}

/**
 * 获取系统状态信息
 * @returns {Promise} 返回系统状态信息
 */
export function fetchSystemStatus() {
  return request({
    url: '/dashboard/system',
    method: 'get'
  });
}

/**
 * 清除系统缓存
 * @returns {Promise} 返回操作结果
 */
export function clearSystemCache() {
  return request({
    url: '/dashboard/clearCache',
    method: 'post'
  });
}

/**
 * 获取访问趋势数据
 * @param {Object} params - 查询参数
 * @param {string} params.type - 数据类型: 'day', 'week', 'month'
 * @param {number} params.days - 天数
 * @returns {Promise} 返回趋势数据
 */
export function fetchVisitTrend(params) {
  return request({
    url: '/dashboard/trend',
    method: 'get',
    params
  });
}

/**
 * 获取来源分布数据
 * @returns {Promise} 返回来源分布数据
 */
export function fetchSourceDistribution() {
  return request({
    url: '/dashboard/source',
    method: 'get'
  });
}

/**
 * 获取最近任务列表
 * @param {Object} params - 查询参数
 * @param {number} params.limit - 条数限制
 * @returns {Promise} 返回最近任务列表
 */
export function fetchRecentTasks(params) {
  return request({
    url: '/dashboard/tasks',
    method: 'get',
    params
  });
}

/**
 * 获取用户地域分布
 * @returns {Promise} 返回用户地域分布数据
 */
export function fetchUserRegion() {
  return request({
    url: '/dashboard/region',
    method: 'get'
  });
}

/**
 * 获取API调用统计
 * @param {Object} params - 查询参数
 * @param {string} params.type - 统计类型: 'hour', 'day', 'week'
 * @returns {Promise} 返回API调用统计数据
 */
export function fetchApiStatistics(params) {
  return request({
    url: '/dashboard/api',
    method: 'get',
    params
  });
}

/**
 * 获取性能监控数据
 * @returns {Promise} 返回性能监控数据
 */
export function fetchPerformanceData() {
  return request({
    url: '/dashboard/performance',
    method: 'get'
  });
}

/**
 * 执行系统备份
 * @param {Object} data - 备份参数
 * @param {string} data.type - 备份类型: 'full', 'config', 'data'
 * @returns {Promise} 返回备份结果
 */
export function executeBackup(data) {
  return request({
    url: '/dashboard/backup',
    method: 'post',
    data
  });
}

/**
 * 获取系统告警信息
 * @returns {Promise} 返回系统告警信息
 */
export function fetchAlerts() {
  return request({
    url: '/dashboard/alerts',
    method: 'get'
  });
}

/**
 * 模拟数据 (开发环境使用)
 * 获取仪表盘数据
 * @returns {Promise} 返回模拟的仪表盘数据
 */
export function getMockDashboardData() {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        data: {
          statistics: [
            {
              value: '5,247',
              change: '12.5%',
              isIncrease: true
            },
            {
              value: '12,683',
              change: '8.2%',
              isIncrease: true
            },
            {
              value: '328',
              change: '3.1%',
              isIncrease: false
            },
            {
              value: '1,562,359',
              change: '25.8%',
              isIncrease: true
            }
          ],
          systemInfo: {
            name: 'AI专业平台管理系统',
            version: 'v1.0.0',
            environment: 'Node.js v16.14.0',
            database: 'MySQL 8.0',
            cache: 'Redis 6.2',
            os: 'Linux CentOS 7.9',
            uptimeSeconds: 2592000 // 30天
          },
          recentTasks: [
            {
              id: 1,
              title: '视频生成任务 #1254',
              status: 1,
              statusText: '完',
              statusColor: '#52c41a',
              type: '视频生成',
              typeColor: '#108ee9',
              time: '2023-04-01 10:23:45'
            },
            {
              id: 2,
              title: '模型训练任务 #986',
              status: 2,
              statusText: '进',
              statusColor: '#1890ff',
              type: '模型训练',
              typeColor: '#722ed1',
              time: '2023-04-01 09:45:30'
            },
            {
              id: 3,
              title: '数据处理任务 #753',
              status: 0,
              statusText: '待',
              statusColor: '#faad14',
              type: '数据处理',
              typeColor: '#fa8c16',
              time: '2023-04-01 08:12:15'
            },
            {
              id: 4,
              title: '批量翻译任务 #562',
              status: 1,
              statusText: '完',
              statusColor: '#52c41a',
              type: '批量翻译',
              typeColor: '#eb2f96',
              time: '2023-03-31 17:50:22'
            },
            {
              id: 5,
              title: '系统备份任务 #421',
              status: 3,
              statusText: '错',
              statusColor: '#f5222d',
              type: '系统任务',
              typeColor: '#389e0d',
              time: '2023-03-31 12:33:18'
            }
          ],
          chartData: {
            dates: ['3月1日', '3月2日', '3月3日', '3月4日', '3月5日', '3月6日', '3月7日', '3月8日', '3月9日', '3月10日', '3月11日', '3月12日', '3月13日', '3月14日'],
            visits: [1200, 1320, 1010, 1340, 900, 1200, 1800, 2200, 1800, 1650, 1300, 1100, 1500, 1750],
            users: [500, 550, 480, 600, 520, 550, 700, 830, 720, 680, 580, 550, 640, 680],
            apiCalls: [12000, 13000, 11000, 12500, 10800, 11500, 14000, 16500, 15000, 14200, 12800, 12000, 13500, 14800]
          },
          pieData: [
            { value: 40, name: '直接访问' },
            { value: 25, name: '搜索引擎' },
            { value: 18, name: '友情链接' },
            { value: 12, name: '社交媒体' },
            { value: 5, name: '其他来源' }
          ]
        }
      });
    }, 800);
  });
}

// 根据环境决定使用真实数据还是模拟数据
export default {
  fetchDashboardData: process.env.NODE_ENV === 'development' ? getMockDashboardData : fetchDashboardData,
  fetchSystemStatus,
  clearSystemCache,
  fetchVisitTrend,
  fetchSourceDistribution,
  fetchRecentTasks,
  fetchUserRegion,
  fetchApiStatistics,
  fetchPerformanceData,
  executeBackup,
  fetchAlerts
}; 