"""
任务监控API
"""

from fastapi import APIRouter, HTTPException, Depends
from sqlalchemy.orm import Session
from typing import List, Optional
import logging
from datetime import datetime, timedelta

from app.core.sqlalchemy_db import get_db
from app.models.digital_human import DigitalHumanGeneration
from app.core.celery_app import celery_app
from app.tasks.digital_human_tasks import get_generation_status

logger = logging.getLogger(__name__)

router = APIRouter()

@router.get("/tasks")
async def list_tasks(
    status: Optional[str] = None,
    limit: int = 20,
    offset: int = 0,
    db: Session = Depends(get_db)
):
    """
    获取任务列表
    """
    try:
        query = db.query(DigitalHumanGeneration)
        
        if status:
            query = query.filter(DigitalHumanGeneration.status == status)
        
        tasks = query.order_by(
            DigitalHumanGeneration.created_at.desc()
        ).offset(offset).limit(limit).all()
        
        total = query.count()
        
        return {
            "success": True,
            "data": [task.to_dict() for task in tasks],
            "total": total,
            "limit": limit,
            "offset": offset
        }
        
    except Exception as e:
        logger.error(f"获取任务列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/tasks/{task_id}")
async def get_task_detail(task_id: str, db: Session = Depends(get_db)):
    """
    获取任务详情
    """
    try:
        # 从数据库获取任务信息
        task = db.query(DigitalHumanGeneration).filter(
            DigitalHumanGeneration.task_id == task_id
        ).first()
        
        if not task:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        # 获取Celery任务状态
        celery_task = None
        if hasattr(task, 'celery_task_id') and task.celery_task_id:
            celery_task = celery_app.AsyncResult(task.celery_task_id)
        
        task_dict = task.to_dict()
        
        # 添加Celery任务信息
        if celery_task:
            task_dict["celery_status"] = celery_task.status
            task_dict["celery_info"] = celery_task.info
        
        return {
            "success": True,
            "data": task_dict
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务详情失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/tasks/{task_id}/cancel")
async def cancel_task(task_id: str, db: Session = Depends(get_db)):
    """
    取消任务
    """
    try:
        # 查找任务
        task = db.query(DigitalHumanGeneration).filter(
            DigitalHumanGeneration.task_id == task_id
        ).first()
        
        if not task:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        if task.status in ["completed", "failed"]:
            raise HTTPException(status_code=400, detail="任务已完成，无法取消")
        
        # 取消Celery任务
        if hasattr(task, 'celery_task_id') and task.celery_task_id:
            celery_app.control.revoke(task.celery_task_id, terminate=True)
        
        # 更新数据库状态
        task.status = "cancelled"
        task.end_time = datetime.utcnow()
        db.commit()
        
        return {
            "success": True,
            "message": "任务已取消"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"取消任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/tasks/{task_id}/logs")
async def get_task_logs(task_id: str, db: Session = Depends(get_db)):
    """
    获取任务日志
    """
    try:
        task = db.query(DigitalHumanGeneration).filter(
            DigitalHumanGeneration.task_id == task_id
        ).first()
        
        if not task:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        # 这里可以从日志文件或日志系统获取详细日志
        logs = []
        
        # 添加基本日志信息
        if task.start_time:
            logs.append({
                "timestamp": task.start_time.isoformat(),
                "level": "INFO",
                "message": "任务开始执行"
            })
        
        if task.status == "completed" and task.end_time:
            logs.append({
                "timestamp": task.end_time.isoformat(),
                "level": "INFO",
                "message": "任务执行完成"
            })
        elif task.status == "failed":
            logs.append({
                "timestamp": task.end_time.isoformat() if task.end_time else datetime.utcnow().isoformat(),
                "level": "ERROR",
                "message": f"任务执行失败: {task.error_message}"
            })
        
        return {
            "success": True,
            "data": {
                "task_id": task_id,
                "logs": logs
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务日志失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/stats")
async def get_task_stats(db: Session = Depends(get_db)):
    """
    获取任务统计信息
    """
    try:
        # 获取各状态任务数量
        total_tasks = db.query(DigitalHumanGeneration).count()
        pending_tasks = db.query(DigitalHumanGeneration).filter(
            DigitalHumanGeneration.status == "pending"
        ).count()
        running_tasks = db.query(DigitalHumanGeneration).filter(
            DigitalHumanGeneration.status == "running"
        ).count()
        completed_tasks = db.query(DigitalHumanGeneration).filter(
            DigitalHumanGeneration.status == "completed"
        ).count()
        failed_tasks = db.query(DigitalHumanGeneration).filter(
            DigitalHumanGeneration.status == "failed"
        ).count()
        
        # 获取今日任务数量
        today = datetime.utcnow().date()
        today_tasks = db.query(DigitalHumanGeneration).filter(
            DigitalHumanGeneration.created_at >= today
        ).count()
        
        # 获取平均处理时间
        completed_with_time = db.query(DigitalHumanGeneration).filter(
            DigitalHumanGeneration.status == "completed",
            DigitalHumanGeneration.processing_time.isnot(None)
        ).all()
        
        avg_processing_time = 0
        if completed_with_time:
            total_time = sum(task.processing_time for task in completed_with_time)
            avg_processing_time = total_time / len(completed_with_time)
        
        # 获取Celery Worker状态
        worker_stats = get_worker_stats()
        
        return {
            "success": True,
            "data": {
                "task_counts": {
                    "total": total_tasks,
                    "pending": pending_tasks,
                    "running": running_tasks,
                    "completed": completed_tasks,
                    "failed": failed_tasks,
                    "today": today_tasks
                },
                "performance": {
                    "avg_processing_time": round(avg_processing_time, 2),
                    "success_rate": round(completed_tasks / max(total_tasks, 1) * 100, 2)
                },
                "workers": worker_stats
            }
        }
        
    except Exception as e:
        logger.error(f"获取任务统计失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/workers")
async def get_worker_status():
    """
    获取Worker状态
    """
    try:
        worker_stats = get_worker_stats()
        
        return {
            "success": True,
            "data": worker_stats
        }
        
    except Exception as e:
        logger.error(f"获取Worker状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

def get_worker_stats():
    """获取Worker统计信息"""
    try:
        # 获取活跃的Worker
        inspect = celery_app.control.inspect()
        
        # 获取Worker状态
        stats = inspect.stats()
        active_tasks = inspect.active()
        registered_tasks = inspect.registered()
        
        workers = []
        if stats:
            for worker_name, worker_stats in stats.items():
                worker_info = {
                    "name": worker_name,
                    "status": "online",
                    "pool": worker_stats.get("pool", {}),
                    "active_tasks": len(active_tasks.get(worker_name, [])) if active_tasks else 0,
                    "registered_tasks": len(registered_tasks.get(worker_name, [])) if registered_tasks else 0,
                    "load_avg": worker_stats.get("rusage", {}).get("utime", 0)
                }
                workers.append(worker_info)
        
        return {
            "total_workers": len(workers),
            "online_workers": len([w for w in workers if w["status"] == "online"]),
            "workers": workers
        }
        
    except Exception as e:
        logger.error(f"获取Worker统计失败: {str(e)}")
        return {
            "total_workers": 0,
            "online_workers": 0,
            "workers": [],
            "error": str(e)
        }
