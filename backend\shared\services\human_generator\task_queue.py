from typing import Dict, Any, Optional, List
import asyncio
import logging
from datetime import datetime
from enum import Enum

logger = logging.getLogger(__name__)

class TaskPriority(Enum):
    """任务优先级"""
    LOW = 0
    NORMAL = 1
    HIGH = 2
    URGENT = 3

class TaskStatus(Enum):
    """任务状态"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class Task:
    """任务类"""
    def __init__(self, task_id: str, data: Dict[str, Any], priority: TaskPriority = TaskPriority.NORMAL):
        self.task_id = task_id
        self.data = data
        self.priority = priority
        self.status = TaskStatus.PENDING
        self.created_at = datetime.now()
        self.updated_at = datetime.now()
        self.result: Optional[Dict[str, Any]] = None
        self.error: Optional[str] = None

class TaskQueue:
    """任务队列管理器"""
    
    def __init__(self):
        self._queues: Dict[TaskPriority, asyncio.Queue[Task]] = {
            TaskPriority.LOW: asyncio.Queue(),
            TaskPriority.NORMAL: asyncio.Queue(),
            TaskPriority.HIGH: asyncio.Queue(),
            TaskPriority.URGENT: asyncio.Queue()
        }
        self._tasks: Dict[str, Task] = {}
        self._lock = asyncio.Lock()
        
    async def add_task(self, task_id: str, data: Dict[str, Any], priority: TaskPriority = TaskPriority.NORMAL) -> None:
        """添加任务到队列"""
        async with self._lock:
            if task_id in self._tasks:
                raise ValueError(f"Task {task_id} already exists")
                
            task = Task(task_id, data, priority)
            self._tasks[task_id] = task
            await self._queues[priority].put(task)
            logger.info(f"Task {task_id} added to queue with priority {priority.name}")
            
    async def get_next_task(self) -> Optional[Task]:
        """获取下一个要处理的任务"""
        # 按优先级从高到低检查队列
        for priority in reversed(list(TaskPriority)):
            if not self._queues[priority].empty():
                task = await self._queues[priority].get()
                task.status = TaskStatus.PROCESSING
                task.updated_at = datetime.now()
                logger.info(f"Task {task.task_id} retrieved from queue with priority {priority.name}")
                return task
        return None
        
    async def update_task_status(self, task_id: str, status: TaskStatus, result: Optional[Dict[str, Any]] = None, error: Optional[str] = None) -> None:
        """更新任务状态"""
        async with self._lock:
            if task_id not in self._tasks:
                raise ValueError(f"Task {task_id} not found")
                
            task = self._tasks[task_id]
            task.status = status
            task.updated_at = datetime.now()
            if result is not None:
                task.result = result
            if error is not None:
                task.error = error
            logger.info(f"Task {task_id} status updated to {status.name}")
            
    async def get_task(self, task_id: str) -> Optional[Task]:
        """获取任务信息"""
        async with self._lock:
            return self._tasks.get(task_id)
            
    async def cancel_task(self, task_id: str) -> None:
        """取消任务"""
        async with self._lock:
            if task_id not in self._tasks:
                raise ValueError(f"Task {task_id} not found")
                
            task = self._tasks[task_id]
            if task.status == TaskStatus.PENDING:
                task.status = TaskStatus.CANCELLED
                task.updated_at = datetime.now()
                logger.info(f"Task {task_id} cancelled")
            else:
                raise ValueError(f"Cannot cancel task {task_id} in status {task.status.name}")
                
    def get_queue_size(self, priority: Optional[TaskPriority] = None) -> Dict[TaskPriority, int]:
        """获取队列大小"""
        if priority:
            return {priority: self._queues[priority].qsize()}
        return {p: self._queues[p].qsize() for p in TaskPriority}
        
    def get_all_tasks(self) -> List[Task]:
        """获取所有任务"""
        return list(self._tasks.values()) 