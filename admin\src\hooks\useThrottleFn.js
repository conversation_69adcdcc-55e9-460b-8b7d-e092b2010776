import { ref, onUnmounted } from 'vue';

/**
 * 节流函数钩子
 * @param {Function} fn - 需要节流的函数
 * @param {Object} options - 配置选项
 * @param {number} options.wait - 节流等待时间(ms)
 * @param {boolean} options.leading - 是否在延迟开始前调用函数
 * @param {boolean} options.trailing - 是否在延迟结束后调用函数
 * @returns {Object} 节流函数控制对象
 */
export default function useThrottleFn(fn, options = {}) {
  if (typeof fn !== 'function') {
    throw new Error('第一个参数必须是函数');
  }
  
  const {
    wait = 300,
    leading = true,
    trailing = true
  } = options;
  
  // 上次执行时间
  let lastExecTime = 0;
  // 上次执行的结果
  let lastResult;
  // 定时器
  let timerId = null;
  // 待执行的参数
  let lastArgs = null;
  // 待执行的上下文
  let lastThis = null;
  // 是否有未执行的尾部调用
  let hasTrailingCall = false;
  
  // 节流后的函数是否正在等待执行
  const isPending = ref(false);
  
  // 获取当前时间
  const now = () => Date.now();
  
  // 执行函数
  const invokeFunc = (time) => {
    const args = lastArgs;
    const thisArg = lastThis;
    
    // 清除状态
    lastArgs = null;
    lastThis = null;
    lastExecTime = time;
    
    // 执行函数
    isPending.value = false;
    lastResult = fn.apply(thisArg, args);
    
    return lastResult;
  };
  
  // 清除定时器
  const clearTimer = () => {
    if (timerId !== null) {
      clearTimeout(timerId);
      timerId = null;
    }
  };
  
  // 执行尾部调用
  const trailingEdge = (time) => {
    timerId = null;
    
    // 如果有尾部调用且允许尾部调用
    if (trailing && hasTrailingCall) {
      hasTrailingCall = false;
      return invokeFunc(time);
    }
    
    lastArgs = null;
    lastThis = null;
    return lastResult;
  };
  
  // 取消节流
  const cancel = () => {
    clearTimer();
    lastExecTime = 0;
    lastArgs = null;
    lastThis = null;
    timerId = null;
    hasTrailingCall = false;
    isPending.value = false;
  };
  
  // 立即执行
  const flush = () => {
    if (timerId !== null) {
      clearTimer();
      return trailingEdge(now());
    }
    return lastResult;
  };
  
  // 节流函数
  const throttled = function(...args) {
    const time = now();
    const context = this;
    const elapsed = time - lastExecTime;
    
    // 保存参数和上下文
    lastArgs = args;
    lastThis = context;
    
    // 是否应该立即执行
    const shouldExec = 
      // 是否是首次调用
      (lastExecTime === 0 && leading) || 
      // 是否已超过等待时间
      elapsed >= wait;
    
    if (shouldExec) {
      // 立即执行
      isPending.value = false;
      return invokeFunc(time);
    }
    
    // 否则，准备尾部调用
    if (trailing && !timerId) {
      isPending.value = true;
      hasTrailingCall = true;
      
      // 设置定时器
      timerId = setTimeout(() => {
        trailingEdge(now());
      }, wait - elapsed);
    }
    
    return lastResult;
  };
  
  // 组件卸载时清除定时器
  onUnmounted(() => {
    cancel();
  });
  
  return {
    run: throttled,
    cancel,
    flush,
    isPending
  };
} 