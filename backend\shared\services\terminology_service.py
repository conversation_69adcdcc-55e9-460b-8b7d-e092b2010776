"""
术语服务模块
整合网络爬虫和术语提取功能，提供完整的术语采集服务
"""

import logging
import os
import sys
import json
import time
from typing import List, Dict, Any, Optional, Tuple, Union
from datetime import datetime

# 导入自定义模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from terminology_extractor import TerminologyExtractor
from backend.services.web_crawler import WebCrawler
from backend.utils.domain_detector import detect_domain, extract_domain_keywords, get_domain_keywords

# 创建日志记录器
logger = logging.getLogger("terminology_service")

class TerminologyService:
    """术语服务类"""
    
    def __init__(self, max_depth: int = 2, max_pages: int = 20, delay: float = 1.0):
        """初始化术语服务"""
        self.extractor = TerminologyExtractor()
        self.crawler = WebCrawler(max_depth=max_depth, max_pages=max_pages, delay=delay)
    
    def collect_from_website(self, url: str, keywords: Optional[List[str]] = None, 
                            language: str = "zh", max_terms: int = 100,
                            required_words: Optional[List[str]] = None, 
                            required_settings: Optional[Dict[str, bool]] = None,
                            restrict_words: Optional[List[str]] = None, 
                            restrict_settings: Optional[Dict[str, bool]] = None,
                            time_range: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
        """
        从单个网站采集术语
        
        Args:
            url: 网站URL
            keywords: 关键词列表，用于过滤页面
            language: 语言代码，默认为中文
            max_terms: 最大返回术语数量
            required_words: 必须包含的词
            required_settings: 必须包含词的设置
            restrict_words: 限制词
            restrict_settings: 限制词的设置
            time_range: 时间范围
            
        Returns:
            dict: 采集结果
        """
        try:
            logger.info(f"[术语服务] 开始从网站采集术语: {url}")
            
            # 爬取网站内容
            crawl_result = self.crawler.crawl(
                url, keywords, 
                required_words, required_settings,
                restrict_words, restrict_settings,
                time_range
            )
            
            if not crawl_result["total_text"]:
                logger.warning(f"[术语服务] 网站内容为空: {url}")
                return {
                    "status": "failed",
                    "message": "网站内容为空",
                    "url": url,
                    "terms": []
                }
            
            # 检测领域
            domain = detect_domain(crawl_result["total_text"])
            logger.info(f"[术语服务] 检测到文本领域: {domain}")
            
            # 提取术语
            terms = self.extract_terms_from_text(crawl_result["total_text"], domain, max_terms)
            
            # 构建结果
            result = {
                "status": "success",
                "url": url,
                "domain": domain,
                "crawled_pages": crawl_result["crawled_pages"],
                "text_length": len(crawl_result["total_text"]),
                "terms": terms,
                "language": language
            }
            
            logger.info(f"[术语服务] 从网站采集术语完成: {url}, 共提取 {len(terms)} 个术语")
            
            return result
        except Exception as e:
            logger.error(f"[术语服务] 从网站采集术语出错: {url}, 错误: {str(e)}")
            return {
                "status": "failed",
                "message": str(e),
                "url": url,
                "terms": []
            }
    
    def collect_from_website_list(self, urls: List[str], keywords: Optional[List[str]] = None, 
                                language: str = "zh", max_terms: int = 100,
                                max_pages_per_site: int = 5, delay: float = 1.0) -> Dict[str, Any]:
        """
        从多个网站采集术语
        
        Args:
            urls: 网站URL列表
            keywords: 关键词列表，用于过滤页面
            language: 语言代码，默认为中文
            max_terms: 最大返回术语数量
            max_pages_per_site: 每个网站最大爬取页面数
            delay: 网站之间的延迟时间（秒）
            
        Returns:
            dict: 采集结果
        """
        try:
            logger.info(f"[术语服务] 开始从 {len(urls)} 个网站采集术语")
            
            # 保存临时爬虫设置
            original_max_pages = self.crawler.max_pages
            
            # 设置每个网站的最大页面数
            self.crawler.max_pages = max_pages_per_site
            
            # 合并所有文本
            all_text = ""
            crawled_sites = 0
            crawled_pages = 0
            
            # 爬取每个网站
            for url in urls:
                try:
                    # 爬取网站
                    crawl_result = self.crawler.crawl(url, keywords)
                    
                    # 合并文本
                    if crawl_result["total_text"]:
                        all_text += crawl_result["total_text"] + " "
                        crawled_sites += 1
                        crawled_pages += crawl_result["crawled_pages"]
                    
                    # 添加延迟
                    time.sleep(delay)
                except Exception as e:
                    logger.error(f"[术语服务] 爬取网站出错: {url}, 错误: {str(e)}")
                    continue
            
            # 恢复原始设置
            self.crawler.max_pages = original_max_pages
            
            # 如果没有爬取到任何内容，返回失败
            if not all_text:
                logger.warning(f"[术语服务] 所有网站内容为空")
                return {
                    "status": "failed",
                    "message": "所有网站内容为空",
                    "urls": urls,
                    "terms": []
                }
            
            # 检测领域
            domain = detect_domain(all_text)
            logger.info(f"[术语服务] 检测到文本领域: {domain}")
            
            # 提取术语
            terms = self.extract_terms_from_text(all_text, domain, max_terms)
            
            # 构建结果
            result = {
                "status": "success",
                "urls": urls,
                "crawled_sites": crawled_sites,
                "crawled_pages": crawled_pages,
                "domain": domain,
                "text_length": len(all_text),
                "terms": terms,
                "language": language
            }
            
            logger.info(f"[术语服务] 从网站列表采集术语完成: 共爬取 {crawled_sites}/{len(urls)} 个网站, {crawled_pages} 个页面, 提取 {len(terms)} 个术语")
            
            return result
        except Exception as e:
            logger.error(f"[术语服务] 从网站列表采集术语出错: {str(e)}")
            return {
                "status": "failed",
                "message": str(e),
                "urls": urls,
                "terms": []
            }
    
    def collect_smart(self, keywords: str, domain: str = None, language: str = "zh", 
                     max_terms: int = 100, time_range: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
        """
        智能采集术语
        
        Args:
            keywords: 关键词字符串，用空格分隔
            domain: 领域，如果为None则自动检测
            language: 语言代码，默认为中文
            max_terms: 最大返回术语数量
            time_range: 时间范围
            
        Returns:
            dict: 采集结果
        """
        try:
            logger.info(f"[术语服务] 开始智能采集术语: 关键词={keywords}, 领域={domain}")
            
            # 将关键词字符串转为列表
            keyword_list = keywords.split()
            
            # 如果提供了领域，获取该领域的关键词
            if domain and domain != "通用":
                domain_keywords = get_domain_keywords(domain)
                # 将领域关键词添加到搜索关键词中
                keyword_list.extend(domain_keywords[:5])  # 只取前5个领域关键词
            
            # 去重
            keyword_list = list(set(keyword_list))
            
            # 构建搜索查询
            search_query = " ".join(keyword_list[:5])  # 只使用前5个关键词进行搜索
            
            # TODO: 实现搜索引擎查询，获取相关网站列表
            # 这里简化处理，假设已经有了一些相关网站
            relevant_sites = [
                "https://example.com/auto",
                "https://example.org/cars",
                "https://example.net/vehicles"
            ]
            
            # 从网站列表采集术语
            result = self.collect_from_website_list(
                relevant_sites,
                keyword_list,
                language,
                max_terms
            )
            
            # 添加搜索信息
            result["search_query"] = search_query
            result["keywords"] = keyword_list
            
            return result
        except Exception as e:
            logger.error(f"[术语服务] 智能采集术语出错: {str(e)}")
            return {
                "status": "failed",
                "message": str(e),
                "keywords": keywords,
                "terms": []
            }
    
    def extract_terms_from_text(self, text: str, domain: str, max_terms: int = 100) -> List[Dict[str, Any]]:
        """
        从文本中提取术语
        
        Args:
            text: 输入文本
            domain: 领域名称
            max_terms: 最大返回术语数量
            
        Returns:
            list: 术语列表
        """
        try:
            logger.info(f"[术语服务] 开始从文本提取术语，领域: {domain}")
            
            # 使用术语提取器提取术语
            results = self.extractor.extract_all_methods(text)
            merged_terms = self.extractor.merge_results(results)
            
            # 限制结果数量
            merged_terms = merged_terms[:max_terms]
            
            # 转换为标准格式
            terms = []
            for term, score in merged_terms:
                # 跳过太短的术语（长度为1的单字术语）
                if len(term) <= 1:
                    continue
                
                # 创建术语对象
                term_obj = {
                    "term": term,
                    "definition": f"{domain}领域术语",
                    "pos": self.guess_pos(term),  # 猜测词性
                    "confidence": float(score),
                    "domain": domain,
                    "context": self.extract_context(text, term)
                }
                
                terms.append(term_obj)
            
            logger.info(f"[术语服务] 从文本提取术语完成，共提取 {len(terms)} 个术语")
            
            return terms
        except Exception as e:
            logger.error(f"[术语服务] 从文本提取术语出错: {str(e)}")
            return []
    
    def guess_pos(self, term: str) -> str:
        """
        猜测术语的词性
        
        Args:
            term: 术语
            
        Returns:
            str: 词性
        """
        # 简单实现，根据术语长度和特征猜测词性
        if len(term) <= 2:
            return "n."  # 名词
        elif term.endswith("化") or term.endswith("性"):
            return "n."  # 名词
        elif term.endswith("器") or term.endswith("机"):
            return "n."  # 名词
        elif term.endswith("法") or term.endswith("术"):
            return "n."  # 名词
        elif "系统" in term or "设备" in term:
            return "n."  # 名词
        else:
            return "n."  # 默认为名词
    
    def extract_context(self, text: str, term: str, context_length: int = 50) -> str:
        """
        从文本中提取术语的上下文
        
        Args:
            text: 输入文本
            term: 术语
            context_length: 上下文长度（前后各多少个字符）
            
        Returns:
            str: 上下文
        """
        try:
            # 查找术语在文本中的位置
            pos = text.find(term)
            if pos == -1:
                return ""
            
            # 计算上下文范围
            start = max(0, pos - context_length)
            end = min(len(text), pos + len(term) + context_length)
            
            # 提取上下文
            context = text[start:end]
            
            # 如果上下文不是完整句子，尝试扩展到句子边界
            if start > 0:
                # 向前找到句子开始
                sentence_start = max(0, context.find("。", 0, context_length) + 1)
                if sentence_start == 1:  # 没找到句号
                    sentence_start = max(0, context.find("，", 0, context_length) + 1)
                context = context[sentence_start:]
            
            if end < len(text):
                # 向后找到句子结束
                sentence_end = context.rfind("。")
                if sentence_end == -1:  # 没找到句号
                    sentence_end = context.rfind("，")
                if sentence_end != -1:
                    context = context[:sentence_end + 1]
            
            return context
        except Exception as e:
            logger.error(f"[术语服务] 提取上下文出错: {str(e)}")
            return "" 