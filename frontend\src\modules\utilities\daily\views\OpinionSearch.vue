<template>
  <div class="opinion-search">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-text">
          <h1>舆情检索</h1>
          <p>多维度舆情数据检索与可视化分析</p>
        </div>
        <div class="header-status">
          <div class="websocket-status">
            <el-tag :type="isWebSocketConnected ? 'success' : 'danger'" size="small">
              {{ isWebSocketConnected ? '实时连接已建立' : '实时连接断开' }}
            </el-tag>
          </div>
          <div v-if="searchProgress.status !== 'idle'" class="search-progress">
            <el-progress
              :percentage="searchProgress.progress"
              :status="searchProgress.status === 'error' ? 'exception' : 'success'"
              :stroke-width="6"
              style="width: 200px;"
            />
            <span class="progress-text">{{ searchProgress.message }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 搜索区域 -->
    <div class="search-section">
      <el-card shadow="never">
        <el-form label-position="top">
          <el-row :gutter="16">
            <el-col :xs="24" :md="12">
              <el-form-item label="检索关键词">
                <el-input
                  v-model="searchQuery"
                  placeholder="输入检索关键词"
                  size="large"
                  @keyup.enter="performSearch"
                >
                  <template #suffix>
                    <el-icon @click="performSearch" style="cursor: pointer;">
                      <Search />
                    </el-icon>
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :md="6">
              <el-form-item label="时间范围">
                <el-date-picker
                  v-model="dateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  size="large"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :md="6">
              <el-form-item label="数据源">
                <el-select
                  v-model="selectedSources"
                  multiple
                  placeholder="选择数据源"
                  size="large"
                  style="width: 100%"
                >
                  <el-option label="新闻媒体" value="news" />
                  <el-option label="社交媒体" value="social" />
                  <el-option label="论坛博客" value="forum" />
                  <el-option label="政府网站" value="government" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="16">
            <el-col :xs="24" :md="8">
              <el-form-item label="地域范围">
                <el-select
                  v-model="selectedRegions"
                  multiple
                  placeholder="选择地域"
                  size="large"
                  style="width: 100%"
                >
                  <el-option label="中国" value="china" />
                  <el-option label="美国" value="usa" />
                  <el-option label="欧洲" value="europe" />
                  <el-option label="亚洲" value="asia" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :md="8">
              <el-form-item label="语言">
                <el-select
                  v-model="selectedLanguage"
                  placeholder="选择语言"
                  size="large"
                  style="width: 100%"
                >
                  <el-option label="全部语言" value="all" />
                  <el-option label="中文" value="zh" />
                  <el-option label="英文" value="en" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :md="8">
              <el-form-item label="排序方式">
                <el-select
                  v-model="sortBy"
                  placeholder="选择排序"
                  size="large"
                  style="width: 100%"
                >
                  <el-option label="相关度" value="relevance" />
                  <el-option label="时间" value="time" />
                  <el-option label="影响力" value="influence" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <div class="search-actions">
            <el-button type="primary" size="large" @click="performSearch" :loading="searching">
              <el-icon><Search /></el-icon>
              开始检索
            </el-button>
            <el-button size="large" @click="resetSearch">
              重置
            </el-button>
            <el-button size="large" @click="saveSearchScheme">
              保存方案
            </el-button>
          </div>
        </el-form>
      </el-card>
    </div>

    <!-- 结果展示区域 -->
    <div v-if="hasSearched" class="results-section">
      <!-- 可视化分析 -->
      <el-card shadow="never" class="analysis-card">
        <template #header>
          <span>可视化分析</span>
        </template>
        <el-tabs v-model="activeAnalysisTab">
          <el-tab-pane label="总览" name="overview">
            <div class="overview-stats">
              <el-row :gutter="16">
                <el-col :xs="12" :sm="6">
                  <el-statistic
                    title="总结果数"
                    :value="searchResults.total"
                    :value-style="{ color: '#67c23a' }"
                  />
                </el-col>
                <el-col :xs="12" :sm="6">
                  <el-statistic
                    title="相关站点"
                    :value="searchResults.sites"
                    :value-style="{ color: '#409eff' }"
                  />
                </el-col>
                <el-col :xs="12" :sm="6">
                  <el-statistic
                    title="时间跨度"
                    :value="searchResults.timeSpan"
                    suffix="天"
                    :value-style="{ color: '#909399' }"
                  />
                </el-col>
                <el-col :xs="12" :sm="6">
                  <el-statistic
                    title="平均情感"
                    :value="searchResults.avgSentiment"
                    :precision="1"
                    :value-style="{ color: '#e6a23c' }"
                  />
                </el-col>
              </el-row>
            </div>
          </el-tab-pane>

          <el-tab-pane label="地域分析" name="region">
            <OpinionAnalysisChart
              type="map"
              :data="analysisData.regionData"
              :loading="searching"
              :height="400"
              @chart-click="handleChartClick"
            />
          </el-tab-pane>

          <el-tab-pane label="趋势分析" name="trend">
            <OpinionAnalysisChart
              type="trend"
              :data="analysisData.trendData"
              :loading="searching"
              :height="400"
              @chart-click="handleChartClick"
            />
          </el-tab-pane>

          <el-tab-pane label="情感分析" name="sentiment">
            <OpinionAnalysisChart
              type="sentiment"
              :data="analysisData.sentimentData"
              :loading="searching"
              :height="400"
              @chart-click="handleChartClick"
            />
          </el-tab-pane>

          <el-tab-pane label="来源分析" name="source">
            <OpinionAnalysisChart
              type="pie"
              :data="analysisData.sourceData"
              :loading="searching"
              :height="400"
              @chart-click="handleChartClick"
            />
          </el-tab-pane>

          <el-tab-pane label="重要舆情" name="hotspot">
            <div class="hotspot-list">
              <div v-for="item in hotspotNews" :key="item.id" class="hotspot-item">
                <div class="hotspot-header">
                  <h4>
                    <a :href="item.url" target="_blank" class="hotspot-title">{{ item.title }}</a>
                    <el-tag :type="getSentimentType(item.sentiment)" size="small" style="margin-left: 8px;">
                      {{ getSentimentText(item.sentiment) }}
                    </el-tag>
                  </h4>
                  <div class="hotspot-actions">
                    <span><el-icon><View /></el-icon> {{ item.views }}</span>
                    <span><el-icon><ChatLineRound /></el-icon> {{ item.comments }}</span>
                    <span><el-icon><Share /></el-icon> {{ item.shares }}</span>
                  </div>
                </div>
                <div class="hotspot-meta">
                  <span>{{ item.source }}</span>
                  <span>{{ item.publishTime }}</span>
                  <span>{{ item.region }}</span>
                </div>
                <div class="hotspot-content">{{ item.summary }}</div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-card>

      <!-- 检索结果列表 -->
      <el-card shadow="never" class="results-card">
        <template #header>
          <span>检索结果</span>
        </template>
        <div class="results-toolbar">
          <div class="toolbar-left">
            <span>共找到 {{ searchResults.total }} 条结果</span>
          </div>
          <div class="toolbar-right">
            <el-switch
              v-model="showChineseTitle"
              active-text="中文标题"
              inactive-text="原文标题"
            />
            <el-button @click="exportResults" style="margin-left: 12px;">
              <el-icon><Download /></el-icon>
              导出结果
            </el-button>
          </div>
        </div>

        <!-- 使用虚拟滚动优化大数据展示 -->
        <VirtualList
          :items="searchResultsList"
          :item-height="120"
          :height="600"
          :buffer="5"
          item-key="id"
          :loading="searching"
          :has-more="pagination.current * pagination.pageSize < pagination.total"
          @load-more="handleLoadMore"
        >
          <template #default="{ item }">
            <div class="result-item">
              <div class="result-header">
                <h4>
                  <a :href="item.url" target="_blank" class="result-title">
                    {{ showChineseTitle ? (item.titleCn || item.title) : item.title }}
                  </a>
                  <el-tag :type="getSentimentType(item.sentiment)" size="small" style="margin-left: 8px;">
                    {{ getSentimentText(item.sentiment) }}
                  </el-tag>
                </h4>
                <div class="result-actions">
                  <span><el-icon><Calendar /></el-icon> {{ item.publishTime }}</span>
                  <span><el-icon><Location /></el-icon> {{ item.source }}</span>
                  <span><el-icon><View /></el-icon> {{ item.views || 0 }}</span>
                </div>
              </div>
              <div class="result-meta">
                <span>{{ item.source }}</span>
                <span>{{ item.region }}</span>
                <span>{{ item.language }}</span>
              </div>
              <div class="result-content">{{ item.summary }}</div>
            </div>
          </template>
        </VirtualList>

        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="pagination.current"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, computed, onMounted, onUnmounted } from 'vue';
import { ElMessage, ElNotification, ElMessageBox } from 'element-plus';
import dayjs from 'dayjs';
import {
  Search,
  Download,
  Calendar,
  Location,
  DataLine,
  DataAnalysis,
  Star,
  View,
  ChatLineRound,
  Share
} from '@element-plus/icons-vue';
import OpinionAnalysisChart from '@/components/charts/OpinionAnalysisChart.vue';
import VirtualList from '@/components/common/VirtualList.vue';
import opinionAnalysisApi from '@/services/opinion-analysis.js';
import websocketService from '@/services/websocket.js';

export default defineComponent({
  name: 'OpinionSearch',
  components: {
    Search,
    Download,
    Calendar,
    Location,
    DataLine,
    DataAnalysis,
    Star,
    View,
    ChatLineRound,
    Share,
    OpinionAnalysisChart,
    VirtualList
  },
  setup() {
    const searching = ref(false);
    const hasSearched = ref(false);
    const showChineseTitle = ref(false);
    const activeAnalysisTab = ref('overview');

    // 搜索参数
    const searchQuery = ref('');
    const dateRange = ref([dayjs().subtract(30, 'day'), dayjs()]);
    const selectedSources = ref(['news', 'social']);
    const selectedRegions = ref(['china']);
    const selectedLanguage = ref('all');
    const sortBy = ref('relevance');

    // 搜索结果
    const searchResults = reactive({
      total: 0,
      sites: 0,
      timeSpan: 0,
      avgSentiment: 0
    });

    const searchResultsList = ref([]);
    const hotspotNews = ref([]);

    // 分析数据
    const analysisData = reactive({
      regionData: {
        data: [],
        max: 100
      },
      trendData: {
        dates: [],
        counts: [],
        positive: [],
        negative: []
      },
      sentimentData: {
        positive: 0,
        neutral: 0,
        negative: 0
      },
      sourceData: {
        title: '来源分布',
        name: '数量',
        data: []
      }
    });

    // 分页配置
    const pagination = reactive({
      current: 1,
      pageSize: 10,
      total: 0
    });

    // 模拟数据
    const mockResults = [
      {
        id: 1,
        title: 'China-US Trade Relations Show Signs of Improvement',
        titleCn: '中美贸易关系显现改善迹象',
        url: 'https://example.com/news/1',
        source: 'Global Times',
        region: '中国',
        language: '英文',
        publishTime: '2024-01-07 10:30',
        sentiment: 'positive',
        views: 1250,
        summary: '最新报道显示，中美两国在贸易谈判中取得了积极进展...'
      },
      {
        id: 2,
        title: '东南亚经济一体化进程加速',
        titleCn: '东南亚经济一体化进程加速',
        url: 'https://example.com/news/2',
        source: '人民日报',
        region: '中国',
        language: '中文',
        publishTime: '2024-01-07 09:15',
        sentiment: 'neutral',
        views: 856,
        summary: '东南亚国家联盟成员国在经济合作方面达成新的共识...'
      }
    ];

    const mockHotspots = [
      {
        id: 1,
        title: '中美高层会晤释放积极信号',
        url: 'https://example.com/hotspot/1',
        source: '新华社',
        region: '中国',
        publishTime: '2024-01-07 14:30',
        sentiment: 'positive',
        views: 5200,
        comments: 128,
        shares: 89,
        summary: '中美两国高层官员在华盛顿举行会晤，双方就贸易、科技等议题进行了深入交流...'
      }
    ];

    // 方法
    const performSearch = async () => {
      if (!searchQuery.value.trim()) {
        ElMessage.warning('请输入检索关键词');
        return;
      }

      searching.value = true;
      try {
        // 调用真实API
        const searchData = {
          query: searchQuery.value,
          sources: selectedSources.value,
          regions: selectedRegions.value,
          language: selectedLanguage.value,
          date_range: dateRange.value ? {
            start: dayjs(dateRange.value[0]).format('YYYY-MM-DD'),
            end: dayjs(dateRange.value[1]).format('YYYY-MM-DD')
          } : {},
          sort_by: sortBy.value,
          page: pagination.current,
          page_size: pagination.pageSize
        };

        const response = await opinionAnalysisApi.search.searchOpinions(searchData);

        if (response.success) {
          const { data } = response;

          // 更新搜索结果
          Object.assign(searchResults, {
            total: data.total || 0,
            sites: data.analysis?.sites || 0,
            timeSpan: data.analysis?.time_span || 0,
            avgSentiment: data.analysis?.avg_sentiment || 0
          });

          searchResultsList.value = data.results || [];
          hotspotNews.value = data.results?.slice(0, 5) || [];

          // 更新分析数据
          updateAnalysisData(data.analysis);

          pagination.total = data.total || 0;
          hasSearched.value = true;

          ElMessage.success('检索完成！');
        } else {
          throw new Error(response.message || '检索失败');
        }
      } catch (error) {
        console.error('Search error:', error);
        ElMessage.error('检索失败，请重试');
      } finally {
        searching.value = false;
      }
    };

    const resetSearch = () => {
      searchQuery.value = '';
      dateRange.value = [dayjs().subtract(30, 'day'), dayjs()];
      selectedSources.value = ['news', 'social'];
      selectedRegions.value = ['china'];
      selectedLanguage.value = 'all';
      sortBy.value = 'relevance';
      hasSearched.value = false;
    };

    const updateAnalysisData = (analysis) => {
      if (!analysis) return;

      // 更新地域数据
      if (analysis.region_distribution) {
        analysisData.regionData.data = Object.entries(analysis.region_distribution).map(([name, value]) => ({
          name,
          value
        }));
        analysisData.regionData.max = Math.max(...Object.values(analysis.region_distribution));
      }

      // 更新趋势数据
      if (analysis.trend_data) {
        analysisData.trendData.dates = analysis.trend_data.map(item => item.date);
        analysisData.trendData.counts = analysis.trend_data.map(item => item.count);
        analysisData.trendData.positive = analysis.trend_data.map(item => item.positive || 0);
        analysisData.trendData.negative = analysis.trend_data.map(item => item.negative || 0);
      }

      // 更新情感数据
      if (analysis.sentiment_distribution) {
        Object.assign(analysisData.sentimentData, analysis.sentiment_distribution);
      }

      // 更新来源数据
      if (analysis.source_distribution) {
        analysisData.sourceData.data = Object.entries(analysis.source_distribution).map(([name, value]) => ({
          name,
          value
        }));
      }
    };

    const saveSearchScheme = async () => {
      if (!searchQuery.value.trim()) {
        ElMessage.warning('请先输入检索关键词');
        return;
      }

      try {
        const schemeData = {
          name: `${searchQuery.value} - ${dayjs().format('YYYY-MM-DD HH:mm')}`,
          description: `检索关键词: ${searchQuery.value}`,
          keywords: [searchQuery.value],
          sources: selectedSources.value,
          regions: selectedRegions.value,
          language: selectedLanguage.value
        };

        await opinionAnalysisApi.searchSchemes.createSearchScheme(schemeData);
        ElMessage.success('检索方案已保存');
      } catch (error) {
        console.error('Save scheme error:', error);
        ElMessage.error('保存失败，请重试');
      }
    };

    const exportResults = () => {
      if (!searchResults.value.length) {
        ElMessage.warning('没有可导出的搜索结果');
        return;
      }

      ElMessageBox.prompt('请选择导出格式', '导出搜索结果', {
        confirmButtonText: '导出',
        cancelButtonText: '取消',
        inputType: 'select',
        inputOptions: [
          { value: 'excel', label: 'Excel格式 (.xlsx)' },
          { value: 'csv', label: 'CSV格式 (.csv)' },
          { value: 'json', label: 'JSON格式 (.json)' },
          { value: 'pdf', label: 'PDF报告 (.pdf)' }
        ],
        inputValue: 'excel'
      }).then(({ value: format }) => {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `opinion-search-results-${timestamp}`;

        switch (format) {
          case 'excel':
            exportAsExcel(filename);
            break;
          case 'csv':
            exportAsCSV(filename);
            break;
          case 'json':
            exportAsJSON(filename);
            break;
          case 'pdf':
            exportAsPDF(filename);
            break;
        }
      }).catch(() => {
        // 用户取消
      });
    };

    const getSentimentType = (sentiment) => {
      const types = {
        positive: 'success',
        neutral: 'info',
        negative: 'danger'
      };
      return types[sentiment] || '';
    };

    const getSentimentText = (sentiment) => {
      const texts = {
        positive: '正面',
        neutral: '中性',
        negative: '负面'
      };
      return texts[sentiment] || '未知';
    };

    // 导出方法
    const exportAsExcel = (filename) => {
      const headers = ['标题', '来源', '发布时间', '情感倾向', '阅读量', '评论数', '分享数', '摘要'];
      const data = searchResults.value.map(item => [
        item.title,
        item.source,
        item.publishTime,
        getSentimentText(item.sentiment),
        item.views,
        item.comments,
        item.shares,
        item.summary
      ]);

      const csvContent = [headers, ...data].map(row =>
        row.map(cell => `"${cell}"`).join(',')
      ).join('\n');

      downloadFile(csvContent, `${filename}.csv`, 'text/csv');
      ElMessage.success('Excel文件导出成功');
    };

    const exportAsCSV = (filename) => {
      const headers = ['标题', '来源', '发布时间', '情感倾向', '阅读量', '评论数', '分享数', '摘要'];
      const data = searchResults.value.map(item => [
        item.title,
        item.source,
        item.publishTime,
        getSentimentText(item.sentiment),
        item.views,
        item.comments,
        item.shares,
        item.summary
      ]);

      const csvContent = [headers, ...data].map(row =>
        row.map(cell => `"${cell}"`).join(',')
      ).join('\n');

      downloadFile(csvContent, `${filename}.csv`, 'text/csv');
      ElMessage.success('CSV文件导出成功');
    };

    const exportAsJSON = (filename) => {
      const exportData = {
        exportTime: new Date().toISOString(),
        searchQuery: searchForm.query,
        totalResults: searchResults.value.length,
        results: searchResults.value
      };

      const jsonContent = JSON.stringify(exportData, null, 2);
      downloadFile(jsonContent, `${filename}.json`, 'application/json');
      ElMessage.success('JSON文件导出成功');
    };

    const exportAsPDF = (filename) => {
      const htmlContent = generatePDFContent();

      const printWindow = window.open('', '_blank');
      printWindow.document.write(htmlContent);
      printWindow.document.close();
      printWindow.print();

      ElMessage.success('PDF报告已生成，请使用浏览器打印功能保存');
    };

    const generatePDFContent = () => {
      return `
        <!DOCTYPE html>
        <html>
        <head>
          <title>舆情检索报告</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .header { text-align: center; margin-bottom: 30px; }
            .summary { background: #f5f5f5; padding: 15px; margin-bottom: 20px; }
            .result-item { border-bottom: 1px solid #eee; padding: 15px 0; }
            .title { font-weight: bold; color: #333; }
            .meta { color: #666; font-size: 12px; margin: 5px 0; }
            .summary-text { margin-top: 10px; }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>舆情检索报告</h1>
            <p>生成时间: ${new Date().toLocaleString()}</p>
            <p>检索关键词: ${searchForm.query}</p>
          </div>

          <div class="summary">
            <h3>检索摘要</h3>
            <p>共检索到 ${searchResults.value.length} 条相关信息</p>
            <p>正面情感: ${searchResults.value.filter(r => r.sentiment === 'positive').length} 条</p>
            <p>负面情感: ${searchResults.value.filter(r => r.sentiment === 'negative').length} 条</p>
            <p>中性情感: ${searchResults.value.filter(r => r.sentiment === 'neutral').length} 条</p>
          </div>

          <div class="results">
            <h3>详细结果</h3>
            ${searchResults.value.map((item, index) => `
              <div class="result-item">
                <div class="title">${index + 1}. ${item.title}</div>
                <div class="meta">
                  来源: ${item.source} |
                  发布时间: ${item.publishTime} |
                  情感倾向: ${getSentimentText(item.sentiment)} |
                  阅读: ${item.views} | 评论: ${item.comments} | 分享: ${item.shares}
                </div>
                <div class="summary-text">${item.summary}</div>
              </div>
            `).join('')}
          </div>
        </body>
        </html>
      `;
    };

    const downloadFile = (content, filename, mimeType) => {
      const blob = new Blob([content], { type: mimeType });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      a.click();
      URL.revokeObjectURL(url);
    };

    const handleChartClick = (params) => {
      console.log('Chart clicked:', params);
    };

    const handleSizeChange = (size) => {
      pagination.pageSize = size;
      performSearch();
    };

    const handleCurrentChange = (page) => {
      pagination.current = page;
      performSearch();
    };

    const handleLoadMore = () => {
      if (pagination.current * pagination.pageSize < pagination.total && !searching.value) {
        pagination.current += 1;
        performSearch();
      }
    };

    // WebSocket相关
    const isWebSocketConnected = ref(false);
    const searchProgress = ref({
      status: 'idle', // idle, searching, completed, error
      message: '',
      progress: 0
    });

    // WebSocket事件处理
    const handleSearchUpdate = (data) => {
      searchProgress.value = {
        status: data.status,
        message: data.message,
        progress: data.progress || 0
      };

      if (data.status === 'completed') {
        ElNotification({
          title: '检索完成',
          message: data.message,
          type: 'success',
          duration: 3000
        });
      } else if (data.status === 'error') {
        ElNotification({
          title: '检索失败',
          message: data.message,
          type: 'error',
          duration: 5000
        });
      }
    };

    const handleReportReady = (data) => {
      ElNotification({
        title: '简报生成完成',
        message: `简报"${data.title}"已生成完成`,
        type: 'success',
        duration: 5000
      });
    };

    const handleSystemNotification = (data) => {
      ElNotification({
        title: '系统通知',
        message: data.message,
        type: 'info',
        duration: 4000
      });
    };

    // 初始化数据
    onMounted(() => {
      // 设置默认日期范围
      dateRange.value = [dayjs().subtract(30, 'day').toDate(), dayjs().toDate()];

      // 连接WebSocket
      const userId = '1'; // TODO: 从用户状态获取真实用户ID
      websocketService.connect(userId);

      // 监听WebSocket事件
      websocketService.on('open', () => {
        isWebSocketConnected.value = true;
        console.log('WebSocket连接成功');
      });

      websocketService.on('close', () => {
        isWebSocketConnected.value = false;
        console.log('WebSocket连接断开');
      });

      websocketService.on('search_update', handleSearchUpdate);
      websocketService.on('report_ready', handleReportReady);
      websocketService.on('system_notification', handleSystemNotification);
    });

    onUnmounted(() => {
      // 清理WebSocket连接
      websocketService.off('search_update', handleSearchUpdate);
      websocketService.off('report_ready', handleReportReady);
      websocketService.off('system_notification', handleSystemNotification);
      websocketService.disconnect();
    });

    return {
      searching,
      hasSearched,
      showChineseTitle,
      activeAnalysisTab,
      searchQuery,
      dateRange,
      selectedSources,
      selectedRegions,
      selectedLanguage,
      sortBy,
      searchResults,
      searchResultsList,
      hotspotNews,
      analysisData,
      pagination,
      isWebSocketConnected,
      searchProgress,
      performSearch,
      resetSearch,
      saveSearchScheme,
      exportResults,
      getSentimentType,
      getSentimentText,
      handleChartClick,
      handleSizeChange,
      handleCurrentChange,
      handleLoadMore
    };
  }
});
</script>

<style scoped>
.opinion-search {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 32px;
  margin-bottom: 24px;
  color: white;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-text {
  text-align: left;
}

.header-status {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 12px;
}

.websocket-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.search-progress {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8px;
}

.progress-text {
  font-size: 12px;
  opacity: 0.9;
}

.page-header h1 {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 8px;
  color: white;
}

.page-header p {
  font-size: 1.1rem;
  opacity: 0.9;
  margin: 0;
}

.search-section {
  margin-bottom: 24px;
}

.search-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  margin-top: 16px;
}

.results-section {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.analysis-card {
  margin-bottom: 24px;
}

.overview-stats {
  padding: 16px 0;
}

.hotspot-list {
  max-height: 400px;
  overflow-y: auto;
}

.hotspot-item {
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
}

.hotspot-item:last-child {
  border-bottom: none;
}

.hotspot-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.hotspot-title {
  color: #409eff;
  text-decoration: none;
  font-weight: 500;
}

.hotspot-title:hover {
  color: #66b1ff;
}

.hotspot-actions {
  display: flex;
  gap: 16px;
  color: #909399;
  font-size: 12px;
}

.hotspot-actions span {
  display: flex;
  align-items: center;
  gap: 4px;
}

.hotspot-meta {
  display: flex;
  gap: 16px;
  color: #909399;
  font-size: 12px;
  margin-bottom: 8px;
}

.hotspot-content {
  color: #606266;
  line-height: 1.6;
}

.results-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.toolbar-left {
  color: #606266;
}

.toolbar-right {
  display: flex;
  align-items: center;
}

.results-list {
  margin-bottom: 24px;
}

.result-item {
  padding: 16px 0;
  border-bottom: 1px solid #ebeef5;
}

.result-item:last-child {
  border-bottom: none;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.result-title {
  color: #409eff;
  text-decoration: none;
  font-weight: 500;
}

.result-title:hover {
  color: #66b1ff;
}

.result-actions {
  display: flex;
  gap: 16px;
  color: #909399;
  font-size: 12px;
}

.result-actions span {
  display: flex;
  align-items: center;
  gap: 4px;
}

.result-meta {
  display: flex;
  gap: 16px;
  color: #909399;
  font-size: 12px;
  margin-bottom: 8px;
}

.result-content {
  color: #606266;
  line-height: 1.6;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 24px;
}

@media (max-width: 768px) {
  .opinion-search {
    padding: 16px;
  }

  .page-header {
    padding: 24px 16px;
  }

  .search-actions {
    flex-direction: column;
    align-items: center;
    gap: 12px;
  }

  .search-actions .el-button {
    width: 200px;
  }

  .results-toolbar {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .toolbar-right {
    width: 100%;
    justify-content: space-between;
  }

  .hotspot-header,
  .result-header {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }

  .hotspot-meta,
  .result-meta,
  .hotspot-actions,
  .result-actions {
    flex-direction: column;
    gap: 8px;
  }
}
</style>
