<template>
  <div class="opinion-search">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1>舆情检索</h1>
      <p>多维度舆情数据检索与可视化分析</p>
    </div>

    <!-- 搜索区域 -->
    <div class="search-section">
      <a-card :bordered="false">
        <a-form layout="vertical">
          <a-row :gutter="16">
            <a-col :xs="24" :md="12">
              <a-form-item label="检索关键词">
                <a-input
                  v-model:value="searchQuery"
                  placeholder="输入检索关键词"
                  size="large"
                  @pressEnter="performSearch"
                >
                  <template #suffix>
                    <search-outlined @click="performSearch" style="cursor: pointer;" />
                  </template>
                </a-input>
              </a-form-item>
            </a-col>
            <a-col :xs="24" :md="6">
              <a-form-item label="时间范围">
                <a-range-picker
                  v-model:value="dateRange"
                  size="large"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :xs="24" :md="6">
              <a-form-item label="数据源">
                <a-select
                  v-model:value="selectedSources"
                  mode="multiple"
                  placeholder="选择数据源"
                  size="large"
                  style="width: 100%"
                >
                  <a-select-option value="news">新闻媒体</a-select-option>
                  <a-select-option value="social">社交媒体</a-select-option>
                  <a-select-option value="forum">论坛博客</a-select-option>
                  <a-select-option value="government">政府网站</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
          
          <a-row :gutter="16">
            <a-col :xs="24" :md="8">
              <a-form-item label="地域范围">
                <a-select
                  v-model:value="selectedRegions"
                  mode="multiple"
                  placeholder="选择地域"
                  size="large"
                  style="width: 100%"
                >
                  <a-select-option value="china">中国</a-select-option>
                  <a-select-option value="usa">美国</a-select-option>
                  <a-select-option value="europe">欧洲</a-select-option>
                  <a-select-option value="asia">亚洲</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :xs="24" :md="8">
              <a-form-item label="语言">
                <a-select
                  v-model:value="selectedLanguage"
                  placeholder="选择语言"
                  size="large"
                  style="width: 100%"
                >
                  <a-select-option value="all">全部语言</a-select-option>
                  <a-select-option value="zh">中文</a-select-option>
                  <a-select-option value="en">英文</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :xs="24" :md="8">
              <a-form-item label="排序方式">
                <a-select
                  v-model:value="sortBy"
                  placeholder="选择排序"
                  size="large"
                  style="width: 100%"
                >
                  <a-select-option value="relevance">相关度</a-select-option>
                  <a-select-option value="time">时间</a-select-option>
                  <a-select-option value="influence">影响力</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>

          <div class="search-actions">
            <a-button type="primary" size="large" @click="performSearch" :loading="searching">
              <search-outlined />
              开始检索
            </a-button>
            <a-button size="large" @click="resetSearch">
              重置
            </a-button>
            <a-button size="large" @click="saveSearchScheme">
              <save-outlined />
              保存方案
            </a-button>
          </div>
        </a-form>
      </a-card>
    </div>

    <!-- 结果展示区域 -->
    <div v-if="hasSearched" class="results-section">
      <!-- 可视化分析 -->
      <a-card title="可视化分析" :bordered="false" class="analysis-card">
        <a-tabs v-model:activeKey="activeAnalysisTab">
          <a-tab-pane key="overview" tab="总览">
            <div class="overview-stats">
              <a-row :gutter="16">
                <a-col :xs="12" :sm="6">
                  <a-statistic
                    title="总结果数"
                    :value="searchResults.total"
                    :value-style="{ color: '#3f8600' }"
                  />
                </a-col>
                <a-col :xs="12" :sm="6">
                  <a-statistic
                    title="相关站点"
                    :value="searchResults.sites"
                    :value-style="{ color: '#1890ff' }"
                  />
                </a-col>
                <a-col :xs="12" :sm="6">
                  <a-statistic
                    title="时间跨度"
                    :value="searchResults.timeSpan"
                    suffix="天"
                    :value-style="{ color: '#722ed1' }"
                  />
                </a-col>
                <a-col :xs="12" :sm="6">
                  <a-statistic
                    title="平均情感"
                    :value="searchResults.avgSentiment"
                    :precision="1"
                    :value-style="{ color: '#eb2f96' }"
                  />
                </a-col>
              </a-row>
            </div>
          </a-tab-pane>
          
          <a-tab-pane key="region" tab="地域分析">
            <div class="chart-container">
              <div class="chart-placeholder">
                <global-outlined style="font-size: 48px; color: #d9d9d9;" />
                <p>地域分布图表</p>
              </div>
            </div>
          </a-tab-pane>
          
          <a-tab-pane key="trend" tab="趋势分析">
            <div class="chart-container">
              <div class="chart-placeholder">
                <line-chart-outlined style="font-size: 48px; color: #d9d9d9;" />
                <p>时间趋势图表</p>
              </div>
            </div>
          </a-tab-pane>
          
          <a-tab-pane key="sentiment" tab="情感分析">
            <div class="chart-container">
              <div class="chart-placeholder">
                <smile-outlined style="font-size: 48px; color: #d9d9d9;" />
                <p>情感分布图表</p>
              </div>
            </div>
          </a-tab-pane>
          
          <a-tab-pane key="source" tab="来源分析">
            <div class="chart-container">
              <div class="chart-placeholder">
                <pie-chart-outlined style="font-size: 48px; color: #d9d9d9;" />
                <p>来源分布图表</p>
              </div>
            </div>
          </a-tab-pane>
          
          <a-tab-pane key="hotspot" tab="重要舆情">
            <div class="hotspot-list">
              <a-list
                :data-source="hotspotNews"
                item-layout="vertical"
              >
                <template #renderItem="{ item }">
                  <a-list-item>
                    <template #actions>
                      <span><eye-outlined /> {{ item.views }}</span>
                      <span><message-outlined /> {{ item.comments }}</span>
                      <span><share-alt-outlined /> {{ item.shares }}</span>
                    </template>
                    <a-list-item-meta>
                      <template #title>
                        <a :href="item.url" target="_blank">{{ item.title }}</a>
                        <a-tag :color="getSentimentColor(item.sentiment)" style="margin-left: 8px;">
                          {{ getSentimentText(item.sentiment) }}
                        </a-tag>
                      </template>
                      <template #description>
                        <div class="news-meta">
                          <span>{{ item.source }}</span>
                          <span>{{ item.publishTime }}</span>
                          <span>{{ item.region }}</span>
                        </div>
                      </template>
                    </a-list-item-meta>
                    <div class="news-content">{{ item.summary }}</div>
                  </a-list-item>
                </template>
              </a-list>
            </div>
          </a-tab-pane>
        </a-tabs>
      </a-card>

      <!-- 检索结果列表 -->
      <a-card title="检索结果" :bordered="false" class="results-card">
        <div class="results-toolbar">
          <a-space>
            <span>共找到 {{ searchResults.total }} 条结果</span>
            <a-divider type="vertical" />
            <a-switch
              v-model:checked="showChineseTitle"
              checked-children="中文标题"
              un-checked-children="原文标题"
            />
            <a-button @click="exportResults">
              <download-outlined />
              导出结果
            </a-button>
          </a-space>
        </div>

        <a-list
          :data-source="searchResultsList"
          :loading="searching"
          :pagination="pagination"
          item-layout="vertical"
        >
          <template #renderItem="{ item }">
            <a-list-item>
              <template #actions>
                <span><calendar-outlined /> {{ item.publishTime }}</span>
                <span><global-outlined /> {{ item.source }}</span>
                <span><eye-outlined /> {{ item.views || 0 }}</span>
              </template>
              <a-list-item-meta>
                <template #title>
                  <a :href="item.url" target="_blank">
                    {{ showChineseTitle ? (item.titleCn || item.title) : item.title }}
                  </a>
                  <a-tag :color="getSentimentColor(item.sentiment)" style="margin-left: 8px;">
                    {{ getSentimentText(item.sentiment) }}
                  </a-tag>
                </template>
                <template #description>
                  <div class="result-meta">
                    <span>{{ item.source }}</span>
                    <span>{{ item.region }}</span>
                    <span>{{ item.language }}</span>
                  </div>
                </template>
              </a-list-item-meta>
              <div class="result-content">{{ item.summary }}</div>
            </a-list-item>
          </template>
        </a-list>
      </a-card>
    </div>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, computed } from 'vue';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';
import {
  SearchOutlined,
  SaveOutlined,
  EyeOutlined,
  MessageOutlined,
  ShareAltOutlined,
  DownloadOutlined,
  CalendarOutlined,
  GlobalOutlined,
  LineChartOutlined,
  SmileOutlined,
  PieChartOutlined
} from '@ant-design/icons-vue';

export default defineComponent({
  name: 'OpinionSearch',
  components: {
    SearchOutlined,
    SaveOutlined,
    EyeOutlined,
    MessageOutlined,
    ShareAltOutlined,
    DownloadOutlined,
    CalendarOutlined,
    GlobalOutlined,
    LineChartOutlined,
    SmileOutlined,
    PieChartOutlined
  },
  setup() {
    const searching = ref(false);
    const hasSearched = ref(false);
    const showChineseTitle = ref(false);
    const activeAnalysisTab = ref('overview');

    // 搜索参数
    const searchQuery = ref('');
    const dateRange = ref([dayjs().subtract(30, 'day'), dayjs()]);
    const selectedSources = ref(['news', 'social']);
    const selectedRegions = ref(['china']);
    const selectedLanguage = ref('all');
    const sortBy = ref('relevance');

    // 搜索结果
    const searchResults = reactive({
      total: 0,
      sites: 0,
      timeSpan: 0,
      avgSentiment: 0
    });

    const searchResultsList = ref([]);
    const hotspotNews = ref([]);

    // 分页配置
    const pagination = reactive({
      current: 1,
      pageSize: 10,
      total: 0,
      showSizeChanger: true,
      showQuickJumper: true,
      onChange: (page, pageSize) => {
        pagination.current = page;
        pagination.pageSize = pageSize;
        // 这里应该重新加载数据
      }
    });

    // 模拟数据
    const mockResults = [
      {
        id: 1,
        title: 'China-US Trade Relations Show Signs of Improvement',
        titleCn: '中美贸易关系显现改善迹象',
        url: 'https://example.com/news/1',
        source: 'Global Times',
        region: '中国',
        language: '英文',
        publishTime: '2024-01-07 10:30',
        sentiment: 'positive',
        views: 1250,
        summary: '最新报道显示，中美两国在贸易谈判中取得了积极进展...'
      },
      {
        id: 2,
        title: '东南亚经济一体化进程加速',
        titleCn: '东南亚经济一体化进程加速',
        url: 'https://example.com/news/2',
        source: '人民日报',
        region: '中国',
        language: '中文',
        publishTime: '2024-01-07 09:15',
        sentiment: 'neutral',
        views: 856,
        summary: '东南亚国家联盟成员国在经济合作方面达成新的共识...'
      }
    ];

    const mockHotspots = [
      {
        id: 1,
        title: '中美高层会晤释放积极信号',
        url: 'https://example.com/hotspot/1',
        source: '新华社',
        region: '中国',
        publishTime: '2024-01-07 14:30',
        sentiment: 'positive',
        views: 5200,
        comments: 128,
        shares: 89,
        summary: '中美两国高层官员在华盛顿举行会晤，双方就贸易、科技等议题进行了深入交流...'
      }
    ];

    // 方法
    const performSearch = async () => {
      if (!searchQuery.value.trim()) {
        message.warning('请输入检索关键词');
        return;
      }

      searching.value = true;
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // 更新搜索结果
        Object.assign(searchResults, {
          total: 1250,
          sites: 45,
          timeSpan: 30,
          avgSentiment: 6.8
        });

        searchResultsList.value = mockResults;
        hotspotNews.value = mockHotspots;
        
        pagination.total = searchResults.total;
        hasSearched.value = true;
        
        message.success('检索完成！');
      } catch (error) {
        message.error('检索失败，请重试');
      } finally {
        searching.value = false;
      }
    };

    const resetSearch = () => {
      searchQuery.value = '';
      dateRange.value = [dayjs().subtract(30, 'day'), dayjs()];
      selectedSources.value = ['news', 'social'];
      selectedRegions.value = ['china'];
      selectedLanguage.value = 'all';
      sortBy.value = 'relevance';
      hasSearched.value = false;
    };

    const saveSearchScheme = () => {
      if (!searchQuery.value.trim()) {
        message.warning('请先输入检索关键词');
        return;
      }
      message.success('检索方案已保存');
    };

    const exportResults = () => {
      message.info('导出功能开发中');
    };

    const getSentimentColor = (sentiment) => {
      const colors = {
        positive: 'green',
        neutral: 'blue',
        negative: 'red'
      };
      return colors[sentiment] || 'default';
    };

    const getSentimentText = (sentiment) => {
      const texts = {
        positive: '正面',
        neutral: '中性',
        negative: '负面'
      };
      return texts[sentiment] || '未知';
    };

    return {
      searching,
      hasSearched,
      showChineseTitle,
      activeAnalysisTab,
      searchQuery,
      dateRange,
      selectedSources,
      selectedRegions,
      selectedLanguage,
      sortBy,
      searchResults,
      searchResultsList,
      hotspotNews,
      pagination,
      performSearch,
      resetSearch,
      saveSearchScheme,
      exportResults,
      getSentimentColor,
      getSentimentText
    };
  }
});
</script>

<style scoped>
.opinion-search {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 32px;
  margin-bottom: 24px;
  color: white;
  text-align: center;
}

.page-header h1 {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 8px;
  color: white;
}

.page-header p {
  font-size: 1.1rem;
  opacity: 0.9;
  margin: 0;
}

.search-section {
  margin-bottom: 24px;
}

.search-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  margin-top: 16px;
}

.results-section {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.analysis-card {
  margin-bottom: 24px;
}

.overview-stats {
  padding: 16px 0;
}

.chart-container {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fafafa;
  border-radius: 8px;
}

.chart-placeholder {
  text-align: center;
  color: #999;
}

.chart-placeholder p {
  margin-top: 16px;
  font-size: 16px;
}

.hotspot-list {
  max-height: 400px;
  overflow-y: auto;
}

.news-meta {
  display: flex;
  gap: 16px;
  color: #666;
  font-size: 12px;
}

.news-content {
  color: #666;
  line-height: 1.6;
  margin-top: 8px;
}

.results-toolbar {
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.result-meta {
  display: flex;
  gap: 16px;
  color: #666;
  font-size: 12px;
}

.result-content {
  color: #666;
  line-height: 1.6;
  margin-top: 8px;
}

@media (max-width: 768px) {
  .opinion-search {
    padding: 16px;
  }
  
  .page-header {
    padding: 24px 16px;
  }
  
  .search-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .search-actions .ant-btn {
    width: 200px;
  }
  
  .news-meta,
  .result-meta {
    flex-direction: column;
    gap: 8px;
  }
}
</style>
