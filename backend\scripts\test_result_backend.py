#!/usr/bin/env python3
"""
测试 Result Backend 配置
"""
import os
import sys
import time
from pathlib import Path
from dotenv import load_dotenv

# 添加后端路径
backend_dir = Path(__file__).parent.parent
sys.path.append(str(backend_dir))

# 加载环境变量
env_path = backend_dir / ".env"
load_dotenv(env_path)

def test_celery_config():
    """测试 Celery 配置"""
    print("🔍 测试 Celery 配置")
    print("=" * 50)
    
    try:
        from app.core.celery_app import celery_app
        
        print(f"📊 Celery 应用配置:")
        print(f"   应用名: {celery_app.main}")
        print(f"   Broker: {celery_app.conf.broker_url}")
        print(f"   Backend: {celery_app.conf.result_backend}")
        print(f"   Backend 类型: {type(celery_app.backend)}")
        print(f"   Backend 类名: {celery_app.backend.__class__.__name__}")
        
        # 检查 Result Backend 是否被禁用
        if hasattr(celery_app.backend, 'disabled'):
            print(f"   Backend 禁用状态: {celery_app.backend.disabled}")
        
        # 检查相关配置
        print(f"\n📊 相关配置:")
        print(f"   task_ignore_result: {celery_app.conf.task_ignore_result}")
        print(f"   result_expires: {celery_app.conf.result_expires}")
        print(f"   result_serializer: {celery_app.conf.result_serializer}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试 Celery 配置失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_redis_backend_direct():
    """直接测试 Redis Backend"""
    print(f"\n🔍 直接测试 Redis Backend")
    print("=" * 50)
    
    try:
        import redis
        
        r = redis.Redis(host='localhost', port=6379, db=0)
        r.ping()
        print(f"✅ Redis 连接正常")
        
        # 测试设置和获取值
        test_key = "celery-test-backend"
        test_value = {"status": "SUCCESS", "result": "test"}
        
        import json
        r.set(test_key, json.dumps(test_value))
        retrieved = r.get(test_key)
        
        if retrieved:
            parsed = json.loads(retrieved.decode())
            print(f"✅ Redis 读写测试成功: {parsed}")
            r.delete(test_key)
            return True
        else:
            print(f"❌ Redis 读写测试失败")
            return False
        
    except Exception as e:
        print(f"❌ Redis Backend 测试失败: {e}")
        return False

def test_simple_task_with_result():
    """测试带结果的简单任务"""
    print(f"\n🧪 测试带结果的简单任务")
    print("=" * 50)
    
    try:
        from app.core.celery_app import celery_app
        
        # 创建一个简单的内联任务
        @celery_app.task(bind=True)
        def simple_test_task(self, x, y):
            """简单的测试任务"""
            result = x + y
            print(f"执行任务: {x} + {y} = {result}")
            return result
        
        # 提交任务
        print(f"🚀 提交任务: 5 + 3")
        async_result = simple_test_task.delay(5, 3)
        
        print(f"✅ 任务提交成功")
        print(f"📊 任务ID: {async_result.id}")
        print(f"📊 初始状态: {async_result.status}")
        print(f"📊 Backend 类型: {type(async_result.backend)}")
        
        # 尝试获取结果
        print(f"⏳ 等待结果（超时 15 秒）...")
        try:
            result = async_result.get(timeout=15)
            print(f"✅ 任务完成: 5 + 3 = {result}")
            return True
        except Exception as get_error:
            print(f"❌ 获取结果失败: {get_error}")
            
            # 检查任务状态
            print(f"📊 最终状态: {async_result.status}")
            print(f"📊 任务信息: {async_result.info}")
            
            return False
        
    except Exception as e:
        print(f"❌ 简单任务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_existing_task():
    """测试现有的统一任务"""
    print(f"\n🧪 测试现有的统一任务")
    print("=" * 50)
    
    try:
        from app.tasks.test_unified_task import test_unified_task
        from app.core.task_manager import task_manager
        
        # 创建任务记录
        task_id = task_manager.create_task(
            task_type="test",
            task_subtype="simple",
            user_id="test-user",
            title="Backend 测试任务",
            description="测试 Result Backend",
            input_params={"test_message": "Backend 测试", "duration": 2}
        )
        
        print(f"📝 任务ID: {task_id}")
        
        # 提交任务
        async_result = test_unified_task.delay(
            task_id=task_id,
            test_message="Backend 测试",
            duration=2
        )
        
        print(f"✅ 统一任务提交成功")
        print(f"📊 Celery 任务ID: {async_result.id}")
        print(f"📊 初始状态: {async_result.status}")
        
        # 监控任务
        print(f"⏳ 监控任务进度...")
        for i in range(10):
            time.sleep(1)
            
            # 检查 Celery 状态
            celery_status = async_result.status
            
            # 检查数据库状态
            task_info = task_manager.get_task(task_id)
            if task_info:
                db_status = task_info['status']
                progress = task_info['progress']
                message = task_info.get('message', 'N/A')
                
                print(f"   [{i+1:2d}s] Celery: {celery_status} | DB: {db_status} ({progress}%) - {message}")
                
                if db_status == 'completed':
                    print(f"🎉 统一任务完成！")
                    return True
                elif db_status == 'failed':
                    print(f"❌ 统一任务失败")
                    return False
            else:
                print(f"   [{i+1:2d}s] Celery: {celery_status} | DB: 任务未找到")
        
        print(f"⏰ 统一任务监控超时")
        return False
        
    except Exception as e:
        print(f"❌ 统一任务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🧪 Result Backend 测试")
    print("=" * 60)
    
    # 1. 测试 Celery 配置
    config_ok = test_celery_config()
    
    # 2. 直接测试 Redis Backend
    redis_ok = test_redis_backend_direct()
    
    # 3. 测试带结果的简单任务
    simple_ok = False
    if config_ok and redis_ok:
        simple_ok = test_simple_task_with_result()
    
    # 4. 测试现有的统一任务
    unified_ok = False
    if simple_ok:
        unified_ok = test_existing_task()
    
    # 总结
    print(f"\n📊 Result Backend 测试总结:")
    print(f"{'✅' if config_ok else '❌'} Celery 配置: {'正常' if config_ok else '异常'}")
    print(f"{'✅' if redis_ok else '❌'} Redis Backend: {'正常' if redis_ok else '异常'}")
    print(f"{'✅' if simple_ok else '❌'} 简单任务: {'成功' if simple_ok else '失败'}")
    print(f"{'✅' if unified_ok else '❌'} 统一任务: {'成功' if unified_ok else '失败'}")
    
    if unified_ok:
        print(f"\n🎉 Result Backend 完全正常！")
        print(f"💡 Celery 系统现在应该能正常工作")
    elif simple_ok:
        print(f"\n✅ 基本 Celery 功能正常")
        print(f"❌ 但统一任务有问题")
        print(f"💡 问题可能在于统一任务的实现")
    else:
        print(f"\n❌ Result Backend 有问题")
        print(f"💡 需要修复 Backend 配置")

if __name__ == "__main__":
    main()
