"""
数据库模型模块
包含所有SQLAlchemy ORM模型以及Pydantic API模型

注意：导入时应使用完全限定名称以避免名称冲突
例如: from models.digital_human import DigitalHuman
而不是: from models import DigitalHuman

警告: 在导入SQLAlchemy模型类时，必须使用完全限定名称，否则会导致
"Multiple classes found for path in the registry of this declarative base"
错误。这是因为SQLAlchemy维护了一个全局注册表，相同名称的类会产生冲突。

重要提示: 避免在多个地方导入相同的模型类，以防止SQLAlchemy注册表冲突。
如果必须多次导入某个模型类，确保在模型类中使用以下配置：
```python
class MyModel(Base):
    __tablename__ = "my_table"
    __table_args__ = {'extend_existing': True}  # 允许表被多次定义
```
"""

# 版本号
__version__ = "1.0.0"

# 为了确保映射关系正确初始化，仅导入关键依赖模型
# 先导入没有外部模型依赖的模型
from models.user import User
from models.team import Team
from models.digital_human import DigitalHuman
# 然后导入依赖User和DigitalHuman的模型
from models.task import TaskModel, Task, TaskLog
from models.agent import Agent, AgentTag, AgentCapability, AgentExample, AgentReview, UserFavorite, AgentSession, AgentMessage
from models.chat_session import ChatSession
from models.chat_message import ChatMessage
from models.terminology import Terminology, MonoCorpus, MonoCorpusFile, MonoCorpusEntry, Term
from models.terminology_collection import TerminologyCollectionTask, TerminologyCollectionResult
from models.corpus import Corpus, CorpusEntry

# 不导入其他可能存在问题的模型，避免引起其他问题
# 按需导入模型更加安全

# 通过相对导入方式明确声明此模块提供的类
# 不要在这里导入模型类，以避免循环依赖问题
# 各个文件应该单独导入所需的模型

# 导入所有模型，确保SQLAlchemy能够正确创建表和关系
from models.user import User
from models.team import Team
from models.terminology import Terminology, MonoCorpus, MonoCorpusFile, MonoCorpusEntry, Term
from models.terminology_collection import TerminologyCollectionTask, TerminologyCollectionResult
from models.corpus import Corpus, CorpusEntry

# 确保所有模型都被导入，以便SQLAlchemy能够正确解析关系
__all__ = [
    'User',
    'Team',
    'Terminology',
    'MonoCorpus',
    'MonoCorpusFile',
    'MonoCorpusEntry',
    'Term',
    'TerminologyCollectionTask',
    'TerminologyCollectionResult',
    'Corpus',
    'CorpusEntry'
] 