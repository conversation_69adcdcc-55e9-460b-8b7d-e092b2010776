"""
翻译服务适配器 - 将翻译服务适配到统一的任务处理流程
"""
import logging
import os
from typing import Dict, Any, Optional, List

from services.task_adapter import TaskAdapter, register_adapter
from services.storage_manager import get_storage_manager

# 配置日志
logger = logging.getLogger(__name__)

class TranslationAdapter(TaskAdapter):
    """翻译服务适配器"""
    
    async def execute(self, task_id: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行翻译任务
        
        Args:
            task_id: 任务ID
            params: 任务参数，包括:
                text: 要翻译的文本
                source_lang: 源语言代码
                target_lang: 目标语言代码
                terminology_id: 术语表ID（可选）
                
        Returns:
            执行结果
        """
        try:
            # 更新进度
            await self.update_progress(task_id, 10, "准备翻译参数...")
            
            # 提取参数
            text = params.get("text")
            source_lang = params.get("source_lang")
            target_lang = params.get("target_lang")
            terminology_id = params.get("terminology_id")
            
            # 验证参数
            if not text:
                return await self.handle_error(task_id, ValueError("缺少必要的文本参数"))
            
            if not source_lang:
                return await self.handle_error(task_id, ValueError("缺少必要的源语言参数"))
            
            if not target_lang:
                return await self.handle_error(task_id, ValueError("缺少必要的目标语言参数"))
            
            # 更新进度
            await self.update_progress(task_id, 20, f"开始翻译: {source_lang} -> {target_lang}...")
            
            # 翻译结果保存路径（如果是大文本）
            translation_output = None
            storage_manager = get_storage_manager()
            
            # 检查是否是大文本，决定是否保存到文件
            is_large_text = len(text) > 10000  # 10k字符以上视为大文本
            
            if is_large_text:
                # 创建源文本文件
                source_filename = f"{source_lang}_source.txt"
                source_file_path = storage_manager.get_upload_path("document", source_filename, task_id)
                
                # 确保目录存在
                os.makedirs(os.path.dirname(source_file_path), exist_ok=True)
                
                # 保存源文本
                with open(source_file_path, 'w', encoding='utf-8') as f:
                    f.write(text)
                
                # 预先创建目标文件路径
                target_filename = f"{target_lang}_translation.txt"
                translation_output = storage_manager.get_result_path("translation", target_filename, task_id)
                
                await self.update_progress(task_id, 30, "大文本已保存，正在翻译...")
            else:
                await self.update_progress(task_id, 30, "正在翻译文本...")
            
            # 执行翻译
            translated_text = await self.service.translate_text(
                text=text,
                source_lang=source_lang,
                target_lang=target_lang,
                terminology_id=terminology_id
            )
            
            # 如果翻译失败
            if not translated_text:
                return await self.handle_error(task_id, ValueError("翻译失败，未获得翻译结果"))
            
            # 如果是大文本，保存翻译结果
            if is_large_text and translation_output:
                # 确保目录存在
                os.makedirs(os.path.dirname(translation_output), exist_ok=True)
                
                # 保存翻译结果
                with open(translation_output, 'w', encoding='utf-8') as f:
                    f.write(translated_text)
                
                await self.update_progress(task_id, 90, "翻译完成，已保存结果...")
            else:
                await self.update_progress(task_id, 90, "翻译完成...")
            
            # 构建返回结果
            result = {
                "translated_text": translated_text,
                "source_language": source_lang,
                "target_language": target_lang,
                "char_count": len(text),
                "translated_char_count": len(translated_text)
            }
            
            # 如果有输出文件，添加到结果
            if translation_output:
                result["output_file"] = translation_output
                result["output_files"] = [translation_output]
            
            # 更新最终进度
            await self.update_progress(
                task_id, 
                100, 
                "翻译任务完成", 
                status="completed",
                result=result
            )
            
            # 返回成功结果
            return {
                "success": True,
                "data": result
            }
            
        except Exception as e:
            # 处理异常
            return await self.handle_error(task_id, e)

class AudioTranslationAdapter(TaskAdapter):
    """音频翻译服务适配器"""
    
    def __init__(self, service=None, progress_updater=None):
        """初始化适配器"""
        if service is None:
            from ..audio_service import get_audio_service
            service = get_audio_service()
        super().__init__(service, progress_updater)
    
    async def execute(self, task_id: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行音频翻译任务
        
        Args:
            task_id: 任务ID
            params: 任务参数，包括:
                audio_path/file_path: 音频文件路径
                source_lang/source_language: 源语言代码
                target_lang/target_language: 目标语言代码
                quality: 翻译质量
                domain: 领域
                output_format: 输出音频格式
                keep_original: 是否保留原始音频
                
        Returns:
            执行结果
        """
        try:
            # 更新进度
            await self.update_progress(task_id, 10, "准备音频翻译参数...")
            
            # 提取参数
            audio_path = params.get("audio_path") or params.get("file_path")
            source_lang = params.get("source_lang") or params.get("source_language")
            target_lang = params.get("target_lang") or params.get("target_language")
            quality = params.get("quality", "standard")
            domain = params.get("domain", "")
            output_format = params.get("output_format", "mp3")
            keep_original = params.get("keep_original", False)
            
            # 验证参数
            if not audio_path:
                return await self.handle_error(task_id, ValueError("缺少必要的音频文件路径参数"))
            
            if not os.path.exists(audio_path):
                return await self.handle_error(task_id, FileNotFoundError(f"音频文件不存在: {audio_path}"))
            
            if not target_lang:
                return await self.handle_error(task_id, ValueError("缺少必要的目标语言参数"))
            
            # 更新进度
            await self.update_progress(task_id, 20, f"开始音频翻译: {source_lang or '自动检测'} -> {target_lang}...")
            
            # 调用音频翻译服务
            translation_result = await self.service.translate_audio(
                audio_path=audio_path,
                source_lang=source_lang,
                target_lang=target_lang,
                quality=quality,
                domain=domain,
                output_format=output_format,
                keep_original=keep_original,
                task_id=task_id
            )
            
            # 更新进度
            await self.update_progress(task_id, 90, "音频翻译完成，处理结果...")
            
            # 构建返回结果
            result = {
                "source_language": translation_result["source_language"],
                "target_language": translation_result["target_language"],
                "translation_text": translation_result["translated_text"],
                "translation_text_file": translation_result["transcript_path"],
                "translation_audio_file": translation_result["translated_audio_path"],
                "segments": translation_result["segments"],
                "quality": quality,
                "domain": domain,
                "output_format": output_format,
                "keep_original": keep_original
            }
            
            # 更新最终进度
            await self.update_progress(
                task_id, 
                100, 
                "音频翻译任务完成", 
                status="completed",
                result=result
            )
            
            # 返回成功结果
            return {
                "success": True,
                "data": result
            }
            
        except Exception as e:
            # 处理异常
            return await self.handle_error(task_id, e)

class VideoTranslationAdapter(TaskAdapter):
    """视频翻译服务适配器"""
    
    async def execute(self, task_id: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行视频翻译任务
        
        Args:
            task_id: 任务ID
            params: 任务参数，包括:
                video_path: 视频文件路径
                source_lang: 源语言代码
                target_lang: 目标语言代码
                subtitle_mode: 字幕模式 (hardcoded/separate)
                voice_mode: 配音模式 (dub/keep_original)
                
        Returns:
            执行结果
        """
        try:
            # 更新进度
            await self.update_progress(task_id, 5, "准备视频翻译参数...")
            
            # 提取参数
            video_path = params.get("video_path")
            source_lang = params.get("source_lang")
            target_lang = params.get("target_lang")
            subtitle_mode = params.get("subtitle_mode", "hardcoded")
            voice_mode = params.get("voice_mode", "dub")
            
            # 验证参数
            if not video_path:
                return await self.handle_error(task_id, ValueError("缺少必要的视频文件路径参数"))
            
            if not os.path.exists(video_path):
                return await self.handle_error(task_id, FileNotFoundError(f"视频文件不存在: {video_path}"))
            
            if not source_lang:
                return await self.handle_error(task_id, ValueError("缺少必要的源语言参数"))
            
            if not target_lang:
                return await self.handle_error(task_id, ValueError("缺少必要的目标语言参数"))
            
            # 更新进度
            await self.update_progress(task_id, 10, f"开始视频翻译: {source_lang} -> {target_lang}...")
            
            # 获取存储管理器
            storage_manager = get_storage_manager()
            
            # 准备输出文件路径
            translated_video_output = storage_manager.get_result_path("video", f"{task_id}_translated_video.mp4", task_id)
            subtitle_output = storage_manager.get_result_path("translation", f"{task_id}_subtitles.srt", task_id)
            transcript_output = storage_manager.get_result_path("transcription", f"{task_id}_transcript.txt", task_id)
            
            # 更新进度
            await self.update_progress(task_id, 20, "开始处理视频...")
            
            # 调用视频翻译服务
            # 注意：这里假设service有translate_video方法，实际实现可能需要根据具体服务调整
            translation_result = await self.service.translate_video(
                video_path=video_path,
                source_lang=source_lang,
                target_lang=target_lang,
                subtitle_mode=subtitle_mode,
                voice_mode=voice_mode,
                video_output=translated_video_output,
                subtitle_output=subtitle_output,
                transcript_output=transcript_output,
                progress_callback=lambda p, m: self.update_progress(task_id, 20 + int(p * 0.7), m)
            )
            
            # 如果翻译失败
            if not translation_result or not translation_result.get("success"):
                error_msg = translation_result.get("error", "视频翻译失败") if translation_result else "视频翻译失败"
                return await self.handle_error(task_id, ValueError(error_msg))
            
            # 更新进度
            await self.update_progress(task_id, 95, "视频翻译完成，处理结果...")
            
            # 构建返回结果
            result = {
                "source_language": source_lang,
                "target_language": target_lang,
                "video_file": translated_video_output,
                "subtitle_file": subtitle_output if os.path.exists(subtitle_output) else None,
                "transcript_file": transcript_output if os.path.exists(transcript_output) else None,
                "duration": translation_result.get("duration", 0),
                "output_files": [f for f in [translated_video_output, subtitle_output, transcript_output] if os.path.exists(f)]
            }
            
            # 添加缩略图（如果有）
            if "thumbnail_path" in translation_result and os.path.exists(translation_result["thumbnail_path"]):
                result["thumbnail_file"] = translation_result["thumbnail_path"]
                result["output_files"].append(translation_result["thumbnail_path"])
            
            # 更新最终进度
            await self.update_progress(
                task_id, 
                100, 
                "视频翻译任务完成", 
                status="completed",
                result=result
            )
            
            # 返回成功结果
            return {
                "success": True,
                "data": result
            }
            
        except Exception as e:
            # 处理异常
            return await self.handle_error(task_id, e)

# 注册适配器
register_adapter("text_translation", TranslationAdapter)
register_adapter("audio_translation", AudioTranslationAdapter)
register_adapter("video_translation", VideoTranslationAdapter) 