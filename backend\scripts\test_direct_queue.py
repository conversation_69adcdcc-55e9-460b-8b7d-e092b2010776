#!/usr/bin/env python3
"""
直接测试队列和任务处理
"""
import os
import sys
import time
import json
from pathlib import Path
from dotenv import load_dotenv

# 添加后端路径
backend_dir = Path(__file__).parent.parent
sys.path.append(str(backend_dir))

# 加载环境变量
env_path = backend_dir / ".env"
load_dotenv(env_path)

def test_redis_direct():
    """直接测试 Redis 队列操作"""
    print("🔍 直接测试 Redis 队列操作")
    print("=" * 50)
    
    try:
        import redis
        
        r = redis.Redis(host='localhost', port=6379, db=0)
        r.ping()
        print("✅ Redis 连接正常")
        
        # 手动向队列添加一个简单任务
        test_task = {
            "id": "test-direct-queue",
            "task": "app.tasks.wanx_video_tasks.wanx_text_to_video",
            "args": [],
            "kwargs": {
                "task_id": "test-direct",
                "prompt": "直接队列测试",
                "model": "t2v-1.3B",
                "duration": 1,
                "resolution": "512x512",
                "fps": 8,
                "guidance_scale": 5.0,
                "num_inference_steps": 5,
                "user_id": "test-user"
            },
            "retries": 0,
            "eta": None,
            "expires": None,
            "utc": True,
            "callbacks": None,
            "errbacks": None,
            "timelimit": [None, None],
            "taskset": None,
            "chord": None
        }
        
        # 将任务推送到 wanx_generation 队列
        task_json = json.dumps(test_task)
        r.lpush("wanx_generation", task_json)
        
        print(f"✅ 手动添加任务到 wanx_generation 队列")
        print(f"📊 队列长度: {r.llen('wanx_generation')}")
        
        # 等待一下看看 Worker 是否处理
        print(f"⏳ 等待 10 秒看 Worker 是否处理...")
        time.sleep(10)
        
        # 检查队列长度变化
        new_length = r.llen('wanx_generation')
        print(f"📊 10秒后队列长度: {new_length}")
        
        if new_length == 0:
            print(f"✅ 任务被 Worker 处理了！")
            return True
        else:
            print(f"❌ 任务没有被处理")
            return False
        
    except Exception as e:
        print(f"❌ Redis 直接测试失败: {e}")
        return False

def test_celery_inspect_detailed():
    """详细检查 Celery 状态"""
    print(f"\n🔍 详细检查 Celery 状态")
    print("=" * 50)
    
    try:
        from app.core.celery_app import celery_app
        
        inspect = celery_app.control.inspect()
        
        # 检查所有可用信息
        print(f"📊 活跃任务:")
        active = inspect.active()
        if active:
            for worker, tasks in active.items():
                print(f"   {worker}: {len(tasks)} 个活跃任务")
                for task in tasks:
                    print(f"     - {task.get('name', 'N/A')}")
        else:
            print(f"   没有活跃任务")
        
        print(f"\n📊 预定任务:")
        scheduled = inspect.scheduled()
        if scheduled:
            for worker, tasks in scheduled.items():
                print(f"   {worker}: {len(tasks)} 个预定任务")
        else:
            print(f"   没有预定任务")
        
        print(f"\n📊 保留任务:")
        reserved = inspect.reserved()
        if reserved:
            for worker, tasks in reserved.items():
                print(f"   {worker}: {len(tasks)} 个保留任务")
        else:
            print(f"   没有保留任务")
        
        print(f"\n📊 注册的任务:")
        registered = inspect.registered()
        if registered:
            for worker, tasks in registered.items():
                print(f"   {worker}: {len(tasks)} 个注册任务")
                wanx_tasks = [t for t in tasks if 'wanx' in t]
                if wanx_tasks:
                    print(f"     Wanx 任务: {wanx_tasks}")
        else:
            print(f"   无法获取注册任务")
        
        print(f"\n📊 Worker 统计:")
        stats = inspect.stats()
        if stats:
            for worker, stat in stats.items():
                print(f"   {worker}:")
                print(f"     进程池: {stat.get('pool', {})}")
                print(f"     总任务数: {stat.get('total', 'N/A')}")
        else:
            print(f"   无法获取统计信息")
        
        return True
        
    except Exception as e:
        print(f"❌ Celery 详细检查失败: {e}")
        return False

def test_simple_task_apply():
    """测试简单任务直接应用"""
    print(f"\n🧪 测试简单任务直接应用")
    print("=" * 50)
    
    try:
        from app.tasks.wanx_video_tasks import wanx_text_to_video
        from app.core.task_manager import task_manager
        from app.models.unified_tasks import TaskTypes
        
        # 创建任务记录
        task_id = task_manager.create_task(
            task_type=TaskTypes.VIDEO_GENERATION,
            task_subtype=TaskTypes.VideoGeneration.TEXT_TO_VIDEO,
            user_id="test-user",
            title="直接应用测试",
            description="测试任务直接应用",
            input_params={
                "prompt": "直接应用测试",
                "model": "t2v-1.3B"
            }
        )
        
        print(f"📝 任务ID: {task_id}")
        
        # 使用 apply_async 而不是 delay
        result = wanx_text_to_video.apply_async(
            kwargs={
                "task_id": task_id,
                "prompt": "直接应用测试",
                "model": "t2v-1.3B",
                "duration": 1,
                "resolution": "512x512",
                "fps": 8,
                "guidance_scale": 5.0,
                "num_inference_steps": 5,
                "user_id": "test-user"
            },
            queue='wanx_generation'  # 明确指定队列
        )
        
        print(f"✅ apply_async 调用成功")
        print(f"📊 Celery 任务ID: {result.id}")
        print(f"📊 初始状态: {result.status}")
        
        # 等待一下看状态变化
        print(f"⏳ 等待 15 秒查看状态变化...")
        for i in range(15):
            time.sleep(1)
            status = result.status
            print(f"   [{i+1:2d}s] Celery 状态: {status}")
            
            if status != 'PENDING':
                print(f"✅ 状态发生变化: {status}")
                break
        
        # 检查数据库中的任务状态
        task_info = task_manager.get_task(task_id)
        if task_info:
            print(f"📊 数据库任务状态: {task_info['status']} ({task_info['progress']}%)")
        
        return result.id, task_id
        
    except Exception as e:
        print(f"❌ 直接应用测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None

def main():
    """主函数"""
    print("🧪 直接队列和任务测试")
    print("=" * 60)
    
    # 1. 详细检查 Celery 状态
    celery_ok = test_celery_inspect_detailed()
    
    # 2. 测试 Redis 直接操作
    redis_ok = test_redis_direct()
    
    # 3. 测试简单任务应用
    celery_task_id, task_id = test_simple_task_apply()
    
    # 总结
    print(f"\n📊 测试总结:")
    print(f"{'✅' if celery_ok else '❌'} Celery 检查: {'正常' if celery_ok else '异常'}")
    print(f"{'✅' if redis_ok else '❌'} Redis 直接测试: {'成功' if redis_ok else '失败'}")
    print(f"{'✅' if celery_task_id else '❌'} 任务应用: {'成功' if celery_task_id else '失败'}")
    
    if redis_ok:
        print(f"\n🎉 Worker 能够处理队列中的任务！")
        print(f"💡 问题可能在于任务路由或提交方式")
    else:
        print(f"\n⚠️ Worker 没有处理队列中的任务")
        print(f"💡 可能的问题:")
        print(f"   1. Worker 没有监听 wanx_generation 队列")
        print(f"   2. Worker 没有加载 wanx_video_tasks 模块")
        print(f"   3. 任务格式不正确")

if __name__ == "__main__":
    main()
