"""
视频生成任务 - 简洁版本
"""
import os
import time
from datetime import datetime
from pathlib import Path
from app.core.celery_unified import celery_app

# 输出目录
OUTPUT_DIR = Path(__file__).parent.parent.parent / "storage" / "videos"
OUTPUT_DIR.mkdir(parents=True, exist_ok=True)

@celery_app.task(bind=True, name='video.text_to_video')
def text_to_video(self, prompt: str, model: str = "wanx-2.1", **kwargs):
    """文本转视频任务 - 标准实现"""
    task_id = self.request.id
    
    try:
        print(f"[视频任务] 开始: {task_id}")
        print(f"[视频任务] 提示词: {prompt}")
        
        # 创建任务记录
        simple_task_manager.create_task(
            task_id=task_id,
            task_type='text_to_video',
            prompt=prompt,
            model=model,
            **kwargs
        )
        
        # 进度更新
        update_task_progress(task_id, 10, "初始化模型...")
        time.sleep(1)
        
        update_task_progress(task_id, 30, "分析提示词...")
        time.sleep(2)
        
        update_task_progress(task_id, 60, "生成视频帧...")
        time.sleep(3)
        
        update_task_progress(task_id, 90, "渲染视频...")
        time.sleep(2)
        
        # 生成输出文件
        output_file = OUTPUT_DIR / f"video_{task_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.mp4"
        
        # 模拟生成视频文件
        with open(output_file.with_suffix('.txt'), 'w', encoding='utf-8') as f:
            f.write(f"视频生成任务\n")
            f.write(f"任务ID: {task_id}\n")
            f.write(f"提示词: {prompt}\n")
            f.write(f"模型: {model}\n")
            f.write(f"生成时间: {datetime.now()}\n")
        
        result = {
            'task_id': task_id,
            'status': 'completed',
            'output_file': str(output_file.with_suffix('.txt')),
            'video_url': f'/api/v1/video/download/{output_file.name}',
            'metadata': {
                'prompt': prompt,
                'model': model,
                'duration': kwargs.get('duration', 5),
                'resolution': kwargs.get('resolution', '768x512')
            }
        }
        
        update_task_progress(task_id, 100, "视频生成完成")
        print(f"[视频任务] 完成: {task_id}")
        
        return result
        
    except Exception as e:
        error_msg = f"视频生成失败: {str(e)}"
        print(f"[视频任务] 错误: {error_msg}")
        
        simple_task_manager.update_task(
            task_id=task_id,
            status='failed',
            error=error_msg
        )
        
        raise

@celery_app.task(bind=True, name='video.image_to_video')
def image_to_video(self, image_path: str, **kwargs):
    """图片转视频任务"""
    task_id = self.request.id
    
    try:
        print(f"[图片转视频] 开始: {task_id}")
        
        simple_task_manager.create_task(
            task_id=task_id,
            task_type='image_to_video',
            image_path=image_path,
            **kwargs
        )
        
        update_task_progress(task_id, 20, "分析输入图片...")
        time.sleep(1)
        
        update_task_progress(task_id, 50, "生成视频序列...")
        time.sleep(2)
        
        update_task_progress(task_id, 80, "合成视频...")
        time.sleep(2)
        
        output_file = OUTPUT_DIR / f"i2v_{task_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.mp4"
        
        with open(output_file.with_suffix('.txt'), 'w', encoding='utf-8') as f:
            f.write(f"图片转视频任务\n")
            f.write(f"任务ID: {task_id}\n")
            f.write(f"输入图片: {image_path}\n")
            f.write(f"生成时间: {datetime.now()}\n")
        
        result = {
            'task_id': task_id,
            'status': 'completed',
            'output_file': str(output_file.with_suffix('.txt')),
            'input_image': image_path
        }
        
        update_task_progress(task_id, 100, "图片转视频完成")
        return result
        
    except Exception as e:
        error_msg = f"图片转视频失败: {str(e)}"
        simple_task_manager.update_task(task_id=task_id, status='failed', error=error_msg)
        raise

@celery_app.task(bind=True, name='video.get_history')
def get_video_history(self, user_id: str = "demo-user"):
    """获取视频历史"""
    task_id = self.request.id
    
    try:
        # 获取该用户的所有视频任务
        video_tasks = simple_task_manager.get_tasks_by_type('text_to_video')
        video_tasks.extend(simple_task_manager.get_tasks_by_type('image_to_video'))
        
        # 过滤用户任务
        user_tasks = [task for task in video_tasks if task.get('user_id') == user_id]
        
        return {
            'user_id': user_id,
            'total_count': len(user_tasks),
            'tasks': user_tasks
        }
        
    except Exception as e:
        print(f"[获取历史] 错误: {str(e)}")
        raise

# 测试任务
@celery_app.task(name='video.test')
def test_video_task(message: str = "Hello Video"):
    """视频模块测试任务"""
    print(f"[视频测试] 消息: {message}")
    time.sleep(1)
    return {
        'message': message,
        'module': 'video',
        'timestamp': datetime.now().isoformat()
    }
