#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Edge TTS服务 - 使用Microsoft Edge浏览器的TTS引擎提供可靠的语音合成
"""

import os
import logging
import tempfile
import time
import json
import asyncio
import traceback
import hashlib
import shutil
import uuid
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor

# 导入edge-tts
try:
    import edge_tts
    from edge_tts import VoicesManager
    from edge_tts import Communicate
    EDGE_TTS_AVAILABLE = True
except ImportError:
    EDGE_TTS_AVAILABLE = False
    logging.warning("edge-tts未安装，请使用pip install edge-tts安装")

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EdgeTTSService:
    """Edge TTS服务 - 使用Microsoft Edge TTS引擎"""
    
    def __init__(self):
        """初始化Edge TTS服务"""
        # 检查edge-tts是否可用
        if not EDGE_TTS_AVAILABLE:
            logger.error("edge-tts库未安装，服务不可用")
            self.available = False
            return
            
        # 服务状态
        self.available = True
        self.stats = {
            "requests": 0,
            "success": 0,
            "failures": 0,
            "start_time": time.time()
        }
        
        # 缓存设置
        cache_dir = os.environ.get("TTS_CACHE_DIR", None)
        if not cache_dir:
            cache_dir = os.path.join(tempfile.gettempdir(), "edge_tts_cache")
        os.makedirs(cache_dir, exist_ok=True)
        self.cache_dir = cache_dir
        
        # 语音列表 - 初始设置一些常用语音
        # 完整列表将在初始化时从API获取
        self.voices = {
            "zh-female1": {"voice": "zh-CN-XiaoxiaoNeural", "lang": "zh-CN", "gender": "female", "name": "小晓(自然)"},
            "zh-female2": {"voice": "zh-CN-XiaoyiNeural", "lang": "zh-CN", "gender": "female", "name": "小艺(温柔)"},
            "zh-male1": {"voice": "zh-CN-YunjianNeural", "lang": "zh-CN", "gender": "male", "name": "云健(男声)"},
            "en-female1": {"voice": "en-US-JennyNeural", "lang": "en-US", "gender": "female", "name": "Jenny(英语女声)"},
            "en-male1": {"voice": "en-US-GuyNeural", "lang": "en-US", "gender": "male", "name": "Guy(英语男声)"},
            "ja-female1": {"voice": "ja-JP-NanamiNeural", "lang": "ja-JP", "gender": "female", "name": "Nanami(日语女声)"},
            # 添加原生Edge TTS语音名称的映射
            "zh-CN-XiaoxiaoNeural": {"voice": "zh-CN-XiaoxiaoNeural", "lang": "zh-CN", "gender": "female", "name": "小晓(自然)"},
            "zh-CN-XiaoyiNeural": {"voice": "zh-CN-XiaoyiNeural", "lang": "zh-CN", "gender": "female", "name": "小艺(温柔)"},
            "zh-CN-YunjianNeural": {"voice": "zh-CN-YunjianNeural", "lang": "zh-CN", "gender": "male", "name": "云健(男声)"}
        }
        
        # 线程池执行器
        self.executor = ThreadPoolExecutor(max_workers=4)
        
        # 初始化标志
        self.initialized = False
        
        logger.info("Edge TTS服务初始化")
    
    async def initialize(self):
        """初始化服务并获取可用语音"""
        if self.initialized:
            return True
            
        if not EDGE_TTS_AVAILABLE:
            logger.error("edge-tts库未安装，无法初始化")
            return False
        
        try:
            # 尝试获取所有可用语音
            logger.info("获取Edge TTS可用语音...")
            
            # 使用线程池异步获取语音列表
            loop = asyncio.get_event_loop()
            voices_manager = await loop.run_in_executor(
                self.executor, 
                lambda: VoicesManager()
            )
            
            # 获取语音列表
            voices = await loop.run_in_executor(
                self.executor,
                lambda: voices_manager.voices
            )
            
            # 更新语音列表
            self._update_voices(voices)
            
            self.initialized = True
            logger.info(f"Edge TTS服务初始化成功，找到{len(self.voices)}个语音")
            return True
        except Exception as e:
            logger.error(f"初始化Edge TTS服务失败: {e}")
            logger.error(traceback.format_exc())
            # 虽然获取语音列表失败，但基本服务可能仍可用
            self.initialized = True
            return False
    
    def _update_voices(self, voices_data: List[Dict]):
        """更新语音列表"""
        # 保留原始的语音映射
        original_voices = self.voices.copy()
        
        # 创建新的语音字典
        new_voices = {}
        
        # 处理中文语音
        zh_female_voices = [v for v in voices_data if v.get("Locale") == "zh-CN" and v.get("Gender") == "Female"]
        zh_male_voices = [v for v in voices_data if v.get("Locale") == "zh-CN" and v.get("Gender") == "Male"]
        
        # 处理英文语音
        en_female_voices = [v for v in voices_data if v.get("Locale") == "en-US" and v.get("Gender") == "Female"]
        en_male_voices = [v for v in voices_data if v.get("Locale") == "en-US" and v.get("Gender") == "Male"]
        
        # 处理日语语音
        ja_female_voices = [v for v in voices_data if v.get("Locale") == "ja-JP" and v.get("Gender") == "Female"]
        ja_male_voices = [v for v in voices_data if v.get("Locale") == "ja-JP" and v.get("Gender") == "Male"]
        
        # 保留原始ID映射的同时更新语音列表
        for voice_id, config in original_voices.items():
            new_voices[voice_id] = config
        
        # 添加额外的语音
        voice_count = len(original_voices)
        
        # 添加额外中文女声
        for i, voice in enumerate(zh_female_voices):
            if i >= 2:  # 跳过已有的前两个
                voice_id = f"zh-female{i+1}"
                if voice_id not in new_voices:
                    new_voices[voice_id] = {
                        "voice": voice.get("ShortName"),
                        "lang": "zh-CN",
                        "gender": "female",
                        "name": f"{voice.get('FriendlyName')}",
                        "provider": "edge-tts"
                    }
        
        # 添加额外中文男声
        for i, voice in enumerate(zh_male_voices):
            if i >= 1:  # 跳过已有的第一个
                voice_id = f"zh-male{i+1}"
                if voice_id not in new_voices:
                    new_voices[voice_id] = {
                        "voice": voice.get("ShortName"),
                        "lang": "zh-CN",
                        "gender": "male",
                        "name": f"{voice.get('FriendlyName')}",
                        "provider": "edge-tts"
                    }
        
        # 添加额外英文语音
        for i, voice in enumerate(en_female_voices):
            if i >= 1:  # 跳过已有的第一个
                voice_id = f"en-female{i+1}"
                if voice_id not in new_voices:
                    new_voices[voice_id] = {
                        "voice": voice.get("ShortName"),
                        "lang": "en-US",
                        "gender": "female",
                        "name": f"{voice.get('FriendlyName')}",
                        "provider": "edge-tts"
                    }
        
        # 添加额外英文男声
        for i, voice in enumerate(en_male_voices):
            if i >= 1:  # 跳过已有的第一个
                voice_id = f"en-male{i+1}"
                if voice_id not in new_voices:
                    new_voices[voice_id] = {
                        "voice": voice.get("ShortName"),
                        "lang": "en-US",
                        "gender": "male",
                        "name": f"{voice.get('FriendlyName')}",
                        "provider": "edge-tts"
                    }
        
        # 更新语音字典
        self.voices = new_voices
        logger.info(f"更新Edge TTS语音列表，共{len(self.voices)}个语音")
    
    def get_available_voices(self) -> Dict:
        """
        获取可用的语音列表
        
        Returns:
            Dict: 语音ID到语音信息的映射
        """
        return self.voices
    
    def _get_cache_key(self, text: str, voice_id: str, speed: float) -> str:
        """生成缓存键"""
        # 使用文本、语音ID和速度的组合作为输入
        voice_name = self.voices.get(voice_id, {}).get("voice", voice_id)
        key_input = f"{text}_{voice_name}_{speed}"
        # 计算MD5哈希作为缓存键
        return hashlib.md5(key_input.encode('utf-8')).hexdigest()
    
    def _get_cached_audio(self, text: str, voice_id: str, speed: float) -> Optional[str]:
        """从缓存获取音频路径"""
        cache_key = self._get_cache_key(text, voice_id, speed)
        cache_path = os.path.join(self.cache_dir, f"{cache_key}.mp3")
        
        if os.path.exists(cache_path) and os.path.getsize(cache_path) > 1000:  # 文件至少1KB
            logger.info(f"在缓存中找到音频: {cache_path}")
            return cache_path
        
        return None
    
    async def text_to_speech(self, 
                           text: str, 
                           voice_id: str = None, 
                           speed: float = 1.0,
                           output_path: str = None) -> Dict[str, Any]:
        """
        将文本转换为语音
        
        Args:
            text: 要转换的文本
            voice_id: 语音ID，例如 zh-female1
            speed: 语速，默认为1.0
            output_path: 输出路径，如果为None则使用临时文件
            
        Returns:
            Dict: 包含结果的字典
        """
        start_time = time.time()
        self.stats["requests"] += 1
        
        # 检查服务是否可用
        if not EDGE_TTS_AVAILABLE:
            return {
                "success": False,
                "message": "Edge TTS库未安装，请使用pip install edge-tts安装",
                "audio_path": None,
                "from_cache": False,
                "duration": 0
            }
        
        # 验证参数
        if not text or len(text.strip()) == 0:
            logger.warning("收到空文本，将生成静音文件")
            if not output_path:
                output_path = os.path.join(tempfile.gettempdir(), f"tts_{uuid.uuid4()}.mp3")
            # 创建一个空文件
            with open(output_path, 'wb') as f:
                f.write(b'')
            return {
                "success": True,
                "message": "收到空文本，生成空文件",
                "audio_path": output_path,
                "from_cache": False,
                "duration": 0
            }
        
        # 验证语音ID
        if not voice_id or voice_id not in self.voices:
            default_voice = "zh-female1"
            logger.warning(f"无效的语音ID: {voice_id}，使用默认值: {default_voice}")
            voice_id = default_voice
        
        # 获取语音配置
        voice_config = self.voices.get(voice_id, {})
        voice_name = voice_config.get("voice", "zh-CN-XiaoxiaoNeural")
        
        # 检查缓存
        cached_path = self._get_cached_audio(text, voice_id, speed)
        if cached_path:
            # 使用缓存的音频
            if output_path:
                shutil.copy2(cached_path, output_path)
                audio_path = output_path
            else:
                audio_path = cached_path
            
            return {
                "success": True,
                "message": "使用缓存的音频",
                "audio_path": audio_path,
                "from_cache": True,
                "duration": 0  # 未计算实际持续时间
            }
        
        # 生成输出路径
        if not output_path:
            cache_key = self._get_cache_key(text, voice_id, speed)
            output_path = os.path.join(self.cache_dir, f"{cache_key}.mp3")
        
        try:
            # 设置语速参数
            rate = "+0%"
            if speed != 1.0:
                # 确保speed是浮点数类型
                try:
                    # 先检查speed的类型
                    if isinstance(speed, str):
                        # 移除可能存在的百分号和空格
                        speed_str = speed.replace('%', '').strip()
                        speed_float = float(speed_str)
                    else:
                        speed_float = float(speed)
                    
                    # Edge TTS语速范围在-100%到+100%之间
                    rate_percent = int((speed_float - 1.0) * 100)
                    rate = f"{rate_percent:+d}%"
                    logger.info(f"设置语速参数为: {rate} (原始speed: {speed}, 转换后: {speed_float})")
                except (ValueError, TypeError) as e:
                    logger.warning(f"语速参数类型错误: {speed}, {type(speed)}, 错误: {e}，使用默认语速")
                    rate = "+0%"  # 使用默认语速
            
            # 创建通信对象
            communicate = edge_tts.Communicate(text, voice_name, rate=rate)
            
            # 异步生成语音
            await communicate.save(output_path)
            
            # 验证输出文件
            if os.path.exists(output_path) and os.path.getsize(output_path) > 0:
                self.stats["success"] += 1
                return {
                    "success": True,
                    "message": "使用Edge TTS生成语音成功",
                    "audio_path": output_path,
                    "from_cache": False,
                    "duration": time.time() - start_time
                }
            else:
                self.stats["failures"] += 1
                return {
                    "success": False,
                    "message": "Edge TTS生成的文件无效或为空",
                    "audio_path": None,
                    "from_cache": False,
                    "duration": time.time() - start_time
                }
        except Exception as e:
            logger.error(f"Edge TTS生成失败: {e}")
            logger.error(traceback.format_exc())
            self.stats["failures"] += 1
            return {
                "success": False,
                "message": f"Edge TTS生成失败: {str(e)}",
                "audio_path": None,
                "from_cache": False,
                "duration": time.time() - start_time
            }
    
    async def generate_speech(self, 
                            text: str, 
                            voice_id: str = None, 
                            speed: float = 1.0,
                            output_path: str = None) -> Dict[str, Any]:
        """
        将文本转换为语音（text_to_speech的别名）
        
        此方法是为了保持与其他TTS服务接口的兼容性。
        功能与text_to_speech完全相同。
        
        Args:
            text: 要转换的文本
            voice_id: 语音ID，例如 zh-female1
            speed: 语速，默认为1.0
            output_path: 输出路径，如果为None则使用临时文件
            
        Returns:
            Dict: 包含结果的字典
        """
        # 调用text_to_speech获取结果
        result = await self.text_to_speech(text, voice_id, speed, output_path)
        
        # 转换结果格式以匹配tts_service.py期望的格式
        converted_result = result.copy()
        
        # 将success布尔值转换为status字符串
        if "success" in result:
            if result["success"]:
                converted_result["status"] = "success"
            else:
                converted_result["status"] = "error"
        
        # 添加message到错误信息
        if "message" in result and not result["success"]:
            converted_result["message"] = result["message"]
        
        # 确保路径字段格式统一
        if "audio_path" in result and result["audio_path"] and "path" not in converted_result:
            converted_result["path"] = result["audio_path"]
        
        # 添加额外信息
        converted_result["engine"] = "edge-tts"
        converted_result["offline"] = False
        
        return converted_result
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """
        获取性能统计信息
        
        Returns:
            Dict: 性能统计数据
        """
        uptime = time.time() - self.stats["start_time"]
        
        return {
            "requests": self.stats["requests"],
            "success": self.stats["success"],
            "failures": self.stats["failures"],
            "success_rate": self.stats["success"] / max(1, self.stats["requests"]),
            "uptime_seconds": uptime,
            "engine": "edge-tts"
        }

# 单例模式
_edge_tts_instance = None

def get_edge_tts_service() -> EdgeTTSService:
    """获取Edge TTS服务实例（单例模式）"""
    global _edge_tts_instance
    if _edge_tts_instance is None:
        _edge_tts_instance = EdgeTTSService()
    return _edge_tts_instance

async def initialize() -> bool:
    """初始化Edge TTS服务"""
    try:
        service = get_edge_tts_service()
        return await service.initialize()
    except Exception as e:
        logger.error(f"初始化Edge TTS服务失败: {e}")
        logger.error(traceback.format_exc())
        return False 