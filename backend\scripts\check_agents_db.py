#!/usr/bin/env python3
"""
检查智能体数据库
"""
import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# 添加后端路径
backend_dir = Path(__file__).parent.parent
sys.path.append(str(backend_dir))

# 加载环境变量
env_path = backend_dir / ".env"
load_dotenv(env_path)

def check_agents_table():
    """检查智能体表"""
    print("🔍 检查智能体数据库表")
    print("=" * 50)
    
    try:
        from app.core.database import get_db_manager
        
        db_manager = get_db_manager()
        
        # 检查表是否存在
        tables = db_manager.execute_query(
            "SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name LIKE '%agent%'"
        )
        
        print(f"📊 智能体相关表:")
        for table in tables:
            table_name = table['table_name']
            print(f"   ✅ {table_name}")
            
            # 获取表结构
            columns = db_manager.execute_query(
                f"SELECT column_name, data_type FROM information_schema.columns WHERE table_name = '{table_name}'"
            )
            print(f"      字段: {[col['column_name'] for col in columns]}")
            
            # 获取记录数
            count = db_manager.execute_query(f"SELECT COUNT(*) as count FROM {table_name}")
            print(f"      记录数: {count[0]['count']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查智能体表失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_specific_agent():
    """检查特定智能体"""
    print(f"\n🔍 检查特定智能体")
    print("=" * 50)
    
    agent_id = "e55f5e84-6d8b-4265-8e55-728bdb0d2455"
    
    try:
        from app.core.database import get_db_manager
        
        db_manager = get_db_manager()
        
        # 查询特定智能体
        agents = db_manager.execute_query(
            "SELECT * FROM true_agents WHERE id = %s",
            [agent_id]
        )
        
        print(f"📊 查询智能体 {agent_id}:")
        if agents:
            agent = agents[0]
            print(f"   ✅ 找到智能体:")
            print(f"      ID: {agent.get('id')}")
            print(f"      名称: {agent.get('name')}")
            print(f"      类型: {agent.get('agent_type')}")
            print(f"      状态: {agent.get('is_active')}")
            print(f"      创建时间: {agent.get('created_at')}")
        else:
            print(f"   ❌ 未找到智能体")
            
            # 查询所有智能体
            all_agents = db_manager.execute_query("SELECT id, name, agent_type, is_active FROM true_agents LIMIT 10")
            print(f"\n📊 数据库中的智能体:")
            if all_agents:
                for agent in all_agents:
                    print(f"      {agent.get('id')} - {agent.get('name')} ({agent.get('agent_type')})")
            else:
                print(f"      数据库中没有任何智能体")
        
        return len(agents) > 0
        
    except Exception as e:
        print(f"❌ 检查特定智能体失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_agent_api():
    """测试智能体 API"""
    print(f"\n🧪 测试智能体 API")
    print("=" * 50)
    
    agent_id = "e55f5e84-6d8b-4265-8e55-728bdb0d2455"
    
    try:
        import requests
        
        # 测试获取智能体详情
        url = f"http://localhost:8000/api/v1/agents/{agent_id}"
        print(f"📊 请求 URL: {url}")
        
        response = requests.get(url)
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API 调用成功:")
            print(f"   成功: {data.get('success')}")
            if data.get('success'):
                agent = data.get('agent', {})
                print(f"   智能体名称: {agent.get('name')}")
                print(f"   智能体类型: {agent.get('agent_type')}")
                print(f"   智能体状态: {agent.get('status')}")
            else:
                print(f"   错误: {data.get('error')}")
        else:
            print(f"❌ API 调用失败: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   错误详情: {error_data}")
            except:
                print(f"   错误内容: {response.text}")
        
        return response.status_code == 200
        
    except Exception as e:
        print(f"❌ 测试 API 失败: {e}")
        return False

def create_default_agent():
    """创建默认智能体"""
    print(f"\n🔧 创建默认智能体")
    print("=" * 50)
    
    try:
        from app.core.database import get_db_manager
        import uuid
        from datetime import datetime
        
        db_manager = get_db_manager()
        
        # 创建默认智能体
        agent_data = {
            "id": "e55f5e84-6d8b-4265-8e55-728bdb0d2455",
            "name": "英语学习助手",
            "description": "专业的英语学习智能体，帮助您提高英语水平，包括语法检查、发音练习、词汇测试等功能。",
            "agent_type": "language_tutor",
            "system_prompt": "你是一个专业的英语学习助手，擅长语法检查、发音指导和词汇教学。",
            "tools": [
                {"type": "translator", "name": "翻译工具"},
                {"type": "grammar_checker", "name": "语法检查"},
                {"type": "pronunciation", "name": "发音练习"}
            ],
            "knowledge_bases": [],
            "memory_types": ["short_term"],
            "workflow_enabled": False,
            "workflow_config": {},
            "max_tokens": 4000,
            "temperature": 0.7,
            "success_rate": 0.85,
            "usage_count": 0,
            "is_active": True,
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow()
        }
        
        # 检查是否已存在
        existing = db_manager.execute_query(
            "SELECT id FROM true_agents WHERE id = %s",
            [agent_data["id"]]
        )
        
        if existing:
            print(f"✅ 智能体已存在，更新数据")
            # 更新现有智能体
            db_manager.execute_query(
                """UPDATE true_agents SET 
                   name = %s, description = %s, agent_type = %s, 
                   system_prompt = %s, tools = %s, is_active = %s, 
                   updated_at = %s
                   WHERE id = %s""",
                [
                    agent_data["name"], agent_data["description"], agent_data["agent_type"],
                    agent_data["system_prompt"], agent_data["tools"], agent_data["is_active"],
                    agent_data["updated_at"], agent_data["id"]
                ]
            )
        else:
            print(f"🔧 创建新智能体")
            # 创建新智能体
            db_manager.execute_query(
                """INSERT INTO true_agents 
                   (id, name, description, agent_type, system_prompt, tools, 
                    knowledge_bases, memory_types, workflow_enabled, workflow_config,
                    max_tokens, temperature, success_rate, usage_count, 
                    is_active, created_at, updated_at)
                   VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)""",
                [
                    agent_data["id"], agent_data["name"], agent_data["description"],
                    agent_data["agent_type"], agent_data["system_prompt"], agent_data["tools"],
                    agent_data["knowledge_bases"], agent_data["memory_types"], 
                    agent_data["workflow_enabled"], agent_data["workflow_config"],
                    agent_data["max_tokens"], agent_data["temperature"], 
                    agent_data["success_rate"], agent_data["usage_count"],
                    agent_data["is_active"], agent_data["created_at"], agent_data["updated_at"]
                ]
            )
        
        print(f"✅ 默认智能体创建/更新成功")
        return True
        
    except Exception as e:
        print(f"❌ 创建默认智能体失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🤖 智能体数据库检查和修复")
    print("=" * 60)
    
    # 1. 检查智能体表
    table_ok = check_agents_table()
    
    # 2. 检查特定智能体
    agent_exists = check_specific_agent()
    
    # 3. 如果智能体不存在，创建默认智能体
    if not agent_exists:
        create_ok = create_default_agent()
        if create_ok:
            # 重新检查
            agent_exists = check_specific_agent()
    
    # 4. 测试 API
    api_ok = test_agent_api()
    
    # 总结
    print(f"\n📊 检查总结:")
    print(f"{'✅' if table_ok else '❌'} 数据库表: {'正常' if table_ok else '异常'}")
    print(f"{'✅' if agent_exists else '❌'} 智能体存在: {'是' if agent_exists else '否'}")
    print(f"{'✅' if api_ok else '❌'} API 测试: {'成功' if api_ok else '失败'}")
    
    if api_ok:
        print(f"\n🎉 智能体系统正常！")
        print(f"💡 现在可以在前端正常使用智能体聊天功能")
    else:
        print(f"\n⚠️ 智能体系统仍有问题")

if __name__ == "__main__":
    main()
