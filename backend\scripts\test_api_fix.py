#!/usr/bin/env python3
"""
测试 API 修复效果
"""
import asyncio
import httpx
import time

async def test_chat_api():
    """测试聊天 API"""
    print("🧪 测试聊天 API 修复效果")
    print("=" * 50)
    
    agent_id = "e55f5e84-6d8b-4265-8e55-728bdb0d2455"
    test_messages = [
        "Hello, how are you?",
        "What does 'policewoman' mean?",
        "Can you help me with English grammar?",
        "I want to practice conversation"
    ]
    
    for i, message in enumerate(test_messages, 1):
        print(f"\n{i}️⃣ 测试消息: {message}")
        print("-" * 40)
        
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                url = f"http://localhost:8000/api/v1/agents/{agent_id}/chat"
                data = {
                    "message": message,
                    "user_id": "test-user"
                }
                
                start_time = time.time()
                response = await client.post(url, json=data)
                end_time = time.time()
                
                duration = (end_time - start_time) * 1000
                
                print(f"⏱️ 响应时间: {duration:.0f}ms")
                print(f"📊 状态码: {response.status_code}")
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get("success"):
                        api_response = result.get("response", "")
                        print(f"📝 回复: {api_response}")
                        print(f"📊 回复长度: {len(api_response)} 字符")
                        
                        # 判断回复质量
                        if duration > 1000:  # 超过1秒说明可能调用了 Ollama
                            print(f"✅ 可能使用了 Ollama（响应时间较长）")
                        else:
                            print(f"⚠️ 可能使用了预设回复（响应时间较短）")
                            
                        if len(api_response) > 100:
                            print(f"✅ 回复较详细，质量较好")
                        elif len(api_response) > 50:
                            print(f"🔶 回复一般")
                        else:
                            print(f"❌ 回复过于简短")
                    else:
                        print(f"❌ API 返回错误: {result.get('error')}")
                else:
                    print(f"❌ HTTP 错误: {response.status_code}")
                    print(f"📊 错误内容: {response.text}")
                    
        except Exception as e:
            print(f"❌ 测试失败: {e}")
    
    print(f"\n💡 分析:")
    print(f"- 如果响应时间 > 1000ms，说明可能调用了 Ollama")
    print(f"- 如果回复长度 > 100 字符，说明质量较好")
    print(f"- 如果回复内容相关且详细，说明 AI 服务正常工作")

if __name__ == "__main__":
    asyncio.run(test_chat_api())
