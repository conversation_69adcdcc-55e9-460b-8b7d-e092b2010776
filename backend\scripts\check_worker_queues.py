#!/usr/bin/env python3
"""
检查 Worker 队列监听
"""
import os
import sys
import time
import json
from pathlib import Path
from dotenv import load_dotenv

# 添加后端路径
backend_dir = Path(__file__).parent.parent
sys.path.append(str(backend_dir))

# 加载环境变量
env_path = backend_dir / ".env"
load_dotenv(env_path)

def check_worker_queues():
    """检查 Worker 监听的队列"""
    print("🔍 检查 Worker 监听的队列")
    print("=" * 50)
    
    try:
        from app.core.celery_app import celery_app
        
        inspect = celery_app.control.inspect()
        
        # 获取活跃队列
        active_queues = inspect.active_queues()
        if active_queues:
            print(f"✅ Worker 活跃队列:")
            for worker, queues in active_queues.items():
                print(f"   {worker}:")
                for queue in queues:
                    queue_name = queue.get('name', 'N/A')
                    exchange = queue.get('exchange', {}).get('name', 'N/A')
                    routing_key = queue.get('routing_key', 'N/A')
                    print(f"     - {queue_name} (exchange: {exchange}, key: {routing_key})")
            return True
        else:
            print(f"❌ 无法获取 Worker 队列信息")
            return False
        
    except Exception as e:
        print(f"❌ 检查 Worker 队列失败: {e}")
        return False

def manually_add_task_to_queue():
    """手动添加任务到特定队列"""
    print(f"\n🔧 手动添加任务到队列")
    print("=" * 50)
    
    try:
        import redis
        import uuid
        
        r = redis.Redis(host='localhost', port=6379, db=0)
        
        # 创建一个符合 Celery 格式的任务
        task_id = str(uuid.uuid4())
        
        # 使用正确的 Celery 消息格式
        message = {
            "body": [[], {}, {"callbacks": None, "errbacks": None, "chain": None, "chord": None}],
            "content-encoding": "utf-8",
            "content-type": "application/json",
            "headers": {
                "lang": "py",
                "task": "test_unified_task",
                "id": task_id,
                "shadow": None,
                "eta": None,
                "expires": None,
                "group": None,
                "group_index": None,
                "retries": 0,
                "timelimit": [None, None],
                "root_id": task_id,
                "parent_id": None,
                "argsrepr": "()",
                "kwargsrepr": "{}",
                "origin": "gen1@test-machine"
            },
            "properties": {
                "correlation_id": task_id,
                "reply_to": task_id,
                "delivery_mode": 2,
                "delivery_info": {"exchange": "", "routing_key": "test"},
                "priority": 0,
                "body_encoding": "base64",
                "delivery_tag": "test-tag"
            }
        }
        
        # 推送到 test 队列
        message_json = json.dumps(message)
        r.lpush("test", message_json)
        
        print(f"✅ 手动添加正确格式的任务到 test 队列")
        print(f"📊 任务ID: {task_id}")
        print(f"📊 队列长度: {r.llen('test')}")
        
        # 等待 Worker 处理
        print(f"⏳ 等待 10 秒看 Worker 是否处理...")
        for i in range(10):
            time.sleep(1)
            length = r.llen('test')
            print(f"   [{i+1:2d}s] test 队列长度: {length}")
            
            if length == 0:
                print(f"✅ 任务被 Worker 处理了！")
                return True
        
        print(f"❌ 任务没有被处理")
        return False
        
    except Exception as e:
        print(f"❌ 手动添加任务失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_celery_send():
    """测试 Celery 发送机制"""
    print(f"\n🚀 测试 Celery 发送机制")
    print("=" * 50)
    
    try:
        from app.core.celery_app import celery_app
        
        # 使用 send_task 发送任务
        result = celery_app.send_task(
            'test_unified_task',
            args=[],
            kwargs={
                'task_id': 'send-test',
                'test_message': 'send_task 测试',
                'duration': 1
            },
            queue='test'
        )
        
        print(f"✅ send_task 调用成功")
        print(f"📊 任务ID: {result.id}")
        print(f"📊 初始状态: {result.status}")
        
        # 检查队列
        import redis
        r = redis.Redis(host='localhost', port=6379, db=0)
        queue_length = r.llen('test')
        print(f"📊 test 队列长度: {queue_length}")
        
        # 等待处理
        print(f"⏳ 等待 10 秒看状态变化...")
        for i in range(10):
            time.sleep(1)
            status = result.status
            queue_length = r.llen('test')
            print(f"   [{i+1:2d}s] 状态: {status}, 队列长度: {queue_length}")
            
            if status != 'PENDING':
                print(f"✅ 状态发生变化: {status}")
                return True
        
        print(f"❌ 状态没有变化")
        return False
        
    except Exception as e:
        print(f"❌ send_task 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_celery_routes():
    """检查 Celery 路由"""
    print(f"\n🔍 检查 Celery 路由")
    print("=" * 50)
    
    try:
        from app.core.celery_app import celery_app
        
        print(f"📊 任务路由配置:")
        if hasattr(celery_app.conf, 'task_routes'):
            for pattern, config in celery_app.conf.task_routes.items():
                print(f"   {pattern} -> {config}")
        
        # 测试路由解析
        print(f"\n📊 测试路由解析:")
        test_tasks = [
            'test_unified_task',
            'app.tasks.test_unified_task.test_unified_task',
            'wanx_text_to_video',
            'app.tasks.wanx_video_tasks.wanx_text_to_video'
        ]
        
        for task_name in test_tasks:
            try:
                route = celery_app.conf.task_routes.get(task_name)
                if not route:
                    # 检查模式匹配
                    for pattern, config in celery_app.conf.task_routes.items():
                        if '*' in pattern:
                            pattern_prefix = pattern.replace('*', '')
                            if task_name.startswith(pattern_prefix):
                                route = config
                                break
                
                print(f"   {task_name} -> {route or 'default'}")
            except Exception as e:
                print(f"   {task_name} -> 错误: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查路由失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 Worker 队列监听检查")
    print("=" * 60)
    
    # 1. 检查 Worker 监听的队列
    queues_ok = check_worker_queues()
    
    # 2. 检查 Celery 路由
    routes_ok = check_celery_routes()
    
    # 3. 测试 Celery 发送机制
    send_ok = test_celery_send()
    
    # 4. 手动添加任务测试
    manual_ok = manually_add_task_to_queue()
    
    # 总结
    print(f"\n📊 检查总结:")
    print(f"{'✅' if queues_ok else '❌'} Worker 队列: {'正常' if queues_ok else '异常'}")
    print(f"{'✅' if routes_ok else '❌'} 路由配置: {'正常' if routes_ok else '异常'}")
    print(f"{'✅' if send_ok else '❌'} Celery 发送: {'成功' if send_ok else '失败'}")
    print(f"{'✅' if manual_ok else '❌'} 手动添加: {'成功' if manual_ok else '失败'}")
    
    if manual_ok:
        print(f"\n🎉 Worker 能处理正确格式的任务！")
        print(f"💡 问题在于 Celery 发送的消息格式")
    elif queues_ok and routes_ok:
        print(f"\n✅ 配置看起来正确")
        print(f"❌ 但 Worker 没有处理任务")
        print(f"💡 可能的问题:")
        print(f"   1. Worker 没有真正监听队列")
        print(f"   2. 消息格式不正确")
        print(f"   3. Worker 进程有问题")
    else:
        print(f"\n❌ 配置有问题")

if __name__ == "__main__":
    main()
