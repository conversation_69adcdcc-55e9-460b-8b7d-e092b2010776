import axios from 'axios';
import { message } from 'ant-design-vue';
import { getToken } from '@/utils/auth';

// 创建axios实例
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/admin-api',
  timeout: 15000,
  headers: {
    'Content-Type': 'application/json',
  }
});

// 请求拦截器
api.interceptors.request.use(
  config => {
    const token = getToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  error => {
    console.error('请求错误:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  response => {
    // 如果接口规范定义了统一的数据格式，可以在这里处理
    const res = response.data;
    
    // 假设接口返回格式为 { code: 0, data: {}, message: '' }
    if (res.code !== 0) {
      message.error(res.message || '操作失败');
      
      // 特定状态码处理
      if (res.code === 401) {
        // 登录过期或未登录
        setTimeout(() => {
          window.location.href = '/admin/auth/login';
        }, 1500);
      }
      
      return Promise.reject(new Error(res.message || '操作失败'));
    }
    
    return res.data;
  },
  error => {
    const { response } = error;
    
    if (response && response.status) {
      const errorMsg = response.data?.message || '请求失败';
      
      switch (response.status) {
        case 400:
          message.error(`请求参数错误: ${errorMsg}`);
          break;
        case 401:
          message.error('未授权，请重新登录');
          // 跳转到登录页
          setTimeout(() => {
            window.location.href = '/admin/auth/login';
          }, 1500);
          break;
        case 403:
          message.error(`权限不足: ${errorMsg}`);
          break;
        case 404:
          message.error(`请求的资源不存在: ${errorMsg}`);
          break;
        case 500:
          message.error(`服务器错误: ${errorMsg}`);
          break;
        default:
          message.error(`未知错误 (${response.status}): ${errorMsg}`);
      }
    } else {
      // 处理网络错误或请求被取消
      if (error.message.includes('timeout')) {
        message.error('请求超时，请检查网络连接');
      } else if (error.message.includes('Network Error')) {
        message.error('网络错误，请检查网络连接');
      } else {
        message.error(error.message || '发生未知错误');
      }
    }
    
    return Promise.reject(error);
  }
);

// 封装GET请求
export function get(url, params) {
  return api.get(url, { params });
}

// 封装POST请求
export function post(url, data) {
  return api.post(url, data);
}

// 封装PUT请求
export function put(url, data) {
  return api.put(url, data);
}

// 封装DELETE请求
export function del(url) {
  return api.delete(url);
}

export default api; 