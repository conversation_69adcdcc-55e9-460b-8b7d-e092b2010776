"""
文档翻译相关的Celery任务
"""
import os
import time
import asyncio
import logging
import json
import shutil
from typing import Dict, Any, List, Optional
from celery import shared_task
from datetime import datetime

# 导入服务和工具
from services.celery_app import app
from services.document_translation_service import document_translation_service
from services.progress_tracker import get_progress_tracker
from services.progress_updater import ProgressUpdater
from .base_task import BaseTask, task_progress
from utils.task_persistence import TaskPersistenceManager

# 配置日志
logger = logging.getLogger(__name__)

@shared_task(name="document_translation.process_document", bind=True, max_retries=2, base=BaseTask)
def process_document_task(
    self,
    task_id: str,
    file_path: str,
    source_language: str,
    target_language: str,
    domain: str = "general",
    style: str = "standard",
    advanced_options: Optional[List[str]] = None,
    glossary_id: Optional[str] = None,
    user_id: Optional[int] = None
) -> Dict[str, Any]:
    """
    文档翻译任务
    
    参数:
        task_id: 任务ID
        file_path: 文件路径
        source_language: 源语言
        target_language: 目标语言
        domain: 领域
        style: 风格
        advanced_options: 高级选项
        glossary_id: 术语表ID
        user_id: 用户ID
        
    返回:
        任务结果
    """
    start_time = time.time()
    success = False
    error_message = None
    loop = None
    
    logger.info(f"开始执行文档翻译任务: {self.name}[{self.request.id}], 任务ID: {task_id}")
    
    try:
        # 初始化服务
        from services.document_translation_service import document_translation_service
        
        # 使用BaseTask的方法更新任务状态
        self.update_progress(task_id, 5, "正在准备文档翻译...")
        
        # 创建异步事件循环
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        # 检查文件是否存在
        if not os.path.exists(file_path):
            error_message = "文档文件不存在或已被删除"
            logger.error(f"文档文件不存在: {file_path}")
            self.update_progress(task_id, 0, error_message)
            return {
                "success": False,
                "error": error_message,
                "task_id": task_id
            }
            
        # 检查文件大小
        file_size = os.path.getsize(file_path)
        if file_size > 50 * 1024 * 1024:  # 50MB
            error_message = f"文件过大: {file_size / (1024 * 1024):.2f}MB，超过50MB限制"
            logger.error(error_message)
            self.update_progress(task_id, 0, error_message)
            return {
                "success": False,
                "error": error_message,
                "task_id": task_id
            }
        
        # 提取文件类型
        file_ext = os.path.splitext(file_path)[1].lower()
        if not file_ext:
            error_message = f"无法识别文件类型: {file_path}"
            logger.error(error_message)
            self.update_progress(task_id, 0, error_message)
            return {
                "success": False,
                "error": error_message,
                "task_id": task_id
            }
        
        # 更新任务状态 - 开始解析文档
        self.update_progress(task_id, 10, f"开始处理文档: {os.path.basename(file_path)}")
        
        # 解析文档内容
        try:
            # 使用同步方法解析文档内容
            document_content = document_translation_service.extract_document_content_sync(
                file_path, 
                file_ext.lstrip('.')
            )
            
            # 计算文档字数
            word_count = 0
            for item in document_content:
                if item["type"] == "paragraph" and "text" in item:
                    word_count += len(item["text"].split())
                elif item["type"] == "table" and "data" in item:
                    for row in item["data"]:
                        for cell in row:
                            if isinstance(cell, str):
                                word_count += len(cell.split())
            
            # 确保源语言设置正确
            if source_language == "auto" or not source_language:
                # 如果是自动检测，设置为默认语言（中文）
                source_language = "zh-CN"
                logger.info(f"源语言自动检测设置为: {source_language}")
            
            # 成功解析文档内容，更新进度
            self.update_progress(task_id, 30, "文档解析完成，准备翻译")
        except Exception as extract_error:
            error_message = f"解析文档失败: {str(extract_error)}"
            logger.error(error_message, exc_info=True)
            self.update_progress(task_id, 0, error_message)
            return {
                "success": False,
                "error": error_message,
                "task_id": task_id
            }
        
        # 检查解析结果
        if not document_content:
            error_message = "文档解析失败，未能提取出内容"
            self.update_progress(task_id, 0, error_message)
            return {
                "success": False,
                "error": error_message,
                "task_id": task_id
            }
        
        # 更新任务状态 - 翻译阶段
        self.update_progress(task_id, 40, "文档解析完成，正在翻译文档内容")
        
        # 翻译文档内容
        try:
            # 使用同步方法翻译文档内容
            translated_content = document_translation_service.translate_document_content_sync(
                document_content, 
                source_language, 
                target_language,
                domain,
                style,
                task_id
            )
            
            # 成功翻译文档内容，更新进度
            self.update_progress(task_id, 70, "文档内容翻译完成，正在生成翻译文档")
        except Exception as translate_error:
            error_message = f"翻译文档内容失败: {str(translate_error)}"
            logger.error(error_message, exc_info=True)
            self.update_progress(task_id, 0, error_message)
            return {
                "success": False,
                "error": error_message,
                "task_id": task_id
            }
        
        # 生成翻译后的文档
        try:
            # 使用同步方法生成翻译后的文档
            translated_file_path = document_translation_service.generate_translated_document_sync(
                file_path, 
                translated_content,
                task_id,
                target_language,
                source_language
            )
            
            # 成功生成翻译后的文档，更新进度
            self.update_progress(task_id, 90, "翻译文档生成完成，正在生成预览")
        except Exception as generate_error:
            error_message = f"生成翻译文档失败: {str(generate_error)}"
            logger.error(error_message, exc_info=True)
            self.update_progress(task_id, 0, error_message)
            return {
                "success": False,
                "error": error_message,
                "task_id": task_id
            }
        
        # 生成预览
        try:
            # 使用同步方法生成预览
            preview_data = document_translation_service.generate_preview_from_content_sync(document_content, translated_content)
            preview_path = os.path.join("temp", "previews", f"{task_id}_preview.json")
            os.makedirs(os.path.dirname(preview_path), exist_ok=True)
            with open(preview_path, "w", encoding="utf-8") as f:
                json.dump(preview_data, f, ensure_ascii=False, indent=2)
                
            # 确保结果目录存在
            os.makedirs("results/translations", exist_ok=True)
            
            # 将翻译后的文件复制到结果目录
            result_file_path = f"results/translations/{task_id}_{os.path.basename(translated_file_path)}"
            shutil.copy2(translated_file_path, result_file_path)
            
            # 完成所有处理后，更新状态为已完成
            processing_time = time.time() - start_time
            success = True
            
            # 构建结果数据
            result_data = {
                "translated_document": result_file_path,
                "preview_path": preview_path,
                "word_count": word_count,
                "source_language": source_language,
                "target_language": target_language,
                "file_type": file_ext.lstrip('.'),
                "original_file": file_path
            }
            
            # 更新任务完成状态
            self.update_progress(
                task_id, 
                100, 
                "文档翻译任务完成",
                result_url=result_file_path,
                output_data=result_data
            )
            
            # 确保任务状态为成功，清除任何错误信息
            TaskPersistenceManager.update_task_result_async(task_id, result_data)
            
            self._update_task_status(
                task_id=task_id,
                status="success",
                progress=100,
                message="任务执行成功: document_translation.process_document",
                result_url=result_file_path,
                error=None  # 显式设置为None，确保清除错误信息
            )
            
            # 记录任务完成日志
            logger.info(f"文档翻译任务完成: {task_id}, 用时: {processing_time:.2f}秒")
            
            return {
                "success": True,
                "task_id": task_id,
                "result": result_data
            }
        except Exception as preview_error:
            error_message = f"生成预览失败: {str(preview_error)}"
            logger.error(error_message, exc_info=True)
            self.update_progress(task_id, 0, error_message)
            return {
                "success": False,
                "error": error_message,
                "task_id": task_id
            }
        
    except Exception as e:
        error_message = str(e)
        logger.exception(f"文档翻译任务异常: {task_id}, 错误: {e}")
        
        try:
            # 更新错误状态
            self.update_progress(task_id, 0, f"文档翻译失败: {error_message}")
        except Exception as update_error:
            logger.error(f"更新任务状态失败: {update_error}")
        
        # 尝试重试任务
        retry_count = self.request.retries
        if retry_count < self.max_retries:
            logger.info(f"尝试重试文档翻译任务: {task_id}, 当前重试次数: {retry_count + 1}")
            self.retry(exc=e, countdown=60)  # 1分钟后重试
        
        return {
            "success": False,
            "error": error_message,
            "task_id": task_id
        }
    finally:
        # 确保关闭事件循环
        try:
            if loop is not None:
                loop.close()
        except Exception as close_error:
            logger.error(f"关闭事件循环失败: {close_error}")
        
        # 记录任务完成时间
        processing_time = time.time() - start_time
        logger.info(f"完成任务: {self.name}[{self.request.id}], 任务ID: {task_id}, 状态: {'SUCCESS' if success else 'FAILURE'}, 用时: {processing_time:.2f}秒")

@shared_task(name="document_translation.delete_expired_files", bind=True)
def delete_expired_files_task(self) -> Dict[str, Any]:
    """
    定期清理过期的文档翻译文件任务
    """
    start_time = time.time()
    deleted_count = 0
    
    try:
        # 调用文档翻译服务中的清理方法
        if hasattr(document_translation_service, 'clean_expired_files_sync'):
            deleted_count = document_translation_service.clean_expired_files_sync(days=30)
            
        processing_time = time.time() - start_time
        logger.info(f"文档翻译文件清理完成, 删除了 {deleted_count} 个文件, 用时: {processing_time:.2f}秒")
        
        return {"success": True, "deleted_count": deleted_count, "processing_time": processing_time}
    except Exception as e:
        logger.exception(f"清理过期文件失败: {e}")
        return {"success": False, "error": str(e)} 