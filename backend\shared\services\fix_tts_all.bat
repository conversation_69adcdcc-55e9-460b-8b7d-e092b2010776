@echo off
echo ========================================
echo TTS系统全面修复工具
echo ========================================
echo 此脚本将执行全面的TTS修复流程，包括:
echo  1. 修复配置文件
echo  2. 清除TTS缓存
echo  3. 修复模型路径问题
echo  4. 下载缺失的模型
echo  5. 应用系统补丁
echo  6. 修复路径问题
echo.

REM 设置工作目录
cd /d "%~dp0"

REM 检查Python环境
python --version > nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo [错误] Python未安装或未添加到PATH环境变量
    echo 请安装Python 3.8或更高版本
    echo 下载地址: https://www.python.org/downloads/
    echo.
    pause
    exit /b
)

echo [步骤1] 修复YourTTS配置文件...
python fix_your_tts_config.py
if %ERRORLEVEL% NEQ 0 (
    echo [警告] YourTTS配置文件修复可能未完全成功，继续下一步...
) else (
    echo [成功] YourTTS配置文件修复完成
)

echo.
echo [步骤2] 检查TTS库安装...
python -c "import TTS" > nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo TTS库未安装，正在安装...
    pip install TTS
    if %ERRORLEVEL% NEQ 0 (
        echo [错误] TTS库安装失败，请手动安装:
        echo pip install TTS
        echo.
        pause
        exit /b
    )
    echo [成功] TTS库安装完成
) else (
    echo [成功] TTS库已安装
)

echo.
echo [步骤3] 清除TTS缓存并修复模型路径...
python fix_tts_model_paths.py
if %ERRORLEVEL% NEQ 0 (
    echo [警告] TTS模型路径修复可能未完全成功，继续下一步...
) else (
    echo [成功] TTS模型路径修复完成
)

echo.
echo [步骤4] 下载TTS模型...
echo 这将下载所需的TTS模型文件，可能需要较长时间
echo.
python download_tts_models.py --all
if %ERRORLEVEL% NEQ 0 (
    echo [警告] 模型下载可能未完全成功，继续下一步...
) else (
    echo [成功] 模型下载完成
)

echo.
echo [步骤5] 再次修复模型路径...
python fix_tts_model_paths.py
if %ERRORLEVEL% NEQ 0 (
    echo [警告] TTS模型路径再次修复可能未完全成功
) else (
    echo [成功] TTS模型路径再次修复完成
)

echo.
echo [步骤6] 应用TTS修复补丁...
python -c "from offline_tts_service_fix import apply_fixes; apply_fixes(force=True)"
if %ERRORLEVEL% NEQ 0 (
    echo [警告] TTS修复补丁应用可能未完全成功
) else (
    echo [成功] TTS修复补丁应用完成
)

echo.
echo [步骤7] 创建模型目录结构...
mkdir ..\models\tts 2>nul
mkdir ..\models\tts\tts 2>nul
mkdir ..\models\tts\tts\tts-models--zh-CN--baker--tacotron2-DDC-GST 2>nul
mkdir ..\models\tts\tts\tts-models--en--ljspeech--tacotron2-DDC 2>nul
mkdir ..\models\tts\tts\tts-models--multilingual--multi-dataset--your_tts 2>nul
echo [成功] 模型目录结构创建完成

echo.
echo [步骤8] 创建音频保存目录...
mkdir ..\data\digital_humans 2>nul
echo [成功] 音频保存目录创建完成

echo.
echo ========================================
echo TTS系统修复完成
echo ========================================
echo 如果系统仍然无法正常工作，请尝试重启应用程序
echo 或联系技术支持以获取进一步帮助
echo.
pause 