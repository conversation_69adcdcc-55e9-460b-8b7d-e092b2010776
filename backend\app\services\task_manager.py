"""
任务管理服务
提供统一的任务状态查询和管理功能
"""

import logging
from typing import Dict, Any, Optional, List
from datetime import datetime
from sqlalchemy.orm import Session

from app.core.celery_unified import celery_app
from app.core.sqlalchemy_db import get_db
from app.models.digital_human import DigitalHumanGeneration

logger = logging.getLogger(__name__)

class TaskManager:
    """任务管理器"""
    
    def __init__(self):
        self.celery_app = celery_app
    
    def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """
        获取任务状态
        
        Args:
            task_id: 任务ID
            
        Returns:
            任务状态信息
        """
        try:
            # 从数据库获取任务信息
            db = next(get_db())
            try:
                task_record = db.query(DigitalHumanGeneration).filter(
                    DigitalHumanGeneration.task_id == task_id
                ).first()
                
                if not task_record:
                    return {
                        "success": False,
                        "error": "任务不存在",
                        "task_id": task_id
                    }
                
                # 从Celery获取任务状态
                celery_task = self.celery_app.AsyncResult(task_record.celery_task_id or task_id)
                celery_state = celery_task.state
                celery_info = celery_task.info or {}
                
                # 合并状态信息
                status_info = {
                    "success": True,
                    "digital_human_id": task_record.digital_human_id,
                    "task_id": task_id,
                    "status": task_record.status,
                    "progress": task_record.progress or 0,
                    "message": task_record.message or "处理中...",
                    "celery_state": celery_state,
                    "start_time": task_record.start_time.isoformat() if task_record.start_time else None,
                    "updated_at": task_record.updated_at.isoformat() if task_record.updated_at else None,
                }
                
                # 如果任务完成，添加结果信息
                if task_record.status == "completed" and task_record.result_urls:
                    status_info["result_urls"] = task_record.result_urls
                
                # 如果任务失败，添加错误信息
                if task_record.status == "failed":
                    status_info["error_message"] = task_record.error_message
                
                # 从Celery获取额外信息
                if isinstance(celery_info, dict):
                    status_info.update({
                        "celery_progress": celery_info.get("progress", 0),
                        "celery_message": celery_info.get("message", ""),
                        "celery_timestamp": celery_info.get("timestamp", "")
                    })
                
                return status_info
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"获取任务状态失败: {e}")
            return {
                "success": False,
                "error": f"获取状态失败: {str(e)}",
                "task_id": task_id
            }
    
    def list_tasks(self, digital_human_id: Optional[int] = None, 
                   status: Optional[str] = None, 
                   limit: int = 50) -> List[Dict[str, Any]]:
        """
        列出任务
        
        Args:
            digital_human_id: 数字人ID（可选）
            status: 任务状态（可选）
            limit: 返回数量限制
            
        Returns:
            任务列表
        """
        try:
            db = next(get_db())
            try:
                query = db.query(DigitalHumanGeneration)
                
                if digital_human_id:
                    query = query.filter(DigitalHumanGeneration.digital_human_id == digital_human_id)
                
                if status:
                    query = query.filter(DigitalHumanGeneration.status == status)
                
                tasks = query.order_by(DigitalHumanGeneration.start_time.desc()).limit(limit).all()
                
                result = []
                for task in tasks:
                    task_info = {
                        "task_id": task.task_id,
                        "digital_human_id": task.digital_human_id,
                        "status": task.status,
                        "progress": task.progress or 0,
                        "message": task.message or "",
                        "start_time": task.start_time.isoformat() if task.start_time else None,
                        "updated_at": task.updated_at.isoformat() if task.updated_at else None,
                    }
                    
                    if task.status == "completed" and task.result_urls:
                        task_info["result_urls"] = task.result_urls
                    
                    if task.status == "failed":
                        task_info["error_message"] = task.error_message
                    
                    result.append(task_info)
                
                return result
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"列出任务失败: {e}")
            return []
    
    def cancel_task(self, task_id: str) -> Dict[str, Any]:
        """
        取消任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            操作结果
        """
        try:
            db = next(get_db())
            try:
                task_record = db.query(DigitalHumanGeneration).filter(
                    DigitalHumanGeneration.task_id == task_id
                ).first()
                
                if not task_record:
                    return {
                        "success": False,
                        "error": "任务不存在"
                    }
                
                # 取消Celery任务
                if task_record.celery_task_id:
                    celery_task = self.celery_app.AsyncResult(task_record.celery_task_id)
                    celery_task.revoke(terminate=True)
                
                # 更新数据库状态
                task_record.status = "cancelled"
                task_record.message = "任务已取消"
                task_record.updated_at = datetime.utcnow()
                db.commit()
                
                return {
                    "success": True,
                    "message": "任务已取消"
                }
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"取消任务失败: {e}")
            return {
                "success": False,
                "error": f"取消失败: {str(e)}"
            }
    
    def get_active_tasks(self) -> List[Dict[str, Any]]:
        """获取活跃任务列表"""
        return self.list_tasks(status="processing")
    
    def get_failed_tasks(self) -> List[Dict[str, Any]]:
        """获取失败任务列表"""
        return self.list_tasks(status="failed")
    
    def retry_task(self, task_id: str) -> Dict[str, Any]:
        """
        重试失败的任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            操作结果
        """
        try:
            db = next(get_db())
            try:
                task_record = db.query(DigitalHumanGeneration).filter(
                    DigitalHumanGeneration.task_id == task_id
                ).first()
                
                if not task_record:
                    return {
                        "success": False,
                        "error": "任务不存在"
                    }
                
                if task_record.status != "failed":
                    return {
                        "success": False,
                        "error": "只能重试失败的任务"
                    }
                
                # 重置任务状态
                task_record.status = "pending"
                task_record.progress = 0
                task_record.message = "等待重试..."
                task_record.error_message = None
                task_record.updated_at = datetime.utcnow()
                db.commit()
                
                # 重新启动任务
                from app.tasks.digital_human import generate_digital_human
                new_task = generate_digital_human.delay(
                    task_id,
                    task_record.digital_human_id,
                    task_record.input_data or {}
                )
                
                # 更新Celery任务ID
                task_record.celery_task_id = new_task.id
                db.commit()
                
                return {
                    "success": True,
                    "message": "任务已重新启动",
                    "new_celery_task_id": new_task.id
                }
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"重试任务失败: {e}")
            return {
                "success": False,
                "error": f"重试失败: {str(e)}"
            }

# 全局任务管理器实例
task_manager = TaskManager()
