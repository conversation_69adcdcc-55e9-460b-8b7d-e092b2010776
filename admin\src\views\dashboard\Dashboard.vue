<template>
  <div class="dashboard-container">
    <a-row :gutter="[16, 16]">
      <!-- 统计卡片 -->
      <a-col :xs="24" :sm="12" :md="12" :lg="6" :xl="6" v-for="(stat, index) in statistics" :key="index">
        <a-card class="stat-card" :loading="loading">
          <div class="stat-card-content">
            <div class="stat-icon" :style="{ backgroundColor: stat.bgColor }">
              <component :is="stat.icon" />
            </div>
            <div class="stat-info">
              <div class="stat-title">{{ stat.title }}</div>
              <div class="stat-value">{{ stat.value }}</div>
              <div class="stat-change" :class="stat.isIncrease ? 'increase' : 'decrease'">
                <component :is="stat.isIncrease ? 'rise-outlined' : 'fall-outlined'" />
                <span>{{ stat.change }}</span>
              </div>
            </div>
          </div>
        </a-card>
      </a-col>
      
      <!-- 系统信息 -->
      <a-col :span="12">
        <a-card title="系统信息" :loading="loading">
          <a-descriptions :column="1">
            <a-descriptions-item label="系统名称">{{ sysInfo.name }}</a-descriptions-item>
            <a-descriptions-item label="系统版本">{{ sysInfo.version }}</a-descriptions-item>
            <a-descriptions-item label="服务器环境">{{ sysInfo.environment }}</a-descriptions-item>
            <a-descriptions-item label="数据库">{{ sysInfo.database }}</a-descriptions-item>
            <a-descriptions-item label="缓存">{{ sysInfo.cache }}</a-descriptions-item>
            <a-descriptions-item label="操作系统">{{ sysInfo.os }}</a-descriptions-item>
            <a-descriptions-item label="运行时长">{{ sysInfo.uptime }}</a-descriptions-item>
          </a-descriptions>
        </a-card>
      </a-col>
      
      <!-- 快捷功能 -->
      <a-col :span="12">
        <a-card title="快捷功能" :loading="loading">
          <a-row :gutter="[16, 16]">
            <a-col :span="8" v-for="(action, i) in quickActions" :key="i">
              <a-button 
                class="quick-action-btn" 
                :type="i === 0 ? 'primary' : ''" 
                block
                @click="handleAction(action)"
              >
                <component :is="action.icon" />
                {{ action.name }}
              </a-button>
            </a-col>
          </a-row>
        </a-card>
      </a-col>
      
      <!-- 访问趋势图表 -->
      <a-col :span="24">
        <a-card title="访问趋势" :loading="loading">
          <div ref="chartRef" class="chart-container"></div>
        </a-card>
      </a-col>
      
      <!-- 用户访问来源 -->
      <a-col :span="12">
        <a-card title="访问来源" :loading="loading">
          <div ref="pieChartRef" class="chart-container pie-chart"></div>
        </a-card>
      </a-col>
      
      <!-- 最近任务 -->
      <a-col :span="12">
        <a-card title="最近任务" :loading="loading" :bodyStyle="{ padding: '0' }">
          <a-list>
            <a-list-item v-for="(task, idx) in recentTasks" :key="idx">
              <a-list-item-meta :title="task.title">
                <template #avatar>
                  <a-avatar :style="{ backgroundColor: task.statusColor }">
                    {{ task.statusText }}
                  </a-avatar>
                </template>
                <template #description>
                  <span>{{ task.time }}</span>
                  <a-tag :color="task.typeColor" class="ml-2">{{ task.type }}</a-tag>
                </template>
              </a-list-item-meta>
              <template #extra>
                <a-button size="small" @click="viewTask(task)">查看</a-button>
              </template>
            </a-list-item>
          </a-list>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, onMounted, onUnmounted } from 'vue';
import { 
  UserOutlined, 
  FileOutlined, 
  TeamOutlined, 
  ApiOutlined,
  ClearOutlined,
  ReloadOutlined,
  SettingOutlined,
  DownloadOutlined,
  CloudUploadOutlined,
  RiseOutlined,
  FallOutlined
} from '@ant-design/icons-vue';
import * as echarts from 'echarts/core';
import { LineChart, PieChart } from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';
import { fetchDashboardData } from '@/api/dashboard';

// 注册ECharts组件
echarts.use([
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  LineChart,
  PieChart,
  CanvasRenderer
]);

export default defineComponent({
  name: 'Dashboard',
  components: {
    UserOutlined,
    FileOutlined,
    TeamOutlined,
    ApiOutlined,
    ClearOutlined,
    ReloadOutlined,
    SettingOutlined,
    DownloadOutlined,
    CloudUploadOutlined,
    RiseOutlined,
    FallOutlined
  },
  setup() {
    // 加载状态
    const loading = ref(true);
    // 图表实例
    let lineChart = null;
    let pieChart = null;
    // 图表DOM引用
    const chartRef = ref(null);
    const pieChartRef = ref(null);
    
    // 统计数据
    const statistics = ref([
      {
        title: '注册用户',
        value: '0',
        change: '0%',
        isIncrease: true,
        icon: 'user-outlined',
        bgColor: '#1890ff'
      },
      {
        title: '总任务数',
        value: '0',
        change: '0%',
        isIncrease: true,
        icon: 'file-outlined',
        bgColor: '#52c41a'
      },
      {
        title: '在线用户',
        value: '0',
        change: '0%',
        isIncrease: false,
        icon: 'team-outlined',
        bgColor: '#faad14'
      },
      {
        title: 'API调用',
        value: '0',
        change: '0%',
        isIncrease: true,
        icon: 'api-outlined',
        bgColor: '#f5222d'
      }
    ]);
    
    // 系统信息
    const sysInfo = reactive({
      name: 'AI专业平台管理系统',
      version: 'v1.0.0',
      environment: 'Node.js v16.14.0',
      database: 'MySQL 8.0',
      cache: 'Redis 6.2',
      os: 'Linux CentOS 7.9',
      uptime: '0天0小时0分钟'
    });
    
    // 快捷功能
    const quickActions = ref([
      { name: '清除缓存', icon: 'clear-outlined', action: 'clearCache' },
      { name: '刷新统计', icon: 'reload-outlined', action: 'refreshStats' },
      { name: '系统设置', icon: 'setting-outlined', action: 'goSettings' },
      { name: '数据备份', icon: 'download-outlined', action: 'backupData' },
      { name: '模型更新', icon: 'cloud-upload-outlined', action: 'updateModels' },
      { name: '查看日志', icon: 'file-outlined', action: 'viewLogs' }
    ]);
    
    // 最近任务
    const recentTasks = ref([]);
    
    // 处理快捷操作
    const handleAction = (action) => {
      console.log('执行操作:', action.action);
      
      // 根据操作类型执行不同的操作
      switch (action.action) {
        case 'clearCache':
          // 清除缓存操作
          break;
        case 'refreshStats':
          // 刷新统计数据
          loadData();
          break;
        case 'goSettings':
          // 跳转到系统设置
          break;
        case 'backupData':
          // 数据备份操作
          break;
        case 'updateModels':
          // 模型更新操作
          break;
        case 'viewLogs':
          // 查看日志操作
          break;
        default:
          break;
      }
    };
    
    // 查看任务详情
    const viewTask = (task) => {
      console.log('查看任务:', task);
    };
    
    // 初始化折线图
    const initLineChart = (data) => {
      if (!chartRef.value) return;
      
      lineChart = echarts.init(chartRef.value);
      const option = {
        title: {
          show: false
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: ['访问量', '用户数', 'API调用']
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: data.dates
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '访问量',
            type: 'line',
            data: data.visits,
            smooth: true,
            lineStyle: {
              width: 2
            }
          },
          {
            name: '用户数',
            type: 'line',
            data: data.users,
            smooth: true,
            lineStyle: {
              width: 2
            }
          },
          {
            name: 'API调用',
            type: 'line',
            data: data.apiCalls,
            smooth: true,
            lineStyle: {
              width: 2
            }
          }
        ]
      };
      
      lineChart.setOption(option);
    };
    
    // 初始化饼图
    const initPieChart = (data) => {
      if (!pieChartRef.value) return;
      
      pieChart = echarts.init(pieChartRef.value);
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          right: 10,
          top: 'center',
          data: data.map(item => item.name)
        },
        series: [
          {
            name: '访问来源',
            type: 'pie',
            radius: ['50%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '18',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: data
          }
        ]
      };
      
      pieChart.setOption(option);
    };
    
    // 处理时间格式
    const formatUptime = (seconds) => {
      const days = Math.floor(seconds / (24 * 60 * 60));
      const hours = Math.floor((seconds % (24 * 60 * 60)) / (60 * 60));
      const minutes = Math.floor((seconds % (60 * 60)) / 60);
      
      return `${days}天${hours}小时${minutes}分钟`;
    };
    
    // 加载数据
    const loadData = async () => {
      loading.value = true;
      
      try {
        const res = await fetchDashboardData();
        const data = res.data;
        
        // 更新统计数据
        if (data.statistics) {
          statistics.value.forEach((stat, index) => {
            const newStat = data.statistics[index];
            if (newStat) {
              stat.value = newStat.value;
              stat.change = newStat.change;
              stat.isIncrease = newStat.isIncrease;
            }
          });
        }
        
        // 更新系统信息
        if (data.systemInfo) {
          Object.assign(sysInfo, data.systemInfo);
          // 格式化运行时间
          if (data.systemInfo.uptimeSeconds) {
            sysInfo.uptime = formatUptime(data.systemInfo.uptimeSeconds);
          }
        }
        
        // 更新最近任务
        if (data.recentTasks) {
          recentTasks.value = data.recentTasks;
        }
        
        // 更新图表数据
        if (data.chartData) {
          initLineChart(data.chartData);
        }
        
        // 更新饼图数据
        if (data.pieData) {
          initPieChart(data.pieData);
        }
      } catch (error) {
        console.error('加载Dashboard数据失败:', error);
      } finally {
        loading.value = false;
      }
    };
    
    // 窗口大小变化时重新计算图表大小
    const handleResize = () => {
      if (lineChart) {
        lineChart.resize();
      }
      if (pieChart) {
        pieChart.resize();
      }
    };
    
    // 组件挂载后初始化数据和图表
    onMounted(() => {
      loadData();
      window.addEventListener('resize', handleResize);
    });
    
    // 组件卸载时销毁图表和事件监听
    onUnmounted(() => {
      if (lineChart) {
        lineChart.dispose();
        lineChart = null;
      }
      if (pieChart) {
        pieChart.dispose();
        pieChart = null;
      }
      window.removeEventListener('resize', handleResize);
    });
    
    return {
      loading,
      statistics,
      sysInfo,
      quickActions,
      recentTasks,
      chartRef,
      pieChartRef,
      handleAction,
      viewTask
    };
  }
});
</script>

<style lang="less" scoped>
.dashboard-container {
  padding: 24px;
  
  .stat-card {
    height: 100%;
    
    .stat-card-content {
      display: flex;
      align-items: center;
      
      .stat-icon {
        width: 64px;
        height: 64px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 16px;
        
        :deep(.anticon) {
          font-size: 32px;
          color: #fff;
        }
      }
      
      .stat-info {
        flex: 1;
        
        .stat-title {
          font-size: 14px;
          color: rgba(0, 0, 0, 0.65);
          margin-bottom: 4px;
        }
        
        .stat-value {
          font-size: 24px;
          font-weight: 500;
          margin-bottom: 4px;
        }
        
        .stat-change {
          font-size: 13px;
          display: flex;
          align-items: center;
          
          &.increase {
            color: #52c41a;
          }
          
          &.decrease {
            color: #f5222d;
          }
          
          :deep(.anticon) {
            margin-right: 4px;
          }
        }
      }
    }
  }
  
  .quick-action-btn {
    height: 42px;
    margin-bottom: 8px;
    
    :deep(.anticon) {
      margin-right: 6px;
    }
  }
  
  .chart-container {
    height: 400px;
    width: 100%;
    
    &.pie-chart {
      height: 300px;
    }
  }
  
  :deep(.ant-list-item) {
    padding: 12px 24px;
  }
  
  .ml-2 {
    margin-left: 8px;
  }
}
</style> 