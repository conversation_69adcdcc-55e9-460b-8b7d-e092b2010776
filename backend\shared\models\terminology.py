from sqlalchemy import Column, Integer, String, Text, ForeignKey, DateTime, Boolean, Enum, JSON
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from sqlalchemy.types import TypeDecorator
from pydantic import BaseModel, ConfigDict
from typing import Optional, Dict, List
from datetime import datetime
import enum
import json
import logging

from utils.db import Base

# 配置日志记录器
logger = logging.getLogger("terminology_model")

# 自定义的枚举类型，确保正确处理枚举值和数据库值之间的转换
class EnumAsStringType(TypeDecorator):
    """
    自定义的枚举类型，确保使用枚举值（value）而不是枚举名称（name）
    与数据库交互，解决Python枚举与PostgreSQL枚举的不匹配问题
    """
    impl = String
    cache_ok = True  # 添加cache_ok属性以解决SAWarning
    
    def __init__(self, enum_class, **kw):
        self.enum_class = enum_class
        super(EnumAsStringType, self).__init__(**kw)
        
    def process_bind_param(self, value, dialect):
        """将Python值转换为数据库值"""
        if value is None:
            return None
        # 如果是枚举对象，使用其值
        if isinstance(value, enum.Enum):
            return value.value
        # 如果已经是字符串，直接返回
        return value
    
    def process_result_value(self, value, dialect):
        """将数据库值转换为Python值"""
        if value is None:
            return None
        # 尝试转换为枚举对象
        try:
            return self.enum_class(value)
        except ValueError:
            # 如果无法转换，记录警告并返回原值
            logger.warning(f"无法将 {value} 转换为 {self.enum_class.__name__} 枚举")
            return value
            
    def copy(self, **kw):
        return EnumAsStringType(self.enum_class, **kw)

# Enum definitions
class VisibilityType(enum.Enum):
    PUBLIC = "public"
    PRIVATE = "private"
    TEAM = "team"

class FileStatus(enum.Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"

class TermStatus(enum.Enum):
    DRAFT = "draft"
    APPROVED = "approved"
    REJECTED = "rejected"

# SQLAlchemy models
class MonoCorpus(Base):
    __tablename__ = "terminology_mono_corpus"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False)
    language = Column(String(10), nullable=False)
    description = Column(Text, nullable=True)
    visibility = Column(EnumAsStringType(VisibilityType), default=VisibilityType.PRIVATE)
    user_id = Column(Integer, ForeignKey("users.id"))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    files = relationship("MonoCorpusFile", back_populates="corpus")
    entries = relationship("MonoCorpusEntry", back_populates="corpus")
    user = relationship("User", back_populates="mono_corpora")
    
    # 添加to_dict方法，确保正确处理枚举值
    def to_dict(self):
        """将对象转换为字典，并正确处理枚举值"""
        result = {
            "id": self.id,
            "name": self.name,
            "language": self.language,
            "description": self.description,
            "user_id": self.user_id,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }
        
        # 处理visibility枚举值
        if self.visibility is None:
            result["visibility"] = None
        elif isinstance(self.visibility, VisibilityType):
            result["visibility"] = self.visibility.value
        else:
            # 如果已经是字符串，直接使用
            result["visibility"] = self.visibility
            
        return result

class MonoCorpusFile(Base):
    __tablename__ = "terminology_mono_corpus_files"
    
    id = Column(Integer, primary_key=True, index=True)
    corpus_id = Column(Integer, ForeignKey("terminology_mono_corpus.id"))
    filename = Column(String(255), nullable=False)
    original_filename = Column(String(255), nullable=False)
    file_size = Column(Integer, nullable=False)
    file_type = Column(String(50), nullable=False)
    status = Column(EnumAsStringType(FileStatus), default=FileStatus.PENDING)
    processed_entries = Column(Integer, default=0)
    total_entries = Column(Integer, default=0)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    corpus = relationship("MonoCorpus", back_populates="files")
    entries = relationship("MonoCorpusEntry", back_populates="file")
    
    # 添加to_dict方法
    def to_dict(self):
        """将对象转换为字典"""
        result = {
            "id": self.id,
            "corpus_id": self.corpus_id,
            "filename": self.filename,
            "original_filename": self.original_filename,
            "file_size": self.file_size,
            "file_type": self.file_type,
            "processed_entries": self.processed_entries,
            "total_entries": self.total_entries,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }
        
        # 处理status枚举值
        if self.status is None:
            result["status"] = None
        elif isinstance(self.status, FileStatus):
            result["status"] = self.status.value
        else:
            # 如果已经是字符串，直接使用
            result["status"] = self.status
            
        return result

class Term(Base):
    __tablename__ = "terminology_terms"
    
    id = Column(Integer, primary_key=True, index=True)
    term_text = Column(String(255), nullable=False)
    language = Column(String(10), nullable=False)
    domain = Column(String(100), nullable=True)
    definition = Column(Text, nullable=True)
    status = Column(EnumAsStringType(TermStatus), default=TermStatus.DRAFT)
    user_id = Column(Integer, ForeignKey("users.id"))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    user = relationship("User", back_populates="terms")
    entries = relationship("MonoCorpusEntry", back_populates="term")

class MonoCorpusEntry(Base):
    __tablename__ = "terminology_mono_corpus_entries"
    
    id = Column(Integer, primary_key=True, index=True)
    corpus_id = Column(Integer, ForeignKey("terminology_mono_corpus.id"))
    file_id = Column(Integer, ForeignKey("terminology_mono_corpus_files.id"), nullable=True)
    term_id = Column(Integer, ForeignKey("terminology_terms.id"), nullable=True)
    text = Column(Text, nullable=False)
    context_before = Column(Text, nullable=True)
    context_after = Column(Text, nullable=True)
    line_number = Column(Integer, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    corpus = relationship("MonoCorpus", back_populates="entries")
    file = relationship("MonoCorpusFile", back_populates="entries")
    term = relationship("Term", back_populates="entries")

# Add the Terminology model required by the API
class Terminology(Base):
    __tablename__ = "terminology_data"
    
    id = Column(Integer, primary_key=True, index=True)
    term_id = Column(String(36), unique=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    term = Column(String(255), nullable=False, index=True)
    definition = Column(Text, nullable=True)
    language = Column(String(10), nullable=False, index=True)
    domain = Column(String(100), nullable=True, index=True)
    translations = Column(Text, nullable=True)  # JSON string of language:translation pairs
    synonyms = Column(Text, nullable=True)  # JSON string of synonyms list
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    user = relationship("User", back_populates="terminology")

# Pydantic models for API validation and responses
class TerminologyBase(BaseModel):
    term: str
    definition: str
    language: str
    domain: Optional[str] = None
    translations: Dict[str, str]  # language code -> translation
    synonyms: Optional[List[str]] = None

class TerminologyCreate(TerminologyBase):
    pass

class TerminologyResponse(TerminologyBase):
    id: int
    term_id: str
    user_id: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    model_config = ConfigDict(
        from_attributes=True,
        arbitrary_types_allowed=True
    )
    
    @classmethod
    def from_orm(cls, obj):
        # Handle the JSON conversions
        obj_dict = {c.name: getattr(obj, c.name) for c in obj.__table__.columns}
        
        # 安全地解析translations字段
        if obj_dict.get("translations"):
            try:
                # 尝试解析JSON字符串
                translations_str = obj_dict["translations"]
                translations = json.loads(translations_str)
                
                # 确保解析结果是字典类型
                if not isinstance(translations, dict):
                    logger.warning(f"术语ID {obj_dict.get('term_id')}: translations不是字典类型: {translations}")
                    # 如果不是字典，创建一个包含原始值的字典
                    obj_dict["translations"] = {"raw": str(translations)}
                else:
                    obj_dict["translations"] = translations
            except json.JSONDecodeError as e:
                logger.error(f"术语ID {obj_dict.get('term_id')}: 解析translations JSON失败: {e}, 原始值: {obj_dict['translations']}")
                # 解析失败时，提供空字典
                obj_dict["translations"] = {}
            except Exception as e:
                logger.error(f"术语ID {obj_dict.get('term_id')}: 处理translations时出现未知错误: {e}")
                obj_dict["translations"] = {}
        else:
            # 确保字段存在
            obj_dict["translations"] = {}
        
        # 安全地解析synonyms字段
        if obj_dict.get("synonyms"):
            try:
                # 尝试解析JSON字符串
                synonyms_str = obj_dict["synonyms"]
                synonyms = json.loads(synonyms_str)
                
                # 确保解析结果是列表类型
                if not isinstance(synonyms, list):
                    logger.warning(f"术语ID {obj_dict.get('term_id')}: synonyms不是列表类型: {synonyms}")
                    # 如果不是列表，尝试转换或创建包含原始值的列表
                    if isinstance(synonyms, str):
                        obj_dict["synonyms"] = [synonyms]
                    else:
                        obj_dict["synonyms"] = [str(synonyms)]
                else:
                    obj_dict["synonyms"] = synonyms
            except json.JSONDecodeError as e:
                logger.error(f"术语ID {obj_dict.get('term_id')}: 解析synonyms JSON失败: {e}, 原始值: {obj_dict['synonyms']}")
                # 解析失败时，提供空列表
                obj_dict["synonyms"] = []
            except Exception as e:
                logger.error(f"术语ID {obj_dict.get('term_id')}: 处理synonyms时出现未知错误: {e}")
                obj_dict["synonyms"] = []
        else:
            # 确保字段存在
            obj_dict["synonyms"] = []
            
        return cls(**obj_dict)