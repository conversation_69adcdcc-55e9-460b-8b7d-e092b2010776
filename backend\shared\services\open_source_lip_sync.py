import os
import subprocess
from typing import List, Dict
import json

class OpenSourceLipSync:
    """开源口型同步服务"""
    
    def __init__(self):
        # 初始化Rhubarb Lip Sync
        self.rhubarb_path = "rhubarb"  # 需要安装Rhubarb Lip Sync
        
        # 口型映射
        self.viseme_map = {
            "A": "aa", "B": "b", "C": "ch", "D": "d", "E": "ee",
            "F": "f", "G": "g", "H": "h", "I": "ih", "J": "jh",
            "K": "k", "L": "l", "M": "m", "N": "n", "O": "oh",
            "P": "p", "Q": "q", "R": "r", "S": "s", "T": "t",
            "U": "uh", "V": "v", "W": "w", "X": "x", "Y": "y",
            "Z": "z", "sil": "sil"
        }
    
    def generate_visemes(self, audio_path: str) -> List[Dict]:
        """生成口型动画序列"""
        try:
            # 生成临时文件路径
            output_path = f"{audio_path}.json"
            
            # 调用Rhubarb生成口型数据
            cmd = [
                self.rhubarb_path,
                "-f", "json",
                "-o", output_path,
                audio_path
            ]
            
            subprocess.run(cmd, check=True)
            
            # 读取生成的口型数据
            with open(output_path, "r") as f:
                viseme_data = json.load(f)
            
            # 转换口型数据格式
            visemes = []
            for frame in viseme_data["mouthCues"]:
                visemes.append({
                    "viseme": self.viseme_map.get(frame["value"], "sil"),
                    "start": frame["start"],
                    "end": frame["end"]
                })
            
            # 清理临时文件
            os.remove(output_path)
            
            return visemes
            
        except Exception as e:
            print(f"口型生成失败: {e}")
            return []
    
    def _map_viseme(self, viseme: str) -> str:
        """映射口型到标准格式"""
        return self.viseme_map.get(viseme.upper(), "sil")

def get_open_source_lip_sync():
    """获取开源口型同步服务实例"""
    return OpenSourceLipSync() 