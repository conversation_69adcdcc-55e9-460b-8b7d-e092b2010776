"""
任务进度跟踪模块
用于跟踪任务进度并处理错误
"""

import logging
import json
import time
import functools
from typing import Dict, List, Optional, Any, Union, Set, Tuple, Callable
from datetime import datetime, timedelta
import asyncio
import re
import redis

# 配置日志
logger = logging.getLogger(__name__)

# 内存存储，当Redis不可用时使用
_memory_storage = {}

# 尝试创建Redis连接
_redis_client = None
try:
    _redis_client = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
    _redis_client.ping()  # 测试连接
    logger.info("Redis连接成功，将使用Redis存储任务进度")
except Exception as e:
    logger.warning(f"Redis连接失败: {str(e)}，将使用内存存储任务进度")
    _redis_client = None

def update_task_progress(task_id: str, progress: Union[int, float], status: str) -> bool:
    """
    更新任务进度
    
    Args:
        task_id: 任务ID
        progress: 进度值(0-100)或-1表示失败
        status: 状态描述
    
    Returns:
        bool: 是否更新成功
    """
    try:
        # 准备数据
        data = {
            "progress": progress,
            "status": status,
            "timestamp": time.time()
        }
        
        # 使用Redis或内存存储
        if _redis_client:
            key = f"task_progress:{task_id}"
            _redis_client.set(key, json.dumps(data))
            # 设置过期时间为1小时
            _redis_client.expire(key, 3600)
        else:
            _memory_storage[task_id] = data
            
        logger.debug(f"任务进度已更新: {task_id}, 进度: {progress}, 状态: {status}")
        return True
    except Exception as e:
        logger.error(f"更新任务进度失败: {str(e)}")
        return False

def get_task_progress(task_id: str) -> Dict[str, Any]:
    """
    获取任务进度
    
    Args:
        task_id: 任务ID
    
    Returns:
        Dict: 包含进度信息的字典，如果任务不存在则返回默认值
    """
    try:
        # 从Redis或内存获取
        if _redis_client:
            key = f"task_progress:{task_id}"
            data = _redis_client.get(key)
            if data:
                return json.loads(data)
        else:
            if task_id in _memory_storage:
                return _memory_storage[task_id]
                
        # 任务不存在，返回默认值
        return {
            "progress": 0,
            "status": "等待中",
            "timestamp": time.time()
        }
    except Exception as e:
        logger.error(f"获取任务进度失败: {str(e)}")
        return {
            "progress": 0,
            "status": f"获取进度失败: {str(e)}",
            "timestamp": time.time()
        }

def track_task_progress(func: Callable) -> Callable:
    """
    任务进度跟踪装饰器
    
    Args:
        func: 要跟踪的函数
    
    Returns:
        包装后的函数
    """
    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        # 获取任务ID
        task_id = None
        for arg in args:
            if isinstance(arg, str) and len(arg) > 8:
                task_id = arg
                break
        
        if not task_id and 'task_id' in kwargs:
            task_id = kwargs['task_id']
            
        if not task_id:
            logger.warning("无法确定任务ID，将不跟踪进度")
            return await func(*args, **kwargs)
            
        # 更新初始进度
        update_task_progress(task_id, 0, "任务开始执行")
        
        try:
            # 执行原函数
            result = await func(*args, **kwargs)
            
            # 根据结果更新最终状态
            if isinstance(result, dict) and 'success' in result:
                if result['success']:
                    update_task_progress(task_id, 100, "任务完成")
                else:
                    error_msg = result.get('error', '未知错误')
                    update_task_progress(task_id, -1, f"任务失败: {error_msg}")
            else:
                update_task_progress(task_id, 100, "任务完成")
                
            return result
        except Exception as e:
            # 捕获异常并更新状态
            logger.exception(f"任务执行异常: {str(e)}")
            update_task_progress(task_id, -1, f"任务异常: {str(e)}")
            raise
    
    return wrapper

def clear_task_progress(task_id: str) -> bool:
    """
    清除任务进度
    
    Args:
        task_id: 任务ID
    
    Returns:
        bool: 是否清除成功
    """
    try:
        if _redis_client:
            key = f"task_progress:{task_id}"
            _redis_client.delete(key)
        else:
            if task_id in _memory_storage:
                del _memory_storage[task_id]
        return True
    except Exception as e:
        logger.error(f"清除任务进度失败: {str(e)}")
        return False 

class ProgressTracker:
    """
    任务进度跟踪器类
    用于管理和跟踪任务的进度、状态和结果
    """
    
    def __init__(self):
        """初始化进度跟踪器"""
        self._websocket_connections = {}
        self.logger = logging.getLogger(__name__)
        self.logger.info("ProgressTracker已初始化")
    
    async def create_task(self, task_id: str, task_type: str = None, user_id: str = None, parameters: Dict = None) -> Dict:
        """
        创建新任务
        
        Args:
            task_id: 任务ID
            task_type: 任务类型
            user_id: 用户ID
            parameters: 任务参数
            
        Returns:
            Dict: 包含任务信息的字典
        """
        self.logger.info(f"创建任务: {task_id}, 类型: {task_type}, 用户: {user_id}")
        
        # 创建任务数据
        task = {
            "task_id": task_id,
            "task_type": task_type,
            "user_id": user_id,
            "status": "pending",
            "progress": 0,
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat(),
            "params": parameters or {},
            "logs": []
        }
        
        # 保存任务数据
        if _redis_client:
            key = f"task:{task_id}"
            _redis_client.set(key, json.dumps(task))
            # 设置过期时间为7天
            _redis_client.expire(key, 7 * 24 * 60 * 60)
            
            # 添加到用户任务列表
            if user_id:
                user_tasks_key = f"user_tasks:{user_id}"
                _redis_client.sadd(user_tasks_key, task_id)
                _redis_client.expire(user_tasks_key, 30 * 24 * 60 * 60)  # 30天过期
        else:
            _memory_storage[f"task:{task_id}"] = task
            if user_id:
                if f"user_tasks:{user_id}" not in _memory_storage:
                    _memory_storage[f"user_tasks:{user_id}"] = set()
                _memory_storage[f"user_tasks:{user_id}"].add(task_id)
        
        # 更新任务进度
        update_task_progress(task_id, 0, "任务已创建")
        
        # 广播任务创建事件
        await self._broadcast_to_websockets(task_id, task)
        
        return task
    
    async def update_task(self, task_id: str, **kwargs) -> Dict:
        """
        更新任务信息
        
        Args:
            task_id: 任务ID
            **kwargs: 要更新的字段
            
        Returns:
            Dict: 更新后的任务信息
        """
        task = await self.get_task(task_id)
        if not task:
            self.logger.warning(f"尝试更新不存在的任务: {task_id}")
            return None
        
        # 更新字段
        task.update(kwargs)
        task["updated_at"] = datetime.now().isoformat()
        
        # 如果提供了进度和状态，更新任务进度
        if "progress" in kwargs or "status" in kwargs:
            progress = kwargs.get("progress", task.get("progress", 0))
            status = kwargs.get("status", task.get("status", "unknown"))
            update_task_progress(task_id, progress, status)
        
        # 保存更新后的任务数据
        if _redis_client:
            key = f"task:{task_id}"
            _redis_client.set(key, json.dumps(task))
            _redis_client.expire(key, 7 * 24 * 60 * 60)  # 7天过期
        else:
            _memory_storage[f"task:{task_id}"] = task
        
        # 广播任务更新事件
        await self._broadcast_to_websockets(task_id, task)
        
        return task
    
    async def get_task(self, task_id: str) -> Dict:
        """
        获取任务信息
        
        Args:
            task_id: 任务ID
            
        Returns:
            Dict: 包含任务信息的字典，如果任务不存在则返回None
        """
        try:
            if _redis_client:
                key = f"task:{task_id}"
                data = _redis_client.get(key)
                if data:
                    return json.loads(data)
            else:
                if f"task:{task_id}" in _memory_storage:
                    return _memory_storage[f"task:{task_id}"]
            
            return None
        except Exception as e:
            self.logger.error(f"获取任务信息失败: {str(e)}")
            return None
    
    async def list_tasks(self, user_id: str = None, offset: int = 0, limit: int = 10) -> List[Dict]:
        """
        列出任务
        
        Args:
            user_id: 用户ID，如果提供则只返回该用户的任务
            offset: 分页偏移量
            limit: 每页数量
            
        Returns:
            List[Dict]: 任务列表
        """
        try:
            task_ids = []
            
            # 获取任务ID列表
            if user_id:
                if _redis_client:
                    user_tasks_key = f"user_tasks:{user_id}"
                    task_ids = list(_redis_client.smembers(user_tasks_key))
                else:
                    if f"user_tasks:{user_id}" in _memory_storage:
                        task_ids = list(_memory_storage[f"user_tasks:{user_id}"])
            else:
                # 获取所有任务
                if _redis_client:
                    keys = _redis_client.keys("task:*")
                    task_ids = [key.split(":", 1)[1] for key in keys]
                else:
                    task_ids = [key.split(":", 1)[1] for key in _memory_storage.keys() if key.startswith("task:")]
            
            # 获取任务详情
            tasks = []
            for task_id in task_ids:
                task = await self.get_task(task_id)
                if task:
                    tasks.append(task)
            
            # 排序和分页
            tasks.sort(key=lambda x: x.get("created_at", ""), reverse=True)
            return tasks[offset:offset+limit]
        except Exception as e:
            self.logger.error(f"列出任务失败: {str(e)}")
            return []
    
    async def delete_task(self, task_id: str) -> bool:
        """
        删除任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 是否删除成功
        """
        try:
            task = await self.get_task(task_id)
            if not task:
                return False
            
            user_id = task.get("user_id")
            
            if _redis_client:
                # 删除任务数据
                key = f"task:{task_id}"
                _redis_client.delete(key)
                
                # 从用户任务列表中移除
                if user_id:
                    user_tasks_key = f"user_tasks:{user_id}"
                    _redis_client.srem(user_tasks_key, task_id)
                
                # 删除任务进度
                progress_key = f"task_progress:{task_id}"
                _redis_client.delete(progress_key)
            else:
                # 删除任务数据
                if f"task:{task_id}" in _memory_storage:
                    del _memory_storage[f"task:{task_id}"]
                
                # 从用户任务列表中移除
                if user_id and f"user_tasks:{user_id}" in _memory_storage:
                    _memory_storage[f"user_tasks:{user_id}"].discard(task_id)
                
                # 删除任务进度
                if task_id in _memory_storage:
                    del _memory_storage[task_id]
            
            return True
        except Exception as e:
            self.logger.error(f"删除任务失败: {str(e)}")
            return False
    
    async def register_websocket(self, task_id: str, websocket) -> None:
        """
        注册WebSocket连接，用于接收任务更新通知
        
        Args:
            task_id: 任务ID
            websocket: WebSocket连接
        """
        if task_id not in self._websocket_connections:
            self._websocket_connections[task_id] = set()
        self._websocket_connections[task_id].add(websocket)
        self.logger.debug(f"WebSocket已注册到任务 {task_id}")
    
    async def unregister_websocket(self, task_id: str, websocket) -> None:
        """
        注销WebSocket连接
        
        Args:
            task_id: 任务ID
            websocket: WebSocket连接
        """
        if task_id in self._websocket_connections:
            if websocket in self._websocket_connections[task_id]:
                self._websocket_connections[task_id].remove(websocket)
            if not self._websocket_connections[task_id]:
                del self._websocket_connections[task_id]
        self.logger.debug(f"WebSocket已从任务 {task_id} 注销")
    
    async def _broadcast_to_websockets(self, task_id: str, data: Dict) -> None:
        """
        向所有注册的WebSocket广播任务更新
        
        Args:
            task_id: 任务ID
            data: 要广播的数据
        """
        if task_id in self._websocket_connections:
            message = json.dumps({"type": "task_update", "task": data})
            disconnected = []
            for websocket in self._websocket_connections[task_id]:
                try:
                    await websocket.send_text(message)
                except Exception as e:
                    self.logger.error(f"向WebSocket发送消息失败: {str(e)}")
                    disconnected.append(websocket)
            
            # 移除断开的连接
            for websocket in disconnected:
                await self.unregister_websocket(task_id, websocket)

# 全局ProgressTracker实例
_progress_tracker = None

def get_progress_tracker() -> ProgressTracker:
    """
    获取进度跟踪器单例
    
    Returns:
        ProgressTracker: 进度跟踪器实例
    """
    global _progress_tracker
    if _progress_tracker is None:
        _progress_tracker = ProgressTracker()
    return _progress_tracker 