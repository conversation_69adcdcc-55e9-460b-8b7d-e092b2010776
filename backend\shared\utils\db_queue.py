import asyncio
import threading
import logging
import time
from typing import Callable, Dict, Any, List, Optional, Tuple, Union
from sqlalchemy.orm import Session
from sqlalchemy import text
from functools import wraps
from concurrent.futures import ThreadPoolExecutor

# 配置日志记录器
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("db_queue")

# 数据库操作队列
db_operation_queue = asyncio.Queue()
# 数据库批处理队列 (按操作类型分组)
batch_operations: Dict[str, List[Dict[str, Any]]] = {}
# 工作线程池
db_workers_pool = ThreadPoolExecutor(max_workers=3, thread_name_prefix="db_worker")
# 控制标志
is_worker_running = False
# 操作结果存储
operation_results: Dict[str, Any] = {}
# 队列处理锁
queue_lock = threading.RLock()

class DBOperation:
    """数据库操作封装类"""
    
    def __init__(self, 
                operation_id: str,
                operation_type: str, 
                func: Callable,
                args: Tuple = None,
                kwargs: Dict[str, Any] = None,
                batch_key: str = None,
                priority: int = 1,
                timeout: int = 30):
        """
        初始化数据库操作
        
        Args:
            operation_id: 操作唯一ID
            operation_type: 操作类型
            func: 要执行的函数
            args: 函数参数
            kwargs: 函数关键字参数
            batch_key: 批处理键（如果适用）
            priority: 优先级 (1-10, 10为最高)
            timeout: 超时时间(秒)
        """
        self.operation_id = operation_id
        self.operation_type = operation_type
        self.func = func
        self.args = args or ()
        self.kwargs = kwargs or {}
        self.batch_key = batch_key
        self.priority = priority
        self.timeout = timeout
        self.created_at = time.time()
        self.executed_at = None
        self.completed_at = None
        self.status = "queued"
        self.result = None
        self.error = None

    async def execute(self, db_session: Optional[Session] = None):
        """执行数据库操作"""
        self.executed_at = time.time()
        self.status = "executing"
        
        try:
            # 如果提供了会话，则使用它，否则将None传递给函数
            if "db_session" in self.kwargs:
                self.kwargs["db_session"] = db_session
                
            # 执行函数
            self.result = await self.func(*self.args, **self.kwargs)
            self.status = "completed"
        except Exception as e:
            self.error = str(e)
            self.status = "failed"
            logger.error(f"数据库操作失败 [ID: {self.operation_id}]: {str(e)}")
        finally:
            self.completed_at = time.time()
            
        # 存储操作结果
        operation_results[self.operation_id] = {
            "status": self.status,
            "result": self.result,
            "error": self.error,
            "execution_time": self.completed_at - self.executed_at if self.completed_at else None
        }
        
        return self.result

async def enqueue_db_operation(operation: DBOperation) -> str:
    """
    将数据库操作添加到队列
    
    Args:
        operation: 要入队的数据库操作
        
    Returns:
        操作ID
    """
    await db_operation_queue.put(operation)
    logger.info(f"数据库操作已入队 [ID: {operation.operation_id}, 类型: {operation.operation_type}]")
    
    # 确保工作线程正在运行
    ensure_worker_running()
    
    return operation.operation_id

def ensure_worker_running():
    """确保工作线程正在运行"""
    global is_worker_running
    
    with queue_lock:
        if not is_worker_running:
            is_worker_running = True
            asyncio.create_task(process_db_operations())
            logger.info("数据库操作处理器已启动")

async def process_db_operations():
    """处理数据库操作队列"""
    global is_worker_running
    
    try:
        while True:
            try:
                # 获取下一个操作
                operation = await asyncio.wait_for(db_operation_queue.get(), timeout=5.0)
                
                # 检查是否为批处理操作
                if operation.batch_key:
                    await handle_batch_operation(operation)
                else:
                    # 直接处理单个操作
                    await process_single_operation(operation)
                
                # 标记任务完成
                db_operation_queue.task_done()
                
            except asyncio.TimeoutError:
                # 队列超时，检查是否有批处理操作需要处理
                await process_pending_batches()
                
                # 如果队列为空，可以考虑退出工作线程
                if db_operation_queue.empty():
                    logger.info("数据库操作队列为空，处理器将休眠")
                    break
    except Exception as e:
        logger.error(f"数据库操作处理器异常: {str(e)}")
    finally:
        with queue_lock:
            is_worker_running = False
            
async def process_single_operation(operation: DBOperation):
    """处理单个数据库操作"""
    from utils.db import get_db
    
    try:
        # 获取数据库会话
        db = next(get_db())
        
        try:
            # 执行操作
            await operation.execute(db_session=db)
            
            # 如果操作成功，提交会话
            if operation.status == "completed":
                db.commit()
                logger.info(f"数据库操作已完成 [ID: {operation.operation_id}]")
            else:
                db.rollback()
                logger.warning(f"数据库操作已回滚 [ID: {operation.operation_id}]")
                
        except Exception as e:
            db.rollback()
            logger.error(f"数据库操作执行错误 [ID: {operation.operation_id}]: {str(e)}")
            raise
        finally:
            db.close()
    except Exception as e:
        logger.error(f"数据库会话创建失败: {str(e)}")
        operation.status = "failed"
        operation.error = str(e)
        operation.completed_at = time.time()

async def handle_batch_operation(operation: DBOperation):
    """处理批处理操作"""
    batch_key = operation.batch_key
    
    if batch_key not in batch_operations:
        batch_operations[batch_key] = []
    
    # 添加到批处理队列
    batch_operations[batch_key].append({
        "operation": operation,
        "added_at": time.time()
    })
    
    # 检查是否需要立即处理批处理
    batch_items = batch_operations[batch_key]
    
    # 如果批处理队列到达一定大小或最早的操作已经等待足够长的时间，则处理
    if (len(batch_items) >= 10 or 
        (len(batch_items) > 0 and time.time() - batch_items[0]["added_at"] > 5)):
        await process_batch(batch_key)

async def process_batch(batch_key: str):
    """处理特定批处理键的批处理操作"""
    if batch_key not in batch_operations or not batch_operations[batch_key]:
        return
    
    logger.info(f"开始处理批处理操作 [Key: {batch_key}, 项目数: {len(batch_operations[batch_key])}]")
    
    from utils.db import get_db
    
    # 获取批处理项目
    batch_items = batch_operations.pop(batch_key)
    operations = [item["operation"] for item in batch_items]
    
    try:
        # 获取数据库会话
        db = next(get_db())
        
        try:
            # 按批处理执行操作
            results = []
            for operation in operations:
                result = await operation.execute(db_session=db)
                results.append(result)
            
            # 提交会话
            db.commit()
            logger.info(f"批处理操作已提交 [Key: {batch_key}]")
            
        except Exception as e:
            db.rollback()
            logger.error(f"批处理操作失败 [Key: {batch_key}]: {str(e)}")
            
            # 将所有操作标记为失败
            for operation in operations:
                if operation.status != "completed":
                    operation.status = "failed"
                    operation.error = str(e)
                    operation.completed_at = time.time()
            
        finally:
            db.close()
    except Exception as e:
        logger.error(f"批处理数据库会话创建失败: {str(e)}")
        
        # 将所有操作标记为失败
        for operation in operations:
            operation.status = "failed"
            operation.error = str(e)
            operation.completed_at = time.time()

async def process_pending_batches():
    """处理所有待处理的批处理操作"""
    pending_batches = list(batch_operations.keys())
    
    for batch_key in pending_batches:
        if batch_key in batch_operations:
            batch_items = batch_operations[batch_key]
            
            # 如果存在项目且最早的项目已经等待一段时间，则处理
            if (len(batch_items) > 0 and 
                time.time() - batch_items[0]["added_at"] > 2):
                await process_batch(batch_key)

async def get_operation_result(operation_id: str, wait: bool = True, timeout: int = 30) -> Dict[str, Any]:
    """
    获取操作结果
    
    Args:
        operation_id: 操作ID
        wait: 是否等待结果
        timeout: 等待超时时间(秒)
        
    Returns:
        操作结果
    """
    if not wait or operation_id in operation_results:
        return operation_results.get(operation_id, {"status": "unknown", "result": None, "error": "Operation not found"})
    
    # 等待结果
    start_time = time.time()
    while time.time() - start_time < timeout:
        if operation_id in operation_results:
            return operation_results[operation_id]
        await asyncio.sleep(0.1)
    
    return {"status": "timeout", "result": None, "error": "Operation result waiting timeout"}

def async_db_operation(operation_type: str, batch_key: str = None, priority: int = 1):
    """
    异步数据库操作装饰器
    
    Args:
        operation_type: 操作类型
        batch_key: 批处理键
        priority: 优先级
        
    Returns:
        装饰器函数
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            import uuid
            
            # 创建操作ID
            operation_id = str(uuid.uuid4())
            
            # 创建操作
            operation = DBOperation(
                operation_id=operation_id,
                operation_type=operation_type,
                func=func,
                args=args,
                kwargs=kwargs,
                batch_key=batch_key,
                priority=priority
            )
            
            # 入队
            await enqueue_db_operation(operation)
            
            return operation_id
        return wrapper
    return decorator

# 初始化 - 确保工作线程在模块加载时启动
def initialize_db_queue():
    """初始化数据库队列处理器"""
    ensure_worker_running() 