#!/usr/bin/env python3
"""
移动已下载的 MuseTalk 模型到正确位置
"""

import os
import shutil
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def move_models():
    """移动模型文件到正确位置"""
    try:
        # 源目录（错误的下载位置）
        source_dir = Path("E:/workspace/AI_system/models")
        
        # 目标目录（正确的位置）
        target_dir = Path(__file__).parent / "models"
        
        logger.info(f"源目录: {source_dir}")
        logger.info(f"目标目录: {target_dir}")
        
        if not source_dir.exists():
            logger.error(f"源目录不存在: {source_dir}")
            return False
        
        # 创建目标目录
        target_dir.mkdir(parents=True, exist_ok=True)
        
        # 移动模型文件
        model_dirs = [
            "musetalk",
            "musetalkV15", 
            "syncnet",
            "dwpose",
            "face-parse-bisent",
            "sd-vae",
            "whisper"
        ]
        
        moved_count = 0
        total_count = len(model_dirs)
        
        for model_dir in model_dirs:
            source_model_dir = source_dir / model_dir
            target_model_dir = target_dir / model_dir
            
            if source_model_dir.exists():
                logger.info(f"移动 {model_dir}...")
                
                # 创建目标子目录
                target_model_dir.mkdir(parents=True, exist_ok=True)
                
                # 移动所有文件
                for file_path in source_model_dir.rglob("*"):
                    if file_path.is_file():
                        relative_path = file_path.relative_to(source_model_dir)
                        target_file = target_model_dir / relative_path
                        
                        # 创建父目录
                        target_file.parent.mkdir(parents=True, exist_ok=True)
                        
                        # 移动文件
                        shutil.move(str(file_path), str(target_file))
                        logger.info(f"移动文件: {relative_path}")
                
                moved_count += 1
                logger.info(f"✅ {model_dir} 移动完成")
            else:
                logger.warning(f"⚠️ 源目录不存在: {source_model_dir}")
        
        # 清理空的源目录
        try:
            if source_dir.exists() and not any(source_dir.iterdir()):
                source_dir.rmdir()
                logger.info(f"清理空目录: {source_dir}")
        except:
            pass
        
        logger.info(f"模型移动完成: {moved_count}/{total_count} 成功")
        
        if moved_count >= 5:  # 至少移动5个模型目录
            logger.info("✅ MuseTalk 模型移动成功！")
            return True
        else:
            logger.warning(f"⚠️ 部分模型移动失败，成功率: {moved_count}/{total_count}")
            return False
        
    except Exception as e:
        logger.error(f"模型移动过程中出现错误: {str(e)}")
        return False

def verify_moved_models():
    """验证移动后的模型文件"""
    try:
        base_dir = Path(__file__).parent
        models_dir = base_dir / "models"
        
        required_files = [
            "musetalk/pytorch_model.bin",
            "musetalkV15/unet.pth", 
            "sd-vae/diffusion_pytorch_model.bin",
            "dwpose/dw-ll_ucoco_384.pth",
            "syncnet/latentsync_syncnet.pt",
            "face-parse-bisent/79999_iter.pth",
            "face-parse-bisent/resnet18-5c106cde.pth"
        ]
        
        found_files = []
        missing_files = []
        
        for file_path in required_files:
            full_path = models_dir / file_path
            if full_path.exists():
                size = full_path.stat().st_size
                found_files.append(file_path)
                logger.info(f"✅ 模型文件 {file_path}: {size / (1024*1024):.1f} MB")
            else:
                missing_files.append(file_path)
                logger.warning(f"❌ 缺少文件: {file_path}")
        
        logger.info(f"找到 {len(found_files)} 个模型文件，缺少 {len(missing_files)} 个文件")
        
        if len(found_files) >= 5:  # 至少有5个核心文件
            logger.info("✅ MuseTalk 模型验证通过！")
            return True
        else:
            logger.warning(f"⚠️ 模型文件不足，需要重新下载")
            return False
        
    except Exception as e:
        logger.error(f"模型验证失败: {str(e)}")
        return False

if __name__ == "__main__":
    logger.info("开始移动 MuseTalk 模型...")
    
    try:
        # 1. 移动模型
        if move_models():
            logger.info("模型移动成功，开始验证...")
            
            # 2. 验证模型
            if verify_moved_models():
                logger.info("🎉 MuseTalk 模型移动和验证完成！")
                print("\n" + "="*50)
                print("✅ MuseTalk 模型已正确安装！")
                print("模型位置: backend/local_models/MuseTalk/models/")
                print("现在可以使用最先进的实时数字人技术了！")
                print("="*50)
            else:
                logger.error("❌ 模型验证失败！")
        else:
            logger.error("❌ 模型移动失败！")
            
    except KeyboardInterrupt:
        logger.info("操作被用户中断")
    except Exception as e:
        logger.error(f"操作过程中出现错误: {str(e)}")
