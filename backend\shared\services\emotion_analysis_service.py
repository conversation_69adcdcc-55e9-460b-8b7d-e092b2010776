#!/usr/bin/env python3
"""
情感分析服务

提供对用户文本和语音的情感分析功能。
支持多种情感分析模型和方法。
"""

import os
import logging
from typing import Dict, Any, List, Optional, Tuple, Union
import json
import numpy as np
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EmotionAnalysisService:
    """情感分析服务类"""
    
    # 情感类别和对应的分数范围
    EMOTION_CATEGORIES = {
        'neutral': {'color': '#808080', 'intensity_range': (0.0, 0.2)},
        'happy': {'color': '#FFC107', 'intensity_range': (0.0, 1.0)},
        'sad': {'color': '#2196F3', 'intensity_range': (0.0, 1.0)},
        'angry': {'color': '#F44336', 'intensity_range': (0.0, 1.0)},
        'surprised': {'color': '#9C27B0', 'intensity_range': (0.0, 1.0)},
        'fearful': {'color': '#607D8B', 'intensity_range': (0.0, 1.0)},
        'disgusted': {'color': '#795548', 'intensity_range': (0.0, 1.0)}
    }
    
    def __init__(self, model_config: Dict[str, Any] = None):
        """
        初始化情感分析服务
        
        Args:
            model_config: 情感分析模型配置，包括模型类型、路径等
        """
        self.model_config = model_config or {}
        self.models = {}
        self.load_models()
    
    def load_models(self):
        """加载情感分析模型"""
        model_type = self.model_config.get('type', 'rule_based')
        
        if model_type == 'huggingface':
            try:
                from transformers import pipeline
                model_name = self.model_config.get('model_name', 'uer/roberta-base-finetuned-jd-binary-chinese')
                self.models['text'] = pipeline('sentiment-analysis', model=model_name)
                logger.info(f"已加载HuggingFace情感分析模型: {model_name}")
            except ImportError:
                logger.warning("未安装transformers库，将使用规则基础模型")
                self.models['text'] = self._rule_based_model
        elif model_type == 'local_bert':
            try:
                # 实现本地BERT模型加载逻辑
                logger.info("已加载本地BERT情感分析模型")
                pass
            except Exception as e:
                logger.error(f"加载本地BERT模型失败: {e}")
                self.models['text'] = self._rule_based_model
        else:
            # 默认使用规则基础模型
            self.models['text'] = self._rule_based_model
            logger.info("使用规则基础情感分析模型")
    
    def _rule_based_model(self, text: str) -> Dict[str, Any]:
        """
        基于规则的简单情感分析模型
        
        Args:
            text: 待分析文本
        
        Returns:
            情感分析结果
        """
        # 情感词典
        emotion_words = {
            'happy': ['开心', '高兴', '快乐', '兴奋', '满意', '喜欢', '爱', '感谢', '谢谢', '好', '棒'],
            'sad': ['难过', '伤心', '悲伤', '痛苦', '失望', '遗憾', '哭', '泪', '忧伤', '难受'],
            'angry': ['生气', '愤怒', '恼火', '烦', '讨厌', '恨', '滚', '混蛋', '可恶', '过分'],
            'surprised': ['惊讶', '吃惊', '震惊', '意外', '不敢相信', '天啊', '难以置信', '哇'],
            'fearful': ['害怕', '恐惧', '担心', '紧张', '焦虑', '怕', '惧', '危险'],
            'disgusted': ['恶心', '厌恶', '反感', '讨厌', '难吃', '臭', '脏']
        }
        
        # 计算情感分数
        scores = {emotion: 0 for emotion in self.EMOTION_CATEGORIES.keys()}
        scores['neutral'] = 0.5  # 默认值
        
        # 逐个情感类别检查
        for emotion, words in emotion_words.items():
            for word in words:
                if word in text:
                    scores[emotion] += 0.2
        
        # 标准化分数
        total = sum(scores.values())
        if total > 0:
            for emotion in scores:
                scores[emotion] /= total
                
        # 找出最高分情感
        dominant_emotion = max(scores, key=scores.get)
        confidence = scores[dominant_emotion]
        
        # 如果最高分情感不是neutral，但分数低于阈值，则仍判断为neutral
        if dominant_emotion != 'neutral' and confidence < 0.4:
            dominant_emotion = 'neutral'
            confidence = scores['neutral']
        
        return {
            'emotion': dominant_emotion,
            'confidence': confidence,
            'scores': scores
        }
    
    def analyze_text(self, text: str) -> Dict[str, Any]:
        """
        分析文本的情感
        
        Args:
            text: 待分析的文本
        
        Returns:
            情感分析结果，包括主要情感、置信度和各情感的分数
        """
        if not text or not text.strip():
            return {
                'emotion': 'neutral',
                'confidence': 1.0,
                'scores': {**{emotion: 0.0 for emotion in self.EMOTION_CATEGORIES.keys()}, 'neutral': 1.0}
            }
        
        try:
            model = self.models.get('text')
            if callable(model):
                result = model(text)
                
                # 处理不同模型的返回格式
                if isinstance(result, list) and len(result) > 0 and 'label' in result[0]:
                    # HuggingFace格式
                    label = result[0]['label']
                    score = result[0]['score']
                    
                    # 将模型输出映射到我们的情感类别
                    emotion_mapping = {
                        'positive': 'happy',
                        'negative': 'sad',
                        'neutral': 'neutral'
                    }
                    
                    emotion = emotion_mapping.get(label.lower(), 'neutral')
                    
                    # 构建完整结果
                    scores = {**{emotion: 0.1 for emotion in self.EMOTION_CATEGORIES.keys()}, 'neutral': 1.0}
                    scores[emotion] = score
                    
                    return {
                        'emotion': emotion,
                        'confidence': score,
                        'scores': scores
                    }
                else:
                    # 自定义规则模型
                    return result
            else:
                logger.error("情感分析模型未正确加载")
                return {
                    'emotion': 'neutral',
                    'confidence': 1.0,
                    'scores': {**{emotion: 0.0 for emotion in self.EMOTION_CATEGORIES.keys()}, 'neutral': 1.0}
                }
        except Exception as e:
            logger.error(f"文本情感分析失败: {e}")
            return {
                'emotion': 'neutral',
                'confidence': 1.0,
                'scores': {**{emotion: 0.0 for emotion in self.EMOTION_CATEGORIES.keys()}, 'neutral': 1.0}
            }
    
    def analyze_audio(self, audio_data: Union[bytes, str], audio_format: str = 'wav') -> Dict[str, Any]:
        """
        分析音频的情感
        
        Args:
            audio_data: 音频数据（bytes）或音频文件路径
            audio_format: 音频格式，默认为wav
        
        Returns:
            情感分析结果
        """
        try:
            # 暂时使用随机情感分析结果作为示例
            # 实际实现应该使用专门的音频情感分析模型
            import random
            
            emotions = list(self.EMOTION_CATEGORIES.keys())
            dominant_emotion = random.choice(emotions)
            confidence = random.uniform(0.6, 0.9)
            
            scores = {emotion: random.uniform(0.0, 0.3) for emotion in emotions}
            scores[dominant_emotion] = confidence
            
            # 标准化
            total = sum(scores.values())
            for emotion in scores:
                scores[emotion] /= total
            
            return {
                'emotion': dominant_emotion,
                'confidence': scores[dominant_emotion],
                'scores': scores,
                'audio_format': audio_format,
                'analysis_time': datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"音频情感分析失败: {e}")
            return {
                'emotion': 'neutral',
                'confidence': 1.0,
                'scores': {**{emotion: 0.0 for emotion in self.EMOTION_CATEGORIES.keys()}, 'neutral': 1.0},
                'error': str(e)
            }
    
    def get_response_style(self, emotion: str, confidence: float) -> Dict[str, Any]:
        """
        基于用户情感生成适当的响应风格
        
        Args:
            emotion: 用户情感
            confidence: 情感置信度
        
        Returns:
            响应风格配置
        """
        # 默认风格
        default_style = {
            'tone': 'neutral',
            'formality': 'moderate',
            'empathy': 'moderate',
            'energy': 'moderate'
        }
        
        # 根据情感调整风格
        style_mapping = {
            'happy': {
                'tone': 'cheerful',
                'formality': 'casual',
                'empathy': 'high',
                'energy': 'high'
            },
            'sad': {
                'tone': 'soothing',
                'formality': 'moderate',
                'empathy': 'very_high',
                'energy': 'low'
            },
            'angry': {
                'tone': 'calming',
                'formality': 'moderate',
                'empathy': 'high',
                'energy': 'moderate'
            },
            'surprised': {
                'tone': 'engaged',
                'formality': 'moderate',
                'empathy': 'high',
                'energy': 'high'
            },
            'fearful': {
                'tone': 'reassuring',
                'formality': 'moderate',
                'empathy': 'very_high',
                'energy': 'moderate'
            },
            'disgusted': {
                'tone': 'respectful',
                'formality': 'formal',
                'empathy': 'high',
                'energy': 'moderate'
            },
            'neutral': default_style
        }
        
        # 获取对应风格
        style = style_mapping.get(emotion, default_style)
        
        # 根据置信度调整风格强度
        if confidence < 0.4:
            # 低置信度时，向默认风格靠拢
            for key in style:
                if key in default_style:
                    style[key] = default_style[key]
        
        # 添加颜色
        style['color'] = self.EMOTION_CATEGORIES.get(emotion, {}).get('color', '#808080')
        
        return style
    
    def combine_analysis_results(self, text_result: Dict[str, Any], audio_result: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        组合文本和音频的情感分析结果
        
        Args:
            text_result: 文本情感分析结果
            audio_result: 音频情感分析结果
        
        Returns:
            组合后的情感分析结果
        """
        if audio_result is None:
            return text_result
        
        # 计算加权平均
        text_weight = 0.6  # 文本情感权重
        audio_weight = 0.4  # 音频情感权重
        
        # 合并分数
        combined_scores = {}
        for emotion in self.EMOTION_CATEGORIES.keys():
            text_score = text_result.get('scores', {}).get(emotion, 0.0)
            audio_score = audio_result.get('scores', {}).get(emotion, 0.0)
            combined_scores[emotion] = text_score * text_weight + audio_score * audio_weight
        
        # 找出主要情感
        dominant_emotion = max(combined_scores, key=combined_scores.get)
        confidence = combined_scores[dominant_emotion]
        
        return {
            'emotion': dominant_emotion,
            'confidence': confidence,
            'scores': combined_scores,
            'text_result': text_result,
            'audio_result': audio_result,
            'analysis_time': datetime.now().isoformat()
        }

# 单例模式
_instance = None

def get_emotion_analysis_service(config: Dict[str, Any] = None) -> EmotionAnalysisService:
    """
    获取情感分析服务实例
    
    Args:
        config: 服务配置
    
    Returns:
        情感分析服务实例
    """
    global _instance
    if _instance is None:
        _instance = EmotionAnalysisService(config)
    return _instance 