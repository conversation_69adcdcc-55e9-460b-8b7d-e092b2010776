"""
Celery应用配置
"""

from celery import Celery
import os
import logging
from app.core.config import settings

logger = logging.getLogger(__name__)

# 检查Redis是否可用
def check_redis_available():
    try:
        import redis
        r = redis.Redis(host='localhost', port=6379, db=0)
        r.ping()
        return True
    except Exception:
        return False

# 检查是否使用FakeRedis进行测试
USE_FAKE_REDIS = os.getenv("USE_FAKE_REDIS", "true").lower() == "true"
REDIS_AVAILABLE = check_redis_available() and not USE_FAKE_REDIS

if USE_FAKE_REDIS:
    # 使用内存模拟Redis
    broker_url = "memory://"
    backend_url = "cache+memory://"
else:
    # 使用真实Redis
    broker_url = settings.REDIS_URL
    backend_url = settings.REDIS_URL

# 创建Celery应用
celery_app = Celery(
    "ai_platform",
    broker=broker_url,
    backend=backend_url,
    include=[
        "app.tasks.digital_human_tasks",
        "app.tasks.image_processing_tasks",
        "app.tasks.voice_processing_tasks",
        "app.tasks.video_generation_tasks",
        "app.tasks.translation_tasks",
        "app.tasks.wanx_video_tasks",
        "app.tasks.wanx_video_tasks_fixed",  # 修复的 Wanx 任务
        "app.tasks.test_unified_task",
        "app.tasks.simple_test_task"
    ]
)

# Celery配置
celery_app.conf.update(
    # 任务序列化
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    result_accept_content=["json"],
    timezone="UTC",
    enable_utc=True,

    # 消息格式兼容性
    task_protocol=2,
    task_ignore_result=False,


    
    # 任务路由
    task_routes={
        "app.tasks.digital_human_tasks.*": {"queue": "digital_human"},
        "app.tasks.image_processing_tasks.*": {"queue": "image_processing"},
        "app.tasks.voice_processing_tasks.*": {"queue": "voice_processing"},
        "app.tasks.video_generation_tasks.*": {"queue": "video_generation"},
        "app.tasks.translation_tasks.*": {"queue": "translation"},
        "wanx_text_to_video": {"queue": "wanx_generation"},  # Wanx 文本转视频
        "wanx_image_to_video": {"queue": "wanx_generation"},  # Wanx 图片转视频
        "wanx_text_to_video_fixed": {"queue": "wanx_generation"},  # 修复的 Wanx 文本转视频
        "wanx_image_to_video_fixed": {"queue": "wanx_generation"},  # 修复的 Wanx 图片转视频
        "test_unified_task": {"queue": "test"},  # 测试任务路由
        "test_wanx_simple": {"queue": "test"},  # 简化 Wanx 测试任务路由
        "simple_add": {"queue": "test"},  # 简单加法任务
        "simple_hello": {"queue": "test"},  # 简单问候任务
    },
    
    # 任务优先级
    task_default_priority=5,
    worker_prefetch_multiplier=1,
    
    # 任务超时 - Windows 兼容性修复
    task_soft_time_limit=None,  # 禁用软超时（Windows 不支持 SIGUSR1）
    task_time_limit=7200,       # 2小时硬超时

    # Windows 平台兼容性配置
    worker_pool='solo',         # 使用 solo 池避免多进程问题
    worker_concurrency=1,       # 单进程执行
    
    # 结果过期时间
    result_expires=86400,       # 24小时
    
    # 任务重试
    task_acks_late=True,
    worker_disable_rate_limits=True,
    
    # 监控
    worker_send_task_events=True,
    task_send_sent_event=True,
    
    # 内存优化
    worker_max_tasks_per_child=50,
    worker_max_memory_per_child=2000000,  # 2GB
)

# 任务状态常量
class TaskStatus:
    PENDING = "PENDING"
    STARTED = "STARTED"
    RETRY = "RETRY"
    FAILURE = "FAILURE"
    SUCCESS = "SUCCESS"
    REVOKED = "REVOKED"

# 自定义任务基类
from celery import Task
from sqlalchemy.orm import Session
from app.core.sqlalchemy_db import get_db
from app.models.digital_human import DigitalHumanGeneration
import logging

logger = logging.getLogger(__name__)

class UnifiedTask(Task):
    """统一任务基类 - 支持所有类型的任务"""

    def __call__(self, *args, **kwargs):
        """任务执行包装器"""
        try:
            return super().__call__(*args, **kwargs)
        except Exception as exc:
            logger.error(f"任务执行失败: {self.name}, 错误: {str(exc)}")
            raise

    def update_task_progress(self, task_id: str, progress: int, message: str = "", **kwargs):
        """更新任务进度"""
        try:
            from app.core.task_manager import task_manager

            update_data = {
                'progress': progress,
                'message': message,
                **kwargs
            }

            task_manager.update_task(task_id, **update_data)

            # 更新Celery任务状态
            self.update_state(
                state="PROGRESS",
                meta={
                    "progress": progress,
                    "message": message,
                    "task_id": task_id
                }
            )

            print(f"📊 任务进度更新: {task_id} - {progress}% - {message}")

        except Exception as e:
            logger.error(f"更新任务进度失败: {e}")

    def start_task(self, task_id: str, celery_task_id: str = None):
        """标记任务开始"""
        try:
            from app.core.task_manager import task_manager
            from datetime import datetime

            task_manager.update_task(
                task_id,
                celery_task_id=celery_task_id or self.request.id,
                status='processing',
                started_at=datetime.utcnow(),
                message='任务开始执行'
            )

            print(f"🚀 任务开始: {task_id}")

        except Exception as e:
            logger.error(f"标记任务开始失败: {e}")

    def complete_task(self, task_id: str, output_data: dict = None, output_files: list = None):
        """标记任务完成"""
        try:
            from app.core.task_manager import task_manager
            from datetime import datetime

            # 计算实际耗时
            task_info = task_manager.get_task(task_id)
            actual_duration = None
            if task_info and task_info.get('started_at'):
                started_at = datetime.fromisoformat(task_info['started_at'].replace('Z', '+00:00'))
                actual_duration = int((datetime.utcnow() - started_at).total_seconds())

            task_manager.update_task(
                task_id,
                status='completed',
                progress=100,
                completed_at=datetime.utcnow(),
                output_data=output_data,
                output_files=output_files,
                actual_duration=actual_duration,
                message='任务完成'
            )

            print(f"✅ 任务完成: {task_id}")

        except Exception as e:
            logger.error(f"标记任务完成失败: {e}")

    def fail_task(self, task_id: str, error_message: str):
        """标记任务失败"""
        try:
            from app.core.task_manager import task_manager
            from datetime import datetime

            task_manager.update_task(
                task_id,
                status='failed',
                completed_at=datetime.utcnow(),
                error_message=error_message
            )

            print(f"❌ 任务失败: {task_id} - {error_message}")

        except Exception as e:
            logger.error(f"标记任务失败失败: {e}")

# 保持向后兼容
DatabaseTask = UnifiedTask

# 设置默认任务基类
celery_app.Task = UnifiedTask

if __name__ == "__main__":
    celery_app.start()
