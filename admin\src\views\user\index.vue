<template>
  <div class="user-container">
    <!-- 搜索栏 -->
    <a-card class="search-card" :bordered="false">
      <a-form layout="inline" :model="queryParams">
        <a-form-item label="用户名">
          <a-input
            v-model:value="queryParams.username"
            placeholder="请输入用户名"
            allow-clear
          />
        </a-form-item>
        <a-form-item label="手机号">
          <a-input
            v-model:value="queryParams.mobile"
            placeholder="请输入手机号"
            allow-clear
          />
        </a-form-item>
        <a-form-item label="状态">
          <a-select
            v-model:value="queryParams.status"
            placeholder="请选择状态"
            style="min-width: 120px"
            allow-clear
          >
            <a-select-option value="1">正常</a-select-option>
            <a-select-option value="0">禁用</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="角色">
          <a-select
            v-model:value="queryParams.roleId"
            placeholder="请选择角色"
            style="min-width: 120px"
            allow-clear
          >
            <a-select-option v-for="role in roles" :key="role.id" :value="role.id">
              {{ role.name }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item>
          <a-space>
            <a-button type="primary" @click="handleSearch">
              <template #icon><search-outlined /></template>
              查询
            </a-button>
            <a-button @click="handleReset">
              <template #icon><clear-outlined /></template>
              重置
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 操作按钮和表格 -->
    <a-card :bordered="false" style="margin-top: 16px">
      <template #title>
        <a-button type="primary" @click="handleAdd">
          <template #icon><plus-outlined /></template>
          新增用户
        </a-button>
        <a-button 
          style="margin-left: 16px" 
          :disabled="!selectedRowKeys.length"
          @click="handleBatchDelete"
        >
          <template #icon><delete-outlined /></template>
          批量删除
        </a-button>
        <a-button
          style="margin-left: 16px" 
          @click="handleExport"
        >
          <template #icon><download-outlined /></template>
          导出
        </a-button>
      </template>

      <!-- 表格 -->
      <a-table
        :loading="loading"
        :columns="columns"
        :data-source="dataSource"
        :pagination="pagination"
        :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
        :row-key="record => record.id"
        @change="handleTableChange"
      >
        <!-- 头像 -->
        <template #bodyCell="{ column, text, record }">
          <template v-if="column.dataIndex === 'avatar'">
            <a-avatar :src="text || '/avatar-default.png'" />
          </template>

          <!-- 状态 -->
          <template v-else-if="column.dataIndex === 'status'">
            <a-switch
              :checked="text === '1'"
              :loading="record.statusLoading"
              @change="status => handleStatusChange(record, status)"
            />
          </template>

          <!-- 角色 -->
          <template v-else-if="column.dataIndex === 'roles'">
            <a-tag v-for="role in text" :key="role.id" color="blue">
              {{ role.name }}
            </a-tag>
          </template>

          <!-- 操作 -->
          <template v-else-if="column.dataIndex === 'action'">
            <a-button type="link" size="small" @click="handleEdit(record)">编辑</a-button>
            <a-divider type="vertical" />
            <a-button type="link" size="small" @click="handleView(record)">查看</a-button>
            <a-divider type="vertical" />
            <a-dropdown>
              <a class="ant-dropdown-link" @click.prevent>
                更多 <down-outlined />
              </a>
              <template #content>
                <a-menu>
                  <a-menu-item key="resetPwd" @click="handleResetPassword(record)">
                    <lock-outlined /> 重置密码
                  </a-menu-item>
                  <a-menu-item key="logs" @click="handleViewLogs(record)">
                    <file-outlined /> 操作日志
                  </a-menu-item>
                  <a-menu-item key="delete" danger @click="handleDelete(record)">
                    <delete-outlined /> 删除
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 用户表单对话框 -->
    <a-modal
      v-model:visible="modalVisible"
      :title="modalTitle"
      width="650px"
      :confirm-loading="confirmLoading"
      @ok="handleModalSubmit"
    >
      <a-form
        ref="formRef"
        :model="form"
        :rules="rules"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item label="用户名" name="username">
          <a-input v-model:value="form.username" placeholder="请输入用户名" />
        </a-form-item>

        <a-form-item 
          label="密码" 
          name="password"
          v-if="modalType === 'add'"
        >
          <a-input-password v-model:value="form.password" placeholder="请输入密码" />
        </a-form-item>

        <a-form-item label="昵称" name="nickname">
          <a-input v-model:value="form.nickname" placeholder="请输入昵称" />
        </a-form-item>

        <a-form-item label="手机号" name="mobile">
          <a-input v-model:value="form.mobile" placeholder="请输入手机号" />
        </a-form-item>

        <a-form-item label="邮箱" name="email">
          <a-input v-model:value="form.email" placeholder="请输入邮箱" />
        </a-form-item>

        <a-form-item label="头像">
          <a-upload
            list-type="picture-card"
            :show-upload-list="false"
            action="/api/upload"
            :before-upload="beforeUpload"
            @change="handleAvatarChange"
          >
            <img v-if="form.avatar" :src="form.avatar" alt="avatar" style="width: 100%" />
            <div v-else>
              <upload-outlined />
              <div style="margin-top: 8px">上传头像</div>
            </div>
          </a-upload>
        </a-form-item>

        <a-form-item label="角色" name="roleIds">
          <a-select
            v-model:value="form.roleIds"
            mode="multiple"
            placeholder="请选择角色"
            style="width: 100%"
          >
            <a-select-option v-for="role in roles" :key="role.id" :value="role.id">
              {{ role.name }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="状态" name="status">
          <a-radio-group v-model:value="form.status">
            <a-radio value="1">正常</a-radio>
            <a-radio value="0">禁用</a-radio>
          </a-radio-group>
        </a-form-item>

        <a-form-item label="备注" name="remark">
          <a-textarea v-model:value="form.remark" placeholder="请输入备注" :rows="4" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 查看操作日志对话框 -->
    <a-modal
      v-model:visible="logModalVisible"
      title="操作日志"
      width="800px"
      :footer="null"
    >
      <a-table
        :loading="logLoading"
        :columns="logColumns"
        :data-source="logs"
        :pagination="logPagination"
        @change="handleLogTableChange"
      >
        <template #bodyCell="{ column, text }">
          <template v-if="column.dataIndex === 'action'">
            <a-tag :color="text === 'login' ? 'green' : text === 'logout' ? 'orange' : 'blue'">
              {{ formatAction(text) }}
            </a-tag>
          </template>
        </template>
      </a-table>
    </a-modal>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, toRaw, computed, onMounted } from 'vue';
import { message, Modal } from 'ant-design-vue';
import {
  PlusOutlined,
  SearchOutlined,
  ClearOutlined,
  DownloadOutlined,
  DeleteOutlined,
  UploadOutlined,
  LockOutlined,
  FileOutlined,
  DownOutlined
} from '@ant-design/icons-vue';
import {
  getUserList,
  getUserRoles,
  createUser,
  updateUser,
  deleteUser,
  batchDeleteUsers,
  changeUserStatus,
  resetUserPassword,
  getUserActivityLogs
} from '@/api/user';

export default defineComponent({
  name: 'UserList',
  components: {
    PlusOutlined,
    SearchOutlined,
    ClearOutlined,
    DownloadOutlined,
    DeleteOutlined,
    UploadOutlined,
    LockOutlined,
    FileOutlined,
    DownOutlined
  },
  setup() {
    // 表格加载状态
    const loading = ref(false);
    // 表格数据
    const dataSource = ref([]);
    // 选中的行
    const selectedRowKeys = ref([]);
    // 角色列表
    const roles = ref([]);
    
    // 查询参数
    const queryParams = reactive({
      username: '',
      mobile: '',
      status: undefined,
      roleId: undefined
    });
    
    // 分页信息
    const pagination = reactive({
      current: 1,
      pageSize: 10,
      total: 0,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: total => `共 ${total} 条记录`
    });
    
    // 表格列定义
    const columns = [
      {
        title: '用户ID',
        dataIndex: 'id',
        key: 'id',
        width: 80
      },
      {
        title: '头像',
        dataIndex: 'avatar',
        key: 'avatar',
        width: 80
      },
      {
        title: '用户名',
        dataIndex: 'username',
        key: 'username',
        width: 120
      },
      {
        title: '昵称',
        dataIndex: 'nickname',
        key: 'nickname',
        width: 120
      },
      {
        title: '手机号',
        dataIndex: 'mobile',
        key: 'mobile',
        width: 120
      },
      {
        title: '邮箱',
        dataIndex: 'email',
        key: 'email',
        width: 150
      },
      {
        title: '角色',
        dataIndex: 'roles',
        key: 'roles',
        width: 150
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        width: 80
      },
      {
        title: '最后登录时间',
        dataIndex: 'lastLoginTime',
        key: 'lastLoginTime',
        width: 180
      },
      {
        title: '创建时间',
        dataIndex: 'createTime',
        key: 'createTime',
        width: 180
      },
      {
        title: '操作',
        dataIndex: 'action',
        key: 'action',
        fixed: 'right',
        width: 220
      }
    ];
    
    // 表单对话框
    const modalVisible = ref(false);
    const modalType = ref('add'); // 'add', 'edit', 'view'
    const modalTitle = computed(() => {
      const titles = {
        add: '新增用户',
        edit: '编辑用户',
        view: '查看用户'
      };
      return titles[modalType.value] || '';
    });
    const confirmLoading = ref(false);
    
    // 表单数据
    const formRef = ref(null);
    const form = reactive({
      id: '',
      username: '',
      password: '',
      nickname: '',
      mobile: '',
      email: '',
      avatar: '',
      roles: [],
      roleIds: [],
      status: '1',
      remark: ''
    });
    
    // 表单验证规则
    const rules = reactive({
      username: [
        { required: true, message: '请输入用户名', trigger: 'blur' },
        { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
      ],
      password: [
        { required: true, message: '请输入密码', trigger: 'blur' },
        { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
      ],
      nickname: [
        { required: true, message: '请输入昵称', trigger: 'blur' }
      ],
      mobile: [
        { pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确', trigger: 'blur' }
      ],
      email: [
        { type: 'email', message: '邮箱格式不正确', trigger: 'blur' }
      ],
      roleIds: [
        { required: true, type: 'array', message: '请选择角色', trigger: 'change' }
      ]
    });
    
    // 操作日志对话框
    const logModalVisible = ref(false);
    const logLoading = ref(false);
    const logs = ref([]);
    const currentUserId = ref('');
    const logPagination = reactive({
      current: 1,
      pageSize: 10,
      total: 0
    });
    
    // 操作日志表格列
    const logColumns = [
      {
        title: '操作类型',
        dataIndex: 'action',
        key: 'action',
        width: 120
      },
      {
        title: 'IP地址',
        dataIndex: 'ip',
        key: 'ip',
        width: 150
      },
      {
        title: '操作内容',
        dataIndex: 'content',
        key: 'content'
      },
      {
        title: '操作时间',
        dataIndex: 'createTime',
        key: 'createTime',
        width: 180
      }
    ];
    
    // 获取用户列表
    const fetchUserList = async () => {
      loading.value = true;
      try {
        const res = await getUserList({
          page: pagination.current,
          limit: pagination.pageSize,
          ...queryParams
        });
        
        dataSource.value = res.data.list || [];
        pagination.total = res.data.total || 0;
      } catch (error) {
        console.error('获取用户列表失败:', error);
        message.error('获取用户列表失败');
      } finally {
        loading.value = false;
      }
    };
    
    // 获取角色列表
    const fetchRoles = async () => {
      try {
        const res = await getUserRoles();
        roles.value = res.data || [];
      } catch (error) {
        console.error('获取角色列表失败:', error);
        message.error('获取角色列表失败');
      }
    };
    
    // 查询
    const handleSearch = () => {
      pagination.current = 1;
      fetchUserList();
    };
    
    // 重置
    const handleReset = () => {
      Object.keys(queryParams).forEach(key => {
        queryParams[key] = '';
      });
      handleSearch();
    };
    
    // 表格变化处理
    const handleTableChange = ({ current, pageSize }, filters, sorter) => {
      pagination.current = current;
      pagination.pageSize = pageSize;
      fetchUserList();
    };
    
    // 选择行变化处理
    const onSelectChange = keys => {
      selectedRowKeys.value = keys;
    };
    
    // 重置表单
    const resetForm = () => {
      formRef.value?.resetFields();
      form.id = '';
      form.username = '';
      form.password = '';
      form.nickname = '';
      form.mobile = '';
      form.email = '';
      form.avatar = '';
      form.roles = [];
      form.roleIds = [];
      form.status = '1';
      form.remark = '';
    };
    
    // 新增用户
    const handleAdd = () => {
      resetForm();
      modalType.value = 'add';
      modalVisible.value = true;
    };
    
    // 编辑用户
    const handleEdit = record => {
      resetForm();
      modalType.value = 'edit';
      
      Object.keys(form).forEach(key => {
        if (key in record) {
          form[key] = record[key];
        }
      });
      
      // 设置角色IDs
      if (record.roles && Array.isArray(record.roles)) {
        form.roleIds = record.roles.map(role => role.id);
      }
      
      modalVisible.value = true;
    };
    
    // 查看用户
    const handleView = record => {
      resetForm();
      modalType.value = 'view';
      
      Object.keys(form).forEach(key => {
        if (key in record) {
          form[key] = record[key];
        }
      });
      
      // 设置角色IDs
      if (record.roles && Array.isArray(record.roles)) {
        form.roleIds = record.roles.map(role => role.id);
      }
      
      modalVisible.value = true;
    };
    
    // 提交表单
    const handleModalSubmit = () => {
      // 只读模式下不需要验证和提交
      if (modalType.value === 'view') {
        modalVisible.value = false;
        return;
      }
      
      formRef.value.validate()
        .then(async () => {
          confirmLoading.value = true;
          
          const formData = toRaw(form);
          
          try {
            if (modalType.value === 'add') {
              await createUser(formData);
              message.success('新增用户成功');
            } else {
              await updateUser(formData.id, formData);
              message.success('更新用户成功');
            }
            
            modalVisible.value = false;
            fetchUserList();
          } catch (error) {
            console.error('保存用户失败:', error);
            message.error('保存用户失败');
          } finally {
            confirmLoading.value = false;
          }
        })
        .catch(error => {
          console.log('表单验证失败:', error);
        });
    };
    
    // 删除用户
    const handleDelete = record => {
      Modal.confirm({
        title: '提示',
        content: `确定要删除用户"${record.username}"吗？`,
        okText: '确定',
        okType: 'danger',
        cancelText: '取消',
        onOk: async () => {
          try {
            await deleteUser(record.id);
            message.success('删除用户成功');
            
            // 重新加载数据
            if (dataSource.value.length === 1 && pagination.current > 1) {
              pagination.current -= 1;
            }
            fetchUserList();
          } catch (error) {
            console.error('删除用户失败:', error);
            message.error('删除用户失败');
          }
        }
      });
    };
    
    // 批量删除用户
    const handleBatchDelete = () => {
      if (!selectedRowKeys.value.length) {
        message.warning('请选择要删除的用户');
        return;
      }
      
      const count = selectedRowKeys.value.length;
      
      Modal.confirm({
        title: '提示',
        content: `确定要删除选中的 ${count} 个用户吗？`,
        okText: '确定',
        okType: 'danger',
        cancelText: '取消',
        onOk: async () => {
          try {
            await batchDeleteUsers(selectedRowKeys.value);
            message.success('批量删除用户成功');
            selectedRowKeys.value = [];
            
            // 重新加载数据
            fetchUserList();
          } catch (error) {
            console.error('批量删除用户失败:', error);
            message.error('批量删除用户失败');
          }
        }
      });
    };
    
    // 导出用户数据
    const handleExport = () => {
      // 实际导出功能需要根据后端接口实现
      message.success('导出功能需要根据实际接口实现');
    };
    
    // 修改用户状态
    const handleStatusChange = async (record, status) => {
      // 设置临时loading状态
      record.statusLoading = true;
      
      try {
        await changeUserStatus(record.id, status ? '1' : '0');
        record.status = status ? '1' : '0';
        message.success(`${status ? '启用' : '禁用'}用户成功`);
      } catch (error) {
        console.error('修改用户状态失败:', error);
        message.error('修改用户状态失败');
      } finally {
        record.statusLoading = false;
      }
    };
    
    // 重置密码
    const handleResetPassword = record => {
      Modal.confirm({
        title: '提示',
        content: `确定要重置用户"${record.username}"的密码吗？`,
        okText: '确定',
        cancelText: '取消',
        onOk: async () => {
          try {
            const res = await resetUserPassword(record.id);
            Modal.success({
              title: '密码重置成功',
              content: `新密码：${res.data}`,
              okText: '复制并关闭',
              onOk: () => {
                // 复制到剪贴板
                try {
                  navigator.clipboard.writeText(res.data);
                  message.success('密码已复制到剪贴板');
                } catch (e) {
                  console.error('复制失败:', e);
                }
              }
            });
          } catch (error) {
            console.error('重置密码失败:', error);
            message.error('重置密码失败');
          }
        }
      });
    };
    
    // 查看用户操作日志
    const handleViewLogs = record => {
      logModalVisible.value = true;
      currentUserId.value = record.id;
      logs.value = [];
      logPagination.current = 1;
      
      fetchUserLogs();
    };
    
    // 获取用户操作日志
    const fetchUserLogs = async () => {
      if (!currentUserId.value) return;
      
      logLoading.value = true;
      
      try {
        const res = await getUserActivityLogs(currentUserId.value, {
          page: logPagination.current,
          limit: logPagination.pageSize
        });
        
        logs.value = res.data.list || [];
        logPagination.total = res.data.total || 0;
      } catch (error) {
        console.error('获取用户操作日志失败:', error);
        message.error('获取操作日志失败');
      } finally {
        logLoading.value = false;
      }
    };
    
    // 操作日志表格变化处理
    const handleLogTableChange = ({ current, pageSize }) => {
      logPagination.current = current;
      logPagination.pageSize = pageSize;
      fetchUserLogs();
    };
    
    // 格式化操作类型
    const formatAction = action => {
      const actionMap = {
        login: '登录',
        logout: '登出',
        create: '创建',
        update: '更新',
        delete: '删除',
        import: '导入',
        export: '导出',
        upload: '上传',
        download: '下载'
      };
      
      return actionMap[action] || action;
    };
    
    // 头像上传前检查
    const beforeUpload = file => {
      const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
      if (!isJpgOrPng) {
        message.error('只能上传JPG/PNG格式的图片！');
      }
      
      const isLt2M = file.size / 1024 / 1024 < 2;
      if (!isLt2M) {
        message.error('图片大小不能超过2MB！');
      }
      
      return isJpgOrPng && isLt2M ? true : Upload.LIST_IGNORE;
    };
    
    // 头像上传变化处理
    const handleAvatarChange = info => {
      if (info.file.status === 'uploading') {
        return;
      }
      
      if (info.file.status === 'done') {
        // 上传成功，设置头像URL
        if (info.file.response && info.file.response.success) {
          form.avatar = info.file.response.data;
          message.success('上传头像成功');
        } else {
          message.error('上传头像失败');
        }
      } else if (info.file.status === 'error') {
        message.error('上传头像失败');
      }
    };
    
    // 组件挂载时加载数据
    onMounted(() => {
      fetchUserList();
      fetchRoles();
    });
    
    return {
      // 状态
      loading,
      dataSource,
      selectedRowKeys,
      roles,
      queryParams,
      pagination,
      columns,
      modalVisible,
      modalType,
      modalTitle,
      confirmLoading,
      formRef,
      form,
      rules,
      logModalVisible,
      logLoading,
      logs,
      logPagination,
      logColumns,
      
      // 方法
      handleSearch,
      handleReset,
      handleTableChange,
      onSelectChange,
      handleAdd,
      handleEdit,
      handleView,
      handleModalSubmit,
      handleDelete,
      handleBatchDelete,
      handleExport,
      handleStatusChange,
      handleResetPassword,
      handleViewLogs,
      handleLogTableChange,
      formatAction,
      beforeUpload,
      handleAvatarChange
    };
  }
});
</script>

<style lang="less" scoped>
.user-container {
  padding: 24px;
  
  .search-card {
    margin-bottom: 16px;
  }
  
  :deep(.ant-upload-select-picture-card) {
    width: 128px;
    height: 128px;
    border-radius: 50%;
    overflow: hidden;
    
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
}
</style> 