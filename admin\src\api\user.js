import { get, post, put, del } from './index';

/**
 * 获取用户列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.limit - 每页数量
 * @param {string} params.keyword - 搜索关键词
 * @param {string} params.status - 用户状态
 * @returns {Promise}
 */
export function getUserList(params) {
  return get('/users', params);
}

/**
 * 获取用户详情
 * @param {string} id - 用户ID
 * @returns {Promise}
 */
export function getUserDetail(id) {
  return get(`/users/${id}`);
}

/**
 * 创建用户
 * @param {Object} data - 用户数据
 * @returns {Promise}
 */
export function createUser(data) {
  return post('/users', data);
}

/**
 * 更新用户
 * @param {string} id - 用户ID
 * @param {Object} data - 用户数据
 * @returns {Promise}
 */
export function updateUser(id, data) {
  return put(`/users/${id}`, data);
}

/**
 * 删除用户
 * @param {string} id - 用户ID
 * @returns {Promise}
 */
export function deleteUser(id) {
  return del(`/users/${id}`);
}

/**
 * 批量删除用户
 * @param {Array} ids - 用户ID数组
 * @returns {Promise}
 */
export function batchDeleteUsers(ids) {
  return post('/users/batch-delete', { ids });
}

/**
 * 修改用户状态
 * @param {string} id - 用户ID
 * @param {string} status - 用户状态
 * @returns {Promise}
 */
export function changeUserStatus(id, status) {
  return put(`/users/${id}/status`, { status });
}

/**
 * 重置用户密码
 * @param {string} id - 用户ID
 * @returns {Promise} - 返回新生成的密码或重置密码的结果
 */
export function resetUserPassword(id) {
  return post(`/users/${id}/reset-password`);
}

/**
 * 获取用户活动日志
 * @param {string} id - 用户ID
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.limit - 每页数量
 * @returns {Promise}
 */
export function getUserActivityLogs(id, params) {
  return get(`/users/${id}/activity-logs`, params);
}

/**
 * 获取用户角色列表
 * @returns {Promise}
 */
export function getUserRoles() {
  return get('/user-roles');
}

/**
 * 分配用户角色
 * @param {string} userId - 用户ID
 * @param {Array} roleIds - 角色ID数组
 * @returns {Promise}
 */
export function assignUserRoles(userId, roleIds) {
  return post(`/users/${userId}/roles`, { roleIds });
}

/**
 * 导出用户数据
 * @param {Object} params - 导出参数
 * @returns {Promise}
 */
export function exportUsers(params) {
  return get('/users/export', {
    ...params,
    responseType: 'blob'
  });
}

export default {
  getUserList,
  getUserDetail,
  createUser,
  updateUser,
  deleteUser,
  batchDeleteUsers,
  changeUserStatus,
  resetUserPassword,
  getUserActivityLogs,
  getUserRoles,
  assignUserRoles,
  exportUsers
}; 