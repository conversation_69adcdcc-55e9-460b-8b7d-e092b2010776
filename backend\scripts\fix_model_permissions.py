#!/usr/bin/env python
"""
WAN模型权限修复工具
用于诊断和修复模型目录的权限问题
"""

import os
import sys
import argparse
import logging
import subprocess
import platform
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_permissions(directory):
    """检查目录权限"""
    if not os.path.exists(directory):
        logger.warning(f"目录不存在: {directory}")
        return False, "目录不存在"
    
    try:
        # 尝试创建测试文件
        test_file = os.path.join(directory, ".permission_test")
        with open(test_file, 'w') as f:
            f.write("test")
        os.remove(test_file)
        logger.info(f"目录权限正常: {directory}")
        return True, "权限正常"
    except Exception as e:
        logger.error(f"权限测试失败: {e}")
        return False, str(e)

def fix_permissions_windows(directory):
    """在Windows上修复目录权限"""
    try:
        logger.info(f"尝试修复Windows权限: {directory}")
        
        # 确保目录存在
        os.makedirs(directory, exist_ok=True)
        
        # 使用icacls命令授予当前用户完全控制权限
        command = f'icacls "{directory}" /grant:r "%USERNAME%":(OI)(CI)F /T'
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        
        if result.returncode != 0:
            logger.error(f"icacls命令失败: {result.stderr}")
            return False, result.stderr
        
        logger.info(f"权限修复命令执行成功: {result.stdout}")
        
        # 验证修复是否成功
        success, message = check_permissions(directory)
        return success, "权限修复成功" if success else f"权限修复失败: {message}"
    
    except Exception as e:
        logger.error(f"修复权限时出错: {e}")
        return False, str(e)

def fix_permissions_unix(directory):
    """在Unix/Linux/MacOS上修复目录权限"""
    try:
        logger.info(f"尝试修复Unix权限: {directory}")
        
        # 确保目录存在
        os.makedirs(directory, exist_ok=True)
        
        # 使用chmod命令修改权限
        command = f'chmod -R 755 "{directory}"'
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        
        if result.returncode != 0:
            logger.error(f"chmod命令失败: {result.stderr}")
            return False, result.stderr
        
        logger.info(f"权限修复命令执行成功")
        
        # 验证修复是否成功
        success, message = check_permissions(directory)
        return success, "权限修复成功" if success else f"权限修复失败: {message}"
    
    except Exception as e:
        logger.error(f"修复权限时出错: {e}")
        return False, str(e)

def setup_alternative_directory():
    """设置备用的模型目录"""
    user_home = os.path.expanduser("~")
    wan_models_dir = os.path.join(user_home, ".wan_models")
    
    try:
        os.makedirs(wan_models_dir, exist_ok=True)
        logger.info(f"创建备用模型目录: {wan_models_dir}")
        
        # 检查权限
        success, message = check_permissions(wan_models_dir)
        if not success:
            logger.warning(f"备用目录权限检查失败: {message}")
            
            # 尝试修复权限
            if os.name == 'nt':
                fix_permissions_windows(wan_models_dir)
            else:
                fix_permissions_unix(wan_models_dir)
        
        # 创建一个环境变量设置批处理文件
        if os.name == 'nt':
            bat_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "run_with_env.bat")
            with open(bat_path, 'w') as f:
                f.write('@echo off\n')
                f.write('echo Setting up WAN model environment...\n')
                f.write(f'set WAN_MODEL_CACHE_DIR={wan_models_dir}\n')
                f.write(f'set WAN_T2V_14B_MODEL=Wan-AI/Wan2.1-T2V-14B-Diffusers\n')
                f.write(f'set WAN_T2V_1_3B_MODEL=Wan-AI/Wan2.1-T2V-1.3B-Diffusers\n')
                f.write('echo Environment variables set.\n')
                f.write('echo Starting application...\n')
                f.write('cd ..\n')
                f.write('python -m backend.main\n')
            
            logger.info(f"创建了启动脚本: {bat_path}")
            print(f"\n您可以使用以下批处理文件启动应用程序，该文件已经设置了正确的环境变量:\n{bat_path}\n")
        
        return wan_models_dir
    
    except Exception as e:
        logger.error(f"设置备用目录时出错: {e}")
        return None

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="WAN模型权限修复工具")
    parser.add_argument("--dir", help="要检查和修复的模型目录", default="local_models/wan/Wan2.1/models")
    parser.add_argument("--alt", help="设置备用模型目录", action="store_true")
    
    args = parser.parse_args()
    
    print("\n===== WAN模型权限修复工具 =====\n")
    
    # 显示系统信息
    print(f"系统信息:")
    print(f"  操作系统: {platform.system()} {platform.release()}")
    print(f"  Python版本: {platform.python_version()}")
    print(f"  当前用户: {os.getlogin() if hasattr(os, 'getlogin') else '未知'}")
    print(f"  工作目录: {os.getcwd()}\n")
    
    # 检查指定目录
    target_dir = os.path.normpath(args.dir)
    print(f"目标目录: {target_dir}")
    
    success, message = check_permissions(target_dir)
    print(f"权限检查结果: {'成功' if success else '失败'} - {message}\n")
    
    if not success:
        print("尝试修复权限问题...")
        
        if os.name == 'nt':
            fix_success, fix_message = fix_permissions_windows(target_dir)
        else:
            fix_success, fix_message = fix_permissions_unix(target_dir)
            
        print(f"修复结果: {'成功' if fix_success else '失败'} - {fix_message}\n")
        
        if not fix_success or args.alt:
            print("设置备用目录...")
            alt_dir = setup_alternative_directory()
            if alt_dir:
                print(f"备用目录设置成功: {alt_dir}")
                print("请使用此目录作为模型缓存目录，或使用生成的批处理文件启动应用程序。\n")
    
    print("\n===== 诊断和修复完成 =====\n")

if __name__ == "__main__":
    main() 