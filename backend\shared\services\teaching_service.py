import os
import json
from typing import Dict, List, Optional
from services.open_source_tts import get_open_source_tts_service
from services.open_source_lip_sync import get_open_source_lip_sync

class TeachingService:
    """语言教学服务"""
    
    def __init__(self):
        self.tts = get_open_source_tts_service()
        self.lip_sync = get_open_source_lip_sync()
        self.lessons = self._load_lessons()
    
    def _load_lessons(self) -> Dict:
        """加载课程数据"""
        lessons_path = "data/lessons"
        os.makedirs(lessons_path, exist_ok=True)
        
        lessons = {}
        for lang in self.tts.get_supported_languages():
            lang_path = os.path.join(lessons_path, f"{lang}.json")
            if os.path.exists(lang_path):
                with open(lang_path, "r", encoding="utf-8") as f:
                    lessons[lang] = json.load(f)
        
        return lessons
    
    async def start_lesson(self, 
                          lesson_id: str,
                          language: str) -> Dict:
        """开始课程"""
        try:
            lesson = self.lessons[language][lesson_id]
            steps = []
            
            for step in lesson["steps"]:
                # 生成语音和口型
                audio_path = self.tts.text_to_speech(
                    step["text"],
                    language
                )
                
                visemes = self.lip_sync.generate_visemes(audio_path)
                
                steps.append({
                    "text": step["text"],
                    "audio": audio_path,
                    "visemes": visemes,
                    "type": step["type"],
                    "hints": step.get("hints", [])
                })
            
            return {
                "lesson_id": lesson_id,
                "title": lesson["title"],
                "description": lesson["description"],
                "steps": steps
            }
            
        except Exception as e:
            print(f"课程加载失败: {e}")
            return {"error": str(e)}
    
    async def practice_word(self,
                          word: str,
                          language: str,
                          user_audio: Optional[str] = None) -> Dict:
        """练习单词"""
        try:
            # 生成标准发音
            audio_path = self.tts.text_to_speech(word, language)
            visemes = self.lip_sync.generate_visemes(audio_path)
            
            result = {
                "word": word,
                "standard_audio": audio_path,
                "visemes": visemes,
                "phonetics": self._get_phonetics(word, language)
            }
            
            # 如果有用户录音，进行评估
            if user_audio:
                score = self._evaluate_pronunciation(user_audio, word)
                result["score"] = score
                result["feedback"] = self._generate_feedback(score)
            
            return result
            
        except Exception as e:
            print(f"单词练习失败: {e}")
            return {"error": str(e)}
    
    def _get_phonetics(self, word: str, language: str) -> str:
        """获取音标"""
        # TODO: 实现音标获取逻辑
        return ""
    
    def _evaluate_pronunciation(self, 
                              user_audio: str,
                              target_word: str) -> float:
        """评估发音"""
        # TODO: 实现发音评估逻辑
        return 0.0
    
    def _generate_feedback(self, score: float) -> str:
        """生成反馈"""
        if score >= 0.9:
            return "发音非常标准！"
        elif score >= 0.7:
            return "发音不错，继续努力！"
        elif score >= 0.5:
            return "发音基本正确，但还需要练习。"
        else:
            return "发音需要改进，请多听标准发音。"

def get_teaching_service():
    """获取教学服务实例"""
    return TeachingService() 