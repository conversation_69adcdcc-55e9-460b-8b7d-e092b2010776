#!/usr/bin/env python3
"""
多模态交互服务
支持图像、文档、语音等多种输入方式
"""

import asyncio
import logging
from typing import Optional, Dict, Any, List, Union
from pathlib import Path
import base64
import io
from PIL import Image
import json

logger = logging.getLogger(__name__)

class MultimodalProcessor:
    """多模态处理器"""
    
    def __init__(self):
        self.supported_image_formats = {'.jpg', '.jpeg', '.png', '.bmp', '.gif', '.webp'}
        self.supported_document_formats = {'.pdf', '.txt', '.docx', '.md'}
        self.supported_audio_formats = {'.wav', '.mp3', '.m4a', '.ogg'}
    
    async def process_image_input(self, image_data: Union[str, bytes, Path]) -> Dict[str, Any]:
        """处理图像输入"""
        try:
            # 解析图像
            if isinstance(image_data, str):
                if image_data.startswith('data:image'):
                    # Base64编码的图像
                    image = self._decode_base64_image(image_data)
                else:
                    # 文件路径
                    image = Image.open(image_data)
            elif isinstance(image_data, bytes):
                image = Image.open(io.BytesIO(image_data))
            elif isinstance(image_data, Path):
                image = Image.open(image_data)
            else:
                raise ValueError("不支持的图像数据格式")
            
            # 图像分析
            analysis_result = await self._analyze_image(image)
            
            return {
                "type": "image",
                "success": True,
                "analysis": analysis_result,
                "size": image.size,
                "format": image.format,
                "mode": image.mode
            }
            
        except Exception as e:
            logger.error(f"❌ 图像处理失败: {e}")
            return {
                "type": "image",
                "success": False,
                "error": str(e)
            }
    
    async def process_document_input(self, document_path: Path) -> Dict[str, Any]:
        """处理文档输入"""
        try:
            # 提取文档内容
            content = await self._extract_document_content(document_path)
            
            # 文档分析
            analysis_result = await self._analyze_document(content)
            
            return {
                "type": "document",
                "success": True,
                "content": content[:1000],  # 限制长度
                "full_content": content,
                "analysis": analysis_result,
                "file_size": document_path.stat().st_size,
                "format": document_path.suffix
            }
            
        except Exception as e:
            logger.error(f"❌ 文档处理失败: {e}")
            return {
                "type": "document",
                "success": False,
                "error": str(e)
            }
    
    async def process_audio_input(self, audio_path: Path) -> Dict[str, Any]:
        """处理音频输入"""
        try:
            # 音频转录
            transcription = await self._transcribe_audio(audio_path)
            
            # 音频分析
            analysis_result = await self._analyze_audio(audio_path)
            
            return {
                "type": "audio",
                "success": True,
                "transcription": transcription,
                "analysis": analysis_result,
                "file_size": audio_path.stat().st_size,
                "format": audio_path.suffix
            }
            
        except Exception as e:
            logger.error(f"❌ 音频处理失败: {e}")
            return {
                "type": "audio",
                "success": False,
                "error": str(e)
            }
    
    def _decode_base64_image(self, base64_string: str) -> Image.Image:
        """解码Base64图像"""
        # 移除data:image前缀
        if ',' in base64_string:
            base64_string = base64_string.split(',')[1]
        
        # 解码
        image_data = base64.b64decode(base64_string)
        return Image.open(io.BytesIO(image_data))
    
    async def _analyze_image(self, image: Image.Image) -> Dict[str, Any]:
        """分析图像内容"""
        try:
            # 基础图像信息
            analysis = {
                "dimensions": image.size,
                "aspect_ratio": round(image.size[0] / image.size[1], 2),
                "color_mode": image.mode,
                "has_transparency": image.mode in ('RGBA', 'LA'),
            }
            
            # 尝试使用更高级的图像分析
            try:
                # 这里可以集成图像识别API
                analysis["description"] = "这是一张图片，我可以看到其中的内容。"
                analysis["objects"] = ["图像内容"]
                analysis["colors"] = self._analyze_dominant_colors(image)
                analysis["brightness"] = self._calculate_brightness(image)
                
            except Exception as e:
                logger.warning(f"高级图像分析失败: {e}")
                analysis["description"] = "我收到了一张图片，但无法详细分析其内容。"
            
            return analysis
            
        except Exception as e:
            logger.error(f"图像分析失败: {e}")
            return {"error": str(e)}
    
    def _analyze_dominant_colors(self, image: Image.Image) -> List[str]:
        """分析主要颜色"""
        try:
            # 转换为RGB模式
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            # 缩小图像以提高性能
            image = image.resize((50, 50))
            
            # 获取颜色统计
            colors = image.getcolors(maxcolors=256*256*256)
            if colors:
                # 获取最常见的颜色
                dominant_colors = sorted(colors, key=lambda x: x[0], reverse=True)[:3]
                color_names = []
                
                for count, color in dominant_colors:
                    color_name = self._rgb_to_color_name(color)
                    color_names.append(color_name)
                
                return color_names
            
            return ["未知"]
            
        except Exception as e:
            logger.error(f"颜色分析失败: {e}")
            return ["未知"]
    
    def _rgb_to_color_name(self, rgb: tuple) -> str:
        """RGB转颜色名称"""
        r, g, b = rgb
        
        if r > 200 and g > 200 and b > 200:
            return "白色"
        elif r < 50 and g < 50 and b < 50:
            return "黑色"
        elif r > g and r > b:
            return "红色"
        elif g > r and g > b:
            return "绿色"
        elif b > r and b > g:
            return "蓝色"
        elif r > 150 and g > 150:
            return "黄色"
        elif r > 150 and b > 150:
            return "紫色"
        elif g > 150 and b > 150:
            return "青色"
        else:
            return "混合色"
    
    def _calculate_brightness(self, image: Image.Image) -> str:
        """计算图像亮度"""
        try:
            # 转换为灰度
            grayscale = image.convert('L')
            
            # 计算平均亮度
            pixels = list(grayscale.getdata())
            avg_brightness = sum(pixels) / len(pixels)
            
            if avg_brightness > 180:
                return "很亮"
            elif avg_brightness > 120:
                return "中等亮度"
            elif avg_brightness > 60:
                return "较暗"
            else:
                return "很暗"
                
        except Exception as e:
            logger.error(f"亮度计算失败: {e}")
            return "未知"
    
    async def _extract_document_content(self, document_path: Path) -> str:
        """提取文档内容"""
        try:
            suffix = document_path.suffix.lower()
            
            if suffix == '.txt':
                return document_path.read_text(encoding='utf-8')
            elif suffix == '.md':
                return document_path.read_text(encoding='utf-8')
            elif suffix == '.pdf':
                return await self._extract_pdf_content(document_path)
            elif suffix == '.docx':
                return await self._extract_docx_content(document_path)
            else:
                raise ValueError(f"不支持的文档格式: {suffix}")
                
        except Exception as e:
            logger.error(f"文档内容提取失败: {e}")
            raise
    
    async def _extract_pdf_content(self, pdf_path: Path) -> str:
        """提取PDF内容"""
        try:
            import PyPDF2
            
            content = []
            with open(pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                for page in pdf_reader.pages:
                    content.append(page.extract_text())
            
            return '\n'.join(content)
            
        except ImportError:
            logger.warning("PyPDF2未安装，无法处理PDF文件")
            return "PDF文件内容（需要安装PyPDF2库）"
        except Exception as e:
            logger.error(f"PDF提取失败: {e}")
            return f"PDF文件（提取失败: {e}）"
    
    async def _extract_docx_content(self, docx_path: Path) -> str:
        """提取DOCX内容"""
        try:
            from docx import Document
            
            doc = Document(docx_path)
            content = []
            
            for paragraph in doc.paragraphs:
                content.append(paragraph.text)
            
            return '\n'.join(content)
            
        except ImportError:
            logger.warning("python-docx未安装，无法处理DOCX文件")
            return "DOCX文件内容（需要安装python-docx库）"
        except Exception as e:
            logger.error(f"DOCX提取失败: {e}")
            return f"DOCX文件（提取失败: {e}）"
    
    async def _analyze_document(self, content: str) -> Dict[str, Any]:
        """分析文档内容"""
        try:
            analysis = {
                "length": len(content),
                "word_count": len(content.split()),
                "line_count": len(content.split('\n')),
                "language": self._detect_language(content),
                "summary": self._generate_summary(content)
            }
            
            return analysis
            
        except Exception as e:
            logger.error(f"文档分析失败: {e}")
            return {"error": str(e)}
    
    def _detect_language(self, text: str) -> str:
        """检测文本语言"""
        # 简单的语言检测
        chinese_chars = sum(1 for char in text if '\u4e00' <= char <= '\u9fff')
        total_chars = len(text)
        
        if total_chars == 0:
            return "未知"
        
        chinese_ratio = chinese_chars / total_chars
        
        if chinese_ratio > 0.3:
            return "中文"
        else:
            return "英文"
    
    def _generate_summary(self, content: str) -> str:
        """生成文档摘要"""
        # 简单的摘要生成
        lines = content.split('\n')
        non_empty_lines = [line.strip() for line in lines if line.strip()]
        
        if not non_empty_lines:
            return "空文档"
        
        # 取前几行作为摘要
        summary_lines = non_empty_lines[:3]
        summary = ' '.join(summary_lines)
        
        if len(summary) > 200:
            summary = summary[:200] + "..."
        
        return summary
    
    async def _transcribe_audio(self, audio_path: Path) -> str:
        """转录音频"""
        try:
            # 这里可以集成Whisper或其他ASR
            # 目前返回模拟结果
            return f"音频转录内容（文件: {audio_path.name}）"
            
        except Exception as e:
            logger.error(f"音频转录失败: {e}")
            return f"音频转录失败: {e}"
    
    async def _analyze_audio(self, audio_path: Path) -> Dict[str, Any]:
        """分析音频"""
        try:
            # 基础音频信息
            analysis = {
                "file_size": audio_path.stat().st_size,
                "format": audio_path.suffix,
                "estimated_duration": "未知",
                "quality": "未知"
            }
            
            return analysis
            
        except Exception as e:
            logger.error(f"音频分析失败: {e}")
            return {"error": str(e)}

# 全局实例
multimodal_processor = MultimodalProcessor()
