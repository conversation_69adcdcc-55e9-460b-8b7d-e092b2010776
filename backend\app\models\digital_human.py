"""
数字人模型
"""

from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, Float, JSON
from sqlalchemy.sql import func
from app.core.sqlalchemy_db import Base

class DigitalHuman(Base):
    """数字人模型"""
    __tablename__ = "digital_humans"

    id = Column(Integer, primary_key=True, index=True)
    
    # 基本信息
    name = Column(String(100), nullable=False, comment="数字人名称")
    type = Column(String(50), nullable=False, comment="数字人类型")
    gender = Column(String(10), nullable=False, comment="性别")
    description = Column(Text, comment="描述")
    welcome_text = Column(Text, comment="欢迎语")
    
    # 外观设置
    appearance_type = Column(String(20), default="template", comment="外观类型")
    selected_template = Column(String(50), comment="选择的模板ID")
    uploaded_image_url = Column(Text, comment="上传的图片URL")
    generated_image_url = Column(Text, comment="生成的图片URL")
    
    # 外观特征
    facial_expression = Column(String(20), default="friendly", comment="面部表情")
    clothing_style = Column(String(20), default="business", comment="服装风格")
    background = Column(String(20), default="office", comment="背景环境")
    
    # 声音设置
    voice_type = Column(String(20), default="standard", comment="声音类型")
    voice_speed = Column(Float, default=1.0, comment="语速")
    voice_pitch = Column(Float, default=1.0, comment="音调")
    selected_voice = Column(String(50), comment="选择的声音ID")
    custom_voice_url = Column(Text, comment="自定义声音URL")
    
    # 生成状态
    status = Column(String(20), default="creating", comment="状态")  # creating, processing, completed, failed
    progress = Column(Integer, default=0, comment="生成进度")
    error_message = Column(Text, comment="错误信息")
    
    # 生成结果
    avatar_video_url = Column(Text, comment="头像视频URL")
    voice_sample_url = Column(Text, comment="声音样本URL")
    model_config = Column(JSON, comment="模型配置")
    
    # 元数据
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")
    created_by = Column(Integer, comment="创建者ID")
    is_active = Column(Boolean, default=True, comment="是否激活")
    
    def to_dict(self):
        """转换为字典"""
        return {
            "id": self.id,
            "name": self.name,
            "type": self.type,
            "gender": self.gender,
            "description": self.description,
            "welcome_text": self.welcome_text,
            "appearance_type": self.appearance_type,
            "selected_template": self.selected_template,
            "uploaded_image_url": self.uploaded_image_url,
            "generated_image_url": self.generated_image_url,
            "facial_expression": self.facial_expression,
            "clothing_style": self.clothing_style,
            "background": self.background,
            "voice_type": self.voice_type,
            "voice_speed": self.voice_speed,
            "voice_pitch": self.voice_pitch,
            "selected_voice": self.selected_voice,
            "custom_voice_url": self.custom_voice_url,
            "status": self.status,
            "progress": self.progress,
            "error_message": self.error_message,
            "avatar_video_url": self.avatar_video_url,
            "voice_sample_url": self.voice_sample_url,
            "model_config": self.model_config,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "created_by": self.created_by,
            "is_active": self.is_active
        }

class DigitalHumanGeneration(Base):
    """数字人生成任务模型"""
    __tablename__ = "digital_human_generations"

    id = Column(Integer, primary_key=True, index=True)
    digital_human_id = Column(Integer, nullable=False, comment="数字人ID")
    
    # 任务信息
    task_id = Column(String(100), unique=True, nullable=False, comment="任务ID")
    celery_task_id = Column(String(100), comment="Celery任务ID")
    task_type = Column(String(50), nullable=False, comment="任务类型")  # avatar, voice, full
    status = Column(String(20), default="pending", comment="任务状态")  # pending, running, completed, failed
    progress = Column(Integer, default=0, comment="进度百分比")
    message = Column(String(500), comment="状态消息")
    
    # 输入参数
    input_data = Column(JSON, comment="输入数据")
    
    # 输出结果
    output_data = Column(JSON, comment="输出数据")
    result_urls = Column(JSON, comment="结果文件URLs")
    
    # 错误信息
    error_message = Column(Text, comment="错误信息")
    error_details = Column(JSON, comment="错误详情")
    
    # 性能指标
    start_time = Column(DateTime(timezone=True), comment="开始时间")
    end_time = Column(DateTime(timezone=True), comment="结束时间")
    processing_time = Column(Float, comment="处理时间(秒)")
    
    # 元数据
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")
    
    def to_dict(self):
        """转换为字典"""
        return {
            "id": self.id,
            "digital_human_id": self.digital_human_id,
            "task_id": self.task_id,
            "task_type": self.task_type,
            "status": self.status,
            "progress": self.progress,
            "input_data": self.input_data,
            "output_data": self.output_data,
            "result_urls": self.result_urls,
            "error_message": self.error_message,
            "error_details": self.error_details,
            "start_time": self.start_time.isoformat() if self.start_time else None,
            "end_time": self.end_time.isoformat() if self.end_time else None,
            "processing_time": self.processing_time,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }
