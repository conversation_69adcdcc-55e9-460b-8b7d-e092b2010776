"""
WebSocket路由
"""

import json
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Query
from typing import Optional
from ...services.websocket_service import websocket_service
import logging

logger = logging.getLogger(__name__)

router = APIRouter()


@router.websocket("/ws/{user_id}")
async def websocket_endpoint(websocket: WebSocket, user_id: str):
    """WebSocket连接端点"""
    await websocket_service.manager.connect(websocket, user_id)
    
    try:
        # 启动预警处理器（如果还未启动）
        await websocket_service.start_alert_processor()
        
        # 发送连接成功消息
        welcome_message = {
            'type': 'connection_established',
            'data': {
                'user_id': user_id,
                'message': '连接成功，欢迎使用舆情分析平台实时服务'
            },
            'timestamp': websocket_service.manager.active_connections.__len__()
        }
        await websocket_service.manager.send_personal_message(user_id, welcome_message)
        
        # 监听消息
        while True:
            try:
                # 接收客户端消息
                data = await websocket.receive_text()
                message = json.loads(data)
                
                # 处理消息
                await websocket_service.handle_websocket_message(user_id, message)
                
            except WebSocketDisconnect:
                break
            except json.JSONDecodeError:
                error_message = {
                    'type': 'error',
                    'data': {'message': '消息格式错误，请发送有效的JSON'},
                    'timestamp': websocket_service.manager.active_connections.__len__()
                }
                await websocket_service.manager.send_personal_message(user_id, error_message)
            except Exception as e:
                logger.error(f"WebSocket消息处理错误: {str(e)}")
                error_message = {
                    'type': 'error',
                    'data': {'message': f'服务器错误: {str(e)}'},
                    'timestamp': websocket_service.manager.active_connections.__len__()
                }
                await websocket_service.manager.send_personal_message(user_id, error_message)
    
    except WebSocketDisconnect:
        pass
    except Exception as e:
        logger.error(f"WebSocket连接错误: {str(e)}")
    finally:
        websocket_service.manager.disconnect(user_id)


@router.get("/ws/stats")
async def get_websocket_stats():
    """获取WebSocket连接统计"""
    stats = websocket_service.manager.get_connection_stats()
    return {
        'success': True,
        'data': stats
    }


@router.post("/ws/broadcast")
async def broadcast_message(message_data: dict):
    """广播消息（管理员功能）"""
    try:
        await websocket_service.send_system_notification(message_data)
        return {
            'success': True,
            'message': '消息广播成功'
        }
    except Exception as e:
        return {
            'success': False,
            'message': f'广播失败: {str(e)}'
        }


@router.post("/ws/alert")
async def send_alert(alert_data: dict):
    """发送预警（系统调用）"""
    try:
        await websocket_service.send_alert(alert_data)
        return {
            'success': True,
            'message': '预警发送成功'
        }
    except Exception as e:
        return {
            'success': False,
            'message': f'预警发送失败: {str(e)}'
        }
