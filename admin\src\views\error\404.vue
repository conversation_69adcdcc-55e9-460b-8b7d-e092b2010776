<template>
  <div class="error-container">
    <div class="error-content">
      <div class="error-image">
        <!-- 使用内联SVG替代外部文件 -->
        <svg width="240" height="200" viewBox="0 0 400 400" xmlns="http://www.w3.org/2000/svg">
          <path fill="#1890ff" d="M363.36,299.69a184.92,184.92,0,0,1-321.56-1.9c-.07-.11-.15-.22-.22-.33A183.68,183.68,0,0,1,107.64,83.28a2.75,2.75,0,0,1,3.56,3.35c-17.54,49.71-7.27,105.4,28.37,146.13,30.67,35,74.52,53.67,118.76,49.89a120.83,120.83,0,0,0,96.39-64.42c15.69-30.33,18.83-65.59,9.14-98.2a2.75,2.75,0,0,1,3.65-3.27A184.08,184.08,0,0,1,363.36,299.69Z"/>
          <path fill="#fff" d="M118.87,233.38l43.93,43.93a12,12,0,0,0,17,0L223.69,233.4a12,12,0,0,0,0-17l-43.94-43.93a12,12,0,0,0-17,0l-43.92,43.92A12,12,0,0,0,118.87,233.38Z"/>
          <path fill="#fff" d="M286.13,233.38l-43.93-43.93a12,12,0,0,1,0-17l43.93-43.92a12,12,0,0,1,17,0l43.93,43.93a12,12,0,0,1,0,17l-43.93,43.92A12,12,0,0,1,286.13,233.38Z"/>
          <path fill="#1890ff" d="M131.58,216.38l31.22,31.22a12,12,0,0,0,17,0l31.22-31.22a12,12,0,0,0,0-17l-31.22-31.22a12,12,0,0,0-17,0l-31.22,31.22A12,12,0,0,0,131.58,216.38Z"/>
          <path fill="#1890ff" d="M273.42,216.38l-31.22-31.22a12,12,0,0,1,0-17l31.22-31.22a12,12,0,0,1,17,0l31.22,31.22a12,12,0,0,1,0,17l-31.22,31.22A12,12,0,0,1,273.42,216.38Z"/>
        </svg>
      </div>
      <h1 class="error-title">404</h1>
      <p class="error-message">抱歉，您访问的页面不存在</p>
      <div class="error-actions">
        <a-button type="primary" @click="goHome">返回首页</a-button>
        <a-button @click="goBack" class="ml-3">返回上一页</a-button>
      </div>
    </div>
  </div>
</template>

<script>
import { defineComponent } from 'vue';
import { useRouter } from 'vue-router';

export default defineComponent({
  name: 'Error404',
  setup() {
    const router = useRouter();
    
    // 返回首页
    const goHome = () => {
      router.push('/');
    };
    
    // 返回上一页
    const goBack = () => {
      router.go(-1);
    };
    
    return {
      goHome,
      goBack
    };
  }
});
</script>

<style lang="less" scoped>
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f0f2f5;
  
  .error-content {
    text-align: center;
    padding: 32px;
    
    .error-image {
      width: 240px;
      margin-bottom: 16px;
      display: inline-block;
    }
    
    .error-title {
      font-size: 72px;
      color: #1890ff;
      margin: 0 0 16px;
      line-height: 1;
    }
    
    .error-message {
      font-size: 20px;
      color: rgba(0, 0, 0, 0.45);
      margin-bottom: 24px;
    }
    
    .error-actions {
      display: flex;
      justify-content: center;
      gap: 16px; /* 增加按钮之间的间距 */
    }
  }
}
</style> 