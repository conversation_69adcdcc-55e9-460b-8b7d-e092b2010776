"""
表单数据处理工具
用于解决表单数据编码问题，确保各种字符集的兼容性
"""
import logging
from fastapi import Request, Form, Depends
from typing import Optional, Dict, Any, Tuple
import re
import json
import urllib.parse
import os
import traceback

# 配置日志
logger = logging.getLogger(__name__)

# 读取环境变量
allow_latin1 = os.getenv("FASTAPI_ALLOW_LATIN1_ENCODING", "").lower() in ["true", "1", "yes"]
accept_multiple_charsets = os.getenv("FASTAPI_ACCEPT_MULTIPLE_CHARSETS", "").lower() in ["true", "1", "yes"]

# 如果启用了多字符集支持，记录日志
if allow_latin1 or accept_multiple_charsets:
    logger.info("增强表单处理启用:")
    if allow_latin1:
        logger.info("- 允许Latin1编码")
    if accept_multiple_charsets:
        logger.info("- 接受多种字符集")

async def parse_form_body(request: Request) -> bytes:
    """
    解析并返回原始表单数据，优先使用已缓存的请求体
    """
    try:
        # 如果请求对象已经有缓存的body，直接返回
        if hasattr(request, "_body") and request._body is not None:
            logger.info("使用已缓存的请求体")
            return request._body
            
        # 否则读取请求体
        try:
            body = await request.body()
            # 保存起来以供后续使用
            request._body = body
            request._body_size = len(body)
            logger.info(f"成功读取原始请求体，大小: {len(body)} 字节")
            return body
        except RuntimeError as e:
            # 处理"Stream consumed"错误
            if "Stream consumed" in str(e):
                logger.warning("请求流已被消费，尝试替代方法")
                # 检查是否还有其他地方可能缓存了请求体
                if hasattr(request.scope, "_body"):
                    return request.scope._body
                # 返回空请求体
                empty_body = b""
                request._body = empty_body
                request._body_size = 0
                return empty_body
            else:
                raise
    except Exception as e:
        logger.error(f"解析请求体时出错: {str(e)}")
        logger.error(traceback.format_exc())
        # 返回空请求体，避免后续处理出错
        empty_body = b""
        request._body = empty_body
        request._body_size = 0
        return empty_body

def safe_decode(data: bytes) -> str:
    """安全地解码数据，尝试多种编码方式"""
    if not data:
        return ""
        
    encodings = ['utf-8', 'latin1', 'iso-8859-1', 'ascii']
    
    for encoding in encodings:
        try:
            return data.decode(encoding)
        except UnicodeDecodeError:
            continue
    
    # 如果所有编码都失败，使用replace错误处理的UTF-8
    try:
        return data.decode('utf-8', errors='replace')
    except Exception as e:
        logger.error(f"所有编码方式都失败: {str(e)}")
        # 最后的手段：强制使用latin1（它可以解码任何字节序列）
        return data.decode('latin1', errors='replace')

async def extract_form_values(request: Request) -> Dict[str, Any]:
    """从请求中提取表单值，增强的错误处理和多种格式支持"""
    result = {}
    
    try:
        # 获取Content-Type
        content_type = request.headers.get('content-type', '').lower()
        logger.info(f"处理请求，Content-Type: {content_type}")
        
        # 1. 尝试获取原始请求体
        try:
            # 检查请求体是否已经被读取
            if hasattr(request, '_body') and request._body:
                body = request._body
                logger.info("使用已保存的请求体")
            else:
                body = await parse_form_body(request)
            
            if not body:
                logger.warning("请求体为空")
                # 对于空请求体，尝试从URL参数获取数据
                try:
                    query_params = dict(request.query_params)
                    if query_params:
                        logger.info(f"请求体为空，使用查询参数: {query_params}")
                        return query_params
                except Exception as qp_err:
                    logger.error(f"获取查询参数失败: {str(qp_err)}")
                
                return {}
        except Exception as e:
            logger.error(f"获取请求体失败: {str(e)}")
            logger.error(traceback.format_exc())
            return {}
            
        # 2. 根据Content-Type尝试不同的解析方法
        logger.info(f"请求体大小: {len(body)} 字节")
        
        # 2.1 JSON数据处理
        if 'application/json' in content_type:
            try:
                # 尝试直接使用FastAPI的json方法
                try:
                    result = await request.json()
                    logger.info("成功使用request.json()解析")
                    return result
                except Exception as e:
                    logger.error(f"FastAPI JSON解析失败: {str(e)}")
                
                # 尝试手动解析JSON
                try:
                    decoded_body = safe_decode(body)
                    logger.info(f"解码后的JSON数据: {decoded_body[:100]}...")
                    result = json.loads(decoded_body)
                    logger.info("成功使用手动JSON解析")
                    return result
                except Exception as manual_err:
                    logger.error(f"手动JSON解析失败: {str(manual_err)}")
                    return {}
            except Exception as json_err:
                logger.error(f"JSON处理过程中出错: {str(json_err)}")
                logger.error(traceback.format_exc())
                return {}
        
        # 2.2 表单数据处理
        elif 'application/x-www-form-urlencoded' in content_type or 'multipart/form-data' in content_type:
            try:
                # 尝试使用FastAPI的form方法
                try:
                    form = await request.form()
                    result = {key: value for key, value in form.items()}
                    if result:
                        logger.info(f"成功使用request.form()解析，找到字段: {list(result.keys())}")
                        return result
                except Exception as form_err:
                    logger.error(f"FastAPI表单解析失败: {str(form_err)}")
                
                # 如果FastAPI方法失败，尝试手动解析
                try:
                    decoded_body = safe_decode(body)
                    logger.info(f"解码后的表单数据: {decoded_body[:100]}...")
                    
                    # 解析表单数据
                    form_data = {}
                    if '&' in decoded_body:
                        for item in decoded_body.split('&'):
                            if '=' in item:
                                key, value = item.split('=', 1)
                                try:
                                    form_data[urllib.parse.unquote(key)] = urllib.parse.unquote(value)
                                except Exception:
                                    # 如果URL解码失败，使用原始值
                                    form_data[key] = value
                    
                    if form_data:
                        logger.info(f"成功使用手动表单解析，找到字段: {list(form_data.keys())}")
                        return form_data
                    else:
                        logger.warning("手动表单解析未找到任何字段")
                except Exception as manual_form_err:
                    logger.error(f"手动表单解析失败: {str(manual_form_err)}")
            except Exception as form_process_err:
                logger.error(f"表单处理过程中出错: {str(form_process_err)}")
                logger.error(traceback.format_exc())
                return {}
        
        # 2.3 尝试作为纯文本处理
        else:
            try:
                text = safe_decode(body)
                logger.info(f"将请求体作为纯文本处理: {text[:100]}...")
                
                # 尝试作为JSON解析
                try:
                    result = json.loads(text)
                    logger.info(f"成功将纯文本作为JSON解析，找到字段: {list(result.keys())}")
                    return result
                except json.JSONDecodeError:
                    # 尝试作为表单数据解析
                    if '=' in text and ('&' in text or '\n' in text):
                        form_data = {}
                        separator = '&' if '&' in text else '\n'
                        for item in text.split(separator):
                            if '=' in item:
                                key, value = item.split('=', 1)
                                form_data[key.strip()] = value.strip()
                        
                        if form_data:
                            logger.info(f"成功将纯文本作为表单数据解析，找到字段: {list(form_data.keys())}")
                            return form_data
                    
                    # 如果都不是，返回纯文本
                    logger.info("无法解析为JSON或表单，返回纯文本")
                    return {"text": text}
            except Exception as text_err:
                logger.error(f"纯文本处理失败: {str(text_err)}")
                logger.error(traceback.format_exc())
                return {}
        
        # 3. 所有方法都失败，返回空字典
        logger.warning("所有解析方法都失败，返回空结果")
        return {}
        
    except Exception as e:
        logger.error(f"表单处理错误: {str(e)}")
        logger.error(traceback.format_exc())
        return {}

# 使用示例:
# 
# from utils.form_utils import extract_form_values
#
# @app.post("/login")
# async def login(request: Request):
#     form_data = await extract_form_values(request)
#     username = form_data.get("username")
#     password = form_data.get("password")
#     # 执行登录逻辑 