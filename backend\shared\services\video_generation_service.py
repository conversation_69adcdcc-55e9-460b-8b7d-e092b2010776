import os
import sys
import torch
import logging
import tempfile
import uuid
import time
import importlib
import subprocess
import psutil
from typing import Optional, Dict, Any, List, Tuple
from PIL import Image
import numpy as np
import cv2
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 从环境变量获取配置
MODELSCOPE_CACHE_DIR = os.getenv("MODELSCOPE_CACHE_DIR", "local_models/modelscope")
VIDEO_GENERATION_MODEL_TEXT2VIDEO = os.getenv("VIDEO_GENERATION_MODEL_TEXT2VIDEO", "damo/text-to-video-synthesis")
VIDEO_GENERATION_MODEL_IMAGE2VIDEO = os.getenv("VIDEO_GENERATION_MODEL_IMAGE2VIDEO", "damo/image-to-video-synthesis")
VIDEO_GENERATION_DEVICE = os.getenv("VIDEO_GENERATION_DEVICE", "cuda" if torch.cuda.is_available() else "cpu")

class VideoGenerationService:
    """
    视频生成服务，基于ModelScope的文本到视频和图像到视频模型
    实现WAN 2.1技术的视频生成功能
    """
    
    def __init__(self):
        """初始化视频生成服务"""
        self.device = VIDEO_GENERATION_DEVICE
        logger.info(f"使用设备: {self.device}")
        self.temp_dir = tempfile.gettempdir()
        self.models = {}
        self.is_initialized = False
        self.init_errors = []  # 添加错误信息列表
        
        # 确保模型缓存目录存在
        os.makedirs(MODELSCOPE_CACHE_DIR, exist_ok=True)
        
        # 设置ModelScope缓存目录
        os.environ['MODELSCOPE_CACHE'] = MODELSCOPE_CACHE_DIR
        
    def _check_dependencies(self) -> Tuple[bool, str, List[str]]:
        """
        检查必要的依赖库是否已安装
        
        Returns:
            Tuple[bool, str, List[str]]: 
                - 是否通过检查
                - 结果消息
                - 缺失的依赖列表
        """
        required_packages = ['modelscope', 'datasets', 'transformers', 'diffusers']
        missing_packages = []
        
        for package in required_packages:
            try:
                importlib.import_module(package)
                logger.info(f"依赖库 {package} 已安装")
            except ImportError:
                logger.error(f"依赖库 {package} 未安装")
                missing_packages.append(package)
        
        if missing_packages:
            logger.error(f"缺少必要的依赖库: {', '.join(missing_packages)}")
            logger.info("尝试自动安装缺失的依赖...")
            for package in missing_packages:
                try:
                    subprocess.check_call([sys.executable, "-m", "pip", "install", package])
                    logger.info(f"成功安装 {package}")
                except Exception as e:
                    logger.error(f"安装 {package} 失败: {e}")
            
            # 再次检查
            still_missing = []
            for package in missing_packages:
                try:
                    importlib.import_module(package)
                except ImportError:
                    still_missing.append(package)
            
            if still_missing:
                error_msg = f"依然缺少必要的依赖库: {', '.join(still_missing)}"
                install_cmd = f"pip install {' '.join(still_missing)}"
                return False, error_msg, still_missing
        
        return True, "所有依赖检查通过", []
    
    def _check_system_resources(self) -> Dict[str, Any]:
        """
        检查系统资源状况
        
        Returns:
            Dict[str, Any]: 系统资源状态信息
        """
        resources = {
            "memory": {
                "total": round(psutil.virtual_memory().total / (1024**3), 2),  # GB
                "available": round(psutil.virtual_memory().available / (1024**3), 2),  # GB
                "percent": psutil.virtual_memory().percent
            },
            "cpu": {
                "percent": psutil.cpu_percent(interval=1),
                "count": psutil.cpu_count(),
                "logical_count": psutil.cpu_count(logical=True)
            },
            "device": self.device
        }
        
        # 如果使用CUDA，添加GPU信息
        if self.device == "cuda" and torch.cuda.is_available():
            resources["gpu"] = {
                "name": torch.cuda.get_device_name(0),
                "count": torch.cuda.device_count(),
                "memory_allocated": round(torch.cuda.memory_allocated(0) / (1024**3), 2),  # GB
                "memory_reserved": round(torch.cuda.memory_reserved(0) / (1024**3), 2)  # GB
            }
            
        return resources
        
    def _check_model_files(self) -> Tuple[bool, str]:
        """
        检查模型文件是否已下载到本地
        
        Returns:
            Tuple[bool, str]: 检查结果和消息
        """
        # 这里只是一个简单检查，实际上应该基于特定模型的文件结构进行检查
        model_dirs = {
            'text2video': os.path.join(MODELSCOPE_CACHE_DIR, *VIDEO_GENERATION_MODEL_TEXT2VIDEO.split('/')),
            'image2video': os.path.join(MODELSCOPE_CACHE_DIR, *VIDEO_GENERATION_MODEL_IMAGE2VIDEO.split('/'))
        }
        
        for name, path in model_dirs.items():
            if not os.path.exists(path):
                logger.warning(f"模型 {name} 文件夹不存在: {path}")
                # 创建目录
                os.makedirs(path, exist_ok=True)
                logger.info(f"已创建模型目录: {path}")
        
        return True, "模型目录已准备"
        
    def initialize(self) -> bool:
        """
        延迟初始化模型，避免在服务启动时立即加载大型模型
        
        Returns:
            bool: 初始化是否成功
        """
        if self.is_initialized:
            return True
            
        # 清空之前的错误
        self.init_errors = []
            
        try:
            # 检查系统资源
            resources = self._check_system_resources()
            logger.info(f"系统资源状态: {resources}")
            
            # 内存检查 - 如果可用内存小于4GB，可能会导致问题
            if resources["memory"]["available"] < 4:
                error_msg = f"可用系统内存不足: {resources['memory']['available']}GB，推荐至少4GB"
                logger.warning(error_msg)
                self.init_errors.append(error_msg)
                
            # GPU内存检查（如果使用CUDA）
            if self.device == "cuda" and torch.cuda.is_available():
                if resources.get("gpu", {}).get("memory_reserved", 0) > resources.get("gpu", {}).get("memory_allocated", 0) + 2:
                    warning_msg = f"GPU内存已大部分被占用: 已分配{resources['gpu']['memory_allocated']}GB，已预留{resources['gpu']['memory_reserved']}GB"
                    logger.warning(warning_msg)
                    self.init_errors.append(warning_msg)
            
            # 检查依赖
            deps_ok, deps_msg, missing_deps = self._check_dependencies()
            if not deps_ok:
                error_msg = f"依赖检查失败: {deps_msg}"
                logger.error(error_msg)
                self.init_errors.append(error_msg)
                # 提供解决方案建议
                self.init_errors.append(f"请尝试手动安装缺失的依赖: pip install {' '.join(missing_deps)}")
                return False
            
            # 检查模型文件
            files_ok, files_msg = self._check_model_files()
            if not files_ok:
                error_msg = f"模型文件检查失败: {files_msg}"
                logger.error(error_msg)
                self.init_errors.append(error_msg)
                return False
            
            import time
            start_time = time.time()
            logger.info("开始加载ModelScope视频生成模型...")

            try:
                # 先尝试导入必要的模块，如果失败直接报错
                try:
                    from modelscope.pipelines import pipeline
                    from modelscope.outputs import OutputKeys
                    import datasets  # 确保datasets库已安装
                except ImportError as e:
                    error_msg = f"导入必要模块失败: {e}"
                    logger.error(error_msg)
                    self.init_errors.append(error_msg)
                    
                    # 尝试安装缺失的模块
                    try:
                        missing_module = str(e).split("'")[1]
                        import sys
                        subprocess.check_call([sys.executable, "-m", "pip", "install", missing_module])
                        logger.info(f"已安装缺失模块: {missing_module}")
                    except Exception as install_error:
                        error_msg = f"安装模块失败: {install_error}"
                        logger.error(error_msg)
                        self.init_errors.append(error_msg)
                        self.init_errors.append(f"请尝试手动安装: pip install {missing_module}")
                    
                    # 再次尝试导入
                    try:
                        from modelscope.pipelines import pipeline
                        from modelscope.outputs import OutputKeys
                    except ImportError as e:
                        error_msg = f"二次导入失败，初始化终止: {e}"
                        logger.error(error_msg)
                        self.init_errors.append(error_msg)
                        return False
                
                # 使用try-except分别加载每个模型，避免一个失败导致全部失败
                try:
                    # 加载Text2Video模型
                    logger.info(f"加载文本到视频模型: {VIDEO_GENERATION_MODEL_TEXT2VIDEO}")
                    self.models['text2video'] = pipeline(
                        'text-to-video-synthesis', 
                        model=VIDEO_GENERATION_MODEL_TEXT2VIDEO,
                        device=self.device
                    )
                    logger.info("文本到视频模型加载成功")
                except Exception as e:
                    error_msg = f"加载文本到视频模型失败: {e}"
                    logger.error(error_msg)
                    self.init_errors.append(error_msg)
                    # 返回False但继续尝试加载其他模型
                
                try:
                    # 加载Image2Video模型
                    logger.info(f"加载图像到视频模型: {VIDEO_GENERATION_MODEL_IMAGE2VIDEO}")
                    self.models['image2video'] = pipeline(
                        'image-to-video-synthesis',
                        model=VIDEO_GENERATION_MODEL_IMAGE2VIDEO,
                        device=self.device
                    )
                    logger.info("图像到视频模型加载成功")
                except Exception as e:
                    error_msg = f"加载图像到视频模型失败: {e}"
                    logger.error(error_msg)
                    self.init_errors.append(error_msg)
                
                # 检查是否至少有一个模型加载成功
                if not self.models:
                    error_msg = "所有模型加载均失败"
                    logger.error(error_msg)
                    self.init_errors.append(error_msg)
                    # 提供诊断信息
                    self.init_errors.append(self._generate_troubleshooting_guide())
                    return False
                
            except Exception as e:
                error_msg = f"加载ModelScope模型失败: {e}"
                logger.error(error_msg)
                self.init_errors.append(error_msg)
                # 提供诊断信息
                self.init_errors.append(self._generate_troubleshooting_guide())
                return False
            
            logger.info(f"模型加载完成，耗时: {time.time() - start_time:.2f}秒")
            self.is_initialized = True
            return True
            
        except Exception as e:
            error_msg = f"初始化视频生成服务失败: {e}"
            logger.error(error_msg)
            import traceback
            logger.error(traceback.format_exc())
            self.init_errors.append(error_msg)
            self.init_errors.append(traceback.format_exc())
            # 提供诊断信息
            self.init_errors.append(self._generate_troubleshooting_guide())
            return False
            
    def _generate_troubleshooting_guide(self) -> str:
        """
        生成故障排除指南
        
        Returns:
            str: 故障排除建议
        """
        resources = self._check_system_resources()
        
        guide = "=== 故障排除指南 ==="
        
        # 设备相关建议
        if self.device == "cuda" and not torch.cuda.is_available():
            guide += "\n- CUDA不可用：虽然设置了使用CUDA，但系统未检测到可用的CUDA设备。"
            guide += "\n  解决方案：检查GPU驱动是否正确安装，或设置VIDEO_GENERATION_DEVICE=cpu使用CPU模式。"
        
        # 内存相关建议
        if resources["memory"]["percent"] > 90:
            guide += f"\n- 系统内存不足：系统内存使用率{resources['memory']['percent']}%，可能导致模型加载失败。"
            guide += "\n  解决方案：关闭其他占用内存的应用，或增加系统内存。"
        
        # GPU内存相关建议
        if self.device == "cuda" and torch.cuda.is_available() and resources.get("gpu", {}).get("memory_reserved", 0) > 6:
            guide += f"\n- GPU内存不足：已预留{resources['gpu']['memory_reserved']}GB，可能不足以加载模型。"
            guide += "\n  解决方案：关闭其他使用GPU的应用，或考虑使用更小的模型。"
        
        # 通用建议
        guide += "\n- 检查模型文件：确保模型文件正确下载到ModelScope缓存目录。"
        guide += f"\n  目录路径：{MODELSCOPE_CACHE_DIR}"
        guide += "\n- 依赖问题：尝试重新安装modelscope及其依赖。"
        guide += "\n  命令：pip install -U modelscope transformers diffusers"
        
        return guide
    
    def get_initialization_errors(self) -> List[str]:
        """
        获取初始化过程中的错误列表
        
        Returns:
            List[str]: 错误消息列表
        """
        return self.init_errors
        
    def generate_video_from_text(
        self,
        prompt: str,
        negative_prompt: str = "",
        num_frames: int = 16,
        num_inference_steps: int = 50,
        height: int = 256,
        width: int = 256,
        guidance_scale: float = 7.5,
        fps: int = 8,
        seed: Optional[int] = None,
        output_format: str = "mp4",
        callback=None
    ) -> Dict[str, Any]:
        """
        文本到视频生成
        
        Args:
            prompt: 文本提示词
            negative_prompt: 负面提示词
            num_frames: 生成的视频帧数
            num_inference_steps: 推理步骤数
            height: 视频高度
            width: 视频宽度
            guidance_scale: 提示词指导强度
            fps: 每秒帧数
            seed: 随机种子
            output_format: 输出格式
            callback: 进度回调函数
            
        Returns:
            dict: 包含结果路径和元数据的字典
        """
        # 初始化服务
        init_success = self.initialize()
        if not init_success:
            # 获取详细的初始化错误信息
            errors = self.get_initialization_errors()
            error_details = "\n".join(errors) if errors else "未知错误"
            
            error_msg = "模型初始化失败，请检查日志获取详细信息"
            logger.error(error_msg)
            return {
                "success": False, 
                "error": error_msg,
                "error_details": error_details,
                "troubleshooting": self._generate_troubleshooting_guide()
            }
        
        # 检查是否有text2video模型
        if 'text2video' not in self.models:
            error_msg = "文本到视频模型未成功加载"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
            
        try:
            # 设置随机种子
            if seed is not None:
                torch.manual_seed(seed)
                np.random.seed(seed)
            
            # 进度报告
            if callback:
                callback(10, "模型推理准备中...")
            
            # 执行推理 (ModelScope的text-to-video-synthesis)
            output_video_path = os.path.join(self.temp_dir, f"{uuid.uuid4()}.{output_format}")
            
            # 数据准备
            from modelscope.outputs import OutputKeys
            input_params = {
                'text': prompt,
                'negative_prompt': negative_prompt,
                'num_frames': num_frames,
                'num_inference_steps': num_inference_steps,
                'height': height, 
                'width': width,
                'guidance_scale': guidance_scale,
                'output_video': output_video_path
            }
            
            # 进度报告
            if callback:
                callback(20, "开始生成视频...")
            
            # 执行推理
            result = self.models['text2video'](input_params)
            
            # 确保有帧被生成
            if callback:
                callback(80, "视频帧生成完成，处理中...")
            
            # 生成缩略图
            thumbnail_path = self._generate_thumbnail(output_video_path)
            
            # 创建结果
            result = {
                "success": True,
                "video_path": output_video_path,
                "thumbnail_path": thumbnail_path,
                "metadata": {
                    "duration": num_frames / fps,
                    "fps": fps,
                    "resolution": f"{width}x{height}",
                    "frames": num_frames
                }
            }
            
            if callback:
                callback(100, "视频生成完成")
                
            return result
            
        except Exception as e:
            logger.error(f"文本到视频生成失败: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return {"success": False, "error": str(e)}
    
    def generate_video_from_image(
        self,
        image_path: str,
        prompt: str = "",
        negative_prompt: str = "",
        num_frames: int = 16,
        num_inference_steps: int = 50,
        guidance_scale: float = 7.5,
        fps: int = 8,
        seed: Optional[int] = None,
        output_format: str = "mp4",
        callback=None
    ) -> Dict[str, Any]:
        """
        图像到视频生成
        
        Args:
            image_path: 源图像路径
            prompt: 文本提示词
            negative_prompt: 负面提示词
            num_frames: 生成的视频帧数
            num_inference_steps: 推理步骤数
            guidance_scale: 提示词指导强度
            fps: 每秒帧数
            seed: 随机种子
            output_format: 输出格式
            callback: 进度回调函数
            
        Returns:
            dict: 包含结果路径和元数据的字典
        """
        if not self.initialize():
            # 获取详细的初始化错误信息
            errors = self.get_initialization_errors()
            error_details = "\n".join(errors) if errors else "未知错误"
            
            error_msg = "模型初始化失败，请检查日志获取详细信息"
            logger.error(error_msg)
            return {
                "success": False, 
                "error": error_msg,
                "error_details": error_details,
                "troubleshooting": self._generate_troubleshooting_guide()
            }
            
        # 检查是否有image2video模型
        if 'image2video' not in self.models:
            error_msg = "图像到视频模型未成功加载"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
            
        try:
            # 设置随机种子
            if seed is not None:
                torch.manual_seed(seed)
                np.random.seed(seed)
            
            # 进度报告
            if callback:
                callback(10, "模型推理准备中...")
            
            # 加载源图像
            if not os.path.exists(image_path):
                error_msg = f"图像文件不存在: {image_path}"
                logger.error(error_msg)
                return {"success": False, "error": error_msg}
                
            # 使用PIL加载图像
            try:
                image = Image.open(image_path)
            except Exception as e:
                error_msg = f"无法打开图像文件: {e}"
                logger.error(error_msg)
                return {"success": False, "error": error_msg}
            
            # 进度报告
            if callback:
                callback(20, "开始生成视频...")
            
            # 执行推理 (ModelScope的image-to-video-synthesis)
            output_video_path = os.path.join(self.temp_dir, f"{uuid.uuid4()}.{output_format}")
            
            # 数据准备
            from modelscope.outputs import OutputKeys
            input_params = {
                'image': image,
                'text': prompt,
                'negative_prompt': negative_prompt,
                'num_frames': num_frames,
                'num_inference_steps': num_inference_steps,
                'guidance_scale': guidance_scale,
                'output_video': output_video_path
            }
            
            # 执行推理
            result = self.models['image2video'](input_params)
            
            # 确保有帧被生成
            if callback:
                callback(80, "视频帧生成完成，处理中...")
            
            # 生成缩略图
            thumbnail_path = self._generate_thumbnail(output_video_path)
            
            # 创建结果
            result = {
                "success": True,
                "video_path": output_video_path,
                "thumbnail_path": thumbnail_path,
                "metadata": {
                    "duration": num_frames / fps,
                    "fps": fps,
                    "resolution": f"{image.width}x{image.height}",
                    "frames": num_frames
                }
            }
            
            if callback:
                callback(100, "视频生成完成")
                
            return result
            
        except Exception as e:
            logger.error(f"图像到视频生成失败: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return {"success": False, "error": str(e), "stack_trace": traceback.format_exc()}
            
    def _generate_thumbnail(self, video_path: str) -> str:
        """
        从视频生成缩略图
        
        Args:
            video_path: 视频文件路径
            
        Returns:
            str: 缩略图路径
        """
        try:
            thumbnail_path = f"{os.path.splitext(video_path)[0]}_thumbnail.jpg"
            
            # 使用OpenCV读取视频并提取帧
            cap = cv2.VideoCapture(video_path)
            
            # 读取第一帧
            success, frame = cap.read()
            if success:
                # 调整尺寸
                width = 320
                height = int(frame.shape[0] * width / frame.shape[1])
                frame = cv2.resize(frame, (width, height))
                
                # 保存缩略图
                cv2.imwrite(thumbnail_path, frame)
                
            cap.release()
            
            return thumbnail_path
            
        except Exception as e:
            logger.error(f"生成缩略图失败: {e}")
            return ""
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        获取模型信息
        
        Returns:
            dict: 模型信息
        """
        return {
            "name": "WAN 2.1",
            "type": "Video Generation Model",
            "device": self.device,
            "initialized": self.is_initialized,
            "supports": ["text-to-video", "image-to-video"],
            "default_settings": {
                "num_frames": 16,
                "fps": 8,
                "guidance_scale": 7.5,
                "num_inference_steps": 50
            }
        }


# 单例模式，确保只有一个服务实例
_video_generation_service = None

def get_video_generation_service() -> VideoGenerationService:
    """获取视频生成服务实例"""
    global _video_generation_service
    if _video_generation_service is None:
        _video_generation_service = VideoGenerationService()
    return _video_generation_service 