# 舆情分析平台使用指南

## 🎯 平台概述

这是一个基于AI技术的企业级舆情分析平台，提供智能简报生成、实时数据爬取、WebSocket实时推送等功能。

## 🚀 快速开始

### 1. 启动服务

```bash
# 启动后端服务
cd backend
python main.py

# 启动前端服务
cd frontend
npm run dev
```

### 2. 访问平台

- **主页**: http://localhost:3000/utilities/daily/opinion-analysis
- **舆情检索**: http://localhost:3000/utilities/daily/opinion-analysis/opinion-search
- **定期简报**: http://localhost:3000/utilities/daily/opinion-analysis/periodic-report
- **知识图谱**: http://localhost:3000/utilities/daily/opinion-analysis/knowledge-graph
- **追踪预警**: http://localhost:3000/utilities/daily/opinion-analysis/search-tracking

## 🔧 AI服务配置

### 配置API密钥

1. 复制环境变量文件：
```bash
cd backend
cp .env.example .env
```

2. 编辑 `.env` 文件，填入真实的API密钥：
```env
# OpenAI配置
OPENAI_API_KEY=sk-your-openai-key
OPENAI_BASE_URL=https://api.openai.com/v1
DEFAULT_AI_MODEL=gpt-3.5-turbo

# Anthropic Claude配置
ANTHROPIC_API_KEY=your-anthropic-key

# 数据源配置
NEWS_API_KEY=your-news-api-key
REDDIT_CLIENT_ID=your-reddit-client-id
REDDIT_CLIENT_SECRET=your-reddit-client-secret
```

### 支持的AI模型

- **OpenAI**: GPT-3.5-turbo, GPT-4
- **Anthropic**: Claude-3-Sonnet
- **功能**: 智能简报生成、情感分析、热点提取

## 📊 核心功能

### 1. 智能简报生成

- **AI驱动**: 使用GPT/Claude生成专业简报
- **定时任务**: 支持每日/每周/每月自动生成
- **模板定制**: 标准/详细/简要三种模板
- **邮件推送**: 自动推送到指定邮箱

### 2. 舆情检索分析

- **多源数据**: 新闻、社交媒体、RSS源
- **实时爬取**: 自动获取最新舆情数据
- **AI分析**: 情感分析、热点提取
- **可视化**: 六大维度分析图表

### 3. 实时通信系统

- **WebSocket**: 双向实时通信
- **实时推送**: 搜索进度、预警通知
- **频道订阅**: 个性化消息推送
- **连接管理**: 多用户并发支持

### 4. 性能优化

- **虚拟滚动**: 支持万级数据展示
- **懒加载**: 按需加载数据
- **缓存机制**: 提升响应速度
- **异步处理**: 后台任务优化

## 🌐 数据源配置

### 新闻数据源

- **NewsAPI**: 全球新闻数据
- **RSS源**: 新华社、人民日报、BBC、CNN等
- **网页爬取**: 自定义网站抓取

### 社交媒体

- **Reddit**: 社区讨论数据
- **微博**: 中文社交媒体（模拟）
- **Twitter**: 推特数据（需配置）

## 🔍 使用示例

### 创建智能简报

1. 访问定期简报页面
2. 点击"创建新简报"
3. 设置简报名称和关键词
4. 选择生成周期和模板
5. 配置邮件推送
6. 保存并启动任务

### 舆情检索分析

1. 访问舆情检索页面
2. 输入关键词
3. 选择数据源和时间范围
4. 点击搜索
5. 查看实时搜索进度
6. 分析可视化结果

### 设置预警监控

1. 访问追踪预警页面
2. 创建预警配置
3. 设置触发条件
4. 配置通知方式
5. 启动监控任务

## 🧪 测试功能

### WebSocket测试

访问: http://localhost:3000/utilities/websocket-test

- 测试实时连接
- 发送心跳包
- 订阅预警频道
- 查看连接统计

### 图标测试

访问: http://localhost:3000/utilities/icon-test

- 验证Element Plus图标
- 检查图标显示

## 📈 API文档

访问: http://localhost:8000/docs

- 完整的API文档
- 交互式测试界面
- 请求/响应示例

## 🛠️ 技术栈

### 后端
- **框架**: FastAPI + Python
- **AI服务**: OpenAI + Anthropic
- **数据爬取**: NewsAPI + RSS + Reddit
- **实时通信**: WebSocket
- **数据库**: PostgreSQL + SQLAlchemy

### 前端
- **框架**: Vue 3 + Element Plus
- **图表**: ECharts + AntV G6
- **实时通信**: WebSocket客户端
- **性能优化**: 虚拟滚动 + 懒加载

## 🔧 故障排除

### 常见问题

1. **图标显示错误**
   - 检查Element Plus版本
   - 确认图标名称正确

2. **WebSocket连接失败**
   - 检查后端服务状态
   - 确认端口8000可访问

3. **AI服务调用失败**
   - 检查API密钥配置
   - 确认网络连接正常

4. **数据爬取失败**
   - 检查数据源API配置
   - 确认网络访问权限

### 日志查看

```bash
# 后端日志
cd backend
python main.py

# 前端开发服务器日志
cd frontend
npm run dev
```

## 📞 技术支持

如遇到问题，请检查：
1. 环境变量配置
2. 依赖包安装
3. 服务启动状态
4. 网络连接情况

## 🎉 功能特色

- ✅ **真实AI集成**: 不再是模拟数据
- ✅ **多源数据整合**: 新闻+社交媒体+RSS
- ✅ **实时双向通信**: WebSocket实时交互
- ✅ **高性能渲染**: 虚拟滚动万级数据
- ✅ **智能化分析**: AI驱动的深度分析
- ✅ **专业级简报**: 高质量舆情报告

这个平台现在已经具备了企业级的完整功能，可以作为专业的舆情监测和分析工具投入使用！
