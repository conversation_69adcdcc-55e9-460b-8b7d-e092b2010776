from typing import Dict, Any

class HumanGenerator:
    async def generate(self, task_id: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """生成数字人"""
        try:
            # 初始化任务状态
            await self.task_state_manager.initialize_task(task_id, params)
            
            # 创建任务队列
            task_queue = TaskQueue()
            
            # 添加任务到队列
            task_queue.add_task({
                'task_id': task_id,
                'params': params,
                'priority': 1,
                'status': 'pending'
            })
            
            # 创建检查点管理器
            checkpoint_manager = CheckpointManager()
            
            # 创建性能监控器
            performance_monitor = PerformanceMonitor()
            
            # 开始监控
            performance_monitor.start_monitoring()
            
            # 获取任务
            task = task_queue.get_next_task()
            if not task:
                raise Exception("No task available")
            
            # 更新任务状态
            task_queue.update_task_status(task['task_id'], 'processing')
            
            # 创建检查点
            checkpoint_id = await checkpoint_manager.create_checkpoint(task_id)
            
            # 生成数字人
            result = await self._generate_human(task_id, params)
            
            # 更新检查点
            await checkpoint_manager.update_checkpoint(checkpoint_id, {
                'status': 'completed',
                'result': result
            })
            
            # 更新任务状态
            task_queue.update_task_status(task['task_id'], 'completed')
            
            # 停止监控
            performance_monitor.stop_monitoring()
            
            # 获取性能报告
            performance_report = performance_monitor.get_performance_report()
            
            # 更新任务状态
            await self.task_state_manager.update_task_state(task_id, {
                'status': 'completed',
                'result': result,
                'performance': performance_report
            })
            
            return result
            
        except Exception as e:
            # 记录错误
            logger.error(f"Error generating human: {str(e)}")
            
            # 更新任务状态
            await self.task_state_manager.update_task_state(task_id, {
                'status': 'failed',
                'error': str(e)
            })
            
            # 停止监控
            performance_monitor.stop_monitoring()
            
            # 获取性能报告
            performance_report = performance_monitor.get_performance_report()
            
            # 记录性能报告
            logger.info(f"Performance report: {performance_report}")
            
            raise 