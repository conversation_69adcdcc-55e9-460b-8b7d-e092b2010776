from sqlalchemy import Column, Integer, String, Text, ForeignKey, DateTime, Boolean, JSON, Float
from sqlalchemy.sql import func
from pydantic import BaseModel, ConfigDict
from typing import Optional, List, Dict, Any
from datetime import datetime

from utils.db import Base

class Translation(Base):
    """Translation database model"""
    __tablename__ = "translations"

    id = Column(Integer, primary_key=True, index=True)
    translation_id = Column(String, unique=True, index=True)
    job_id = Column(String, unique=True, index=True, nullable=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    source_language = Column(String)
    target_language = Column(String)
    source_text = Column(Text, nullable=True)
    translated_text = Column(Text, nullable=True)
    source_audio_url = Column(String, nullable=True)
    translated_audio_url = Column(String, nullable=True)
    status = Column(String)  # completed, in_progress, failed
    progress = Column(Float, default=0.0)
    error_message = Column(Text, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    completed_at = Column(DateTime(timezone=True), nullable=True)
    detected_language = Column(String, nullable=True)
    meta_info = Column(JSON, nullable=True)

# Pydantic models for API
class TranslationBase(BaseModel):
    source_language: Optional[str] = None
    target_language: Optional[str] = None
    source_text: Optional[str] = None

class TranslationCreate(TranslationBase):
    pass

class TranslationResponse(BaseModel):
    id: Optional[int] = None
    translation_id: str
    job_id: Optional[str] = None
    user_id: Optional[int] = None
    source_language: Optional[str] = None
    target_language: Optional[str] = None
    source_text: Optional[str] = None
    translated_text: Optional[str] = None
    source_audio_url: Optional[str] = None
    translated_audio_url: Optional[str] = None
    status: str
    progress: Optional[float] = 0.0
    error_message: Optional[str] = None
    created_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    content: Optional[List[str]] = None
    detected_language: Optional[str] = None
    meta_info: Optional[Dict[str, Any]] = None

    model_config = ConfigDict(
        from_attributes=True, 
        arbitrary_types_allowed=True,
        populate_by_name=True
    ) 