"""
舆情分析API路由
"""

from fastapi import APIRouter, HTTPException, Depends, Query, BackgroundTasks
from typing import List, Optional, Dict, Any
from pydantic import BaseModel
from datetime import datetime, timedelta
import json
from ...core.database import get_db_manager
from ...services.opinion_analysis_service import OpinionAnalysisService
from ...services.ollama_service import ollama_service

router = APIRouter()
opinion_service = OpinionAnalysisService()


# Pydantic模型定义
class OpinionReportCreate(BaseModel):
    """创建简报任务请求模型"""
    name: str
    description: Optional[str] = None
    template_type: str = "standard"
    cycle: str = "weekly"
    sources: List[str] = ["news", "social"]
    keywords: List[str] = []
    regions: List[str] = ["china"]
    language: str = "zh"
    execution_time: str = "09:00"
    recipients: List[str] = []
    email_enabled: bool = True


class OpinionReportUpdate(BaseModel):
    """更新简报任务请求模型"""
    name: Optional[str] = None
    description: Optional[str] = None
    template_type: Optional[str] = None
    cycle: Optional[str] = None
    sources: Optional[List[str]] = None
    keywords: Optional[List[str]] = None
    regions: Optional[List[str]] = None
    language: Optional[str] = None
    execution_time: Optional[str] = None
    recipients: Optional[List[str]] = None
    email_enabled: Optional[bool] = None
    status: Optional[str] = None


class OpinionSearchSchemeCreate(BaseModel):
    """创建检索方案请求模型"""
    name: str
    description: Optional[str] = None
    keywords: List[str] = []
    sources: List[str] = ["news", "social"]
    regions: List[str] = ["china"]
    language: str = "all"
    date_range_type: str = "relative"
    date_range_value: Dict[str, Any] = {"days": 30}
    exclude_keywords: Optional[List[str]] = None


class OpinionAlertCreate(BaseModel):
    """创建预警配置请求模型"""
    search_scheme_id: int
    enabled: bool = True
    trigger_type: str = "time"
    frequency: Optional[str] = "daily"
    check_time: Optional[str] = "09:00"
    threshold_value: Optional[int] = None
    threshold_operator: Optional[str] = ">"
    change_rate: Optional[float] = None
    change_period: Optional[str] = "day"
    alert_level: str = "medium"
    recipients: List[str] = []
    notification_methods: List[str] = ["email"]


class OpinionSearchRequest(BaseModel):
    """舆情检索请求模型"""
    query: str
    sources: List[str] = ["news", "social"]
    regions: List[str] = ["china"]
    language: str = "all"
    date_range: Dict[str, str] = {}
    sort_by: str = "relevance"
    page: int = 1
    page_size: int = 20


# 简报管理API
@router.get("/reports")
async def get_reports(
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    status: Optional[str] = None,
    user_id: int = 1  # TODO: 从认证中获取
):
    """获取简报任务列表"""
    try:
        result = await opinion_service.get_reports(
            user_id=user_id,
            page=page,
            page_size=page_size,
            status=status
        )
        return {
            "success": True,
            "data": result["items"],
            "total": result["total"],
            "page": page,
            "page_size": page_size
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/reports")
async def create_report(
    report_data: OpinionReportCreate,
    user_id: int = 1  # TODO: 从认证中获取
):
    """创建简报任务"""
    try:
        report = await opinion_service.create_report(
            user_id=user_id,
            **report_data.dict()
        )
        return {
            "success": True,
            "data": report,
            "message": "简报任务创建成功"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/reports/{report_id}")
async def get_report(report_id: int, user_id: int = 1):
    """获取简报任务详情"""
    try:
        report = await opinion_service.get_report(report_id, user_id)
        if not report:
            raise HTTPException(status_code=404, detail="简报任务不存在")
        return {"success": True, "data": report}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/reports/{report_id}")
async def update_report(
    report_id: int,
    report_data: OpinionReportUpdate,
    user_id: int = 1
):
    """更新简报任务"""
    try:
        report = await opinion_service.update_report(
            report_id=report_id,
            user_id=user_id,
            **report_data.dict(exclude_unset=True)
        )
        return {
            "success": True,
            "data": report,
            "message": "简报任务更新成功"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/reports/{report_id}")
async def delete_report(report_id: int, user_id: int = 1):
    """删除简报任务"""
    try:
        await opinion_service.delete_report(report_id, user_id)
        return {"success": True, "message": "简报任务删除成功"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/reports/{report_id}/generate")
async def generate_report(
    report_id: int,
    background_tasks: BackgroundTasks,
    user_id: int = 1
):
    """手动生成简报"""
    try:
        # 添加后台任务生成简报
        background_tasks.add_task(
            opinion_service.generate_report_instance,
            report_id,
            user_id
        )
        return {
            "success": True,
            "message": "简报生成任务已启动，请稍后查看结果"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/reports/{report_id}/instances")
async def get_report_instances(
    report_id: int,
    page: int = Query(1, ge=1),
    page_size: int = Query(10, ge=1, le=50),
    user_id: int = 1
):
    """获取简报实例列表"""
    try:
        result = await opinion_service.get_report_instances(
            report_id=report_id,
            user_id=user_id,
            page=page,
            page_size=page_size
        )
        return {
            "success": True,
            "data": result["items"],
            "total": result["total"]
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# 检索方案管理API
@router.get("/search-schemes")
async def get_search_schemes(
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    user_id: int = 1
):
    """获取检索方案列表"""
    try:
        result = await opinion_service.get_search_schemes(
            user_id=user_id,
            page=page,
            page_size=page_size
        )
        return {
            "success": True,
            "data": result["items"],
            "total": result["total"]
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/search-schemes")
async def create_search_scheme(
    scheme_data: OpinionSearchSchemeCreate,
    user_id: int = 1
):
    """创建检索方案"""
    try:
        scheme = await opinion_service.create_search_scheme(
            user_id=user_id,
            **scheme_data.dict()
        )
        return {
            "success": True,
            "data": scheme,
            "message": "检索方案创建成功"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/search-schemes/{scheme_id}")
async def delete_search_scheme(scheme_id: int, user_id: int = 1):
    """删除检索方案"""
    try:
        await opinion_service.delete_search_scheme(scheme_id, user_id)
        return {"success": True, "message": "检索方案删除成功"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# 预警管理API
@router.post("/alerts")
async def create_alert(alert_data: OpinionAlertCreate, user_id: int = 1):
    """创建预警配置"""
    try:
        alert = await opinion_service.create_alert(
            user_id=user_id,
            **alert_data.dict()
        )
        return {
            "success": True,
            "data": alert,
            "message": "预警配置创建成功"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/alerts/recent")
async def get_recent_alerts(
    limit: int = Query(10, ge=1, le=50),
    user_id: int = 1
):
    """获取最近预警记录"""
    try:
        alerts = await opinion_service.get_recent_alerts(user_id, limit)
        return {"success": True, "data": alerts}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# 舆情检索API
@router.post("/search")
async def search_opinions(search_request: OpinionSearchRequest, user_id: int = 1):
    """执行舆情检索"""
    try:
        result = await opinion_service.search_opinions(
            user_id=user_id,
            **search_request.dict()
        )
        return {"success": True, "data": result}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/search/analysis")
async def get_search_analysis(
    query: str,
    sources: List[str] = Query(["news", "social"]),
    date_range: str = Query("30d"),
    user_id: int = 1
):
    """获取检索结果分析"""
    try:
        analysis = await opinion_service.get_search_analysis(
            query=query,
            sources=sources,
            date_range=date_range,
            user_id=user_id
        )
        return {"success": True, "data": analysis}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# 知识图谱API
@router.post("/knowledge-graph")
async def create_knowledge_graph(
    name: str,
    data_source: str = "recent-reports",
    keywords: List[str] = Query([]),
    time_range: str = Query("30d"),
    user_id: int = 1
):
    """创建知识图谱"""
    try:
        graph = await opinion_service.create_knowledge_graph(
            user_id=user_id,
            name=name,
            data_source=data_source,
            keywords=keywords,
            time_range=time_range
        )
        return {
            "success": True,
            "data": graph,
            "message": "知识图谱创建成功"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/knowledge-graphs")
async def get_knowledge_graphs(user_id: int = 1):
    """获取知识图谱列表"""
    try:
        graphs = await opinion_service.get_knowledge_graphs(user_id)
        return {"success": True, "data": graphs}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# 系统状态API
@router.get("/system/status")
async def get_system_status():
    """获取系统状态"""
    try:
        # 检查Ollama服务状态
        ollama_status = {
            "available": ollama_service.available,
            "base_url": ollama_service.base_url,
            "best_model": ollama_service.best_model if ollama_service.available else None,
            "models": ollama_service.list_models() if ollama_service.available else []
        }

        # 系统整体状态
        system_status = {
            "database": "connected",  # 假设数据库已连接
            "redis": "connected",     # 假设Redis已连接
            "ollama": ollama_status,
            "timestamp": datetime.now().isoformat()
        }

        return {"success": True, "data": system_status}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# 统计API
@router.get("/statistics")
async def get_statistics(user_id: int = 1):
    """获取统计信息"""
    try:
        stats = await opinion_service.get_statistics(user_id)
        return {"success": True, "data": stats}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
