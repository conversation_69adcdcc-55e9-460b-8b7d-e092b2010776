// utilities 模块统一导出

// 懒加载组件
const DocumentConversion = () => import('./conversion/views/DocumentConversion.vue');
const VideoConversion = () => import('./conversion/views/VideoConversion.vue');
const AudioConversion = () => import('./conversion/views/AudioConversion.vue');
const ImageConversion = () => import('./conversion/views/ImageConversion.vue');

const Transcription = () => import('./office/views/Transcription.vue');
const TextToAudio = () => import('./office/views/TextToAudio.vue');
const Reports = () => import('./office/views/Reports.vue');
const SmartPPT = () => import('./office/views/SmartPPT.vue');

const Resume = () => import('./daily/views/Resume.vue');
const PublicOpinionAnalysis = () => import('./daily/views/PublicOpinionAnalysis.vue');
const PeriodicReport = () => import('./daily/views/PeriodicReport.vue');
const KnowledgeGraph = () => import('./daily/views/KnowledgeGraph.vue');
const SearchTracking = () => import('./daily/views/SearchTracking.vue');
const OpinionSearch = () => import('./daily/views/OpinionSearch.vue');
const WebSocketTest = () => import('../../components/test/WebSocketTest.vue');
const IconTest = () => import('../../components/test/IconTest.vue');

// 工具模块路由
const utilitiesRoutes = [
  // 转换工具
  {
    path: 'utilities/conversion/document',
    name: 'DocumentConversion',
    component: DocumentConversion,
    meta: { title: '文档转换', module: 'utilities' }
  },
  {
    path: 'utilities/conversion/video',
    name: 'VideoConversion',
    component: VideoConversion,
    meta: { title: '视频转换', module: 'utilities' }
  },
  {
    path: 'utilities/conversion/audio',
    name: 'AudioConversion',
    component: AudioConversion,
    meta: { title: '音频转换', module: 'utilities' }
  },
  {
    path: 'utilities/conversion/image',
    name: 'ImageConversion',
    component: ImageConversion,
    meta: { title: '图片转换', module: 'utilities' }
  },
  
  // 办公工具
  {
    path: 'utilities/office/transcription',
    name: 'Transcription',
    component: Transcription,
    meta: { title: '语音转录', module: 'utilities' }
  },
  {
    path: 'utilities/office/text-to-audio',
    name: 'TextToAudio',
    component: TextToAudio,
    meta: { title: '文本转音频', module: 'utilities' }
  },
  {
    path: 'utilities/office/reports',
    name: 'Reports',
    component: Reports,
    meta: { title: '日报周报', module: 'utilities' }
  },
  {
    path: 'utilities/office/ppt',
    name: 'SmartPPT',
    component: SmartPPT,
    meta: { title: '智能PPT', module: 'utilities' }
  },
  
  // 便民工具
  {
    path: 'utilities/daily/resume',
    name: 'Resume',
    component: Resume,
    meta: { title: '简历制作', module: 'utilities' }
  },
  {
    path: 'utilities/daily/opinion-analysis',
    name: 'PublicOpinionAnalysis',
    component: PublicOpinionAnalysis,
    meta: { title: '舆情分析', module: 'utilities' }
  },
  {
    path: 'utilities/daily/opinion-analysis/periodic-report',
    name: 'PeriodicReport',
    component: PeriodicReport,
    meta: { title: '定期简报', module: 'utilities' }
  },
  {
    path: 'utilities/daily/opinion-analysis/knowledge-graph',
    name: 'KnowledgeGraph',
    component: KnowledgeGraph,
    meta: { title: '知识图谱', module: 'utilities' }
  },
  {
    path: 'utilities/daily/opinion-analysis/search-tracking',
    name: 'SearchTracking',
    component: SearchTracking,
    meta: { title: '检索追踪预警', module: 'utilities' }
  },
  {
    path: 'utilities/daily/opinion-analysis/opinion-search',
    name: 'OpinionSearch',
    component: OpinionSearch,
    meta: { title: '舆情检索', module: 'utilities' }
  },
  {
    path: 'utilities/websocket-test',
    name: 'WebSocketTest',
    component: WebSocketTest,
    meta: { title: 'WebSocket测试', module: 'utilities' }
  },
  {
    path: 'utilities/icon-test',
    name: 'IconTest',
    component: IconTest,
    meta: { title: '图标测试', module: 'utilities' }
  }
];

export default utilitiesRoutes;
