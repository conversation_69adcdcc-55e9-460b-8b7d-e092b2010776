from typing import Dict, Any, List
import time
import logging
import asyncio
from datetime import datetime, timedelta
from collections import deque

logger = logging.getLogger(__name__)

class PerformanceMetrics:
    """性能指标类"""
    def __init__(self):
        self.task_count = 0
        self.success_count = 0
        self.failure_count = 0
        self.total_processing_time = 0.0
        self.avg_processing_time = 0.0
        self.max_processing_time = 0.0
        self.min_processing_time = float('inf')
        self.queue_size_history = deque(maxlen=100)  # 保留最近100个队列大小记录
        self.processing_times = deque(maxlen=100)  # 保留最近100个处理时间记录
        
    def update(self, processing_time: float, success: bool):
        """更新性能指标"""
        self.task_count += 1
        if success:
            self.success_count += 1
        else:
            self.failure_count += 1
            
        self.total_processing_time += processing_time
        self.avg_processing_time = self.total_processing_time / self.task_count
        self.max_processing_time = max(self.max_processing_time, processing_time)
        self.min_processing_time = min(self.min_processing_time, processing_time)
        self.processing_times.append(processing_time)
        
    def update_queue_size(self, size: int):
        """更新队列大小历史"""
        self.queue_size_history.append((datetime.now(), size))
        
    def get_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        return {
            "task_count": self.task_count,
            "success_count": self.success_count,
            "failure_count": self.failure_count,
            "success_rate": self.success_count / self.task_count if self.task_count > 0 else 0,
            "avg_processing_time": self.avg_processing_time,
            "max_processing_time": self.max_processing_time,
            "min_processing_time": self.min_processing_time if self.min_processing_time != float('inf') else 0,
            "current_queue_size": self.queue_size_history[-1][1] if self.queue_size_history else 0,
            "recent_processing_times": list(self.processing_times),
            "queue_size_history": [(t.isoformat(), s) for t, s in self.queue_size_history]
        }

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, update_interval: int = 60):
        self.metrics = PerformanceMetrics()
        self.update_interval = update_interval
        self._monitoring = False
        self._stop_event = asyncio.Event()
        self._last_update = datetime.now()
        
    async def start(self):
        """启动性能监控"""
        self._monitoring = True
        self._stop_event.clear()
        asyncio.create_task(self._monitor_loop())
        logger.info("Performance monitor started")
        
    async def stop(self):
        """停止性能监控"""
        self._stop_event.set()
        self._monitoring = False
        logger.info("Performance monitor stopped")
        
    async def _monitor_loop(self):
        """监控循环"""
        while self._monitoring and not self._stop_event.is_set():
            try:
                # 记录当前性能指标
                metrics = self.metrics.get_metrics()
                logger.info(f"Performance metrics: {metrics}")
                
                # 检查性能异常
                await self._check_performance_anomalies(metrics)
                
                # 等待下一次更新
                await asyncio.sleep(self.update_interval)
                
            except Exception as e:
                logger.error(f"Error in performance monitoring: {str(e)}")
                logger.exception("Detailed error")
                
    async def _check_performance_anomalies(self, metrics: Dict[str, Any]):
        """检查性能异常"""
        # 检查处理时间异常
        if metrics["avg_processing_time"] > 300:  # 5分钟
            logger.warning("Average processing time exceeds 5 minutes")
            
        # 检查队列大小异常
        if metrics["current_queue_size"] > 100:
            logger.warning("Queue size exceeds 100 tasks")
            
        # 检查失败率异常
        if metrics["success_rate"] < 0.8:  # 80%
            logger.warning("Success rate below 80%")
            
    def record_task_completion(self, processing_time: float, success: bool):
        """记录任务完成"""
        self.metrics.update(processing_time, success)
        
    def update_queue_size(self, size: int):
        """更新队列大小"""
        self.metrics.update_queue_size(size)
        
    def get_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        return self.metrics.get_metrics()
        
    def get_performance_report(self, time_range: timedelta = timedelta(hours=1)) -> Dict[str, Any]:
        """获取性能报告"""
        now = datetime.now()
        start_time = now - time_range
        
        # 过滤时间范围内的数据
        recent_queue_sizes = [
            (t, s) for t, s in self.metrics.queue_size_history
            if start_time <= t <= now
        ]
        
        recent_processing_times = [
            t for t in self.metrics.processing_times
            if t > 0  # 忽略无效数据
        ]
        
        return {
            "time_range": {
                "start": start_time.isoformat(),
                "end": now.isoformat()
            },
            "task_metrics": {
                "total_tasks": self.metrics.task_count,
                "success_rate": self.metrics.success_count / self.metrics.task_count if self.metrics.task_count > 0 else 0,
                "avg_processing_time": self.metrics.avg_processing_time
            },
            "queue_metrics": {
                "current_size": self.metrics.queue_size_history[-1][1] if self.metrics.queue_size_history else 0,
                "max_size": max(s for _, s in recent_queue_sizes) if recent_queue_sizes else 0,
                "min_size": min(s for _, s in recent_queue_sizes) if recent_queue_sizes else 0
            },
            "processing_time_metrics": {
                "avg": sum(recent_processing_times) / len(recent_processing_times) if recent_processing_times else 0,
                "max": max(recent_processing_times) if recent_processing_times else 0,
                "min": min(recent_processing_times) if recent_processing_times else 0
            }
        } 