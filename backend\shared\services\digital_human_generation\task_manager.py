"""
数字人生成任务管理器

负责任务的创建、更新、获取和队列管理
"""

import asyncio
import os
import logging
import time
import json
from typing import Dict, Optional, List, Any, Callable, Awaitable, Union, Tuple
from datetime import datetime, timedelta
import threading
from pathlib import Path
import uuid
from concurrent.futures import ThreadPoolExecutor
import traceback

from .models import GenerationTask, TaskStatus, GenerationStage, LogLevel

# 设置日志
logger = logging.getLogger(__name__)


class TaskManager:
    """
    任务管理器
    
    负责任务的创建、更新、获取和队列管理，支持：
    1. 创建新任务
    2. 更新任务状态和进度
    3. 获取任务状态
    4. 取消任务
    5. 清理过期任务
    6. 任务持久化和恢复
    """
    
    def __init__(self, 
                 max_concurrent_tasks: int = 5,
                 task_timeout_seconds: int = 1800,
                 data_dir: str = "data/digital_humans",
                 clean_interval_seconds: int = 3600,
                 enable_persistence: bool = True):
        """
        初始化任务管理器
        
        Args:
            max_concurrent_tasks: 最大并发任务数
            task_timeout_seconds: 任务超时时间（秒）
            data_dir: 数据存储目录
            clean_interval_seconds: 清理过期任务的间隔时间
            enable_persistence: 是否启用持久化
        """
        self.max_concurrent_tasks = max_concurrent_tasks
        self.task_timeout_seconds = task_timeout_seconds
        self.data_dir = data_dir
        self.clean_interval_seconds = clean_interval_seconds
        self.enable_persistence = enable_persistence
        
        # 任务映射表：task_id -> task
        self.tasks: Dict[str, GenerationTask] = {}
        
        # 任务队列
        self.task_queue: List[str] = []
        
        # 正在处理的任务数
        self.active_task_count = 0
        
        # 同步锁
        self._lock = asyncio.Lock()
        self._file_lock = threading.Lock()  # 用于文件操作的线程锁
        
        # 状态变化回调
        self._status_callbacks: Dict[str, List[Callable[[GenerationTask], Awaitable[None]]]] = {}
        
        # 线程池
        self._executor = ThreadPoolExecutor(max_workers=2)
        
        # 任务存储目录
        self.tasks_dir = os.path.join(self.data_dir, "tasks")
        
        # 初始化
        self._initialize()
    
    def _initialize(self):
        """初始化任务管理器"""
        # 确保目录存在
        os.makedirs(self.data_dir, exist_ok=True)
        os.makedirs(self.tasks_dir, exist_ok=True)
        
        # 加载持久化的任务
        if self.enable_persistence:
            self._load_tasks()
    
    def _load_tasks(self):
        """从持久化存储加载任务"""
        try:
            # 查找所有任务文件
            task_files = list(Path(self.tasks_dir).glob("*.json"))
            
            if task_files:
                logger.info(f"找到 {len(task_files)} 个持久化任务文件")
                
                for task_file in task_files:
                    try:
                        with open(task_file, 'r', encoding='utf-8') as f:
                            task_data = json.load(f)
                            
                        # 创建任务对象
                        task = GenerationTask.from_dict(task_data)
                        
                        # 添加到任务映射表
                        self.tasks[task.task_id] = task
                        
                        # 检查任务状态，将未完成的任务添加到队列
                        if task.status in [TaskStatus.CREATED, TaskStatus.QUEUED, TaskStatus.WAITING]:
                            self.task_queue.append(task.task_id)
                            logger.info(f"已将未完成任务 {task.task_id} 添加到队列")
                        
                        # 更新正在处理的任务数
                        if task.status == TaskStatus.PROCESSING:
                            # 检查是否已超时
                            if task.update_time and datetime.now() - task.update_time > timedelta(seconds=self.task_timeout_seconds):
                                logger.warning(f"任务 {task.task_id} 已超时，标记为失败")
                                task.update_status(TaskStatus.TIMEOUT, "任务在服务重启期间超时")
                                self._persist_task(task)
                            else:
                                self.active_task_count += 1
                                logger.info(f"恢复处理中的任务 {task.task_id}，当前活跃任务数: {self.active_task_count}")
                                
                    except Exception as e:
                        logger.error(f"加载任务文件 {task_file} 失败: {e}", exc_info=True)
                
                logger.info(f"成功加载 {len(self.tasks)} 个任务, 队列中 {len(self.task_queue)} 个, 活跃 {self.active_task_count} 个")
            else:
                logger.info("没有找到持久化任务文件")
        except Exception as e:
            logger.error(f"加载持久化任务失败: {e}", exc_info=True)
    
    def _persist_task(self, task: GenerationTask):
        """
        将任务持久化到文件
        
        Args:
            task: 要持久化的任务
        """
        if not self.enable_persistence:
            return
        
        try:
            # 使用文件锁确保线程安全
            with self._file_lock:
                # 准备文件路径
                file_path = os.path.join(self.tasks_dir, f"{task.task_id}.json")
                
                # 将任务转换为JSON并写入文件
                task_json = task.to_json()
                
                # 使用临时文件，然后重命名，避免部分写入问题
                temp_file = file_path + ".tmp"
                with open(temp_file, 'w', encoding='utf-8') as f:
                    f.write(task_json)
                
                # 重命名为最终文件
                os.replace(temp_file, file_path)
                
        except Exception as e:
            logger.error(f"持久化任务 {task.task_id} 失败: {e}", exc_info=True)
            
    async def _clean_expired_tasks(self):
        """清理过期任务"""
        while True:
            try:
                await asyncio.sleep(self.clean_interval_seconds)
                
                logger.info("开始清理过期任务")
                current_time = datetime.now()
                expired_task_ids = []
                
                async with self._lock:
                    # 查找过期任务
                    for task_id, task in self.tasks.items():
                        # 已完成/失败/取消的任务超过24小时
                        if task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED, TaskStatus.TIMEOUT]:
                            if task.update_time and (current_time - task.update_time) > timedelta(hours=24):
                                expired_task_ids.append(task_id)
                        
                        # 处理中的任务超过超时时间
                        elif task.status == TaskStatus.PROCESSING:
                            if task.update_time and (current_time - task.update_time) > timedelta(seconds=self.task_timeout_seconds):
                                logger.warning(f"任务 {task_id} 已超时，标记为超时")
                                task.update_status(TaskStatus.TIMEOUT, "任务处理超时")
                                self._persist_task(task)
                        
                        # 队列中但很久没有更新的任务
                        elif task.update_time and (current_time - task.update_time) > timedelta(hours=48):
                            expired_task_ids.append(task_id)
                
                # 删除过期任务
                if expired_task_ids:
                    for task_id in expired_task_ids:
                        await self.remove_task(task_id)
                    
                    logger.info(f"清理了 {len(expired_task_ids)} 个过期任务")
            
            except Exception as e:
                logger.error(f"清理过期任务时出错: {e}", exc_info=True)
    
    def start_cleaner(self):
        """启动任务清理器，会定期检查并删除过期任务"""
        logger.info("任务清理器已启动")
        
        try:
            # 尝试获取当前事件循环
            import asyncio
            try:
                loop = asyncio.get_running_loop()
                # 有运行中的事件循环，创建清理任务
                asyncio.create_task(self._clean_expired_tasks())
            except RuntimeError:
                # 没有运行中的事件循环，记录警告
                logger.warning("没有运行中的事件循环，任务清理器将不会自动运行")
                # 存储清理协程，以便稍后在有事件循环时运行
                self._cleaner_coro = self._clean_expired_tasks()
        except Exception as e:
            logger.error(f"启动任务清理器时出错: {str(e)}")
        
        return True
    
    async def create_task(self,
                         digital_human_id: str,
                         media_file_path: str,
                         user_id: Optional[int] = None,
                         metadata: Optional[Dict[str, Any]] = None,
                         priority: bool = False,
                         initial_progress: float = 0) -> GenerationTask:
        """
        创建新的生成任务
        
        Args:
            digital_human_id: 数字人ID
            media_file_path: 媒体文件路径
            user_id: 用户ID
            metadata: 任务元数据
            priority: 是否优先级任务
            initial_progress: 初始进度
            
        Returns:
            GenerationTask: 创建的任务对象
        """
        # 生成任务ID
        task_id = str(uuid.uuid4())
        
        # 创建任务对象
        task = GenerationTask(
            task_id=task_id,
            digital_human_id=digital_human_id,
            user_id=user_id,
            media_file_path=media_file_path,
            status=TaskStatus.CREATED,
            progress=initial_progress,  # 使用初始进度
            metadata=metadata or {}
        )
        
        # 保存任务
        self.tasks[task_id] = task
        
        # 添加到队列
        if priority:
            self.task_queue.appendleft(task)
        else:
            self.task_queue.append(task)
        
        # 记录日志
        logger.info(f"创建任务: {task_id}, 数字人ID: {digital_human_id}, 初始进度: {initial_progress}%")
        
        return task
    
    async def get_task(self, task_id: str) -> Optional[GenerationTask]:
        """
        获取任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            Optional[GenerationTask]: 任务对象，如果不存在则返回None
        """
        async with self._lock:
            return self.tasks.get(task_id)
    
    async def update_task(self, task_id: str, **kwargs) -> Optional[GenerationTask]:
        """
        更新任务
        
        Args:
            task_id: 任务ID
            **kwargs: 要更新的字段
            
        Returns:
            Optional[GenerationTask]: 更新后的任务，如果不存在则返回None
        """
        async with self._lock:
            task = self.tasks.get(task_id)
            
            if not task:
                logger.warning(f"尝试更新不存在的任务: {task_id}")
                return None
            
            old_status = task.status
            
            # 更新状态
            if 'status' in kwargs:
                new_status = kwargs['status']
                progress = kwargs.get('progress')
                message = kwargs.get('message')
                
                task.update_status(new_status, message, progress)
                
                # 如果状态从PROCESSING变为终止状态，减少活跃任务计数
                if old_status == TaskStatus.PROCESSING and task.status in [
                    TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED, TaskStatus.TIMEOUT
                ]:
                    self.active_task_count = max(0, self.active_task_count - 1)
                    logger.info(f"任务 {task_id} 处理完成，当前活跃任务数: {self.active_task_count}")
                
                # 如果状态变为PROCESSING，增加活跃任务计数
                elif old_status != TaskStatus.PROCESSING and task.status == TaskStatus.PROCESSING:
                    self.active_task_count += 1
                    logger.info(f"任务 {task_id} 开始处理，当前活跃任务数: {self.active_task_count}")
            
            # 更新阶段
            if 'stage' in kwargs:
                stage = kwargs['stage']
                progress = kwargs.get('progress')
                message = kwargs.get('message')
                
                task.update_stage(stage, message, progress)
            
            # 更新进度（如果只更新进度）
            if 'progress' in kwargs and 'status' not in kwargs and 'stage' not in kwargs:
                task.progress = float(kwargs['progress'])
                task.update_time = datetime.now()
                
                # 添加日志
                if 'message' in kwargs:
                    task.add_log(kwargs['message'])
            
            # 更新结果URL
            if 'result_url' in kwargs:
                task.result_url = kwargs['result_url']
                task.update_time = datetime.now()
            
            # 更新音频URL
            if 'audio_url' in kwargs:
                task.audio_url = kwargs['audio_url']
                task.update_time = datetime.now()
                
            # 更新缩略图URL
            if 'thumbnail_url' in kwargs:
                task.thumbnail_url = kwargs['thumbnail_url']
                task.update_time = datetime.now()
            
            # 更新错误信息
            if 'error_message' in kwargs:
                task.error_message = kwargs['error_message']
                task.update_time = datetime.now()
                
                # 添加错误日志
                task.add_log(kwargs['error_message'], level=LogLevel.ERROR)
            
            # 更新元数据
            if 'metadata' in kwargs and kwargs['metadata']:
                if isinstance(kwargs['metadata'], dict):
                    task.metadata.update(kwargs['metadata'])
                    task.update_time = datetime.now()
            
            # 持久化
            self._persist_task(task)
            
            # 触发状态回调
            if old_status != task.status and task.status.value in self._status_callbacks:
                for callback in self._status_callbacks.get(task.status.value, []):
                    try:
                        await callback(task)
                    except Exception as e:
                        logger.error(f"执行状态回调时出错: {e}", exc_info=True)
            
            return task
    
    async def add_log(self, task_id: str, message: str, level: Union[LogLevel, str] = LogLevel.INFO,
                      stage: Optional[str] = None, details: Optional[Dict[str, Any]] = None) -> bool:
        """
        添加任务日志
        
        Args:
            task_id: 任务ID
            message: 日志消息
            level: 日志级别
            stage: 阶段
            details: 详细信息
            
        Returns:
            bool: 是否添加成功
        """
        async with self._lock:
            task = self.tasks.get(task_id)
            
            if not task:
                logger.warning(f"尝试为不存在的任务添加日志: {task_id}")
                return False
            
            # 添加日志
            task.add_log(message, level, stage, details)
            
            # 持久化
            self._persist_task(task)
            
            return True
    
    async def get_next_task(self) -> Optional[GenerationTask]:
        """
        获取下一个等待处理的任务
        
        Returns:
            Optional[GenerationTask]: 下一个任务，如果没有则返回None
        """
        async with self._lock:
            # 检查是否达到最大并发任务数
            if self.active_task_count >= self.max_concurrent_tasks:
                return None
            
            # 获取下一个任务
            while self.task_queue:
                task_id = self.task_queue[0]
                task = self.tasks.get(task_id)
                
                if not task:
                    # 队列中的任务不存在，移除
                    self.task_queue.pop(0)
                    logger.warning(f"队列中存在不存在的任务ID: {task_id}，已移除")
                    continue
                
                # 如果任务状态不是QUEUED或WAITING，移除
                if task.status not in [TaskStatus.QUEUED, TaskStatus.WAITING, TaskStatus.CREATED]:
                    self.task_queue.pop(0)
                    logger.warning(f"队列中的任务 {task_id} 状态为 {task.status.value}，已从队列移除")
                    continue
                
                # 获取到有效任务
                self.task_queue.pop(0)
                
                # 更新状态为处理中
                task.update_status(TaskStatus.PROCESSING, "开始处理任务")
                self.active_task_count += 1
                
                # 持久化
                self._persist_task(task)
                
                logger.info(f"获取下一个任务 {task_id}，当前活跃任务数: {self.active_task_count}")
                return task
            
            return None
    
    async def cancel_task(self, task_id: str, message: str = "任务已取消") -> bool:
        """
        取消任务
        
        Args:
            task_id: 任务ID
            message: 取消原因
            
        Returns:
            bool: 是否取消成功
        """
        async with self._lock:
            task = self.tasks.get(task_id)
            
            if not task:
                logger.warning(f"尝试取消不存在的任务: {task_id}")
                return False
            
            # 如果任务已完成或已取消，无法再取消
            if task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED, TaskStatus.TIMEOUT]:
                logger.warning(f"无法取消已结束的任务 {task_id}，当前状态: {task.status.value}")
                return False
            
            old_status = task.status
            
            # 更新状态为已取消
            task.update_status(TaskStatus.CANCELLED, message)
            
            # 从队列中移除
            if task_id in self.task_queue:
                self.task_queue.remove(task_id)
                logger.info(f"从队列中移除已取消的任务 {task_id}")
            
            # 如果之前是处理中状态，减少活跃任务计数
            if old_status == TaskStatus.PROCESSING:
                self.active_task_count = max(0, self.active_task_count - 1)
                logger.info(f"取消处理中的任务 {task_id}，当前活跃任务数: {self.active_task_count}")
            
            # 持久化
            self._persist_task(task)
            
            logger.info(f"成功取消任务 {task_id}")
            return True
    
    async def remove_task(self, task_id: str) -> bool:
        """
        移除任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 是否移除成功
        """
        async with self._lock:
            if task_id not in self.tasks:
                logger.warning(f"尝试移除不存在的任务: {task_id}")
                return False
            
            # 获取任务
            task = self.tasks[task_id]
            
            # 如果是处理中状态，减少活跃任务计数
            if task.status == TaskStatus.PROCESSING:
                self.active_task_count = max(0, self.active_task_count - 1)
                logger.info(f"移除处理中的任务 {task_id}，当前活跃任务数: {self.active_task_count}")
            
            # 从队列中移除
            if task_id in self.task_queue:
                self.task_queue.remove(task_id)
            
            # 从映射表中移除
            del self.tasks[task_id]
            
            # 删除持久化文件
            if self.enable_persistence:
                try:
                    file_path = os.path.join(self.tasks_dir, f"{task_id}.json")
                    if os.path.exists(file_path):
                        os.remove(file_path)
                except Exception as e:
                    logger.error(f"删除任务文件失败: {e}", exc_info=True)
            
            logger.info(f"成功移除任务 {task_id}")
            return True
    
    async def get_all_tasks(self, status_filter: Optional[List[Union[TaskStatus, str]]] = None, 
                         limit: int = 100, offset: int = 0, sort_by_date: bool = True,
                         user_id: Optional[int] = None) -> List[GenerationTask]:
        """
        获取所有任务
        
        Args:
            status_filter: 状态过滤
            limit: 限制数量
            offset: 偏移量
            sort_by_date: 是否按日期排序
            user_id: 用户ID过滤
            
        Returns:
            List[GenerationTask]: 任务列表
        """
        async with self._lock:
            # 转换状态过滤
            if status_filter:
                status_values = []
                for status in status_filter:
                    if isinstance(status, TaskStatus):
                        status_values.append(status.value)
                    else:
                        try:
                            status_values.append(TaskStatus(status).value)
                        except (ValueError, TypeError):
                            logger.warning(f"无效的任务状态过滤值: {status}")
            else:
                status_values = None
            
            # 过滤任务
            filtered_tasks = []
            for task in self.tasks.values():
                # 用户过滤
                if user_id is not None and task.user_id != user_id:
                    continue
                
                # 状态过滤
                if status_values and (task.status.value not in status_values):
                    continue
                
                filtered_tasks.append(task)
            
            # 排序
            if sort_by_date:
                filtered_tasks.sort(key=lambda t: t.update_time or t.create_time, reverse=True)
            
            # 分页
            return filtered_tasks[offset:offset+limit]
    
    async def get_task_count(self, status_filter: Optional[List[Union[TaskStatus, str]]] = None,
                           user_id: Optional[int] = None) -> int:
        """
        获取任务数量
        
        Args:
            status_filter: 状态过滤
            user_id: 用户ID过滤
            
        Returns:
            int: 任务数量
        """
        async with self._lock:
            # 转换状态过滤
            if status_filter:
                status_values = []
                for status in status_filter:
                    if isinstance(status, TaskStatus):
                        status_values.append(status.value)
                    else:
                        try:
                            status_values.append(TaskStatus(status).value)
                        except (ValueError, TypeError):
                            logger.warning(f"无效的任务状态过滤值: {status}")
            else:
                status_values = None
            
            # 计数
            count = 0
            for task in self.tasks.values():
                # 用户过滤
                if user_id is not None and task.user_id != user_id:
                    continue
                
                # 状态过滤
                if status_values and (task.status.value not in status_values):
                    continue
                
                count += 1
            
            return count
    
    async def register_status_callback(self, status: Union[TaskStatus, str], 
                                     callback: Callable[[GenerationTask], Awaitable[None]]) -> None:
        """
        注册状态变化回调
        
        Args:
            status: 任务状态
            callback: 回调函数
        """
        if isinstance(status, TaskStatus):
            status_value = status.value
        else:
            try:
                status_value = TaskStatus(status).value
            except (ValueError, TypeError):
                logger.error(f"注册回调失败: 无效的任务状态 {status}")
                return
        
        async with self._lock:
            if status_value not in self._status_callbacks:
                self._status_callbacks[status_value] = []
            
            self._status_callbacks[status_value].append(callback)
            logger.info(f"为状态 {status_value} 注册回调函数")
    
    async def sync_to_database(self, task_id: str) -> Dict[str, Any]:
        """
        将任务同步到数据库
        
        Args:
            task_id: 任务ID
            
        Returns:
            Dict[str, Any]: 同步结果
        """
        # 这里实现将任务同步到数据库的逻辑
        # 可以通过调用外部函数或服务实现
        # 例如：return await external_sync_function(task_id)
        
        # 暂时返回一个示例结果
        return {
            "success": True,
            "message": f"任务 {task_id} 同步成功",
            "updates": ["status", "progress"]
        }
    
    def get_queue_position(self, task_id: str) -> Optional[int]:
        """
        获取任务在队列中的位置
        
        Args:
            task_id: 任务ID
            
        Returns:
            Optional[int]: 队列位置（0为下一个处理的任务），如果不在队列中则返回None
        """
        if task_id in self.task_queue:
            return self.task_queue.index(task_id)
        return None
    
    async def get_system_status(self) -> Dict[str, Any]:
        """
        获取系统状态
        
        Returns:
            Dict[str, Any]: 系统状态信息
        """
        async with self._lock:
            return {
                "active_tasks": self.active_task_count,
                "max_concurrent_tasks": self.max_concurrent_tasks,
                "queue_length": len(self.task_queue),
                "total_tasks": len(self.tasks),
                "tasks_by_status": self._count_tasks_by_status(),
                "system_load": self.active_task_count / self.max_concurrent_tasks if self.max_concurrent_tasks > 0 else 0
            }
    
    def _count_tasks_by_status(self) -> Dict[str, int]:
        """
        按状态统计任务数量
        
        Returns:
            Dict[str, int]: 各状态的任务数量
        """
        status_counts = {status.value: 0 for status in TaskStatus}
        
        for task in self.tasks.values():
            status_value = task.status.value if isinstance(task.status, TaskStatus) else str(task.status)
            if status_value in status_counts:
                status_counts[status_value] += 1
            else:
                status_counts[status_value] = 1
        
        return status_counts 