<template>
  <div class="dashboard">
    <a-row :gutter="16">
      <a-col :span="6">
        <a-card title="总用户数" :bordered="false">
          <p style="font-size: 24px; color: #1890ff;">1,234</p>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card title="今日翻译" :bordered="false">
          <p style="font-size: 24px; color: #52c41a;">567</p>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card title="系统状态" :bordered="false">
          <a-tag color="green">正常运行</a-tag>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card title="在线用户" :bordered="false">
          <p style="font-size: 24px; color: #fa8c16;">89</p>
        </a-card>
      </a-col>
    </a-row>
    
    <a-card title="系统信息" style="margin-top: 20px;">
      <a-descriptions :column="2">
        <a-descriptions-item label="系统版本">1.0.0</a-descriptions-item>
        <a-descriptions-item label="运行时间">2小时30分钟</a-descriptions-item>
        <a-descriptions-item label="服务器状态">正常</a-descriptions-item>
        <a-descriptions-item label="数据库状态">正常</a-descriptions-item>
      </a-descriptions>
    </a-card>
    
    <a-card title="快速操作" style="margin-top: 20px;">
      <a-space>
        <a-button type="primary">用户管理</a-button>
        <a-button>系统设置</a-button>
        <a-button>日志查看</a-button>
        <a-button>数据备份</a-button>
      </a-space>
    </a-card>
  </div>
</template>

<script>
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'Dashboard'
})
</script>

<style scoped>
.dashboard {
  padding: 20px;
}
</style>
