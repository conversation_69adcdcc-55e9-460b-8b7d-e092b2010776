"""
统一任务管理 API - 简洁版本
"""
from fastapi import APIRouter, HTTPException
from typing import Optional, Dict, Any
from pydantic import BaseModel
from app.core.celery_unified import celery_app

router = APIRouter()

# 请求模型
class VideoTaskRequest(BaseModel):
    prompt: str
    model: str = "wanx-2.1"
    duration: int = 5
    resolution: str = "768x512"
    fps: int = 24
    user_id: str = "demo-user"

class TranslationTaskRequest(BaseModel):
    text: str
    source_lang: str = "auto"
    target_lang: str = "zh"
    user_id: str = "demo-user"

class ImageTaskRequest(BaseModel):
    prompt: str
    model: str = "stable-diffusion"
    resolution: str = "512x512"
    user_id: str = "demo-user"

class AudioTaskRequest(BaseModel):
    text: str
    voice: str = "default"
    user_id: str = "demo-user"

class DigitalHumanTaskRequest(BaseModel):
    text: str
    voice: str = "default"
    avatar: str = "default"
    user_id: str = "demo-user"

# 视频任务 API
@router.post("/video/text-to-video")
async def create_video_task(request: VideoTaskRequest):
    """创建文本转视频任务"""
    try:
        from app.tasks.video_tasks import text_to_video
        
        task = text_to_video.delay(
            prompt=request.prompt,
            model=request.model,
            duration=request.duration,
            resolution=request.resolution,
            fps=request.fps,
            user_id=request.user_id
        )
        
        return {
            "success": True,
            "task_id": task.id,
            "status": "started",
            "message": "视频生成任务已启动"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"启动任务失败: {str(e)}")

@router.post("/video/image-to-video")
async def create_image_to_video_task(image_path: str, user_id: str = "demo-user"):
    """创建图片转视频任务"""
    try:
        from app.tasks.video_tasks import image_to_video
        
        task = image_to_video.delay(
            image_path=image_path,
            user_id=user_id
        )
        
        return {
            "success": True,
            "task_id": task.id,
            "status": "started",
            "message": "图片转视频任务已启动"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"启动任务失败: {str(e)}")

# 翻译任务 API
@router.post("/translation/translate")
async def create_translation_task(request: TranslationTaskRequest):
    """创建翻译任务"""
    try:
        from app.tasks.translation_tasks_simple import translate_text
        
        task = translate_text.delay(
            text=request.text,
            source_lang=request.source_lang,
            target_lang=request.target_lang,
            user_id=request.user_id
        )
        
        return {
            "success": True,
            "task_id": task.id,
            "status": "started",
            "message": "翻译任务已启动"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"启动任务失败: {str(e)}")

# 图像任务 API
@router.post("/image/text-to-image")
async def create_image_task(request: ImageTaskRequest):
    """创建文本生成图像任务"""
    try:
        from app.tasks.image_tasks import text_to_image
        
        task = text_to_image.delay(
            prompt=request.prompt,
            model=request.model,
            resolution=request.resolution,
            user_id=request.user_id
        )
        
        return {
            "success": True,
            "task_id": task.id,
            "status": "started",
            "message": "图像生成任务已启动"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"启动任务失败: {str(e)}")

# 音频任务 API
@router.post("/audio/text-to-speech")
async def create_tts_task(request: AudioTaskRequest):
    """创建文本转语音任务"""
    try:
        from app.tasks.audio_tasks import text_to_speech
        
        task = text_to_speech.delay(
            text=request.text,
            voice=request.voice,
            user_id=request.user_id
        )
        
        return {
            "success": True,
            "task_id": task.id,
            "status": "started",
            "message": "语音合成任务已启动"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"启动任务失败: {str(e)}")

# 数字人任务 API
@router.post("/digital-human/create")
async def create_digital_human_task(request: DigitalHumanTaskRequest):
    """创建数字人视频任务"""
    try:
        from app.tasks.digital_human_tasks_simple import create_avatar
        
        task = create_avatar.delay(
            text=request.text,
            voice=request.voice,
            avatar=request.avatar,
            user_id=request.user_id
        )
        
        return {
            "success": True,
            "task_id": task.id,
            "status": "started",
            "message": "数字人视频生成任务已启动"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"启动任务失败: {str(e)}")

# 通用任务状态 API
@router.get("/task/{task_id}/status")
async def get_task_status(task_id: str):
    """获取任务状态"""
    try:
        # 从 Celery 获取任务状态
        task_result = celery_app.AsyncResult(task_id)
        
        # 从简单任务管理器获取详细信息
        task_info = simple_task_manager.get_task(task_id)
        
        status_info = {
            "task_id": task_id,
            "status": task_result.status,
            "progress": 0,
            "message": "",
            "result": None
        }
        
        if task_info:
            status_info.update({
                "progress": task_info.get("progress", 0),
                "message": task_info.get("message", ""),
                "task_type": task_info.get("task_type", ""),
                "created_at": task_info.get("created_at", "")
            })
        
        if task_result.ready():
            if task_result.successful():
                status_info["result"] = task_result.result
            else:
                status_info["error"] = str(task_result.info)
        elif task_result.status == 'PROGRESS':
            status_info.update(task_result.info)
        
        return {
            "success": True,
            "data": status_info
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取任务状态失败: {str(e)}")

@router.get("/tasks")
async def get_all_tasks(task_type: Optional[str] = None, user_id: Optional[str] = None):
    """获取所有任务"""
    try:
        if task_type:
            tasks = simple_task_manager.get_tasks_by_type(task_type)
        else:
            tasks = list(simple_task_manager.tasks.values())
        
        if user_id:
            tasks = [task for task in tasks if task.get('user_id') == user_id]
        
        return {
            "success": True,
            "total": len(tasks),
            "tasks": tasks
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取任务列表失败: {str(e)}")

@router.delete("/task/{task_id}")
async def cancel_task(task_id: str):
    """取消任务"""
    try:
        celery_app.control.revoke(task_id, terminate=True)
        
        # 更新任务状态
        simple_task_manager.update_task(
            task_id=task_id,
            status='cancelled',
            message='任务已取消'
        )
        
        return {
            "success": True,
            "message": "任务已取消"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"取消任务失败: {str(e)}")

# 测试 API
@router.post("/test/{module}")
async def test_module(module: str):
    """测试指定模块"""
    try:
        if module == "video":
            from app.tasks.video_tasks import test_video_task
            task = test_video_task.delay("Test Video Module")
        elif module == "translation":
            from app.tasks.translation_tasks_simple import test_translation_task
            task = test_translation_task.delay("Test Translation Module")
        elif module == "image":
            from app.tasks.image_tasks import test_image_task
            task = test_image_task.delay("Test Image Module")
        elif module == "audio":
            from app.tasks.audio_tasks import test_audio_task
            task = test_audio_task.delay("Test Audio Module")
        elif module == "digital_human":
            from app.tasks.digital_human_tasks_simple import test_digital_human_task
            task = test_digital_human_task.delay("Test Digital Human Module")
        else:
            raise HTTPException(status_code=400, detail="不支持的模块")
        
        return {
            "success": True,
            "task_id": task.id,
            "module": module,
            "message": f"{module} 模块测试任务已启动"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"测试失败: {str(e)}")
