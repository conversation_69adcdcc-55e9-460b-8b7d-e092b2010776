import os
import sys
import logging
from pathlib import Path
from gtts import gTTS

# 添加项目根目录到Python路径
current_file = os.path.abspath(__file__)
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_file)))
sys.path.append(project_root)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def generate_greeting_audios():
    """生成数字人问候语音频"""
    try:
        # 音频输出目录
        audio_dir = os.path.join(project_root, "frontend", "public", "assets", "audio")
        Path(audio_dir).mkdir(parents=True, exist_ok=True)
        
        # 数字人配置
        avatars = [
            {"id": "default", "name": "默认助手", "greeting": "你好，我是默认数字助手，很高兴为你服务。"},
            {"id": "business", "name": "商务助手", "greeting": "您好，我是您的商务助理，随时为您提供专业服务。"},
            {"id": "cartoon", "name": "卡通助手", "greeting": "嗨！我是卡通小助手，让我们一起开始有趣的对话吧！"},
            {"id": "realistic", "name": "写实助手", "greeting": "您好，我是您的智能助手，有什么可以帮助您的吗？"}
        ]
        
        # 为每个数字人生成问候语音频
        for avatar in avatars:
            # 设置输出路径
            output_path = os.path.join(audio_dir, f"greeting_{avatar['id']}.mp3")
            
            # 使用gTTS生成音频
            try:
                tts = gTTS(text=avatar["greeting"], lang='zh-CN')
                tts.save(output_path)
                logger.info(f"成功生成问候语音频: {output_path}")
            except Exception as e:
                logger.error(f"生成问候语音频失败 '{avatar['id']}': {e}")
                
                # 如果TTS失败，创建一个空的音频文件作为占位
                try:
                    with open(output_path, 'wb') as f:
                        # 写入最小的有效MP3文件头
                        f.write(b'\xFF\xFB\x90\x44\x00')
                    logger.warning(f"已创建空白占位音频: {output_path}")
                except Exception:
                    logger.error(f"创建占位音频失败: {output_path}")
    
    except Exception as e:
        logger.error(f"生成问候语音频过程中出错: {e}")

if __name__ == "__main__":
    logger.info("开始生成问候语音频...")
    generate_greeting_audios()
    logger.info("问候语音频生成完成!") 