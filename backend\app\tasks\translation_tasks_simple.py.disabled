"""
翻译任务 - 简洁版本
"""
import time
from datetime import datetime
from app.core.celery_unified import celery_app
from app.tasks.base import BaseTask, TaskProgressTracker, task_with_progress

@celery_app.task(bind=True, name='translation.translate_text')
def translate_text(self, text: str, source_lang: str = "auto", target_lang: str = "zh", **kwargs):
    """文本翻译任务"""
    task_id = self.request.id
    
    try:
        print(f"[翻译任务] 开始: {task_id}")
        print(f"[翻译任务] 文本: {text[:50]}...")
        
        # 创建任务记录
        simple_task_manager.create_task(
            task_id=task_id,
            task_type='translate_text',
            text=text,
            source_lang=source_lang,
            target_lang=target_lang,
            **kwargs
        )
        
        # 进度更新
        update_task_progress(task_id, 20, "检测语言...")
        time.sleep(0.5)
        
        # 简单的语言检测
        detected_lang = source_lang
        if source_lang == "auto":
            # 简单检测：包含中文字符就是中文，否则是英文
            if any('\u4e00' <= char <= '\u9fff' for char in text):
                detected_lang = "zh"
            else:
                detected_lang = "en"
        
        update_task_progress(task_id, 50, f"翻译中 ({detected_lang} -> {target_lang})...")
        time.sleep(1)
        
        # 模拟翻译
        if detected_lang == "en" and target_lang == "zh":
            # 英文转中文的简单映射
            translations = {
                "hello": "你好",
                "how are you": "你好吗",
                "thank you": "谢谢",
                "goodbye": "再见",
                "yes": "是的",
                "no": "不是"
            }
            translated_text = translations.get(text.lower(), f"[翻译] {text}")
        elif detected_lang == "zh" and target_lang == "en":
            # 中文转英文的简单映射
            translations = {
                "你好": "Hello",
                "谢谢": "Thank you",
                "再见": "Goodbye",
                "是的": "Yes",
                "不是": "No"
            }
            translated_text = translations.get(text, f"[Translated] {text}")
        else:
            # 其他情况，添加标记
            translated_text = f"[{detected_lang}->{target_lang}] {text}"
        
        update_task_progress(task_id, 90, "完成翻译...")
        time.sleep(0.5)
        
        result = {
            'task_id': task_id,
            'status': 'completed',
            'original_text': text,
            'translated_text': translated_text,
            'source_lang': detected_lang,
            'target_lang': target_lang,
            'translation_time': datetime.now().isoformat()
        }
        
        update_task_progress(task_id, 100, "翻译完成")
        print(f"[翻译任务] 完成: {translated_text}")
        
        return result
        
    except Exception as e:
        error_msg = f"翻译失败: {str(e)}"
        print(f"[翻译任务] 错误: {error_msg}")
        
        simple_task_manager.update_task(
            task_id=task_id,
            status='failed',
            error=error_msg
        )
        
        raise

@celery_app.task(bind=True, name='translation.batch_translate')
def batch_translate(self, texts: list, source_lang: str = "auto", target_lang: str = "zh"):
    """批量翻译任务"""
    task_id = self.request.id
    
    try:
        print(f"[批量翻译] 开始: {task_id}, 文本数量: {len(texts)}")
        
        simple_task_manager.create_task(
            task_id=task_id,
            task_type='batch_translate',
            text_count=len(texts),
            source_lang=source_lang,
            target_lang=target_lang
        )
        
        results = []
        total = len(texts)
        
        for i, text in enumerate(texts):
            progress = int((i + 1) / total * 90)
            update_task_progress(task_id, progress, f"翻译第 {i+1}/{total} 条...")
            
            # 调用单个翻译任务
            translate_result = translate_text.apply(args=[text, source_lang, target_lang])
            results.append(translate_result.get())
            
            time.sleep(0.2)  # 避免过快
        
        result = {
            'task_id': task_id,
            'status': 'completed',
            'total_count': total,
            'results': results,
            'completed_at': datetime.now().isoformat()
        }
        
        update_task_progress(task_id, 100, f"批量翻译完成 ({total} 条)")
        return result
        
    except Exception as e:
        error_msg = f"批量翻译失败: {str(e)}"
        simple_task_manager.update_task(task_id=task_id, status='failed', error=error_msg)
        raise

@celery_app.task(name='translation.test')
def test_translation_task(text: str = "Hello World"):
    """翻译模块测试任务"""
    print(f"[翻译测试] 文本: {text}")
    time.sleep(0.5)
    return {
        'text': text,
        'module': 'translation',
        'timestamp': datetime.now().isoformat()
    }
