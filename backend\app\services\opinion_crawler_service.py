"""
舆情数据爬虫服务
"""

import asyncio
import aiohttp
import feedparser
import json
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from bs4 import BeautifulSoup
import re
from urllib.parse import urljoin, urlparse
from newsapi import NewsApiClient
import praw
from ..core.config import settings
import logging

logger = logging.getLogger(__name__)


class OpinionCrawlerService:
    """舆情数据爬虫服务类"""
    
    def __init__(self):
        # 初始化各种API客户端
        self.news_api = None
        if settings.NEWS_API_KEY:
            self.news_api = NewsApiClient(api_key=settings.NEWS_API_KEY)
        
        self.reddit = None
        if settings.REDDIT_CLIENT_ID and settings.REDDIT_CLIENT_SECRET:
            self.reddit = praw.Reddit(
                client_id=settings.REDDIT_CLIENT_ID,
                client_secret=settings.REDDIT_CLIENT_SECRET,
                user_agent="OpinionCrawler/1.0"
            )
        
        # RSS源配置
        self.rss_sources = {
            'xinhua': 'http://www.xinhuanet.com/politics/news_politics.xml',
            'people': 'http://www.people.com.cn/rss/politics.xml',
            'cctv': 'http://news.cctv.com/2019/07/gaiban/cmsdatainterface/page/politics_1.jsonp',
            'bbc': 'http://feeds.bbci.co.uk/news/world/rss.xml',
            'cnn': 'http://rss.cnn.com/rss/edition.rss',
            'reuters': 'http://feeds.reuters.com/reuters/topNews'
        }
        
        # 社交媒体关键词
        self.social_keywords = [
            '政策', '经济', '国际', '社会', '科技', '环境',
            'policy', 'economy', 'international', 'society', 'technology'
        ]
    
    async def crawl_news_data(self, 
                             keywords: List[str], 
                             sources: List[str] = None,
                             language: str = 'zh',
                             days_back: int = 7,
                             max_results: int = 100) -> List[Dict[str, Any]]:
        """
        爬取新闻数据
        
        Args:
            keywords: 关键词列表
            sources: 数据源列表
            language: 语言
            days_back: 回溯天数
            max_results: 最大结果数
        
        Returns:
            新闻数据列表
        """
        all_results = []
        
        try:
            # 1. 使用NewsAPI爬取
            if self.news_api and 'news' in (sources or []):
                news_results = await self._crawl_newsapi(keywords, language, days_back, max_results // 3)
                all_results.extend(news_results)
            
            # 2. 爬取RSS源
            if 'rss' in (sources or []):
                rss_results = await self._crawl_rss_sources(keywords, max_results // 3)
                all_results.extend(rss_results)
            
            # 3. 爬取网页内容
            if 'web' in (sources or []):
                web_results = await self._crawl_web_sources(keywords, max_results // 3)
                all_results.extend(web_results)
            
            # 去重和排序
            all_results = self._deduplicate_results(all_results)
            all_results = sorted(all_results, key=lambda x: x.get('publish_time', ''), reverse=True)
            
            return all_results[:max_results]
            
        except Exception as e:
            logger.error(f"爬取新闻数据失败: {str(e)}")
            return []
    
    async def crawl_social_media_data(self,
                                    keywords: List[str],
                                    platforms: List[str] = None,
                                    max_results: int = 50) -> List[Dict[str, Any]]:
        """
        爬取社交媒体数据
        
        Args:
            keywords: 关键词列表
            platforms: 平台列表 ['reddit', 'twitter']
            max_results: 最大结果数
        
        Returns:
            社交媒体数据列表
        """
        all_results = []
        
        try:
            # 1. Reddit数据
            if self.reddit and 'reddit' in (platforms or []):
                reddit_results = await self._crawl_reddit(keywords, max_results // 2)
                all_results.extend(reddit_results)
            
            # 2. 微博数据（模拟）
            if 'weibo' in (platforms or []):
                weibo_results = await self._crawl_weibo_simulation(keywords, max_results // 2)
                all_results.extend(weibo_results)
            
            return all_results[:max_results]
            
        except Exception as e:
            logger.error(f"爬取社交媒体数据失败: {str(e)}")
            return []
    
    async def _crawl_newsapi(self, keywords: List[str], language: str, days_back: int, max_results: int) -> List[Dict]:
        """使用NewsAPI爬取新闻"""
        try:
            results = []
            query = ' OR '.join(keywords)
            
            # 计算日期范围
            from_date = (datetime.now() - timedelta(days=days_back)).strftime('%Y-%m-%d')
            
            # 调用NewsAPI
            articles = self.news_api.get_everything(
                q=query,
                language='zh' if language == 'zh' else 'en',
                from_param=from_date,
                sort_by='publishedAt',
                page_size=min(max_results, 100)
            )
            
            for article in articles.get('articles', []):
                results.append({
                    'title': article.get('title', ''),
                    'url': article.get('url', ''),
                    'source': article.get('source', {}).get('name', ''),
                    'author': article.get('author', ''),
                    'publish_time': article.get('publishedAt', ''),
                    'content': article.get('content', ''),
                    'summary': article.get('description', ''),
                    'language': language,
                    'source_type': 'news',
                    'crawl_time': datetime.now().isoformat()
                })
            
            return results
            
        except Exception as e:
            logger.error(f"NewsAPI爬取失败: {str(e)}")
            return []
    
    async def _crawl_rss_sources(self, keywords: List[str], max_results: int) -> List[Dict]:
        """爬取RSS源"""
        results = []
        
        try:
            async with aiohttp.ClientSession() as session:
                tasks = []
                for source_name, rss_url in self.rss_sources.items():
                    task = self._crawl_single_rss(session, source_name, rss_url, keywords)
                    tasks.append(task)
                
                rss_results = await asyncio.gather(*tasks, return_exceptions=True)
                
                for result in rss_results:
                    if isinstance(result, list):
                        results.extend(result)
            
            return results[:max_results]
            
        except Exception as e:
            logger.error(f"RSS爬取失败: {str(e)}")
            return []
    
    async def _crawl_single_rss(self, session: aiohttp.ClientSession, source_name: str, rss_url: str, keywords: List[str]) -> List[Dict]:
        """爬取单个RSS源"""
        try:
            async with session.get(rss_url, timeout=10) as response:
                if response.status == 200:
                    content = await response.text()
                    feed = feedparser.parse(content)
                    
                    results = []
                    for entry in feed.entries[:20]:  # 限制每个源的数量
                        title = entry.get('title', '')
                        summary = entry.get('summary', '')
                        
                        # 关键词匹配
                        if any(keyword.lower() in (title + summary).lower() for keyword in keywords):
                            results.append({
                                'title': title,
                                'url': entry.get('link', ''),
                                'source': source_name,
                                'publish_time': entry.get('published', ''),
                                'summary': summary,
                                'language': 'zh' if source_name in ['xinhua', 'people', 'cctv'] else 'en',
                                'source_type': 'rss',
                                'crawl_time': datetime.now().isoformat()
                            })
                    
                    return results
            
            return []
            
        except Exception as e:
            logger.error(f"爬取RSS源 {source_name} 失败: {str(e)}")
            return []
    
    async def _crawl_web_sources(self, keywords: List[str], max_results: int) -> List[Dict]:
        """爬取网页源"""
        results = []
        
        # 模拟网页爬取结果
        web_sources = [
            {'name': '环球网', 'url': 'https://www.huanqiu.com'},
            {'name': '观察者网', 'url': 'https://www.guancha.cn'},
            {'name': '澎湃新闻', 'url': 'https://www.thepaper.cn'}
        ]
        
        try:
            for i, keyword in enumerate(keywords[:max_results]):
                for j, source in enumerate(web_sources):
                    if len(results) >= max_results:
                        break
                    
                    results.append({
                        'title': f'{keyword}相关报道 - {source["name"]}',
                        'url': f'{source["url"]}/news/{i+1}',
                        'source': source['name'],
                        'publish_time': (datetime.now() - timedelta(hours=j+1)).isoformat(),
                        'summary': f'关于{keyword}的最新报道和分析...',
                        'language': 'zh',
                        'source_type': 'web',
                        'crawl_time': datetime.now().isoformat()
                    })
            
            return results
            
        except Exception as e:
            logger.error(f"网页爬取失败: {str(e)}")
            return []
    
    async def _crawl_reddit(self, keywords: List[str], max_results: int) -> List[Dict]:
        """爬取Reddit数据"""
        try:
            results = []
            
            for keyword in keywords:
                # 搜索相关subreddit
                subreddits = self.reddit.subreddits.search(keyword, limit=5)
                
                for subreddit in subreddits:
                    # 获取热门帖子
                    hot_posts = subreddit.hot(limit=10)
                    
                    for post in hot_posts:
                        if len(results) >= max_results:
                            break
                        
                        results.append({
                            'title': post.title,
                            'url': f'https://reddit.com{post.permalink}',
                            'source': f'Reddit - r/{subreddit.display_name}',
                            'author': str(post.author) if post.author else 'Unknown',
                            'publish_time': datetime.fromtimestamp(post.created_utc).isoformat(),
                            'content': post.selftext[:500] if post.selftext else '',
                            'summary': post.title,
                            'language': 'en',
                            'source_type': 'social',
                            'crawl_time': datetime.now().isoformat(),
                            'engagement': {
                                'upvotes': post.ups,
                                'comments': post.num_comments
                            }
                        })
            
            return results
            
        except Exception as e:
            logger.error(f"Reddit爬取失败: {str(e)}")
            return []
    
    async def _crawl_weibo_simulation(self, keywords: List[str], max_results: int) -> List[Dict]:
        """模拟微博数据爬取"""
        results = []
        
        try:
            for i, keyword in enumerate(keywords[:max_results]):
                results.append({
                    'title': f'#{keyword}# 相关微博讨论',
                    'url': f'https://weibo.com/search?q={keyword}',
                    'source': '微博',
                    'author': f'用户{i+1}',
                    'publish_time': (datetime.now() - timedelta(minutes=i*30)).isoformat(),
                    'content': f'关于{keyword}的热门讨论内容...',
                    'summary': f'{keyword}话题在微博上引起热议',
                    'language': 'zh',
                    'source_type': 'social',
                    'crawl_time': datetime.now().isoformat(),
                    'engagement': {
                        'likes': (i+1) * 100,
                        'reposts': (i+1) * 50,
                        'comments': (i+1) * 30
                    }
                })
            
            return results
            
        except Exception as e:
            logger.error(f"微博模拟爬取失败: {str(e)}")
            return []
    
    def _deduplicate_results(self, results: List[Dict]) -> List[Dict]:
        """去重处理"""
        seen_urls = set()
        seen_titles = set()
        unique_results = []
        
        for result in results:
            url = result.get('url', '')
            title = result.get('title', '')
            
            # 基于URL和标题去重
            if url not in seen_urls and title not in seen_titles:
                seen_urls.add(url)
                seen_titles.add(title)
                unique_results.append(result)
        
        return unique_results
    
    async def get_trending_topics(self, region: str = 'china', limit: int = 20) -> List[Dict[str, Any]]:
        """获取热门话题"""
        try:
            # 模拟热门话题数据
            trending_topics = [
                {'topic': '经济政策', 'heat': 95, 'change': '+15%'},
                {'topic': '国际关系', 'heat': 88, 'change': '+8%'},
                {'topic': '科技创新', 'heat': 82, 'change': '+12%'},
                {'topic': '环境保护', 'heat': 76, 'change': '+5%'},
                {'topic': '教育改革', 'heat': 71, 'change': '+3%'},
                {'topic': '医疗健康', 'heat': 68, 'change': '+7%'},
                {'topic': '社会治理', 'heat': 65, 'change': '+2%'},
                {'topic': '文化发展', 'heat': 62, 'change': '+4%'},
            ]
            
            return trending_topics[:limit]
            
        except Exception as e:
            logger.error(f"获取热门话题失败: {str(e)}")
            return []
