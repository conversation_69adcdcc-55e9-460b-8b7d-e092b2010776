
# NumPy兼容性补丁
import numpy as np
import warnings

# 忽略NumPy兼容性警告
warnings.filterwarnings("ignore", message=".*numpy.dtype size changed.*")
warnings.filterwarnings("ignore", message=".*numpy.ufunc size changed.*")

# 为旧版本代码提供兼容性
if not hasattr(np, 'complex'):
    np.complex = complex
if not hasattr(np, 'float'):
    np.float = float
if not hasattr(np, 'int'):
    np.int = int

print("✅ NumPy兼容性补丁已加载")
