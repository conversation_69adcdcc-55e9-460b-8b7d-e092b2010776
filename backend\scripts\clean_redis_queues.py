#!/usr/bin/env python3
"""
清理 Redis 队列中的错误消息
"""
import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# 添加后端路径
backend_dir = Path(__file__).parent.parent
sys.path.append(str(backend_dir))

# 加载环境变量
env_path = backend_dir / ".env"
load_dotenv(env_path)

def clean_redis_queues():
    """清理 Redis 队列"""
    print("🧹 清理 Redis 队列")
    print("=" * 50)
    
    try:
        import redis
        
        r = redis.Redis(host='localhost', port=6379, db=0)
        r.ping()
        print("✅ Redis 连接正常")
        
        # 清理所有队列
        queues = ['test', 'wanx_generation', 'celery', 'digital_human', 
                 'image_processing', 'voice_processing', 'video_generation', 'translation']
        
        print(f"\n🧹 清理队列:")
        for queue in queues:
            length_before = r.llen(queue)
            if length_before > 0:
                r.delete(queue)
                print(f"   {queue}: 删除了 {length_before} 个任务")
            else:
                print(f"   {queue}: 队列为空")
        
        # 清理任务元数据（可选）
        print(f"\n🧹 清理任务元数据:")
        celery_keys = [k.decode() for k in r.keys('*') if b'celery-task-meta' in k]
        if celery_keys:
            r.delete(*celery_keys)
            print(f"   删除了 {len(celery_keys)} 个任务元数据")
        else:
            print(f"   没有任务元数据需要清理")
        
        print(f"\n✅ Redis 队列清理完成")
        return True
        
    except Exception as e:
        print(f"❌ 清理 Redis 队列失败: {e}")
        return False

def main():
    """主函数"""
    print("🧹 Redis 队列清理工具")
    print("=" * 60)
    
    success = clean_redis_queues()
    
    if success:
        print(f"\n🎉 清理完成！")
        print(f"💡 现在可以重启 Celery Worker 并测试新的任务")
    else:
        print(f"\n❌ 清理失败")

if __name__ == "__main__":
    main()
