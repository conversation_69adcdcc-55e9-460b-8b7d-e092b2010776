#!/usr/bin/env python3
"""
添加缺失列脚本
此脚本用于向tasks表添加缺失的task_type和output_data列
"""
import os
import sys
import logging
import psycopg2
from psycopg2 import sql
from dotenv import load_dotenv

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def add_missing_columns():
    """向tasks表添加缺失的task_type和output_data列"""
    try:
        # 加载环境变量
        load_dotenv()
        
        # 获取数据库URL
        database_url = os.getenv("DATABASE_URL", "")
        
        # 如果环境变量中没有设置，使用默认连接信息
        if not database_url or not database_url.startswith("postgresql://"):
            logger.warning("未在环境变量中找到DATABASE_URL，使用默认连接信息")
            # 默认连接信息 - 根据实际情况修改这些值
            hostname = "localhost"  # 或实际的数据库主机名/IP
            port = 5432             # 默认PostgreSQL端口
            username = "postgres"   # 默认用户名
            password = "postgres"   # 默认密码
            dbname = "ai_platform"  # 数据库名称
        else:
            # 解析连接URL
            url_parts = database_url.replace("postgresql://", "").split("/")
            if len(url_parts) < 2:
                logger.error("数据库URL格式不正确")
                return False
                
            # 获取主机部分和数据库名
            auth_host = url_parts[0]
            dbname = url_parts[1].split("?")[0]
            
            # 分离用户名密码和主机
            if "@" in auth_host:
                auth, host = auth_host.split("@")
                if ":" in auth:
                    username, password = auth.split(":")
                else:
                    username = auth
                    password = ""
            else:
                host = auth_host
                username = "postgres"
                password = ""
                
            # 分离端口
            if ":" in host:
                hostname, port = host.split(":")
            else:
                hostname = host
                port = 5432
            
        logger.info(f"连接到数据库: {dbname} 在 {hostname}:{port}")
        
        # 连接到数据库
        conn = psycopg2.connect(
            host=hostname,
            port=int(port),
            user=username,
            password=password,
            dbname=dbname
        )
        conn.autocommit = True  # 自动提交是必须的
        
        success = True
        
        try:
            with conn.cursor() as cursor:
                # 检查tasks表是否存在
                cursor.execute("SELECT EXISTS(SELECT 1 FROM information_schema.tables WHERE table_name = 'tasks')")
                if not cursor.fetchone()[0]:
                    logger.error("tasks表不存在")
                    return False
                    
                # 1. 检查并添加task_type列
                cursor.execute("""
                    SELECT EXISTS(
                        SELECT 1 
                        FROM information_schema.columns 
                        WHERE table_name = 'tasks' AND column_name = 'task_type'
                    )
                """)
                if cursor.fetchone()[0]:
                    logger.info("task_type列已存在，无需添加")
                else:
                    # 添加task_type列，设置为NOT NULL并提供默认值
                    cursor.execute("ALTER TABLE tasks ADD COLUMN task_type VARCHAR(50) NOT NULL DEFAULT 'unknown'")
                    logger.info("成功添加task_type列到tasks表")
                    
                    # 尝试从现有数据更新task_type的值
                    try:
                        cursor.execute("""
                            UPDATE tasks
                            SET task_type = 
                                CASE 
                                    WHEN input_data->>'source_language' IS NOT NULL AND input_data->>'target_language' IS NOT NULL THEN 
                                        CASE 
                                            WHEN input_data->>'audio_url' IS NOT NULL THEN 'audio_translation'
                                            WHEN input_data->>'video_url' IS NOT NULL THEN 'video_translation'
                                            ELSE 'text_translation' 
                                        END
                                    WHEN input_data->>'text' IS NOT NULL AND input_data->>'voice_id' IS NOT NULL THEN 'text_to_speech'
                                    WHEN input_data->>'audio_url' IS NOT NULL AND input_data->>'transcript' IS NULL THEN 'speech_to_text'
                                    WHEN digital_human_id IS NOT NULL THEN 'digital_human'
                                    ELSE 'unknown'
                                END
                            WHERE task_type = 'unknown';
                        """)
                        logger.info(f"已更新 {cursor.rowcount} 行数据的task_type值")
                    except Exception as e:
                        logger.warning(f"更新task_type值时出错: {e}")
                
                # 2. 检查并添加output_data列
                cursor.execute("""
                    SELECT EXISTS(
                        SELECT 1 
                        FROM information_schema.columns 
                        WHERE table_name = 'tasks' AND column_name = 'output_data'
                    )
                """)
                if cursor.fetchone()[0]:
                    logger.info("output_data列已存在，无需添加")
                else:
                    # 添加output_data列，JSON类型，可为NULL
                    cursor.execute("ALTER TABLE tasks ADD COLUMN output_data JSONB")
                    logger.info("成功添加output_data列到tasks表")
                
                # 3. 验证所有列是否都添加成功
                cursor.execute("""
                    SELECT 
                        (SELECT EXISTS(SELECT 1 FROM information_schema.columns 
                         WHERE table_name = 'tasks' AND column_name = 'task_type')) as has_task_type,
                        (SELECT EXISTS(SELECT 1 FROM information_schema.columns 
                         WHERE table_name = 'tasks' AND column_name = 'output_data')) as has_output_data
                """)
                result = cursor.fetchone()
                
                if result[0] and result[1]:
                    logger.info("验证成功：所有列都已正确添加")
                else:
                    missing_columns = []
                    if not result[0]:
                        missing_columns.append("task_type")
                    if not result[1]:
                        missing_columns.append("output_data")
                    logger.error(f"验证失败：以下列未成功添加: {', '.join(missing_columns)}")
                    success = False
                
                return success
        finally:
            conn.close()
            
    except psycopg2.Error as e:
        logger.error(f"PostgreSQL错误: {e}", exc_info=True)
        return False
    except Exception as e:
        logger.error(f"添加列时发生未知错误: {e}", exc_info=True)
        return False

def get_connection_params():
    """交互式获取数据库连接参数"""
    print("\n请输入PostgreSQL数据库连接信息：")
    hostname = input("主机名 [localhost]: ") or "localhost"
    port = input("端口 [5432]: ") or "5432"
    dbname = input("数据库名 [ai_platform]: ") or "ai_platform"
    username = input("用户名 [postgres]: ") or "postgres"
    password = input("密码 [postgres]: ") or "postgres"
    
    return {
        "hostname": hostname,
        "port": port,
        "dbname": dbname,
        "username": username,
        "password": password
    }

if __name__ == "__main__":
    print("===== 添加缺失列 =====")
    
    # 检查是否提供了--interactive标志
    interactive = "--interactive" in sys.argv
    if interactive:
        print("您选择了交互模式，将引导您输入数据库连接信息")
        conn_params = get_connection_params()
        # 设置环境变量
        os.environ["DATABASE_URL"] = f"postgresql://{conn_params['username']}:{conn_params['password']}@{conn_params['hostname']}:{conn_params['port']}/{conn_params['dbname']}"
    
    # 添加缺失列
    if add_missing_columns():
        print("\n✅ 所有缺失列添加/检查成功")
    else:
        print("\n❌ 缺失列添加/检查失败")
        sys.exit(1)
        
    print("\n✅ 操作完成")
    sys.exit(0) 