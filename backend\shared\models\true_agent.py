"""
真正的智能体模型
支持工具调用、知识库、记忆系统、工作流等核心能力
"""

from sqlalchemy import Column, String, Float, Integer, Boolean, DateTime, ForeignKey, Text, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime
import uuid

# 创建Base类
Base = declarative_base()

class TrueAgent(Base):
    """真正的智能体模型"""
    __tablename__ = "true_agents"
    
    id = Column(String, primary_key=True, index=True, default=lambda: str(uuid.uuid4()))
    name = Column(String, index=True, nullable=False)
    description = Column(Text, nullable=True)
    agent_type = Column(String, default="general", index=True)
    system_prompt = Column(Text, nullable=True)
    
    # 智能体能力配置
    tools = Column(JSON, default=list)  # 可用工具列表 [{"type": "web_search", "config": {...}}]
    knowledge_bases = Column(JSON, default=list)  # 知识库配置
    memory_types = Column(JSON, default=list)  # 记忆类型 ["short_term", "long_term"]
    workflow_enabled = Column(Boolean, default=False)  # 是否启用工作流
    workflow_config = Column(JSON, default=dict)  # 工作流配置
    
    # 运行时配置
    max_tokens = Column(Integer, default=4000)
    temperature = Column(Float, default=0.7)
    max_tool_calls = Column(Integer, default=5)  # 最大工具调用次数
    
    # 状态信息
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 统计信息
    usage_count = Column(Integer, default=0)
    success_rate = Column(Float, default=0.0)
    
    # 关联关系
    conversations = relationship("AgentConversation", back_populates="agent")
    knowledge_bases_rel = relationship("AgentKnowledgeBase", back_populates="agent")
    memories = relationship("AgentMemory", back_populates="agent")
    tool_executions = relationship("ToolExecution", back_populates="agent")
    
    def to_dict(self):
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "agent_type": self.agent_type,
            "system_prompt": self.system_prompt,
            "tools": self.tools,
            "knowledge_bases": self.knowledge_bases,
            "memory_types": self.memory_types,
            "workflow_enabled": self.workflow_enabled,
            "workflow_config": self.workflow_config,
            "max_tokens": self.max_tokens,
            "temperature": self.temperature,
            "max_tool_calls": self.max_tool_calls,
            "is_active": self.is_active,
            "usage_count": self.usage_count,
            "success_rate": self.success_rate,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }

class AgentTool(Base):
    """智能体工具定义"""
    __tablename__ = "agent_tools"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    name = Column(String, nullable=False)
    type = Column(String, nullable=False)  # web_search, code_execution, etc.
    description = Column(Text)
    icon = Column(String, default="🛠️")
    category = Column(String, default="general")
    
    # 工具配置
    parameters_schema = Column(JSON, default=dict)  # 参数定义
    output_schema = Column(JSON, default=dict)  # 输出定义
    implementation = Column(Text)  # 工具实现代码
    
    # 状态
    is_enabled = Column(Boolean, default=True)
    is_builtin = Column(Boolean, default=True)  # 是否为内置工具
    created_at = Column(DateTime, default=datetime.utcnow)
    
    def to_dict(self):
        return {
            "id": self.id,
            "name": self.name,
            "type": self.type,
            "description": self.description,
            "icon": self.icon,
            "category": self.category,
            "parameters_schema": self.parameters_schema,
            "output_schema": self.output_schema,
            "is_enabled": self.is_enabled,
            "is_builtin": self.is_builtin,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }

class AgentKnowledgeBase(Base):
    """智能体知识库"""
    __tablename__ = "agent_knowledge_bases"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    agent_id = Column(String, ForeignKey("true_agents.id"), nullable=False)
    name = Column(String, nullable=False)
    type = Column(String, nullable=False)  # document, faq, structured, vector
    description = Column(Text)
    
    # 知识库配置
    config = Column(JSON, default=dict)
    embedding_model = Column(String, default="text-embedding-ada-002")
    chunk_size = Column(Integer, default=1000)
    chunk_overlap = Column(Integer, default=200)
    
    # 统计信息
    document_count = Column(Integer, default=0)
    total_chunks = Column(Integer, default=0)
    
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关联关系
    agent = relationship("TrueAgent", back_populates="knowledge_bases_rel")
    documents = relationship("KnowledgeDocument", back_populates="knowledge_base")
    
    def to_dict(self):
        return {
            "id": self.id,
            "agent_id": self.agent_id,
            "name": self.name,
            "type": self.type,
            "description": self.description,
            "config": self.config,
            "embedding_model": self.embedding_model,
            "chunk_size": self.chunk_size,
            "chunk_overlap": self.chunk_overlap,
            "document_count": self.document_count,
            "total_chunks": self.total_chunks,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }

class KnowledgeDocument(Base):
    """知识库文档"""
    __tablename__ = "knowledge_documents"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    knowledge_base_id = Column(String, ForeignKey("agent_knowledge_bases.id"), nullable=False)
    title = Column(String, nullable=False)
    content = Column(Text, nullable=False)
    content_type = Column(String, default="text")  # text, markdown, pdf, docx
    
    # 文档元数据
    doc_metadata = Column(JSON, default=dict)
    file_path = Column(String)  # 原始文件路径
    file_size = Column(Integer, default=0)
    
    # 向量化信息
    chunks = Column(JSON, default=list)  # 文档分块信息
    embeddings = Column(JSON, default=list)  # 向量嵌入
    
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # 关联关系
    knowledge_base = relationship("AgentKnowledgeBase", back_populates="documents")
    
    def to_dict(self):
        return {
            "id": self.id,
            "knowledge_base_id": self.knowledge_base_id,
            "title": self.title,
            "content": self.content[:500] + "..." if len(self.content) > 500 else self.content,
            "content_type": self.content_type,
            "metadata": self.doc_metadata,
            "file_path": self.file_path,
            "file_size": self.file_size,
            "chunk_count": len(self.chunks) if self.chunks else 0,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }

class AgentMemory(Base):
    """智能体记忆"""
    __tablename__ = "agent_memories"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    agent_id = Column(String, ForeignKey("true_agents.id"), nullable=False)
    memory_type = Column(String, nullable=False)  # short_term, long_term, episodic, semantic, procedural
    
    # 记忆内容
    content = Column(Text, nullable=False)
    context = Column(JSON, default=dict)  # 记忆上下文
    importance = Column(Float, default=0.5)  # 重要性评分 0-1
    
    # 关联信息
    conversation_id = Column(String)  # 关联的对话ID
    user_id = Column(String)  # 关联的用户ID
    
    # 时间信息
    created_at = Column(DateTime, default=datetime.utcnow)
    last_accessed = Column(DateTime, default=datetime.utcnow)
    access_count = Column(Integer, default=0)
    
    # 关联关系
    agent = relationship("TrueAgent", back_populates="memories")
    
    def to_dict(self):
        return {
            "id": self.id,
            "agent_id": self.agent_id,
            "memory_type": self.memory_type,
            "content": self.content,
            "context": self.context,
            "importance": self.importance,
            "conversation_id": self.conversation_id,
            "user_id": self.user_id,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "last_accessed": self.last_accessed.isoformat() if self.last_accessed else None,
            "access_count": self.access_count
        }

class AgentConversation(Base):
    """智能体对话"""
    __tablename__ = "agent_conversations"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    agent_id = Column(String, ForeignKey("true_agents.id"), nullable=False)
    user_id = Column(String)  # 用户ID
    title = Column(String, default="新对话")
    
    # 对话状态
    status = Column(String, default="active")  # active, completed, error
    message_count = Column(Integer, default=0)
    
    # 统计信息
    total_tokens = Column(Integer, default=0)
    tool_calls_count = Column(Integer, default=0)
    
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关联关系
    agent = relationship("TrueAgent", back_populates="conversations")
    messages = relationship("ConversationMessage", back_populates="conversation")
    
    def to_dict(self):
        return {
            "id": self.id,
            "agent_id": self.agent_id,
            "user_id": self.user_id,
            "title": self.title,
            "status": self.status,
            "message_count": self.message_count,
            "total_tokens": self.total_tokens,
            "tool_calls_count": self.tool_calls_count,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }

class ConversationMessage(Base):
    """对话消息"""
    __tablename__ = "conversation_messages"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    conversation_id = Column(String, ForeignKey("agent_conversations.id"), nullable=False)
    role = Column(String, nullable=False)  # user, agent, system, tool
    content = Column(Text, nullable=False)
    
    # 消息元数据
    msg_metadata = Column(JSON, default=dict)  # 工具调用结果、思考过程等
    tokens = Column(Integer, default=0)
    
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # 关联关系
    conversation = relationship("AgentConversation", back_populates="messages")
    
    def to_dict(self):
        return {
            "id": self.id,
            "conversation_id": self.conversation_id,
            "role": self.role,
            "content": self.content,
            "metadata": self.msg_metadata,
            "tokens": self.tokens,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }

class ToolExecution(Base):
    """工具执行记录"""
    __tablename__ = "tool_executions"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    agent_id = Column(String, ForeignKey("true_agents.id"), nullable=False)
    conversation_id = Column(String, ForeignKey("agent_conversations.id"))
    tool_type = Column(String, nullable=False)
    
    # 执行信息
    input_params = Column(JSON, default=dict)
    output_result = Column(JSON, default=dict)
    status = Column(String, default="pending")  # pending, success, error
    error_message = Column(Text)
    
    # 性能信息
    execution_time = Column(Float, default=0.0)  # 执行时间（秒）
    tokens_used = Column(Integer, default=0)
    
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # 关联关系
    agent = relationship("TrueAgent", back_populates="tool_executions")
    
    def to_dict(self):
        return {
            "id": self.id,
            "agent_id": self.agent_id,
            "conversation_id": self.conversation_id,
            "tool_type": self.tool_type,
            "input_params": self.input_params,
            "output_result": self.output_result,
            "status": self.status,
            "error_message": self.error_message,
            "execution_time": self.execution_time,
            "tokens_used": self.tokens_used,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }
