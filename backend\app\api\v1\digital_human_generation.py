"""
数字人生成API
"""

from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from sqlalchemy.orm import Session
from pydantic import BaseModel
from typing import Optional, Dict, Any, List
import uuid
import logging
import asyncio
from datetime import datetime

from app.core.sqlalchemy_db import get_db
from app.models.digital_human import DigitalHuman, DigitalHumanGeneration
from app.services.digital_human_generator import DigitalHumanGenerator

logger = logging.getLogger(__name__)

router = APIRouter()

class DigitalHumanCreateRequest(BaseModel):
    """数字人创建请求"""
    # 基本信息
    name: str
    type: str
    gender: str
    description: Optional[str] = ""
    welcome_text: Optional[str] = ""
    
    # 外观设置
    appearance_type: str = "template"
    selected_template: Optional[str] = None
    uploaded_image_url: Optional[str] = None
    generated_image_url: Optional[str] = None
    
    # 外观特征
    facial_expression: str = "friendly"
    clothing_style: str = "business"
    background: str = "office"
    
    # 声音设置
    voice_type: str = "standard"
    voice_speed: float = 1.0
    voice_pitch: float = 1.0
    selected_voice: Optional[str] = None
    custom_voice_url: Optional[str] = None

class DigitalHumanResponse(BaseModel):
    """数字人响应"""
    success: bool
    digital_human_id: int
    task_id: str
    status: str
    message: str
    progress: int = 0

class GenerationStatusResponse(BaseModel):
    """生成状态响应"""
    success: bool
    digital_human_id: int
    task_id: str
    status: str
    progress: int
    message: str
    result_urls: Optional[Dict[str, str]] = None
    error_message: Optional[str] = None

@router.post("/create", response_model=DigitalHumanResponse)
async def create_digital_human(
    request: DigitalHumanCreateRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """
    创建数字人
    """
    try:
        logger.info(f"开始创建数字人: {request.name}")
        
        # 创建数字人记录
        digital_human = DigitalHuman(
            name=request.name,
            type=request.type,
            gender=request.gender,
            description=request.description,
            welcome_text=request.welcome_text,
            appearance_type=request.appearance_type,
            selected_template=request.selected_template,
            uploaded_image_url=request.uploaded_image_url,
            generated_image_url=request.generated_image_url,
            facial_expression=request.facial_expression,
            clothing_style=request.clothing_style,
            background=request.background,
            voice_type=request.voice_type,
            voice_speed=request.voice_speed,
            voice_pitch=request.voice_pitch,
            selected_voice=request.selected_voice,
            custom_voice_url=request.custom_voice_url,
            status="creating"
        )
        
        db.add(digital_human)
        db.commit()
        db.refresh(digital_human)
        
        # 生成任务ID
        task_id = str(uuid.uuid4())
        
        # 创建生成任务记录
        generation_task = DigitalHumanGeneration(
            digital_human_id=digital_human.id,
            task_id=task_id,
            task_type="full",
            status="pending",
            input_data=request.dict(),
            start_time=datetime.utcnow()
        )
        
        db.add(generation_task)
        db.commit()
        
        # 启动Celery异步任务
        from app.tasks.digital_human import generate_digital_human

        # 启动异步任务
        celery_task = generate_digital_human.delay(task_id, digital_human.id, request.dict())

        # 更新生成任务记录的Celery任务ID
        generation_task.celery_task_id = celery_task.id
        db.commit()

        result = {
            "success": True,
            "digital_human_id": digital_human.id,
            "task_id": task_id,
            "celery_task_id": celery_task.id,
            "status": "started",
            "message": "数字人生成任务已启动"
        }
        
        logger.info(f"数字人创建任务已启动: ID={digital_human.id}, TaskID={task_id}")
        
        return DigitalHumanResponse(
            success=True,
            digital_human_id=digital_human.id,
            task_id=task_id,
            status="creating",
            message="数字人创建任务已启动",
            progress=0
        )
        
    except Exception as e:
        logger.error(f"创建数字人失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建数字人失败: {str(e)}")

@router.get("/status/{task_id}", response_model=GenerationStatusResponse)
async def get_generation_status(task_id: str, db: Session = Depends(get_db)):
    """
    获取生成状态
    """
    try:
        logger.info(f"查询生成状态: {task_id}")

        # 使用任务管理器获取状态
        from app.services.task_manager import task_manager
        status_info = task_manager.get_task_status(task_id)

        if not status_info["success"]:
            raise HTTPException(status_code=404, detail=status_info.get("error", "任务不存在"))

        # 构建响应
        response = GenerationStatusResponse(
            success=True,
            digital_human_id=status_info["digital_human_id"],
            task_id=task_id,
            status=status_info["status"],
            progress=status_info["progress"],
            message=status_info["message"],
            result_urls=status_info.get("result_urls"),
            error_message=status_info.get("error_message")
        )

        logger.info(f"返回生成状态: {response}")
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取生成状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取状态失败: {str(e)}")

@router.get("/list")
async def list_digital_humans(
    skip: int = 0,
    limit: int = 20,
    db: Session = Depends(get_db)
):
    """
    获取数字人列表
    """
    try:
        digital_humans = db.query(DigitalHuman).filter(
            DigitalHuman.is_active == True
        ).offset(skip).limit(limit).all()
        
        return {
            "success": True,
            "data": [dh.to_dict() for dh in digital_humans],
            "total": db.query(DigitalHuman).filter(DigitalHuman.is_active == True).count()
        }
        
    except Exception as e:
        logger.error(f"获取数字人列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取列表失败: {str(e)}")

@router.get("/{digital_human_id}")
async def get_digital_human(digital_human_id: int, db: Session = Depends(get_db)):
    """
    获取数字人详情
    """
    try:
        digital_human = db.query(DigitalHuman).filter(
            DigitalHuman.id == digital_human_id,
            DigitalHuman.is_active == True
        ).first()
        
        if not digital_human:
            raise HTTPException(status_code=404, detail="数字人不存在")
        
        return {
            "success": True,
            "data": digital_human.to_dict()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取数字人详情失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取详情失败: {str(e)}")

@router.delete("/{digital_human_id}")
async def delete_digital_human(digital_human_id: int, db: Session = Depends(get_db)):
    """
    删除数字人
    """
    try:
        digital_human = db.query(DigitalHuman).filter(
            DigitalHuman.id == digital_human_id
        ).first()
        
        if not digital_human:
            raise HTTPException(status_code=404, detail="数字人不存在")
        
        # 软删除
        digital_human.is_active = False
        db.commit()
        
        return {
            "success": True,
            "message": "数字人已删除"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除数字人失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除失败: {str(e)}")

# 后台任务函数已移至Celery任务中

def get_status_message(status: str, progress: int) -> str:
    """获取状态消息"""
    status_messages = {
        "pending": "等待开始生成",
        "running": f"正在生成中... ({progress}%)",
        "completed": "生成完成",
        "failed": "生成失败"
    }
    return status_messages.get(status, "未知状态")
