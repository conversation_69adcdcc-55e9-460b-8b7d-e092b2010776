import { ref, reactive, computed } from 'vue';

/**
 * 模态框管理钩子
 * @param {Object} options - 配置选项
 * @param {boolean} options.defaultVisible - 默认是否可见
 * @param {string} options.defaultTitle - 默认标题
 * @param {Object} options.defaultData - 默认数据
 * @param {Function} options.onOpen - 打开回调
 * @param {Function} options.onClose - 关闭回调
 * @param {Function} options.onSubmit - 提交回调
 * @param {Function} options.onCancel - 取消回调
 * @param {Function} options.beforeClose - 关闭前钩子
 * @returns {Object} 模态框控制对象
 */
export default function useModal(options = {}) {
  const {
    defaultVisible = false,
    defaultTitle = '',
    defaultData = {},
    onOpen,
    onClose,
    onSubmit,
    onCancel,
    beforeClose
  } = options;
  
  // 模态框可见状态
  const visible = ref(defaultVisible);
  // 模态框标题
  const title = ref(defaultTitle);
  // 模态框数据
  const data = reactive({...defaultData});
  // 确认按钮加载状态
  const confirmLoading = ref(false);
  // 模态框类型 (add/edit/view)
  const modalType = ref('add');
  
  // 重置数据到默认值
  const resetData = () => {
    Object.keys(data).forEach(key => {
      if (key in defaultData) {
        data[key] = defaultData[key];
      } else {
        delete data[key];
      }
    });
  };
  
  // 打开模态框
  const open = async (params = {}) => {
    const {
      type = 'add',
      title: newTitle,
      data: newData,
      ...rest
    } = params;
    
    // 设置模态框类型
    modalType.value = type;
    
    // 设置标题
    if (newTitle) {
      title.value = newTitle;
    } else {
      // 根据类型设置默认标题
      switch (type) {
        case 'add':
          title.value = '新增';
          break;
        case 'edit':
          title.value = '编辑';
          break;
        case 'view':
          title.value = '查看';
          break;
        default:
          title.value = defaultTitle;
      }
    }
    
    // 重置或设置数据
    if (type === 'add') {
      resetData();
    } else if (newData) {
      // 合并新数据到data
      Object.keys(newData).forEach(key => {
        data[key] = newData[key];
      });
    }
    
    // 显示模态框
    visible.value = true;
    
    // 调用打开回调
    if (typeof onOpen === 'function') {
      try {
        await onOpen({
          type: modalType.value,
          data,
          ...rest
        });
      } catch (error) {
        console.error('模态框打开回调执行失败:', error);
      }
    }
  };
  
  // 关闭模态框
  const close = async () => {
    // 执行关闭前钩子
    if (typeof beforeClose === 'function') {
      try {
        const shouldClose = await beforeClose({
          type: modalType.value,
          data
        });
        
        // 如果返回false，则阻止关闭
        if (shouldClose === false) {
          return;
        }
      } catch (error) {
        console.error('模态框关闭前钩子执行失败:', error);
      }
    }
    
    // 隐藏模态框
    visible.value = false;
    
    // 重置加载状态
    confirmLoading.value = false;
    
    // 调用关闭回调
    if (typeof onClose === 'function') {
      try {
        await onClose({
          type: modalType.value,
          data
        });
      } catch (error) {
        console.error('模态框关闭回调执行失败:', error);
      }
    }
  };
  
  // 提交表单
  const submit = async () => {
    confirmLoading.value = true;
    
    try {
      // 调用提交回调
      if (typeof onSubmit === 'function') {
        await onSubmit({
          type: modalType.value,
          data
        });
      }
      
      // 关闭模态框
      await close();
    } catch (error) {
      console.error('模态框提交失败:', error);
    } finally {
      confirmLoading.value = false;
    }
  };
  
  // 取消提交
  const cancel = async () => {
    try {
      // 调用取消回调
      if (typeof onCancel === 'function') {
        await onCancel({
          type: modalType.value,
          data
        });
      }
      
      // 关闭模态框
      await close();
    } catch (error) {
      console.error('模态框取消回调执行失败:', error);
    }
  };
  
  // 根据类型判断是否为新增模式
  const isAdd = computed(() => modalType.value === 'add');
  
  // 根据类型判断是否为编辑模式
  const isEdit = computed(() => modalType.value === 'edit');
  
  // 根据类型判断是否为查看模式
  const isView = computed(() => modalType.value === 'view');
  
  // 模态框属性
  const modalProps = computed(() => ({
    visible: visible.value,
    title: title.value,
    confirmLoading: confirmLoading.value,
    destroyOnClose: true,
    onOk: submit,
    onCancel: cancel,
    width: options.width || 720
  }));
  
  // 表单禁用状态（查看模式下禁用）
  const formDisabled = computed(() => modalType.value === 'view');
  
  return {
    visible,
    title,
    data,
    confirmLoading,
    modalType,
    isAdd,
    isEdit,
    isView,
    formDisabled,
    modalProps,
    open,
    close,
    submit,
    cancel,
    resetData
  };
}