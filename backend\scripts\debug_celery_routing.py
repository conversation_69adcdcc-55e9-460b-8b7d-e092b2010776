#!/usr/bin/env python3
"""
调试 Celery 任务路由问题
"""
import os
import sys
import uuid
from pathlib import Path
from dotenv import load_dotenv

# 添加后端路径
backend_dir = Path(__file__).parent.parent
sys.path.append(str(backend_dir))

# 加载环境变量
env_path = backend_dir / ".env"
load_dotenv(env_path)

def check_celery_app():
    """检查 Celery 应用配置"""
    print("🔍 检查 Celery 应用配置...")
    
    try:
        from app.core.celery_app import celery_app
        
        print("✅ Celery 应用导入成功")
        print(f"📊 应用名称: {celery_app.main}")
        print(f"📊 Broker URL: {celery_app.conf.broker_url}")
        print(f"📊 Result Backend: {celery_app.conf.result_backend}")
        
        # 检查任务路由
        print(f"\n📋 任务路由配置:")
        for pattern, config in celery_app.conf.task_routes.items():
            print(f"   {pattern} -> {config}")
        
        # 检查已注册的任务
        print(f"\n📋 已注册的任务:")
        for task_name in sorted(celery_app.tasks.keys()):
            if not task_name.startswith('celery.'):
                print(f"   ✅ {task_name}")
        
        return celery_app
        
    except Exception as e:
        print(f"❌ Celery 应用检查失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def check_task_registration():
    """检查 Wanx 任务注册"""
    print("\n🔍 检查 Wanx 任务注册...")
    
    try:
        from app.tasks.wanx_video_tasks import wanx_text_to_video, wanx_image_to_video
        
        print("✅ Wanx 任务导入成功")
        print(f"📊 文本转视频任务: {wanx_text_to_video.name}")
        print(f"📊 图片转视频任务: {wanx_image_to_video.name}")
        
        # 检查任务的队列配置
        print(f"\n📋 任务队列配置:")
        print(f"   文本转视频: {getattr(wanx_text_to_video, 'queue', '默认队列')}")
        print(f"   图片转视频: {getattr(wanx_image_to_video, 'queue', '默认队列')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Wanx 任务检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_task_routing(celery_app):
    """测试任务路由"""
    print("\n🧪 测试任务路由...")
    
    try:
        # 测试路由解析
        task_name = "app.tasks.wanx_video_tasks.wanx_text_to_video"
        
        # 手动检查路由匹配
        matched_queue = None
        for pattern, config in celery_app.conf.task_routes.items():
            if pattern.endswith('*'):
                prefix = pattern[:-1]
                if task_name.startswith(prefix):
                    matched_queue = config.get('queue')
                    print(f"✅ 任务 {task_name} 匹配路由 {pattern} -> 队列 {matched_queue}")
                    break
            elif pattern == task_name:
                matched_queue = config.get('queue')
                print(f"✅ 任务 {task_name} 精确匹配路由 -> 队列 {matched_queue}")
                break
        
        if not matched_queue:
            print(f"❌ 任务 {task_name} 没有匹配的路由")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 任务路由测试失败: {e}")
        return False

def test_redis_queue():
    """测试 Redis 队列"""
    print("\n🔍 检查 Redis 队列...")
    
    try:
        import redis
        
        r = redis.Redis(host='localhost', port=6379, db=0)
        r.ping()
        print("✅ Redis 连接正常")
        
        # 检查队列长度
        queues = ['wanx_generation', 'digital_human', 'image_processing', 'voice_processing', 'video_generation', 'translation']
        
        print(f"\n📊 队列状态:")
        for queue in queues:
            length = r.llen(queue)
            print(f"   {queue}: {length} 个任务")
        
        return True
        
    except Exception as e:
        print(f"❌ Redis 检查失败: {e}")
        return False

def test_manual_task_send(celery_app):
    """手动发送任务到指定队列"""
    print("\n🧪 手动发送任务到队列...")
    
    try:
        task_id = str(uuid.uuid4())
        
        # 直接发送任务到 wanx_generation 队列
        result = celery_app.send_task(
            'wanx_text_to_video',
            args=[],
            kwargs={
                'task_id': task_id,
                'prompt': '测试任务',
                'model': 't2v-1.3B',
                'duration': 3,
                'resolution': '512x512',
                'fps': 24,
                'guidance_scale': 7.5,
                'num_inference_steps': 20,
                'user_id': 'test-user'
            },
            queue='wanx_generation'
        )
        
        print(f"✅ 任务已发送到 wanx_generation 队列")
        print(f"📊 任务ID: {task_id}")
        print(f"📊 Celery 任务ID: {result.id}")
        print(f"📊 任务状态: {result.status}")
        
        return task_id, result
        
    except Exception as e:
        print(f"❌ 手动发送任务失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None

def main():
    """主函数"""
    print("🧪 Celery 任务路由调试")
    print("=" * 50)
    
    # 检查 Celery 应用
    celery_app = check_celery_app()
    if not celery_app:
        return
    
    # 检查任务注册
    task_registered = check_task_registration()
    if not task_registered:
        return
    
    # 测试任务路由
    routing_ok = test_task_routing(celery_app)
    
    # 检查 Redis
    redis_ok = test_redis_queue()
    
    # 手动发送任务测试
    if routing_ok and redis_ok:
        print("\n🚀 尝试手动发送任务...")
        task_id, result = test_manual_task_send(celery_app)
        
        if task_id:
            print(f"\n⏳ 等待 10 秒查看任务处理...")
            import time
            time.sleep(10)
            
            # 检查任务状态
            if result:
                print(f"📊 最终任务状态: {result.status}")
                try:
                    print(f"📊 任务结果: {result.result}")
                except:
                    print("📊 任务结果: 无法获取")
    
    # 总结
    print(f"\n📊 调试总结:")
    print(f"✅ Celery 应用: 正常")
    print(f"{'✅' if task_registered else '❌'} 任务注册: {'正常' if task_registered else '失败'}")
    print(f"{'✅' if routing_ok else '❌'} 任务路由: {'正常' if routing_ok else '失败'}")
    print(f"{'✅' if redis_ok else '❌'} Redis 队列: {'正常' if redis_ok else '失败'}")
    
    if not (task_registered and routing_ok and redis_ok):
        print(f"\n💡 建议:")
        if not task_registered:
            print(f"   - 检查 Wanx 任务模块导入")
        if not routing_ok:
            print(f"   - 检查任务路由配置")
        if not redis_ok:
            print(f"   - 检查 Redis 服务状态")

if __name__ == "__main__":
    main()
