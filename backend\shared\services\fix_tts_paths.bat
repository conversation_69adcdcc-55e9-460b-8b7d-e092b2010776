@echo off
echo ========================================
echo TTS模型路径修复工具
echo ========================================
echo 此脚本将修复TTS模型路径问题，包括：
echo  1. 清除TTS下载缓存
echo  2. 查找系统中存在的模型文件
echo  3. 将模型文件复制到TTS期望的位置
echo.

REM 设置工作目录
cd /d "%~dp0"

REM 检查Python环境
python --version > nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo [错误] Python未安装或未添加到PATH环境变量
    echo 请安装Python 3.8或更高版本
    echo 下载地址: https://www.python.org/downloads/
    echo.
    pause
    exit /b
)

echo [步骤1] 检查TTS库安装...
python -c "import TTS" > nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo TTS库未安装，正在安装...
    pip install TTS
    if %ERRORLEVEL% NEQ 0 (
        echo [错误] TTS库安装失败，请手动安装:
        echo pip install TTS
        echo.
        pause
        exit /b
    )
    echo [成功] TTS库安装完成
) else (
    echo [成功] TTS库已安装
)

echo.
echo [步骤2] 修复TTS模型路径...
python fix_tts_model_paths.py
if %ERRORLEVEL% NEQ 0 (
    echo [警告] TTS模型路径修复可能未完全成功
) else (
    echo [成功] TTS模型路径修复完成
)

echo.
echo [步骤3] 创建关键目录结构...
mkdir ..\models\tts 2>nul
mkdir ..\models\tts\tts 2>nul
mkdir ..\models\tts\tts\tts-models--zh-CN--baker--tacotron2-DDC-GST 2>nul
mkdir ..\models\tts\tts\tts-models--en--ljspeech--tacotron2-DDC 2>nul
mkdir ..\models\tts\tts\tts-models--multilingual--multi-dataset--your_tts 2>nul
echo [成功] 目录结构创建完成

echo.
echo ========================================
echo TTS模型路径修复完成
echo ========================================
echo 如果系统仍然无法正常工作，请尝试运行fix_tts_all.bat
echo 进行全面修复或联系技术支持
echo.
pause 