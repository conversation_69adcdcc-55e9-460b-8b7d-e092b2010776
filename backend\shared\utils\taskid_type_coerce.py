# -*- coding: utf-8 -*-
"""
SQLAlchemy类型转换器，自动将generation_task_id字段转换为字符串类型
使用方法：
from backend.utils.taskid_type_coerce import TaskIDTypeCoercePlugin
engine = create_engine(DATABASE_URL)
TaskIDTypeCoercePlugin(engine)  # 注册插件
"""
import logging
import traceback
from sqlalchemy import event
from sqlalchemy.engine import Engine
from sqlalchemy.exc import SQLAlchemyError

# 获取日志记录器
logger = logging.getLogger(__name__)

class TaskIDTypeCoercePlugin:
    """
    SQLAlchemy插件，自动将generation_task_id字段转换为字符串类型
    
    增强版本具有以下功能：
    1. 在多个事件点监听参数类型
    2. 添加错误处理和日志记录
    3. 支持不同形式的参数（字典、元组、关键字等）
    4. 处理UUID和其他非字符串类型
    """
    def __init__(self, engine):
        self.engine = engine
        self.setup_listeners()
        logger.info("TaskIDTypeCoercePlugin已初始化，将自动转换generation_task_id字段为字符串类型")
        
    def setup_listeners(self):
        """注册所有事件监听器"""
        # 主要转换器：在执行SQL前处理参数
        event.listen(self.engine, "before_execute", self.before_execute)
        
        # 错误恢复：在SQL执行出错时尝试修复
        event.listen(self.engine, "handle_error", self.handle_error)
        
        # 检查空值处理
        event.listen(self.engine, "engine_connect", self.engine_connect)
        
        logger.debug("TaskIDTypeCoercePlugin已注册所有事件监听器")
    
    def before_execute(self, conn, clauseelement, multiparams, params):
        """在执行SQL前处理参数，确保generation_task_id是字符串"""
        try:
            # 处理multiparams（多参数）
            if multiparams:
                new_multiparams = []
                for mp in multiparams:
                    # 处理字典形式的参数
                    if isinstance(mp, dict) and 'generation_task_id' in mp:
                        mp = mp.copy()  # 创建副本避免修改原始对象
                        if mp['generation_task_id'] is not None:
                            mp['generation_task_id'] = str(mp['generation_task_id'])
                            logger.debug(f"Coerced 'generation_task_id' to string: {mp['generation_task_id']}")
                    new_multiparams.append(mp)
                
                if new_multiparams != multiparams:
                    multiparams = tuple(new_multiparams)
            
            # 处理params（命名参数）
            if params and isinstance(params, dict) and 'generation_task_id' in params:
                params = params.copy()  # 创建副本避免修改原始对象
                if params['generation_task_id'] is not None:
                    params['generation_task_id'] = str(params['generation_task_id'])
                    logger.debug(f"Coerced params 'generation_task_id' to string: {params['generation_task_id']}")
            
            return clauseelement, multiparams, params
        
        except Exception as e:
            logger.error(f"TaskIDTypeCoercePlugin.before_execute错误: {e}")
            # 出错时返回原始参数，避免阻止SQL执行
            return clauseelement, multiparams, params
    
    def handle_error(self, context):
        """
        在SQL执行出错时尝试修复类型问题
        
        如果发现类型错误与generation_task_id相关，尝试修复并重新执行
        """
        error = context.original_exception
        if error and isinstance(error, SQLAlchemyError):
            # 检查是否是类型不匹配错误
            error_text = str(error).lower()
            if ('invalid input syntax' in error_text or 
                'type integer' in error_text or 
                'generation_task_id' in error_text):
                
                logger.warning(f"检测到可能的generation_task_id类型错误: {error}")
                
                # 不尝试自动修复，而是记录详细信息以便调试
                if hasattr(context, 'statement') and context.statement:
                    logger.debug(f"出错的SQL语句: {context.statement}")
                
                if hasattr(context, 'parameters') and context.parameters:
                    logger.debug(f"参数: {context.parameters}")
                    
                    # 检查参数中是否有generation_task_id
                    params = context.parameters
                    if isinstance(params, dict) and 'generation_task_id' in params:
                        logger.debug(f"generation_task_id值: {params['generation_task_id']}, 类型: {type(params['generation_task_id'])}")
                    elif isinstance(params, (list, tuple)) and len(params) > 0:
                        for param in params:
                            if isinstance(param, dict) and 'generation_task_id' in param:
                                logger.debug(f"generation_task_id值: {param['generation_task_id']}, 类型: {type(param['generation_task_id'])}")
    
    def engine_connect(self, conn, branch):
        """
        在创建新连接时执行一次类型检查
        
        这有助于验证连接配置是否正确以及类型转换是否正常工作
        """
        if not branch:  # 仅在主连接上执行
            try:
                # 只检查连接对象是否有效，不执行任何SQL
                is_valid = conn is not None
                
                # 记录连接信息但不执行任何操作
                if is_valid:
                    logger.debug("数据库连接初始化成功")
                    
                    # 尝试获取数据库方言信息
                    if hasattr(conn, 'engine') and hasattr(conn.engine, 'dialect'):
                        dialect = conn.engine.dialect
                        dialect_name = getattr(dialect, 'name', 'unknown')
                        logger.debug(f"数据库类型: {dialect_name}")
                else:
                    logger.warning("数据库连接无效")
            except Exception as e:
                logger.error(f"数据库连接检查失败: {str(e)}")
        
        # 不修改连接行为
        return conn
