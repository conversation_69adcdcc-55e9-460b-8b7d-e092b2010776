/**
 * WebSocket实时通信服务
 */

class WebSocketService {
  constructor() {
    this.ws = null;
    this.userId = null;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectInterval = 3000;
    this.heartbeatInterval = 30000;
    this.heartbeatTimer = null;
    this.isConnecting = false;
    this.isManualClose = false;
    
    // 事件监听器
    this.listeners = {
      open: [],
      close: [],
      error: [],
      message: [],
      alert: [],
      report_ready: [],
      search_update: [],
      system_notification: [],
      hot_topic_update: [],
      stats_update: []
    };
  }

  /**
   * 连接WebSocket
   * @param {string} userId - 用户ID
   */
  connect(userId) {
    if (this.isConnecting || (this.ws && this.ws.readyState === WebSocket.OPEN)) {
      return;
    }

    this.userId = userId;
    this.isConnecting = true;
    this.isManualClose = false;

    const wsUrl = `ws://localhost:8000/api/v1/ws/ws/${userId}`;
    
    try {
      this.ws = new WebSocket(wsUrl);
      this.setupEventListeners();
    } catch (error) {
      console.error('WebSocket连接失败:', error);
      this.isConnecting = false;
      this.handleReconnect();
    }
  }

  /**
   * 设置WebSocket事件监听器
   */
  setupEventListeners() {
    this.ws.onopen = (event) => {
      console.log('WebSocket连接已建立');
      this.isConnecting = false;
      this.reconnectAttempts = 0;
      this.startHeartbeat();
      this.emit('open', event);
    };

    this.ws.onclose = (event) => {
      console.log('WebSocket连接已关闭', event.code, event.reason);
      this.isConnecting = false;
      this.stopHeartbeat();
      this.emit('close', event);
      
      if (!this.isManualClose) {
        this.handleReconnect();
      }
    };

    this.ws.onerror = (error) => {
      console.error('WebSocket错误:', error);
      this.isConnecting = false;
      this.emit('error', error);
    };

    this.ws.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data);
        this.handleMessage(message);
      } catch (error) {
        console.error('解析WebSocket消息失败:', error);
      }
    };
  }

  /**
   * 处理接收到的消息
   * @param {Object} message - 消息对象
   */
  handleMessage(message) {
    const { type, data } = message;
    
    console.log('收到WebSocket消息:', type, data);
    
    // 触发通用消息事件
    this.emit('message', message);
    
    // 触发特定类型事件
    if (this.listeners[type]) {
      this.emit(type, data);
    }

    // 处理特殊消息类型
    switch (type) {
      case 'pong':
        // 心跳响应，无需处理
        break;
      case 'connection_established':
        console.log('WebSocket连接确认:', data.message);
        break;
      case 'error':
        console.error('服务器错误:', data.message);
        break;
      default:
        // 其他消息类型已通过事件系统处理
        break;
    }
  }

  /**
   * 发送消息
   * @param {Object} message - 消息对象
   */
  send(message) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
    } else {
      console.warn('WebSocket未连接，无法发送消息');
    }
  }

  /**
   * 订阅频道
   * @param {string} channel - 频道名称
   */
  subscribe(channel) {
    this.send({
      type: 'subscribe',
      data: { channel }
    });
  }

  /**
   * 取消订阅频道
   * @param {string} channel - 频道名称
   */
  unsubscribe(channel) {
    this.send({
      type: 'unsubscribe',
      data: { channel }
    });
  }

  /**
   * 获取连接统计
   */
  getStats() {
    this.send({
      type: 'get_stats'
    });
  }

  /**
   * 开始心跳检测
   */
  startHeartbeat() {
    this.stopHeartbeat();
    this.heartbeatTimer = setInterval(() => {
      this.send({ type: 'ping' });
    }, this.heartbeatInterval);
  }

  /**
   * 停止心跳检测
   */
  stopHeartbeat() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
  }

  /**
   * 处理重连
   */
  handleReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts && !this.isManualClose) {
      this.reconnectAttempts++;
      console.log(`尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
      
      setTimeout(() => {
        if (this.userId && !this.isManualClose) {
          this.connect(this.userId);
        }
      }, this.reconnectInterval);
    } else {
      console.error('WebSocket重连失败，已达到最大重试次数');
    }
  }

  /**
   * 手动关闭连接
   */
  disconnect() {
    this.isManualClose = true;
    this.stopHeartbeat();
    
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }

  /**
   * 添加事件监听器
   * @param {string} event - 事件名称
   * @param {Function} callback - 回调函数
   */
  on(event, callback) {
    if (!this.listeners[event]) {
      this.listeners[event] = [];
    }
    this.listeners[event].push(callback);
  }

  /**
   * 移除事件监听器
   * @param {string} event - 事件名称
   * @param {Function} callback - 回调函数
   */
  off(event, callback) {
    if (this.listeners[event]) {
      const index = this.listeners[event].indexOf(callback);
      if (index > -1) {
        this.listeners[event].splice(index, 1);
      }
    }
  }

  /**
   * 触发事件
   * @param {string} event - 事件名称
   * @param {*} data - 事件数据
   */
  emit(event, data) {
    if (this.listeners[event]) {
      this.listeners[event].forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`事件处理器错误 (${event}):`, error);
        }
      });
    }
  }

  /**
   * 获取连接状态
   */
  getConnectionState() {
    if (!this.ws) return 'CLOSED';
    
    switch (this.ws.readyState) {
      case WebSocket.CONNECTING:
        return 'CONNECTING';
      case WebSocket.OPEN:
        return 'OPEN';
      case WebSocket.CLOSING:
        return 'CLOSING';
      case WebSocket.CLOSED:
        return 'CLOSED';
      default:
        return 'UNKNOWN';
    }
  }

  /**
   * 检查是否已连接
   */
  isConnected() {
    return this.ws && this.ws.readyState === WebSocket.OPEN;
  }
}

// 创建全局WebSocket服务实例
const websocketService = new WebSocketService();

export default websocketService;
