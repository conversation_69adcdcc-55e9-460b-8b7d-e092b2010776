#!/usr/bin/env python3
"""
完整的 Celery 系统测试 - 简洁版本
"""
import os
import sys
import time
from pathlib import Path
from dotenv import load_dotenv

# 添加后端路径
backend_dir = Path(__file__).parent.parent
sys.path.append(str(backend_dir))

# 加载环境变量
env_path = backend_dir / ".env"
load_dotenv(env_path)

def test_celery_connection():
    """测试 Celery 连接"""
    print("🔍 测试 Celery 连接")
    print("=" * 50)
    
    try:
        from app.core.celery_simple import celery_app
        
        # 检查 broker 连接
        inspect = celery_app.control.inspect()
        active_workers = inspect.active()
        
        if active_workers:
            print(f"✅ 发现 {len(active_workers)} 个活跃 Worker")
            for worker_name in active_workers.keys():
                print(f"   📍 {worker_name}")
            return True
        else:
            print("❌ 没有活跃的 Worker")
            return False
            
    except Exception as e:
        print(f"❌ Celery 连接失败: {e}")
        return False

def test_all_modules():
    """测试所有模块"""
    print(f"\n🧪 测试所有模块")
    print("=" * 50)
    
    modules = ['video', 'translation', 'image', 'audio', 'digital_human']
    results = {}
    
    for module in modules:
        try:
            print(f"\n📦 测试 {module} 模块...")
            
            if module == 'video':
                from app.tasks.video_tasks import test_video_task
                task = test_video_task.delay(f"Test {module}")
            elif module == 'translation':
                from app.tasks.translation_tasks_simple import test_translation_task
                task = test_translation_task.delay(f"Test {module}")
            elif module == 'image':
                from app.tasks.image_tasks import test_image_task
                task = test_image_task.delay(f"Test {module}")
            elif module == 'audio':
                from app.tasks.audio_tasks import test_audio_task
                task = test_audio_task.delay(f"Test {module}")
            elif module == 'digital_human':
                from app.tasks.digital_human_tasks_simple import test_digital_human_task
                task = test_digital_human_task.delay(f"Test {module}")
            
            print(f"   📋 任务 ID: {task.id}")
            
            # 等待结果
            result = task.get(timeout=10)
            print(f"   ✅ 结果: {result}")
            results[module] = True
            
        except Exception as e:
            print(f"   ❌ 失败: {e}")
            results[module] = False
    
    return results

def test_real_tasks():
    """测试真实任务"""
    print(f"\n🎬 测试真实任务")
    print("=" * 50)
    
    results = {}
    
    # 1. 测试视频生成
    try:
        print("\n📹 测试视频生成任务...")
        from app.tasks.video_tasks import text_to_video
        
        task = text_to_video.delay(
            prompt="一个和尚敲木鱼",
            model="wanx-2.1",
            duration=5
        )
        
        print(f"   📋 任务 ID: {task.id}")
        print("   ⏳ 等待完成...")
        
        result = task.get(timeout=30)
        print(f"   ✅ 视频生成完成: {result['status']}")
        results['video_generation'] = True
        
    except Exception as e:
        print(f"   ❌ 视频生成失败: {e}")
        results['video_generation'] = False
    
    # 2. 测试翻译
    try:
        print("\n🌐 测试翻译任务...")
        from app.tasks.translation_tasks_simple import translate_text
        
        task = translate_text.delay(
            text="Hello, how are you?",
            source_lang="en",
            target_lang="zh"
        )
        
        print(f"   📋 任务 ID: {task.id}")
        result = task.get(timeout=15)
        print(f"   ✅ 翻译完成: {result['translated_text']}")
        results['translation'] = True
        
    except Exception as e:
        print(f"   ❌ 翻译失败: {e}")
        results['translation'] = False
    
    # 3. 测试图像生成
    try:
        print("\n🖼️ 测试图像生成任务...")
        from app.tasks.image_tasks import text_to_image
        
        task = text_to_image.delay(
            prompt="A beautiful sunset",
            model="stable-diffusion"
        )
        
        print(f"   📋 任务 ID: {task.id}")
        result = task.get(timeout=20)
        print(f"   ✅ 图像生成完成: {result['status']}")
        results['image_generation'] = True
        
    except Exception as e:
        print(f"   ❌ 图像生成失败: {e}")
        results['image_generation'] = False
    
    return results

def test_task_management():
    """测试任务管理"""
    print(f"\n📊 测试任务管理")
    print("=" * 50)
    
    try:
        from app.core.celery_simple import simple_task_manager
        
        # 创建测试任务
        task_id = simple_task_manager.create_task(
            task_id="test-task-123",
            task_type="test",
            user_id="test-user",
            message="测试任务管理"
        )
        
        print(f"✅ 创建任务: {task_id}")
        
        # 更新任务
        simple_task_manager.update_task(
            task_id=task_id,
            status="processing",
            progress=50,
            message="处理中..."
        )
        
        print("✅ 更新任务状态")
        
        # 获取任务
        task_info = simple_task_manager.get_task(task_id)
        if task_info:
            print(f"✅ 获取任务信息: {task_info['status']}")
        
        # 获取按类型的任务
        test_tasks = simple_task_manager.get_tasks_by_type("test")
        print(f"✅ 获取测试任务: {len(test_tasks)} 个")
        
        return True
        
    except Exception as e:
        print(f"❌ 任务管理测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 Celery 完整系统测试")
    print("=" * 60)
    
    all_results = {}
    
    # 1. 测试连接
    all_results['connection'] = test_celery_connection()
    
    if not all_results['connection']:
        print(f"\n❌ Celery 连接失败，请先启动 Worker:")
        print(f"   cd backend")
        print(f"   celery -A app.core.celery_simple worker --pool=solo --loglevel=info")
        return False
    
    # 2. 测试任务管理
    all_results['task_management'] = test_task_management()
    
    # 3. 测试所有模块
    module_results = test_all_modules()
    all_results.update(module_results)
    
    # 4. 测试真实任务
    real_task_results = test_real_tasks()
    all_results.update(real_task_results)
    
    # 总结
    print(f"\n📊 测试结果总结")
    print("=" * 60)
    
    passed = 0
    total = len(all_results)
    
    for test_name, result in all_results.items():
        status_icon = "✅" if result else "❌"
        status_text = "通过" if result else "失败"
        print(f"{status_icon} {test_name.upper()}: {status_text}")
        if result:
            passed += 1
    
    print(f"\n📈 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print(f"\n🎉 所有测试通过！Celery 系统工作正常！")
        print(f"💡 系统特点:")
        print(f"   - 简洁的任务定义")
        print(f"   - 统一的任务管理")
        print(f"   - 模块化设计")
        print(f"   - Windows 兼容")
        print(f"   - 实时进度监控")
    else:
        print(f"\n⚠️ 部分测试失败，请检查相关配置")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ Celery 系统测试完成！")
    else:
        print("\n❌ Celery 系统存在问题")
    
    input("按 Enter 键退出...")
