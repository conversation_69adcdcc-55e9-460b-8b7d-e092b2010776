import sys
import os
import importlib.util
import logging
import asyncio
import json
from typing import Optional, Dict, Any, List, Union
from datetime import datetime
from sqlalchemy import text

# 进度跟踪器路径
file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "progress_tracker.py")

# 动态导入
if os.path.exists(file_path):
    try:
        spec = importlib.util.spec_from_file_location("progress_tracker", file_path)
        progress_tracker = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(progress_tracker)
        get_progress_tracker = getattr(progress_tracker, "get_progress_tracker", None)
    except Exception as e:
        get_progress_tracker = None
        logging.warning(f"无法导入进度跟踪器: {e}")
else:
    get_progress_tracker = None
    logging.warning(f"进度跟踪器文件不存在: {file_path}")

# 配置日志
logger = logging.getLogger(__name__)

from utils.db import get_async_session

class ProgressUpdater:
    """
    进度更新器 - 处理任务执行过程中的进度更新
    支持数据库和实时进度更新
    """
    
    def __init__(self, task_id: str):
        """
        初始化进度更新器
        
        Args:
            task_id: 任务ID
        """
        self.task_id = task_id
        self.db_pool = None
        self.websocket_manager = None
        self.locks = {}  # 用于不同资源的锁
    
    async def _get_websocket_manager(self):
        """
        获取WebSocket管理器
        
        Returns:
            WebSocket管理器或None
        """
        if self.websocket_manager is None:
            try:
                # 导入WebSocket管理器
                from services.websocket_manager import get_websocket_manager
                self.websocket_manager = get_websocket_manager()
            except ImportError:
                logger.debug("WebSocket管理器不可用，不会发送实时更新")
                self.websocket_manager = None
                
        return self.websocket_manager
    
    async def _get_lock(self, resource_name: str):
        """
        获取指定资源的锁
        
        Args:
            resource_name: 资源名称
            
        Returns:
            异步锁
        """
        if resource_name not in self.locks:
            self.locks[resource_name] = asyncio.Lock()
        return self.locks[resource_name]
    
    async def update(
        self, 
        status: Optional[str] = None, 
        progress: Optional[int] = None, 
        message: Optional[str] = None,
        error: Optional[Union[str, Dict[str, Any]]] = None,
        **kwargs
    ) -> bool:
        """
        更新任务进度
        
        Args:
            status: 任务状态 (pending, running, completed, failed)
            progress: 进度百分比 (0-100)
            message: 进度消息
            error: 错误信息
            **kwargs: 附加数据
            
        Returns:
            是否成功
        """
        # 获取数据库锁
        db_lock = await self._get_lock("db")
        
        try:
            # 验证进度
            if progress is not None:
                progress = max(0, min(100, progress))
            
            # 标准化错误信息
            error_data = None
            if error:
                if isinstance(error, str):
                    error_data = {"message": error}
                else:
                    error_data = error
            
            # 同步更新数据库
            async with db_lock:
                success = await self._update_database(
                    status=status,
                    progress=progress,
                    message=message,
                    error=error_data,
                    **kwargs
                )
            
            # 发送实时更新
            await self._send_realtime_update(
                status=status,
                progress=progress,
                message=message,
                error=error_data,
                **kwargs
            )
            
            return success
        except Exception as e:
            logger.error(f"更新任务进度失败: {e}")
            return False
    
    async def _update_database(
        self, 
        status: Optional[str] = None, 
        progress: Optional[int] = None, 
        message: Optional[str] = None,
        error: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> bool:
        """
        更新数据库中的任务状态
        """
        try:
            # 直接判断是否有异步支持，走异步分支
            from utils.db import HAS_ASYNC_SUPPORT
            if HAS_ASYNC_SUPPORT:
                return await self._update_async_database(
                    status=status,
                    progress=progress,
                    message=message,
                    error=error,
                    **kwargs
                )
            else:
                return await self._update_sync_database(
                    status=status,
                    progress=progress,
                    message=message,
                    error=error,
                    **kwargs
                )
        except Exception as e:
            logger.error(f"更新数据库失败: {e}")
            return False
    
    async def _update_async_database(
        self, 
        status: Optional[str] = None, 
        progress: Optional[int] = None, 
        message: Optional[str] = None,
        error: Optional[Union[str, Dict[str, Any]]] = None,
        **kwargs
    ) -> bool:
        """
        使用异步数据库更新任务状态
        
        Args:
            status: 任务状态
            progress: 进度百分比
            message: 进度消息
            error: 错误信息
            **kwargs: 附加数据
            
        Returns:
            是否成功
        """
        try:
            # 构建更新数据
            update_data = {}
            if status:
                update_data["status"] = status
            if progress is not None:
                update_data["progress"] = progress
            if status == "running" and "started_at" not in kwargs:
                update_data["started_at"] = datetime.utcnow()
            elif status in ["completed", "failed"] and "completed_at" not in kwargs:
                update_data["completed_at"] = datetime.utcnow()
            
            # 处理错误信息 - 确保它是字符串
            if error:
                if isinstance(error, dict):
                    try:
                        import json
                        update_data["error"] = json.dumps(error)
                    except Exception as e:
                        logger.warning(f"错误信息序列化失败: {e}")
                        update_data["error"] = str(error)
                else:
                    update_data["error"] = str(error)

            # 处理 input_data 和 output_data，确保为 JSON 字符串
            input_data = kwargs.get("input_data")
            if input_data is not None:
                import json
                update_data["input_data"] = json.dumps(input_data) if isinstance(input_data, dict) else input_data

            output_data = {}
            for key, value in kwargs.items():
                if key in ["started_at", "completed_at", "input_data"]:
                    continue  # 已处理
                else:
                    output_data[key] = value
            if output_data:
                import json
                update_data["output_data"] = json.dumps(output_data) if isinstance(output_data, dict) else output_data
            if not update_data:
                return True
            async with get_async_session() as session:
                # 更新任务表
                if update_data:
                    set_clause = ", ".join([f"{k} = :{k}" for k in update_data.keys()])
                    query = f"UPDATE tasks SET {set_clause} WHERE task_id = :task_id"
                    params = {**update_data, "task_id": self.task_id}
                    await session.execute(text(query), params)
                # 添加日志
                if message:
                    log_type = "error" if status == "failed" else "info"
                    await session.execute(
                        text("INSERT INTO task_logs (task_id, log_type, message, created_at) VALUES (:task_id, :log_type, :message, :created_at)"),
                        {"task_id": self.task_id, "log_type": log_type, "message": message, "created_at": datetime.utcnow()}
                    )
                await session.commit()
            logger.debug(f"已更新任务 {self.task_id} 的状态: {status}, 进度: {progress}%")
            return True
        except Exception as e:
            logger.error(f"异步更新数据库失败: {e}")
            return False
    
    async def _update_sync_database(
        self, 
        status: Optional[str] = None, 
        progress: Optional[int] = None, 
        message: Optional[str] = None,
        error: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> bool:
        """
        使用同步方式更新数据库中的任务状态
        
        Args:
            status: 任务状态
            progress: 进度百分比
            message: 进度消息
            error: 错误信息
            **kwargs: 附加数据
            
        Returns:
            是否成功
        """
        try:
            # 导入同步数据库模块
            from utils.db import get_db
            from models.task import TaskModel
            
            db = next(get_db())
            try:
                # 查询任务 - 使用task_id字段进行查询，而不是id字段
                task_id_str = str(self.task_id)  # 确保将任务ID转换为字符串
                task = db.query(TaskModel).filter(TaskModel.task_id == task_id_str).first()
                
                if task:
                    # 如果任务存在，更新它
                    if status:
                        task.status = status
                    if progress is not None:
                        task.progress = progress
                    
                    # 处理特殊字段
                    if "started_at" in kwargs and not task.started_at:
                        task.started_at = kwargs["started_at"]
                    if "completed_at" in kwargs and not task.completed_at:
                        task.completed_at = kwargs["completed_at"]
                    if "task_type" in kwargs and not task.task_type:
                        task.task_type = kwargs["task_type"]
                    
                    # 更新输入/输出数据
                    if task.input_data is None:
                        task.input_data = {}
                    if task.output_data is None:
                        task.output_data = {}
                    
                    # 合并新的输入/输出数据
                    if "input_data" in kwargs and kwargs["input_data"]:
                        if isinstance(task.input_data, dict) and isinstance(kwargs["input_data"], dict):
                            task.input_data.update(kwargs["input_data"])
                    if "output_data" in kwargs and kwargs["output_data"]:
                        if isinstance(task.output_data, dict) and isinstance(kwargs["output_data"], dict):
                            task.output_data.update(kwargs["output_data"])
                    
                    # 处理错误信息
                    if error:
                        if task.error is None:
                            task.error = {}
                        if isinstance(task.error, dict):
                            task.error.update(error)
                    
                    # 处理文件路径
                    if "input_files" in kwargs and kwargs["input_files"]:
                        task.input_files = kwargs["input_files"]
                    if "output_files" in kwargs and kwargs["output_files"]:
                        task.output_files = kwargs["output_files"]
                    
                    # 处理其他元数据
                    if "metadata" in kwargs and kwargs["metadata"]:
                        metadata = kwargs["metadata"]
                        if isinstance(metadata, dict):
                            if task.input_data is None:
                                task.input_data = {}
                            task.input_data.update(metadata)
                    
                    # 为消息创建任务日志
                    if message:
                        from models.task import TaskLog
                        log = TaskLog(
                            task_id=self.task_id,
                            log_type="info",
                            message=message
                        )
                        db.add(log)
                else:
                    # 如果任务不存在，创建新任务
                    task_data = {
                        "task_id": self.task_id,  # 设置task_id字段而不是id字段
                        "status": status or "pending",
                        "progress": progress or 0
                    }
                    
                    # 添加必要字段
                    if "task_type" in kwargs:
                        task_data["task_type"] = kwargs["task_type"]
                    else:
                        task_data["task_type"] = "unknown"  # 必须提供任务类型
                    
                    # 处理用户ID
                    if "user_id" in kwargs:
                        task_data["user_id"] = kwargs["user_id"]
                    
                    # 处理数字人ID
                    if "digital_human_id" in kwargs:
                        task_data["digital_human_id"] = kwargs["digital_human_id"]
                    
                    # 处理Celery任务ID
                    if "celery_task_id" in kwargs:
                        task_data["celery_task_id"] = kwargs["celery_task_id"]
                    
                    # 处理时间戳
                    if "created_at" in kwargs:
                        task_data["created_at"] = kwargs["created_at"]
                    if "started_at" in kwargs:
                        task_data["started_at"] = kwargs["started_at"]
                    if "completed_at" in kwargs:
                        task_data["completed_at"] = kwargs["completed_at"]
                    
                    # 处理输入/输出数据
                    input_data = {}
                    if "input_data" in kwargs and kwargs["input_data"]:
                        input_data.update(kwargs["input_data"])
                    if "metadata" in kwargs and kwargs["metadata"]:
                        input_data.update(kwargs["metadata"])
                    if input_data:
                        task_data["input_data"] = input_data
                    
                    if "output_data" in kwargs and kwargs["output_data"]:
                        task_data["output_data"] = kwargs["output_data"]
                    
                    if error:
                        task_data["error"] = error
                    
                    # 处理文件路径
                    if "input_files" in kwargs and kwargs["input_files"]:
                        task_data["input_files"] = kwargs["input_files"]
                    if "output_files" in kwargs and kwargs["output_files"]:
                        task_data["output_files"] = kwargs["output_files"]
                    
                    # 创建任务
                    new_task = TaskModel(**task_data)
                    db.add(new_task)
                    
                    # 为消息创建任务日志
                    if message:
                        from models.task import TaskLog
                        log = TaskLog(
                            task_id=self.task_id,
                            log_type="info",
                            message=message
                        )
                        db.add(log)
                
                # 提交更改
                db.commit()
                return True
            except Exception as e:
                db.rollback()
                logger.error(f"同步更新数据库失败: {e}")
                return False
            finally:
                db.close()
        except Exception as e:
            logger.error(f"同步数据库更新失败: {e}")
            return False
    
    async def _send_realtime_update(
        self, 
        status: Optional[str] = None, 
        progress: Optional[int] = None, 
        message: Optional[str] = None,
        error: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> bool:
        """
        发送实时更新
        
        Args:
            status: 任务状态
            progress: 进度百分比
            message: 进度消息
            error: 错误信息
            **kwargs: 附加数据
            
        Returns:
            是否成功
        """
        try:
            # 获取WebSocket管理器
            ws_manager = await self._get_websocket_manager()
            
            if not ws_manager:
                # WebSocket不可用，跳过
                return False
            
            # 构建更新消息
            update_data = {
                "task_id": self.task_id,
                "event": "task_update",
                "timestamp": datetime.utcnow().isoformat()
            }
            
            if status:
                update_data["status"] = status
            if progress is not None:
                update_data["progress"] = progress
            if message:
                update_data["message"] = message
            if error:
                update_data["error"] = error
                
            # 添加其他数据
            for key, value in kwargs.items():
                update_data[key] = value
            
            # 发送更新
            await ws_manager.send_task_update(self.task_id, update_data)
            
            return True
        except Exception as e:
            logger.warning(f"发送实时更新失败: {e}")
            return False
    
    async def log(self, message: str, log_type: str = "info") -> bool:
        """
        添加任务日志
        
        Args:
            message: 日志消息
            log_type: 日志类型 (info, warning, error)
            
        Returns:
            是否成功
        """
        try:
            # 验证日志类型
            if log_type not in ["info", "warning", "error"]:
                log_type = "info"
            
            # 获取数据库锁
            db_lock = await self._get_lock("db")
            
            async with db_lock:
                # 更新数据库
                from utils.db import get_db
                from models.task import TaskLog
                
                db = next(get_db())
                try:
                    log = TaskLog(
                        task_id=self.task_id,
                        log_type=log_type,
                        message=message,
                        created_at=datetime.utcnow()
                    )
                    db.add(log)
                    db.commit()
                finally:
                    db.close()
            
            # 发送实时更新
            await self._send_realtime_update(
                message=message,
                log_type=log_type,
                log_timestamp=datetime.utcnow().isoformat()
            )
            
            return True
        except Exception as e:
            logger.error(f"添加任务日志失败: {e}")
            return False

    def set_task_id(self, task_id: str) -> None:
        """设置任务ID"""
        self.task_id = task_id 

# 全局ProgressUpdater实例缓存
_progress_updater_instances = {}

def get_progress_updater(task_id: str) -> ProgressUpdater:
    """
    获取指定任务的进度更新器实例
    
    Args:
        task_id: 任务ID
        
    Returns:
        ProgressUpdater实例
    """
    if task_id not in _progress_updater_instances:
        _progress_updater_instances[task_id] = ProgressUpdater(task_id)
    
    return _progress_updater_instances[task_id]

async def create_task(
    task_type: str, 
    status: str = "pending", 
    progress: int = 0, 
    message: str = "任务已创建", 
    user_id: Optional[int] = None,
    **kwargs
) -> Optional[str]:
    """
    创建新任务
    
    Args:
        task_type: 任务类型
        status: 初始状态
        progress: 初始进度
        message: 初始消息
        user_id: 用户ID
        **kwargs: 其他参数
        
    Returns:
        任务ID，如果失败则为None
    """
    try:
        # 生成唯一任务ID
        import uuid
        task_id = str(uuid.uuid4())
        
        # 创建进度更新器
        updater = get_progress_updater(task_id)
        
        # 更新数据库 - 确保task_id参数被正确添加
        success = await updater.update(
            task_type=task_type,
            status=status,
            progress=progress,
            message=message,
            user_id=user_id,
            created_at=datetime.utcnow(),
            task_id=task_id,  # 显式传递task_id
            **kwargs
        )
        
        if success:
            logger.info(f"创建任务成功: {task_id}, 类型: {task_type}")
            return task_id
        else:
            logger.error(f"创建任务失败: 类型: {task_type}")
            return None
    except Exception as e:
        logger.error(f"创建任务异常: {e}")
        return None 