<template>
  <div class="periodic-report">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1>定期简报</h1>
      <p>AI智能体自动生成舆情热点汇编，支持定时推送</p>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <el-row :gutter="24">
        <!-- 左侧：简报任务管理 -->
        <el-col :xs="24" :lg="16">
          <el-card>
            <template #header>
              <span>简报任务管理</span>
            </template>
            <!-- 创建新任务按钮 -->
            <div class="task-actions">
              <el-button type="primary" @click="showCreateModal = true">
                <el-icon><Plus /></el-icon>
                创建简报任务
              </el-button>
            </div>

            <!-- 任务列表 -->
            <div class="task-list">
              <el-table
                :data="reportTasks"
                v-loading="loading"
                style="width: 100%"
              >
                <el-table-column prop="name" label="任务名称" min-width="150">
                  <template #default="{ row }">
                    <div class="task-title">
                      {{ row.name }}
                      <el-tag :type="getStatusType(row.status)" size="small">
                        {{ getStatusText(row.status) }}
                      </el-tag>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="description" label="描述" min-width="200" />
                <el-table-column label="任务信息" min-width="250">
                  <template #default="{ row }">
                    <div class="task-meta">
                      <div><el-icon><Calendar /></el-icon> 周期：{{ row.cycle }}</div>
                      <div><el-icon><Message /></el-icon> 接收人：{{ row.recipients.length }}人</div>
                      <div><el-icon><Clock /></el-icon> 下次执行：{{ row.nextExecution }}</div>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="150">
                  <template #default="{ row }">
                    <el-button link type="primary" @click="viewReport(row)">查看</el-button>
                    <el-button link type="primary" @click="editTask(row)">编辑</el-button>
                    <el-popconfirm
                      title="确定要删除这个任务吗？"
                      @confirm="deleteTask(row.id)"
                    >
                      <template #reference>
                        <el-button link type="danger">删除</el-button>
                      </template>
                    </el-popconfirm>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-card>
        </el-col>

        <!-- 右侧：快速统计和最近简报 -->
        <el-col :xs="24" :lg="8">
          <!-- 统计卡片 -->
          <el-card class="stats-card">
            <template #header>
              <span>任务统计</span>
            </template>
            <el-row :gutter="16">
              <el-col :span="12">
                <div class="stat-item">
                  <div class="stat-value" style="color: #3f8600">{{ activeTasksCount }}</div>
                  <div class="stat-title">活跃任务</div>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="stat-item">
                  <div class="stat-value" style="color: #1890ff">{{ monthlyReportsCount }}</div>
                  <div class="stat-title">本月简报</div>
                </div>
              </el-col>
            </el-row>
          </el-card>

          <!-- 最近简报 -->
          <el-card class="recent-reports">
            <template #header>
              <span>最近简报</span>
            </template>
            <div class="recent-reports-list">
              <div v-for="item in recentReports" :key="item.id" class="report-item">
                <div class="report-info">
                  <div class="report-title">
                    <el-link @click="viewReport(item)">{{ item.title }}</el-link>
                  </div>
                  <div class="report-date">{{ item.createdAt }}</div>
                </div>
                <div class="report-actions">
                  <el-button link type="primary" @click="downloadReport(item)">下载</el-button>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 创建任务模态框 -->
    <el-dialog
      v-model="showCreateModal"
      title="创建简报任务"
      width="800px"
      @close="resetForm"
    >
      <el-form
        ref="formRef"
        :model="taskForm"
        :rules="formRules"
        label-position="top"
      >
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="任务名称" prop="name">
              <el-input v-model="taskForm.name" placeholder="请输入任务名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="生成周期" prop="cycle">
              <el-select v-model="taskForm.cycle" placeholder="选择生成周期">
                <el-option value="daily" label="每日" />
                <el-option value="weekly" label="每周" />
                <el-option value="monthly" label="每月" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="任务描述" prop="description">
          <el-input
            v-model="taskForm.description"
            type="textarea"
            placeholder="请描述简报的主要内容和要求"
            :rows="3"
          />
        </el-form-item>

        <el-form-item label="简报模板" prop="template">
          <el-select v-model="taskForm.template" placeholder="选择简报模板">
            <el-option value="standard" label="标准模板" />
            <el-option value="detailed" label="详细模板" />
            <el-option value="brief" label="简要模板" />
          </el-select>
        </el-form-item>

        <el-form-item label="参考来源" prop="sources">
          <el-checkbox-group v-model="taskForm.sources">
            <el-checkbox value="news" label="舆情站点" />
            <el-checkbox value="search" label="联网搜索" />
            <el-checkbox value="knowledge" label="知识库" />
          </el-checkbox-group>
        </el-form-item>

        <el-form-item label="邮件接收人" prop="recipients">
          <el-select
            v-model="taskForm.recipients"
            multiple
            filterable
            allow-create
            placeholder="输入邮箱地址"
          />
        </el-form-item>

        <el-form-item label="执行时间" prop="executionTime">
          <el-time-picker
            v-model="taskForm.executionTime"
            format="HH:mm"
            placeholder="选择执行时间"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="resetForm">取消</el-button>
          <el-button type="primary" @click="createTask">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, computed, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import dayjs from 'dayjs';
import {
  Plus,
  Calendar,
  Message,
  Clock
} from '@element-plus/icons-vue';

export default defineComponent({
  name: 'PeriodicReport',
  components: {
    Plus,
    Calendar,
    Message,
    Clock
  },
  setup() {
    const loading = ref(false);
    const showCreateModal = ref(false);
    const formRef = ref();

    // 任务表单
    const taskForm = reactive({
      name: '',
      description: '',
      cycle: 'weekly',
      template: 'standard',
      sources: ['news', 'search'],
      recipients: [],
      executionTime: null
    });

    // 表单验证规则
    const formRules = {
      name: [{ required: true, message: '请输入任务名称' }],
      description: [{ required: true, message: '请输入任务描述' }],
      cycle: [{ required: true, message: '请选择生成周期' }],
      template: [{ required: true, message: '请选择简报模板' }],
      sources: [{ required: true, message: '请选择至少一个参考来源' }],
      recipients: [{ required: true, message: '请添加至少一个接收人' }],
      executionTime: [{ required: true, message: '请选择执行时间' }]
    };

    // 模拟数据
    const reportTasks = ref([
      {
        id: 1,
        name: '东南亚地区舆情周报',
        description: '监测东南亚地区政治、经济、社会热点事件',
        cycle: '每周',
        status: 'active',
        recipients: ['<EMAIL>', '<EMAIL>'],
        nextExecution: '2024-01-15 09:00',
        createdAt: '2024-01-01'
      },
      {
        id: 2,
        name: '中美关系舆情日报',
        description: '跟踪中美关系相关的舆情动态',
        cycle: '每日',
        status: 'active',
        recipients: ['<EMAIL>'],
        nextExecution: '2024-01-08 08:00',
        createdAt: '2024-01-01'
      }
    ]);

    const recentReports = ref([
      {
        id: 1,
        title: '东南亚地区舆情周报 - 第1期',
        createdAt: '2024-01-07 09:00',
        taskId: 1
      },
      {
        id: 2,
        title: '中美关系舆情日报 - 1月6日',
        createdAt: '2024-01-06 08:00',
        taskId: 2
      }
    ]);

    // 计算属性
    const activeTasksCount = computed(() => {
      return reportTasks.value.filter(task => task.status === 'active').length;
    });

    const monthlyReportsCount = computed(() => {
      return recentReports.value.length;
    });

    // 方法
    const getStatusType = (status) => {
      const types = {
        active: 'success',
        paused: 'warning',
        stopped: 'danger'
      };
      return types[status] || '';
    };

    const getStatusText = (status) => {
      const texts = {
        active: '运行中',
        paused: '已暂停',
        stopped: '已停止'
      };
      return texts[status] || '未知';
    };

    const createTask = async () => {
      try {
        await formRef.value.validate();
        
        // 模拟创建任务
        const newTask = {
          id: Date.now(),
          name: taskForm.name,
          description: taskForm.description,
          cycle: taskForm.cycle === 'daily' ? '每日' : taskForm.cycle === 'weekly' ? '每周' : '每月',
          status: 'active',
          recipients: taskForm.recipients,
          nextExecution: dayjs().add(1, 'day').format('YYYY-MM-DD HH:mm'),
          createdAt: dayjs().format('YYYY-MM-DD')
        };

        reportTasks.value.unshift(newTask);
        ElMessage.success('简报任务创建成功！');
        showCreateModal.value = false;
        resetForm();
      } catch (error) {
        console.error('表单验证失败:', error);
      }
    };

    const resetForm = () => {
      Object.assign(taskForm, {
        name: '',
        description: '',
        cycle: 'weekly',
        template: 'standard',
        sources: ['news', 'search'],
        recipients: [],
        executionTime: null
      });
    };

    const editTask = (task) => {
      ElMessage.info('编辑功能开发中');
    };

    const deleteTask = (taskId) => {
      const index = reportTasks.value.findIndex(task => task.id === taskId);
      if (index > -1) {
        reportTasks.value.splice(index, 1);
        ElMessage.success('任务删除成功');
      }
    };

    const viewReport = (item) => {
      ElMessage.info('查看简报功能开发中');
    };

    const downloadReport = (item) => {
      ElMessage.info('下载简报功能开发中');
    };

    onMounted(() => {
      // 初始化数据
    });

    return {
      loading,
      showCreateModal,
      formRef,
      taskForm,
      formRules,
      reportTasks,
      recentReports,
      activeTasksCount,
      monthlyReportsCount,
      getStatusType,
      getStatusText,
      createTask,
      resetForm,
      editTask,
      deleteTask,
      viewReport,
      downloadReport
    };
  }
});
</script>

<style scoped>
.periodic-report {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 32px;
  margin-bottom: 24px;
  color: white;
  text-align: center;
}

.page-header h1 {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 8px;
  color: white;
}

.page-header p {
  font-size: 1.1rem;
  opacity: 0.9;
  margin: 0;
}

.task-actions {
  margin-bottom: 24px;
}

.task-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.task-description p {
  margin-bottom: 8px;
  color: #666;
}

.task-meta {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #999;
}

.task-meta span {
  display: flex;
  align-items: center;
  gap: 4px;
}

.stats-card {
  margin-bottom: 24px;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 4px;
}

.stat-title {
  font-size: 14px;
  color: #666;
}

.recent-reports {
  max-height: 400px;
  overflow-y: auto;
}

.recent-reports-list {
  max-height: 300px;
  overflow-y: auto;
}

.report-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.report-item:last-child {
  border-bottom: none;
}

.report-info {
  flex: 1;
}

.report-title {
  font-weight: 500;
  margin-bottom: 4px;
}

.report-date {
  font-size: 12px;
  color: #999;
}

.report-actions {
  margin-left: 12px;
}

@media (max-width: 768px) {
  .periodic-report {
    padding: 16px;
  }
  
  .page-header {
    padding: 24px 16px;
  }
  
  .task-meta {
    flex-direction: column;
    gap: 8px;
  }
}
</style>
