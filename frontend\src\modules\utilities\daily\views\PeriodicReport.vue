<template>
  <div class="periodic-report">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1>定期简报</h1>
      <p>AI智能体自动生成舆情热点汇编，支持定时推送</p>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <el-row :gutter="24">
        <!-- 左侧：简报任务管理 -->
        <el-col :xs="24" :lg="16">
          <el-card>
            <template #header>
              <span>简报任务管理</span>
            </template>
            <!-- 创建新任务按钮 -->
            <div class="task-actions">
              <el-button type="primary" @click="showCreateModal = true">
                <el-icon><Plus /></el-icon>
                创建简报任务
              </el-button>
            </div>

            <!-- 任务列表 -->
            <div class="task-list">
              <el-table
                :data="reportTasks"
                v-loading="loading"
                style="width: 100%"
              >
                <template #empty>
                  <div class="empty-state">
                    <el-icon class="empty-icon"><Box /></el-icon>
                    <p>暂无简报任务</p>
                    <p class="empty-tip">点击"创建简报任务"开始使用</p>
                  </div>
                </template>
                <el-table-column prop="name" label="任务名称" min-width="150">
                  <template #default="{ row }">
                    <div class="task-title">
                      {{ row.name }}
                      <el-tag :type="getStatusType(row.status)" size="small">
                        {{ getStatusText(row.status) }}
                      </el-tag>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="description" label="描述" min-width="200" />
                <el-table-column label="任务信息" min-width="250">
                  <template #default="{ row }">
                    <div class="task-meta">
                      <div><el-icon><Calendar /></el-icon> 周期：{{ row.cycle || '未设置' }}</div>
                      <div><el-icon><Message /></el-icon> 接收人：{{ (row.recipients && Array.isArray(row.recipients)) ? row.recipients.length : 0 }}人</div>
                      <div><el-icon><Clock /></el-icon> 下次执行：{{ row.nextExecution || row.next_execution || '未设置' }}</div>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="150">
                  <template #default="{ row }">
                    <el-button link type="primary" @click="viewReport(row)">查看</el-button>
                    <el-button link type="primary" @click="editTask(row)">编辑</el-button>
                    <el-popconfirm
                      title="确定要删除这个任务吗？"
                      @confirm="deleteTask(row.id)"
                    >
                      <template #reference>
                        <el-button link type="danger">删除</el-button>
                      </template>
                    </el-popconfirm>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-card>
        </el-col>

        <!-- 右侧：快速统计和最近简报 -->
        <el-col :xs="24" :lg="8">
          <!-- 统计卡片 -->
          <el-card class="stats-card">
            <template #header>
              <span>任务统计</span>
            </template>
            <el-row :gutter="16">
              <el-col :span="12">
                <div class="stat-item">
                  <div class="stat-value" style="color: #3f8600">{{ activeTasksCount }}</div>
                  <div class="stat-title">活跃任务</div>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="stat-item">
                  <div class="stat-value" style="color: #1890ff">{{ monthlyReportsCount }}</div>
                  <div class="stat-title">本月简报</div>
                </div>
              </el-col>
            </el-row>
          </el-card>

          <!-- 最近简报 -->
          <el-card class="recent-reports">
            <template #header>
              <span>最近简报</span>
            </template>
            <div class="recent-reports-list">
              <div v-for="item in recentReports" :key="item.id" class="report-item">
                <div class="report-info">
                  <div class="report-title">
                    <el-link @click="viewReport(item)">{{ item.title }}</el-link>
                  </div>
                  <div class="report-date">{{ item.createdAt }}</div>
                </div>
                <div class="report-actions">
                  <el-button link type="primary" @click="downloadReport(item)">下载</el-button>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 创建任务模态框 -->
    <el-dialog
      v-model="showCreateModal"
      title="创建简报任务"
      width="800px"
      @close="resetForm"
    >
      <el-form
        ref="formRef"
        :model="taskForm"
        :rules="formRules"
        label-position="top"
      >
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="任务名称" prop="name">
              <el-input v-model="taskForm.name" placeholder="请输入任务名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="生成周期" prop="cycle">
              <el-select v-model="taskForm.cycle" placeholder="选择生成周期">
                <el-option value="daily" label="每日" />
                <el-option value="weekly" label="每周" />
                <el-option value="monthly" label="每月" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="任务描述" prop="description">
          <el-input
            v-model="taskForm.description"
            type="textarea"
            placeholder="请描述简报的主要内容和要求"
            :rows="3"
          />
        </el-form-item>

        <el-form-item label="简报模板" prop="template">
          <el-select v-model="taskForm.template" placeholder="选择简报模板">
            <el-option value="standard" label="标准模板" />
            <el-option value="detailed" label="详细模板" />
            <el-option value="brief" label="简要模板" />
          </el-select>
        </el-form-item>

        <el-form-item label="参考来源" prop="sources">
          <el-checkbox-group v-model="taskForm.sources">
            <el-checkbox value="news" label="舆情站点" />
            <el-checkbox value="search" label="联网搜索" />
            <el-checkbox value="knowledge" label="知识库" />
          </el-checkbox-group>
        </el-form-item>

        <el-form-item label="邮件接收人" prop="recipients">
          <el-select
            v-model="taskForm.recipients"
            multiple
            filterable
            allow-create
            placeholder="输入邮箱地址"
          />
        </el-form-item>

        <el-form-item label="执行时间" prop="executionTime">
          <el-time-picker
            v-model="taskForm.executionTime"
            format="HH:mm"
            placeholder="选择执行时间"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="resetForm">取消</el-button>
          <el-button type="primary" @click="createTask">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, computed, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import dayjs from 'dayjs';
import {
  Plus,
  Calendar,
  Message,
  Clock,
  Box
} from '@element-plus/icons-vue';
import opinionAnalysisApi from '@/services/opinion-analysis.js';

export default defineComponent({
  name: 'PeriodicReport',
  components: {
    Plus,
    Calendar,
    Message,
    Clock,
    Box
  },
  setup() {
    const loading = ref(false);
    const showCreateModal = ref(false);
    const formRef = ref();

    // 任务表单
    const taskForm = reactive({
      name: '',
      description: '',
      cycle: 'weekly',
      template: 'standard',
      sources: ['news', 'search'],
      recipients: [],
      executionTime: null
    });

    // 表单验证规则
    const formRules = {
      name: [
        { required: true, message: '请输入任务名称', trigger: 'blur' },
        { min: 2, max: 50, message: '任务名称长度在 2 到 50 个字符', trigger: 'blur' }
      ],
      description: [
        { required: true, message: '请输入任务描述', trigger: 'blur' },
        { min: 5, max: 200, message: '任务描述长度在 5 到 200 个字符', trigger: 'blur' }
      ],
      cycle: [
        { required: true, message: '请选择生成周期', trigger: 'change' }
      ],
      template: [
        { required: true, message: '请选择简报模板', trigger: 'change' }
      ],
      sources: [
        { type: 'array', required: true, message: '请选择至少一个参考来源', trigger: 'change' }
      ],
      recipients: [
        { type: 'array', required: true, message: '请添加至少一个接收人', trigger: 'change' }
      ],
      executionTime: [
        { required: true, message: '请选择执行时间', trigger: 'change' }
      ]
    };

    // 数据 - 确保初始化为数组
    const reportTasks = ref([]);
    const recentReports = ref([]);

    // 计算属性
    const activeTasksCount = computed(() => {
      return reportTasks.value.filter(task => task.status === 'active').length;
    });

    const monthlyReportsCount = computed(() => {
      return recentReports.value.length;
    });

    // 数据加载函数
    const loadReportTasks = async () => {
      try {
        loading.value = true;
        const response = await opinionAnalysisApi.reports.getReports();

        // 确保数据是数组格式
        const data = response.data || response || [];
        reportTasks.value = Array.isArray(data) ? data : [];

        console.log('API响应:', response);
        console.log('处理后的简报任务:', reportTasks.value);
        console.log('数据类型检查:', typeof reportTasks.value, Array.isArray(reportTasks.value));
      } catch (error) {
        console.error('加载简报任务失败:', error);
        // 使用模拟数据作为备用
        reportTasks.value = [
          {
            id: 1,
            name: '东南亚地区舆情周报',
            description: '监测东南亚地区政治、经济、社会热点事件',
            cycle: 'weekly',
            status: 'active',
            recipients: ['<EMAIL>', '<EMAIL>'],
            nextExecution: '2024-01-15 09:00',
            createdAt: '2024-01-01'
          },
          {
            id: 2,
            name: '中美关系舆情日报',
            description: '跟踪中美关系相关的舆情动态',
            cycle: 'daily',
            status: 'active',
            recipients: ['<EMAIL>'],
            nextExecution: '2024-01-08 08:00',
            createdAt: '2024-01-01'
          }
        ];
        ElMessage.warning('使用演示数据，后台服务连接失败');
      } finally {
        loading.value = false;
      }
    };

    const loadRecentReports = async () => {
      try {
        // 获取所有简报任务的最近实例
        let allInstances = [];

        // 遍历所有简报任务，获取它们的实例
        for (const task of reportTasks.value) {
          try {
            const response = await opinionAnalysisApi.reports.getReportInstances(task.id);
            if (response.data && response.data.length > 0) {
              // 只取每个任务的最新实例
              allInstances.push(response.data[0]);
            }
          } catch (err) {
            console.warn(`获取任务${task.id}的实例失败:`, err);
          }
        }

        // 按创建时间排序，取最新的几个
        recentReports.value = allInstances
          .sort((a, b) => new Date(b.createdAt || b.created_at) - new Date(a.createdAt || a.created_at))
          .slice(0, 5);

        console.log('加载的最近简报:', recentReports.value);

      } catch (error) {
        console.error('加载最近简报失败:', error);
        // 使用模拟数据作为备用
        recentReports.value = [
          {
            id: 1,
            title: '东南亚地区舆情周报 - 第1期',
            createdAt: '2024-01-07 09:00',
            taskId: 1
          },
          {
            id: 2,
            title: '中美关系舆情日报 - 1月6日',
            createdAt: '2024-01-06 08:00',
            taskId: 2
          }
        ];
      }
    };

    // 方法
    const getStatusType = (status) => {
      const types = {
        active: 'success',
        paused: 'warning',
        stopped: 'danger'
      };
      return types[status] || '';
    };

    const getStatusText = (status) => {
      const texts = {
        active: '运行中',
        paused: '已暂停',
        stopped: '已停止'
      };
      return texts[status] || '未知';
    };

    const createTask = async () => {
      try {
        await formRef.value.validate();

        loading.value = true;

        // 准备API数据
        const taskData = {
          name: taskForm.name,
          description: taskForm.description,
          cycle: taskForm.cycle,
          template_type: taskForm.template,
          sources: taskForm.sources,
          recipients: taskForm.recipients,
          execution_time: taskForm.executionTime ? dayjs(taskForm.executionTime).format('HH:mm') : null,
          keywords: [], // 可以后续添加关键词配置
          language: 'zh'
        };

        try {
          // 尝试调用API创建任务
          const response = await opinionAnalysisApi.reports.createReport(taskData);

          if (response.success) {
            ElMessage.success('简报任务创建成功！');
            showCreateModal.value = false;
            resetForm();
            // 重新加载任务列表
            await loadReportTasks();
          } else {
            ElMessage.error(response.message || '创建任务失败');
          }
        } catch (apiError) {
          console.error('API调用失败，使用演示模式:', apiError);

          // 演示模式：添加到本地数据
          const newTask = {
            id: Date.now(),
            name: taskForm.name,
            description: taskForm.description,
            cycle: taskForm.cycle,
            status: 'active',
            recipients: taskForm.recipients,
            nextExecution: dayjs().add(1, 'day').format('YYYY-MM-DD HH:mm'),
            createdAt: dayjs().format('YYYY-MM-DD')
          };

          reportTasks.value.unshift(newTask);
          ElMessage.success('简报任务创建成功！（演示模式）');
          showCreateModal.value = false;
          resetForm();
        }
      } catch (error) {
        console.error('表单验证失败:', error);
        ElMessage.error('请检查表单输入');
      } finally {
        loading.value = false;
      }
    };

    const resetForm = () => {
      Object.assign(taskForm, {
        name: '',
        description: '',
        cycle: 'weekly',
        template: 'standard',
        sources: ['news', 'search'],
        recipients: [],
        executionTime: null
      });
    };

    const editTask = (task) => {
      // 填充表单数据
      Object.assign(taskForm, {
        name: task.name,
        description: task.description,
        cycle: task.cycle,
        template: task.template_type || 'standard',
        sources: task.sources || ['news', 'search'],
        recipients: task.recipients || [],
        executionTime: task.execution_time ? dayjs(`2024-01-01 ${task.execution_time}`).toDate() : null
      });
      showCreateModal.value = true;
    };

    const deleteTask = async (taskId) => {
      try {
        const response = await opinionAnalysisApi.reports.deleteReport(taskId);
        if (response.success) {
          ElMessage.success('任务删除成功');
          await loadReportTasks();
        } else {
          ElMessage.error(response.message || '删除任务失败');
        }
      } catch (error) {
        console.error('删除任务失败:', error);
        ElMessage.error('删除任务失败，请稍后重试');
      }
    };

    const viewReport = async (item) => {
      try {
        const response = await opinionAnalysisApi.reports.getReport(item.id);
        if (response.success) {
          // 可以打开一个新的页面或模态框显示简报详情
          ElMessage.success('简报详情加载成功');
          console.log('简报详情:', response.data);
        } else {
          ElMessage.error('加载简报详情失败');
        }
      } catch (error) {
        console.error('查看简报失败:', error);
        ElMessage.error('查看简报失败，请稍后重试');
      }
    };

    const downloadReport = (item) => {
      // 创建下载链接
      const downloadUrl = `/api/v1/opinion/reports/${item.id}/download`;
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = `${item.title || '简报'}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      ElMessage.success('开始下载简报');
    };

    onMounted(async () => {
      // 先加载简报任务，再加载最近简报（因为最近简报依赖任务数据）
      await loadReportTasks();
      await loadRecentReports();
    });

    return {
      loading,
      showCreateModal,
      formRef,
      taskForm,
      formRules,
      reportTasks,
      recentReports,
      activeTasksCount,
      monthlyReportsCount,
      getStatusType,
      getStatusText,
      createTask,
      resetForm,
      editTask,
      deleteTask,
      viewReport,
      downloadReport,
      loadReportTasks,
      loadRecentReports
    };
  }
});
</script>

<style scoped>
.periodic-report {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 32px;
  margin-bottom: 24px;
  color: white;
  text-align: center;
}

.page-header h1 {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 8px;
  color: white;
}

.page-header p {
  font-size: 1.1rem;
  opacity: 0.9;
  margin: 0;
}

.task-actions {
  margin-bottom: 24px;
}

.task-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.task-description p {
  margin-bottom: 8px;
  color: #666;
}

.task-meta {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #999;
}

.task-meta span {
  display: flex;
  align-items: center;
  gap: 4px;
}

.stats-card {
  margin-bottom: 24px;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 4px;
}

.stat-title {
  font-size: 14px;
  color: #666;
}

.recent-reports {
  max-height: 400px;
  overflow-y: auto;
}

.recent-reports-list {
  max-height: 300px;
  overflow-y: auto;
}

.report-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.report-item:last-child {
  border-bottom: none;
}

.report-info {
  flex: 1;
}

.report-title {
  font-weight: 500;
  margin-bottom: 4px;
}

.report-date {
  font-size: 12px;
  color: #999;
}

.report-actions {
  margin-left: 12px;
}

.empty-state {
  padding: 40px 20px;
  text-align: center;
  color: #909399;
}

.empty-state .empty-icon {
  font-size: 48px;
  color: #c0c4cc;
  margin-bottom: 16px;
}

.empty-state p {
  margin: 8px 0;
}

.empty-tip {
  font-size: 12px;
  color: #c0c4cc;
}

@media (max-width: 768px) {
  .periodic-report {
    padding: 16px;
  }
  
  .page-header {
    padding: 24px 16px;
  }
  
  .task-meta {
    flex-direction: column;
    gap: 8px;
  }
}
</style>
