<template>
  <div class="periodic-report">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1>定期简报</h1>
      <p>AI智能体自动生成舆情热点汇编，支持定时推送</p>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <a-row :gutter="[24, 24]">
        <!-- 左侧：简报任务管理 -->
        <a-col :xs="24" :lg="16">
          <a-card title="简报任务管理" :bordered="false">
            <!-- 创建新任务按钮 -->
            <div class="task-actions">
              <a-button type="primary" @click="showCreateModal = true">
                <plus-outlined />
                创建简报任务
              </a-button>
            </div>

            <!-- 任务列表 -->
            <div class="task-list">
              <a-list
                :data-source="reportTasks"
                :loading="loading"
              >
                <template #renderItem="{ item }">
                  <a-list-item>
                    <template #actions>
                      <a @click="viewReport(item)">查看</a>
                      <a @click="editTask(item)">编辑</a>
                      <a-popconfirm
                        title="确定要删除这个任务吗？"
                        @confirm="deleteTask(item.id)"
                      >
                        <a style="color: #ff4d4f;">删除</a>
                      </a-popconfirm>
                    </template>
                    
                    <a-list-item-meta>
                      <template #title>
                        <div class="task-title">
                          {{ item.name }}
                          <a-tag :color="getStatusColor(item.status)">
                            {{ getStatusText(item.status) }}
                          </a-tag>
                        </div>
                      </template>
                      <template #description>
                        <div class="task-description">
                          <p>{{ item.description }}</p>
                          <div class="task-meta">
                            <span><calendar-outlined /> 周期：{{ item.cycle }}</span>
                            <span><mail-outlined /> 接收人：{{ item.recipients.length }}人</span>
                            <span><clock-circle-outlined /> 下次执行：{{ item.nextExecution }}</span>
                          </div>
                        </div>
                      </template>
                    </a-list-item-meta>
                  </a-list-item>
                </template>
              </a-list>
            </div>
          </a-card>
        </a-col>

        <!-- 右侧：快速统计和最近简报 -->
        <a-col :xs="24" :lg="8">
          <!-- 统计卡片 -->
          <a-card title="任务统计" :bordered="false" class="stats-card">
            <a-row :gutter="16">
              <a-col :span="12">
                <a-statistic
                  title="活跃任务"
                  :value="activeTasksCount"
                  :value-style="{ color: '#3f8600' }"
                />
              </a-col>
              <a-col :span="12">
                <a-statistic
                  title="本月简报"
                  :value="monthlyReportsCount"
                  :value-style="{ color: '#1890ff' }"
                />
              </a-col>
            </a-row>
          </a-card>

          <!-- 最近简报 -->
          <a-card title="最近简报" :bordered="false" class="recent-reports">
            <a-list
              :data-source="recentReports"
              size="small"
            >
              <template #renderItem="{ item }">
                <a-list-item>
                  <template #actions>
                    <a @click="downloadReport(item)">下载</a>
                  </template>
                  <a-list-item-meta>
                    <template #title>
                      <a @click="viewReport(item)">{{ item.title }}</a>
                    </template>
                    <template #description>
                      {{ item.createdAt }}
                    </template>
                  </a-list-item-meta>
                </a-list-item>
              </template>
            </a-list>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 创建任务模态框 -->
    <a-modal
      v-model:open="showCreateModal"
      title="创建简报任务"
      width="800px"
      @ok="createTask"
      @cancel="resetForm"
    >
      <a-form
        ref="formRef"
        :model="taskForm"
        :rules="formRules"
        layout="vertical"
      >
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="任务名称" name="name">
              <a-input v-model:value="taskForm.name" placeholder="请输入任务名称" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="生成周期" name="cycle">
              <a-select v-model:value="taskForm.cycle" placeholder="选择生成周期">
                <a-select-option value="daily">每日</a-select-option>
                <a-select-option value="weekly">每周</a-select-option>
                <a-select-option value="monthly">每月</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item label="任务描述" name="description">
          <a-textarea 
            v-model:value="taskForm.description" 
            placeholder="请描述简报的主要内容和要求"
            :rows="3"
          />
        </a-form-item>

        <a-form-item label="简报模板" name="template">
          <a-select v-model:value="taskForm.template" placeholder="选择简报模板">
            <a-select-option value="standard">标准模板</a-select-option>
            <a-select-option value="detailed">详细模板</a-select-option>
            <a-select-option value="brief">简要模板</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="参考来源" name="sources">
          <a-checkbox-group v-model:value="taskForm.sources">
            <a-checkbox value="news">舆情站点</a-checkbox>
            <a-checkbox value="search">联网搜索</a-checkbox>
            <a-checkbox value="knowledge">知识库</a-checkbox>
          </a-checkbox-group>
        </a-form-item>

        <a-form-item label="邮件接收人" name="recipients">
          <a-select
            v-model:value="taskForm.recipients"
            mode="tags"
            placeholder="输入邮箱地址，按回车添加"
            :token-separators="[',', ';']"
          />
        </a-form-item>

        <a-form-item label="执行时间" name="executionTime">
          <a-time-picker
            v-model:value="taskForm.executionTime"
            format="HH:mm"
            placeholder="选择执行时间"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, computed, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';
import {
  PlusOutlined,
  CalendarOutlined,
  MailOutlined,
  ClockCircleOutlined
} from '@ant-design/icons-vue';

export default defineComponent({
  name: 'PeriodicReport',
  components: {
    PlusOutlined,
    CalendarOutlined,
    MailOutlined,
    ClockCircleOutlined
  },
  setup() {
    const loading = ref(false);
    const showCreateModal = ref(false);
    const formRef = ref();

    // 任务表单
    const taskForm = reactive({
      name: '',
      description: '',
      cycle: 'weekly',
      template: 'standard',
      sources: ['news', 'search'],
      recipients: [],
      executionTime: null
    });

    // 表单验证规则
    const formRules = {
      name: [{ required: true, message: '请输入任务名称' }],
      description: [{ required: true, message: '请输入任务描述' }],
      cycle: [{ required: true, message: '请选择生成周期' }],
      template: [{ required: true, message: '请选择简报模板' }],
      sources: [{ required: true, message: '请选择至少一个参考来源' }],
      recipients: [{ required: true, message: '请添加至少一个接收人' }],
      executionTime: [{ required: true, message: '请选择执行时间' }]
    };

    // 模拟数据
    const reportTasks = ref([
      {
        id: 1,
        name: '东南亚地区舆情周报',
        description: '监测东南亚地区政治、经济、社会热点事件',
        cycle: '每周',
        status: 'active',
        recipients: ['<EMAIL>', '<EMAIL>'],
        nextExecution: '2024-01-15 09:00',
        createdAt: '2024-01-01'
      },
      {
        id: 2,
        name: '中美关系舆情日报',
        description: '跟踪中美关系相关的舆情动态',
        cycle: '每日',
        status: 'active',
        recipients: ['<EMAIL>'],
        nextExecution: '2024-01-08 08:00',
        createdAt: '2024-01-01'
      }
    ]);

    const recentReports = ref([
      {
        id: 1,
        title: '东南亚地区舆情周报 - 第1期',
        createdAt: '2024-01-07 09:00',
        taskId: 1
      },
      {
        id: 2,
        title: '中美关系舆情日报 - 1月6日',
        createdAt: '2024-01-06 08:00',
        taskId: 2
      }
    ]);

    // 计算属性
    const activeTasksCount = computed(() => {
      return reportTasks.value.filter(task => task.status === 'active').length;
    });

    const monthlyReportsCount = computed(() => {
      return recentReports.value.length;
    });

    // 方法
    const getStatusColor = (status) => {
      const colors = {
        active: 'green',
        paused: 'orange',
        stopped: 'red'
      };
      return colors[status] || 'default';
    };

    const getStatusText = (status) => {
      const texts = {
        active: '运行中',
        paused: '已暂停',
        stopped: '已停止'
      };
      return texts[status] || '未知';
    };

    const createTask = async () => {
      try {
        await formRef.value.validate();
        
        // 模拟创建任务
        const newTask = {
          id: Date.now(),
          name: taskForm.name,
          description: taskForm.description,
          cycle: taskForm.cycle === 'daily' ? '每日' : taskForm.cycle === 'weekly' ? '每周' : '每月',
          status: 'active',
          recipients: taskForm.recipients,
          nextExecution: dayjs().add(1, 'day').format('YYYY-MM-DD HH:mm'),
          createdAt: dayjs().format('YYYY-MM-DD')
        };

        reportTasks.value.unshift(newTask);
        message.success('简报任务创建成功！');
        showCreateModal.value = false;
        resetForm();
      } catch (error) {
        console.error('表单验证失败:', error);
      }
    };

    const resetForm = () => {
      Object.assign(taskForm, {
        name: '',
        description: '',
        cycle: 'weekly',
        template: 'standard',
        sources: ['news', 'search'],
        recipients: [],
        executionTime: null
      });
    };

    const editTask = (task) => {
      message.info('编辑功能开发中');
    };

    const deleteTask = (taskId) => {
      const index = reportTasks.value.findIndex(task => task.id === taskId);
      if (index > -1) {
        reportTasks.value.splice(index, 1);
        message.success('任务删除成功');
      }
    };

    const viewReport = (item) => {
      message.info('查看简报功能开发中');
    };

    const downloadReport = (item) => {
      message.info('下载简报功能开发中');
    };

    onMounted(() => {
      // 初始化数据
    });

    return {
      loading,
      showCreateModal,
      formRef,
      taskForm,
      formRules,
      reportTasks,
      recentReports,
      activeTasksCount,
      monthlyReportsCount,
      getStatusColor,
      getStatusText,
      createTask,
      resetForm,
      editTask,
      deleteTask,
      viewReport,
      downloadReport
    };
  }
});
</script>

<style scoped>
.periodic-report {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 32px;
  margin-bottom: 24px;
  color: white;
  text-align: center;
}

.page-header h1 {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 8px;
  color: white;
}

.page-header p {
  font-size: 1.1rem;
  opacity: 0.9;
  margin: 0;
}

.task-actions {
  margin-bottom: 24px;
}

.task-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.task-description p {
  margin-bottom: 8px;
  color: #666;
}

.task-meta {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #999;
}

.task-meta span {
  display: flex;
  align-items: center;
  gap: 4px;
}

.stats-card {
  margin-bottom: 24px;
}

.recent-reports {
  max-height: 400px;
  overflow-y: auto;
}

@media (max-width: 768px) {
  .periodic-report {
    padding: 16px;
  }
  
  .page-header {
    padding: 24px 16px;
  }
  
  .task-meta {
    flex-direction: column;
    gap: 8px;
  }
}
</style>
