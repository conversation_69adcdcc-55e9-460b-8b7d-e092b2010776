from sqlalchemy import Column, Integer, String, Text, ForeignKey, DateTime, Boolean
from sqlalchemy.sql import func
from pydantic import BaseModel, validator, Field
from typing import Optional, Dict, Any, Union
from datetime import datetime
import json

from utils.db import Base
from utils.model_helpers import safe_json_loads

class VideoGeneration(Base):
    """Video generation database model"""
    __tablename__ = "video_generations"

    id = Column(Integer, primary_key=True, index=True)
    task_id = Column(String, unique=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    prompt = Column(Text)
    negative_prompt = Column(Text, nullable=True)
    parameters = Column(Text)  # JSON string with generation parameters
    status = Column(String)  # pending, processing, completed, failed
    video_url = Column(String, nullable=True)
    thumbnail_url = Column(String, nullable=True)
    error_message = Column(Text, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    completed_at = Column(DateTime(timezone=True), nullable=True)
    is_public = Column(Boolean, default=False)

# Pydantic models for API
class VideoGenerationBase(BaseModel):
    prompt: str
    negative_prompt: Optional[str] = None

class VideoGenerationCreate(VideoGenerationBase):
    # 允许参数是字典或字符串
    parameters: Union[Dict[str, Any], str]
    
    @validator('parameters')
    def validate_parameters(cls, v):
        """确保parameters是字典类型"""
        if isinstance(v, str):
            try:
                # 尝试将字符串解析为JSON
                return json.loads(v)
            except json.JSONDecodeError as e:
                raise ValueError(f"无法解析parameters字符串: {e}")
        return v

class VideoGenerationResponse(VideoGenerationBase):
    id: int
    task_id: str
    user_id: int
    status: str
    video_url: Optional[str] = None
    thumbnail_url: Optional[str] = None
    error_message: Optional[str] = None
    created_at: datetime
    completed_at: Optional[datetime] = None
    is_public: bool
    parameters: Dict[str, Any]

    class Config:
        from_attributes = True

    @classmethod
    def from_orm(cls, obj):
        # Handle the parameters JSON conversion
        obj_dict = {c.name: getattr(obj, c.name) for c in obj.__table__.columns}
        
        # 使用安全的JSON解析函数处理parameters字段
        if obj_dict.get("parameters"):
            obj_dict["parameters"] = safe_json_loads(
                obj_dict["parameters"], 
                default_value={}, 
                expected_type=dict,
                field_name="parameters", 
                entity_id=obj_dict.get("task_id")
            )
        else:
            obj_dict["parameters"] = {}
            
        return cls(**obj_dict) 