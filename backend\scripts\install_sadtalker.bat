@echo off
echo 开始安装SadTalker及其依赖...

REM 创建第三方库目录
mkdir third_party 2>nul
cd third_party

REM 检查SadTalker是否已安装
if exist SadTalker (
    echo SadTalker目录已存在，跳过克隆步骤
) else (
    echo 克隆SadTalker仓库...
    git clone https://github.com/OpenTalker/SadTalker.git
    cd SadTalker

    REM 检查是否成功克隆
    if not exist .git (
        echo 错误: 无法克隆SadTalker仓库
        exit /b 1
    )

    REM 安装依赖
    echo 安装SadTalker依赖...
    pip install -r requirements.txt

    REM 下载预训练模型
    echo 下载预训练模型...
    call scripts\download_models.bat

    REM 返回上级目录
    cd ..
)

REM 添加环境变量到.env文件
echo 配置环境变量...
cd ..

set CURRENT_DIR=%cd%
set SADTALKER_PATH=%CURRENT_DIR%\third_party\SadTalker

if exist .env (
    REM 检查SADTALKER_PATH是否已存在
    findstr /C:"SADTALKER_PATH" .env >nul
    if %errorlevel% neq 0 (
        echo 添加SADTALKER_PATH环境变量到.env文件
        echo SADTALKER_PATH=%SADTALKER_PATH% >> .env
    ) else (
        echo SADTALKER_PATH环境变量已存在，跳过添加
    )
    
    REM 检查USE_AI_GENERATION是否已存在
    findstr /C:"USE_AI_GENERATION" .env >nul
    if %errorlevel% neq 0 (
        echo 添加USE_AI_GENERATION环境变量到.env文件
        echo USE_AI_GENERATION=true >> .env
    ) else (
        echo USE_AI_GENERATION环境变量已存在，跳过添加
    )
) else (
    echo 创建.env文件并添加环境变量
    echo SADTALKER_PATH=%SADTALKER_PATH% > .env
    echo USE_AI_GENERATION=true >> .env
)

REM 创建资源目录
echo 创建资源目录...
mkdir backend\assets\default_avatars 2>nul
mkdir backend\assets\models 2>nul

REM 确保default.glb文件存在
if not exist frontend\public\assets\models\default.glb (
    echo 找不到默认模型，正在创建空模型文件...
    echo This file simulates a 3D model in GLTF Binary format. In a production environment, this would be replaced with an actual 3D model. > frontend\public\assets\models\default.glb
)

echo SadTalker安装和配置完成！
echo 请运行以下命令来启动服务:
echo   cd backend
echo   uvicorn main:app --reload 