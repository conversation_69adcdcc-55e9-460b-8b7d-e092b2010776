"""
AI舆情分析服务 - 集成大模型实现智能分析
"""

import asyncio
import json
import openai
import anthropic
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import aiohttp
import re
from ..core.config import settings
import logging

logger = logging.getLogger(__name__)


class AIOpinionService:
    """AI舆情分析服务类"""
    
    def __init__(self):
        # 初始化AI客户端
        self.openai_client = openai.AsyncOpenAI(
            api_key=getattr(settings, 'OPENAI_API_KEY', ''),
            base_url=getattr(settings, 'OPENAI_BASE_URL', 'https://api.openai.com/v1')
        )
        
        self.anthropic_client = anthropic.AsyncAnthropic(
            api_key=getattr(settings, 'ANTHROPIC_API_KEY', '')
        )
        
        # 配置参数
        self.default_model = getattr(settings, 'DEFAULT_AI_MODEL', 'gpt-3.5-turbo')
        self.max_tokens = 4000
        self.temperature = 0.7
    
    async def generate_opinion_report(self, 
                                    title: str,
                                    keywords: List[str],
                                    raw_data: List[Dict],
                                    template_type: str = "standard",
                                    language: str = "zh") -> Dict[str, Any]:
        """
        生成智能舆情简报
        
        Args:
            title: 简报标题
            keywords: 关键词列表
            raw_data: 原始舆情数据
            template_type: 模板类型 (standard, detailed, brief)
            language: 语言 (zh, en)
        
        Returns:
            生成的简报内容
        """
        try:
            # 构建提示词
            prompt = self._build_report_prompt(title, keywords, raw_data, template_type, language)
            
            # 调用AI生成简报
            if self.default_model.startswith('gpt'):
                content = await self._generate_with_openai(prompt)
            elif self.default_model.startswith('claude'):
                content = await self._generate_with_anthropic(prompt)
            else:
                content = await self._generate_with_openai(prompt)  # 默认使用OpenAI
            
            # 解析生成的内容
            report = self._parse_report_content(content)
            
            # 添加元数据
            report.update({
                'generated_at': datetime.now().isoformat(),
                'model_used': self.default_model,
                'keywords': keywords,
                'data_count': len(raw_data),
                'template_type': template_type,
                'language': language
            })
            
            return report
            
        except Exception as e:
            logger.error(f"生成舆情简报失败: {str(e)}")
            raise Exception(f"AI简报生成失败: {str(e)}")
    
    async def analyze_sentiment_batch(self, texts: List[str]) -> List[Dict[str, Any]]:
        """
        批量情感分析
        
        Args:
            texts: 文本列表
        
        Returns:
            情感分析结果列表
        """
        try:
            results = []
            
            # 分批处理，避免token限制
            batch_size = 10
            for i in range(0, len(texts), batch_size):
                batch = texts[i:i + batch_size]
                batch_results = await self._analyze_sentiment_batch(batch)
                results.extend(batch_results)
            
            return results
            
        except Exception as e:
            logger.error(f"批量情感分析失败: {str(e)}")
            return [{'sentiment': 'neutral', 'score': 0.0, 'confidence': 0.0} for _ in texts]
    
    async def extract_hot_topics(self, texts: List[str], top_k: int = 10) -> List[Dict[str, Any]]:
        """
        提取热点话题
        
        Args:
            texts: 文本列表
            top_k: 返回前k个热点
        
        Returns:
            热点话题列表
        """
        try:
            # 构建热点提取提示词
            prompt = f"""
            请分析以下舆情文本，提取出最重要的{top_k}个热点话题。
            
            要求：
            1. 每个热点包含：话题名称、重要性评分(1-10)、相关文本数量、简要描述
            2. 按重要性排序
            3. 返回JSON格式
            
            文本内容：
            {json.dumps(texts[:50], ensure_ascii=False)}  # 限制输入长度
            
            请返回如下格式的JSON：
            {{
                "hot_topics": [
                    {{
                        "topic": "话题名称",
                        "importance": 9.5,
                        "count": 15,
                        "description": "话题描述"
                    }}
                ]
            }}
            """
            
            if self.default_model.startswith('gpt'):
                content = await self._generate_with_openai(prompt)
            else:
                content = await self._generate_with_anthropic(prompt)
            
            # 解析JSON结果
            try:
                result = json.loads(content)
                return result.get('hot_topics', [])
            except json.JSONDecodeError:
                # 如果JSON解析失败，尝试正则提取
                return self._extract_topics_from_text(content)
                
        except Exception as e:
            logger.error(f"热点话题提取失败: {str(e)}")
            return []
    
    async def generate_summary(self, text: str, max_length: int = 200) -> str:
        """
        生成文本摘要
        
        Args:
            text: 原文本
            max_length: 最大摘要长度
        
        Returns:
            摘要文本
        """
        try:
            prompt = f"""
            请为以下文本生成一个简洁的摘要，长度不超过{max_length}字：
            
            原文：
            {text}
            
            摘要要求：
            1. 保留核心信息
            2. 语言简洁明了
            3. 长度控制在{max_length}字以内
            """
            
            if self.default_model.startswith('gpt'):
                summary = await self._generate_with_openai(prompt)
            else:
                summary = await self._generate_with_anthropic(prompt)
            
            return summary.strip()
            
        except Exception as e:
            logger.error(f"摘要生成失败: {str(e)}")
            return text[:max_length] + "..." if len(text) > max_length else text
    
    def _build_report_prompt(self, title: str, keywords: List[str], 
                           raw_data: List[Dict], template_type: str, language: str) -> str:
        """构建简报生成提示词"""
        
        # 数据摘要
        data_summary = []
        for item in raw_data[:20]:  # 限制数据量
            data_summary.append({
                'title': item.get('title', ''),
                'source': item.get('source', ''),
                'publish_time': item.get('publish_time', ''),
                'summary': item.get('summary', '')[:200]  # 限制长度
            })
        
        if language == 'zh':
            prompt = f"""
            请根据以下舆情数据生成一份专业的舆情简报。
            
            简报标题：{title}
            关键词：{', '.join(keywords)}
            模板类型：{template_type}
            
            舆情数据：
            {json.dumps(data_summary, ensure_ascii=False, indent=2)}
            
            请按照以下结构生成简报：
            
            1. 本期导读（3-5个要点）
            2. 编者的话（深度分析和趋势预测）
            3. 舆情内容（每个热点包含：热点导读、报道摘要、报道简评、热点延伸）
            4. 引用来源
            
            要求：
            - 语言专业、客观
            - 分析深入、有见地
            - 结构清晰、逻辑性强
            - 字数控制在2000-5000字
            """
        else:
            prompt = f"""
            Please generate a professional public opinion report based on the following data.
            
            Report Title: {title}
            Keywords: {', '.join(keywords)}
            Template Type: {template_type}
            
            Opinion Data:
            {json.dumps(data_summary, ensure_ascii=False, indent=2)}
            
            Please structure the report as follows:
            
            1. Executive Summary (3-5 key points)
            2. Editor's Note (in-depth analysis and trend prediction)
            3. Opinion Content (for each hot topic: overview, summary, commentary, extension)
            4. References
            
            Requirements:
            - Professional and objective language
            - In-depth and insightful analysis
            - Clear structure and strong logic
            - Word count: 2000-5000 words
            """
        
        return prompt
    
    async def _generate_with_openai(self, prompt: str) -> str:
        """使用OpenAI生成内容"""
        try:
            response = await self.openai_client.chat.completions.create(
                model=self.default_model,
                messages=[
                    {"role": "system", "content": "你是一个专业的舆情分析师，擅长生成高质量的舆情简报和分析。"},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=self.max_tokens,
                temperature=self.temperature
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            logger.error(f"OpenAI API调用失败: {str(e)}")
            raise
    
    async def _generate_with_anthropic(self, prompt: str) -> str:
        """使用Anthropic Claude生成内容"""
        try:
            response = await self.anthropic_client.messages.create(
                model="claude-3-sonnet-20240229",
                max_tokens=self.max_tokens,
                temperature=self.temperature,
                messages=[
                    {"role": "user", "content": prompt}
                ]
            )
            
            return response.content[0].text
            
        except Exception as e:
            logger.error(f"Anthropic API调用失败: {str(e)}")
            raise
    
    async def _analyze_sentiment_batch(self, texts: List[str]) -> List[Dict[str, Any]]:
        """批量情感分析实现"""
        prompt = f"""
        请对以下文本进行情感分析，返回JSON格式结果：
        
        文本列表：
        {json.dumps(texts, ensure_ascii=False)}
        
        请返回如下格式：
        {{
            "results": [
                {{
                    "sentiment": "positive/neutral/negative",
                    "score": 0.8,
                    "confidence": 0.9
                }}
            ]
        }}
        """
        
        try:
            if self.default_model.startswith('gpt'):
                content = await self._generate_with_openai(prompt)
            else:
                content = await self._generate_with_anthropic(prompt)
            
            result = json.loads(content)
            return result.get('results', [])
            
        except Exception as e:
            logger.error(f"情感分析失败: {str(e)}")
            return [{'sentiment': 'neutral', 'score': 0.0, 'confidence': 0.0} for _ in texts]
    
    def _parse_report_content(self, content: str) -> Dict[str, Any]:
        """解析生成的简报内容"""
        try:
            # 尝试按章节分割内容
            sections = {
                'title': '',
                'summary': '',
                'editor_note': '',
                'content': '',
                'references': '',
                'full_content': content
            }
            
            # 简单的章节提取逻辑
            lines = content.split('\n')
            current_section = 'content'
            
            for line in lines:
                line = line.strip()
                if not line:
                    continue
                
                if '导读' in line or 'Summary' in line:
                    current_section = 'summary'
                elif '编者' in line or 'Editor' in line:
                    current_section = 'editor_note'
                elif '引用' in line or 'Reference' in line:
                    current_section = 'references'
                else:
                    sections[current_section] += line + '\n'
            
            return sections
            
        except Exception as e:
            logger.error(f"解析简报内容失败: {str(e)}")
            return {
                'title': '',
                'summary': '',
                'editor_note': '',
                'content': content,
                'references': '',
                'full_content': content
            }
    
    def _extract_topics_from_text(self, text: str) -> List[Dict[str, Any]]:
        """从文本中提取话题（备用方法）"""
        topics = []
        
        # 简单的正则提取
        topic_pattern = r'(\d+)\.\s*([^：:]+)[:：]\s*([^。.]+)'
        matches = re.findall(topic_pattern, text)
        
        for i, (num, topic, desc) in enumerate(matches[:10]):
            topics.append({
                'topic': topic.strip(),
                'importance': 10 - i,  # 简单的重要性评分
                'count': 1,
                'description': desc.strip()
            })
        
        return topics
