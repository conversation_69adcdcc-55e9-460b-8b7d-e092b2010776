<template>
  <div class="login-container">
    <div class="login-card">
      <div class="login-header">
        <div class="logo">
          <home-outlined class="logo-icon" />
        </div>
        <h1 class="title">后台管理系统</h1>
      </div>
      
      <a-form
        :model="loginForm"
        :rules="rules"
        ref="loginFormRef"
        @submit.prevent="handleSubmit"
      >
        <a-form-item name="username">
          <a-input
            v-model:value="loginForm.username"
            placeholder="用户名"
            size="large"
            :prefix="() => h(UserOutlined)"
          />
        </a-form-item>
        
        <a-form-item name="password">
          <a-input-password
            v-model:value="loginForm.password"
            placeholder="密码"
            size="large"
            :prefix="() => h(LockOutlined)"
          />
        </a-form-item>
        
        <div class="remember-forgot">
          <a-checkbox v-model:checked="rememberMe">记住我</a-checkbox>
          <a href="javascript:;" class="forgot-link">忘记密码？</a>
        </div>
        
        <a-form-item>
          <a-button
            type="primary"
            html-type="submit"
            :loading="loading"
            size="large"
            block
          >
            登录
          </a-button>
        </a-form-item>
      </a-form>
      
      <div class="login-footer">
        <p>测试账号: admin / 密码: admin123</p>
        <p>Copyright © {{ new Date().getFullYear() }} 后台管理系统</p>
      </div>
    </div>
  </div>
</template>

<script>
import { defineComponent, reactive, ref, h, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { UserOutlined, LockOutlined, HomeOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';

export default defineComponent({
  name: 'Login',
  setup() {
    const router = useRouter();
    const route = useRoute();
    
    const loginFormRef = ref(null);
    const loading = ref(false);
    const rememberMe = ref(false);
    
    // 登录表单数据
    const loginForm = reactive({
      username: '',
      password: ''
    });
    
    // 表单验证规则
    const rules = {
      username: [
        { required: true, message: '请输入用户名', trigger: 'blur' },
        { min: 3, max: 20, message: '用户名长度应在3-20个字符之间', trigger: 'blur' }
      ],
      password: [
        { required: true, message: '请输入密码', trigger: 'blur' },
        { min: 6, max: 20, message: '密码长度应在6-20个字符之间', trigger: 'blur' }
      ]
    };
    
    // 处理表单提交
    const handleSubmit = () => {
      loginFormRef.value.validate().then(() => {
        loading.value = true;
        
        // 模拟登录请求
        setTimeout(() => {
          // 登录成功后的处理
          if (loginForm.username === 'admin' && loginForm.password === 'admin123') {
            // 保存token到本地存储
            localStorage.setItem('token', 'demo-token-abc123');
            
            // 保存用户信息
            localStorage.setItem('userInfo', JSON.stringify({
              id: 1,
              username: loginForm.username,
              name: '管理员',
              avatar: '',
              roles: ['admin']
            }));
            
            if (rememberMe.value) {
              localStorage.setItem('rememberedUser', loginForm.username);
            } else {
              localStorage.removeItem('rememberedUser');
            }
            
            message.success('登录成功');
            
            // 获取重定向地址
            const redirect = route.query.redirect || '/';
            router.push(redirect);
          } else {
            message.error('用户名或密码错误');
          }
          
          loading.value = false;
        }, 1000);
      }).catch(error => {
        console.log('验证失败:', error);
      });
    };
    
    // 组件挂载时，如果有记住的用户，则自动填充用户名
    onMounted(() => {
      const rememberedUser = localStorage.getItem('rememberedUser');
      if (rememberedUser) {
        loginForm.username = rememberedUser;
        rememberMe.value = true;
      }
    });
    
    return {
      loginForm,
      loginFormRef,
      rules,
      loading,
      rememberMe,
      handleSubmit,
      h,
      UserOutlined,
      LockOutlined,
      HomeOutlined
    };
  }
});
</script>

<style lang="less" scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background: linear-gradient(135deg, #1890ff 0%, #52c41a 100%);
  
  .login-card {
    width: 360px;
    padding: 40px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
    
    .login-header {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: 40px;
      
      .logo {
        width: 64px;
        height: 64px;
        margin-bottom: 16px;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: #1890ff;
        border-radius: 50%;
        
        .logo-icon {
          font-size: 36px;
          color: #fff;
        }
      }
      
      .title {
        font-size: 24px;
        font-weight: 600;
        color: #333;
        margin: 0;
      }
    }
    
    .remember-forgot {
      display: flex;
      justify-content: space-between;
      margin-bottom: 24px;
      
      .forgot-link {
        color: #1890ff;
        
        &:hover {
          color: #40a9ff;
        }
      }
    }
    
    .login-footer {
      margin-top: 16px;
      text-align: center;
      color: #999;
      font-size: 12px;
      
      p {
        margin: 8px 0;
      }
    }
  }
}
</style> 