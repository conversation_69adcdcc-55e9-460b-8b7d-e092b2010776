-- PostgreSQL初始化脚本
-- 运行方式: psql -U postgres -f init_postgres.sql

-- 创建数据库
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_database WHERE datname = 'ai_platform') THEN
        -- 使用C为默认排序规则，避免字符编码问题
        CREATE DATABASE ai_platform
        WITH 
        ENCODING = 'UTF8'
        LC_COLLATE = 'C'
        LC_CTYPE = 'C'
        TEMPLATE template0;
        
        RAISE NOTICE '数据库已创建';
    ELSE
        RAISE NOTICE '数据库已存在，跳过创建步骤';
    END IF;
END
$$;

-- 连接到新创建的数据库
\c ai_platform

-- 检查编码设置
SELECT current_setting('server_encoding') as server_encoding;
SELECT current_setting('client_encoding') as client_encoding;

-- 设置客户端编码
SET client_encoding = 'UTF8';

-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    hashed_password VARCHAR(100) NOT NULL,
    full_name VARCHAR(100),
    is_active BOOLEAN DEFAULT TRUE,
    is_admin BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE
);

-- 创建翻译表
CREATE TABLE IF NOT EXISTS translations (
    id SERIAL PRIMARY KEY,
    translation_id VARCHAR(50) UNIQUE NOT NULL,
    user_id INTEGER REFERENCES users(id),
    source_language VARCHAR(10),
    target_language VARCHAR(10),
    source_text TEXT,
    translated_text TEXT,
    source_audio_url VARCHAR(255),
    translated_audio_url VARCHAR(255),
    status VARCHAR(20),
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_translations_user_id ON translations(user_id);
CREATE INDEX IF NOT EXISTS idx_translations_translation_id ON translations(translation_id);

-- 创建测试用户（密码是'password'的哈希值）
INSERT INTO users (username, email, hashed_password, full_name, is_admin)
VALUES 
    ('admin', '<EMAIL>', '$2b$12$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW', 'Admin User', TRUE),
    ('user', '<EMAIL>', '$2b$12$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW', 'Normal User', FALSE)
ON CONFLICT (username) DO NOTHING;

SELECT 'PostgreSQL初始化完成' as status; 