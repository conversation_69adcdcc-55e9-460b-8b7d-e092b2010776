#!/usr/bin/env python3
"""
AI响应生成器

负责生成数字人的回复内容，支持多种模型和方法。
"""

import os
import logging
import json
import asyncio
from typing import Dict, Any, List, Optional, Union
from datetime import datetime
import uuid
import random

# 导入服务
from backend.services.knowledge_service import get_knowledge_service
from backend.db import get_db
from sqlalchemy.orm import Session
from backend.models.digital_human import DigitalHuman

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AIResponseGenerator:
    """AI响应生成器类"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化AI响应生成器
        
        Args:
            config: 配置信息
        """
        self.config = config or {}
        self.model_config = self.config.get('model', {})
        
        # 尝试加载知识服务
        try:
            self.knowledge_service = get_knowledge_service()
        except Exception as e:
            logger.warning(f"知识服务加载失败: {e}")
            self.knowledge_service = None
    
    async def generate_response(
        self, 
        user_message: Dict[str, Any],
        session_context: Dict[str, Any],
        digital_human_id: str
    ) -> Dict[str, Any]:
        """
        生成AI回复
        
        Args:
            user_message: 用户消息
            session_context: 会话上下文
            digital_human_id: 数字人ID
        
        Returns:
            AI回复内容
        """
        try:
            # 获取数据库会话
            db = next(get_db())
            
            # 获取数字人信息
            digital_human = db.query(DigitalHuman).filter(
                DigitalHuman.digital_human_id == digital_human_id
            ).first()
            
            if not digital_human:
                return {
                    'message_id': str(uuid.uuid4()),
                    'text': "抱歉，我无法找到对应的数字人信息。",
                    'error': "数字人不存在"
                }
            
            # 获取用户消息内容
            user_text = user_message.get('text', '')
            user_emotion = user_message.get('emotion', {}).get('emotion', 'neutral')
            
            # 提取会话上下文
            recent_messages = session_context.get('messages', [])[-5:]  # 获取最近5条消息
            emotion_history = session_context.get('context', {}).get('emotion_history', [])
            
            # 选择响应生成模型
            model_type = self.model_config.get('type', 'template')
            
            if model_type == 'template':
                # 使用模板生成回复
                response = await self._generate_from_template(
                    user_text,
                    user_emotion,
                    digital_human,
                    recent_messages
                )
            elif model_type == 'llm':
                # 使用大语言模型生成回复
                response = await self._generate_from_llm(
                    user_text,
                    user_emotion,
                    digital_human,
                    recent_messages
                )
            else:
                # 使用随机回复
                response = await self._generate_random_response(
                    user_text,
                    user_emotion,
                    digital_human
                )
            
            # 生成消息ID
            message_id = str(uuid.uuid4())
            
            # 构建完整回复
            ai_response = {
                'message_id': message_id,
                'text': response.get('text', "抱歉，我理解不了您的意思。"),
                'voice_id': response.get('voice_id'),
                'knowledge_source': response.get('knowledge_source'),
                'topics': response.get('topics', []),
                'generation_time': datetime.now().isoformat()
            }
            
            return ai_response
        
        except Exception as e:
            logger.error(f"生成AI回复失败: {e}")
            return {
                'message_id': str(uuid.uuid4()),
                'text': f"抱歉，我遇到了一些问题，无法正常回应。",
                'error': str(e)
            }
    
    async def _generate_from_template(
        self,
        user_text: str,
        user_emotion: str,
        digital_human: Any,
        recent_messages: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """使用模板生成回复"""
        # 基于情感的回复模板
        emotion_templates = {
            'happy': [
                "我也很高兴能和您交流！{answer}",
                "看到您这么开心，我也很开心。{answer}",
                "真棒！{answer}"
            ],
            'sad': [
                "别担心，{answer}",
                "我理解您的感受，{answer}",
                "希望我的回答能让您感觉好一些。{answer}"
            ],
            'angry': [
                "我理解您的沮丧，{answer}",
                "让我们一起解决这个问题。{answer}",
                "我很抱歉听到这个，{answer}"
            ],
            'surprised': [
                "确实令人惊讶！{answer}",
                "我也没想到！{answer}",
                "{answer} 这确实很不寻常。"
            ],
            'neutral': [
                "{answer}",
                "关于这个问题，{answer}",
                "我想告诉您，{answer}"
            ]
        }
        
        # 默认使用中性模板
        templates = emotion_templates.get(user_emotion, emotion_templates['neutral'])
        
        # 随机选择一个模板
        template = random.choice(templates)
        
        # 尝试从知识库获取回答
        answer = "我没有找到相关的信息。"
        knowledge_source = None
        
        if self.knowledge_service:
            try:
                # 查询知识库
                knowledge_result = self.knowledge_service.get_relevant_knowledge(
                    user_text, 
                    digital_human_id=digital_human.digital_human_id
                )
                
                if knowledge_result.get('success') and knowledge_result.get('count', 0) > 0:
                    # 使用最相关的知识作为回答
                    top_result = knowledge_result['results'][0]
                    answer = top_result['text']
                    knowledge_source = {
                        'id': top_result.get('id'),
                        'source': top_result.get('source'),
                        'score': top_result.get('score')
                    }
            except Exception as e:
                logger.error(f"查询知识库失败: {e}")
        
        # 如果知识库没有回答，使用备选回答
        if answer == "我没有找到相关的信息。":
            # 简单的关键词匹配
            if "你好" in user_text or "您好" in user_text:
                answer = f"您好，我是{digital_human.name}，很高兴为您服务！"
            elif "再见" in user_text or "拜拜" in user_text:
                answer = "再见，祝您有美好的一天！"
            elif "谢谢" in user_text:
                answer = "不客气，很高兴能帮到您！"
            elif "什么" in user_text and "名字" in user_text:
                answer = f"我的名字是{digital_human.name}，是一个数字人助手。"
            elif "天气" in user_text:
                answer = "抱歉，我目前无法获取实时天气信息。"
            elif "时间" in user_text:
                answer = f"现在的时间是{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}。"
            else:
                # 默认回复
                default_responses = [
                    f"作为{digital_human.name}，我还在学习中。请问您有什么特定的问题吗？",
                    "这个问题很有趣，让我思考一下...",
                    "抱歉，我对这个不太了解，能告诉我更多细节吗？",
                    "这个话题很有深度，您能详细说明一下您的问题吗？"
                ]
                answer = random.choice(default_responses)
        
        # 填充模板
        response_text = template.format(answer=answer)
        
        return {
            'text': response_text,
            'knowledge_source': knowledge_source,
            'topics': self._extract_topics(user_text)
        }
    
    async def _generate_from_llm(
        self,
        user_text: str,
        user_emotion: str,
        digital_human: Any,
        recent_messages: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """使用大语言模型生成回复"""
        # 实际应用中应该接入真实的大语言模型
        # 此处为示例实现
        
        # 构建提示词
        system_prompt = f"""
        你是一个名为{digital_human.name}的数字人，性格特点是{digital_human.personality or '友善、专业'}。
        用户的情绪状态是：{user_emotion}。
        请以数字人身份回复用户，语气自然、友好，不要过于机械。
        回复应该简洁、有帮助性，不要过长。
        """
        
        # 构建对话历史
        conversation = []
        for msg in recent_messages:
            if msg.get('type') == 'user':
                conversation.append({"role": "user", "content": msg.get('text', '')})
            elif msg.get('type') == 'ai':
                conversation.append({"role": "assistant", "content": msg.get('text', '')})
        
        # 添加当前用户消息
        conversation.append({"role": "user", "content": user_text})
        
        # 随机生成一个模拟回复（实际应该调用真实的LLM）
        llm_responses = [
            f"作为{digital_human.name}，我很高兴回答您的问题。关于\"{user_text}\"，我认为这是一个很好的话题。您想了解更多哪方面的信息呢？",
            f"我是{digital_human.name}，很高兴能帮助您。根据我的理解，您问的是关于\"{user_text}\"的问题。这是一个很有意思的话题，我们可以进一步探讨。",
            f"感谢您的提问！作为{digital_human.name}，我专注于提供高质量的服务。关于\"{user_text}\"，我有一些想法可以分享。"
        ]
        
        response_text = random.choice(llm_responses)
        
        return {
            'text': response_text,
            'knowledge_source': None,
            'topics': self._extract_topics(user_text)
        }
    
    async def _generate_random_response(
        self,
        user_text: str,
        user_emotion: str,
        digital_human: Any
    ) -> Dict[str, Any]:
        """生成随机回复（备选方案）"""
        random_responses = [
            f"作为{digital_human.name}，我还在学习如何更好地回答这类问题。",
            "这是一个很好的问题，让我思考一下...",
            "您的问题很有趣，我需要更多信息来给出准确的回答。",
            "谢谢您的提问！我正在处理您的请求。"
        ]
        
        return {
            'text': random.choice(random_responses),
            'knowledge_source': None,
            'topics': []
        }
    
    def _extract_topics(self, text: str) -> List[str]:
        """从文本中提取主题"""
        # 简单示例，实际应用应该使用更复杂的NLP技术
        topics = []
        
        # 简单的关键词匹配
        keyword_to_topic = {
            "天气": "天气",
            "新闻": "新闻",
            "音乐": "娱乐",
            "电影": "娱乐",
            "游戏": "娱乐",
            "健康": "健康",
            "运动": "健康",
            "病": "健康",
            "学": "教育",
            "教育": "教育",
            "课程": "教育",
            "钱": "财务",
            "金融": "财务",
            "工作": "职业",
            "职位": "职业",
            "公司": "商业",
            "产品": "商业",
            "技术": "科技",
            "手机": "科技",
            "电脑": "科技",
            "软件": "科技",
            "旅游": "旅行",
            "旅行": "旅行",
            "地点": "旅行",
            "食物": "美食",
            "菜": "美食",
            "吃": "美食"
        }
        
        for keyword, topic in keyword_to_topic.items():
            if keyword in text and topic not in topics:
                topics.append(topic)
        
        return topics[:3]  # 限制最多3个主题

# 单例模式
_instance = None

def get_ai_response_generator(config: Dict[str, Any] = None) -> AIResponseGenerator:
    """
    获取AI响应生成器实例
    
    Args:
        config: 配置信息
    
    Returns:
        AI响应生成器实例
    """
    global _instance
    if _instance is None:
        _instance = AIResponseGenerator(config)
    return _instance

async def generate_ai_response(
    session_id: str,
    user_message: Dict[str, Any],
    digital_human_id: str,
    config: Dict[str, Any] = None
) -> Dict[str, Any]:
    """
    生成AI回复的便捷函数
    
    Args:
        session_id: 会话ID
        user_message: 用户消息
        digital_human_id: 数字人ID
        config: 配置信息
    
    Returns:
        AI回复内容
    """
    # 获取生成器实例
    generator = get_ai_response_generator(config)
    
    # 简化的会话上下文
    session_context = {
        'messages': [user_message],
        'context': {
            'emotion_history': []
        }
    }
    
    # 生成回复
    response = await generator.generate_response(
        user_message,
        session_context,
        digital_human_id
    )
    
    return response 