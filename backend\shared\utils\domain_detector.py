#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
领域检测工具
用于自动识别文本所属领域
"""

import logging
import re
import jieba
import jieba.analyse
from typing import Dict, List, Tuple, Optional
from collections import Counter

# 创建日志记录器
logger = logging.getLogger("domain_detector")

# 领域关键词
DOMAIN_KEYWORDS = {
    "汽车": [
        "汽车", "车", "发动机", "轮胎", "底盘", "变速箱", "刹车", "方向盘", "车轮", 
        "悬挂", "油耗", "排量", "涡轮", "电动汽车", "混合动力", "自动驾驶", "SUV",
        "轿车", "货车", "客车", "卡车", "皮卡", "越野车", "跑车", "新能源", "充电桩",
        "续航", "车门", "车窗", "仪表盘", "座椅", "安全带", "气囊", "ABS", "ESP",
        "四驱", "前驱", "后驱", "涡轮增压", "自然吸气", "柴油", "汽油", "电池", "电机",
        "车身", "车架", "转向", "制动", "悬架", "传动", "动力", "排放", "尾气", "油箱",
        "油门", "离合器", "刹车踏板", "点火", "钥匙", "遥控", "车灯", "雨刷", "后视镜",
        "天窗", "行李箱", "引擎盖", "保险杠", "车牌", "车型", "品牌", "厂商", "4S店",
        "保养", "维修", "保险", "年检", "违章", "驾照", "驾驶", "停车", "加油", "充电",
        "燃油喷射", "缸内直喷", "气门正时", "可变气门正时", "双离合", "CVT", "AMT", "DSG",
        "麦弗逊悬挂", "多连杆悬挂", "空气悬挂", "扭力梁", "独立悬挂", "非独立悬挂",
        "防抱死制动系统", "电子稳定程序", "牵引力控制", "车道保持", "自适应巡航", "全景影像",
        "盲区监测", "自动泊车", "电子驻车", "陡坡缓降", "上坡辅助", "胎压监测", "无钥匙进入",
        "一键启动", "倒车雷达", "自动空调", "座椅加热", "座椅通风", "座椅记忆", "氙气大灯",
        "LED大灯", "日间行车灯", "自动大灯", "转向辅助灯", "雾灯", "电动尾门", "全景天窗",
        "电动后备厢", "电动座椅", "电动后视镜", "多功能方向盘", "换挡拨片", "巡航定速",
        "胎噪", "风噪", "NVH", "操控", "越野", "通过性", "离地间隙", "接近角", "离去角",
        "汽车专业术语", "车辆术语", "汽车领域", "汽车工业", "汽车制造", "汽车设计", "汽车性能",
        "进气", "排气", "进排气系统", "三元催化", "汽车电子", "汽车电气", "汽车内饰", "汽车外饰"
    ],
    "医疗": [
        "医院", "医生", "护士", "患者", "病人", "疾病", "症状", "治疗", "药物", "手术",
        "诊断", "检查", "病毒", "细菌", "感染", "免疫", "抗体", "疫苗", "康复", "心脏",
        "肺", "肝", "肾", "脑", "血液", "血压", "血糖", "癌症", "肿瘤", "慢性病",
        "急性", "炎症", "抗生素", "消炎药", "止痛药", "麻醉", "CT", "核磁", "B超", "X光",
        "住院", "门诊", "急诊", "挂号", "病历", "处方", "药方", "药店", "药房", "药剂师",
        "医保", "社保", "报销", "自费", "医疗费", "病床", "病房", "ICU", "监护", "护理",
        "输液", "注射", "口服", "外用", "用药", "剂量", "副作用", "禁忌", "适应症", "病因",
        "病理", "病灶", "病变", "病情", "预后", "预防", "保健", "养生", "康复", "理疗",
        "中医", "西医", "针灸", "推拿", "按摩", "拔罐", "刮痧", "艾灸", "中药", "草药"
    ],
    "金融": [
        "银行", "金融", "投资", "股票", "基金", "债券", "保险", "理财", "贷款", "信贷",
        "利率", "汇率", "证券", "期货", "期权", "外汇", "股市", "牛市", "熊市", "IPO",
        "融资", "风险", "收益", "资产", "负债", "存款", "取款", "转账", "支付", "信用卡",
        "借记卡", "支票", "余额", "本金", "利息", "分红", "手续费", "财务", "会计", "审计",
        "报表", "资产负债表", "利润表", "现金流量表", "财报", "财年", "季报", "年报", "上市公司",
        "股东", "董事会", "监事会", "CEO", "CFO", "CTO", "COO", "创始人", "创业", "风投",
        "天使投资", "A轮", "B轮", "C轮", "D轮", "Pre-IPO", "上市", "退市", "停牌", "复牌",
        "涨停", "跌停", "开盘", "收盘", "高开", "低开", "高走", "低走", "震荡", "波动",
        "趋势", "技术分析", "基本面", "K线", "均线", "成交量", "换手率", "市值", "市盈率", "市净率"
    ],
    "IT": [
        "计算机", "软件", "硬件", "编程", "代码", "算法", "数据", "网络", "服务器", "云计算",
        "大数据", "人工智能", "机器学习", "深度学习", "神经网络", "区块链", "物联网", "5G", "互联网",
        "程序员", "开发者", "工程师", "架构师", "测试", "运维", "产品经理", "设计师", "UI", "UX",
        "前端", "后端", "全栈", "移动端", "PC端", "服务端", "客户端", "数据库", "SQL", "NoSQL",
        "Java", "Python", "C++", "C#", "JavaScript", "TypeScript", "PHP", "Ruby", "Go", "Swift",
        "HTML", "CSS", "React", "Vue", "Angular", "Node.js", "Django", "Flask", "Spring", "Express",
        "Linux", "Windows", "macOS", "iOS", "Android", "Docker", "Kubernetes", "Git", "GitHub", "GitLab",
        "API", "SDK", "IDE", "框架", "库", "插件", "组件", "模块", "函数", "类",
        "对象", "接口", "抽象", "继承", "多态", "封装", "线程", "进程", "并发", "异步"
    ],
    "能源": [
        "能源", "电力", "石油", "天然气", "煤炭", "核能", "太阳能", "风能", "水能", "地热",
        "生物质", "可再生", "不可再生", "发电", "输电", "配电", "用电", "电网", "电站", "电厂",
        "火电", "水电", "核电", "风电", "光伏", "储能", "电池", "充电", "节能", "减排",
        "碳中和", "碳达峰", "碳排放", "温室气体", "气候变化", "全球变暖", "清洁能源", "绿色能源", "能效", "能耗",
        "电压", "电流", "功率", "电阻", "电容", "电感", "变压器", "开关", "断路器", "继电器",
        "发电机", "汽轮机", "水轮机", "燃气轮机", "锅炉", "冷却塔", "输电线", "变电站", "配电室", "电表",
        "电费", "峰谷电", "分时电价", "阶梯电价", "电力市场", "电力交易", "电力调度", "电力平衡", "电力规划", "电力建设",
        "油田", "气田", "煤矿", "油井", "气井", "钻井", "采油", "炼油", "成品油", "原油",
        "汽油", "柴油", "煤油", "燃料油", "润滑油", "天然气管道", "液化天然气", "压缩天然气", "页岩气", "煤层气"
    ],
    "教育": [
        "学校", "大学", "中学", "小学", "幼儿园", "教育", "教学", "学习", "教师", "学生",
        "课程", "课堂", "教材", "教案", "作业", "考试", "成绩", "分数", "学分", "学历",
        "学士", "硕士", "博士", "学位", "毕业", "入学", "招生", "录取", "升学", "辍学",
        "留学", "奖学金", "助学金", "学费", "补习", "培训", "辅导", "家教", "在线教育", "远程教育",
        "教授", "讲师", "班主任", "校长", "院长", "系主任", "教务处", "学生处", "招生办", "就业办",
        "课外活动", "社团", "实验室", "图书馆", "食堂", "宿舍", "校园", "校区", "学院", "系",
        "专业", "学科", "文科", "理科", "工科", "医科", "农科", "商科", "艺术", "体育",
        "语文", "数学", "英语", "物理", "化学", "生物", "历史", "地理", "政治", "音乐",
        "美术", "体育课", "信息技术", "思想品德", "心理健康", "班会", "早读", "晚自习", "课间操", "运动会"
    ],
    "法律": [
        "法律", "法规", "条例", "规定", "法院", "检察院", "律师", "法官", "检察官", "公证员",
        "诉讼", "起诉", "上诉", "判决", "裁定", "调解", "仲裁", "和解", "庭审", "取证",
        "证据", "证人", "鉴定", "司法", "执法", "立法", "普法", "违法", "犯罪", "刑事",
        "民事", "行政", "商事", "经济", "知识产权", "著作权", "专利", "商标", "合同", "协议",
        "权利", "义务", "责任", "赔偿", "补偿", "罚款", "拘留", "逮捕", "监禁", "缓刑",
        "假释", "减刑", "特赦", "赦免", "无罪", "有罪", "从轻", "从重", "免责", "连带责任",
        "主观过错", "客观过错", "故意", "过失", "直接责任", "间接责任", "刑法", "民法", "行政法", "诉讼法",
        "宪法", "婚姻法", "继承法", "物权法", "债权法", "公司法", "证券法", "保险法", "劳动法", "环境法",
        "国际法", "海商法", "税法", "知识产权法", "网络安全法", "反垄断法", "反不正当竞争法", "消费者权益保护法", "广告法", "商标法"
    ]
}

def detect_domain(text: str, threshold: float = 0.4) -> str:
    """
    检测文本所属的领域
    
    Args:
        text: 输入文本
        threshold: 置信度阈值，超过该阈值才会返回领域，否则返回"通用"
        
    Returns:
        str: 领域名称，如果无法确定则返回"通用"
    """
    try:
        # 如果文本为空，返回通用
        if not text or len(text.strip()) == 0:
            return "通用"
        
        # 分词
        words = [w for w in jieba.cut(text) if len(w) > 1]
        
        # 计算每个领域的匹配度
        domain_scores = {}
        for domain, keywords in DOMAIN_KEYWORDS.items():
            # 计算文本中包含该领域关键词的数量
            matches = sum(1 for word in words if word in keywords)
            # 计算匹配度（匹配数量 / 文本长度）
            score = matches / len(words) if words else 0
            domain_scores[domain] = score
        
        # 找出得分最高的领域
        best_domain = max(domain_scores.items(), key=lambda x: x[1])
        
        logger.info(f"[领域检测] 文本长度: {len(text)}, 分词数量: {len(words)}, 最佳领域: {best_domain[0]}, 得分: {best_domain[1]:.4f}")
        
        # 如果最高得分超过阈值，返回该领域，否则返回通用
        if best_domain[1] >= threshold:
            return best_domain[0]
        else:
            return "通用"
    except Exception as e:
        logger.error(f"[领域检测] 检测领域出错: {str(e)}")
        return "通用"

def detect_domain_with_scores(text: str) -> Dict[str, float]:
    """
    检测文本所属的领域，并返回所有领域的得分
    
    Args:
        text: 输入文本
        
    Returns:
        dict: 各领域的得分
    """
    try:
        # 如果文本为空，返回空结果
        if not text or len(text.strip()) == 0:
            return {"通用": 1.0}
        
        # 分词
        words = [w for w in jieba.cut(text) if len(w) > 1]
        
        # 计算每个领域的匹配度
        domain_scores = {}
        for domain, keywords in DOMAIN_KEYWORDS.items():
            # 计算文本中包含该领域关键词的数量
            matches = sum(1 for word in words if word in keywords)
            # 计算匹配度（匹配数量 / 文本长度）
            score = matches / len(words) if words else 0
            domain_scores[domain] = score
        
        # 如果所有领域得分都很低，添加通用领域
        if all(score < 0.3 for score in domain_scores.values()):
            domain_scores["通用"] = 0.5
        
        return domain_scores
    except Exception as e:
        logger.error(f"[领域检测] 检测领域得分出错: {str(e)}")
        return {"通用": 1.0}

def extract_domain_keywords(text: str, domain: str, top_k: int = 10) -> List[str]:
    """
    提取特定领域的关键词
    
    Args:
        text: 输入文本
        domain: 领域名称
        top_k: 返回的关键词数量
        
    Returns:
        list: 关键词列表
    """
    try:
        # 获取领域关键词
        domain_kws = DOMAIN_KEYWORDS.get(domain, [])
        
        # 使用jieba提取关键词
        keywords = jieba.analyse.extract_tags(text, topK=top_k*2)
        
        # 优先选择领域相关的关键词
        domain_related = [kw for kw in keywords if kw in domain_kws]
        
        # 如果领域相关的关键词不足，补充其他关键词
        if len(domain_related) < top_k:
            other_kws = [kw for kw in keywords if kw not in domain_related]
            result = domain_related + other_kws[:top_k - len(domain_related)]
        else:
            result = domain_related[:top_k]
        
        return result
    except Exception as e:
        logger.error(f"[领域检测] 提取领域关键词出错: {str(e)}")
        return []

def determine_domain(task_name: str, keywords: str) -> str:
    """
    根据任务名称和关键词确定领域
    
    Args:
        task_name: 任务名称
        keywords: 关键词字符串
        
    Returns:
        str: 领域名称
    """
    # 合并任务名称和关键词
    text = f"{task_name} {keywords}"
    
    # 检测领域
    try:
        # 先进行一些关键词的直接匹配，提高特定领域的识别率
        # 汽车领域的关键词匹配
        auto_keywords = ["汽车", "车", "发动机", "轮胎", "底盘", "变速箱", "刹车", "方向盘", "SUV", "轿车", 
                         "汽车专业术语", "汽车领域", "车辆", "车型", "自动驾驶", "新能源汽车", "电动汽车"]
        for kw in auto_keywords:
            if kw in text:
                return "汽车"
                
        # 医疗领域的关键词匹配
        medical_keywords = ["医疗", "医院", "医生", "患者", "病人", "疾病", "症状", "治疗", "药物", "手术", 
                           "医学", "医疗术语", "医学术语"]
        for kw in medical_keywords:
            if kw in text:
                return "医疗"
                
        # 法律领域的关键词匹配
        legal_keywords = ["法律", "法规", "法院", "律师", "诉讼", "合同", "起诉", "被告", "原告", "判决", 
                         "法律术语", "法律条款"]
        for kw in legal_keywords:
            if kw in text:
                return "法律"
                
        # 技术领域的关键词匹配
        tech_keywords = ["技术", "软件", "编程", "开发", "算法", "数据", "网络", "系统", "应用", "程序", 
                        "IT", "IT术语", "计算机", "技术术语"]
        for kw in tech_keywords:
            if kw in text:
                return "IT"
        
        # 如果没有明确的领域匹配，使用detect_domain函数进行更详细的分析
        return detect_domain(text)
    except Exception as e:
        logger.error(f"[领域检测] 确定领域出错: {str(e)}")
        # 如果出错，检查是否包含汽车相关词
        if "汽车" in text or "车" in text:
            return "汽车"
        return "通用"

def get_domain_keywords(domain: str) -> List[str]:
    """
    获取指定领域的关键词列表
    
    Args:
        domain: 领域名称
        
    Returns:
        list: 关键词列表
    """
    return DOMAIN_KEYWORDS.get(domain, [])

def add_domain_keywords(domain: str, keywords: List[str]) -> None:
    """
    向指定领域添加关键词
    
    Args:
        domain: 领域名称
        keywords: 要添加的关键词列表
    """
    if domain in DOMAIN_KEYWORDS:
        # 添加新关键词，避免重复
        DOMAIN_KEYWORDS[domain] = list(set(DOMAIN_KEYWORDS[domain] + keywords))
    else:
        # 创建新领域
        DOMAIN_KEYWORDS[domain] = keywords
    
    logger.info(f"[领域检测] 向领域 {domain} 添加了 {len(keywords)} 个关键词，当前共有 {len(DOMAIN_KEYWORDS[domain])} 个关键词")

def add_new_domain(domain: str, keywords: List[str]) -> None:
    """
    添加新的领域
    
    Args:
        domain: 领域名称
        keywords: 关键词列表
    """
    if domain in DOMAIN_KEYWORDS:
        logger.warning(f"[领域检测] 领域 {domain} 已存在，将更新关键词")
    
    DOMAIN_KEYWORDS[domain] = keywords
    logger.info(f"[领域检测] 添加了新领域 {domain}，包含 {len(keywords)} 个关键词")

def get_all_domains() -> List[str]:
    """
    获取所有可用的领域名称
    
    Returns:
        list: 领域名称列表
    """
    return list(DOMAIN_KEYWORDS.keys())

def analyze_domain_distribution(text: str) -> Dict[str, float]:
    """
    分析文本的领域分布情况
    
    Args:
        text: 输入文本
        
    Returns:
        dict: 各领域的分布比例
    """
    # 获取所有领域的得分
    scores = detect_domain_with_scores(text)
    
    # 计算总分
    total_score = sum(scores.values())
    
    # 如果总分为0，返回均匀分布
    if total_score == 0:
        return {domain: 1.0 / len(scores) for domain in scores}
    
    # 计算各领域的比例
    return {domain: score / total_score for domain, score in scores.items()} 