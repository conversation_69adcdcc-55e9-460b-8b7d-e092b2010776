import os
import time
import uuid
import asyncio
import logging
from typing import List, Dict, Any, Optional, Tuple, Union
from datetime import datetime, timedelta
from pathlib import Path
import mimetypes
import shutil
import re
import tempfile
import json
from concurrent.futures import ThreadPoolExecutor
from sqlalchemy import select

import docx
import pdfplumber
from openpyxl import load_workbook
from pptx import Presentation
from fastapi import UploadFile, HTTPException

from models.document_translation import DocumentTranslation, DocumentTranslationUpdate
from services.translation_service import get_translation_service
from utils.db import get_db
from utils.file_utils import ensure_dir, get_file_extension, get_mime_type, count_words
from utils.common import generate_unique_id
from utils.task_persistence import TaskPersistenceManager

# 获取日志记录器
logger = logging.getLogger(__name__)

# 配置文件存储目录
UPLOAD_DIR = "storage/document_translation/uploads"
RESULT_DIR = "storage/document_translation/results"
PREVIEW_DIR = "storage/document_translation/previews"

# 确保目录存在 - 在初始化时创建
async def ensure_directories():
    """确保所有必要的目录都存在"""
    await ensure_dir(UPLOAD_DIR)
    await ensure_dir(RESULT_DIR)
    await ensure_dir(PREVIEW_DIR)

# 在应用启动时调用此函数
# 修复: 不要直接调用asyncio.create_task，而是创建一个同步初始化函数
def init_directories():
    """初始化目录 - 可以在同步上下文中调用"""
    import asyncio
    try:
        # 为不同环境创建不同的初始化方式
        if asyncio.get_event_loop().is_running():
            # 如果有事件循环在运行，使用create_task
            asyncio.create_task(ensure_directories())
        else:
            # 如果没有事件循环，创建一个新的来运行协程
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(ensure_directories())
            loop.close()
    except RuntimeError:
        # 如果出现"no running event loop"错误，使用新的事件循环
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        loop.run_until_complete(ensure_directories())
        loop.close()

# 调用初始化函数
init_directories()

# 支持的文件格式
SUPPORTED_FORMATS = [
    {
        "name": "Microsoft Word",
        "extensions": [".doc", ".docx"],
        "mimeTypes": ["application/msword", "application/vnd.openxmlformats-officedocument.wordprocessingml.document"]
    },
    {
        "name": "PDF",
        "extensions": [".pdf"],
        "mimeTypes": ["application/pdf"]
    },
    {
        "name": "Text",
        "extensions": [".txt"],
        "mimeTypes": ["text/plain"]
    },
    {
        "name": "Microsoft PowerPoint",
        "extensions": [".ppt", ".pptx"],
        "mimeTypes": ["application/vnd.ms-powerpoint", "application/vnd.openxmlformats-officedocument.presentationml.presentation"]
    },
    {
        "name": "Microsoft Excel",
        "extensions": [".xls", ".xlsx"],
        "mimeTypes": ["application/vnd.ms-excel", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"]
    }
]

class DocumentTranslationService:
    """Document translation service"""
    
    def __init__(self):
        self.translation_service = get_translation_service()
        self.tasks = {}  # 内存中跟踪任务状态
        self.executor = ThreadPoolExecutor(max_workers=5)
        self.logger = logger  # 添加logger实例变量
        
    def get_supported_formats(self) -> Dict:
        """获取支持的文件格式信息"""
        return {
            "formats": SUPPORTED_FORMATS,
            "maxSize": 50 * 1024 * 1024,  # 50MB
            "sizeLimit": "50MB"
        }
    
    async def create_translation_task(self, file: UploadFile, user_id: int, 
                                     source_language: str, target_language: str,
                                     domain: str = "general", style: str = "standard",
                                     advanced_options: List[str] = None,
                                     glossary_id: str = None) -> str:
        """创建文档翻译任务"""
        if not file:
            raise HTTPException(status_code=400, detail="没有上传文件")
        
        # 验证文件类型
        file_ext = get_file_extension(file.filename)
        if not any(ext in file_ext.lower() for format_info in SUPPORTED_FORMATS 
                  for ext in format_info["extensions"]):
            raise HTTPException(status_code=400, detail="不支持的文件类型")
        
        # 生成唯一的任务ID和文件路径
        task_id = generate_unique_id()
        file_path = os.path.join(UPLOAD_DIR, f"{task_id}{file_ext}")
        
        # 保存上传的文件
        with open(file_path, "wb") as f:
            content = await file.read()
            f.write(content)
            file_size = len(content)
        
        # 计算文档字数
        word_count = await self._count_document_words(file_path, file_ext)
        
        # 确保user_id是整数
        actual_user_id = getattr(user_id, 'id', user_id)
        logger.info(f"将User对象转换为user_id: {actual_user_id}")
        
        # 创建任务记录
        db = next(get_db())
        translation_task = DocumentTranslation(
            task_id=task_id,
            user_id=actual_user_id,
            file_name=file.filename,
            file_type=file_ext.lstrip('.'),
            file_size=file_size,
            source_language=source_language,
            target_language=target_language,
            source_file_path=file_path,
            status="pending",
            progress=0.0,
            word_count=word_count,
            domain=domain,
            style=style,
            advanced_options=advanced_options or [],
            glossary_id=glossary_id
        )
        
        db.add(translation_task)
        db.commit()
        db.refresh(translation_task)
        
        # 启动异步翻译任务
        asyncio.create_task(self._process_document(task_id))
        
        return task_id
    
    async def get_task_status(self, task_id: str) -> Dict:
        """获取任务状态"""
        db = next(get_db())
        task = db.query(DocumentTranslation).filter(DocumentTranslation.task_id == task_id).first()
        
        if not task:
            raise HTTPException(status_code=404, detail="翻译任务不存在")
        
        return {
            "taskId": task.task_id,
            "status": task.status,
            "progress": task.progress,
            "wordCount": task.word_count,
            "fileName": task.file_name,
            "errorMessage": task.error_message
        }
    
    async def get_translation_preview(self, task_id: str) -> Dict[str, str]:
        """获取翻译预览"""
        db = next(get_db())
        task = db.query(DocumentTranslation).filter(DocumentTranslation.task_id == task_id).first()
        
        if not task:
            raise HTTPException(status_code=404, detail="翻译任务不存在")
            
        if task.status != "completed":
            raise HTTPException(status_code=400, detail="翻译尚未完成，无法预览")
        
        # 如果已经生成了预览，直接返回
        if task.preview_path and os.path.exists(task.preview_path):
            with open(task.preview_path, "r", encoding="utf-8") as f:
                preview_data = json.load(f)
                return preview_data
        
        # 否则生成预览
        preview_data = await self._generate_preview(task)
        
        # 保存预览到文件
        preview_path = os.path.join(PREVIEW_DIR, f"{task_id}_preview.json")
        with open(preview_path, "w", encoding="utf-8") as f:
            json.dump(preview_data, f, ensure_ascii=False)
        
        # 更新任务记录
        db.query(DocumentTranslation).filter(DocumentTranslation.task_id == task_id).update(
            {"preview_path": preview_path}
        )
        db.commit()
        
        return preview_data
    
    async def download_translated_file(self, task_id: str) -> Tuple[str, str]:
        """下载翻译文件"""
        db = next(get_db())
        task = db.query(DocumentTranslation).filter(DocumentTranslation.task_id == task_id).first()
        
        if not task:
            raise HTTPException(status_code=404, detail="翻译任务不存在")
            
        if task.status != "completed":
            raise HTTPException(status_code=400, detail="翻译尚未完成，无法下载")
            
        if not task.translated_file_path or not os.path.exists(task.translated_file_path):
            raise HTTPException(status_code=404, detail="翻译文件不存在")
        
        # 更新下载次数
        db.query(DocumentTranslation).filter(DocumentTranslation.task_id == task_id).update(
            {"download_count": DocumentTranslation.download_count + 1}
        )
        db.commit()
        
        file_name = task.file_name
        file_path = task.translated_file_path
        return file_path, file_name
    
    async def get_history(self, user_id: int, page: int = 1, page_size: int = 10, 
                         status: str = None, language: str = None, 
                         file_name: str = None) -> Dict:
        """获取用户的翻译历史"""
        # 确保user_id是整数
        actual_user_id = getattr(user_id, 'id', user_id)
        
        db = next(get_db())
        query = db.query(DocumentTranslation).filter(DocumentTranslation.user_id == actual_user_id)
        
        # 应用筛选条件
        if status:
            query = query.filter(DocumentTranslation.status == status)
        
        if language:
            query = query.filter(
                (DocumentTranslation.source_language == language) | 
                (DocumentTranslation.target_language == language)
            )
            
        if file_name:
            query = query.filter(DocumentTranslation.file_name.like(f"%{file_name}%"))
        
        # 获取总数
        total = query.count()
        
        # 分页
        items = query.order_by(DocumentTranslation.created_at.desc()) \
                    .offset((page - 1) * page_size) \
                    .limit(page_size) \
                    .all()
        
        return {
            "list": items,
            "pagination": {
                "total": total,
                "page": page,
                "pageSize": page_size,
                "pages": (total + page_size - 1) // page_size
            }
        }
    
    async def delete_history(self, task_id: str, user_id: int) -> bool:
        """删除翻译历史记录"""
        # 确保user_id是整数
        actual_user_id = getattr(user_id, 'id', user_id)
        
        db = next(get_db())
        task = db.query(DocumentTranslation).filter(
            DocumentTranslation.task_id == task_id,
            DocumentTranslation.user_id == actual_user_id
        ).first()
        
        if not task:
            raise HTTPException(status_code=404, detail="翻译任务不存在")
        
        # 删除相关文件
        for file_path in [task.source_file_path, task.translated_file_path, task.preview_path]:
            if file_path and os.path.exists(file_path):
                try:
                    os.remove(file_path)
                except Exception as e:
                    logger.warning(f"删除文件失败: {file_path}, 错误: {str(e)}")
        
        # 删除数据库记录
        db.delete(task)
        db.commit()
        
        return True
    
    async def _process_document(self, task_id: str):
        """处理文档翻译任务"""
        from utils.db import get_db
        from utils.task_persistence import TaskPersistenceManager
        from sqlalchemy import select
        
        try:
            # 获取任务信息 - 使用同步方式
            db = next(get_db())
            stmt = select(DocumentTranslation).where(DocumentTranslation.task_id == task_id)
            task = db.execute(stmt).scalar_one_or_none()
            
            if not task:
                logger.error(f"任务不存在: {task_id}")
                return
            
            # 使用同步方法更新状态
            TaskPersistenceManager.update_task_status_sync(
                db=db,
                task_id=task_id,
                status="processing",
                progress=5,
                message="开始处理文档翻译任务"
            )
            
            # 解析文档内容
            TaskPersistenceManager.update_task_status_sync(
                db=db,
                task_id=task_id,
                status="processing",
                progress=10,
                message="正在解析文档内容"
            )
            
            # 使用同步方法提取文档内容
            file_path = task.source_file_path
            file_type = task.file_type
            document_content = self.extract_document_content_sync(file_path, file_type)
            
            # 计算文档字数
            word_count = 0
            for item in document_content:
                if item["type"] == "paragraph" and "text" in item:
                    word_count += len(item["text"].split())
                elif item["type"] == "table" and "data" in item:
                    for row in item["data"]:
                        for cell in row:
                            if isinstance(cell, str):
                                word_count += len(cell.split())
            
            # 更新任务字数统计
            db.query(DocumentTranslation).filter(DocumentTranslation.task_id == task_id).update(
                {"word_count": word_count}
            )
            db.commit()
            
            # 确保源语言设置正确
            source_language = task.source_language
            if source_language == "auto" or not source_language:
                # 如果是自动检测，设置为默认语言（中文）
                source_language = "zh-CN"
                db.query(DocumentTranslation).filter(DocumentTranslation.task_id == task_id).update(
                    {"source_language": source_language}
                )
                db.commit()
                logger.info(f"源语言自动检测设置为: {source_language}")
            
            # 翻译文档内容 - 使用同步方法
            TaskPersistenceManager.update_task_status_sync(
                db=db,
                task_id=task_id,
                status="processing",
                progress=20,
                message="开始翻译文档内容"
            )
            
            # 使用同步方法进行翻译
            translated_content = self.translate_document_content_sync(
                document_content, 
                source_language, 
                task.target_language,
                task.domain,
                task.style,
                task_id
            )
            
            # 生成翻译文档
            TaskPersistenceManager.update_task_status_sync(
                db=db,
                task_id=task_id,
                status="processing",
                progress=80,
                message="正在生成翻译后的文档"
            )
            
            # 使用同步方法生成翻译文档
            translated_file_path = await self._generate_translated_document(
                file_path,
                translated_content,
                task_id,
                task.target_language,
                source_language
            )
            
            # 生成预览数据
            preview_data = self.generate_preview_from_content_sync(document_content, translated_content)
            preview_path = os.path.join("temp", "previews", f"{task_id}_preview.json")
            os.makedirs(os.path.dirname(preview_path), exist_ok=True)
            
            # 保存预览数据
            with open(preview_path, "w", encoding="utf-8") as f:
                json.dump(preview_data, f, ensure_ascii=False, indent=2)
            
            # 准备结果数据
            result_data = {
                "translated_file_path": translated_file_path,
                "preview_path": preview_path,
                "word_count": word_count,
                "source_language": source_language,
                "target_language": task.target_language
            }
            
            # 更新任务完成状态
            TaskPersistenceManager.update_task_status_sync(
                db=db,
                task_id=task_id,
                status="completed",
                progress=100,
                message="文档翻译完成",
                result_url=translated_file_path,
                output_data=result_data
            )
            
            # 记录任务完成日志
            TaskPersistenceManager.add_task_log_sync(
                db=db,
                task_id=task_id,
                log_type="info",
                message=f"文档翻译完成，生成文件: {os.path.basename(translated_file_path)}"
            )
            
            logger.info(f"文档翻译任务完成: {task_id}")
            
        except Exception as e:
            logger.exception(f"处理文档翻译任务失败: {str(e)}")
            
            try:
                # 更新任务失败状态
                db = next(get_db())
                TaskPersistenceManager.update_task_status_sync(
                    db=db,
                    task_id=task_id,
                    status="failed",
                    progress=0,
                    message=f"文档翻译失败: {str(e)}"
                )
                
                # 记录错误日志
                TaskPersistenceManager.add_task_log_sync(
                    db=db,
                    task_id=task_id,
                    log_type="error",
                    message=f"文档翻译失败: {str(e)}"
                )
            except Exception as log_error:
                logger.error(f"更新任务失败状态时出错: {str(log_error)}")
    
    async def _extract_document_content(self, file_path: str, file_type: str) -> List[Dict[str, Any]]:
        """提取文档内容"""
        file_ext = f".{file_type}"
        content = []
        
        if file_ext in ['.txt']:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.readlines()
                # 按段落分割
                content = [p for p in '\n'.join(content).split('\n\n') if p.strip()]
                
        elif file_ext in ['.doc', '.docx']:
            doc = docx.Document(file_path)
            for para in doc.paragraphs:
                if para.text.strip():
                    content.append(para.text)
                    
        elif file_ext == '.pdf':
            with pdfplumber.open(file_path) as pdf:
                for page in pdf.pages:
                    text = page.extract_text()
                    if text:
                        # 按段落分割
                        paragraphs = [p for p in text.split('\n\n') if p.strip()]
                        content.extend(paragraphs)
                        
        elif file_ext in ['.ppt', '.pptx']:
            prs = Presentation(file_path)
            for slide in prs.slides:
                for shape in slide.shapes:
                    if hasattr(shape, "text") and shape.text.strip():
                        content.append(shape.text)
                        
        elif file_ext in ['.xls', '.xlsx']:
            wb = load_workbook(file_path)
            for sheet in wb.worksheets:
                for row in sheet.iter_rows():
                    for cell in row:
                        if cell.value and str(cell.value).strip():
                            content.append(str(cell.value))
        
        # 处理内容，删除空行等
        content = [text.strip() for text in content if text and text.strip()]
        return content
    
    async def _translate_document_content(self, content: List[str], 
                                         source_lang: str, target_lang: str,
                                         domain: str, style: str,
                                         task_id: str) -> List[str]:
        """翻译文档内容"""
        if not content:
            return []
            
        # 更新进度
        await self._update_task_status(task_id, "processing", 30)
        
        # 对于非常长的内容，将其分批处理以避免请求过大
        batch_size = 10  # 每批10段
        batches = [content[i:i+batch_size] for i in range(0, len(content), batch_size)]
        
        translated_content = []
        total_batches = len(batches)
        
        for i, batch in enumerate(batches):
            try:
                # 清理特殊字符
                cleaned_batch = [self._clean_special_characters(text) if text else "" for text in batch]
                
                # 记录清理情况
                for j, (original, cleaned) in enumerate(zip(batch, cleaned_batch)):
                    if original != cleaned:
                        self.logger.info(f"批次 {i+1}/{total_batches}, 项目 {j+1}: 清理前长度={len(original)}, 清理后长度={len(cleaned)}")
                
                # 使用翻译服务翻译批次
                translated_batch = await self.translation_service.translate_text(
                    cleaned_batch,
                    source_lang,
                    target_lang
                )
                
                # 如果返回的是单个字符串，将其转换为列表
                if isinstance(translated_batch, str):
                    translated_batch = [translated_batch]
                    
                # 如果翻译结果为空，使用原始内容
                if not translated_batch:
                    self.logger.warning(f"批次 {i+1}/{total_batches} 翻译结果为空，使用原始内容")
                    translated_batch = batch
                    
                # 如果翻译结果与原始内容长度不匹配，进行调整
                if len(translated_batch) != len(batch):
                    self.logger.warning(f"批次 {i+1}/{total_batches} 翻译结果长度不匹配: 原始={len(batch)}, 翻译={len(translated_batch)}")
                    # 如果翻译结果较少，补充原始内容
                    if len(translated_batch) < len(batch):
                        translated_batch.extend(batch[len(translated_batch):])
                    # 如果翻译结果较多，截断
                    else:
                        translated_batch = translated_batch[:len(batch)]
                
                # 添加到结果中
                translated_content.extend(translated_batch)
                
                # 更新进度
                progress = 30 + int(60 * (i + 1) / total_batches)
                await self._update_task_status(task_id, "processing", progress)
                
            except Exception as e:
                self.logger.error(f"翻译批次 {i+1}/{total_batches} 失败: {str(e)}")
                # 如果翻译失败，使用原始内容
                translated_content.extend(batch)
                
        return translated_content
    
    async def _generate_translated_document(self, source_file_path: str, 
                                          translated_content: List[str],
                                          task_id: str,
                                          target_language: str,
                                          source_language: str = "auto") -> str:
        """
        生成翻译后的文档
        
        参数:
            source_file_path: 源文件路径
            translated_content: 翻译后的内容
            task_id: 任务ID
            target_language: 目标语言
            source_language: 源语言，默认为auto
            
        返回:
            翻译后的文档路径
        """
        # 确保临时目录存在
        os.makedirs("temp/documents", exist_ok=True)
        
        # 获取文件扩展名
        file_ext = os.path.splitext(source_file_path)[1].lower()
        file_name = os.path.basename(source_file_path)
        file_name_without_ext = os.path.splitext(file_name)[0]
        
        # 生成目标文件名（添加源语言和目标语言代码）
        source_lang_code = source_language if source_language != "auto" else "auto"
        target_file_name = f"{file_name_without_ext}_{source_lang_code}_to_{target_language}{file_ext}"
        target_file_path = os.path.join("temp/documents", f"{task_id}_{target_file_name}")
        
        # 记录翻译方向
        logger.info(f"生成翻译文档: {source_lang_code} -> {target_language}, 文件: {target_file_name}")
        
        # 根据文件类型选择不同的处理方法
        if file_ext in ['.txt', '.md', '.html']:
            # 文本类型文件直接写入
            with open(target_file_path, 'w', encoding='utf-8') as f:
                f.write('\n\n'.join(translated_content))
                
        elif file_ext in ['.doc', '.docx']:
            # 处理Word文档 - 使用改进的方法
            try:
                # 尝试直接创建新文档而不是复制原文件
                from docx import Document
                doc = Document()
                
                # 添加翻译信息到文档属性
                doc.core_properties.comments = f"Translated from {source_lang_code} to {target_language}"
                
                # 按段落分割并添加到文档
                paragraphs = translated_content.split('\n\n')
                for para_text in paragraphs:
                    if isinstance(para_text, str) and para_text.strip():
                        # 清理段落中的特殊字符
                        clean_para = self._clean_special_characters(para_text)
                        if clean_para:
                            doc.add_paragraph(clean_para)
                    elif not isinstance(para_text, str):
                        # 处理非字符串类型
                        doc.add_paragraph(str(para_text))
                
                # 保存修改后的文档
                doc.save(target_file_path)
                logger.info(f"成功创建Word文档: {target_file_path}")
            except Exception as e:
                logger.error(f"创建Word文档失败，尝试备用方法: {str(e)}")
                
                # 备用方法：复制原文件并替换内容
                shutil.copy2(source_file_path, target_file_path)
                doc = Document(target_file_path)
                
                # 清除所有段落
                for i in range(len(doc.paragraphs)-1, -1, -1):
                    p = doc.paragraphs[i]
                    p.clear()
                
                # 添加翻译内容
                paragraphs = translated_content.split('\n\n')
                for para_text in paragraphs:
                    if isinstance(para_text, str) and para_text.strip():
                        # 清理段落中的特殊字符
                        clean_para = self._clean_special_characters(para_text)
                        if clean_para:
                            doc.add_paragraph(clean_para)
                
                # 保存文档
                doc.save(target_file_path)
                
        elif file_ext == '.pdf':
            # PDF文件暂时只能生成文本版本
            txt_file_path = f"{os.path.splitext(target_file_path)[0]}_translated.txt"
            with open(txt_file_path, 'w', encoding='utf-8') as f:
                f.write(f"翻译方向: {source_lang_code} -> {target_language}\n\n")
                f.write('\n\n'.join(translated_content))
            
            # 返回文本文件路径
            return txt_file_path
                
        elif file_ext in ['.ppt', '.pptx']:
            # 处理PPT - 简单实现，复制并替换文本
            shutil.copy2(source_file_path, target_file_path)
            
            # TODO: 使用python-pptx库处理PPT内容
            # 当前版本只生成文本版本
            txt_file_path = f"{os.path.splitext(target_file_path)[0]}_translated.txt"
            with open(txt_file_path, 'w', encoding='utf-8') as f:
                f.write(f"翻译方向: {source_lang_code} -> {target_language}\n\n")
                f.write('\n\n'.join(translated_content))
            
            # 返回文本文件路径
            return txt_file_path
            
        else:
            # 其他格式暂不支持，返回原文件
            shutil.copy2(source_file_path, target_file_path)
            
        return target_file_path
    
    async def _generate_preview(self, task: DocumentTranslation) -> Dict[str, str]:
        """生成文档预览"""
        try:
            # 提取原文内容
            source_content = await self._extract_document_content(task.source_file_path, task.file_type)
            
            # 读取翻译内容
            translated_content = []
            if task.translated_file_path and os.path.exists(task.translated_file_path):
                translated_content = await self._extract_document_content(
                    task.translated_file_path, 
                    get_file_extension(task.translated_file_path).lstrip('.')
                )
            
            return await self._generate_preview_from_content(source_content, translated_content)
            
        except Exception as e:
            logger.exception(f"生成预览失败: {str(e)}")
            return {
                "source": "<p>预览生成失败</p>",
                "translated": "<p>预览生成失败</p>"
            }
    
    def generate_preview_from_content_sync(self, original_content: List[Dict[str, Any]], 
                                     translated_content: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        同步方法：根据原文内容和翻译内容生成预览数据
        
        参数:
            original_content: 原始内容
            translated_content: 翻译后的内容
            
        返回:
            预览数据
        """
        try:
            # 创建预览数据结构
            preview_data = []
            
            # 检查输入类型
            if isinstance(original_content, str) and isinstance(translated_content, str):
                # 处理简单字符串情况
                return [
                    {
                        "type": "paragraph",
                        "source": original_content,
                        "translated": translated_content
                    }
                ]
            
            # 确保列表结构正确
            if not isinstance(original_content, list):
                logger.warning(f"原始内容不是列表类型: {type(original_content)}")
                if isinstance(original_content, str):
                    paragraphs = self._extract_paragraphs(original_content)
                    original_content = [{"type": "paragraph", "text": p} for p in paragraphs]
                else:
                    original_content = []
            
            if not isinstance(translated_content, list):
                logger.warning(f"翻译内容不是列表类型: {type(translated_content)}")
                if isinstance(translated_content, str):
                    paragraphs = self._extract_paragraphs(translated_content)
                    translated_content = [{"type": "paragraph", "text": p} for p in paragraphs]
                else:
                    translated_content = []
                    
            # 处理内容为空的情况
            if not original_content and not translated_content:
                return []
                
            # 如果翻译内容为空但原始内容不为空，则创建空翻译占位
            if not translated_content and original_content:
                for item in original_content:
                    preview_item = {
                        "type": item.get("type", "paragraph"),
                        "source": item.get("text", ""),
                        "translated": ""
                    }
                    preview_data.append(preview_item)
                return preview_data
                
            # 如果长度相同，假设一一对应
            if len(original_content) == len(translated_content):
                for i in range(len(original_content)):
                    orig_item = original_content[i]
                    trans_item = translated_content[i]
                    
                    preview_item = {
                        "type": orig_item.get("type", "paragraph"),
                        "source": orig_item.get("text", ""),
                        "translated": trans_item.get("text", "")
                    }
                    
                    # 对于表格类型，特殊处理
                    if orig_item.get("type") == "table" and "data" in orig_item:
                        preview_item["headers"] = orig_item.get("metadata", {}).get("headers", [])
                        preview_item["sourceRows"] = orig_item.get("data", [])
                        preview_item["rows"] = trans_item.get("data", [])
                        
                    preview_data.append(preview_item)
            else:
                # 长度不同，无法一一对应，只能分别处理
                # 首先处理原始内容
                for item in original_content:
                    preview_item = {
                        "type": item.get("type", "paragraph"),
                        "source": item.get("text", ""),
                        "translated": ""
                    }
                    
                    if item.get("type") == "table" and "data" in item:
                        preview_item["headers"] = item.get("metadata", {}).get("headers", [])
                        preview_item["sourceRows"] = item.get("data", [])
                        
                    preview_data.append(preview_item)
                
                # 然后添加翻译内容
                for i, item in enumerate(translated_content):
                    if i < len(preview_data):
                        # 更新已有项
                        preview_data[i]["translated"] = item.get("text", "")
                        
                        # 更新表格行
                        if item.get("type") == "table" and "data" in item:
                            preview_data[i]["rows"] = item.get("data", [])
                    else:
                        # 添加新项
                        preview_item = {
                            "type": item.get("type", "paragraph"),
                            "source": "",
                            "translated": item.get("text", "")
                        }
                        
                        if item.get("type") == "table" and "data" in item:
                            preview_item["headers"] = item.get("metadata", {}).get("headers", [])
                            preview_item["rows"] = item.get("data", [])
                            
                        preview_data.append(preview_item)
            
            return preview_data
                
        except Exception as e:
            logger.error(f"生成预览数据失败: {str(e)}", exc_info=True)
            # 返回简单的错误预览
            return [
                {
                    "type": "paragraph",
                    "source": "预览生成失败",
                    "translated": f"错误: {str(e)}"
                }
            ]
            
    async def _count_document_words(self, file_path: str, file_ext: str) -> int:
        """计算文档字数"""
        try:
            content = await self._extract_document_content(file_path, file_ext.lstrip('.'))
            text = " ".join(content)
            return count_words(text)
        except Exception as e:
            logger.error(f"计算字数失败: {str(e)}")
            return 0
    
    async def clean_expired_files(self, days: int = 30) -> int:
        """清理过期的文件
        
        参数:
            days: 文件保留天数，超过此天数的文件将被删除
            
        返回:
            已删除的文件数量
        """
        # 计算过期时间
        expiry_time = time.time() - (days * 24 * 60 * 60)
        deleted_count = 0
        
        # 要清理的目录
        dirs_to_clean = [UPLOAD_DIR, RESULT_DIR, PREVIEW_DIR]
        
        for directory in dirs_to_clean:
            if not os.path.exists(directory):
                continue
                
            logger.info(f"清理目录: {directory}")
            
            # 获取目录中的所有文件
            for filename in os.listdir(directory):
                file_path = os.path.join(directory, filename)
                
                # 跳过目录
                if os.path.isdir(file_path):
                    continue
                    
                # 获取文件修改时间
                file_mod_time = os.path.getmtime(file_path)
                
                # 如果文件超过保留期，删除
                if file_mod_time < expiry_time:
                    try:
                        os.remove(file_path)
                        deleted_count += 1
                        logger.debug(f"已删除过期文件: {file_path}")
                    except Exception as e:
                        logger.error(f"删除文件失败: {file_path}, 错误: {str(e)}")
        
        logger.info(f"清理完成，共删除 {deleted_count} 个过期文件")
        return deleted_count

    def extract_document_content_sync(self, file_path: str, file_type: str) -> List[Dict[str, Any]]:
        """
        同步方法：从文档中提取内容
        
        参数:
            file_path: 文件路径
            file_type: 文件类型
            
        返回:
            文档内容列表
        """
        try:
            if file_type.lower() in ['docx', 'doc']:
                return self._extract_word_content_sync(file_path)
            elif file_type.lower() == 'pdf':
                return self._extract_pdf_content_sync(file_path)
            elif file_type.lower() == 'txt':
                return self._extract_txt_content_sync(file_path)
            elif file_type.lower() in ['pptx', 'ppt']:
                return self._extract_ppt_content_sync(file_path)
            elif file_type.lower() in ['xlsx', 'xls']:
                return self._extract_excel_content_sync(file_path)
            elif file_type.lower() == 'md':
                return self._extract_markdown_content_sync(file_path)
            elif file_type.lower() == 'html':
                return self._extract_html_content_sync(file_path)
            else:
                raise ValueError(f"不支持的文件类型: {file_type}")
        except Exception as e:
            logger.error(f"提取文档内容失败: {str(e)}", exc_info=True)
            raise
            
    def _extract_word_content_sync(self, file_path: str) -> List[Dict[str, Any]]:
        """同步方法：从Word文档中提取内容"""
        import docx
        try:
            doc = docx.Document(file_path)
            content = []
            
            # 处理段落
            for i, para in enumerate(doc.paragraphs):
                if para.text.strip():
                    content.append({
                        "id": f"p{i}",
                        "type": "paragraph",
                        "text": para.text,
                        "metadata": {}
                    })
                    
            # 处理表格
            for t, table in enumerate(doc.tables):
                table_data = []
                for r, row in enumerate(table.rows):
                    row_data = []
                    for c, cell in enumerate(row.cells):
                        row_data.append(cell.text)
                    table_data.append(row_data)
                
                if table_data:
                    content.append({
                        "id": f"t{t}",
                        "type": "table",
                        "data": table_data,
                        "metadata": {}
                    })
            
            return content
        except Exception as e:
            logger.error(f"提取Word文档内容失败: {str(e)}", exc_info=True)
            raise
            
    def _extract_pdf_content_sync(self, file_path: str) -> List[Dict[str, Any]]:
        """同步方法：从PDF文档中提取内容"""
        import fitz  # PyMuPDF
        try:
            doc = fitz.open(file_path)
            content = []
            
            for page_num in range(len(doc)):
                page = doc.load_page(page_num)
                text = page.get_text()
                
                # 简单分段处理
                paragraphs = text.split('\n\n')
                for i, para in enumerate(paragraphs):
                    if para.strip():
                        content.append({
                            "id": f"p{page_num}_{i}",
                            "type": "paragraph",
                            "text": para.strip(),
                            "metadata": {"page": page_num + 1}
                        })
            
            return content
        except Exception as e:
            logger.error(f"提取PDF文档内容失败: {str(e)}", exc_info=True)
            raise
            
    def _extract_txt_content_sync(self, file_path: str) -> List[Dict[str, Any]]:
        """同步方法：从文本文件中提取内容"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                text = f.read()
                
            # 按空行分段
            paragraphs = text.split('\n\n')
            content = []
            
            for i, para in enumerate(paragraphs):
                if para.strip():
                    content.append({
                        "id": f"p{i}",
                        "type": "paragraph",
                        "text": para.strip(),
                        "metadata": {}
                    })
            
            return content
        except UnicodeDecodeError:
            # 尝试其他编码
            try:
                with open(file_path, 'r', encoding='gbk') as f:
                    text = f.read()
                    
                # 按空行分段
                paragraphs = text.split('\n\n')
                content = []
                
                for i, para in enumerate(paragraphs):
                    if para.strip():
                        content.append({
                            "id": f"p{i}",
                            "type": "paragraph",
                            "text": para.strip(),
                            "metadata": {}
                        })
                
                return content
            except Exception as e:
                logger.error(f"提取文本文件内容失败: {str(e)}", exc_info=True)
                raise
        except Exception as e:
            logger.error(f"提取文本文件内容失败: {str(e)}", exc_info=True)
            raise
            
    def _extract_ppt_content_sync(self, file_path: str) -> List[Dict[str, Any]]:
        """同步方法：从PPT文档中提取内容"""
        import pptx
        try:
            prs = pptx.Presentation(file_path)
            content = []
            
            for slide_num, slide in enumerate(prs.slides):
                slide_content = []
                
                # 处理形状中的文本
                for shape in slide.shapes:
                    if hasattr(shape, "text") and shape.text.strip():
                        slide_content.append({
                            "id": f"s{slide_num}_{len(slide_content)}",
                            "type": "paragraph",
                            "text": shape.text,
                            "metadata": {"slide": slide_num + 1}
                        })
                
                # 如果有内容，添加到总内容中
                content.extend(slide_content)
            
            return content
        except Exception as e:
            logger.error(f"提取PPT文档内容失败: {str(e)}", exc_info=True)
            raise
            
    def _extract_excel_content_sync(self, file_path: str) -> List[Dict[str, Any]]:
        """同步方法：提取Excel内容"""
        import openpyxl
        try:
            content = []
            
            # 打开Excel文件
            wb = openpyxl.load_workbook(file_path, data_only=True)
            
            # 处理每个工作表
            for sheet_name in wb.sheetnames:
                sheet = wb[sheet_name]
                
                # 获取工作表的数据范围
                data_rows = sheet.max_row
                data_cols = sheet.max_column
                
                # 如果工作表为空，跳过
                if data_rows == 0 or data_cols == 0:
                    continue
                
                # 提取工作表数据
                sheet_data = []
                for row in range(1, data_rows + 1):
                    row_data = []
                    for col in range(1, data_cols + 1):
                        cell = sheet.cell(row=row, column=col)
                        # 获取单元格值，确保不是None
                        cell_value = cell.value if cell.value is not None else ""
                        row_data.append(cell_value)
                    # 检查行是否全为空
                    if any(cell != "" for cell in row_data):
                        sheet_data.append(row_data)
                
                # 检查表格是否为空
                if not sheet_data:
                    continue
                
                # 为每个表格创建唯一ID
                table_id = f"excel_table_{sheet_name}_{len(content)}"
                
                # 添加到内容列表
                content.append({
                    "id": table_id,
                    "type": "table",
                    "data": sheet_data,
                    "metadata": {
                        "sheet": sheet_name,
                        "start_row": 1,
                        "start_col": 1,
                        "rows": len(sheet_data),
                        "cols": max(len(row) for row in sheet_data) if sheet_data else 0
                    }
                })
                
                logger.info(f"提取Excel工作表: {sheet_name}, 行数: {len(sheet_data)}")
            
            return content
        except Exception as e:
            logger.error(f"提取Excel内容失败: {str(e)}", exc_info=True)
            raise
            
    def _extract_markdown_content_sync(self, file_path: str) -> List[Dict[str, Any]]:
        """同步方法：从Markdown文件中提取内容"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                text = f.read()
                
            # 按段落分割
            paragraphs = text.split('\n\n')
            content = []
            
            for i, para in enumerate(paragraphs):
                if para.strip():
                    content.append({
                        "id": f"p{i}",
                        "type": "paragraph",
                        "text": para.strip(),
                        "metadata": {}
                    })
            
            return content
        except Exception as e:
            logger.error(f"提取Markdown文件内容失败: {str(e)}", exc_info=True)
            raise
            
    def _extract_html_content_sync(self, file_path: str) -> List[Dict[str, Any]]:
        """同步方法：从HTML文件中提取内容"""
        from bs4 import BeautifulSoup
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                html_content = f.read()
                
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 移除脚本和样式元素
            for script in soup(["script", "style"]):
                script.extract()
                
            # 获取文本
            text = soup.get_text()
            
            # 按行分割，然后去除多余的空白
            lines = (line.strip() for line in text.splitlines())
            chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
            text = '\n'.join(chunk for chunk in chunks if chunk)
            
            # 按段落分割
            paragraphs = text.split('\n\n')
            content = []
            
            for i, para in enumerate(paragraphs):
                if para.strip():
                    content.append({
                        "id": f"p{i}",
                        "type": "paragraph",
                        "text": para.strip(),
                        "metadata": {}
                    })
            
            return content
        except Exception as e:
            logger.error(f"提取HTML文件内容失败: {str(e)}", exc_info=True)
            raise
            
    def translate_document_content_sync(self, document_content, source_language: str, target_language: str, 
                                   domain: str = "general", style: str = "standard", 
                                   task_id: str = None) -> List[str]:
        """
        同步方法：翻译文档内容
        
        参数:
            document_content: 文档内容，可以是字符串或列表
            source_language: 源语言
            target_language: 目标语言
            domain: 领域
            style: 风格
            task_id: 任务ID
            
        返回:
            翻译后的文档内容列表
        """
        try:
            # 记录开始时间
            start_time = time.time()
            logger.info(f"开始翻译文档内容: {source_language} -> {target_language}, 内容类型: {type(document_content)}")
            
            # 如果是字符串，拆分成段落
            if isinstance(document_content, str):
                paragraphs = self._extract_paragraphs(document_content)
                logger.info(f"从字符串提取的段落数量: {len(paragraphs)}")
                
                # 清理特殊字符
                cleaned_paragraphs = [self._clean_special_characters(para) for para in paragraphs]
                
                # 记录清理情况
                for i, (original, cleaned) in enumerate(zip(paragraphs, cleaned_paragraphs)):
                    if original != cleaned:
                        self.logger.info(f"段落 {i+1}: 清理前长度={len(original)}, 清理后长度={len(cleaned)}")
                
                # 调用翻译服务进行同步翻译
                translation_service = get_translation_service()
                logger.info(f"调用translation_service.translate_text_sync翻译{len(cleaned_paragraphs)}个段落")
                translated_paragraphs = translation_service.translate_text_sync(
                    cleaned_paragraphs, 
                    source_language, 
                    target_language,
                    domain=domain,
                    style=style
                )
                
                logger.info(f"翻译完成，结果数量: {len(translated_paragraphs) if translated_paragraphs else 0}")
                return translated_paragraphs
                
            # 如果是列表，直接翻译
            elif isinstance(document_content, list):
                # 处理列表中的每个元素
                processed_content = []
                texts_to_translate = []
                
                for item in document_content:
                    # 如果是字符串，添加到待翻译列表
                    if isinstance(item, str):
                        # 清理特殊字符
                        cleaned_text = self._clean_special_characters(item)
                        texts_to_translate.append(cleaned_text)
                        processed_content.append({'type': 'text', 'index': len(texts_to_translate) - 1})
                    # 如果是字典，检查是否包含文本内容
                    elif isinstance(item, dict):
                        # 检查是否有文本字段
                        text_fields = ['text', 'content', 'paragraph', 'cell']
                        text_found = False
                        
                        for field in text_fields:
                            if field in item and isinstance(item[field], str):
                                # 清理特殊字符
                                cleaned_text = self._clean_special_characters(item[field])
                                texts_to_translate.append(cleaned_text)
                                # 创建新字典，保留原始结构但替换文本字段为索引
                                new_item = item.copy()
                                new_item[field] = {'type': 'text_ref', 'index': len(texts_to_translate) - 1}
                                processed_content.append(new_item)
                                text_found = True
                                break
                                
                        # 如果没有找到文本字段，保留原始项
                        if not text_found:
                            processed_content.append(item)
                    # 其他类型，直接保留
                    else:
                        processed_content.append(item)
                
                # 如果没有需要翻译的内容，返回空列表
                if not texts_to_translate:
                    logger.warning(f"没有找到需要翻译的文本内容")
                    return document_content  # 返回原始内容
                
                logger.info(f"需要翻译的文本数量: {len(texts_to_translate)}")
                
                # 调用翻译服务进行同步翻译
                translation_service = get_translation_service()
                
                # 如果文本过多，分批翻译
                max_batch_size = 50  # 每批最多翻译50个段落
                translated_texts = []
                
                for i in range(0, len(texts_to_translate), max_batch_size):
                    batch = texts_to_translate[i:i+max_batch_size]
                    logger.info(f"翻译批次 {i//max_batch_size + 1}/{(len(texts_to_translate) + max_batch_size - 1) // max_batch_size}, 数量: {len(batch)}")
                    
                    # 使用批量翻译方法
                    if hasattr(translation_service, 'translate_batch'):
                        logger.info(f"使用translation_service.translate_batch方法")
                        batch_translated = translation_service.translate_batch(
                            batch, 
                            source_language, 
                            target_language
                        )
                    else:
                        # 回退到translate_text_sync方法
                        logger.info(f"使用translation_service.translate_text_sync方法")
                        batch_translated = translation_service.translate_text_sync(
                            batch, 
                            source_language, 
                            target_language,
                            domain=domain,
                            style=style
                        )
                    
                    # 如果返回的是字符串，转换为列表
                    if isinstance(batch_translated, str):
                        batch_translated = [batch_translated]
                        
                    # 如果翻译结果为空或长度不匹配，使用原始内容
                    if not batch_translated or len(batch_translated) != len(batch):
                        logger.warning(f"批次翻译结果异常: 原始数量={len(batch)}, 结果数量={len(batch_translated) if batch_translated else 0}")
                        if not batch_translated:
                            batch_translated = batch
                        elif len(batch_translated) < len(batch):
                            # 补充缺失的翻译
                            batch_translated.extend(batch[len(batch_translated):])
                        else:
                            # 截断多余的翻译
                            batch_translated = batch_translated[:len(batch)]
                    
                    # 添加到翻译结果列表
                    translated_texts.extend(batch_translated)
                
                # 替换引用的文本
                result = []
                for item in processed_content:
                    if isinstance(item, dict) and any(isinstance(item.get(field), dict) and item[field].get('type') == 'text_ref' for field in item):
                        # 找到包含文本引用的字段
                        new_item = item.copy()
                        for field in item:
                            if isinstance(item[field], dict) and item[field].get('type') == 'text_ref':
                                index = item[field].get('index', -1)
                                if 0 <= index < len(translated_texts):
                                    new_item[field] = translated_texts[index]
                                else:
                                    # 索引无效，保留原始文本
                                    logger.warning(f"无效的文本引用索引: {index}, 最大索引: {len(translated_texts)-1}")
                                    new_item[field] = "翻译错误"
                        result.append(new_item)
                    elif isinstance(item, dict) and item.get('type') == 'text' and 'index' in item:
                        # 直接文本引用
                        index = item.get('index', -1)
                        if 0 <= index < len(translated_texts):
                            result.append(translated_texts[index])
                        else:
                            # 索引无效，添加错误消息
                            logger.warning(f"无效的直接文本引用索引: {index}, 最大索引: {len(translated_texts)-1}")
                            result.append("翻译错误")
                    else:
                        # 其他类型，直接添加
                        result.append(item)
                
                # 记录翻译时间
                elapsed_time = time.time() - start_time
                logger.info(f"文档内容翻译完成，耗时: {elapsed_time:.2f}秒")
                
                return result
            else:
                # 不支持的文档内容类型
                logger.error(f"不支持的文档内容类型: {type(document_content)}")
                return document_content  # 返回原始内容
                
        except Exception as e:
            logger.error(f"翻译文档内容失败: {str(e)}", exc_info=True)
            # 返回原始内容，避免完全失败
            return document_content
    
    def _extract_paragraphs(self, document_content: str) -> List[str]:
        """
        从文档内容中提取段落
        
        参数:
            document_content: 文档内容字符串
            
        返回:
            段落列表
        """
        if not document_content:
            return []
            
        # 按换行符分割段落
        paragraphs = re.split(r'\n\s*\n', document_content)
        
        # 过滤空段落并合并短段落
        filtered_paragraphs = []
        current_paragraph = ""
        max_paragraph_length = 2000  # 与MT服务中的最大段落长度保持一致
        
        for para in paragraphs:
            para = para.strip()
            if not para:
                continue
                
            # 如果当前段落加上新段落不超过最大长度，则合并
            if current_paragraph and len(current_paragraph) + len(para) + 2 < max_paragraph_length:
                current_paragraph += "\n\n" + para
            else:
                # 否则添加当前段落并开始新段落
                if current_paragraph:
                    filtered_paragraphs.append(current_paragraph)
                current_paragraph = para
                
        # 添加最后一个段落
        if current_paragraph:
            filtered_paragraphs.append(current_paragraph)
            
        return filtered_paragraphs

    def generate_translated_document_sync(self, source_file_path: str, 
                                   translated_content,
                                   task_id: str,
                                   target_language: str,
                                   source_language: str = "auto") -> str:
        """
        同步生成翻译后的文档
        
        参数:
            source_file_path: 源文件路径
            translated_content: 翻译后的内容，可以是字符串、列表或包含文档内容的字典列表
            task_id: 任务ID
            target_language: 目标语言
            source_language: 源语言，默认为auto
            
        返回:
            翻译后的文档路径
        """
        try:
            # 确保临时目录存在
            os.makedirs("storage/temp", exist_ok=True)
            
            # 获取文件扩展名
            file_ext = os.path.splitext(source_file_path)[1].lower()
            file_name = os.path.basename(source_file_path)
            file_name_without_ext = os.path.splitext(file_name)[0]
            
            # 生成目标文件名（添加源语言和目标语言代码）
            source_lang_code = source_language if source_language != "auto" else "auto"
            target_file_name = f"{file_name_without_ext}_{source_lang_code}_to_{target_language}{file_ext}"
            target_file_path = os.path.join("storage/temp", f"{task_id}_{target_file_name}")
            
            # 记录翻译方向
            logger.info(f"生成翻译文档: {source_lang_code} -> {target_language}, 文件: {target_file_name}")
            
            # 将翻译内容转换为文本
            text_content = ""
            if isinstance(translated_content, str):
                # 如果是字符串，直接使用
                text_content = translated_content
            elif isinstance(translated_content, list):
                # 如果是列表，检查是否包含字典
                if translated_content and isinstance(translated_content[0], dict):
                    # 提取字典中的文本内容
                    paragraphs = []
                    for item in translated_content:
                        if "text" in item and isinstance(item["text"], str):
                            paragraphs.append(item["text"])
                        elif "content" in item and isinstance(item["content"], str):
                            paragraphs.append(item["content"])
                    text_content = "\n\n".join(paragraphs)
                else:
                    # 普通字符串列表
                    text_content = "\n\n".join([str(p) for p in translated_content if p])
            else:
                # 其他类型，转换为字符串
                text_content = str(translated_content)
                
            # 清理特殊字符
            text_content = self._clean_special_characters(text_content)
            
            # 根据文件类型选择不同的处理方法
            if file_ext in ['.txt', '.md', '.html']:
                # 文本类型文件直接写入
                with open(target_file_path, 'w', encoding='utf-8') as f:
                    f.write(text_content)
            elif file_ext in ['.doc', '.docx']:
                # 处理Word文档 - 使用改进的方法
                try:
                    # 尝试直接创建新文档而不是复制原文件
                    from docx import Document
                    doc = Document()
                    
                    # 添加翻译信息到文档属性
                    doc.core_properties.comments = f"Translated from {source_lang_code} to {target_language}"
                    
                    # 按段落分割并添加到文档
                    paragraphs = text_content.split('\n\n')
                    for para_text in paragraphs:
                        if isinstance(para_text, str) and para_text.strip():
                            # 清理段落中的特殊字符
                            clean_para = self._clean_special_characters(para_text)
                            if clean_para:
                                doc.add_paragraph(clean_para)
                        elif not isinstance(para_text, str):
                            # 处理非字符串类型
                            doc.add_paragraph(str(para_text))
                    
                    # 保存修改后的文档
                    doc.save(target_file_path)
                    logger.info(f"成功创建Word文档: {target_file_path}")
                except Exception as e:
                    logger.error(f"创建Word文档失败: {str(e)}")
                    # 回退到文本文件
                    fallback_path = target_file_path.replace(file_ext, '.txt')
                    with open(fallback_path, 'w', encoding='utf-8') as f:
                        f.write(text_content)
                    logger.info(f"已创建文本格式的回退文件: {fallback_path}")
                    target_file_path = fallback_path
            elif file_ext in ['.pdf']:
                # 处理PDF文件 - 创建文本版本
                txt_path = target_file_path.replace('.pdf', '.txt')
                with open(txt_path, 'w', encoding='utf-8') as f:
                    f.write(text_content)
                logger.info(f"已创建文本格式的PDF翻译: {txt_path}")
                target_file_path = txt_path
            elif file_ext in ['.xls', '.xlsx']:
                # 处理Excel文件 - 创建文本版本
                txt_path = target_file_path.replace(file_ext, '.txt')
                with open(txt_path, 'w', encoding='utf-8') as f:
                    f.write(text_content)
                logger.info(f"已创建文本格式的Excel翻译: {txt_path}")
                target_file_path = txt_path
            elif file_ext in ['.ppt', '.pptx']:
                # 处理PowerPoint文件 - 创建文本版本
                txt_path = target_file_path.replace(file_ext, '.txt')
                with open(txt_path, 'w', encoding='utf-8') as f:
                    f.write(text_content)
                logger.info(f"已创建文本格式的PowerPoint翻译: {txt_path}")
                target_file_path = txt_path
            else:
                # 不支持的文件类型，创建文本文件
                txt_path = os.path.join("storage/temp", f"{task_id}_{file_name_without_ext}.txt")
                with open(txt_path, 'w', encoding='utf-8') as f:
                    f.write(text_content)
                logger.info(f"不支持的文件类型 {file_ext}，已创建文本文件: {txt_path}")
                target_file_path = txt_path
                
            return target_file_path
            
        except Exception as e:
            logger.error(f"生成翻译文档失败: {str(e)}", exc_info=True)
            # 创建错误文件
            error_path = os.path.join("storage/temp", f"{task_id}_error.txt")
            try:
                with open(error_path, 'w', encoding='utf-8') as f:
                    f.write(f"翻译文档生成失败: {str(e)}\n\n")
                    if isinstance(translated_content, str):
                        f.write(translated_content)
                    elif isinstance(translated_content, list):
                        f.write("\n\n".join([str(p) for p in translated_content if p]))
                    else:
                        f.write(str(translated_content))
                return error_path
            except Exception as write_error:
                logger.error(f"创建错误文件也失败了: {str(write_error)}")
                return ""

    def _clean_special_characters(self, text: str) -> str:
        """
        清理文本中的特殊字符，包括零宽字符和变体选择器等
        
        参数:
            text: 要处理的文本
            
        返回:
            处理后的文本
        """
        if not text:
            return ""
            
        # 记录原始文本长度
        original_length = len(text)
        
        # 特殊字符映射
        special_char_map = {
            # 零宽字符
            '\u200b': '',  # 零宽空格
            '\u200c': '',  # 零宽非连接符
            '\u200d': '',  # 零宽连接符
            '\u200e': '',  # 从左到右标记
            '\u200f': '',  # 从右到左标记
            '\ufeff': '',  # 零宽不换行空格
            
            # 变体选择器
            '\ufe00': '',  # 变体选择器-1
            '\ufe01': '',  # 变体选择器-2
            '\ufe02': '',  # 变体选择器-3
            '\ufe03': '',  # 变体选择器-4
            '\ufe04': '',  # 变体选择器-5
            '\ufe05': '',  # 变体选择器-6
            '\ufe06': '',  # 变体选择器-7
            '\ufe07': '',  # 变体选择器-8
            '\ufe08': '',  # 变体选择器-9
            '\ufe09': '',  # 变体选择器-10
            '\ufe0a': '',  # 变体选择器-11
            '\ufe0b': '',  # 变体选择器-12
            '\ufe0c': '',  # 变体选择器-13
            '\ufe0d': '',  # 变体选择器-14
            '\ufe0e': '',  # 变体选择器-15
            '\ufe0f': '',  # 变体选择器-16
            
            # 控制字符 (ASCII 0-31)
            '\u0000': '', '\u0001': '', '\u0002': '', '\u0003': '', '\u0004': '',
            '\u0005': '', '\u0006': '', '\u0007': '', '\u0008': '', '\u0009': ' ',  # Tab转为空格
            '\u000a': '\n', '\u000b': '', '\u000c': '', '\u000d': '\n',  # 保留换行
            '\u000e': '', '\u000f': '', '\u0010': '', '\u0011': '', '\u0012': '',
            '\u0013': '', '\u0014': '', '\u0015': '', '\u0016': '', '\u0017': '',
            '\u0018': '', '\u0019': '', '\u001a': '', '\u001b': '', '\u001c': '',
            '\u001d': '', '\u001e': '', '\u001f': '',
            
            # 特殊字符替换
            '・': '·',  # 日文中点替换为中文中点
            '￥': '¥',  # 全角日元符号替换为半角
        }
        
        # 替换特殊字符
        processed_text = text
        replacement_count = 0
        
        for special, replacement in special_char_map.items():
            original_text = processed_text
            processed_text = processed_text.replace(special, replacement)
            
            # 计算替换次数
            if original_text != processed_text:
                current_replacements = original_text.count(special)
                replacement_count += current_replacements
                self.logger.debug(f"替换了 {special} -> {replacement}, 次数: {current_replacements}")
        
        # 处理连续的空格（保留最多两个空格）
        processed_text = re.sub(r' {3,}', '  ', processed_text)
        
        # 如果有替换，记录日志
        if replacement_count > 0:
            self.logger.info(f"处理了{replacement_count}个特殊字符: 原始长度={original_length}, 处理后长度={len(processed_text)}")
        
        return processed_text

# 创建服务实例
document_translation_service = DocumentTranslationService() 