import os
import sys
import logging
from logging.handlers import RotatingFileHandler

def get_logger(name, log_file=None, level=logging.INFO):
    """
    获取一个配置好的日志记录器
    
    Args:
        name: 日志记录器名称
        log_file: 日志文件路径，如果为None则使用名称作为日志文件名
        level: 日志级别
        
    Returns:
        配置好的日志记录器
    """
    # 创建日志记录器
    logger = logging.getLogger(name)
    
    # 如果已经配置过处理器，直接返回
    if logger.handlers:
        return logger
    
    # 设置日志级别
    logger.setLevel(level)
    
    # 创建日志目录
    logs_dir = os.path.join("backend", "logs")
    os.makedirs(logs_dir, exist_ok=True)
    
    # 确定日志文件路径
    if log_file is None:
        log_file = os.path.join(logs_dir, f"{name}.log")
    elif not os.path.isabs(log_file):
        log_file = os.path.join(logs_dir, log_file)
    
    # 创建文件处理器，限制每个日志文件大小为10MB，最多保留5个备份
    file_handler = RotatingFileHandler(
        log_file,
        maxBytes=10 * 1024 * 1024,  # 10 MB
        backupCount=5,
        encoding="utf-8"
    )
    
    # 创建控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    
    # 设置日志格式
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # 为处理器设置格式化器
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)
    
    # 为日志记录器添加处理器
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    # 阻止日志传播到根日志记录器
    logger.propagate = False
    
    return logger 