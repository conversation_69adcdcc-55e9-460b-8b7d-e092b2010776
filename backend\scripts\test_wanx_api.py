#!/usr/bin/env python3
"""
测试 Wanx 2.1 API 功能
"""
import requests
import time
import json

# API 基础 URL
BASE_URL = "http://localhost:8000/api/v1/video"

def test_text_to_video():
    """测试文本转视频 API"""
    print("🎬 测试 Wanx 2.1 文本转视频...")
    
    # 准备测试数据
    test_data = {
        "prompt": "一只可爱的橙色小猫在绿色草地上快乐地奔跑",
        "model": "t2v-1.3B",
        "duration": 3,
        "resolution": "512x512",
        "fps": 24,
        "guidance_scale": 7.5,
        "num_inference_steps": 20  # 减少步数以加快测试
    }
    
    try:
        # 发送请求
        response = requests.post(f"{BASE_URL}/text-to-video", json=test_data)
        
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                task_id = result.get("task_id")
                celery_task_id = result.get("celery_task_id")
                
                print(f"✅ 任务创建成功!")
                print(f"   - 任务ID: {task_id}")
                print(f"   - Celery任务ID: {celery_task_id}")
                
                return task_id
            else:
                print(f"❌ 任务创建失败: {result}")
                return None
        else:
            print(f"❌ API 请求失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return None

def check_task_status(task_id):
    """检查任务状态"""
    print(f"\n🔍 检查任务状态: {task_id}")
    
    try:
        response = requests.get(f"{BASE_URL}/status/{task_id}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                status = result.get("status")
                progress = result.get("progress", 0)
                message = result.get("message", "")
                video_url = result.get("video_url")
                error = result.get("error")
                
                print(f"📊 状态: {status}")
                print(f"📈 进度: {progress}%")
                print(f"💬 消息: {message}")
                
                if video_url:
                    print(f"🎥 视频URL: {video_url}")
                
                if error:
                    print(f"❌ 错误: {error}")
                
                return status, progress, video_url
            else:
                print(f"❌ 状态查询失败: {result}")
                return None, None, None
        else:
            print(f"❌ 状态查询请求失败: {response.status_code}")
            return None, None, None
            
    except Exception as e:
        print(f"❌ 状态查询异常: {e}")
        return None, None, None

def test_video_history():
    """测试视频历史 API"""
    print("\n📚 测试视频历史...")
    
    try:
        response = requests.get(f"{BASE_URL}/history/demo-user")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                videos = result.get("videos", [])
                total = result.get("total", 0)
                
                print(f"✅ 历史记录查询成功!")
                print(f"📊 总数: {total}")
                
                for i, video in enumerate(videos[:3]):  # 只显示前3个
                    print(f"   {i+1}. {video.get('title', 'N/A')} - {video.get('status', 'N/A')}")
                
                return True
            else:
                print(f"❌ 历史记录查询失败: {result}")
                return False
        else:
            print(f"❌ 历史记录请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 历史记录查询异常: {e}")
        return False

def monitor_task(task_id, max_wait_minutes=5):
    """监控任务进度"""
    print(f"\n⏱️ 监控任务进度 (最多等待 {max_wait_minutes} 分钟)...")
    
    max_checks = max_wait_minutes * 6  # 每10秒检查一次
    
    for i in range(max_checks):
        status, progress, video_url = check_task_status(task_id)
        
        if status == "completed":
            print(f"\n🎉 任务完成!")
            if video_url:
                print(f"🎥 视频下载地址: http://localhost:8000{video_url}")
            return True
        elif status == "failed":
            print(f"\n❌ 任务失败!")
            return False
        elif status in ["pending", "processing"]:
            print(f"⏳ 等待中... ({i+1}/{max_checks})")
            time.sleep(10)  # 等待10秒
        else:
            print(f"⚠️ 未知状态: {status}")
            time.sleep(10)
    
    print(f"\n⏰ 监控超时 ({max_wait_minutes} 分钟)")
    return False

def main():
    """主测试函数"""
    print("🧪 Wanx 2.1 API 功能测试")
    print("=" * 50)
    
    # 检查后端是否运行
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        print("✅ 后端服务正常运行")
    except:
        print("❌ 后端服务未运行，请先启动后端")
        print("💡 运行: cd backend && python main.py")
        return
    
    # 测试视频历史
    test_video_history()
    
    # 测试文本转视频
    task_id = test_text_to_video()
    
    if task_id:
        # 监控任务进度
        success = monitor_task(task_id, max_wait_minutes=3)
        
        if success:
            print("\n🎯 测试总结:")
            print("✅ Wanx 2.1 文本转视频功能正常")
            print("✅ 任务状态监控正常")
            print("✅ PostgreSQL 数据库集成正常")
            print("✅ Celery 异步处理正常")
        else:
            print("\n⚠️ 测试总结:")
            print("✅ API 接口正常")
            print("✅ 任务创建正常")
            print("⏳ 视频生成可能需要更长时间")
            print("💡 请检查 Celery Worker 日志")
    else:
        print("\n❌ 测试失败:")
        print("❌ 无法创建视频生成任务")
        print("💡 请检查 API 配置和 Celery Worker")

if __name__ == "__main__":
    main()
