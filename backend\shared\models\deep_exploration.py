from sqlalchemy import <PERSON>umn, Integer, String, Text, ForeignKey, DateTime, Boolean
from sqlalchemy.sql import func
from pydantic import BaseModel
from typing import Optional, Dict, Any
from datetime import datetime
import json

from utils.db import Base
from utils.model_helpers import safe_json_loads

class DeepExploration(Base):
    """Deep exploration database model"""
    __tablename__ = "deep_explorations"

    id = Column(Integer, primary_key=True, index=True)
    request_id = Column(String, unique=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    prompt = Column(Text)
    response = Column(Text, nullable=True)
    parameters = Column(Text)  # JSON string with parameters
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    completed_at = Column(DateTime(timezone=True), nullable=True)
    is_shared = Column(Boolean, default=False)  # Whether this exploration is shared with others

# Pydantic models for API
class DeepExplorationBase(BaseModel):
    prompt: str

class DeepExplorationCreate(DeepExplorationBase):
    parameters: Dict[str, Any]

class DeepExplorationResponse(DeepExplorationBase):
    id: int
    request_id: str
    user_id: int
    response: Optional[str] = None
    created_at: datetime
    completed_at: Optional[datetime] = None
    is_shared: bool
    parameters: Dict[str, Any]

    class Config:
        from_attributes = True

    @classmethod
    def from_orm(cls, obj):
        # Handle the parameters JSON conversion
        obj_dict = {c.name: getattr(obj, c.name) for c in obj.__table__.columns}
        if obj_dict.get("parameters"):
            obj_dict["parameters"] = safe_json_loads(
                obj_dict["parameters"], 
                default_value={}, 
                expected_type=dict,
                field_name="parameters", 
                entity_id=obj_dict.get("request_id")
            )
        else:
            obj_dict["parameters"] = {}
        return cls(**obj_dict) 