#!/usr/bin/env python3
"""
实时数字人对话服务
整合ASR、对话生成、TTS和数字人视频生成
"""

import asyncio
import json
import logging
from typing import Optional, Dict, Any, List
from datetime import datetime
from pathlib import Path
import uuid

from .realtime_asr import asr_manager, RealtimeASR
from .advanced_ai_chat import advanced_ai_chat, emotion_analyzer
from .emotion_expression import advanced_video_generator
from .speech_recognition_service import speech_recognition_service, voice_command_processor
from ..tasks.digital_human import _generate_audio_from_text
from ..config.digital_human_agents import get_digital_human_config, get_emotion_mapping

logger = logging.getLogger(__name__)

class ConversationSession:
    """对话会话类"""
    
    def __init__(self, session_id: str, digital_human_config: Dict[str, Any]):
        self.session_id = session_id
        self.digital_human_config = digital_human_config
        self.conversation_history: List[Dict[str, Any]] = []
        self.is_active = False
        
        # ASR实例
        self.asr: Optional[RealtimeASR] = None
        
        # 回调函数
        self.on_user_speech = None
        self.on_ai_response = None
        self.on_video_generated = None
        self.on_error = None
        
        # 状态
        self.is_listening = False
        self.is_generating = False
    
    async def initialize(self):
        """初始化会话"""
        try:
            # 创建ASR实例
            asr_config = {
                'model_name': 'whisper-base',
                'language': 'zh',
                'sample_rate': 16000,
                'chunk_duration': 1.0
            }
            
            self.asr = await asr_manager.create_session(self.session_id, asr_config)
            
            # 设置ASR回调
            self.asr.set_callbacks(
                on_partial=self._on_partial_speech,
                on_final=self._on_final_speech,
                on_error=self._on_asr_error
            )
            
            self.is_active = True
            logger.info(f"✅ 对话会话初始化成功: {self.session_id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 对话会话初始化失败: {e}")
            return False
    
    async def start_listening(self):
        """开始监听用户语音"""
        if not self.asr:
            await self.initialize()
        
        if self.asr and not self.is_listening:
            self.is_listening = True
            await self.asr.start_listening()
            logger.info(f"🎤 开始监听用户语音: {self.session_id}")
    
    async def stop_listening(self):
        """停止监听"""
        if self.asr and self.is_listening:
            self.is_listening = False
            await self.asr.stop_listening()
            logger.info(f"🛑 停止监听: {self.session_id}")
    
    async def _on_partial_speech(self, text: str):
        """处理部分语音识别结果"""
        if self.on_user_speech:
            await self.on_user_speech({
                'type': 'partial',
                'text': text,
                'timestamp': datetime.now().isoformat()
            })
    
    async def _on_final_speech(self, text: str):
        """处理最终语音识别结果"""
        if not text.strip():
            return
        
        logger.info(f"👤 用户说话: {text}")
        
        # 添加到对话历史
        user_message = {
            'role': 'user',
            'content': text,
            'timestamp': datetime.now().isoformat()
        }
        self.conversation_history.append(user_message)
        
        # 通知前端
        if self.on_user_speech:
            await self.on_user_speech({
                'type': 'final',
                'text': text,
                'timestamp': user_message['timestamp']
            })
        
        # 生成AI回复
        await self._generate_ai_response(text)
    
    async def _on_asr_error(self, error: str):
        """处理ASR错误"""
        logger.error(f"❌ ASR错误: {error}")
        if self.on_error:
            await self.on_error(f"语音识别错误: {error}")
    
    async def _generate_ai_response(self, user_input: str):
        """生成AI回复"""
        if self.is_generating:
            return
        
        self.is_generating = True
        
        try:
            # 生成AI回复文本
            ai_response = await self._get_ai_response(user_input)
            
            if ai_response:
                # 添加到对话历史
                ai_message = {
                    'role': 'assistant',
                    'content': ai_response,
                    'timestamp': datetime.now().isoformat()
                }
                self.conversation_history.append(ai_message)
                
                logger.info(f"🤖 AI回复: {ai_response}")
                
                # 通知前端
                if self.on_ai_response:
                    await self.on_ai_response({
                        'text': ai_response,
                        'timestamp': ai_message['timestamp']
                    })
                
                # 生成数字人视频
                await self._generate_digital_human_video(ai_response)
        
        except Exception as e:
            logger.error(f"❌ AI回复生成失败: {e}")
            if self.on_error:
                await self.on_error(f"AI回复生成失败: {e}")
        
        finally:
            self.is_generating = False
    
    async def _get_ai_response(self, user_input: str) -> str:
        """获取高级AI回复"""
        try:
            # 分析用户情感
            user_emotion = emotion_analyzer.analyze_emotion(user_input)
            logger.info(f"🎭 检测到用户情感: {user_emotion}")

            # 获取智能体信息
            agent_info = self.digital_human_config.get("agent_info", {})
            agent_type = agent_info.get("agent_type", "chat")

            # 获取情感映射
            emotion_mapping = get_emotion_mapping(agent_type)
            mapped_emotion = emotion_mapping.get(user_emotion, user_emotion)

            # 构建上下文
            context = {
                "digital_human_name": self.digital_human_config.get("name", "AI助手"),
                "agent_type": agent_type,
                "agent_name": agent_info.get("name", "智能体"),
                "conversation_length": len(self.conversation_history),
                "user_emotion": user_emotion,
                "mapped_emotion": mapped_emotion,
                "system_prompt": agent_info.get("system_prompt", "")
            }

            # 使用高级AI生成回复
            ai_response = await advanced_ai_chat.generate_response(
                user_input=user_input,
                conversation_history=self.conversation_history,
                user_emotion=mapped_emotion,
                context=context
            )

            logger.info(f"🤖 高级AI回复: {ai_response}")
            return ai_response

        except Exception as e:
            logger.error(f"❌ 高级AI回复失败: {e}")
            # 回退到简单回复
            return self._get_simple_ai_response(user_input)

    def _get_simple_ai_response(self, user_input: str) -> str:
        """获取简单AI回复（回退方案）"""
        user_input_lower = user_input.lower()

        if "你好" in user_input_lower or "hello" in user_input_lower:
            return "你好！我是你的AI数字人助手，很高兴与你对话！"
        elif "数字人" in user_input_lower:
            return "我是一个数字人，可以与你进行实时对话。我能理解你的语音并生成相应的回复视频。"
        elif "怎么样" in user_input_lower or "如何" in user_input_lower:
            return "我觉得很好！我可以帮你解答问题，或者我们可以聊聊其他话题。"
        elif "再见" in user_input_lower or "拜拜" in user_input_lower:
            return "再见！很高兴与你聊天，期待下次见面！"
        elif "谢谢" in user_input_lower:
            return "不客气！我很乐意帮助你。还有什么其他问题吗？"
        else:
            return f"我听到你说：{user_input}。这是一个很有趣的话题！你能告诉我更多吗？"
    
    async def _generate_digital_human_video(self, text: str):
        """生成情感感知的数字人说话视频"""
        try:
            from pathlib import Path
            import subprocess
            import sys

            # 生成唯一的输出文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            video_filename = f"realtime_{self.session_id}_{timestamp}.mp4"
            video_path = Path("storage/realtime_videos") / video_filename

            # 确保目录存在
            video_path.parent.mkdir(parents=True, exist_ok=True)

            # 使用数字人配置中的头像
            avatar_path = Path(self.digital_human_config.get('avatar_url', '').replace('/storage/', 'storage/'))

            if not avatar_path.exists():
                logger.warning(f"头像文件不存在: {avatar_path}")
                return

            # 分析文本情感
            text_emotion = emotion_analyzer.analyze_emotion(text)
            logger.info(f"🎭 文本情感分析: {text_emotion}")

            # 尝试使用高级情感视频生成
            success = await self._generate_emotion_video(avatar_path, text, video_path, text_emotion)

            if not success:
                # 回退到MuseTalk
                success = await self._generate_musetalk_video(avatar_path, text, video_path)

            if success:
                video_url = f"/storage/realtime_videos/{video_filename}"
                logger.info(f"✅ 实时数字人视频生成成功: {video_url}")

                if self.on_video_generated:
                    await self.on_video_generated({
                        'video_url': video_url,
                        'text': text,
                        'emotion': text_emotion,
                        'timestamp': datetime.now().isoformat()
                    })
            else:
                logger.error("❌ 所有视频生成方法都失败了")

        except Exception as e:
            logger.error(f"❌ 数字人视频生成异常: {e}")

    async def _generate_emotion_video(self, avatar_path: Path, text: str, video_path: Path, emotion: str) -> bool:
        """生成情感视频"""
        try:
            # 使用高级视频生成器
            success = await asyncio.to_thread(
                advanced_video_generator.generate_emotion_aware_video,
                avatar_path=avatar_path,
                text=text,
                output_path=video_path,
                emotion=emotion,
                fps=25,
                duration=min(10, max(3, len(text) // 8)),  # 根据文本长度调整时长
                quality="high"
            )

            if success:
                logger.info(f"✅ 情感视频生成成功: {emotion}")
                return True
            else:
                logger.warning(f"⚠️  情感视频生成失败: {emotion}")
                return False

        except Exception as e:
            logger.error(f"❌ 情感视频生成异常: {e}")
            return False

    async def _generate_musetalk_video(self, avatar_path: Path, text: str, video_path: Path) -> bool:
        """生成MuseTalk视频（回退方案）"""
        try:
            import subprocess
            import sys

            musetalk_script = Path("storage/models/digital_human/musetalk/working_musetalk.py")

            if not musetalk_script.exists():
                logger.error("❌ MuseTalk脚本不存在")
                return False

            # 生成音频
            audio_path = video_path.with_suffix('.wav')
            if not _generate_audio_from_text(text, audio_path):
                logger.error("❌ 音频生成失败")
                return False

            # 调用MuseTalk
            cmd = [
                sys.executable,
                str(musetalk_script),
                "--source_image", str(avatar_path),
                "--driving_audio", str(audio_path),
                "--output", str(video_path),
                "--device", "auto",
                "--fps", "25",
                "--duration", str(min(10, max(3, len(text) // 8))),
                "--quality", "medium"
            ]

            result = await asyncio.to_thread(
                subprocess.run, cmd, capture_output=True, text=True, timeout=30
            )

            # 清理音频文件
            if audio_path.exists():
                audio_path.unlink()

            if result.returncode == 0 and video_path.exists():
                logger.info("✅ MuseTalk视频生成成功")
                return True
            else:
                logger.error(f"❌ MuseTalk生成失败: {result.stderr}")
                return False

        except Exception as e:
            logger.error(f"❌ MuseTalk视频生成异常: {e}")
            return False
    
    async def cleanup(self):
        """清理会话资源"""
        await self.stop_listening()
        await asr_manager.remove_session(self.session_id)
        self.is_active = False
        logger.info(f"🧹 对话会话已清理: {self.session_id}")

class ConversationManager:
    """对话管理器"""
    
    def __init__(self):
        self.sessions: Dict[str, ConversationSession] = {}
    
    async def create_session(self, digital_human_config: Dict[str, Any]) -> str:
        """创建对话会话"""
        session_id = str(uuid.uuid4())
        
        session = ConversationSession(session_id, digital_human_config)
        success = await session.initialize()
        
        if success:
            self.sessions[session_id] = session
            logger.info(f"✅ 对话会话创建成功: {session_id}")
            return session_id
        else:
            logger.error(f"❌ 对话会话创建失败")
            return None
    
    def get_session(self, session_id: str) -> Optional[ConversationSession]:
        """获取对话会话"""
        return self.sessions.get(session_id)
    
    async def remove_session(self, session_id: str):
        """移除对话会话"""
        if session_id in self.sessions:
            session = self.sessions[session_id]
            await session.cleanup()
            del self.sessions[session_id]
            logger.info(f"🗑️ 对话会话已移除: {session_id}")
    
    async def cleanup_all(self):
        """清理所有会话"""
        for session_id in list(self.sessions.keys()):
            await self.remove_session(session_id)

# 全局对话管理器实例
conversation_manager = ConversationManager()
